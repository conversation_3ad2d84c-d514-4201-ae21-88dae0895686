<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.moredian.magicube</groupId>
    <artifactId>device-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <artifactId>magicube-device-api</artifactId>
  <version>${current.version}</version>

  <properties>
    <current.version>3.1.55-SNAPSHOT</current.version>
    <java.version>1.8</java.version>
    <jackson-annotations.version>2.8.0</jackson-annotations.version>
    <commons-lang.version>2.6</commons-lang.version>
    <magicube.common.version>3.0.68-SNAPSHOT</magicube.common.version>
    <filemanager.version>1.3.4</filemanager.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.moredian.bee</groupId>
        <artifactId>bee-pom-dependencies</artifactId>
        <version>2.1.0-SNAPSHOT</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>show</id>
      <properties>
        <package.environment>show</package.environment>
        <current.version>3.1.55</current.version>
        <java.version>1.8</java.version>
        <jackson-annotations.version>2.8.0</jackson-annotations.version>
        <commons-lang.version>2.6</commons-lang.version>
        <magicube.common.version>3.0.68</magicube.common.version>
        <filemanager.version>1.3.4</filemanager.version>
      </properties>
    </profile>
  </profiles>

  <dependencies>
    <!-- Third Party Dependencies Begin, Alphabetical order -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>${jackson-annotations.version}</version>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>${commons-lang.version}</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.22</version>
    </dependency>
    <!-- Third Party Dependencies End -->

    <!-- Moredian Dependencies Begin, Alphabetical order -->
    <dependency>
      <groupId>com.xier.sesame.common</groupId>
      <artifactId>common-base</artifactId>
      <version>0.0.2</version>
      <exclusions>
        <exclusion>
          <artifactId>dubbo</artifactId>
          <groupId>com.alibaba</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.moredian.magicube</groupId>
      <artifactId>magicube-common</artifactId>
      <version>${magicube.common.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>redisson</artifactId>
          <groupId>org.redisson</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.moredian.bee</groupId>
      <artifactId>bee-filemanager</artifactId>
      <version>${filemanager.version}</version>
    </dependency>

    <!-- Moredian Dependencies End -->
  </dependencies>

  <build>
    <plugins>
      <!-- 要将源码放上去，需要加入这个插件 -->
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <attach>true</attach>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>