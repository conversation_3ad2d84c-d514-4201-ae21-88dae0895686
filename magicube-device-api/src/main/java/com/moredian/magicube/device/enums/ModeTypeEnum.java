package com.moredian.magicube.device.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $Id: RuleTypeEnum.java, v 1.0 Exp $
 * @desc 模式
 */
@Getter
public enum ModeTypeEnum {

    ENERGY_SAVE(1, "节能模式"),
    ENERGY_CONSUME(2, "用能模式"),

    ;
    private final int code;

    private final String desc;

    ModeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
