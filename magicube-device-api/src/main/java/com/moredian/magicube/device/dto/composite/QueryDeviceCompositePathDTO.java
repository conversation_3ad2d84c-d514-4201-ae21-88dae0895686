package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/28 11:23
 */
@Data
public class QueryDeviceCompositePathDTO implements Serializable {

    private static final long serialVersionUID = -4533440381122335611L;

    /**
     * 组织 ID
     */
    private Long orgId;


    /**
     * 设备 ID列表
     */
    private List<Long> deviceIds;


    /**
     * 设备组 IDl列表
     */
    private List<Long> compositeIds;

}
