package com.moredian.magicube.device.dto.blu;

import java.io.Serializable;

public class DeviceBluConnectDeCryptReq implements Serializable {

    private static final long serialVersionUID = -1945260934564496949L;
    /**
     * 加密字符串
     */
    private String encryptStr;

    /**
     * 设备sn
     */
    private String deviceSn;

    public String getEncryptStr() {
        return encryptStr;
    }

    public void setEncryptStr(String encryptStr) {
        this.encryptStr = encryptStr;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
}
