package com.moredian.magicube.device.dto.device.iot;

import com.moredian.magicube.device.enums.iot.IotFunctionIdEnum;
import lombok.Data;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description iot设备功能调用参数
 * @create 2024-12-26 15:09
 */
@Data
public class DeviceFunctionInvokeDTO implements Serializable {

    private String deviceSn;

    /**
     * 调用功能id，需要与魔链平台设备物魔型中功能id相同
     * @see IotFunctionIdEnum
     */
    private String functionId;

    private Map<String, Object> params;
}
