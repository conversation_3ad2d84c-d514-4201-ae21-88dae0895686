package com.moredian.magicube.device.dto.white;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;

/**
 * 批量新增白单
 *
 * <AUTHOR>
 * @since 2022/7/20
 */
@Getter
@Setter
public class BatchInsertWhiteListDeviceRequest implements Serializable {

    private static final long serialVersionUID = -3845487979484564790L;

    /**
     * 白单列表
     */
    @NotEmpty(message = "白单列表不能为空")
    private List<WhiteListDeviceDTO> deviceInventoryList;

}
