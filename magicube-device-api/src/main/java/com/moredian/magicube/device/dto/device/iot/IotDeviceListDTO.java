package com.moredian.magicube.device.dto.device.iot;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description IOT 设备列表出参
 * @create 2024-12-27 10:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IotDeviceListDTO implements Serializable {

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备类型
     */
    private Integer deviceType;
    private String deviceTypeName;

    /**
     * 空间树Id
     */
    private Long treeId;

    /**
     * 全路径名称
     */
    private String pathTreeName;

    /**
     * 是否在线
     */
    private Boolean online;

    /**
     * 列表关键参数展示
     */
    private List<IotKeyPropertyDTO> keyPropertyList;

    /**
     * 是否虚拟设备
     */
    private Boolean virtualFlag;
}
