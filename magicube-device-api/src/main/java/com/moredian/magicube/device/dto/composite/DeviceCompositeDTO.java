package com.moredian.magicube.device.dto.composite;

import java.util.Date;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设备分组信息
 *
 * <AUTHOR>
 */

@Data
public class DeviceCompositeDTO implements Serializable {

    private static final long serialVersionUID = -1258598066412038923L;

    /**
     * 设备分组Id
     */
    private Long deviceCompositeId;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 类型，使用，0-门禁，1-访客
     */
    private Integer bizType;

    /**
     * 组名
     */
    private String deviceCompositeName;

    /**
     * 当前组下以及子组下的所有设备统计
     */
    private Integer deviceSize;

    private Date gmtCreate;

    private Date gmtModify;

    /**
     * 父组 ID
     */
    private Long parentId;

    /**
     * 组 code
     */
    private String code;

    /**
     * 组code路径 包括自身code
     * /00000000/xx
     * 从虚拟根开始
     */
    private String path;

    /**
     * 设备组名称路径
     */
    private String pathName;

    /**
     * 是否有子组
     */
    private Boolean hasChildren;

    /**
     * 设备的type集合
     */
    private List<Integer> deviceTypeList;

    /**
     * 设备分组条目列表
     */
    List<DeviceCompositeItemDTO> items;
}
