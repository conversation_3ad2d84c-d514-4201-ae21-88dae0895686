package com.moredian.magicube.device.dto.peripherals;

import com.moredian.magicube.common.enums.PeripheralsType;
import java.io.Serializable;
import lombok.Data;

/**
 * 批量导入
 */
@Data
public class PeripheralsWhiteListNewErrorDTO implements Serializable {

    private static final long serialVersionUID = -4996448959750343120L;


    /**
     * 下标
     */
    private Integer index;
    /**
     * 外设sn
     */
    private String peripheralsSn;
    /**
     * 关联设备类型，全部类型为0
     */
    private Integer deviceType;
    /**
     * 关联appType类型，全部为0
     */
    private Integer appType;
    /**
     * 外设类型
     * @see PeripheralsType
     */
    private Integer peripheralsType;
    /**
     * deviceType名称
     */
    private String deviceTypeName;
    /**
     * appType名称
     */
    private String appTypeName;
    /**
     * 配件类型名称
     */
    private String peripheralsTypeName;
    /**
     * 备注字段
     */
    private String remark;
    /**
     * 错误原因
     */
    private String errorMsg;

}
