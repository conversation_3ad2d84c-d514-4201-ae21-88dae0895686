package com.moredian.magicube.device.dto.peripherals;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 外设信息
 *
 * <AUTHOR>
 */
@Data
public class PeripheralsDTO implements Serializable {

    private static final long serialVersionUID = -3963738071461504794L;

    /**
     * 外设Id
     */
    private Long peripheralsId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 外设名称
     */
    private String peripheralsName;

    /**
     * 外设sn
     */
    private String peripheralsSn;

    /**
     * 关联设备sn
     */
    private String deviceSn;

    /**
     * 关联设备名称
     */
    private String deviceName;

    /**
     * 设备状态  -1- 解绑  0-断开，1-绑定
     */
    private Integer status;

    /**
     * 启动状态  0-不启用；1-启用
     */
    private Integer available;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 设备型号,目前使用不到
     */
    private String modelType;

    /**
     * 绑定时间
     */
    private Date bindTime;

    /**
     * 关联的设备类型
     */
    private Integer deviceType;

    /**
     * 外设的设备类型
     */
    private Integer peripheralsType;

    /**
     * 手机imei
     */
    private String peripheralsConnectId;

    /**
     * 系统类型 1-ios ，2-android
     */
    private Integer mobileSysType;

    /**
     * 匹配类型，1-匹配，0-不匹配
     */
    private Integer matchStatus;

    //创建时间
    private Date gmtCreate;
    //解绑时间
    private Date gmtModify;
}
