/**
 * 
 */
package com.moredian.magicube.device.dto.upgrade;

import java.io.Serializable;

public class UpdateAppToLastRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4652389823138084707L;

	private String serialNumber;
	
	private int systemType;
	
	private int appType;

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public int getSystemType() {
		return systemType;
	}

	public void setSystemType(int systemType) {
		this.systemType = systemType;
	}

	public int getAppType() {
		return appType;
	}

	public void setAppType(int appType) {
		this.appType = appType;
	}
	
}
