package com.moredian.magicube.device.dto.accessory;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/12/8
 */
@Data
public class AccessoryQueryDTO implements Serializable {

    private static final long serialVersionUID = 7188640283884762927L;

    /**
     * 机构ID -required
     */
    @NotNull(message = "机构id不能为空")
    private Long orgId;

    /**
     * 配件sn
     */
    private String accessorySn;

    /**
     * 配件类型
     */
    private Integer accessorType;

    /**
     * 关联设备sn
     */
    private String deviceSn;

    /**
     * 设备sn列表
     */
    private List<String> deviceSnList;

}
