package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.dto.device.RebootDTO;
import com.moredian.magicube.device.dto.device.TransferDTO;
import java.util.List;

/**
 * 设备iot服务
 *
 * <AUTHOR>
 */
public interface DeviceIotService {

    /**
     * 查询设备状态
     *
     * @param deviceSn 设备sn
     * @return
     */
    ServiceResponse<DeviceStatusDTO> getStatusByDeviceSn(String deviceSn);

    /**
     * 根据设备sn列表查询设备状态列表
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    ServiceResponse<List<DeviceStatusDTO>> listDeviceStateByDeviceSns(List<String> deviceSns);

    /**
     * 重启设备
     *
     * @param dto 重启信息
     * @return
     */
    ServiceResponse<DeviceStatusDTO> reboot(RebootDTO dto);

    /**
     * 透传命令
     *
     * @param dto 透传信息
     * @return
     */
    ServiceResponse<DeviceStatusDTO> transfer(TransferDTO dto);
}