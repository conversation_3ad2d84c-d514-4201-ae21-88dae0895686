package com.moredian.magicube.device.dto.project;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12
 */
@Getter
@Setter
@ToString
public class QueryDeviceProjectInfoRequest implements Serializable {

    private static final long serialVersionUID = 5843873313963639390L;

    /**
     * 设备Sn列表
     */
    private List<String> deviceSnList;

    /**
     * 项目Id
     */
    private String projectId;

    /**
     * 版本信息
     */
    private String version;

    /**
     * 场景
     */
    private Integer sceneType;
}
