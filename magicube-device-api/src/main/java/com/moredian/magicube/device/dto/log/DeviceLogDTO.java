package com.moredian.magicube.device.dto.log;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备日志Log返回
 *
 * @Author: fangJ
 */

@Data
public class DeviceLogDTO implements Serializable {

    private static final long serialVersionUID = 4446697667034353812L;

    /**
     * 设备日志Id
     */
    private Long deviceLogId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 事件类型
     */
    private Integer envenType;

    /**
     * 事件描述
     */
    private String envenDesc;

    /**
     * 操作人Id
     */
    private Long memberId;

    /**
     * 操作时间戳
     */
    private Long operTime;
}
