package com.moredian.magicube.device.dto.third;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description
 * @author: xiesj
 * @date 2022/12/29 15:43
 */
@Getter
@Setter
@ToString
public class ThirdDeviceResponse implements Serializable {

    private static final long serialVersionUID = 689745460718382340L;

    /**
     * 主键id
     */
    private Long thirdDeviceId;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 厂商
     */
    private String manufacturer;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 三方设备SN
     */
    private String parentDeviceSn;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
