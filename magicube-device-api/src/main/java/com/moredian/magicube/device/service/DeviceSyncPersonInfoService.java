package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.person.PagePersonInfoDTO;
import com.moredian.magicube.device.dto.person.PersonInfoDTO;
import com.moredian.magicube.device.dto.person.QueryPersonInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 设备同步人员信息接口
 * 由于core服务在架构上无法依赖magicube-device,所以device封装一层后再调用magicube-core
 * 后续如果有类似的接口，直接在magicube-device中组装，逻辑全部放magicube-device中。
 * @Date 2023/12/25
 */
public interface DeviceSyncPersonInfoService {

    /**
     * 门锁全量同步人员特征值、卡片、权限组
     *
     * @param dto
     * @return
     */
    ServiceResponse<Pagination<PersonInfoDTO>> pagePersonInfo(PagePersonInfoDTO dto);

    /**
     * 门锁查询人员特征值、卡片、权限组
     *
     * @param dto
     * @return
     */
    ServiceResponse<List<PersonInfoDTO>> queryPersonInfo(QueryPersonInfoDTO dto);
}
