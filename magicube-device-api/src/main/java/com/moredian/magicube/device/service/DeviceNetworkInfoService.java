package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.network.DeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.network.InsertDeviceNetworkInfoDTO;

/**
 * 设备网络信息接口
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
public interface DeviceNetworkInfoService {

    /**
     * 上报设备网络信息
     *
     * @param dto 上报设备网络信息
     * @return
     */
    ServiceResponse<Boolean> upload(InsertDeviceNetworkInfoDTO dto);

    /**
     * 根据机构号和设备Id获取网络信息
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    ServiceResponse<DeviceNetworkInfoDTO> getByOrgIdAndDeviceId(Long orgId, Long deviceId);
}
