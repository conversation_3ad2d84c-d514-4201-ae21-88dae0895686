package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/20
 */
@Getter
@Setter
@ToString
public class QueryAppTypeRedirectDTO implements Serializable {

    private static final long serialVersionUID = 4108134289253565239L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 跳转链接
     */
    private String redirectUrl;

    /**
     * 环境
     */
    private String env;
}
