package com.moredian.magicube.device.dto.activate;

import lombok.Data;

import java.io.Serializable;

/**
 * 激活信息
 *
 * <AUTHOR>
 */

@Data
public class QrCodeDTO implements Serializable {

    private static final long serialVersionUID = 8787938358907682797L;

    /**
     * 是否需要扫码
     */
    private Boolean generate;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 是否开放平台 开放平台激活下发给设备不需要带H5跳转链接（具体跳转的页面又开发者自行决定）
     */
    private Boolean isOpenPlat;

    /**
     * 跳转url链接
     */
    private String url;

}
