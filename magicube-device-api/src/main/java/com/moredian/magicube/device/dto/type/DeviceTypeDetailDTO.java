package com.moredian.magicube.device.dto.type;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 设备类型详情
 *
 * <AUTHOR>
 */

@Data
public class DeviceTypeDetailDTO implements Serializable {

    private static final long serialVersionUID = -960579474982265466L;

    /**
     * 设备类型Id
     */
    private Long deviceTypeId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备类型描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 设备类型属性信息列表
     */
    private List<DeviceTypePropertyDTO> propertyDTOS;
}