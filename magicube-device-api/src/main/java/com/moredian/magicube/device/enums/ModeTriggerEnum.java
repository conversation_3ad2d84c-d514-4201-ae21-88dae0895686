package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @version $Id: ModeTriggerTemplateEnum.java, v 1.0 Exp $
 */
public enum ModeTriggerEnum {

    /**
     * 场景触发模版枚举
     */
    SAVE_ENERGY_TIMER_TRIGGER(ModeTypeEnum.ENERGY_SAVE, TriggerTypeEnum.TIMER),
    SAVE_ENERGY_DEVICE_TRIGGER(ModeTypeEnum.ENERGY_SAVE, TriggerTypeEnum.DEVICE),
    USE_ENERGY_HUMAN_TRIGGER(ModeTypeEnum.ENERGY_CONSUME, TriggerTypeEnum.HUMAN),

    ;

    /**
     * ${@link ModeTypeEnum}
     */
    private final ModeTypeEnum modeTypeEnum;

    /**
     * ${@link TriggerTypeEnum}
     */
    private final TriggerTypeEnum triggerTypeEnum;

    ModeTriggerEnum(ModeTypeEnum modeTypeEnum, TriggerTypeEnum triggerTypeEnum) {
        this.modeTypeEnum = modeTypeEnum;
        this.triggerTypeEnum = triggerTypeEnum;
    }

    public static ModeTriggerEnum getTemplate(int modeType, int triggerType) {
        for (ModeTriggerEnum item : values()) {
            if (item.modeTypeEnum.getCode() == modeType  && item.triggerTypeEnum.getCode() == triggerType) {
                return item;
            }
        }
        return null;
    }
}
