package com.moredian.magicube.device.constant;

/**
 * @class: DeviceMemberLimit
 * @description: 设备人员限制常量
 * @author: Yyp
 * @create: 2020-12-24 20:02
 **/
public class DeviceMemberLimitConst {
    public static final int X2_DEVICE_MEMBER = 1000;
    public static final int Y2_DEVICE_MEMBER = 1500;
    public static final int Y2A_DEVICE_MEMBER = 1500;
    public static final int Y2B_DEVICE_MEMBER = 1500;
    public static final int G2_DEVICE_MEMBER = 3000;
    public static final int D2_DEVICE_MEMBER = 10000;
    public static final int D2C_DEVICE_MEMBER = 10000;
    public static final int D2P_DEVICE_MEMBER = 15000;
    public static final int D3_DEVICE_MEMBER = 30000;
    public static final int S5_DEVICE_MEMBER = 20000;
    public static final int MG3_DEVICE_MEMBER = 30000;
    public static final int Y3_DEVICE_MEMBER = 3000;
    public static final int Y3s_DEVICE_MEMBER = 3000;
    public static final int MY3_DEVICE_MEMBER = 10000;
    public static final int MY3A_DEVICE_MEMBER = 10000;
    public static final int MX1A_DEVICE_MEMBER = 3000;
    public static final int X1_DEVICE_MEMBER = 3000;
    public static final int T21_DEVICE_MEMBER = 30000;
    public static final int T10V_DEVICE_MEMBER = 30000;
}
