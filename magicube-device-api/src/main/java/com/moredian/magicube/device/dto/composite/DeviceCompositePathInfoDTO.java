package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/28 11:23
 */
@Data
public class DeviceCompositePathInfoDTO implements Serializable {

    private static final long serialVersionUID = -4533440381122335611L;

    /**
     * 组织 ID
     */
    private Long orgId;


    /**
     * 设备的设备组路径信息 列表
     */
    private List<DeviceInCompositePathDTO> devicePathInfoList;


    /**
     * 设备组详情列表
     */
    private List<DeviceCompositeDTO> compositeDtoList;
}
