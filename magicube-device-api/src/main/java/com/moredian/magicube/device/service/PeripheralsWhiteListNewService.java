package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddBatchDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewErrorDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewInfoDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewQueryDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewUpdateDTO;
import com.xier.sesame.common.rpc.dto.PaginationDto;
import java.util.List;

/**
 * @Auther: _AF
 * @Date: 2/23/22 17:25
 * @Description: 新的配件外设白名单操作
 */
public interface PeripheralsWhiteListNewService {
    /**
     * 根据条件查询
     *
     * @param dto
     * @return
     */
    ServiceResponse<PaginationDto<PeripheralsWhiteListNewInfoDTO>> pageByCondition(
        PeripheralsWhiteListNewQueryDTO dto);

    /**
     * 根据id集合删除，不能为空
     *
     * @param ids
     * @return
     */
    ServiceResponse<Boolean> removeByIds(List<Long> ids);


    /**
     * 修改
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> updateById(PeripheralsWhiteListNewUpdateDTO dto);

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> addPeripheralsWhiteListNew(PeripheralsWhiteListNewAddDTO dto);

    /**
     * 批量导入
     *
     * @param dto
     * @return
     */
    ServiceResponse<List<PeripheralsWhiteListNewErrorDTO>> addByBatch(List<PeripheralsWhiteListNewAddBatchDTO> dto);


    /**
     * 根据sn和type查询
     *
     * @param peripheralsSn   不能为空
     * @param peripheralsType 不能为空
     * @return
     */
    ServiceResponse<PeripheralsWhiteListNewInfoDTO> getBySnAndType(String peripheralsSn, Integer peripheralsType);


    /**
     * 根据sn集合查询
     *
     * @param peripheralsSnList
     * @return
     */
    ServiceResponse<List<PeripheralsWhiteListNewInfoDTO>> getBySnList(List<String> peripheralsSnList);

    /**
     * 旧表数据同步到新表
     */
    ServiceResponse<Boolean> syncOldDataToNewData();

}
