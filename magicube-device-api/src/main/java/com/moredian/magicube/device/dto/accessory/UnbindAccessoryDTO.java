package com.moredian.magicube.device.dto.accessory;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/12/8
 */
@Data
public class UnbindAccessoryDTO implements Serializable {

    private static final long serialVersionUID = -4206261323273357802L;

    /**
     * 机构ID
     */
    @NotNull(message = "机构id不能为空")
    private Long orgId;

    /**
     * 设备sn
     */
    @NotNull(message = "设备sn不能为空")
    private String deviceSn;

    /**
     * 配件sn
     */
    @NotNull(message = "配件sn不能为空")
    private String accessorySn;

    /**
     * 配件类型
     */
    @NotNull(message = "配件类型不能为空")
    private Integer accessoryType;

}
