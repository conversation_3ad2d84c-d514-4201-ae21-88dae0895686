package com.moredian.magicube.device.dto.upgrade;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/10
 */
@Getter
@Setter
@ToString
public class GetIncrOrFullNewAppRequest implements Serializable {

    private static final long serialVersionUID = -1570265417291732851L;

    private Long orgId;

    /**
     * rom或app类型
     */
    private Integer appType;

    /**
     * 设备当前版本
     */
    private Integer versionCode;

    /**
     * rom中的app类型
     */
    private Integer apkAppType;
}
