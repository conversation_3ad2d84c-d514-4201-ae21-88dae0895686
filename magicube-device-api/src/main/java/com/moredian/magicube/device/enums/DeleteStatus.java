package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @date 2024/11/28 10:06
 */
public enum DeleteStatus {


    AVAILABLE(0L, "正常状态"),

    /**
     * 除了0外的其他值都是删除状态
     */
    DELETED(1L, "删除状态");


    private Long code;


    private String desc;


    DeleteStatus(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Long getCode() {
        if (code == 1L) {
            return System.currentTimeMillis();
        }
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
