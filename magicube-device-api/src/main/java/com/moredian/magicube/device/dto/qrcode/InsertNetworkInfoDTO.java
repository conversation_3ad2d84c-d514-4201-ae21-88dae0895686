package com.moredian.magicube.device.dto.qrcode;

import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import java.io.Serializable;
import lombok.Data;

/**
 * @Classname： ActivateQrCodeCreateDto
 * @Date: 2023/1/4 11:44 上午
 * @Author: fangJ
 * @Description: 新增配网信息
 */
@Data
public class InsertNetworkInfoDTO implements Serializable {

    private static final long serialVersionUID = -6565274128303858140L;

    private Long id;

    private Long orgId;

    private String deviceAddressName;

    /**
     * 1-有线，默认
     * 2-无线
     * @see ActivateQrCodeConstant.NetworkTypeEnum
     */
    private Integer networkType = 1;

    /**
     * 1-DHCP
     * 2-静态，默认
     * @see  ActivateQrCodeConstant.ConnectTypeEnum
     */
    private Integer connectType = 2;

    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;

    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;

    /**
     * 二维码地址
     */
    private String url;

    /**
     * 服务器地址 目前仅用于保存私有化服务器服务器地址
     */
    private String serverAddress;

    /**
     * @see ActivateQrCodeConstant.AppletEnum
     */
    private Integer source;
}
