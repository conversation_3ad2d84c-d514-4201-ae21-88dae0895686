package com.moredian.magicube.device.dto.third;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description
 * @author: xiesj
 * @date 2022/12/29 17:42
 */
@Getter
@Setter
@ToString
public class AddThirdDeviceRequest implements Serializable {

    private static final long serialVersionUID = -5611117181584227967L;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 厂商
     */
    private String manufacturer;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 备注
     */
    private String remark;
}
