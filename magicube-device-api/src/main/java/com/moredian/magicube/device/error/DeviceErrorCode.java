package com.moredian.magicube.device.error;

import com.moredian.bee.common.exception.BaseError;
import com.moredian.bee.common.exception.ErrorCode;
import com.moredian.bee.common.exception.v1.ErrorLevel;
import com.moredian.bee.common.exception.v1.ErrorType;
import com.moredian.bee.common.exception.v1.V1Error;

/**
 * <AUTHOR>
 */

public enum DeviceErrorCode implements ErrorCode {

    PARAMS_WRONG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0000", "参数不符合要求"),

    //由于需要兼容线上的U2,MU3,S5等设备,所以先不改动,保持原来的错误码
    DEVICE_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0048", "设备已存在"),

    DEVICE_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0002", "设备不存在"),

    WRONG_VIDEO_STREAM(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0003", "视频流格式有误"),

    NO_VIDEO_STREAM_PATTERN(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0004", "%s的视频流解析规则未配置"),

    DEVICE_EDIT_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0005", "设备编辑失败,请重试"),

    DEVICE_DELETE_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0006", "设备删除失败,请重试"),

    CE_DEVICE_DELETE_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0007", "删除云眼设备失败"),

    CE_DEVICE_UPDATE_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0008", "修改云眼设备失败"),

    RECOGNIZE_BUSI_NOT_OPEN(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0009", "1比N识别业务未开启"),

    CE_DEVICE_ADD_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0010", "添加云眼设备失败"),

    INVALID_ACTIVE_EXCEPTION(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0019", "激活码无效"),

    /**
     * 设备类型不匹配，无法激活
     */
    EQUIPMENT_TYPE_NOMATCH(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0053", "设备类型不匹配，无法激活"),

    /**
     * 设备已激活过
     */
    ACTIVE_CONFLICT_ERROR(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0018", "设备已在[%s]激活过"),

    /**
     * 此激活码只能在原设备上使用
     */
    ACTIVE_REFUSED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0039", "此激活码只能在原设备上使用"),

    DEPLOY_GROUP_EMPTY(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0049", "布控目标组不能为空"),

    DEVICE_MATCH_REPEAT(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0011", "此摄像头已匹配过盒子"),

    DEVICE_CE_DEPLOY_ERROR(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0012", "设备云眼布控异常"),

    /**
     * 摄像机和魔点盒子有绑定关系，不能删除摄像机或魔点盒子
     */
    DEVICE_CAMERA_BINDING_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0052", "设备和其他设备有绑定关系"),

    DEVICE_CREATE_FAILED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0054", "创建摄像头失败"),

    DEVICE_BOX_CAMERA_BIND_FAILED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0055", "摄像头和盒子绑定"),

    DEVICE_BOX_UPDATED_FAILED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0056", "摄像头更新失败"),

    DEVICE_CAMERA_DELETE_FAILED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0057", "删除摄像头失败"),

    DEVICE_APPUPDATE_CROSSLEVEL(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0058", "设备app升级目前不支持跨版本"),

    DEVICE_ACTIVE_CHANGE_VERIFYCHANNLE(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0059", "设备激活不允许变更识别通道"),

    DEVICE_BIND_GROUP_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.INFO, "006", "0060", "设备绑定的群组不存在"),

    DEVICE_ACTIVE_CHANGE_VERIFY_ARITHMETIC(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0061", "设备激活不允许变更识别算法"),

    OUT_OF_RANGE_DEVICE_GROUP_MEMBERSIZE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0062", "设备绑定的组内人员总数超过范围"),

    DEVICE_ROM_VERSION_GET_ERROR(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0063", "设备rom版本获取失败"),

    ERROR_DEVICE_XML_CONFIG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0064", "设备xml配置错误！"),

    PERIPHREALS_ADD_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0065", "外设设备添加失败！"),

    PERIPHREALS_UPDATE_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0066", "外设设备更新失败！"),

    DEVICE_WHITE_LIST_DATA_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0067", "数据已存在！"),

    DEVICE_WHITE_LIST_DATA_ADD_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0068", "测温白名单数据添加失败！"),

    DEVICE_WHITE_LIST_UN_BIND_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0069", "测温白名单解绑失败！"),

    DEVICE_WHITE_LIST_IND_SUCCESS(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0070", "测温白名单绑定失败！"),

    PERIPHREALS_BIND_PERIOUS_ORG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0071", "外设绑定上一个机构！"),

    ACCOUNT_DEVICE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0072", "设备与账号不匹配"),

    SN_NOT_USE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0073", "暂不支持此SN"),

    DEVICE_ALEADY_USE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0074", "设备已被激活"),

    QRCODE_EXPRICE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0075", "二维码失效,请刷新二维码"),

    CHECK_EQUIPMENT_EXCEPTION(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0076", "设备效验失败"),

    INVALID_ACTIVE_EXPIRE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0077", "超时过期"),

    NO_EXIST_EQUIPMENT(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0078", "未找到设备"),

    ISSU_DEVICE_ACCESS_KEY_FAIL(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0079", "设备密钥颁发失败"),

    ORG_ID_VALIDATE_GET_ERROR(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0080", "机构id错误"),

    SYS_ERROR(ErrorType.SYSTEM, ErrorLevel.ERROR, "006", "0081", "系统错误"),

    NOT_CLOUND_EYE_RECOGNITION(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0082", "非云眼识别方式不支持该操作"),

    GROUPS_MUST_NOT_BE_EMPTY(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0083", "groups不能为空"),

    DEVICE_TYPE_MUST_NOT_BE_EMPTY(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0084", "设备类型不能为空"),

    DEVICE_WHITE_LIST_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0085", "设备白名单已存在"),

    DEVICE_COMPOSITE_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0086", "分组不存在"),

    DEVICE_COMPOSITE_NAME_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0087", "设备组名称重复"),

    DEVICE_BIND_COMPOSITE_ALREADY(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0088", "设备已绑定组"),

    BIND_DEVICE_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0089", "绑组设备不存在"),

    TOO_MANY_DATA_REQUEST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0090", "超过每次最大获取数量限额100"),

    IMPORT_LIMIT(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0091", "超过最大导入数量500，请缩小范围重试"),

    DEVICE_WHITE_LIST_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0092", "设备白名单不存在"),

    DEVICE_ACTIVATE_ENGINE_NOT_FOUND(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0093", "未找到设备激活执行器type:%S"),

    DING_CORP_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0094", "设备未经过认证:未获取到机构信息"),

    CAMPUS_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0095", "班牌设备所属机构不存在校区：无法激活"),

    DEVICE_AND_ORG_NOT_BELONG_SAME_AREA(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0096", "设备和机构不属于同一区域"),

    INVALID_ACTIVE_INDUSTRY_NOT_MATCH(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0097", "设备和机构行业不匹配，禁止激活"),

    CREATE_ORG_FAILED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0098", "机构创建失败"),
    DEVICE_TYPE_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0099", "设备类型已存在"),
    DEVICE_TYPE_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0100", "设备类型不存在"),
    DEVICE_TYPE_PROPERTY_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0101", "设备类型属性已存在"),
    DEVICE_TYPE_PROPERTY_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0102", "设备类型属性不存在"),
    DEVICE_TYPE_PROPERTY_RELATION_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0103", "设备类型和属性关系已存在"),

    DEVICE_BLU_DECRYPT_ERROR(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0104", "蓝牙签名解析错误"),

    DEVICE_BLU_SIGN_EXPIRE(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0105", "蓝牙签名过期"),

    DEVICE_BLU_SIGN_VALID(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0106", "无效的蓝牙签名"),

    DEVICE_BLU_ENCRYPT_ERROR(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0107", "蓝牙加密异常"),

    DEVICE_NOT_WHITE(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0108", "设备没有添加白名单"),
    ACTIVATE_QR_CODE_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0109", "激活码不存在"),
    ACTIVATE_QR_CODE_INVALID(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0110", "激活码已失效"),
    PERIPHREALS_BIND_PREVIOUS_DEVICE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0111", "外设绑定上一个设备！"),
    PERIPHREALS_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0112", "外设不存在"),
    PERIPHREALS_UNBIND_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0113", "外设断开失败"),
    PERIPHREALS_SN_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0114", "外设sn数据已存在"),
    LENGTH_TOO_LONG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0115", "长度太长"),
    DEVICE_TYPE_NOT_SUPPORT(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0116", "设备类型不支持"),
    DEVICE_RESET_NETWORK_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0117", "设备重新配网失败，请重试"),
    DEVICE_RESET_NETWORK_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0118", "该设备正在进行重新配网，请勿重复操作"),
    WIFI_NAME_TOO_LONG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0119", "WIFI名称不能超过20字符"),
    WIFI_PASSWORD_TOO_LONG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0120", "WIFI密码不能超过20字符"),
    DEVICE_SUBJECT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0121", "设备主题已存在"),
    DEVICE_SUBJECT_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0122", "设备主题不存在"),
    DEVICE_SUBJECT_NUMBER_RESTRICTION(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0123", "设备壁纸数量不能超过10个"),
    DEVICE_POSITION_TOO_LONG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0124", "具体位置输入内容过长"),
    DEVICE_NOT_AUTHORIZE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0125", "设备未授权"),
    DEVICE_SUBJECT_INSERT_FIAL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0126", "设备壁纸插入失败"),
    APP_TYPE_REDIRECT_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0127", "APP类型跳转链接不存在"),
    APP_TYPE_REDIRECT_EXISTS(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0128", "APP类型跳转链接已存在"),
    DEVICE_CAPACITY_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0129", "设备类型人脸容量未配置"),
    DEVICE_SNAPSHOT_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0130", "设备快照不存在"),
    DEVICE_POSITION_TOOLONG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0131", "设备位置最多支持100个字符"),
    DEVICE_ONT_ONLINE(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0132", "迁移设备不在线"),
    MIGRATION_DEVICE_RUNNABLE(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0133", "迁移设备中，请等待"),
    DEVICE_PRODUCT_MODEL_NOT_EXIST(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0134", "产品型号不存在"),
    DEVICE_COMPOSITE_BEYOND_LEVEL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0135", "设备组超出层级限制，最多%s层"),
    DEVICE_COMPOSITE_NAME_LIMIT(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0136", "设备组名最多支持20位"),
    DEVICE_COMPOSITE_CHANGE_ORG(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0137", "设备组变更频繁，请稍后"),
    DEVICE_COMPOSITE_PARENT_EXIST(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0138", "父设备组不存在"),

    // 新增错误码用于规范化异常处理
    DEVICE_RULE_ID_AND_TEMPLATE_ID_NOT_NULL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0139", "请传入ruleId或者templateId"),
    DEVICE_RULE_TEMPLATE_REQUIRED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0140", "请传入ruleId或者templateId"),
    DEVICE_UNKNOWN_ERROR(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0141", "未知错误"),
    DEVICE_DATA_FORMAT_ERROR(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0142", "输入数据格式错误，请校验数据格式"),
    DEVICE_DD_SERVICE_CALL_FAILED(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0143", "发起调用塔上服务失败"),
    DEVICE_WIFI_CONFIG_FAILED(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0144", "上报锁wifi信息失败"),
    DEVICE_ACCESS_TOKEN_FAILED(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0145", "获取通通锁accessToken失败"),
    DEVICE_THIRD_PARTY_CONFIRM_INFO_MISSING(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0146", "未取得第三方确认信息"),
    DEVICE_ORG_CREATE_FAILED(ErrorType.SERVICE, ErrorLevel.ERROR, "006", "0147", "机构创建失败"),
    DEVICE_ORG_ID_REQUIRED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0148", "机构ID不能为空"),
    DEVICE_SN_REQUIRED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0149", "设备SN不能为空"),
    DEVICE_RELATIONSHIPS_REQUIRED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0150", "没有设备需要添加到设备组"),
    DEVICE_BLU_INVALID_SIGNATURE(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0151", "无效蓝牙连接签名"),
    DEVICE_FUNCTION_NOT_SUPPORT(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0152", "设备不支持该功能"),
    DEVICE_RULE_NODE_CANNOT_BE_NULL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0153", "规则编排连接节点不能为空"),
    DEVICE_UNBIND_FAIL(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0154", "解绑设备失败"),
    DEVICE_TIME_ZONE_LANGUAGE_NOT_SUPPORTED(ErrorType.SERVICE, ErrorLevel.WARN, "006", "0155", "当前设备类型不支持更新时区和语言配置"),
    ;

    /**
     * 错误码，不能为空
     */
    private BaseError error;

    DeviceErrorCode(ErrorType errorType, ErrorLevel errorLevel, String systemCode, String systemErrorCode, String message) {
        this.error = new V1Error(errorType, errorLevel, systemCode, systemErrorCode, message);
    }

    @Override
    public String getCode() {
        return this.error.getCode();
    }

    /**
     * @return the errorMessage
     */
    public String getMessage() {
        return this.error.getMessage();
    }

    public BaseError getError() {
        return error;
    }

}