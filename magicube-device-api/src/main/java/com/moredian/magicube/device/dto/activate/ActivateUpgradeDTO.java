package com.moredian.magicube.device.dto.activate;

import java.io.Serializable;
import lombok.Data;


/**
 * <AUTHOR>
 * 
 * 设备激活后升级请求
 *
 */

@Data
public class ActivateUpgradeDTO implements Serializable {
	private static final long serialVersionUID = 8356288975826327991L;
	
	private Long orgId;
	private String serialNumber;
	private Long deviceId;
	private Integer systemType;
	private Integer appType;
	private Integer versionCode;
	// true ROM版本, false APP版本
	private Boolean isRom;
}
