package com.moredian.magicube.device.dto.project;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/12
 */
@Getter
@Setter
@ToString
public class UpdateProjectInfoResponse implements Serializable {

    /**
     * 变更的设备列表
     */
    private List<String> changedDeviceSnList;

    /**
     * 未变更的设备列表
     */
    private List<String> unchangedDeviceSnList;
}
