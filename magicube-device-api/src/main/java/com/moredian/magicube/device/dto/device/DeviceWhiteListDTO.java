package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Author:dongchao
 * @Description:
 * @Date: 2020/2/24 21:33
 */
@Data
public class DeviceWhiteListDTO implements Serializable {
    //白名单id
    private Long whiteListId;
    //机构id
    private Long orgId;
    //设备sn
    private String deviceSn;
    //设备名
    private String deviceName;
    private Long deviceId;
    //状态:1-可用,0-无效
    private Integer status;
    //添加时间
    private Date gmtCreate;
    //修改时间
    private Date gmtModify;
}
