package com.moredian.magicube.device.constant;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * iot设备属性常量
 */
public class DeviceIotPropertyConstants {

    public static final String MOTION_PROPERTY = "motion";

    public static final String NO_MOTION_PROPERTY = "no_motion";

    public static final String MOTION_FORMAT_VALUE = "有人移动";

    public static final String NO_MOTION_FORMAT_VALUE = "无人移动";

    public static final List<String> NOT_SPLIT_PROPERTIES = Lists.newArrayList("map");

    /**
     * 不存在到存在属性
     */
    public static final String UN_OCCUPIED_TO_OCCUPY = "UnoccupiedToOccupiedDelay";

    /**
     * 存在到不存在变化时间
     */
    public static final String OCCUPIED_TO_OCCUPY = "OccupiedToUnoccupiedDelay";

    public static final String MOTION_STATUS = "motionStatus";

    //jetlinks 场景规则
    public static final String JET_LINK_RULE_TYPE_SCENE = "SCENE";

    //jetlinks规则编排
    public static final String JET_LINK_RULE_TYPE_RULE_ARRANGE = "RULE_ARRANGE";

    /**
     * iot设备额关键读数
     */
    public static final Map<Integer, List<String>> IOT_DEVICE_KEY_PROPERTY = new HashMap<>();


    static {
        // 计量插座
        IOT_DEVICE_KEY_PROPERTY.put(505, Lists.newArrayList("PowerSwitch", "RealTimePower"));
        // 人体传感器
        IOT_DEVICE_KEY_PROPERTY.put(506, Lists.newArrayList("motionStatus"));
        // 温湿度传感器
        IOT_DEVICE_KEY_PROPERTY.put(507, Lists.newArrayList("temperature", "humidity"));
        // 单键开关
        IOT_DEVICE_KEY_PROPERTY.put(509, Lists.newArrayList("power"));
        // 人体红外传感器
        IOT_DEVICE_KEY_PROPERTY.put(510, Lists.newArrayList("motionStatus"));
        // 开关
        IOT_DEVICE_KEY_PROPERTY.put(511, Lists.newArrayList("power"));
        // 空调
        IOT_DEVICE_KEY_PROPERTY.put(512, Lists.newArrayList("ac_power_", "ac_target_temperature_", "ac_indoor_temperature_"));
        // 红外线单体空调温控器
        IOT_DEVICE_KEY_PROPERTY.put(515, Lists.newArrayList("pwr", "ac_mark", "temp", "ac_mode"));
    }


}
