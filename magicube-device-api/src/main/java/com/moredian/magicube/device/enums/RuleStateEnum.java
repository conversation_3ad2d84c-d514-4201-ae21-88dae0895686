package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @version $Id: RuleStateEnum.java, v 1.0 Exp $
 * @desc 规则状态
 */
public enum RuleStateEnum {
    /**
     * 规则类型
     */
    DISABLE(0, "禁用"),
    ACTIVE(1, "启用"),

    ;
    private final int code;

    private final String desc;

    RuleStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
