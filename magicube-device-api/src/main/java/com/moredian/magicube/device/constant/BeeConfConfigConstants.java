package com.moredian.magicube.device.constant;

/**
 * <AUTHOR>
 *
 * BeeConfNewClient
 * domainCode       groupId                     key                             field               value
 * device           environment                 100(appType)                                        hy
 * device           device_module               100(appType)                    1.0(version)        [35,36](device_model)
 * device           device_module               100(appType)                    1.1(version)        [35](device_model)
 * device           device_app                  100-1.0(apptype-version)        35                  [101,102,103](device_model_app)
 * device           device_app                  100-1.0(apptype-version)        36                  [102,104](app)
 * device           device_app                  100-1.1(apptype-version)        37                  [101,107](app)
 */
public class BeeConfConfigConstants {

    /*---------------------------------领域CODE  领域，比如门禁、考勤、设备、人、场  --------------------------------------*/
    public static final String DOMAIN_CODE = "device";



    /*--------------------------------groupId：领域下的分段，比如门禁多时间，门常开等 -------------------------------------*/

    /**
     * 设备模式
     */
    public static final String GROUP_DEVICE_MODULE = "device_module";

    public static final String GROUP_DEVICE_APP_TYPE= "device_app_type";

    /**
     * 设备模式应用
     */
    public static final String GROUP_DEVICE_APP = "device_app";

    /**
     * 设备运行环境
     */
    public static final String GROUP_ENVIRONMENT = "environment";
    /**
     * 设备场景激活数据
     */
    public static final String SYS_ACTIVATE_SCENE_SWITH_CONFIG_KEY = "sys_activate_scene_swith_config";

    /**
     * 设备管控授权机构白名单
     */
    public static final String DEVICE_AUTHORIZE_ORG_WHITE = "device_authorize_org_white";

    /**
     * 新激活设备，设备关联应用更具场景去关联（水牌统一激活开始）
     */
    public static final String DEVICE_APPCODE_BY_SCENE_TYPE = "device_appcode_by_scene_type";
}
