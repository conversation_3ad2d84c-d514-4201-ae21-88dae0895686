package com.moredian.magicube.device.dto.white;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 白名单设备
 *
 * <AUTHOR>
 */

@Data
public class WhiteDeviceDTO implements Serializable {

    private static final long serialVersionUID = 2621712317533751097L;

    /**
     * 设备sn
     */
    private String serialNumber;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * mac地址
     */
    private String macAddress2;

    /**
     * 设备激活私钥
     */
    private String privateKey;

    /**
     * 批量标记
     */
    private Integer batchFlag;

    /**
     * 设备激活状态 0-未激活 1-已激活
     */
    private Integer activityStatus;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 是否开放平台激活
     */
    private Boolean isOpenPlat;

    /**
     * 第三方设备Id
     */
    private String thirdDeviceId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备来源 1魔点设备 2三方设备
     */
    private Integer deviceSource;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

    /**
     * OABase,1:非钉，2：钉钉
     */
    private Integer oaBase;

    /**
     * SDKBase,1:非钉，2：钉钉
     */
    private Integer sdkBase;
}