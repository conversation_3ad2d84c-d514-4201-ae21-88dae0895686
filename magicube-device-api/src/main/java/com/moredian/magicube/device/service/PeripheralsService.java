package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.peripherals.InsertPeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.QueryPeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.UpdatePeripheralsDTO;

import java.util.List;

/**
 * 外设服务
 *
 * <AUTHOR>
 */
public interface PeripheralsService {

    /**
     * 增加外设
     *
     * @param dto 新增的外设信息
     * @return
     */
    ServiceResponse<Boolean> insert(InsertPeripheralsDTO dto);

    /**
     * 更新
     *
     * @param dto 更新的外设信息
     * @return
     */
    ServiceResponse<Boolean> update(UpdatePeripheralsDTO dto);

    /**
     * 根据条件查询外设信息列表
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<List<PeripheralsDTO>> listByCondition(QueryPeripheralsDTO dto);

    /**
     * 根据设备sn查询该机构下外设信息列表
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    ServiceResponse<List<PeripheralsDTO>> listByOrgIdAndDeviceSn(Long orgId, String deviceSn);

    /**
     * 外设断开
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> disConnect(UpdatePeripheralsDTO dto);

    /**
     * 手机端-连接操作
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> mobileConnect(UpdatePeripheralsDTO dto);

    /**
     * 手机端-断开以及解绑操作
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> mobileDisConnect(UpdatePeripheralsDTO dto);

    /**
     * 查询机构是否存在历史外设设备
     *
     * @param orgId 机构号
     * @return
     */
    ServiceResponse<Boolean> existHistoryPeripherals(Long orgId);

    /**
     * 根据设备sn列表获取外设信息列表
     *
     * @param orgId     机构号
     * @param deviceSns 设备sn列表
     * @return
     */
    ServiceResponse<List<PeripheralsDTO>> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns);
}
