package com.moredian.magicube.device.dto.qrcode;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Classname： QrCodeActivateRecordResponse
 * @Date: 2023/1/12 11:11 上午
 * @Author: _AF
 * @Description:
 */
@Data
public class QrCodeActivateRecordResponse implements Serializable {

    private Long id;

    private Long orgId;
    /**
     * 激活码id
     */
    private Long qrCodeId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    private Date gmtCreate;

}
