package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.dto.device.RebootDTO;
import com.moredian.magicube.device.dto.device.TransferDTO;

import java.util.List;

/**
 * 设备配置服务
 *
 * <AUTHOR>
 */
public interface DeviceConfigService {

    /**
     * 新增或者更新设备配置
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @param xml      配置信息
     * @return
     */
    ServiceResponse<Boolean> insertOrUpdate(Long orgId, Long deviceId, String xml);

    /**
     * 查询设备配置
     *
     * @param deviceSn 机构号
     * @return
     */
    ServiceResponse<String> getXmlConfig(String deviceSn);
}