package com.moredian.magicube.device.dto.global;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Auther: _AF
 * @Date: 12/28/21 15:11
 * @Description:
 */
@Data
public class GlobalConfigDTO implements Serializable {

    private static final long serialVersionUID = -6559461793165485844L;

    /**
     * id
     */
    private Long id;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 配置值
     */
    private String configValue;
    /**
     * 配置状态，0-停用，1-启用|默认
     */
    private Integer configStatus;

    private Date gmtCreate;

    private Date gmtModify;
}
