package com.moredian.magicube.device.dto.monitor;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/10/13
 */
@Getter
@Setter
@ToString
public class MonitorLogDto implements Serializable {

    private static final long serialVersionUID = 8492685546311842924L;

    /**
     * 监控事件码
     */
    private String eventCode;

    /**
     * 触发时间
     */
    private Long eventTime;

    /**
     * 监控额外数据/可用于写入备注
     */
    private String eventData;

    /**
     * 指标值
     */
    private BigDecimal indicatorNum;

    /**
     * 钉钉人员ID
     */
    private String personId;

    /**
     * 附件链接集合
     */
    private String files;
}
