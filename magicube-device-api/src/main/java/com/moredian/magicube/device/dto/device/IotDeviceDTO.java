package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.List;

import com.moredian.magicube.device.dto.device.iot.IotKeyPropertyDTO;
import lombok.Data;

/**
 * Iot设备信息
 *
 * <AUTHOR>
 */

@Data
public class IotDeviceDTO implements Serializable {

    private static final long serialVersionUID = -2451580353945349654L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 位置
     */
    private String position;

    /**
     * 第三方设备Id
     */
    private String tpId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 激活时间
     */
    private Long activeTime;

    /**
     * 空间树Id
     */
    private Long treeId;

    /**
     * 全路径名称
     */
    private String pathTreeName;

    /**
     * 空间树名称(酒店业务需要)
     */
    private String treeName;

    /**
     * 设备运行环境
     *
     * @see com.moredian.magicube.device.enums.EnvConfigEnum
     */
    private Integer environment;

    /**
     * 是否在线
     */
    private Boolean online;

    /**
     * 存在子设备
     */
    private Boolean existSubDevice;

    /**
     * 父设备sn
     */
    private String parentDeviceSn;

    /**
     * 父设备Id
     */
    private Long parentDeviceId;

    /**
     * 父设备全路径名称
     */
    private String parentPathTreeName;

    /**
     * 父设备位置
     */
    private String parentPosition;

    /**
     * 虚拟设备标识
     */
    private Boolean virtualDeviceFlag;

    /**
     * 子设备信息列表
     */
    private List<DeviceInfoDTO> subDevices;

    /**
     * 子设备数量
     */
    private Integer subDeviceNum;

    /**
     * 设备属性
     */
    private List<IotDevicePropertyDTO> deviceProperties;

    /**
     * 设备物模型元数据
     */
    private String metadata;

    /**
     * 空间id，iot设备的treeId和spaceId是一样的
     */
    private Long spaceId;

    /**
     * 全路径树Id（用/区分）
     */
    private String path;

    /**
     * 子设备信息列表
     */
    private List<RuleDTO> ruleDTOS;

    /**
     * 电表倍率
     */
    private Double rate;

    /**
     * 开合匝，0：关匝、1：合匝
     */
    private Integer meterSwitch;

    private IotKeyPropertyDTO iotKeyValueDTO;
}