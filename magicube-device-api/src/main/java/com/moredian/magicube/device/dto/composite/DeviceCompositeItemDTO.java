package com.moredian.magicube.device.dto.composite;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备分组条目
 *
 * <AUTHOR>
 */

@Data
public class DeviceCompositeItemDTO implements Serializable {

    private static final long serialVersionUID = -8204398571564439981L;

    /**
     * 主键Id
     */
    private Long deviceCompositeItemId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 所在组Id
     */
    private Long deviceCompositeId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备sn
     */
    private String deviceSn;
}
