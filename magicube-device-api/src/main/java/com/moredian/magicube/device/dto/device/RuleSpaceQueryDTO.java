package com.moredian.magicube.device.dto.device;

import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.magicube.common.enums.SpaceTypeEnums;
import com.moredian.magicube.device.error.DeviceErrorCode;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $Id: RuleSpaceQueryDTO.java, v 1.0 Exp $
 */
@Data
public class RuleSpaceQueryDTO implements Serializable {

    private static final long serialVersionUID = 5697725107399724965L;

    @NotNull(message = "orgId不能为空")
    private Long orgId;

    /**
     * ${@link SpaceTypeEnums}
     */
    @NotNull(message = "spaceType不能为空")
    private Integer spaceType;

    private Long ruleId;

    private Long templateId;
    /**
     * 开关：511(虚拟)，509(单键)
     * 插座：505(计量插座)
     * 空调：512(虚拟)
     */
    private List<Integer> deviceTypes;

    /**
     * 空间名称
     */
    private String spaceName;


    public void checkParamAvailable() {
        if (this.ruleId == null && this.templateId == null) {
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_RULE_TEMPLATE_REQUIRED, DeviceErrorCode.DEVICE_RULE_TEMPLATE_REQUIRED.getMessage()));
        }
    }
}
