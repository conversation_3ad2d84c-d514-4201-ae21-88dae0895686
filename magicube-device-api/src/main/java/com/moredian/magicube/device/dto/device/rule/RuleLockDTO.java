package com.moredian.magicube.device.dto.device.rule;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 规则编排锁参数
 * @create 2024-12-20 16:47
 */
@Data
public class RuleLockDTO implements Serializable {

    private static final long serialVersionUID = -2776635193266634612L;

    private Long ruleId;

    private String deviceId;

    /**
     * 锁类型，0：解锁，1：加锁
     */
    private Integer lockType;
}
