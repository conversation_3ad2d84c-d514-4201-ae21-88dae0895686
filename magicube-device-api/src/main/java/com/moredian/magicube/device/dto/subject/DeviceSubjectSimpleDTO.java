package com.moredian.magicube.device.dto.subject;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 设备主题基础信息
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
public class DeviceSubjectSimpleDTO implements Serializable {

    private static final long serialVersionUID = 7592160907716617071L;

    /**
     * 设备主题id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备主题名称
     */
    private String name;

    /**
     * 主题图片url列表
     */
    private List<String> imgUrls;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业logo图片url
     */
    private String logoImgUrl;

    /**
     * 主题类型 1-壁纸 2-屏保
     */
    private Integer type;

    /**
     * 模板类型：1-模板一 2-模板二
     */
    private Integer templateType;

    /**
     * 屏保信息（视频存在该信息）
     */
    private ScreenSaver screenSaver;
}
