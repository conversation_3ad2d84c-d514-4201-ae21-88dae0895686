package com.moredian.magicube.device.enums;

import com.google.common.collect.Lists;
import com.moredian.magicube.common.enums.DeviceType;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: DeviceTypeSwitchEnum.java, v 1.0 Exp $
 */
public enum DeviceTypeSwitchCodeEnum {

    /**
     * 设备类型 -》开关code
     */

    AIR_CONDITIONING_GATEWAY_SWITCH_CODE(DeviceType.AIR_CONDITIONING_GATEWAY, "ac_power", "", ""),

    TURN_ON_OFF_THREE_BUTTON_SWITCH_CODE(DeviceType.TURN_ON_OFF_THREE_BUTTON, "power", "", ""),
    TURN_ON_OFF_TWO_BUTTON_SWITCH_CODE(DeviceType.TURN_ON_OFF_TWO_BUTTON,  "power", "", ""),

    MEASUREMENT_SOCKET_SWITCH_CODE(DeviceType.MEASUREMENT_SOCKET,  "PowerSwitch", "", ""),

    TURN_ON_OFF_ONE_BUTTON_SWITCH_CODE(DeviceType.TURN_ON_OFF_ONE_BUTTON,  "power", "", ""),

    SWITCH_SWITCH_CODE(DeviceType.SWITCH,  "", "", "_power"),
    AIR_CONDITIONER_SWITCH_CODE(DeviceType.AIR_CONDITIONER, "", "ac_power_", ""),
    RAY_SINGLE_AIR_CONTROLLER_SWITCH_CODE(DeviceType.RAY_SINGLE_AIR_CONTROLLER, "pwr", "", ""),

    OTHER_IOT_DEVICE_TYPE_SWITCH_CODE(DeviceType.OTHER_IOT_DEVICE_TYPE,  "power", "", ""),
    ;

    @Getter
    private final DeviceType deviceType;

    /**
     * 开关字段code
     */
    @Getter
    private final String iotPropertySwitchCode;

    @Getter
    private final String iotPropertySwitchPrefix;

    @Getter
    private final String iotPropertySwitchSuffix;

    DeviceTypeSwitchCodeEnum(DeviceType deviceType, String iotPropertySwitchCode, String iotPropertySwitchPrefix, String iotPropertySwitchSuffix) {
        this.deviceType = deviceType;
        this.iotPropertySwitchCode = iotPropertySwitchCode;
        this.iotPropertySwitchPrefix = iotPropertySwitchPrefix;
        this.iotPropertySwitchSuffix = iotPropertySwitchSuffix;
    }

    public static String getSwitchPropertyId(Integer deviceType, String group) {
        List<DeviceTypeSwitchCodeEnum> switchCodeEnums = Lists.newArrayList(SWITCH_SWITCH_CODE);
        for (DeviceTypeSwitchCodeEnum item : switchCodeEnums) {
            if (item.deviceType.getValue() == deviceType) {
                return group + item.iotPropertySwitchSuffix;
            }
        }

        switchCodeEnums = Lists.newArrayList(AIR_CONDITIONER_SWITCH_CODE);
        for (DeviceTypeSwitchCodeEnum item : switchCodeEnums) {
            if (item.deviceType.getValue() == deviceType) {
                return item.iotPropertySwitchPrefix + group;
            }
        }

        switchCodeEnums = Lists.newArrayList(AIR_CONDITIONING_GATEWAY_SWITCH_CODE, TURN_ON_OFF_THREE_BUTTON_SWITCH_CODE,RAY_SINGLE_AIR_CONTROLLER_SWITCH_CODE,
                        TURN_ON_OFF_TWO_BUTTON_SWITCH_CODE, MEASUREMENT_SOCKET_SWITCH_CODE, TURN_ON_OFF_ONE_BUTTON_SWITCH_CODE);
        for (DeviceTypeSwitchCodeEnum item : switchCodeEnums) {
            if (item.deviceType.getValue() == deviceType) {
                return item.iotPropertySwitchCode;
            }
        }

        return OTHER_IOT_DEVICE_TYPE_SWITCH_CODE.iotPropertySwitchCode;
    }
}
