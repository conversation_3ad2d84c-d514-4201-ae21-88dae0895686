package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.composite.DeviceCompositeGroupRelationAddReq;
import com.moredian.magicube.device.dto.composite.SimpleDeviceCompositeGroupDTO;
import java.util.List;

/**
 * 权限组设备组关联服务
 *
 * <AUTHOR>
 * @date 2024/11/21 09:18
 */
public interface DeviceCompositeGroupRelationService {


    /**
     * 根据组id列表查询组设备信息
     *
     * @param orgId    机构Id
     * @param groupIds 通行权限组ID
     * @return SimpleDeviceCompositeGroupResponse 实体
     */
    ServiceResponse<List<SimpleDeviceCompositeGroupDTO>> findDeviceCompositeGroupByGroupIds(Long orgId, List<Long> groupIds);

    /**
     * 根据组id查询组设备信息
     *
     * @param orgId   机构Id
     * @param groupId 通行权限组ID
     * @return SimpleDeviceCompositeGroupResponse 实体
     */
    ServiceResponse<SimpleDeviceCompositeGroupDTO> getDeviceCompositeGroupByGroupId(Long orgId, Long groupId);

    /**
     * 添加通行组/设备/设备组关系
     * @param req 请求
     * @return Boolean
     */
    ServiceResponse<Boolean> resetDeviceCompositeGroupRelation(
        DeviceCompositeGroupRelationAddReq req);

    /**
     * 根据组id列表查询组设备信息(deviceList 非选择设备+组里的设备组合)
     *
     * @param orgId    机构Id
     * @param groupIds 通行权限组ID
     * @return SimpleDeviceCompositeGroupResponse 实体
     */
    ServiceResponse<List<SimpleDeviceCompositeGroupDTO>> findDeviceCompositeGroupByGroupIdsNonAllDevice(Long orgId, List<Long> groupIds);
}
