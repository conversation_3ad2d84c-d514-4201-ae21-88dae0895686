package com.moredian.magicube.device.dto.accessory;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 配件绑定请求
 *
 * <AUTHOR>
 * @since 2021/12/8
 */
@Data
public class BindAccessoryDTO implements Serializable {

    private static final long serialVersionUID = 2204161224795822054L;

    /**
     * 机构ID
     */
    @NotNull(message = "机构id不能为空")
    private Long orgId;

    /**
     * 配件sn
     */
    @NotNull(message = "配件sn不能为空")
    private String accessorySn;

    /**
     * 配件名
     */
    private String accessoryName;

    /**
     * 配件类型
     */
    @NotNull(message = "配件类型不能为空")
    private Integer accessoryType;

    /**
     * 关联设备ID
     */
    @NotNull(message = "设备id不能为空")
    private Long deviceId;

    /**
     * 关联设备sn
     */
    @NotNull(message = "设备sn不能为空")
    private String deviceSn;

    /**
     * 匹配类型：0-不匹配，1-匹配
     */
    @NotNull(message = "匹配类型不能为空")
    private Integer matchStatus;

}
