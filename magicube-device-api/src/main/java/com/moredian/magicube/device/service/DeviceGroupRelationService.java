
package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.common.enums.GroupAppType;
import com.moredian.magicube.device.dto.group.DeviceGroupDTO;
import com.moredian.magicube.device.dto.group.DeviceGroupPersonCountDTO;
import com.moredian.magicube.device.dto.group.GroupDeviceSizeDTO;
import com.moredian.magicube.device.dto.group.GroupInfoDTO;
import com.moredian.magicube.device.dto.group.QueryGroupDTO;
import com.moredian.magicube.device.dto.group.ResetGroupRelationDTO;
import com.moredian.magicube.device.dto.group.SimpleDeviceGroupDTO;
import java.util.List;
import java.util.Map;

/**
 * 设备和组关系接口
 *
 * <AUTHOR>
 */
public interface DeviceGroupRelationService {

    /**
     * 根据机构号和组Id获取设备Id列表 替换findDeviceIdByGroupId
     *
     * @param orgId   机构号
     * @param groupId 组Id
     * @return 设备Id列表
     */
    ServiceResponse<List<Long>> listDeviceIdByOrgIdAndGroupId(Long orgId, Long groupId);

    /**
     * 根据机构号和组Id列表获取设备Id列表 替换findDeviceIdByGroupIds
     *
     * @param orgId    机构号
     * @param groupIds 组Id列表
     * @return 设备Id列表
     */
    ServiceResponse<List<Long>> listDeviceIdByOrgIdAndGroupIds(Long orgId, List<Long> groupIds);

    /**
     * 查询设备Id列表 替换listDeviceIdsByGroupId
     *
     * @param orgId   机构号
     * @param groupId 组Id
     * @param offset  开始数(非必填)
     * @param limit   查询数量(非必填)
     * @return
     */
    ServiceResponse<List<Long>> listDeviceIdByCondition(Long orgId, Long groupId, int offset,
        int limit);

    /**
     * 获取组Id对设备Id列表map 替换findDeviceIdsByGroupIds
     *
     * @param orgId    机构号
     * @param groupIds 组Id列表
     * @return
     */
    ServiceResponse<Map<Long, List<Long>>> getGroupIdToDeviceIdsMapByOrgIdAndGroupIds(Long orgId,
        List<Long> groupIds);

    /**
     * 获取设备Id对组Id列表map 替换findGroupIdsByDeviceIds
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @param appType   组app类型
     * @return
     */
    ServiceResponse<Map<Long, List<Long>>> getDeviceIdToGroupIdsByOrgIdAndDeviceIds(Long orgId,
        List<Long> deviceIds, Integer appType);

    /**
     * 获取组Id列表 替换findGroupIdByDeviceId,findGroupIdByDeviceIdAppType,findDeviceGroupByDeviceIds
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<List<Long>> listGroupIdByCondition(QueryGroupDTO dto);

    /**
     * 获取组名称列表 替换findGroupNameByDeviceId
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<List<String>> listGroupNameByCondition(QueryGroupDTO dto);

    /**
     * 获取组信息列表 替换findGroupByDeviceId
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<List<GroupInfoDTO>> listGroupByCondition(QueryGroupDTO dto);

    /**
     * 根据条件删除关系 替换deleteByDevice
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<Boolean> deleteByCondition(Long orgId, List<Long> deviceIds);

    ServiceResponse<Boolean> deleteByDeviceIds(Long orgId, List<Long> deviceIds, List<Long> groupIds);

    /**
     * 重置设备和多个组的关系 替换resetDeviceGroupRelation
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @param groupIds 组Id列表
     * @param appType  权限组应用类型
     * @return
     * @see GroupAppType
     */
    ServiceResponse<Boolean> resetRelationByDeviceId(Long orgId, Long deviceId,
        List<Long> groupIds, Integer appType);

    /**
     *  增量添加多个设备和多个组的关系
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @param groupIds  组Id列表
     * @param appType   权限组应用类型
     * @return
     */
    ServiceResponse<Boolean> insertRelationByDeviceIds(Long orgId, List<Long> deviceIds, List<Long> groupIds, Integer appType);

    /**
     * 重置一个组和多台设备的关系 替换resetRelationOfDeviceGroup
     *
     * @param orgId     机构号
     * @param groupId   组Id
     * @param deviceIds 设备Id列表
     * @param appType   权限组应用类型
     * @return
     * @see GroupAppType
     */
    ServiceResponse<Boolean> resetRelationByGroupId(Long orgId, Long groupId, List<Long> deviceIds,
        Integer appType);

    /**
     * 重置一个组和多台设备的关系 替换resetRelationOfDeviceGroup
     * 由于增量事件中心需要过滤新增绑定与删除绑定的设备，所以返回参数中需要更改，故拷出一个接口来修改
     *
     * @param orgId     机构号
     * @param groupId   组Id
     * @param deviceIds 设备Id列表
     * @param appType   权限组应用类型
     * @return
     * @see GroupAppType
     */
    ServiceResponse<ResetGroupRelationDTO> resetRelationByGroupIdAndDeviceIdList(Long orgId, Long groupId, List<Long> deviceIds,
                                                                                 Integer appType);


    /**
     * 查询指定设备绑定的去重成员总数
     * 说明：可以指定群组类型，也可以不指定群组类型，当不指定时就默认查询全部群组人员去重总数
     *
     * @param dto
     * @return
     */
    ServiceResponse<Integer> distinctCountDeviceGroupPerson(DeviceGroupPersonCountDTO dto);

    List<DeviceGroupDTO> findRelationByGroupId(Long orgId, Long groupId);

    ServiceResponse<List<GroupDeviceSizeDTO>> countDeviceSizeGroupByGroupId(Long orgId, List<Long> groupIdList);

    /**
     * 根据组id列表查询组设备信息
     *
     * @param orgId
     * @param groupIds
     * @return
     */
    ServiceResponse<List<SimpleDeviceGroupDTO>> findDeviceGroupByGroupIds(Long orgId, List<Long> groupIds);
}
