package com.moredian.magicube.device.dto.qrcode;

import java.io.Serializable;
import lombok.Data;

/**
 * 设备选择空间空间节点
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
public class DeviceChooseSpaceDTO implements Serializable {

    private static final long serialVersionUID = -1853367632542737633L;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 空间树Id
     */
    private Long treeId;

    /**
     * 空间属性id 可以为空
     */
    private Long propertyTreeNodeId;

    /**
     * 出入方向 1入口 2出口
     */
    private Integer direction;

    /**
     * 设备位置
     */
    private String position;

    /**
     * 父deviceSn
     */
    private String parentDeviceSn;
}
