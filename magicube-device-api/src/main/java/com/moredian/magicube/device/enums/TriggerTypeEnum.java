package com.moredian.magicube.device.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version $Id: TriggerTypeEnum.java, v 1.0 Exp $
 * @desc 触发类型
 */
@Getter
public enum TriggerTypeEnum {
    /**
     * 触发类型
     */
    TIMER(1, "定时触发"),
    DEVICE(2, "设备触发"),
    HUMAN(3, "有人触发")

    ;
    private final int code;

    private final String desc;

    TriggerTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
