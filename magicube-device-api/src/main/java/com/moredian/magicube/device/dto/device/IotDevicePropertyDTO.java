package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class IotDevicePropertyDTO implements Serializable {

    private static final long serialVersionUID = -5767303516074901227L;

    private DataObject data;

    @Data
    public static class DataObject implements Serializable{

        private static final long serialVersionUID = -576731236074901227L;

        /**
         * 属性值
         */
        private Properties value;
        /**
         * 时间字符串：yyyy-MM-dd HH:mm:ss
         */
        private String timeString;
        /**
         * 时间戳
         */
        private Long timestamp;
    }

    @Data
    public static class Properties implements Serializable{

        private static final long serialVersionUID = -576731236074911227L;

        /**
         * 属性id
         */
        private String id;

        /**
         * 设备sn
         */
        private String deviceSn;

        /**
         * 属性标识
         */
        private String property;

        /**
         * 属性名称
         */
        private String propertyName;

        /**
         * 属性类型
         */
        private String type;

        /**
         * 如果是枚举值，需要解析出枚举值的个数
         */
        private Integer enumsNum;

        /**
         * 属性值
         */
        private String value;

        /**
         * 格式化后的属性值
         */
        private String formatValue;

        /**
         * 创建时间
         */
        private Long createTime;

        /**
         * 时间戳
         */
        private Long timestamp;
    }
}