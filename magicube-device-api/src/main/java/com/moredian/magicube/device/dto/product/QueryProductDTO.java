package com.moredian.magicube.device.dto.product;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2025/1/6
 */
@Data
public class QueryProductDTO implements Serializable {

    private static final long serialVersionUID = 6035082863622532377L;

    private Long id;

    /*
     * 父产品标签code (产品分类的父标签是0)
     */
    private String parentProductTagCode;

    /*
     * 标签类型 1：产品分类 2：产品族系 3：产品型号（系列）
     */
    private Integer tagType;

    /*
     * 产品标签code
     */
    private String productTagCode;

    /*
     * 产品标签名称
     */
    private String productTagName;
}
