
package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.activate.*;
import com.moredian.magicube.device.dto.device.DeviceActiveStateChangeDTO;
import com.moredian.magicube.device.dto.device.DeviceAuthDetailDTO;
import com.moredian.magicube.device.dto.device.DeviceBaseInfoDTO;
import com.moredian.magicube.device.dto.qrcode.DeviceActiveStateDTO;
import com.moredian.magicube.device.dto.upgrade.MergeUpdateAppToLastResponse;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 设备激活服务接口
 *
 * <AUTHOR>
 */

public interface ActivityService {

    /**
     * 生成激活码接口：获取设备激活码接口 设备调用：根据返回的二维码串生成二维码（只有塔下会调用,塔上激活流程不同）
     *
     * @param dto 生成激活码参数
     * @return
     */
    ServiceResponse<QrCodeDTO> generateQrCode(GenerateQrCodeDTO dto);

    /**
     * 根据二维码获取对应状态（只有塔下会调用,塔上激活流程不同）
     *
     * @param dto 二维码
     * @return
     */
    ServiceResponse<QrCodeStatusDTO> getStatusByQrCode(QueryQrCodeStatusDTO dto);

    /**
     * 修改扫码状态（只有塔下会调用,塔上激活流程不同）
     *
     * @param dto 二维码信息
     * @return
     */
    ServiceResponse<QrCodeStatusDTO> updateQrCodeStatus(UpdateQrCodeStatusDTO dto);

    /**
     * 根据sn修改扫码状态，开放平台使用（只有塔下会调用,塔上激活流程不同）
     *
     * @param orgId     机构号
     * @param accountId 账号Id
     * @param deviceSn  设备sn
     * @return
     */
    ServiceResponse<QrCodeStatusDTO> updateQrCodeStatusBySn(Long orgId, Long accountId,
        String deviceSn);

    /**
     * 根据二维码获取激活状态（只有塔下会调用,塔上激活流程不同）
     *
     * @param qrCode 二维码
     * @return
     */
    ServiceResponse<QueryActivationStatusDTO> getActivityStatusByQrCode(String qrCode);

    /**
     * 根据第三方设备Id获取激活状态
     *
     * @param thirdPartyDeviceId 第三方设备Id
     * @return
     */
    ServiceResponse<QueryActivationStatusDTO> getThirdPartyActivityStatusByTpId(
        String thirdPartyDeviceId);

    /**
     * 激活接口
     *
     * @param dto 设备激活信息
     * @return
     */
    ServiceResponse<ActivateDeviceResultDTO> activate(ActivateDeviceDTO dto);

    ServiceResponse<Boolean> updateThirdPartyDeviceInfo(ThirdPartyDeviceInfoDTO dto);

    /**
     * 解绑设备
     *
     * @param deviceSn 设备sn
     * @return
     */
    ServiceResponse<Boolean> unbindDevice(String deviceSn);

    /**
     * 获取激活信息
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<ActivateDeviceResultDTO> getActivateInfo(QueryActivateInfoDTO dto);

    /**
     * 设备激活后升级
     *
     * @param request
     * @return
     */
    ServiceResponse<Boolean> enforceUpgradeDevice(List<ActivateUpgradeDTO> request);

    /**
     * 设备激活后升级——带返回值
     *
     * @param requestList
     * @return
     */
    ServiceResponse<MergeUpdateAppToLastResponse> enforceUpgradeDeviceReturn(List<ActivateUpgradeDTO> requestList);

    /**
     * 清除设备激活状态
     * @param deviceSn
     * @return
     */
    ServiceResponse<Boolean> deActivateDevice(String deviceSn);

    /**
     * 获取设备的初始化基本信息
     */
    ServiceResponse<DeviceBaseInfoDTO> deviceInitConfig(String deviceSn);

    /**
     * 根据设备sn获取设备授权信息
     */
    ServiceResponse<DeviceAuthDetailDTO> authDetailBySn(@NotBlank String deviceSn);

    /**
     * 获取设备激活状态信息
     * @param deviceSn
     * @return
     */
    ServiceResponse<DeviceActiveStateDTO> getDeviceActiveState(String deviceSn);

    /**
     * 设备激活状态变更
     *
     * @param deviceSn 设备sn
     * @param state    设备状态
     * @param bindOrgUrl    钉钉绑定团队地址
     * @return
     */
    ServiceResponse<Boolean> deviceActiveStateChange(String deviceSn, Integer state, String bindOrgUrl);

    /**
     * 设备激活状态变更
     */
    ServiceResponse<Boolean> deviceActiveStateChange(DeviceActiveStateChangeDTO dto);

    /**
     * 修改设备运行模式
     * @param deviceSn
     * @return
     */
    ServiceResponse<Boolean> updateSceneType(String deviceSn, Integer sceneType, Long memberId);

    /**
     * 修改设备运行模式(切换模式用-不发激活消息)
     * @param deviceSn
     * @return
     */
    ServiceResponse<Boolean> updateSceneTypeBySwitch(String deviceSn, Integer sceneType, Long memberId);

    /**
     * 校验机构是否免管控
     * @param tpId 钉钉机构Id
     * @return
     */
    ServiceResponse<Boolean> checkOrgAuthorize(String tpId);
}
