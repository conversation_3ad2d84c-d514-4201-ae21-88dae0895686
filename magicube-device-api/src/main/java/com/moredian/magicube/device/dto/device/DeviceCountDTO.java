package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description：设备管理-设备统计接口
 * @date ：2024/07/27 10:21
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceCountDTO implements Serializable {

    private Integer smartDeviceNum;

    private Integer iotDeviceNum;
}
