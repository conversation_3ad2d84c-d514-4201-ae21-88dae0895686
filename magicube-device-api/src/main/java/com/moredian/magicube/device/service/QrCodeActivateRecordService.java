package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.qrcode.QrCodeActivateRecordResponse;
import java.util.List;

/**
 * @Classname： QrCodeActivateRecordService
 * @Date: 2023/1/12 11:17 上午
 * @Author: _AF
 * @Description:
 */
public interface QrCodeActivateRecordService {

    /**
     * 查询列表
     *
     * @param orgId
     * @param qrCodeId
     * @return
     */
    ServiceResponse<List<QrCodeActivateRecordResponse>> listByQrCodeId(Long orgId, Long qrCodeId);

    /**
     * 查询激活记录
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    ServiceResponse<QrCodeActivateRecordResponse> getByDeviceId(Long orgId, Long deviceId);

    /**
     * 查询机构下最新的反扫激活记录
     *
     * @param orgId
     * @return
     */
    ServiceResponse<QrCodeActivateRecordResponse> getNewestRecordByOrgId(Long orgId);
}
