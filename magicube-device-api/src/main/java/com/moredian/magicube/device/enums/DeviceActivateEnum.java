package com.moredian.magicube.device.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date ：2024/11/30 14:27
 */
@Getter
public enum DeviceActivateEnum {

    INACTIVATED("未激活", 0),
    ACTIVATED("已激活", 1);

    private final String desc;

    private final int value;

    DeviceActivateEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public static String getDesc(int value) {
        for (DeviceActivateEnum item : DeviceActivateEnum.values()) {
            if (item.value == value) {
                return item.desc;
            }
        }
        return "";
    }
}
