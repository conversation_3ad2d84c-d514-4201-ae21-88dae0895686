package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.ThirdDeviceRelationDTO;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
public interface ThirdDeviceRelationService {


    /**
     * @param tpId 三方id
     * @param tpType 三方id类型
     * @see com.moredian.magicube.common.enums.TpType
     * @return 三方设备信息
     */
    ServiceResponse<ThirdDeviceRelationDTO> getByTpId(String tpId, Integer tpType);


}
