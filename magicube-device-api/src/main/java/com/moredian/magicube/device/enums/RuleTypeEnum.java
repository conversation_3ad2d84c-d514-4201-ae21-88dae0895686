package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @version $Id: RuleTypeEnum.java, v 1.0 Exp $
 * @desc 规则类型
 */
public enum RuleTypeEnum {
    /**
     * 规则类型
     */
    DEFAULT(1, "默认设备规则"),
    CUSTOM(2, "自定义设备规则"),

            ;
    private final int code;

    private final String desc;

    RuleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
