package com.moredian.magicube.device.dto.activate;

import lombok.Data;

import java.io.Serializable;

/**
 * 二维码状态信息
 *
 * <AUTHOR>
 */

@Data
public class QrCodeStatusDTO implements Serializable {

    private static final long serialVersionUID = -6678991666987768195L;

    /**
     * 二维码状态 0-未扫码 1-已扫描 2-已失效
     */
    private int statusCode;

    /**
     * 二维码信息：二维码已失效
     */
    private String message;

    /**
     * 设备sn
     */
    private String sn;
}
