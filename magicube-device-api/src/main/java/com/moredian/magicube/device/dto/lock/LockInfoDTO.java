package com.moredian.magicube.device.dto.lock;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description 锁信息
 * @create 2025-03-31 13:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LockInfoDTO implements java.io.Serializable{

    private static final long serialVersionUID = 2584733178606579939L;

    /**
     * 锁sn
     */
    private String deviceSn;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 锁详情信息
     */
    private LockInfo lockInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LockInfo implements java.io.Serializable{

        private static final long serialVersionUID = 3361793741988405853L;

        private String lockId;

        /**
         * 锁电量
         */
        private Integer electricQuantity;

        /**
         * 管理员密码
         */
        private String noKeyPwd;

        /**
         * 在离线
         */
        private Boolean online;

        /**
         * 通行密码id
         */
        private Integer keyboardPwdId;

        /**
         * 通行密码
         */
        private String keyboardPwd;

        /**
         * 元石deviceId
         */
        private Long deviceId;
    }

}
