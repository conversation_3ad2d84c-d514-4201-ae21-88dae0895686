package com.moredian.magicube.device.dto.version;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/11/3
 */
@Getter
@Setter
@ToString
public class StockDeviceVersionDTO implements Serializable {

    private static final long serialVersionUID = 8340442607483272952L;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * apk类型
     */
    private Integer appType;

    /**
     * apk版本号
     */
    private Integer appVersionCode;

    /**
     * rom类型
     */
    private Integer romType;

    /**
     * rom版本号
     */
    private Integer romVersionCode;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;

}
