package com.moredian.magicube.device.dto.composite;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询设备分组条件
 *
 * <AUTHOR>
 */

@Data
public class QueryDeviceCompositeDTO implements Serializable {

    private static final long serialVersionUID = -1636708137787378860L;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 父设备组id
     */
    private Long parentId;

    /**
     * 父设备组id列表（有权限的父设备组Id）
     */
    List<Long> parentIds;

    /**
     * 类型，使用，0-通用
     */
    private Integer bizType;

    /**
     * 所有设备标识
     */
    private Boolean allDeviceFlag;

    /**
     * 设备Id列表（有权限的设备Id）
     */
    List<Long> deviceIds;

    /**
     * @see com.moredian.magicube.common.enums.SceneTypeEnums
     */
    private Integer sceneType;

    /**
     * 设备组名称or设备名称模糊搜索
     */
    private String keyWords;

    /**
     * 设备组列表（有权限的设备组Id）
     */
    List<Long> compositeIds;

    /**
     * 设备组标识（false:无有权限的设备组，直接返回null）
     */
    private Boolean compositeFlag;

    /**
     * 设备类型列表
     */
    private List<Integer> deviceTypes;
}
