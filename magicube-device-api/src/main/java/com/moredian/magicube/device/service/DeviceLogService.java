
package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.log.DeviceLogDTO;

import java.util.List;

/**
 * 设备日志服务
 *
 * <AUTHOR>
 */

public interface DeviceLogService {

    /**
     * 新增设备日志
     *
     * @param dto 设备日志信息
     * @return 设备日志Id
     */
    ServiceResponse<Long> insert(DeviceLogDTO dto);

    /**
     * 更新设备激活日志的操作人Id
     *
     * @param dto 设备日志信息
     * @return 设备日志Id
     */
    ServiceResponse<Boolean> updateDeviceLogOperatorId(DeviceLogDTO dto);

    /**
     * 根据设备日志Id获取设备日志信息
     *
     * @param orgId       机构号
     * @param deviceLogId 设备日志Id
     * @return 设备日志信息
     */
    ServiceResponse<DeviceLogDTO> getByOrgIdAndId(Long orgId, Long deviceLogId);

    /**
     * 根据设备Id获取设备日志信息列表
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return 设备日志信息列表
     */
    ServiceResponse<List<DeviceLogDTO>> listByOrgIdAndDeviceId(Long orgId, Long deviceId);

    /**
     * 根据机构Id和设备sn获取设备日志信息列表
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return 设备日志信息列表
     */
    ServiceResponse<List<DeviceLogDTO>> listByOrgIdAndDeviceSn(Long orgId, String deviceSn);

    /**
     * 根据设备sn获取设备日志信息列表
     *
     * @param deviceSn 设备sn
     * @return 设备日志信息列表
     */
    ServiceResponse<List<DeviceLogDTO>> listByDeviceSn(String deviceSn);

    /**
     * 根据机构号获取设备日志信息类型去重列表
     *
     * @param orgId 机构号
     * @return 去重设备类型列表
     */
    ServiceResponse<List<Integer>> listDistinctDeviceTypeByOrgId(Long orgId);

    /**
     * 根据机构号统计设备日志数量
     *
     * @param orgId 机构号
     * @return 设备日志数量
     */
    ServiceResponse<Integer> countByOrgId(Long orgId);
}

