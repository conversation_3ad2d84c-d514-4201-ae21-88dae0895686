package com.moredian.magicube.device.dto.mqtt;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/7/14
 */
@Getter
@Setter
@ToString
public class TransferMessageInfoDTO<T> implements Serializable {

    private static final long serialVersionUID = -7915582067260293259L;

    private String seqId;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 描述事件的信息
     */
    private String message;

    /**
     * 数据，根据需要自己组装内容
     */
    private T data;

    /**
     * 事件紧急程度
     */
    private Integer Severity;

    /**
     * 事件版本号，用于重复更新的case
     */
    private Integer version;
}
