package com.moredian.magicube.device.dto.group;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import com.moredian.magicube.common.enums.GroupAppType;

/**
 * 设备和组关系查询条件
 *
 * <AUTHOR>
 */

@Data
public class QueryGroupDTO implements Serializable {

    private static final long serialVersionUID = 779419288607958818L;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private List<Long> deviceIds;

    /**
     * 组类型(非必填，不填代表查全部)
     *
     * @see GroupAppType
     */
    private Integer appType;

    /**
     * 组类型列表
     */
    private List<Integer> appTypeList;
}
