package com.moredian.magicube.device.dto.version;

import java.io.Serializable;
import lombok.Data;

@Data
public class AppVersionIncreWithAppVersionInfoDTO implements Serializable {

    private static final long serialVersionUID = -0L;

    //是否需要升级
    private Boolean isUpdate;

    //是否增量包
    private Boolean isIncre;

    //是否增量包
    private Boolean isEnforceUpdate;

    //之前的versionCode
    private Integer preVersionCode;

    //将要升级到的版本
    private Integer versionCode;

    //文件连接
    private String url;

    //版本名
    private String versionName;

    //版本描述
    private String versionDesc;

    //md5值
    private String md5Value;

    private Integer storageLocation;

    /**
     * 扩展信息json，兼容门锁各个app的升级包信息
     */
    private String extendInfo;

    /**
     * rom里面自带的apk类型
     */
    private Integer apkAppType;

    /**
     * rom里面自带的apk版本号
     */
    private Integer apkVersionCode;
}