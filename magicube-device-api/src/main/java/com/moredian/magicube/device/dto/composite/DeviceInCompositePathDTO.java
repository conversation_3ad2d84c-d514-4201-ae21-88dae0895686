package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/28 11:25
 */
@Data
public class DeviceInCompositePathDTO implements Serializable {

    private static final long serialVersionUID = -3988393176361451486L;

    private Long deviceId;

    private String deviceName;

    private String deviceSn;

    private Integer deviceType;

    /**
     * 设备在设备的code path
     * （如果返回空，就是这个设备无设备组）
     */
    private List<String> pathList = new ArrayList<String>();

}
