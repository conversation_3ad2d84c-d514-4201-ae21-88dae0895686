package com.moredian.magicube.device.service;


import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.DeviceAppRelationConfigDTO;
import com.moredian.magicube.device.dto.device.DeviceAppRelationFixedConfigDTO;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;

import java.util.List;

public interface DeviceAppRelationFixedConfigService {



    /**
     * 分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<Pagination<DeviceAppRelationFixedConfigDTO>> listPage(QueryDeviceAppRelationConfigDTO dto);


    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    ServiceResponse<List<DeviceAppRelationFixedConfigDTO>> listAll();


    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    ServiceResponse<DeviceAppRelationFixedConfigDTO> getById(Long id);
	
    /**
     * 新增
     *
     * @param deviceAppRelationFixedConfigDTO 新增的记录
     * @return 主键id
     */
    ServiceResponse<Long> insert(DeviceAppRelationFixedConfigDTO deviceAppRelationFixedConfigDTO);
	
    /**
     * 修改所有字段
     *
     * @param deviceAppRelationFixedConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> update(DeviceAppRelationFixedConfigDTO deviceAppRelationFixedConfigDTO);
	
    /**
     * 修改不为null字段
     *
     * @param deviceAppRelationFixedConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> updateSelective(DeviceAppRelationFixedConfigDTO deviceAppRelationFixedConfigDTO);
	
    /**
     * 根据主键删除
     *
     * @param id 待删除的记录
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> delete(Long id);

    /**
     * 根据设备SN删除
     *
     * @param deviceSn 设备sn
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> deleteByDeviceSn(String  deviceSn);

    /**
     * 根据设备SN删除
     *
     * @param dto 待删除的记录
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> deleteByBizTypeAndId(DeviceAppRelationFixedConfigDTO  dto);

}