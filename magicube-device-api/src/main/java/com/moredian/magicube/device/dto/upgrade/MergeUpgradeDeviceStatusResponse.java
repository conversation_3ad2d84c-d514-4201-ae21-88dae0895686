package com.moredian.magicube.device.dto.upgrade;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class MergeUpgradeDeviceStatusResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	//10准备开始 20 升级中 30 成功 40 失败
	private Integer globalStatus;
	
	private List<DeviceUpgradeStatusModel> statusList = new ArrayList<DeviceUpgradeStatusModel>();

	public Integer getGlobalStatus() {
		return globalStatus;
	}

	public void setGlobalStatus(Integer globalStatus) {
		this.globalStatus = globalStatus;
	}

	public List<DeviceUpgradeStatusModel> getStatusList() {
		return statusList;
	}

	public void setStatusList(List<DeviceUpgradeStatusModel> statusList) {
		this.statusList = statusList;
	}

}
