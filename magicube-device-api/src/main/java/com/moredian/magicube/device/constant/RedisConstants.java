package com.moredian.magicube.device.constant;

/**
 * redis缓存key
 *
 * <AUTHOR>
 */
public class RedisConstants {

    /**
     * 缓存key公共前缀
     */
    public static final String COMMON_CACHE_KEY_PREFIX = "moredian:device:cache:";


    /**
     * 设备重新配网状态缓存key
     * <li>%s-机构Id</li>
     * <li>%s-设备Sn</li>
     */
    public static final String DEVICE_RESET_NETWORK_STATUS = COMMON_CACHE_KEY_PREFIX + "reset.network.status:%s:%s";


    /**
     * 设备属性配置
     * <li>%s-设备类型id</li>
     */
    public static final String DEVICE_TYPE_PROPERTY_CONFIG = COMMON_CACHE_KEY_PREFIX + "device-config:%s";


    /**
     * 规则编排不能连续修改时间
     * <li>%s-规则id</li>
     */
    public static final String JETLINK_RULE_ARRANGE = COMMON_CACHE_KEY_PREFIX + "rule-arrange:%s";

    /**
     * 规则编排锁
     */
    public static final String JETLINK_RULE_ARRANGE_LOCK = "rule-arrange:ruleLock:%s";

    /**
     * 通通锁开放平台 token
     */
    public static final String TT_LOCK_ACCESS_TOKEN = COMMON_CACHE_KEY_PREFIX + "ttlock:accessToken";

    /**
     * 通通锁设备网络状态缓存
     */
    public static final String TT_LOCK_NETWORK_INFO = COMMON_CACHE_KEY_PREFIX + "ttlock:network:%s";

    /**
     * 通通锁lockData
     */
    public static final String TT_LOCK_DATA = COMMON_CACHE_KEY_PREFIX + "ttlock:lockData:%s";

    /**
     * 设备组上次和设备的历史关系:机构ID:设备组ID
     */
    public static final String DEVICE_COMPOSITE_LAST_RELATION = "device_composite_last_relation:%s:%s";


    public static final long DEVICE_RESET_NETWORK_TIME_OUT = 300L;

    public static String getKey(String keyFormat, Object... params) {
        return String.format(keyFormat, params);
    }
}
