package com.moredian.magicube.device.dto.blu;

import java.io.Serializable;

public class DeviceBLuConnectResp  implements Serializable {
    private static final long serialVersionUID = 3960332750447416245L;
    /**
     * "时间戳（秒）"
     */
    private long timestamp;
    /**
     * 随机数
     */
    private long nonce;
    /**
     * 新签名
     */
    private String sign;
    /**
     * 新加密串
     */
    private String encryptStr;

    private String deviceSn;


    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getNonce() {
        return nonce;
    }

    public void setNonce(long nonce) {
        this.nonce = nonce;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getEncryptStr() {
        return encryptStr;
    }

    public void setEncryptStr(String encryptStr) {
        this.encryptStr = encryptStr;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
}
