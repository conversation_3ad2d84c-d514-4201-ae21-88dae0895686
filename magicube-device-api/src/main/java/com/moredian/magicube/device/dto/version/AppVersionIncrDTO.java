package com.moredian.magicube.device.dto.version;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class AppVersionIncrDTO implements Serializable {

    private static final long serialVersionUID = -0L;

    //主键id
    private Long appVersionIncreId;

    //之前的versionCode
    private Integer preVersionCode;

    //将要升级到的版本
    private Long versionId;

    //包路径
    private String packageUrl;

    //创建人
    private Long userCreate;

    //创建时间
    private Date gmtCreate;

    //修改时间
    private Date gmtModify;

}