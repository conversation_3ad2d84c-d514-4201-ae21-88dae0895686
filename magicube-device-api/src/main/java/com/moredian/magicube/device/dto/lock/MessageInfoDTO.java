package com.moredian.magicube.device.dto.lock;

import com.moredian.magicube.device.enums.iot.MessageStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-03 17:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageInfoDTO implements Serializable {

    private static final long serialVersionUID = -3020669729675413256L;

    private Long messageId;

    private Long deviceId;

    private String deviceSn;

    private Long orgId;

    /**
     * 消息类型
     * @see com.moredian.magicube.device.enums.iot.IotFunctionIdEnum
     */
    private Integer messageType;

    /**
     * 消息状态
     * @see MessageStatusEnum
     */
    private Integer messageStatus;

    /**
     * 创建时间
     */
    private Date gmtCreate;
}
