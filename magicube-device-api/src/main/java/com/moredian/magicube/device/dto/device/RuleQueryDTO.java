package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version $Id: DeviceQueryDTO.java, v 1.0 Exp $
 */
@Data
@Accessors(chain = true)
public class RuleQueryDTO implements Serializable {
    private static final long serialVersionUID = -8668729683519937280L;

    @NotNull(message = "orgId不能为空")
    private Long orgId;

    /**
     * 空间Id
     */
    private Long spaceId;

    /**
     * 空间类型
     */
    private Integer spaceType;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * ${@link com.moredian.magicube.device.enums.ModeTypeEnum}
     */
    private Integer modeType;

    /**
     * 1、显示空间关联规则
     * 2、显示机构所有规则，空间关联状态
     * 3、显示机构所有规则
     */
    private Integer showType;

    /**
     * 规则Id
     */
    private Long ruleId;
}
