package com.moredian.magicube.device.dto.type;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 设备类型属性详情信息
 *
 * <AUTHOR>
 */
@Data
public class DeviceTypePropertyDetailDTO implements Serializable {

    private static final long serialVersionUID = 4830282895828414086L;

    /**
     * 设备类型属性Id
     */
    private Long id;

    /**
     * 属性key
     */
    private String propertyKey;

    /**
     * 属性值
     */
    private String propertyValue;

    /**
     * 属性描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 设备类型信息列表
     */
    private List<DeviceTypeDTO> deviceTypeDTOS;
}
