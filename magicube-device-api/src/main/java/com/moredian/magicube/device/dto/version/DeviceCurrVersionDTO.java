package com.moredian.magicube.device.dto.version;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Data
public class DeviceCurrVersionDTO implements Serializable {

    private static final long serialVersionUID = 1781799795100835172L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 当前apk版本信息
     */
    private AppVersionDTO currAppVersion = new AppVersionDTO();

    /**
     * 当前rom版本信息
     */
    private AppVersionDTO currRomVersion = new AppVersionDTO();

    /**
     * 更新的apk版本信息
     */
    private AppVersionDTO lastAppVersion = new AppVersionDTO();

    /**
     * 更新的rom版本信息
     */
    private AppVersionDTO lastRomVersion = new AppVersionDTO();


    public void setCurrAppVersion(Integer appType, Integer versionCode, String versionName, String versionDesc) {
        this.currAppVersion.setAppType(appType);
        this.currAppVersion.setVersionCode(versionCode);
        this.currAppVersion.setVersionName(versionName);
        this.currAppVersion.setVersionDesc(versionDesc);
    }

    public void setCurrRomVersion(Integer appType, Integer versionCode, String versionName, String versionDesc) {
        this.currRomVersion.setAppType(appType);
        this.currRomVersion.setVersionCode(versionCode);
        this.currRomVersion.setVersionName(versionName);
        this.currRomVersion.setVersionDesc(versionDesc);
    }

    public void setLastAppVersion(Integer appType, Integer versionCode, String versionName, String versionDesc) {
        this.lastAppVersion.setAppType(appType);
        this.lastAppVersion.setVersionCode(versionCode);
        this.lastAppVersion.setVersionName(versionName);
        this.lastAppVersion.setVersionDesc(versionDesc);
    }

    public void setLastRomVersion(Integer appType, Integer versionCode, String versionName, String versionDesc) {
        this.lastRomVersion.setAppType(appType);
        this.lastRomVersion.setVersionCode(versionCode);
        this.lastRomVersion.setVersionName(versionName);
        this.lastRomVersion.setVersionDesc(versionDesc);
    }
}