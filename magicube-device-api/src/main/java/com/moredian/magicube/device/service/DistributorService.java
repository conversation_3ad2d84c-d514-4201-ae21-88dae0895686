package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.distributor.DistributorDTO;
import com.moredian.magicube.device.dto.distributor.InsertDistributorDTO;

import java.util.List;

/**
 * 经销商设备区域锁定服务
 *
 * <AUTHOR>
 */
public interface DistributorService {

    /**
     * 新增经销商锁定设备列表
     *
     * @param dto 经销商设备信息
     * @return
     */
    ServiceResponse<Long> insert(InsertDistributorDTO dto);

    /**
     * 根据机构名称查询经销商设备信息
     *
     * @param orgName 经销商设备信息
     * @return
     */
    ServiceResponse<List<DistributorDTO>> listByOrgName(String orgName);

    /**
     * 根据设备sn查询经销商设备信息
     *
     * @param deviceSn 经销商设备信息
     * @return
     */
    ServiceResponse<DistributorDTO> getByDeviceSn(String deviceSn);
}
