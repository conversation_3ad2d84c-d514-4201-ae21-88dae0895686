package com.moredian.magicube.device.dto.carme;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class InsertPeopleNumStatisticDTO implements Serializable {

    private static final long serialVersionUID = -5425689439936253182L;

    /**
     * id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 区域内人数
     */
    private Integer insidePeopleNum;
}
