package com.moredian.magicube.device.dto.qrcode;

import java.io.Serializable;
import lombok.Data;

/**
 * @Author: fangJ
 * @Description: 配网码状态信息
 */
@Data
public class UploadNetworkCodeStatusDTO implements Serializable {

    private static final long serialVersionUID = -8215355450957632387L;

    private Long orgNetworkId;

    private Long orgId;

    /**
     * 设备配网码状态 0-待处理 1-处理中 2-处理完成
     */
    private Integer status;

    /**
     * 绑定机构页面url（仅用于钉钉SDK激活流程）
     */
    private String bindOrgUrl;
}
