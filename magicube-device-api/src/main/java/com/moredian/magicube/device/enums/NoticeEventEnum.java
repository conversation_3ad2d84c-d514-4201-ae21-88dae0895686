package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
public enum NoticeEventEnum {

    DEVICE_OFFLINE_NOTICE_EVENT(1, "设备离线通知");


    private final int code;

    private final String desc;

    NoticeEventEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
