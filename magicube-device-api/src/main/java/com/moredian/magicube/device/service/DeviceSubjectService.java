package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.subject.DeviceSubjectDTO;
import com.moredian.magicube.device.dto.subject.DeviceSubjectSimpleDTO;
import com.moredian.magicube.device.dto.subject.InsertDeviceSubjectDTO;
import com.moredian.magicube.device.dto.subject.UpdateDeviceSubjectDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 设备主题服务
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface DeviceSubjectService {

    /**
     * 查询主题模板列表
     *
     * @return
     */
    ServiceResponse<List<String>> listTemplate();

    /**
     * 新增设备主题
     *
     * @param dto 设备主题信息
     * @return
     */
    ServiceResponse<Long> insert(InsertDeviceSubjectDTO dto);

    /**
     * 编辑设备主题信息
     *
     * @param dto 设备主题信息
     * @return
     */
    ServiceResponse<Boolean> update(UpdateDeviceSubjectDTO dto);

    /**
     * 根据机构id和主题id查询设备主题信息
     *
     * @param orgId     机构Id
     * @param subjectId 设备主题Id
     * @return
     */
    ServiceResponse<DeviceSubjectDTO> getByOrgIdAndSubjectId(Long orgId, Long subjectId);

    /**
     * 根据机构id和主题id列表查询设备主题信息
     *
     * @param orgId      机构Id
     * @param subjectIds 设备主题Id列表
     * @return
     */
    ServiceResponse<List<DeviceSubjectSimpleDTO>> listSimpleByOrgIdAndSubjectIds(Long orgId,
        List<Long> subjectIds);

    /**
     * 查询设备主题列表
     *
     * @param orgId 机构Id
     * @param type  主题类型 1-壁纸 2-屏保
     * @return
     */
    ServiceResponse<List<DeviceSubjectDTO>> listByOrgIdAndType(Long orgId, Integer type);




    /**
     * 分页查询设备主题列表
     *
     * @return
     */
    ServiceResponse<Pagination<DeviceSubjectDTO>> listByOrgIdAndTypePage(DeviceSubjectDTO deviceSubjectDTO);

    /**
     * 批量删除设备主题
     *
     * @param orgId      机构Id
     * @param subjectIds 设备主题Id列表
     * @return
     */
    ServiceResponse<Boolean> deleteByOrgIdAndIds(Long orgId, List<Long> subjectIds);

    /**
     * 更新屏保开关（0-关闭，1-开启）
     *
     * @return
     */
    ServiceResponse<Boolean> updateScreenSwitch(Long orgId,Long subjectId,Integer enable);
}
