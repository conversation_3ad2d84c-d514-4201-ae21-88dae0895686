package com.moredian.magicube.device.dto.snapshot;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/29
 */
@Getter
@Setter
@ToString
public class GroupPersonSnapshotDTO implements Serializable {

    private static final long serialVersionUID = 9014176134272097025L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 快照Id
     */
    private Long snapshotId;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 权限组id
     */
    private Long groupId;

    /**
     * 人员类型
     */
    private Integer personType;
    /**
     * 人员Id
     */
    private Long personId;
}
