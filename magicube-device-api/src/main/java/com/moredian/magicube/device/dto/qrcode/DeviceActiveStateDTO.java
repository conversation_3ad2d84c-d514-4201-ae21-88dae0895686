package com.moredian.magicube.device.dto.qrcode;

import com.moredian.magicube.device.enums.DeviceActiveStateEnum;
import java.io.Serializable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 设备激活状态
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Builder
public class DeviceActiveStateDTO implements Serializable {

    private static final long serialVersionUID = -8573314049885895804L;

    /**
     * 设备激活状态Id
     */
    private Long id;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * @see DeviceActiveStateEnum 设备激活状态
     */
    private Integer state;

    /**
     * 绑定机构页面url（仅用于钉钉SDK激活流程）
     */
    private String bindOrgUrl;

    /**
     * 非钉激活的机构id
     */
    private Long bindOrgId;
}