package com.moredian.magicube.device.dto.group;

import java.io.Serializable;
import lombok.Data;

/**
 * 组信息
 *
 * <AUTHOR>
 */
@Data
public class GroupInfoDTO implements Serializable {

    private static final long serialVersionUID = 8758297917002472624L;

    /**
     * 组Id
     */
    private Long groupId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 组类型
     */
    private Integer groupType;

    /**
     * 组code码
     */
    private String groupCode;

    /**
     * 组名称
     */
    private String groupName;

    /**
     * 是否默认组 0-是 1-不是
     */
    private Integer systemDefault;

    /**
     * 是否全员组 0-是 1-不是
     */
    private Integer allMemberFlag;

    /**
     * 人员数量
     */
    private Integer memberSize;

    /**
     * 组app类型
     *
     * @see com.moredian.magicube.common.enums.GroupAppType
     */
    private Integer appType;

    /**
     * 组状态 0-正常 1-同步中
     */
    private Integer groupStatus;
}