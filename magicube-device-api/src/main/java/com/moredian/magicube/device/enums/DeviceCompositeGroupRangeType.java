package com.moredian.magicube.device.enums;

/**
 * 通行权限组设备组关系类型
 *
 * <AUTHOR>
 * @date 2024/11/25 16:32
 */
public enum DeviceCompositeGroupRangeType {

    /**
     * 设备分组
     */
    GROUP(1, "设备分组"),
    /**
     * 设备
     */
    DEVICE(2, "设备"),
    ;

    private final Integer code;
    private final String desc;

    DeviceCompositeGroupRangeType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
