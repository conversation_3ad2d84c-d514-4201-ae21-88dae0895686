package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDetailDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.UpdateDeviceTypePropertyDTO;
import java.util.List;

/**
 * 设备类型属性相关接口
 *
 * <AUTHOR>
 */
public interface DeviceTypePropertyService {

    /**
     * 新增设备类型属性
     *
     * @param dto 设备类型属性信息
     * @return
     */
    ServiceResponse<Long> insert(InsertDeviceTypePropertyDTO dto);

    /**
     * 编辑设备类型属性
     *
     * @param dto 设备类型属性信息
     * @return
     */
    ServiceResponse<Long> update(UpdateDeviceTypePropertyDTO dto);

    /**
     * 根据Id查询设备类型属性
     *
     * @param id 设备类型Id
     * @return
     */
    ServiceResponse<DeviceTypePropertyDetailDTO> getById(Long id);

    /**
     * 根据属性key查询设备类型属性
     *
     * @param propertyKey 属性key
     * @return
     */
    ServiceResponse<DeviceTypePropertyDetailDTO> getByPropertyKey(String propertyKey);

    /**
     * 查询设备类型属性列表
     *
     * @return
     */
    ServiceResponse<List<DeviceTypePropertyDTO>> list();

    /**
     * 查询设备类型属性列表
     *
     * @param ids 设备类型属性Id列表
     * @return
     */
    ServiceResponse<List<DeviceTypePropertyDTO>> listByIds(List<Long> ids);


    /**
     * 根据属性key查询设备类型
     *
     * @param propertyKey 属性key
     * @return 设备List
     */
    ServiceResponse<List<Integer>> getDeviceTypeByPropertyKey(String propertyKey);

    /**
     * 属性是否关联指定设备类型
     *
     * @param propertyKey 属性key
     * @param deviceType 设备类型
     * @return 设备List
     */
    ServiceResponse<Boolean> containsDeviceType(String propertyKey, Integer deviceType);
}