package com.moredian.magicube.device.dto.version;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/17
 */
@Getter
@Setter
@ToString
public class QueryDeviceVersionDTO implements Serializable {

    private static final long serialVersionUID = 4285368230288357450L;

    /**
     * 机构id列表
     */
    private List<Long> orgIdList;

    /**
     * rom/app类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否为rom
     */
    private Boolean romFlag;

    /**
     * rom中app类型
     */
    private Integer apkAppType;

    /**
     * 是否为os
     */
    private Boolean osFlag;
}
