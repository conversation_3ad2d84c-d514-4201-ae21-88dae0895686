package com.moredian.magicube.device.dto.lock;

import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.enums.iot.IotFunctionIdEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description iot 方法调用入参
 * @create 2025-03-03 17:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InvokeMessageDTO implements java.io.Serializable {

    private static final long serialVersionUID = 9075300810628445584L;

    /**
     * 消息类型
     * @see com.moredian.magicube.device.enums.iot.IotFunctionIdEnum
     */
    private Integer messageType;

    /**
     * 调用设备sn
     */
    private String deviceSn;

    /**
     * 调用参数
     */
    private Map<String, Object> params = Maps.newHashMap();

    /**
     * 校验参数
     */
    public static void validateData(InvokeMessageDTO invokeMessageDTO) {

        BizAssert.isTrue(!ObjectUtils.isEmpty(invokeMessageDTO), "invokeMessageDTO must be not null");
        BizAssert.isTrue(!ObjectUtils.isEmpty(invokeMessageDTO.getMessageType()), "messageType must be not null");
        BizAssert.isTrue(!ObjectUtils.isEmpty(invokeMessageDTO.getDeviceSn()), "deviceSn must be not null");

        IotFunctionIdEnum functionIdEnum = IotFunctionIdEnum.getByCode(invokeMessageDTO.getMessageType());
        BizAssert.isTrue(functionIdEnum != null, "type must be not null");

    }
}
