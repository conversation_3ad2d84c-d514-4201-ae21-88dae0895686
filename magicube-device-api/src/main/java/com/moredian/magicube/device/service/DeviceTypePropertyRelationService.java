package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.type.InsertDeviceTypePropertyRelationDTO;
import java.util.List;

/**
 * 设备类型和属性关系接口
 *
 * <AUTHOR>
 */
public interface DeviceTypePropertyRelationService {

    /**
     * 根据设备类型查询属性Id列表
     *
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<List<Long>> listPropertyIdByDeviceType(Integer deviceType);

    /**
     * 根据设备类型属性Id查询设备类型列表
     *
     * @param deviceTypePropertyId 设备类型属性Id
     * @return
     */
    ServiceResponse<List<Integer>> listDeviceTypeByPropertyId(Long deviceTypePropertyId);

    /**
     * 新增设备类型和属性关系
     *
     * @param dto 设备类型属性信息
     * @return
     */
    ServiceResponse<Long> insert(InsertDeviceTypePropertyRelationDTO dto);

    /**
     * 重置设备类型和多个属性的关系
     *
     * @param deviceType  设备类型
     * @param propertyIds 设备类型属性Id列表
     * @return
     */
    ServiceResponse<Boolean> resetRelationByDeviceType(Integer deviceType, List<Long> propertyIds);

    /**
     * 重置属性和多个设备类型的关系
     *
     * @param propertyId  设备类型属性Id
     * @param deviceTypes 设备类型列表
     * @return
     */
    ServiceResponse<Boolean> resetRelationByPropertyId(Long propertyId, List<Integer> deviceTypes);
}