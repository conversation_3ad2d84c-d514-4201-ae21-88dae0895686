package com.moredian.magicube.device.dto.blu;

import java.io.Serializable;

public class DeviceBLuCheckApiReq implements Serializable {
    private static final long serialVersionUID = -2738750978526323012L;

    /**
     * 请求签名
     */
    private String signature;
    /**
     * 设备sn
     */
    private String deviceSn;
    /**
     * 设备版本
     */
    private String version;

    /**
     * 时间戳
     */
    private Long timeStamp;


    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }


    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    @Override
    public String toString() {
        return "DeviceBLuCheckReq{" +
                "sign='" + signature + '\'' +
                ", deviceSn='" + deviceSn + '\'' +
                ", version='" + version + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }
}

