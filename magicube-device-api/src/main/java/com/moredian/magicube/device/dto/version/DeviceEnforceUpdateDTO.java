package com.moredian.magicube.device.dto.version;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备强制升级信息
 *
 * <AUTHOR>
 */

@Data
public class DeviceEnforceUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1139212319899072054L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * apk系统类型 1安卓 2苹果
     */
    private Integer systemType;

    /**
     * 设备app类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 强制升级重试次数
     */
    private Integer tryTimes;

    /**
     * 强制升级时间
     */
    private Date enforceUpdateTime;
}