package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @date : 2024/5/22
 */
public enum IotDeviceStatusEnums {
    offline(0, "offline"),
    online(1, "online");

    private Integer code;
    private String msg;

    IotDeviceStatusEnums(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getByCode(Integer code) {
        for (IotDeviceStatusEnums state : values()) {
            if (code.equals(state.code)) {
                return state.msg;
            }
        }
        throw new RuntimeException("engine code error");
    }
}
