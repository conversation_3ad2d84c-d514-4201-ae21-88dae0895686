package com.moredian.magicube.device.dto.type;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.moredian.magicube.common.util.DateUtils;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 设备类型属性信息
 *
 * <AUTHOR>
 */
@Data
public class DeviceTypePropertyDTO implements Serializable {

    private static final long serialVersionUID = 4830282895828414086L;

    /**
     * 设备类型属性Id
     */
    private Long id;

    /**
     * 属性key
     */
    private String propertyKey;

    /**
     * 属性值
     */
    private String propertyValue;

    /**
     * 属性描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;
}
