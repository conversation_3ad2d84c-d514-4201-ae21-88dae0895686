package com.moredian.magicube.device.dto.version;

import java.util.List;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备版本信息
 *
 * <AUTHOR>
 */

@Data
public class VersionDTO implements Serializable {

    private static final long serialVersionUID = 1458659607066637391L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 系统类型 1安卓 2苹果
     */
    private Integer systemType;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备app类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 版本名
     */
    private String versionName;

    /**
     * 版本描述
     */
    private String versionDesc;

    /**
     * 安装文件url
     */
    private String appUrl;

    /**
     * 是否立即升级
     */
    private Boolean isActive;

    /**
     * 是否需要强制更新
     */
    private Boolean isEnforceUpdate;

    /**
     * 删除标识
     */
    private Boolean isDelFlag;

    /**
     * 创建人ID
     */
    private Long userCreate;

    /**
     * 是否上电升级(0否 1是)
     */
    private Integer enforcementUpgrade;

    /**
     * 人脸模型列表
     */
    private List<String> faceModels;

    /**
     * 更新时间
     */
    private Date gmtModify;

    /**
     * 包存储平台 1->魔蓝，2->钉钉
     */
    private Integer storageLocation;

    /**
     * 扩展信息json，兼容门锁各个app的升级包信息
     */
    private String extendInfo;

    /**
     * rom包中携带的apk类型
     */
    private Integer apkAppType;

    /**
     * rom包中携带的apk版本号
     */
    private Integer apkVersionCode;
}