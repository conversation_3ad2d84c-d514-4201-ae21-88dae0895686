package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/7/6
 */
@Getter
@Setter
@ToString
public class DeviceElectricDTO implements Serializable {

    private static final long serialVersionUID = 7142414119033534444L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 电量
     */
    private Integer electric;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
