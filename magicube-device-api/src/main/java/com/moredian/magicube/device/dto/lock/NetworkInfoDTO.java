package com.moredian.magicube.device.dto.lock;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description 通通锁网络状态信息
 * @create 2025-03-24 16:14
 */
@Data
public class NetworkInfoDTO implements java.io.Serializable{

    private static final long serialVersionUID = -4080019019084108717L;

    /**
     * 网络名称
     */
    private String networkName;

    /**
     * wifi 密码
     */
    private String wifiPassword;

    /**
     * wifi，mac地址
     */
    private String wifiMac;


    /**
     * 信号强度
     */
    private Integer rssi;

    /**
     * 是否使用静态ip
     */
    private Boolean useStaticIp;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 子网掩码
     */
    private String subnetMask;

    /**
     * 默认网关
     */
    private String defaultGateway;

    /**
     * 首选dns
     */
    private String preferredDns;

    /**
     * 备选dns
     */
    private String alternateDns;

    /**
     * 是否改变设备激活状态
     */
    private Boolean changeActivateStatus;
}
