package com.moredian.magicube.device.dto.white;

import lombok.Data;

import java.io.Serializable;

/**
 * 新增设备白名单
 *
 * <AUTHOR>
 */
@Data
public class InsertWhiteDeviceDTO implements Serializable {

    private static final long serialVersionUID = -5684904135549269529L;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备Sn 必填
     */
    private String serialNumber;

    /**
     * mac地址 必填
     */
    private String macAddress;

    /**
     * mac地址
     */
    private String macAddress2;

    /**
     * 设备激活私钥 必填
     */
    private String privateKey;

    /**
     * 是否开放平台激活
     */
    private Boolean openPlat;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备来源 1魔点设备 2第三方设备
     */
    private Integer deviceSource;

    /**
     * 设备激活oaBase 1-非钉 2-钉钉
     */
    private Integer deviceActivateOaBase;

    /**
     * 设备激活sdkBase 1-非钉 2-钉钉
     */
    private Integer deviceActivateSdkBase;
}
