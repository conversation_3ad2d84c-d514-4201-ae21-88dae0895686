package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备关联固定应用配置表 DTO
 */
@Setter
@Getter
public class DeviceAppRelationFixedConfigDTO implements Serializable {

private static final long serialVersionUID = -0L;

	/** 主键id */
	private Long id;
	/** 业务类型 */
	private Integer bizType;
	/** 业务id */
	private String bizId;
	/** 默认应用 */
	private String defaultAppCode;
	/** 可承载应用列表 */
	private String availableAppCodeList;
	/** 创建时间 */
	private Date gmtCreate;
	/** 修改时间 */
	private Date gmtModify;


    @Override
    public String toString() {
        return "DeviceAppRelationFixedConfig{" +
				"id=" + id +
						",bizType='" + bizType + "'" + 
						",bizId='" + bizId + "'" + 
						",defaultAppCode='" + defaultAppCode + "'" + 
						",availableAppCodeList='" + availableAppCodeList + "'" + 
						",gmtCreate='" + gmtCreate + "'" + 
						",gmtModify='" + gmtModify + "'" + 
		                '}';
    }
	
}