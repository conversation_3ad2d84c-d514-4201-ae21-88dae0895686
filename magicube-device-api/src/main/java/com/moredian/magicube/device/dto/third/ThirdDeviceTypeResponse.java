package com.moredian.magicube.device.dto.third;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description
 * @author: xiesj
 * @date 2022/12/29 9:56
 */
@Getter
@Setter
@ToString
public class ThirdDeviceTypeResponse implements Serializable {

    private static final long serialVersionUID = 8159948782055054091L;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
