package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/20 10:28
 */
@Data
public class CompositeDeepDeviceSizeDTO implements Serializable {

    private static final long serialVersionUID = -1027478637033117434L;

    /**
     * 设备组 ID
     */
    private Long deviceCompositeId;

    /**
     * 这个设备组下以子组下设备数量情况（去重）
     */
    private Integer deviceSize;

    /**
     * 设备Id列表
     */
    private List<Long> deviceIds;
}
