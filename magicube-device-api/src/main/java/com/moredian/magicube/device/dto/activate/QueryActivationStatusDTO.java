package com.moredian.magicube.device.dto.activate;

import lombok.Data;

import java.io.Serializable;

/**
 * 激活状态信息
 *
 * <AUTHOR>
 */

@Data
public class QueryActivationStatusDTO implements Serializable {

    private static final long serialVersionUID = 1049162066736123836L;

    /**
     * 设备Sn
     */
    private String serialNumber;

    /**
     * 二维码状态
     */
    private int statusCode;

    /**
     * 二维码状态信息
     */
    private String message;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备Sn
     */
    private String deviceSn;
}
