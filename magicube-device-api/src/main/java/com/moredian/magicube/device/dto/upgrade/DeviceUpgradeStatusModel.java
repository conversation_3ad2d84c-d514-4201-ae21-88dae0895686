package com.moredian.magicube.device.dto.upgrade;

import java.io.Serializable;

public class DeviceUpgradeStatusModel implements Serializable {

	private static final long serialVersionUID = 1L;
	
	// 10 app   20 rom
	private Integer upgradeType;
	
	//状态 5待开始   10 接收消息成功  20 正在下载文件  30 正在安装  40 成功  50 失败
	//EnumUpdateStatus
    private Integer upgradeStatus;
    
    //总体进度 0-100 百分比
    private short progress;
    
    //EnumUpdateErrorNo
    private Integer errorNo;
           
    private Integer versionCode;
    
    private Integer appType;

	public Integer getUpgradeStatus() {
		return upgradeStatus;
	}

	public void setUpgradeStatus(Integer upgradeStatus) {
		this.upgradeStatus = upgradeStatus;
	}

	public short getProgress() {
		return progress;
	}

	public void setProgress(short progress) {
		this.progress = progress;
	}

	public Integer getErrorNo() {
		return errorNo;
	}

	public void setErrorNo(Integer errorNo) {
		this.errorNo = errorNo;
	}

	public Integer getVersionCode() {
		return versionCode;
	}

	public void setVersionCode(Integer versionCode) {
		this.versionCode = versionCode;
	}

	public Integer getAppType() {
		return appType;
	}

	public void setAppType(Integer appType) {
		this.appType = appType;
	}

	public Integer getUpgradeType() {
		return upgradeType;
	}

	public void setUpgradeType(Integer upgradeType) {
		this.upgradeType = upgradeType;
	}
}
