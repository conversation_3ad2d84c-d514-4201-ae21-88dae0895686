package com.moredian.magicube.device.dto.activate;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备激活返回信息
 *
 * <AUTHOR>
 */

@Data
public class ActivateDeviceResultDTO implements Serializable {

    private static final long serialVersionUID = 2997650255871492959L;

    /**
     * 设备Id
     */
    private Long equipmentId;

    /**
     * 主机构Id
     */
    private Long orgId;

    /**
     * 子机构编号
     */
    private Long subOrgId;

    /**
     * 子机构码
     */
    private String subOrgCode;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 设备类型
     */
    private Integer equipmentType;

    /**
     * 离线的密码key
     */
    private String offLineSeed;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 设备位置描述
     */
    private String addressDesc;

    /**
     * 设备Sn
     */
    private String serialNumber;

    /**
     * 设备授权密钥
     */
    private String accessKeySecret;

    /**
     * 第三方机构类型
     */
    private Integer orgTpType;

    /**
     * 第三方机构Id
     */
    private String orgTpId;

    /**----------以下返回参数为电信项目混合云使用----------**/
    /**
     * 局域网ip+host
     */
    private String localCloudLan;

    /**
     * 盒子编码
     */
    private String localCloudBoxCode;

    /**
     * 算法版本
     */
    private String localCloudAlgorithmVersion;

    /**
     * 机构名
     */
    private String localCloudOrgName;

    /**
     * 是否在线识别 1-是 0-否
     * {@link com.moredian.magicube.common.enums.YesNoFlag}
     */
    private String localCloudOnlineIdent;

    /**
     * 是否为混合云机构 1-是 0-否
     */
    private String localCloudOrg;

    /**
     * 本地云密钥
     */
    private String localCloudSecretKey;
}