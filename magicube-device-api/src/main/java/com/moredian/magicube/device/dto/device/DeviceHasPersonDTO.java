package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.Data;

@Data
public class DeviceHasPersonDTO implements Serializable {

    private static final long serialVersionUID = 7704592995881851525L;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 是否有人，true-有人，false-无人
     */
    private Boolean hasPerson;
}
