package com.moredian.magicube.device.dto.version;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/11/2
 */
@Getter
@Setter
@ToString
public class DeviceVersionInfoDTO implements Serializable {

    private static final long serialVersionUID = 2865696581733860316L;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;

}
