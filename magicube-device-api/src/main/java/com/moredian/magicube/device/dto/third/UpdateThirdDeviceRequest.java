package com.moredian.magicube.device.dto.third;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description
 * @author: xiesj
 * @date 2022/12/29 18:01
 */
@Getter
@Setter
@ToString
public class UpdateThirdDeviceRequest implements Serializable {

    private static final long serialVersionUID = 5461509327618603372L;

    /**
     * 主键id
     */
    private Long thirdDeviceId;

    /**
     * 厂商
     */
    private String manufacturer;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 备注
     */
    private String remark;
}
