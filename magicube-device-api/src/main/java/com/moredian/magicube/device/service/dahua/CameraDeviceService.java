package com.moredian.magicube.device.service.dahua;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.dto.dahua.CameraPeopleNumDTO;
import java.util.List;

/**
 * 大华摄像头设备相关接口
 *
 * <AUTHOR>
 */
public interface CameraDeviceService {

    /**
     * 查询摄像头列表
     *
     * @return
     */
    ServiceResponse<List<CameraDeviceInfoDTO>> list();

    /**
     * 根据ip和端口号查询摄像头信息
     *
     * @param ip 设备ip
     * @param port 设备端口
     * @return
     */
    ServiceResponse<CameraDeviceInfoDTO> getByIpAndPort(String ip, int port);

    /**
     * 根据设备sn查询，绑定这个设备空间下摄像头的人数统计
     */
    ServiceResponse<CameraPeopleNumDTO> getCameraPeopleNum(Long orgId, String deviceId);
}
