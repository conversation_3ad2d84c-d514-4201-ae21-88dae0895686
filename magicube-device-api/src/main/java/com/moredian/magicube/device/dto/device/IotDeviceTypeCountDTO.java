package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Iot设备类型统计
 *
 * <AUTHOR>
 */

@Data
public class IotDeviceTypeCountDTO implements Serializable {

    private static final long serialVersionUID = 893161871581412473L;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备类型数量统计
     */
    private Integer deviceCount;
}