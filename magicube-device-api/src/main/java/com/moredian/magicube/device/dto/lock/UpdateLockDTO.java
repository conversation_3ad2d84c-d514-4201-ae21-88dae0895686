package com.moredian.magicube.device.dto.lock;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-04-07 14:19
 */
@Data
public class UpdateLockDTO implements java.io.Serializable{

    private static final long serialVersionUID = 128156989003346972L;

    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备在离线
     */
    private Boolean online;

    /**
     * 通行密码id（存放在魔链设备物模型中）
     */
    private Integer keyboardPwdId;

    /**
     * 通行密码
     */
    private String newKeyboardPwd;

    /**
     * 是否开启密码，1：开启，0：关闭
     */
    private Integer enablePassword;
}
