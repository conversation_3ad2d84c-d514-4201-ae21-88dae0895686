package com.moredian.magicube.device.dto.white;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/12/22
 */
@Getter
@Setter
@ToString
public class SimpleWhiteDeviceResponse implements Serializable {

    private static final long serialVersionUID = -5326646170718669823L;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * app版本号
     */
    private Integer romType;

    /**
     * rom类型
     */
    private Integer romVersionCode;

    /**
     * rom版本号
     */
    private Integer appVersionCode;

    /**
     * 激活状态
     */
    private Boolean activated = Boolean.FALSE;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;
}
