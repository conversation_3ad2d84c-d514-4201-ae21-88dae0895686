package com.moredian.magicube.device.constant;

/**
 * 适配旧fishnet-device服务，保持ID全局唯一，取相同的包名+类名生成ID
 * 防止新老设备服务同时跑的过程中出现ID相同导致插入数据库失败
 *
 * <AUTHOR>
 * @since 2021/11/03
 */

public class BeanConstants {

    public static final String APP_VERSION = "com.moredian.fishnet.decvice.domain.AppVersion";
    public static final String DEPLOY = "com.xier.fishnet.device.Deploy";
    public static final String DEVICE = "com.moredian.fishnet.device.Device";
    public static final String DEVICE_ACCOUNT = "com.moredian.fishnet.device.domain.domain.HiveDeviceAccount";
    public static final String DEVICE_GROUP = "com.moredian.fishnet.device.DeviceGroup";
    public static final String DEVICE_CONFIG = "com.moredian.fishnet.device.DeviceConfig";
    public static final String PERIPHERALS = "com.xier.sesame.attence.Peripherals";
    public static final String DEVICE_WHITE_LIST = "com.xier.sesame.attence.DeviceWhiteList";
    public static final String DEVICE_COMPOSITE_ITEM = "com.moredian.fishnet.device.domain.DeviceCompositeItem";
    public static final String PERIPHERALS_WHITE_LIST = "com.moredian.fishnet.device.domain.PeripheralsWhiteList";
    public static final String DEVICE_TYPE = "com.moredian.magicube.device.dao.entity.DeviceType";
    public static final String DEVICE_TYPE_PROPERTY = "com.moredian.magicube.device.dao.entity.DeviceTypeProperty";
    public static final String DEVICE_TYPE_PROPERTY_RELATION = "com.moredian.magicube.device.dao.entity.DeviceTypePropertyRelation";
    public static final String RULE = "com.moredian.magicube.device.dao.entity.Rule";
    public static final String DEVICE_RULE_RELATION = "com.moredian.magicube.device.dao.entity.DeviceRuleRelation";
    public static final String SPACE_RULE_RELATION = "com.moredian.magicube.device.dao.entity.SpaceRuleRelation";
    public static final String DEVICE_APP_RELATION_CONFIG = "com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig";
}