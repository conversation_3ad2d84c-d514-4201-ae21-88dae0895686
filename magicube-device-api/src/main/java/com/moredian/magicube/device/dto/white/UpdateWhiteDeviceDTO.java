package com.moredian.magicube.device.dto.white;

import lombok.Data;

import java.io.Serializable;

/**
 * 更新设备白名单信息
 *
 * <AUTHOR>
 */
@Data
public class UpdateWhiteDeviceDTO implements Serializable {

    private static final long serialVersionUID = 4051847860036519667L;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备Sn
     */
    private String serialNumber;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 激活状态
     */
    private Integer activityStatus;

    /**
     * 设备激活oaBase 1-非钉 2-钉钉
     */
    private Integer deviceActivateOaBase;

    /**
     * 设备激活sdkBase 1-非钉 2-钉钉
     */
    private Integer deviceActivateSdkBase;

    /**
     * 新设备sn
     */
    private String newSerialNumber;
}
