package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.project.DeviceProjectInfoResponse;
import com.moredian.magicube.device.dto.project.PullDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.ReportDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateProjectInfoResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 设备项目信息接口
 * @Date 2023/5/10
 */
public interface DeviceProjectInfoService {
    /**
     * 设备上报项目信息
     *
     * @param request
     * @return
     */
    ServiceResponse<Long> reportProjectInfo(ReportDeviceProjectInfoRequest request);

    /**
     * 设备主动拉取项目信息
     *
     * @param request
     * @return
     */
    ServiceResponse<DeviceProjectInfoResponse> pullProjectInfo(PullDeviceProjectInfoRequest request);

    /**
     * 查询设备项目信息
     *
     * @param request
     * @return
     */
    ServiceResponse<List<DeviceProjectInfoResponse>> findDeviceProjectInfo(
        QueryDeviceProjectInfoRequest request);

    /**
     * 修改设备项目信息(暂时只支持修改单个设备)
     *
     * @param request
     * @return
     */
    ServiceResponse<UpdateProjectInfoResponse> updateProjectInfo(
        UpdateDeviceProjectInfoRequest request);
}
