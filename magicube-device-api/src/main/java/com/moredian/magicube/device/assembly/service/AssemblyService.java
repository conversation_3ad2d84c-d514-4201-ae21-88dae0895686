package com.moredian.magicube.device.assembly.service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/2 17:07
 */
public interface AssemblyService {


    /**
     * 普通的rabbitMQ消息发布
     *
     * @param msg 味精
     * @return {@link Integer }
     */
    public Integer publish(Object msg);


    /**
     * 具有降噪功能的MQ消息发布
     *
     * @param msg   消息内容
     * @param clazz 消息类型
     * @param key   降噪键
     * @return {@link Integer }
     */
    public Integer publish(Object msg, Class<?> clazz, Long orgId, String key);


    /**
     * 具有降噪功能的MQ消息发布
     *
     * @param msg   消息内容
     * @param clazz 消息类型
     * @param key   降噪键
     * @return {@link Integer }
     */
    public Integer publish(Object msg, Class<?> clazz, Long orgId, String key, String redisKey);


    /**
     * 待集合字段的消息，根据集合内容降噪，把集合内容作为多key传入，并且指定集合字段名
     *
     * @param msg        味精
     * @param clazz      克拉兹
     * @param orgId      组织 ID
     * @param keyList    密钥列表
     * @param resetField reset 字段
     * @return {@link Integer }
     */
    Integer publish(Object msg, Class<?> clazz, Long orgId, List<String> keyList,
        String resetField);

    /**
     * 待集合字段的消息，根据集合内容降噪，把集合内容作为多key传入，并且指定集合字段名
     *
     * @param msg        味精
     * @param clazz      克拉兹
     * @param orgId      组织 ID
     * @param keyList        钥匙
     * @param redisKey   Redis 键
     * @param resetField reset 字段
     * @return {@link Integer }
     */
    public Integer publish(Object msg, Class<?> clazz, Long orgId, List<String> keyList,
        String redisKey, String resetField);

}
