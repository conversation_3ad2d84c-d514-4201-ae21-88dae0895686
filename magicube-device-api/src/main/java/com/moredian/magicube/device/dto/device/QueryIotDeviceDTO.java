package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 查询Iot设备条件
 *
 * <AUTHOR>
 */

@Data
public class QueryIotDeviceDTO implements Serializable {

    private static final long serialVersionUID = 490169966740843479L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 父设备id，不传查根设备，传查父设备下的子设备数据
     */
    private String parentDeviceId;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 设备类型列表
     */
    private List<Integer> deviceTypes;

    /**
     * 设备Id列表
     */
    private List<Long> deviceIds;

    /**
     * 在线状态 0-离线 1-在线
     */
    private Integer onlineStatus;

    /**
     * 排序规则，0-按名称排序，1-按激活时间倒序，2-按激活时间正序
     */
    private Integer sort;

    /**
     * 空间树Id列表
     */
    private List<Long> treeIds;

    /**
     * 模糊搜索的设备Id列表(用或条件来查)
     */
    private List<Long> queryDeviceIds;

    /**
     * iot设备类型列表
     */
    private List<Integer> iotDeviceTypes;

    /**
     * 是否查询所有设备
     */
    private Boolean allDeviceFlag;

    /**
     * 账号id
     */
    private Long memberId;

    /**
     * 权限tag
     */
    private List<String> roleTag;
}