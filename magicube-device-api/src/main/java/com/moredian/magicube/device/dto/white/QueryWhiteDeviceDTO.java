package com.moredian.magicube.device.dto.white;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询白名单条件
 *
 * <AUTHOR>
 */

@Data
public class QueryWhiteDeviceDTO implements Serializable {

    private static final long serialVersionUID = 4642559310543632269L;

    /**
     * 设备sn
     */
    private String serialNumber;

    /**
     * 设备sn列表
     */
    private List<String> serialNumbers;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * 机构号
     */
    private Long orgId;

    private String thirdDeviceId;

    /**
     * 每页显示数目
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNo;

    /**
     * 设备来源 1魔点设备,2三方设备
     */
    private Integer deviceSource;
}
