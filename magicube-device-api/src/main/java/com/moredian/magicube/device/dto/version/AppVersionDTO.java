package com.moredian.magicube.device.dto.version;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备版本信息
 *
 * <AUTHOR>
 */

@Data
public class AppVersionDTO implements Serializable {

    private static final long serialVersionUID = -3130258235384085171L;

    /**
     * 设备app类型
     */
    private Integer appType;

    /**
     * 设备版本号
     */
    private Integer versionCode;

    /**
     * 设备版本名称
     */
    private String versionName;

    /**
     * 设备版本描述
     */
    private String versionDesc;
}