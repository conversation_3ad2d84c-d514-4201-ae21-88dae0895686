package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @version $Id: MotionStatusEnum.java, v 1.0 Exp $
 */
public enum MotionStatusEnum {

    /**
     * 规则类型
     */
    NO_PERSON(0, "无人"),
    PERSON(1, "有人"),
    ;

    private final int code;

    private final String desc;

    MotionStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
