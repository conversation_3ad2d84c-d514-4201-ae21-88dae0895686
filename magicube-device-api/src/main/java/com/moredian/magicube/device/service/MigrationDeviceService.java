package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.migration.*;

/**
 * @Description: 一键上钉
 * @author: wbf
 * @date: 2024/8/12 下午2:42
 */
public interface MigrationDeviceService {

    /**
     * 查询待迁移设备列表
     *
     * @param orgId
     * @return
     */
    ServiceResponse<MigrationDeviceRes> getList(Long orgId);

    /**
     * 请求获取组织列表页url
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    ServiceResponse<Boolean> reqBindOrgUrl(Long orgId, Long deviceId);

    /**
     * 客户端轮询获取组织列表页
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    ServiceResponse<MigrationDeviceBindOrgUrlRes> getBindOrgUrl(Long orgId, Long deviceId);

    /**
     * 终端同步组织列表页地址
     *
     * @param deviceSn
     * @param bindOrgUrl 组织页url
     * @return
     */
    ServiceResponse<Boolean> bindOrgUrlSync(String deviceSn, String bindOrgUrl);

    /**
     * 终端判断用户选择组织id是否与当前组织id一致
     *
     * @param deviceSn
     * @param corpId   用户选择钉钉组织id
     * @return
     */
    ServiceResponse<MigrationDeviceOrgRes> getOrgIsEqual(String deviceSn, String corpId);

    /**
     * 客户端轮询设备迁移状态
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    ServiceResponse<MigrationDeviceStatusRes> getMigrationDeviceStatus(Long orgId, Long deviceId);

    /**
     * 终端同步迁移结果
     *
     * @param orgId
     * @param deviceSn
     * @param isSuccess
     * @return
     */
    ServiceResponse<Boolean> migrationDeviceStatusSync(Long orgId, String deviceSn, Boolean isSuccess);

    /**
     * 查询组织待迁移设备数量
     *
     * @param orgId
     * @return
     */
    ServiceResponse<MigrationDeviceOrgCountRes> getMigrationDeviceCount(Long orgId);

}
