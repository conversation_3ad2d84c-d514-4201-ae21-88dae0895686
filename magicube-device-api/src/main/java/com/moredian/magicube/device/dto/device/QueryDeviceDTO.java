package com.moredian.magicube.device.dto.device;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询设备条件
 *
 * <AUTHOR>
 */

@Data
public class QueryDeviceDTO implements Serializable {

    private static final long serialVersionUID = 6548162464337653689L;

    /**
     * 机构ID(空间所在Id)
     */
    private Long orgId;

    /**
     * 机构
     */
    private List<Long> orgIds;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 设备MAC地址
     */
    private String macAddress;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 设备类型列表
     */
    private List<Integer> deviceTypes;

    /**
     * 设备Id列表
     */
    private List<Long> deviceIds;

    /**
     * 在线状态 0-离线 1-在线
     */
    private Integer onlineStatus;

    /**
     * 设备容量超限：默认0-没有超限，1-超限
     */
    private Integer capacityExceeded;

    /**
     * 设备状态列表
     */
    private List<Integer> statusList;

    /**
     * 排序规则，0-按名称排序，1-按激活时间倒序，2-按激活时间正序
     */
    private Integer sort;

    /**
     * 每页显示数目
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNo;

    /**
     * 空间树Id列表
     */
    private List<Long> treeIds;

    /**
     * 模糊搜索的设备Id列表(用或条件来查)
     */
    private List<Long> queryDeviceIds;

    /**
     * @see com.moredian.magicube.common.enums.SceneTypeEnums
     */
    private Integer sceneType;

    /**
     * @see com.moredian.magicube.common.enums.SceneTypeEnums
     */
    private List<Integer> sceneTypes;

    /**
     * iot设备类型列表
     */
    private List<Integer> iotDeviceTypes;

    /**
     * 是否查询所有设备
     */
    private Integer allDeviceFlag;

    /**
     * 关联应用
     */
    private String appCode;

    /**
     * 是否查询子树标识 true-需要查询子树 false-不需要查询子树
     */
    private Boolean subTreeFlag;

    /**
     * 查询类型 1-我的设备 2-租赁设备 3-公共设备（目前只用于租户视角下的门禁设备记录页面）
     * 不传代表查询所有设备
     */
    private Integer type;

    /**
     * 设备组Id
     */
    private Long deviceCompositeId;

    /**
     * 是否查询所有设备组
     */
    private Boolean allDeviceCompositeFlag;

    /**
     * 设备组
     */
    private List<Long> deviceCompositeIds;
}