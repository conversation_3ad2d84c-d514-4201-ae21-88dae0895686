package com.moredian.magicube.device.dto.notice;

import com.moredian.magicube.device.enums.NoticeEventEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
@Data
@NoArgsConstructor
public class StatisticsModelDTO implements Serializable {

    private static final long serialVersionUID = 8650658789767410177L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 触发通知事件
     *
     * @see NoticeEventEnum
     */
    private Integer noticeEvent;

    /**
     * 通知结果，0-失败，1-成功
     */
    private Integer noticeResult;

    /**
     * 开始时间
     */
    private Date startDay;

    /**
     * 结束时间
     */
    private Date endDay;
}
