package com.moredian.magicube.device.constant;

/**
 * 导入设备白名单错误信息
 *
 * <AUTHOR>
 */
public class ImportErrorConstants {

    /**
     * 设备SN不能为空
     */
    public static final String DEVICE_SN_NOT_EXIST = "设备SN不能为空";

    /**
     * 设备SN格式有误
     */
    public static final String DEVICE_SN_FORMAT_ERROR = "设备SN格式有误";

    /**
     * 设备SN重复
     */
    public static final String DEVICE_SN_REPETITION = "设备SN重复";

    /**
     * 设备SN已经存在
     */
    public static final String DEVICE_SN_EXIST = "设备SN已经存在";

    /**
     * 无线MAC地址不能为空
     */
    public static final String WIRELESS_MAC_NOT_EXIST = "无线MAC地址不能为空";

    /**
     * 无线MAC格式有误
     */
    public static final String WIRELESS_MAC_FORMAT_ERROR = "无线MAC格式有误";

    /**
     * 有线MAC格式有误
     */
    public static final String WIRED_MAC_FORMAT_ERROR = "有线MAC格式有误";

    /**
     * 私钥不能为空
     */
    public static final String PRIVATE_KEY_NOT_EXIST = "私钥不能为空";

    /**
     * 私钥格式有误
     */
    public static final String PRIVATE_KEY_FORMAT_ERROR = "私钥格式有误";
}
