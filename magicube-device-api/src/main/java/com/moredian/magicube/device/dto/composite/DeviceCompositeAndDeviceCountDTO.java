package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @Auther: _AF
 * @Date: 2020/12/2 14:06
 * @Description:
 */
@Data
public class DeviceCompositeAndDeviceCountDTO implements Serializable {


    private static final long serialVersionUID = -6790574379080143009L;


    private Long deviceCompositeId;
    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 类型，使用，0-通用
     */
    private Integer bizType;
    /**
     * 组名
     */
    private String deviceCompositeName;

    /**
     * 路径
     */
    private String path;

    /**
     * 组 code
     */
    private String code;

    private Date gmtCreate;

    private Date gmtModify;

    //下面的设备item数量
    private Integer deviceCompositeItemCount = 0;


    //设备的type集合
    private List<Integer> deviceTypeList;

    /**
     * 设备列表
     */
    List <DeviceCompositeItemDTO> deviceList;

    /**
     * 是否还有子组
     * 下面的接口才会给这个字段塞值
     */
    private Boolean hasChildren = Boolean.FALSE;

    /**
     * 当需要查出子组的时候有值，且需要结合指定层级
     * 下面的接口才会给这个字段塞值
     * @see com.moredian.magicube.device.manager.impl.DeviceCompositeManagerImpl#getTreeAndDeviceByIds(Long, Integer, List)
     */
    private List<DeviceCompositeAndDeviceCountDTO> children;
}
