package com.moredian.magicube.device.dto.subject;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ScreenSaver implements Serializable {

    private static final long serialVersionUID = 7592160907716617071L;


    /**
     * 单屏保：1，多屏保：0
     */
    private Integer onlyOne;

    /**
     * 周期，1-7 ，如果type = 1不生效
     */
    private List<Integer> weekdays;


    /**
     * 是否静音，如果非屏保视频不生效
     */
    private Integer enableVoice;


    /**
     * 是否触摸屏幕、1：触摸，0：人脸
     */
    private Integer touchSwtich;

    /**
     * 屏保列表
     */
    private List<ScreenSaverItem> screenSaverItemList;


    /**
     * 屏保类型，1：会议期间正常展示，2：会议期间自动退出，3：自定义退出时间
     */
    private Integer displayType = 3;

    /**
     * 屏保开始类型，1：会议开始前，2：会议开始后，3：会议结束前，4：会议结束后
     */
    private Integer startType = 1;

    /**
     * 具体多少分钟
     */
    private Integer startTime = 15;


    /**
     * 屏保开始类型，1：会议开始前，2：会议开始后，3：会议结束前，4：会议结束后
     */
    private Integer endType = 2;

    /**
     * 具体多少分钟
     */
    private Integer endTime = 10;



}
