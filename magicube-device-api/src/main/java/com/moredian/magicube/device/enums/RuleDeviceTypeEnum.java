package com.moredian.magicube.device.enums;

import com.moredian.bee.common.utils.StringUtil;
import com.moredian.magicube.common.enums.DeviceType;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

/**
 * <p>
 * 规则设备类型集合
 * </p>
 *
 * <AUTHOR> chen
 * @since 2024/6/13 9:05
 **/
@Getter
public enum RuleDeviceTypeEnum {
    /**
     * 规则设备类型集合
     */
    AIR_CONDITIONER(1, "空调",
        Lists.newArrayList(DeviceType.AIR_CONDITIONER.getValue(),
            DeviceType.AIR_CONDITIONING_GATEWAY.getValue(),
            DeviceType.RAY_SINGLE_AIR_CONTROLLER.getValue(),
            DeviceType.AIR_CONDITIONING_GATEWAY_64.getValue()
        )),
    SWITCH(2, "开关",
        Lists.newArrayList(
            DeviceType.SWITCH.getValue(),
            DeviceType.TURN_ON_OFF_ONE_BUTTON.getValue(),
            DeviceType.TURN_ON_OFF_THREE_BUTTON.getValue(),
            DeviceType.TURN_ON_OFF_TWO_BUTTON.getValue()
        )),
    MEASUREMENT_SOCKET(3, "插座",
        Lists.newArrayList(
            DeviceType.MEASUREMENT_SOCKET.getValue()
        )),
    ;

    public static final List<Integer> SUPPORT_ALL_DEVICE_TYPES = new ArrayList<>();

    static {
        for (RuleDeviceTypeEnum r : RuleDeviceTypeEnum.values()) {
            List<Integer> deviceTypes = r.getDeviceTypes();
            SUPPORT_ALL_DEVICE_TYPES.addAll(deviceTypes);
        }
    }

    private final int code;

    private final List<Integer> deviceTypes;

    private final String desc;

    RuleDeviceTypeEnum(int code, String desc, List<Integer> deviceTypes) {
        this.code = code;
        this.deviceTypes = deviceTypes;
        this.desc = desc;
    }

    /**
     * 判断物联网设备类型是否包含在规则支持的设备类型中
     */
    public static Boolean containDeviceType(Integer deviceType) {
        return SUPPORT_ALL_DEVICE_TYPES.contains(deviceType);
    }

    /**
     * 将物联网设备类型转换成规则定义的设备类型
     */
    public static List<Integer> convertRuleDeviceType(List<Integer> dbDeviceTypes) {
        Set<Integer> ruleDeviceType = new HashSet<>();
        for (RuleDeviceTypeEnum r : RuleDeviceTypeEnum.values()) {
            List<Integer> deviceTypes = new ArrayList<>(r.getDeviceTypes());
            deviceTypes.retainAll(dbDeviceTypes);
            if (CollectionUtils.isNotEmpty(deviceTypes)) {
                ruleDeviceType.add(r.getCode());
            }
        }
        return new ArrayList<>(ruleDeviceType);
    }

    /**
     * 将规则设备类型转换成支持的物联网设备类型
     */
    public static List<Integer> convertDeviceType(List<Integer> showDeviceTypes) {
        if (CollectionUtils.isEmpty(showDeviceTypes)) {
            return new ArrayList<>();
        }
        Set<Integer> deviceType = new HashSet<>();

        for (RuleDeviceTypeEnum r : RuleDeviceTypeEnum.values()) {
            if (showDeviceTypes.contains(r.getCode())) {
                deviceType.addAll(r.getDeviceTypes());
            }
        }
        return new ArrayList<>(deviceType);
    }

    /**
     * 将规则设备类型转换成对应描述，并用逗号分开
     */
    public static String convertDeviceTypeDesc(List<Integer> showDeviceTypes) {
        return showDeviceTypes.stream()
            .map(v -> {
                    if (RuleDeviceTypeEnum.AIR_CONDITIONER.getCode() == v) {
                        return RuleDeviceTypeEnum.AIR_CONDITIONER.getDesc();
                    }

                    if (RuleDeviceTypeEnum.SWITCH.getCode() == v) {
                        return RuleDeviceTypeEnum.SWITCH.getDesc();
                    }
                    if (RuleDeviceTypeEnum.MEASUREMENT_SOCKET.getCode() == v) {
                        return RuleDeviceTypeEnum.MEASUREMENT_SOCKET.getDesc();
                    }
                    return StringUtil.EMPTY_STRING;
                }
            ).distinct().sorted().collect(Collectors.joining("、"));
    }
}
