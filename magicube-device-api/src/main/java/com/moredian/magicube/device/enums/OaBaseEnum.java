package com.moredian.magicube.device.enums;

import com.moredian.bee.common.utils.StringUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description：oa base 枚举类
 * @date ：2024/10/30 9:28
 */
@Getter
public enum OaBaseEnum {
    MD(1, "魔蓝"),
    DING(2, "钉钉"),
    ;

    private final int code;

    private final String desc;

    OaBaseEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (OaBaseEnum value : OaBaseEnum.values()) {
            if (code != null && code.equals(value.code)){
                return value.desc;
            }
        }
        return StringUtil.EMPTY_STRING;
    }
}
