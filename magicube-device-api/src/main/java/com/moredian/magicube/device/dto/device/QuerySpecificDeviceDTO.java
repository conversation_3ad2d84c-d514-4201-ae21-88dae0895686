package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/8/12
 */
@Getter
@Setter
@ToString
public class QuerySpecificDeviceDTO implements Serializable {

    private static final long serialVersionUID = 3163473027541798023L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * 设备SN
     */
    private List<String> deviceSnList;
}
