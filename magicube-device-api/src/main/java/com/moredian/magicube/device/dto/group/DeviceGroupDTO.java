package com.moredian.magicube.device.dto.group;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @class: DeviceGroupDto
 * @description:
 * @author: Yyp
 * @create: 2021-01-25 11:33
 **/
@Getter
@Setter
@ToString
public class DeviceGroupDTO implements Serializable {

    private static final long serialVersionUID = -2458740754171004452L;

    private Long deviceGroupId;
    private Long orgId;
    private Long deviceId;
    private Long groupId;
    private String groupCode;
    private Date gmtCreate;
    private Date gmtModify;
}
