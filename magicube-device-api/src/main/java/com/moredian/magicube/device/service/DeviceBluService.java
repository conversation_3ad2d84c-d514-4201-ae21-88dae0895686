package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.blu.DeviceBLuCheckApiReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuCheckReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectApiReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectApiResp;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectResp;
import com.moredian.magicube.device.dto.blu.DeviceBluConnectDeCryptReq;

public interface DeviceBluService {

    /**
     * 检查签名校验 开放接口
     * @param deviceBLuConnectApiReq
     * @return
     */
    ServiceResponse<DeviceBLuConnectApiResp> checkSignApi(
        DeviceBLuConnectApiReq deviceBLuConnectApiReq);


    /**
     * 检查签名校验
     * @param deviceBLuConnectReq
     * @return
     */
    ServiceResponse<DeviceBLuConnectResp> checkSign( DeviceBLuConnectReq deviceBLuConnectReq);

    /**
     * 解密内容
     * @param deviceBluConnectDeCryptReq
     * @return 解密字符串
     */
    ServiceResponse<String> decrypt(DeviceBluConnectDeCryptReq deviceBluConnectDeCryptReq);



    /**
     * 校验合法性 开放接口
     * @param bLuCheckApiReq
     * @return 校验合法性
     */
    ServiceResponse<Boolean> checValid(DeviceBLuCheckApiReq bLuCheckApiReq);

    /**
     * 校验合法性
     * @param bLuCheckReq
     * @return 校验合法性
     */
    ServiceResponse<Boolean> checValid(DeviceBLuCheckReq bLuCheckReq);


}
