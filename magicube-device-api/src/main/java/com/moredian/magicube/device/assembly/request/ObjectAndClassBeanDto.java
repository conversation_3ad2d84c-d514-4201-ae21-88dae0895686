package com.moredian.magicube.device.assembly.request;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/2 17:30
 */
@Data
public class ObjectAndClassBeanDto implements Serializable {

    private static final long serialVersionUID = -2983649001577245876L;

    private String msgStr;


    private String clazzName;

    /**
     * 是否合并
     */
    private Boolean merge = Boolean.FALSE;

    /**
     * 合并字段，这个字段是一个list
     */
    private String resetField;
}
