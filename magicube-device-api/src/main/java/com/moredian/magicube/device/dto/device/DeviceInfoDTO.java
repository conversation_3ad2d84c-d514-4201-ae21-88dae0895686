package com.moredian.magicube.device.dto.device;

import com.moredian.magicube.common.enums.DirectionEnum;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 设备信息
 *
 * <AUTHOR>
 */

@Data
public class DeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = -3700545350330079121L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 位置Id
     */
    private Long positionId;

    /**
     * 位置
     */
    private String position;

    /**
     * 第三方设备Id
     */
    private String tpId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 激活时间
     */
    private Long activeTime;

    /**
     * 扩展信息
     */
    private String extendsInfo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否在线
     */
    private Boolean online;


    /**
     * 设备容量超限：默认0-没有超限，1-超限
     */
    private Integer capacityExceeded;

    /**
     * 设备容量
     */
    private Integer deviceCapacity;

    /**
     * 空间树Id
     */
    private Long treeId;

    /**
     * 全路径名称
     */
    private String pathTreeName;

    /**
     * 全路径id
     */
    private String path;

    /**
     * 空间属性id
     */
    private Long propertyTreeNodeId;

    /**
     * 父级空间树类型节点id
     */
    private Long parentTreeNodeId;

    /**
     * 出入口/闸机通道需求使用,不管设备绑定的是空间/出入口, 该id为空间id
     */
    private Long spaceId;

    /**
     * 空间属性名称[出入口,闸机通道]
     */
    private String propertyTreeName;

    private Integer direction;

    /**
     * 空间树名称(酒店业务需要)
     */
    private String treeName;

    /**
     * 设备电量
     */
    private Integer electric;

    /**
     * 电量最新上报时间
     */
    private Date latestReportElectric;

    /**
     * @see com.moredian.magicube.common.enums.SceneTypeEnums
     */
    private Integer sceneType;

    /**
     * 设备模式应用列表
     */
    private List<String> deviceModelAppCodeList;

    /**
     * 设备关联应用
     */
    private List<DeviceRelateAppDTO> deviceRelatedApp;

    /**
     * 默认跳转应用
     */
    private String appCode;

    /**
     * 设备可关联应用
     */
    private String appCodeList;

    /**
     * 设备运行环境
     * @see com.moredian.magicube.device.enums.EnvConfigEnum
     */
    private Integer environment;

    /**
     * 是否为虚拟设备
     * 0-非虚拟设备，1-虚拟设备
     */
    private Integer virtualFlag;

    /**
     * 父设备sn
     */
    private String parentDeviceSn;

    /**
     * 是否租赁设备
     */
    private Boolean tenantDevice;

    private Integer deviceFlag;

    /**
     * 设备时区
     */
    private String deviceTimeZone;

    /**
     * 设备语言
     */
    private String deviceLanguage;
    
    public String showName() {
         String showName = (StringUtils.isBlank(pathTreeName) ? "" : this.pathTreeName)
                + (StringUtils.isBlank(this.propertyTreeName) ? "" : "/" + this.propertyTreeName)
                + (this.direction == null ? "" : "/" + DirectionEnum.getDirectionName(this.direction))
                + (StringUtils.isBlank(this.position) ? "" : "/" + this.position);
        //若显示名以/结尾 则去除/
        if (StringUtils.isNotBlank(showName) && showName.endsWith("/")){
            showName = showName.substring(0, showName.lastIndexOf("/"));
        }
        //若显示名以/开始 则去除/
        if (StringUtils.isNotBlank(showName) && showName.startsWith("/")){
            showName = showName.substring(1);
        }
        return showName;
    }
}