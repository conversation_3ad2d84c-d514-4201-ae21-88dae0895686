package com.moredian.magicube.device.dto.type;

import java.io.Serializable;
import lombok.Data;

/**
 * 设备类型查询条件
 *
 * <AUTHOR>
 */

@Data
public class QueryDeviceTypeDTO implements Serializable {

    private static final long serialVersionUID = -8798932990602257418L;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 产品型号（子型号）编码
     */
    private String productModelCode;

    /**
     * 模糊搜索条件
     */
    private String keywords;
}