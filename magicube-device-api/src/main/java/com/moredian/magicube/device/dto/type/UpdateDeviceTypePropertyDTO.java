package com.moredian.magicube.device.dto.type;

import java.io.Serializable;
import lombok.Data;

/**
 * 编辑设备类型属性
 *
 * <AUTHOR>
 */
@Data
public class UpdateDeviceTypePropertyDTO implements Serializable {

    private static final long serialVersionUID = 1908821325905383880L;

    /**
     * 设备类型属性Id
     */
    private Long id;

    /**
     * 属性描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
}
