package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.AppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.PageAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.QueryAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.SaveAppTypeRedirectDTO;

/**
 * <AUTHOR>
 * @Description 设备app类型跳转链接接口
 * @Date 2023/12/18
 */
public interface AppTypeRedirectService {

    /**
     * 添加或编辑appType跳转链接
     *
     * @param dto
     * @return
     */
    ServiceResponse<Long> save(SaveAppTypeRedirectDTO dto);

    /**
     * 获取appType跳转详情
     *
     * @param dto
     * @return
     */
    ServiceResponse<AppTypeRedirectDTO> detail(QueryAppTypeRedirectDTO dto);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    ServiceResponse<Boolean> deleteById(Long id);

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    ServiceResponse<Pagination<AppTypeRedirectDTO>> page(PageAppTypeRedirectDTO dto);
}
