package com.moredian.magicube.device.dto.activate;

import lombok.Data;
import java.io.Serializable;

/**
 * 生成激活码
 *
 * <AUTHOR>
 */
@Data
public class GenerateQrCodeDTO implements Serializable {

    private static final long serialVersionUID = -353316265488494411L;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 识别服务：默认为云眼 null 或其他不包含1  慧眼:1
     */
    private Integer regCoreType;

    /**
     * 如果是慧眼,可设置算法,1:旷世, 2:依图
     */
    private Integer regArithmetic;

    /**
     * 设备类型
     */
    private Integer equipmentType;

    /**
     * 是否验证checkCode，没有内置秘钥签名验证的激活，是需要验证checkCode的
     * 有内置秘钥的激活，以前验证过设备内置秘钥签名，不再验证checkCode(好像已经不再使用)
     */
    private Boolean isCheck = true;

    private Integer appType;

    /**
     * 校验码：mac+privateKey做签名
     */
    private String checkCode;
}
