package com.moredian.magicube.device.dto.notice;

import com.moredian.magicube.device.enums.NoticeEventEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
@Data
@NoArgsConstructor
public class NoticeRecordDTO implements Serializable {

    private static final long serialVersionUID = -8169960881085386969L;

    /**
     * 记录ID
     */
    private Long noticeRecordId;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 触发通知事件
     *
     * @see NoticeEventEnum
     */
    private Integer noticeEvent;

    /**
     * 通知结果，0-失败，1-成功
     */
    private Integer noticeResult;

    /**
     * 上一次离线时间
     */
    private Long lastOfflineTime;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
