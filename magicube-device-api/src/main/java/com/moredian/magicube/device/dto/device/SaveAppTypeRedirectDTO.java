package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/18
 */
@Getter
@Setter
@ToString
public class SaveAppTypeRedirectDTO implements Serializable {

    private static final long serialVersionUID = -6063736834573015585L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 跳转链接
     */
    private String redirectUrl;
}
