package com.moredian.magicube.device.service;


import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.DeviceAppRelationConfigDTO;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.DeviceAppRelationFixedConfigDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;

import java.util.List;
import java.util.Map;

public interface DeviceAppRelationConfigService {


    /**
     * 分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<Pagination<DeviceAppRelationConfigDTO>> listPage(QueryDeviceAppRelationConfigDTO dto);

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    ServiceResponse<List<DeviceAppRelationConfigDTO>> listAll();


    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    ServiceResponse<DeviceAppRelationConfigDTO> getById(Long id);

    /**
     * 新增
     *
     * @param deviceAppRelationConfigDTO 新增的记录
     * @return 主键id
     */
    ServiceResponse<Long> insert(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO);

    /**
     * 修改所有字段
     *
     * @param deviceAppRelationConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> update(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO);

    /**
     * 修改不为null字段
     *
     * @param deviceAppRelationConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> updateSelective(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO);

    /**
     * 删除
     *
     * @param deviceAppRelationConfigDTO 待删除的记录
     * @return 是否成功true/false
     */
    ServiceResponse<Boolean> delete(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO);

    /**
     * 批量获取设备关联应用配置
     * spaceType可以为null， 为null时 查询设备支持的所有应用
     */
    ServiceResponse<Map<Long, List<DeviceAppRelationConfigDTO>>> getByOrgIdAndDeviceIdList(Long orgId, List<Long> deviceId,
    Integer spaceType);

    /**
     * 获取设备关联应用配置
     */
    ServiceResponse<DeviceAppRelationConfigDTO> getByOrgIdAndDeviceId(Long orgId, Long deviceId, Integer spaceType);


    /**
     * 获取设备可承载所有应用列表
     * @param orgId 机构id
     * @param deviceId 设备id
     * @return
     */
    ServiceResponse<List<DeviceAppRelationConfigDTO>> getAllAppByOrgIdAndDeviceId(Long orgId, Long deviceId);

    /**
     * 根据设备sn查询设备支持的模式
     * @param orgId 机构id
     * @param deviceSn 设备sn
     *
     * @return 返回集合，没有返回空List
     */
    ServiceResponse<List<Integer>> listSceneTypeByDeviceSn(Long orgId, String deviceSn);
}