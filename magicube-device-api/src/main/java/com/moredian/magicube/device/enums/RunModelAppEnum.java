package com.moredian.magicube.device.enums;

/**
 * 设备支持应用Appcode枚举
 * <AUTHOR>
 */
public enum RunModelAppEnum {
    FACEDOOR("facedoor", "001","通行"),
    ATTENDANCE("attendance", "002","考勤"),
    MEETING("meeting", "003", "会议"),
    APPROVAL("approval", "004", "审批"),
    VISITOR("visitor", "005", "访客"),
    LADDER("ladder", "009", "梯控"),
    SPACE("space","010","空间档案"),
    PARK("park","011", "智慧园区"),
    VIDEOCALL("videoCall", "101", "亲情通话"),
    VIDELCALL_OPERATION("videlCallOperation", "102", "亲情通话运营"),
    CLASSCARD("clasCard", "103", "数智教室"),
    DORMITORY("dormitory", "104","宿管"),
    DATASCREEN("datascreen", "105","大屏"),
    VENUES("venues","106","空间预约"),
    MYSTERY("mystery","107","天机"),
    CABINET("cabinet","108","储物"),
    AI("ai", "109", "慧眼AI");


    private String app;
    private String code;
    private String description;


    RunModelAppEnum(String app, String code, String description) {
        this.app = app;
        this.code = code;
        this.description = description;
    }
}
