/**
 * 
 */
package com.moredian.magicube.device.dto.upgrade;

import java.io.Serializable;

public class UpdateStateResponse implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1252803363691881827L;

	private int versionCode; // app版本号

	private int status ;//升级状态；  1 : 下载中; 2 : 下载完成，准备安装; 3:安装失败（未知原因）; 4 : 安装成功

	public int getVersionCode() {
		return versionCode;
	}

	public void setVersionCode(int versionCode) {
		this.versionCode = versionCode;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}
	
}
