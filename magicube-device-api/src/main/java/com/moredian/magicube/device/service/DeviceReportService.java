package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.DeviceReportInfoDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceInfoDTO;

/**
 * <AUTHOR>
 * @Description 终端设备上报信息接口
 * @Date 2024/1/24
 */
public interface DeviceReportService {

    /**
     * 通知设备上报电量，wifi强度，版本细腻，本地时间接口
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> notifyDeviceReportInfo(DeviceReportInfoDTO dto);

    /**
     * 门锁设备上报电量，wifi强度，版本细腻，本地时间接口
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> reportDeviceInfo(ReportDeviceInfoDTO dto);
}
