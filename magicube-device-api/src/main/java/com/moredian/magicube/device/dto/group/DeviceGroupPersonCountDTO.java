package com.moredian.magicube.device.dto.group;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-02-06
 */
@Data
public class DeviceGroupPersonCountDTO implements Serializable {

    private static final long serialVersionUID = 1087882770377780750L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 权限组应用类型
     * 为空时 查所有
     */
    private List<Integer> appTypeList;

}
