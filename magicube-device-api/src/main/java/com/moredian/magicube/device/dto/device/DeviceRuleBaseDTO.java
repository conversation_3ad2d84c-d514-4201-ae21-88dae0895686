package com.moredian.magicube.device.dto.device;

import com.moredian.magicube.common.enums.DeviceType;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleDTO.java, v 1.0 Exp $
 */
@Data
public class DeviceRuleBaseDTO implements Serializable {
    private static final long serialVersionUID = -9051298689032467153L;

    /**
     * 场景名称
     */
    private String modeName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 触发描述
     */
    private String triggerDescription;

    /**
     * 触发提醒
     */
    private String triggerTip;

    private String picUrl;

    private String picDetailUrl;

    private Boolean humanPriority;
}
