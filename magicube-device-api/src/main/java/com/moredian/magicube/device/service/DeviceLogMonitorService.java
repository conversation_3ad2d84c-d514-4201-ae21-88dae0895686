package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.monitor.MonitorLogDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 设备日志埋点接口
 * @Date 2023/10/13
 */
public interface DeviceLogMonitorService {

    /**
     * 上传设备日志
     *
     * @param logList
     * @return
     */
    ServiceResponse<Integer> syncMonitorLog(String deviceSn, List<MonitorLogDto> logList);
}
