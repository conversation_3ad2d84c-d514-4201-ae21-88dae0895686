package com.moredian.magicube.device.dto.project;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/10
 */
@Getter
@Setter
@ToString
public class DeviceProjectInfoDTO implements Serializable {

    private static final long serialVersionUID = -1609206172847246942L;

    /**
     * 项目Id
     */
    private String projectId;

    /**
     * 版本信息
     */
    private String version;

    /**
     * 定制参数集合
     */
    private String customParams;

    /**
     * 场景
     */
    private Integer sceneType;
}
