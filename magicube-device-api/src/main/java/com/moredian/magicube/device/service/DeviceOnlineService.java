package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.DeviceOnlineStateDTO;
import com.moredian.ota.api.msg.DeviceOnlineStateMsg;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceOnlineService {

    /**
     * 根据机构id和设备id列表查询设备在离线信息
     * @param orgId 机构id
     * @param deviceIdList  设备id列表
     * @return  返回设备
     */
    ServiceResponse<List<DeviceOnlineStateDTO>> selectByOrgId(Long orgId, List<Long> deviceIdList);

    /**
     * 根据设备SN 查询设备在离线信息
     * @param deviceSnList 设备SN
     * @return
     */
    ServiceResponse<List<DeviceOnlineStateDTO>> selectByDeviceSnList(List<String> deviceSnList);

    /**
     * 插入设备在离线信息<为支持塔上调用，暴露出去>
     * @param msg
     * @return
     */
    ServiceResponse<Boolean> insertDeviceOnlineStateMsg(DeviceOnlineStateMsg msg);

}
