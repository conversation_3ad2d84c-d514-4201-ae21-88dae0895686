package com.moredian.magicube.device.dto.group;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/4/20
 */
@Data
@NoArgsConstructor
public class GroupDeviceSizeDTO implements Serializable {

    private static final long serialVersionUID = -7216773089443530352L;

    /**
     * 权限组ID
     */
    private Long groupId;

    /**
     * 关联设备数量
     */
    private Integer deviceSize;
}
