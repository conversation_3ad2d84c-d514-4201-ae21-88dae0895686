package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.third.AddThirdDeviceRequest;
import com.moredian.magicube.device.dto.third.AddThirdDeviceTypeRequest;
import com.moredian.magicube.device.dto.third.QueryThirdDeviceRequest;
import com.moredian.magicube.device.dto.third.ThirdDeviceResponse;
import com.moredian.magicube.device.dto.third.ThirdDeviceTypeResponse;
import com.moredian.magicube.device.dto.third.UpdateThirdDeviceRequest;
import java.util.List;

/**
 * @description 第三方设备服务
 * @author: xiesj
 * @date 2022/12/29 9:53
 */
public interface ThirdDeviceService {

    /**
     * 获取设备类型列表
     * @return
     */
    ServiceResponse<List<ThirdDeviceTypeResponse>> listThirdDeviceType();

    /**
     * 新增设备类型
     * @return
     */
    ServiceResponse<Boolean> addThirdDeviceType(AddThirdDeviceTypeRequest request);

    /**
     * 删除设备类型
     * @return
     */
    ServiceResponse<Boolean> deleteThirdDeviceType(Integer deviceType);

    /**
     * 第三方设备分页
     * @return
     */
    ServiceResponse<Pagination<ThirdDeviceResponse>> paginationThirdDevice(Pagination<ThirdDeviceResponse> pagination, QueryThirdDeviceRequest request);

    /**
     * 新增第三方设备
     * @param request
     * @return
     */
    ServiceResponse<Boolean> addThirdDevice(AddThirdDeviceRequest request);

    /**
     * 删除第三方设备
     * @param thirdDeviceId
     * @return
     */
    ServiceResponse<Boolean> deleteThirdDevice(Long thirdDeviceId);

    /**
     * 修改第三方设备
     * @param request
     * @return
     */
    ServiceResponse<Boolean> updateThirdDevice(UpdateThirdDeviceRequest request);

    /**
     * 查询第三方设备列表
     * @param orgId
     * @param thirdDeviceIdList
     * @return
     */
    ServiceResponse<List<ThirdDeviceResponse>> getThirdDeviceByIdList(Long orgId, List<Long> thirdDeviceIdList);
}
