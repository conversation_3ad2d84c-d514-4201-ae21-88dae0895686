package com.moredian.magicube.device.dto.migration;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 设备迁移状态
 * @author: wbf
 * @date: 2024/8/12 上午11:12
 */
@Data
public class MigrationDeviceStatusRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 迁移状态
     *
     * @see com.moredian.magicube.device.enums.MigrationStatusEnum
     */
    private Integer migrationStatus;
    /**
     * 是否全部完成
     */
    private Boolean isAllEnd;
}
