package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.common.valid.BeeValid;
import com.moredian.magicube.device.dto.accessory.AccessoryInfoDTO;
import com.moredian.magicube.device.dto.accessory.AccessoryQueryDTO;
import com.moredian.magicube.device.dto.accessory.BindAccessoryDTO;
import com.moredian.magicube.device.dto.accessory.UnbindAccessoryDTO;
import java.util.List;
import javax.validation.Valid;

/**
 * 设备配件（外设）接口
 *
 * <AUTHOR>
 * @since 2021/12/8
 */
@BeeValid
public interface AccessoryService {

    /**
     * 配件（外设）绑定
     *
     * @param dto 绑定配件请求
     * @return true or false
     */
    ServiceResponse<Boolean> bindAccessory(@Valid BindAccessoryDTO dto);

    /**
     * 配件（外设）解绑
     *
     * @param dto 解绑配件请求
     * @return true or false
     */
    ServiceResponse<Boolean> unbindAccessory(@Valid UnbindAccessoryDTO dto);

    /**
     * 列表 - 设备配件（外设）查询
     *
     * @param dto 设备列表查询参数
     * @return true or false
     */
    ServiceResponse<List<AccessoryInfoDTO>> listAccessoryInfo(@Valid AccessoryQueryDTO dto);

}
