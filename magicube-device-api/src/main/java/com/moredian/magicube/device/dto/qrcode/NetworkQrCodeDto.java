package com.moredian.magicube.device.dto.qrcode;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Classname： NetworkQrCodeDto
 * @Date: 2023/3/23 3:11 下午
 * @Author: _AF
 * @Description:
 */
@Data
public class NetworkQrCodeDto implements Serializable {

    private static final long serialVersionUID = 1284458633725756196L;

    private Long id;

    private Long orgId;

    private String deviceAddressName;

    /**
     * 1-有线，默认
     * 2-无线
     */
    private Integer networkType = 1;

    /**
     * 1-DHCP
     * 2-静态，默认
     */
    private Integer connectType = 2;

    /**
     * 私有云，默认关
     */
    private Integer privateCloudSwitch = 0;

    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;

    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;

    /**
     * 私有云配置
     */
    private PrivateCloud privateCloud;

    /**
     * 二维码地址
     */
    private String url;

    /**
     * 空间id
     */
    private Long treeId;

    /**
     * 设备配网码状态 0-待处理 1-处理中 2-处理完成
     */
    private Integer status;

    /**
     * 绑定机构页面url（仅用于钉钉SDK激活流程）
     */
    private String bindOrgUrl;

    /**
     * 出入方向 1入口 2出口
     */
    private Integer direction;

    private Date gmtCreate;

    private Date gmtModify;

    /**
     * 服务器地址 目前仅用于保存私有化服务器服务器地址
     */
    private String serverAddress;
}
