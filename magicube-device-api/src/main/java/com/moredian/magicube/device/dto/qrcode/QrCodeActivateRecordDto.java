package com.moredian.magicube.device.dto.qrcode;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Classname： QrCodeActivateRecordDto
 * @Date: 2023/1/18 3:10 下午
 * @Author: _AF
 * @Description: 激活记录
 */
@Data
public class QrCodeActivateRecordDto implements Serializable {
    private static final long serialVersionUID = -5533210892671243796L;

    private Long id;

    private Long orgId;
    /**
     * 激活码id
     */
    private Long qrCodeId;
    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    private Date gmtCreate;
}
