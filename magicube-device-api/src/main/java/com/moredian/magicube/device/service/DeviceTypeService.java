package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.type.DeviceTypeDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDetailDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypeDTO;
import com.moredian.magicube.device.dto.type.QueryDeviceTypeDTO;
import com.moredian.magicube.device.dto.type.UpdateDeviceTypeDTO;

import java.util.List;

/**
 * 设备类型相关接口
 *
 * <AUTHOR>
 */
public interface DeviceTypeService {

    /**
     * 新增设备类型
     *
     * @param dto 设备类型信息
     * @return
     */
    ServiceResponse<Long> insert(InsertDeviceTypeDTO dto);

    /**
     * 编辑设备类型信息
     *
     * @param dto 设备类型信息
     * @return
     */
    ServiceResponse<Long> update(UpdateDeviceTypeDTO dto);

    /**
     * 删除设备类型
     *
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<Boolean> deleteByDeviceType(Integer deviceType);

    /**
     * 查询设备类型列表
     *
     * @return
     */
    ServiceResponse<List<DeviceTypeDTO>> list();

    /**
     * 根据设备类型列表查询设备类型列表
     *
     * @param deviceTypes 设备类型列表
     * @return
     */
    ServiceResponse<List<DeviceTypeDTO>> listByTypes(List<Integer> deviceTypes);

    /**
     * 查询设备类型详情
     *
     * @param deviceTypeId 设备类型Id
     * @return
     */
    ServiceResponse<DeviceTypeDetailDTO> getById(Long deviceTypeId);

    /**
     * 查询设备类型名称
     *
     * @param deviceType 设备类型
     * @return 设备类型名称
     */
    ServiceResponse<String> getDeviceTypeName(Integer deviceType);

    /**
     * 根据条件查询查询设备类型列表
     *
     * @param dto 设备类型
     * @return
     */
    ServiceResponse<List<DeviceTypeDTO>> listByCondition(QueryDeviceTypeDTO dto);

    /**
     * 根据产品型号查询设备类型信息列表
     *
     * @param productModelCode
     * @return 设备类型名称
     */
    ServiceResponse<DeviceTypeDTO> getByProductModelCode(String productModelCode);

    /**
     * 根据产品型号列表查询设备类型信息列表
     *
     * @param productModelCodes 产品型号列表
     * @return 设备类型名称
     */
    ServiceResponse<List<DeviceTypeDTO>> listByProductModelCodes(List<String> productModelCodes);

    /**
     * 根据设备内部名称模糊查询设备类型列表
     *
     * @param keywords 设备内部
     * @return
     */
    ServiceResponse<List<DeviceTypeDTO>> listLikeName(String keywords);

    /**
     * 根据设备sn+设备类型查询设备展示的名称（用于审计日志）
     * 先SPU，再设备类型维护的名称
     * @param deviceSn 设备sn
     * @param deviceType 设备类型
     * @return 展示名称-SN后6位
     */
    ServiceResponse<DeviceTypeDTO> getDeviceDisplayName(String deviceSn, Integer deviceType);
}