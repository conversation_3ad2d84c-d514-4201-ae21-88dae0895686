package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.global.GlobalConfigDTO;

/**
 * @Auther: _AF
 * @Date: 12/28/21 15:10
 * @Description: 获取系统配置
 */
public interface GlobalConfigService {

    /**
     * 根据名称获取有效的配置信息
     *
     * @param configName
     * @return
     */
    ServiceResponse<GlobalConfigDTO> getByConfigName(String configName);
}
