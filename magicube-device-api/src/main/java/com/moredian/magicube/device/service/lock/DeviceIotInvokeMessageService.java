package com.moredian.magicube.device.service.lock;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.lock.InvokeMessageDTO;
import com.moredian.magicube.device.dto.lock.MessageInfoDTO;
import com.moredian.magicube.device.dto.lock.UpdateMessageDTO;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-03 16:35
 */
public interface DeviceIotInvokeMessageService {

    /**
     * iot 功能调用入参
     */
    ServiceResponse<Long> invoke(InvokeMessageDTO invokeMessageDTO);


    /**
     * 根据消息id更新消息状态
     * @param updateMessageDTO --> messageId not null
     *                         --> messageStatus not null
     * @see com.moredian.magicube.device.enums.iot.MessageStatusEnum
     */
    ServiceResponse<Boolean> updateStatus(UpdateMessageDTO updateMessageDTO);


    /**
     * messageId查询消息详情
     */
    ServiceResponse<MessageInfoDTO> getByMessageId(Long orgId, Long messageId);
}
