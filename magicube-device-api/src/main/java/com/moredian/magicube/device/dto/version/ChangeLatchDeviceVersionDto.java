package com.moredian.magicube.device.dto.version;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 门锁设备上报版本信息
 * @Date 2023/8/8
 */
@Getter
@Setter
@ToString
public class ChangeLatchDeviceVersionDto implements Serializable {

    private static final long serialVersionUID = -3223453080022158383L;

    private Long orgId;

    private Long deviceId;

    private String deviceSn;

    /**
     * 全局app类型
     */
    private Integer appType;

    /**
     * 全局app版本
     */
    private Integer appVersion;

    /**
     * 全局rom类型
     */
    private Integer romType;

    /**
     * 全局rom版本
     */
    private Integer romVersion;


    /**
     * 锁板3861L版本
     */
    private Integer lockBoard3861LVersion;

    /**
     * 锁板804版本
     */
    private Integer lockBoard804Version;

    /**
     * 锁板805版本
     */
    private Integer lockBoard805Version;

    /**
     * 模型版本
     */
    private Integer modelVersion;

    /**
     * 锁版本
     */
    private Integer lockVersion;
}
