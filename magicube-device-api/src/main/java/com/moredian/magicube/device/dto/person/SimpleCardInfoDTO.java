package com.moredian.magicube.device.dto.person;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 应终端要求，由于拆分包机制问题，导致数据需要放在一包中下发。需要小于4K，所以名字缩写
 * @Date 2023/12/25
 */
@Getter
@Setter
@ToString
public class SimpleCardInfoDTO implements Serializable {

    private static final long serialVersionUID = -5623956092628057939L;

    /**
     * 卡号
     */
    private String cN;

    /**
     * 卡状态
     */
    private Integer cS;
}
