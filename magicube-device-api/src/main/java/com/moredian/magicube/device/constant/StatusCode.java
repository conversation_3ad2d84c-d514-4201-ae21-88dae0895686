package com.moredian.magicube.device.constant;

/**
 * Created by wuyb on 2016/12/7.
 */
public class StatusCode {

    public final static int NOT_SCAN = 0;
    public final static int SCAN = 1;
    public final static int EXPIRED = 2;

    public final static int SCAN_SUCCESS = 1;

    public final static int ACTIVE_WAIT = 0;
    public final static int ACTIVE_SUCCESS = 1;
    public final static int ACTIVE_FAIL = 3;
    public final static int ACTIVE_EXPIRED = 2;

    public final static int DEVICE_NOT_RECORDED = 6;
    public final static int THIRD_PARTY_ACTIVE_SUCCESS = 1;
    public final static int THIRD_PARTY_ACTIVE_WAIT = 5;
    public final static int THIRD_PARTY_ACTIVE_FAILED = 4;
    public final static int DEVICE_NOT_AUTHORIZATION = 8;
}