package com.moredian.magicube.device.dto.qrcode;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Classname： ActivateQrCodeCreateDto
 * @Date: 2023/1/4 11:44 上午
 * @Author: _AF
 * @Description: 创建激活二维码对象
 */
@Data
public class ActivateQrCodeSimpleDto implements Serializable {

    private static final long serialVersionUID = 6643672732609559518L;

    private Long id;

    private Long orgId;
    /**
     * 平台账号
     */
    private Long accountId;
    /**
     * 二维码名称
     */
    private String qrCodeName;


    private String deviceAddressName;

    private Date gmtCreate;
    /**
     * 角色类型
     */
    private Integer roleType;
    /**
     * 激活码类型
     */
    private Integer functionType;
    /**
     * 私有云配置
     */
    private PrivateCloud privateCloud;


}
