package com.moredian.magicube.device.enums.iot;

import lombok.Getter;


/**
 * <AUTHOR>
 * @Description iot 设备功能调用id
 * @create 2024-12-26 15:39
 */
@Getter
public enum IotFunctionIdEnum {

    RY_GATEWAY_LOG_UPLOAD(20001, "logupload", "瑞瀛网关上传日志"),
    TTLOCK_REMOTE_OPEN(10001, "remote_open", "远程开门"),
    TTLOCK_ADD_PASSWORD(10002, "add_password", "添加通行密码"),
    TTLOCK_UPDATE_PASS_MODE(10003, "update_pass_mode", "常开模式"),
    TTLOCK_UPDATE_PASSWORD(10004, "update_password", "修改通行密码"),
    TTLOCK_DELETE_PASSWORD(10005, "delete_password", "删除通行密码");

    /**
     * 功能调用
     */
    private final Integer code;
    private final String id;
    private final String desc;

    IotFunctionIdEnum(Integer code, String id, String desc) {
        this.code = code;
        this.id = id;
        this.desc = desc;
    }


    public static IotFunctionIdEnum getByCode(Integer code) {
        for (IotFunctionIdEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
