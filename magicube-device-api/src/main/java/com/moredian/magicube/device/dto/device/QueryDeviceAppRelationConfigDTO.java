package com.moredian.magicube.device.dto.device;


import com.moredian.magicube.device.enums.DeviceRelateAppFixedBIzTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryDeviceAppRelationConfigDTO implements Serializable{


    /**
     * 每页显示数目
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNo;

    /**
     * 空间类型
     */
    private Integer spaceType;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 关联的业务类型
     * @see DeviceRelateAppFixedBIzTypeEnum
     */
    private Integer bizType;

    /**
     * 关联的业务id
     */
    private String bizId;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 默认关联应用
     */
    private String defaultAppCode;

    /**
     * 可承载应用列表
     */
    private String availableAppCode;

}
