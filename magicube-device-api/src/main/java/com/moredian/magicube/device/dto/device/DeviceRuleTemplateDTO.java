package com.moredian.magicube.device.dto.device;

import com.moredian.magicube.common.enums.DeviceType;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <p>
 * 请添加描述信息
 * </p>
 *
 * <AUTHOR> chen
 * @since 2024/6/12 16:49
 **/
@Data
public class DeviceRuleTemplateDTO implements Serializable {

    private static final long serialVersionUID = -9051298689032467153L;

    /**
     * 模板Id
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String ruleName;

    /**
     * ${@link com.moredian.magicube.device.enums.ModeTypeEnum}
     */
    private Integer modeType;

    /**
     * 触发类型，1-定时触发，2-设备触发
     */
    private Integer triggerType;

    /**
     * 触发值
     */
    private String triggerValue;

    /**
     * 场景名称
     */
    private String modeName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 触发描述
     */
    private String triggerDescription;

    /**
     * ${@link DeviceType}
     */
    private List<Integer> deviceTypes;

    /**
     * 触发提醒
     */
    private String triggerTip;

    private String picUrl;

    private String picDetailUrl;

    private Boolean humanPriority;
}
