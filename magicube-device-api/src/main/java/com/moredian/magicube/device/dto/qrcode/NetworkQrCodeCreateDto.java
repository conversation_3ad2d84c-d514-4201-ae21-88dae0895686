package com.moredian.magicube.device.dto.qrcode;

import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import com.moredian.magicube.device.enums.PlatformEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * @Classname： ActivateQrCodeCreateDto
 * @Date: 2023/1/4 11:44 上午
 * @Author: _AF
 * @Description: 创建配网二维码对象
 */
@Data
public class  NetworkQrCodeCreateDto implements Serializable {

    private static final long serialVersionUID = 5860505390855706159L;

    private Long id;

    private Long orgId;

    private Long memberId;

    private String deviceSn;

    private String deviceAddressName;
    /**
     * 1-有线，默认
     * 2-无线
     * @see ActivateQrCodeConstant.NetworkTypeEnum
     */
    private Integer networkType = 1;
    /**
     * 1-DHCP
     * 2-静态，默认
     * @see  ActivateQrCodeConstant.ConnectTypeEnum
     */
    private Integer connectType = 2;
    /**
     * 私有云，默认关
     * @see ActivateQrCodeConstant.PrivateCloudSwitchEnum
     */
    private Integer privateCloudSwitch = 0;


    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;
    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;
    /**
     * 私有云配置
     */
    private PrivateCloud privateCloud;
    /**
     * 安装师傅
     * @see ActivateQrCodeConstant.RoleTypeEnum
     */
    private Integer roleType;

    /**
     * 来源
     *
     * @see ActivateQrCodeConstant.AppletEnum
     */
    private Integer source = 1;

    /**
     * 重新配网类型 1-在线配网 2-离线配网
     */
    private Integer resetNetworkType;

    /**
     * 服务器地址 目前仅用于保存私有化服务器服务器地址
     */
    private String serverAddress;

    /**
     * 激活平台
     *  @see PlatformEnum
     */
    private Integer platformCode;
}
