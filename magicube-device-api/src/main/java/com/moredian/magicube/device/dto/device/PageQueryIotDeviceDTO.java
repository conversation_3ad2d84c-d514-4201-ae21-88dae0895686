package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 分页查询Iot设备条件
 *
 * <AUTHOR>
 */

@Data
public class PageQueryIotDeviceDTO implements Serializable {

    private static final long serialVersionUID = -4031178852361411825L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * iot设备类型列表
     */
    private List<Integer> iotDeviceTypes;

    /**
     * 在线状态 0-离线 1-在线
     */
    private Integer onlineStatus;

    /**
     * 设备类型列表
     */
    private List<Integer> deviceTypes;

    /**
     * 空间树Id列表
     */
    private List<Long> treeIds;

    /**
     * 模糊搜索的设备Id列表(用或条件来查)
     */
    private List<Long> queryDeviceIds;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 设备Id列表
     */
    private List<Long> deviceIds;

    /**
     * 排序规则，0-按名称排序，1-按激活时间倒序，2-按激活时间正序
     */
    private Integer sort;

    /**
     * 每页显示数目
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNo;

    /**
     * 是否查询设备最新属性
     */
    private Integer searchProperties;

    /**
     * 是否过滤可拆分设备
     */
    private Boolean filterSplitDevice;

    /**
     * 是否过滤虚拟设备 0-过滤 1-不过滤
     */
    private Integer virtualFlag;

    /**
     * 是否查询所有设备
     */
    private Boolean allDeviceFlag;

    /**
     * 账号id
     */
    private Long memberId;

    /**
     * 权限tag
     */
    private List<String> roleTag;

    /**
     * 是否查询子树标识 true-需要查询子树 false-不需要查询子树
     */
    private Boolean subTreeFlag;
}