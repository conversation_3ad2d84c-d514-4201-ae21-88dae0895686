package com.moredian.magicube.device.dto.migration;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 获取组织列表页url
 * @author: wbf
 * @date: 2024/8/12 上午10:47
 */
@Data
public class MigrationDeviceBindOrgUrlRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 组织页url
     */
    private String bindOrgUrl;
}
