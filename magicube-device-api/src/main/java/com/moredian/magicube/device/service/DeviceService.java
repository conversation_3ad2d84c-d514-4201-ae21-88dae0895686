package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.*;

import java.util.List;

/**
 * 设备服务
 *
 * <AUTHOR>
 */
public interface DeviceService {

    /**
     * 根据设备sn获取设备信息
     *
     * @param deviceSn 设备sn
     * @return
     */
    ServiceResponse<DeviceInfoDTO> getByDeviceSn(String deviceSn);

    /**
     * 根据机构号和设备sn获取设备信息
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    ServiceResponse<DeviceInfoDTO> getByOrgAndDeviceSn(Long orgId, String deviceSn);

    /**
     * 根据设备sn列表获取设备信息列表
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByDeviceSns(List<String> deviceSns);

    /**
     * 根据设备sn列表获取设备基础信息列表
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    ServiceResponse<List<SimpleDeviceInfoDTO>> listBaseByDeviceSns(List<String> deviceSns);

    /**
     * 根据设备sn列表获取设备信息列表
     *
     * @param orgId     机构号
     * @param deviceSns 设备sn列表
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndDeviceSns(Long orgId,
        List<String> deviceSns);

    /**
     * 根据设备Id获取设备信息
     *
     * @param deviceId 设备Id
     * @return
     */
    ServiceResponse<DeviceInfoDTO> getById(Long deviceId);

    /**
     * 根据机构号和设备Id获取设备信息
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    ServiceResponse<DeviceInfoDTO> getByOrgIdAndId(Long orgId, Long deviceId);

    /**
     * 根据机构号和设备Id获取设备信息
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    ServiceResponse<SimpleDeviceInfoDTO> getSimpleByOrgIdAndId(Long orgId, Long deviceId);

    /**
     * 根据设备Id列表获取设备信息列表
     *
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByIds(List<Long> deviceIds);

    /**
     * 根据设备Id列表获取设备基础信息列表
     *
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<List<SimpleDeviceInfoDTO>> listBaseByIds(List<Long> deviceIds);

    /**
     * 根据机构号和设备Id列表获取设备信息列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndIds(Long orgId, List<Long> deviceIds);

    /**
     * 根据机构号和设备Id列表获取设备基础信息列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listSimpleByOrgIdAndIds(Long orgId, List<Long> deviceIds);

    /**
     * 查询该机构下所有设备信息
     *
     * @param orgId 机构号
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByOrgId(Long orgId);

    /**
     * 新增设备信息
     *
     * @param dto 设备信息
     * @return
     */
    ServiceResponse<Long> addDevice(AddDeviceDTO dto);

    /**
     * 修改设备信息
     *
     * @param dto 设备信息
     * @return
     */
    ServiceResponse<Boolean> update(UpdateDeviceDTO dto);

    /**
     * 批量修改设备
     *
     * @param list 设备信息列表
     * @return
     */
    ServiceResponse<Boolean> batchUpdate(List<UpdateDeviceDTO> list);

    /**
     * 删除设备
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    ServiceResponse<Boolean> deleteById(Long orgId, Long deviceId);

    /**
     * 分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<Pagination<DeviceInfoDTO>> listPage(QueryDeviceDTO dto);

    /**
     * 分页查询所有设备信息，包括iot设备
     *
     * @param dto 查询条件
     */
    ServiceResponse<Pagination<DeviceInfoDTO>> listAllDevicePage(QueryDeviceDTO dto);

    /**
     * 分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<Pagination<DeviceInfoDTO>> listPageV2(QueryDeviceDTO dto);

    /**
     * 多租户分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<Pagination<DeviceInfoDTO>> parkPage(QueryDeviceDTO dto);

    /**
     * 按设备名称查询设备详情
     *
     * @param orgId      机构号
     * @param deviceName 设备名称
     * @return
     */
    ServiceResponse<DeviceInfoDTO> getByOrgIdAndDeviceName(Long orgId, String deviceName);

    /**
     * 按设备名称查询模糊查询设备ID列表
     *
     * @param orgId    机构号
     * @param keywords 设备名称
     * @return
     */
    ServiceResponse<List<Long>> listDeviceIdByLikeName(Long orgId, String keywords);

    /**
     * 根据设备Id列表查询设备名称列表
     *
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<List<String>> listDeviceNameByIds(List<Long> deviceIds);

    /**
     * 根据设备类型查询设备Id列表
     *
     * @param orgId      机构号
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<List<Long>> listDeviceIdByOrgIdAndType(Long orgId, Integer deviceType);

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByType(Integer deviceType);

    /**
     * 根据机构号和设备类型查询设备信息列表
     *
     * @param orgId      机构号
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndType(Long orgId, Integer deviceType);

    /**
     * 根据设备类型列表查询设备信息列表
     *
     * @param orgId       机构号
     * @param deviceTypes 设备类型列表
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndTypes(Long orgId, List<Integer> deviceTypes);

    /**
     * 根据条件模糊查询设备信息列表
     *
     * @param queryDeviceDTO 查询条件
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByLikeCondition(QueryDeviceDTO queryDeviceDTO);

    /**
     * 处理设备容量超限
     *
     * @param orgId     机构Id
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<Boolean> doDeviceCapacityExceeded(Long orgId, List<Long> deviceIds);

    /**
     * 获取设备行业app列表
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    ServiceResponse<List<DeviceIndustryAppTypeDTO>> listDeviceIndustryAppTypeByDeviceSns(
        List<String> deviceSns);

    /**
     * 根据第三方设备id获取设备信息
     *
     * @param orgId         机构号
     * @param thirdDeviceId 第三方设备Id
     * @return
     */
    ServiceResponse<DeviceInfoDTO> getByOrgIdAndTpId(Long orgId, String thirdDeviceId);

    /**
     * 根据设备sn，设备名称查询设备列表
     *
     * @param dto
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> listByDeviceSnListAndDeviceName(QuerySpecificDeviceDTO dto);

    /**
     * 根据机构id和设备id查询设备信息
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    ServiceResponse<DeviceDTO> getByOrgIdAndDeviceId(Long orgId, Long deviceId);

    /**
     * 查询所有设备
     *
     * @return
     */
    ServiceResponse<List<DeviceDTO>> listAllDevices();

    /**
     * 查询所有设备
     *
     * @param onlineStatus
     * @return
     */
    ServiceResponse<List<DeviceDTO>> listAllDevicesWithOnlineStatus(boolean onlineStatus);

    /**
     * 根据机构id查询设备简单信息
     *
     * @param orgIds
     * @return
     */
    ServiceResponse<List<SimpleDeviceInfoDTO>> findAllSimpleDevices(List<Long> orgIds);

    /**
     * 根据设备类型及版本号进行设备过滤
     *
     * @param dto
     * @return
     */
    ServiceResponse<List<Long>> filterViewDeviceIds(ViewDeviceDTO dto);

    /**
     * 查询设备及版本信息列表
     *
     * @param dto
     * @return
     */
    ServiceResponse<List<DeviceWithVersionInfoDTO>> listDeviceWithVersionInfo(
        QueryDeviceWithVersionDTO dto);

    /**
     * 查询所有设备类型
     *
     * @param orgId 机构ID
     * @return {@link ServiceResponse}<{@link List}<{@link Integer}>>
     */
    ServiceResponse<List<Integer>> findAllDeviceTypesByOrgId(Long orgId);

    /**
     * 查询设备版本信息
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    ServiceResponse<VersionInfoDTO> getVersionInfoByDeviceId(Long orgId, Long deviceId);



    /**
     * 上报设备电量
     *
     * @param deviceElectricDto
     * @return
     */
    ServiceResponse<Boolean> reportDeviceElectric(ReportDeviceElectricDTO deviceElectricDto);

    /**
     * 批量查询设备电量
     *
     * @param deviceIdList
     * @return
     */
    ServiceResponse<List<DeviceElectricDTO>> findElectricByDeviceIdList(List<Long> deviceIdList);

    /**
     * 保存临时图片
     *
     * @param saveImageDto 保存图像 DTO
     * @return {@link ServiceResponse}<{@link ImagePathDTO}>
     */
    ServiceResponse<ImagePathDTO> saveImage(SaveImageDTO saveImageDto);

    /**
     * 判断水牌会议室内是否有人
     * @param deviceId 水牌设备
     * @param orgId 机构id
     * @return
     */
    ServiceResponse<DeviceHasPersonDTO> checkDeviceHasPerson(Long deviceId, Long orgId);


    /**
     * 获取设备激活后跳转链接
     * @param orgId 机构id
     * @param deviceSn deviceSn
     * @return 跳转应用配置
     */
    ServiceResponse<DeviceActiveJumpAppConfigDTO> getDeviceJumpLinkAfterActive(Long orgId, String deviceSn);

    /**
     * 查询当前机构下可进入设备管理后台的设备SN列表
     *
     * @param orgId 组织 ID
     * @return {@link ServiceResponse }<{@link List }<{@link String 设备SN }>>
     */
    ServiceResponse<List<String>> supportAuthDeviceManagementList(Long orgId);
}