package com.moredian.magicube.device.enums;

import lombok.Getter;

/**
 * @Description: 设备迁移状态
 * @author: wbf
 * @date: 2024/8/12 上午11:14
 */
@Getter
public enum MigrationStatusEnum {

    MIGRATION_WAITING(0,"迁移中"),
    MIGRATION_SUCCESS(1,"迁移成功"),
    MIGRATION_FAIL_DEVICE_ERROR(2,"迁移失败，设备端错误"),
    MIGRATION_FAIL_ORG_NOT_EQUAL(3,"迁移失败，用户选择组织与设备当前组织不一致"),
    ;

    private Integer code;

    private String name;

    MigrationStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
