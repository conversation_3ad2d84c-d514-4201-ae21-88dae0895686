package com.moredian.magicube.device.dto.activate;

import java.io.Serializable;

/**
 * Created by xxu on 2017/12/28.
 */
public class ThirdPartyDeviceInfoDTO implements Serializable {

    //deviceSn
    private String deviceSn;

    //第三方设备ID
    private String thirdDeviceId;

    //绑定类型
    private String bindType;

    // 设备类型
    private Integer equipmentType;

    private Long orgId;

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getThirdDeviceId() {
        return thirdDeviceId;
    }

    public void setThirdDeviceId(String thirdDeviceId) {
        this.thirdDeviceId = thirdDeviceId;
    }

    public String getBindType() {
        return bindType;
    }

    public void setBindType(String bindType) {
        this.bindType = bindType;
    }

    public Integer getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(Integer equipmentType) {
        this.equipmentType = equipmentType;
    }
}
