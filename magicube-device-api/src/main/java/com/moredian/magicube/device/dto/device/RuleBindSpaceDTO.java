package com.moredian.magicube.device.dto.device;

import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.magicube.device.error.DeviceErrorCode;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: RuleBindTreeReq.java, v 1.0 Exp $
 */
@Data
@Accessors(chain = true)
public class RuleBindSpaceDTO implements Serializable {

    @NotNull(message = "orgId不能为空")
    private Long orgId;

    private String ruleName;
    /**
     * 规则Id
     */
//    @NotNull(message = "ruleId不能为空")
    private Long ruleId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 应用场景id
     */
    private Integer spaceType;

    /**
     * 触发值
     */
    @NotNull(message = "triggerValue不能为空")
    private String triggerValue;

    /**
     * 空间Id列表
     */
    private List<Long> spaceIdList;

    @Size(min = 1, message = "设备类型选择不能为空")
    private List<Integer> deviceTypes;

    /**
     * 只改变TriggerValue
     */
    private Integer triggerValueOnlyChange;

    /**
     * 请求来源，1：前端请求，2：消息推送
     */
    private Integer requestSource;

    /**
     * 是否人为优先
     */
    private Boolean humanPriority;

    public void checkParamAvailable() {
        if (this.ruleId == null && this.templateId == null) {
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_RULE_ID_AND_TEMPLATE_ID_NOT_NULL, DeviceErrorCode.DEVICE_RULE_ID_AND_TEMPLATE_ID_NOT_NULL.getMessage()));
        }
    }
}
