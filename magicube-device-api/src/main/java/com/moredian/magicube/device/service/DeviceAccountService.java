package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;

import java.util.List;

/**
 * @Author: limh
 * @Date: 2020/4/10 18:49
 */
public interface DeviceAccountService {

    /**
     * 将sn 与 accountId 绑定起来  定制需要
     *
     * @param accountId 账号Id
     * @param snList    设备sn列表
     * @return
     */
    ServiceResponse<Boolean> addDeviceSnWithAccountId(Long accountId, List<String> snList);
}
