package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@Data
@NoArgsConstructor
public class DeviceDTO implements Serializable {

    private static final long serialVersionUID = 5770094307757243728L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 激活时间
     */
    private Long activeTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否在线
     */
    private Boolean online;

    /**
     * 最后更新时间
     */
    private Long lastTimeStamp;

    /**
     * 蓝牙mac
     */
    private String bluetoothMac;

    private String parentDeviceSn;

    /**
     * 全路径名称
     */
    private String pathTreeName;

    /**
     * 空间属性名称[出入口,闸机通道]
     */
    private String propertyTreeName;

    private Integer directionText;

    /**
     * 位置
     */
    private String position;
}
