package com.moredian.magicube.device.dto.subject;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ScreenSaverItem implements Serializable {


    private static final long serialVersionUID = 7592160907716617071L;

    /**
     * 标识ID，前端需要
     */
    private String sid;

    /**
     * 是否是视频（主题不生效，屏保生效，0-屏保图片，1-屏保视频），如果type = 1不生效
     */
    private Integer isVideo = 0;

    /**
     * 地址
     */
    private String url;

    /**
     * 第一帧图片
     */
    private String firstScreenImg;

    /**
     * 是否静音，如果非屏保视频不生效
     */
    private Integer enableVoice;

    /**
     * 是否是全天
     */
    private Integer isAllDay;

    /**
     * 时间段，如果type = 1不生效
     */
    private List<String> timeList;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private String fileSize;
}

