package com.moredian.magicube.device.enums;

/**
 * 设备激活状态
 * <AUTHOR>
 */
public enum DeviceActiveStateEnum {

    DEVICE_CONFIGURE_NETWORK(1, "配置网络"),
    DEVICE_WAIT_NETWORK(2, "待设备联网"),
    DEVICE_NETWORK_CONNECTING(3, "设备联网中"),
    DEVICE_NETWORK_CONNECTED(4, "设备联网成功"),
    DEVICE_BIND_ORG(5, "设备绑定团队"),
    DEVICE_BIND_ORG_SUCCESS(6, "设备绑定团队成功"),
    DEVICE_SCENE_TYPE_CHOOSE(7, "设备运行模式选择中"),
    DEVICE_ACTIVATING(8, "设备激活中"),
    DEVICE_ACTIVATE_SUCCESS(9, "设备激活成功"),
    RELEVANCE_MEETING_SUCCESS(10, "设备关联位置成功"),
    ;

    private final int code;

    private final String desc;

    DeviceActiveStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
