package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 请添加描述信息
 * </p>
 *
 * <AUTHOR> chen
 * @since 2024/6/12 16:49
 **/
@Data
@Accessors(chain = true)
public class RuleTemplateQueryDTO implements Serializable {
    private static final long serialVersionUID = -8668729683519937280L;

    @NotNull(message = "orgId不能为空")
    private Long orgId;

}
