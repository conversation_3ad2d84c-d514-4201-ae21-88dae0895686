package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/8
 */
@Data
@NoArgsConstructor
public class SimpleDeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = 4309871600455171924L;

    private Long orgId;

    private Long deviceId;

    private String deviceSn;

    private String deviceName;

    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 位置
     */
    private String position;

    /**
     * 是否为虚拟设备
     * 0-非虚拟设备，1-虚拟设备
     */
    private Integer virtualFlag;

    /**
     * 父设备sn
     */
    private String parentDeviceSn;

    /**
     * @see com.moredian.magicube.common.enums.SceneTypeEnums
     */
    private Integer sceneType;
}
