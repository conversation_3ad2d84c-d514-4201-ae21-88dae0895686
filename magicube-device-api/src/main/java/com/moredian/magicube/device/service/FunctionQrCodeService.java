package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeSimpleDto;
import com.moredian.magicube.device.dto.qrcode.AleadyActivateOrgDto;
import com.moredian.magicube.device.dto.qrcode.BindPersonnelRelationDto;
import com.moredian.magicube.device.dto.qrcode.DeviceChooseSpaceDTO;
import com.moredian.magicube.device.dto.qrcode.InsertNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.QrCodeActivateRecordDto;
import com.moredian.magicube.device.dto.qrcode.SimpleActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.UpdateNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.UploadNetworkCodeStatusDTO;
import java.util.List;

/**
 * @Classname： QrCodeService
 * @Date: 2023/1/5 3:38 下午
 * @Author: _AF
 * @Description:
 */
public interface FunctionQrCodeService {

    /**
     * 创建激活二维码
     *
     * @param dto
     * @return
     */
    ServiceResponse<Long> createActivateQrCode(ActivateQrCodeCreateDto dto);


    /**
     * 获取激活码id
     *
     * @param memberId
     * @param id
     * @return
     */
    ServiceResponse<ActivateQrCodeDto> getActivateQrCodeById(Long memberId, Long id);

    /**
     *  通过ID获取激活二维码
     *
     * @param orgId 组织 ID
     * @param id    编号
     * @return {@link ActivateQrCodeDto}
     */
    ServiceResponse<ActivateQrCodeDto> getActivateQrCodeByOrgIdAndId(Long orgId, Long id);

    /**
     * 平台账号
     *
     * @param memberId
     * @return
     */
    ServiceResponse<List<ActivateQrCodeSimpleDto>> listByMemberId(Long memberId, Integer roleType);

    /**
     * 根据memberId获取历史机构信息
     *
     * @param memberId
     * @param roleType
     * @return
     */
    ServiceResponse<List<ActivateQrCodeSimpleDto>> listHistoryOrgByMemberId(Long memberId, Integer roleType);


    /**
     * 二维码激活记录
     *
     * @param orgId
     * @param qrCodeId
     * @return
     */
    ServiceResponse<List<QrCodeActivateRecordDto>> listActivateRecord(Long orgId, Long qrCodeId);


    /**
     * 查询安装师傅已经使用过的安装机构
     *
     * @param accountId
     * @return
     */
    ServiceResponse<List<AleadyActivateOrgDto>> listByAleadyActivateOrg(Long accountId);



    /**
     * 获取身份绑定关系
     * @return
     */
    ServiceResponse<Integer> getPersonnelRelation(Long accountId);

    /**
     * 绑定关系
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> bindPersonnelRelation(BindPersonnelRelationDto dto);


    /**
     * 创建配网码
     *
     * @param
     * @return 二维码路径
     */
    ServiceResponse<Long> createNetworkQrCode(NetworkQrCodeCreateDto dto);

    /**
     * 新增机构配网信息
     *
     * @param   dto 网络信息
     * @return 二维码路径
     */
    ServiceResponse<Long> insertNetworkInfo(InsertNetworkInfoDTO dto);

    /**
     * 编辑机构配网信息
     *
     * @param   dto 网络信息
     * @return 二维码路径
     */
    ServiceResponse<Boolean> updateNetworkInfo(UpdateNetworkInfoDTO dto);

    /**
     * 根据网络Id删除机构配网信息
     *
     * @param orgId 机构Id
     * @param id    网络Id
     * @return 二维码路径
     */
    ServiceResponse<Boolean> deleteByNetworkId(Long orgId, Long id);

    /**
     * 查询配网码id
     *
     * @param orgId 机构Id
     * @param id    网络Id
     * @return
     */
    ServiceResponse<NetworkQrCodeDto> getNetworkQrCodeById(Long orgId, Long id);


    /**
     * 查询机构下最新的激活码的网络信息
     *
     * @param orgId
     * @return
     */
    ServiceResponse<NetworkQrCodeDto> getNewestNetWorkInfoByOrgId(Long orgId);

    /**
     * 获取配网码信息列表
     *
     * @param orgId
     * @return
     */
    ServiceResponse<List<NetworkQrCodeDto>> listNetworkInfoByOrgId(Long orgId);

    /**
     * 判断激活码是否已激活设备
     *
     * @param orgId
     * @param qrCodeId
     * @return
     */
    ServiceResponse<Boolean> judgeQrCodeActivatedDevice(Long orgId, Long qrCodeId);

    /**
     * 获取激活码简单信息
     *
     * @param qrCodeId
     * @return
     */
    ServiceResponse<SimpleActivateQrCodeDto> getSimpleQrCodeByQrCodeId(Long qrCodeId);

    /**
     * 配网码状态信息上报
     *
     * @param dto 配网码状态信息
     * @return
     */
    ServiceResponse<Boolean> uploadNetworkCodeStatus(UploadNetworkCodeStatusDTO dto);

    /**
     * 设备选择空间空间节点
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> deviceChooseSpace(DeviceChooseSpaceDTO dto);
}
