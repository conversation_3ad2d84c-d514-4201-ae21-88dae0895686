package com.moredian.magicube.device.dto.device;

import com.moredian.magicube.common.enums.DeviceType;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AddDeviceDTO implements Serializable {

    private static final long serialVersionUID = 2129998734096109009L;

    /**
     * 设备类型  {@link DeviceType}
     */
    private Integer deviceType;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 位置Id
     */
    private Long positionId;

    /**
     * 位置
     */
    private String position;
}
