package com.moredian.magicube.device.enums;

import java.util.Objects;
import lombok.Getter;

/**
 * @Classname： ActivateQrCodeConstant
 * @Date: 2023/1/4 2:09 下午
 * @Author: _AF
 * @Description:
 */
public interface ActivateQrCodeConstant {


    @Getter
    enum NetworkTypeEnum {
        WIRED(1, "有线网络"),
        WIRELESS(2, "无线网络");

        private Integer type;

        private String desc;

        NetworkTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static boolean checkType(Integer type) {
            if (type == null) {
                return false;
            }
            for (NetworkTypeEnum networkType : NetworkTypeEnum.values()) {
                if (Objects.equals(networkType.getType(), type)) {
                    return true;
                }
            }
            return false;
        }
    }


    @Getter
    enum ConnectTypeEnum {
        DHCP(1, "dhcp"),
        STATIC_CONFIG(2, "静态配置"),
        WIFI_CONFIG(3,"无线配置");

        private Integer type;

        private String desc;

        ConnectTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static boolean checkType(Integer type) {
            if (type == null) {
                return false;
            }
            for (ConnectTypeEnum connectType : ConnectTypeEnum.values()) {
                if (Objects.equals(connectType.getType(), type)) {
                    return true;
                }
            }
            return false;
        }
    }


    @Getter
    enum PrivateCloudSwitchEnum {
        OPEN(1, "打开"),
        CLOSE(0, "关闭");

        private Integer value;

        private String desc;

        PrivateCloudSwitchEnum(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static boolean checkValue(Integer value) {
            if (value == null) {
                return false;
            }
            for (PrivateCloudSwitchEnum privateCloud : PrivateCloudSwitchEnum.values()) {
                if (Objects.equals(privateCloud.getValue(), value)) {
                    return true;
                }
            }
            return false;
        }
    }

    @Getter
    enum FunctionTypeEnum {
        ACTIVATE(1, "激活码"),
        NETWORK(2, "配网码"),
        PRIVATIZATION(3, "私有化激活码"),
        DING_PRIVATIZATION(4, "钉私有化激活码");

        private Integer type;

        private String desc;

        FunctionTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static boolean checkType(Integer type) {
            if (type == null) {
                return false;
            }
            for (FunctionTypeEnum functionTypeEnum : FunctionTypeEnum.values()) {
                if (Objects.equals(functionTypeEnum.getType(), type)) {
                    return true;
                }
            }
            return false;
        }
    }

    @Getter
    enum RoleTypeEnum {
        ADMINISTRATOR(1, "管理员"),
        WORKER(2, "安装师傅");

        private Integer type;

        private String desc;

        RoleTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static boolean checkType(Integer type) {
            if (type == null) {
                return false;
            }
            for (RoleTypeEnum roleTypeEnum : RoleTypeEnum.values()) {
                if (Objects.equals(roleTypeEnum.getType(), type)) {
                    return true;
                }
            }
            return false;
        }
    }

    @Getter
    enum AppletEnum {
        ACTIVATE_ASSISTANT(1, "激活小助手"),
        MOLAN_OFFICE(2, "魔蓝办公小程序"),
        SMART_OFFICE(3, "慧办公小程序")
        ;

        private Integer type;

        private String desc;

        AppletEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static boolean checkType(Integer type) {
            if (type == null) {
                return false;
            }
            for (AppletEnum appletEnum : AppletEnum.values()) {
                if (Objects.equals(appletEnum.getType(), type)) {
                    return true;
                }
            }
            return false;
        }
    }

}
