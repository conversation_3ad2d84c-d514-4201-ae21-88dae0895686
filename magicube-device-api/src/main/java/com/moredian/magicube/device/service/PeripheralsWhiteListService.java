package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.peripherals.InsertPeripheralsWhiteListDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListDTO;

import java.util.List;

/**
 * 外设白名单服务
 *
 * <AUTHOR>
 */
public interface PeripheralsWhiteListService {

    /**
     * 批量添加外设白名单
     *
     * @param list 外设白名单参数
     * @return
     */
    ServiceResponse<Boolean> batchInsert(List<InsertPeripheralsWhiteListDTO> list);

    /**
     * 根据外设sn和外设类型查询外设白名单信息
     *
     * @param peripheralsSn   外设sn
     * @param peripheralsType 外设类型
     * @return
     */
    ServiceResponse<PeripheralsWhiteListDTO> getBySnAndType(String peripheralsSn, Integer peripheralsType);

    /**
     * 根据外设设备sn列表查询外设白名单列表
     *
     * @param peripheralsSnList
     * @return
     */
    ServiceResponse<List<PeripheralsWhiteListDTO>> getByPeripheralsSns(List<String> peripheralsSnList);

    /**
     * 根据外设白名单Id修改外设白名单状态
     *
     * @param peripheralsWhiteListId 外设白名单Id
     * @param status                 外设状态 0-断开 1-连接
     * @return
     */
    ServiceResponse<Integer> updateStatusById(Long peripheralsWhiteListId, Integer status);
}
