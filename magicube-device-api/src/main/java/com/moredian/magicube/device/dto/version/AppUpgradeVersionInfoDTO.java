package com.moredian.magicube.device.dto.version;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/19
 */
@Getter
@Setter
@ToString
public class AppUpgradeVersionInfoDTO implements Serializable {

    private static final long serialVersionUID = 1584054232720132884L;

    /**
     * app/rom类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 是否有比当前版本更高的包
     */
    private Boolean haveHigherApk = Boolean.FALSE;
}
