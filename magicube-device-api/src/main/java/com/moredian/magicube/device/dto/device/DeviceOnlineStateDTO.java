package com.moredian.magicube.device.dto.device;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DeviceOnlineStateDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主鍵id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 需要升级的设备sn
     */
    private String deviceSn;

    /**
     * 是否上线
     */
    private Boolean onlineFlag;

    /**
     * 消息发出时间戳 毫秒
     */
    private Long msgTimestamp;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;

}
