package com.moredian.magicube.device.enums;

public enum DeviceRelateAppFixedBIzTypeEnum {


    DEVICE_SN(1, "设备SN"),
    DEVICE_TYPE(2, "设备类型"),
            ;

    private final Integer code;

    private final String msg;

    DeviceRelateAppFixedBIzTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
