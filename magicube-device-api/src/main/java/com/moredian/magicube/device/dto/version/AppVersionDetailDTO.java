package com.moredian.magicube.device.dto.version;

import java.io.Serializable;
import lombok.Data;

/**
 * apk/rom版本信息详情
 * <AUTHOR>
 */
@Data
public class AppVersionDetailDTO implements Serializable {

    private static final long serialVersionUID = 7178950212784083323L;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 最新版版本
     */
    private Integer newVersion;

    /**
     * 之前的版本
     */
    private Integer preVersion;

    /**
     * 安装文件url
     */
    private String url;

    /**
     * 是否增量包
     */
    private Boolean incre;

    /**
     * 扩展信息json，兼容门锁各个app的升级包信息
     */
    private String extendInfo;
}