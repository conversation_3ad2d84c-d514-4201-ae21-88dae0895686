package com.moredian.magicube.device.dto.migration;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 查询组织待迁移设备数量
 * @author: wbf
 * @date: 2024/8/12 上午11:32
 */
@Data
public class MigrationDeviceOrgCountRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 组织id
     */
    private Long orgId;
    /**
     * 待迁移设备数
     */
    private Integer deviceCount;
}
