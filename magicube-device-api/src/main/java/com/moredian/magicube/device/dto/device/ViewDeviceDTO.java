package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/19
 */
@Data
@NoArgsConstructor
public class ViewDeviceDTO implements Serializable {

    private static final long serialVersionUID = -2684522646804686494L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 等待过滤的设备ID
     */
    private List<Long> deviceIds;

    /**
     * 过滤条件
     * <li>key-设备类型</li>
     * <li>value-app版本号</li>
     */
    private Map<Integer, Integer> deviceTypeVersionMap;
}
