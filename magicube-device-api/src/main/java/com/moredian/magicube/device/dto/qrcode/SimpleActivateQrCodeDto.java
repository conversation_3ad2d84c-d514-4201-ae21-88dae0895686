package com.moredian.magicube.device.dto.qrcode;

import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/27
 */
@Getter
@Setter
@ToString
public class SimpleActivateQrCodeDto implements Serializable {

    private static final long serialVersionUID = 2324545926544359796L;

    private Long id;

    private Long orgId;
    /**
     * 二维码名称
     */
    private String qrCodeName;

    private String deviceAddressName;

    private Long memberId;

    /**
     * @see ActivateQrCodeConstant.RoleTypeEnum
     */
    private Integer roleType;

    private Date gmtCreate;

    private Integer Source;
}
