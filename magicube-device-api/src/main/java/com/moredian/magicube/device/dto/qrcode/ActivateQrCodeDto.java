package com.moredian.magicube.device.dto.qrcode;

import java.io.Serializable;
import lombok.Data;

/**
 * @Classname： ActivateQrCodeCreateDto
 * @Date: 2023/1/4 11:44 上午
 * @Author: _AF
 * @Description: 创建激活二维码对象
 */
@Data
public class ActivateQrCodeDto implements Serializable {

    private static final long serialVersionUID = 6643672732609559518L;

    private Long id;

    private Long orgId;
    /**
     * 二维码名称
     */
    private String qrCodeName;
    /**
     * 平台账号
     */
    private Long accountId;

    private String deviceAddressName;
    /**
     * 1-有线，默认
     * 2-无线
     */
    private Integer networkType = 1;
    /**
     * 1-DHCP
     * 2-静态，默认
     */
    private Integer connectType = 2;
    /**
     * 私有云，默认关
     */
    private Integer privateCloudSwitch = 0;


    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;
    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;
    /**
     * 私有云配置
     */
    private PrivateCloud privateCloud;
    /**
     * 二维码地址
     */
    private String url;

    /**
     * 类别
     */
    private Integer functionType;

    /**
     * 空间id
     */
    private Long treeId;

    /**
     * 名称
     */
    private String treeName;

    /**
     * 出入方向名称
     */
    private String directionName;

    /**
     * 全路径名称
     */
    private String pathTreeName;

    /**
     * 机构网络信息Id
     */
    private Long orgNetworkId;

    /**
     * 出入方向 1入口 2出口
     */
    private Integer direction;

    private String propertyTreeName;
}
