package com.moredian.magicube.device.dto.upgrade;

import java.io.Serializable;

public class MergeUpgradeDeviceRequest implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private Long orgId;
	
	private String serialNumber;
	
	private Integer systemType;
	
	private Integer appType;

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public Integer getAppType() {
		return appType;
	}

	public void setAppType(Integer appType) {
		this.appType = appType;
	}

	public Integer getSystemType() {
		return systemType;
	}

	public void setSystemType(Integer systemType) {
		this.systemType = systemType;
	}

}
