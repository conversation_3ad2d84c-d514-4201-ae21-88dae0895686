package com.moredian.magicube.device.dto.group;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/24
 */
@Getter
@Setter
@ToString
public class SimpleDeviceGroupDTO implements Serializable {

    private static final long serialVersionUID = 4536027281018641187L;

    /**
     * 组id
     */
    private Long groupId;

    /**
     * 设备列表
     */
    private List<SimpleDevice> deviceList;

    @Getter
    @Setter
    public static class SimpleDevice implements Serializable {

        private static final long serialVersionUID = -7695863422830747905L;

        /**
         * 设备id
         */
        private Long deviceId;

        /**
         * 设备SN
         */
        private String deviceSn;
    }
}
