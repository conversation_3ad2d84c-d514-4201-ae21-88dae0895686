package com.moredian.magicube.device.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description：环境枚举类
 * @date ：2024/12/03 16:25
 */
@Getter
public enum EnvEnum {

    HY(1, "行业"),
    DD(2, "塔上"),
    MD(3, "塔下");

    private final Integer code;

    private final String desc;

    private EnvEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (EnvEnum envEnum : EnvEnum.values()) {
            if (envEnum.getCode().equals(code)) {
                return envEnum.getDesc();
            }
        }
        return null;
    }
}
