package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.subject.DeviceSubjectSimpleDTO;

import java.util.List;

/**
 * 主题和设备关联关系
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface DeviceSubjectRelationService {

    /**
     * 重置一个主题和多台设备的关系
     *
     * @param orgId     机构号
     * @param subjectId 设备主题Id
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<Boolean> resetRelationBySubjectId(Long orgId, Long subjectId,
        List<Long> deviceIds);

    /**
     * 验证设备是否已应用其他壁纸
     *
     * @param orgId     机构Id
     * @param deviceIds 设备Id列表
     * @param subjectId 设备主题Id
     * @return
     */
    ServiceResponse<List<DeviceInfoDTO>> checkDevice(Long orgId, List<Long> deviceIds,
        Long subjectId,Integer type);

    /**
     * 根据设备Id查询主题信息
     * @param orgId     机构Id
     * @param deviceId  设备Id
     * @param type      主题类型
     * @return
     */
    ServiceResponse<List<DeviceSubjectSimpleDTO>> getDeviceSubjectByOrgIdAndDeviceId(Long orgId,Long deviceId,Integer type);
}
