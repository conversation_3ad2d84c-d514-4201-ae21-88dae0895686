package com.moredian.magicube.device.dto.peripherals;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 查询外设信息
 *
 * <AUTHOR>
 */
@Data
public class QueryPeripheralsDTO implements Serializable {

    private static final long serialVersionUID = -6032143444931183667L;

    /**
     * 外设Id
     */
    private Long peripheralsId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 外设名称
     */
    private String peripheralsName;

    /**
     * 外设sn
     */
    private String peripheralsSn;

    /**
     * 关联设备sn
     */
    private String deviceSn;

    /**
     * 关联设备名称
     */
    private String deviceName;

    /**
     * 设备状态  -1- 解绑  0-断开，1-绑定
     */
    private Integer status;

    /**
     * 启动状态  0-不启用；1-启用
     */
    private Integer available;

    /**
     * 关联的设备类型
     */
    private Integer deviceType;

    /**
     * 外设的设备类型
     */
    private Integer peripheralsType;

    /**
     * 手机imei
     */
    private String peripheralsConnectId;

    /**
     * 系统类型 1-ios ，2-android
     */
    private Integer mobileSysType;

    /**
     * 设备sn列表
     */
    private List<String> deviceSns;
}
