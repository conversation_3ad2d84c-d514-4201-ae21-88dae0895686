package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version $Id: SpaceRuleUpdateDTO.java, v 1.0 Exp $
 */

@Data
public class SpaceRuleUpdateDTO implements Serializable {
    private static final long serialVersionUID = -5466041421998449967L;

    @NotNull(message = "orgId不能为空")
    private Long orgId;

    @NotNull(message = "spaceId不能为空")
    private Long spaceId;

    /**
     * 规则Id
     */
    @NotNull(message = "ruleId不能为空")
    private Long ruleId;

    /**
     * ${@link com.moredian.magicube.device.enums.RuleStateEnum}
     */
    @NotNull(message = "state不能为空")
    private Integer state;
}
