package com.moredian.magicube.device.dto.composite;

import com.moredian.magicube.common.enums.SceneTypeEnums;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/21 10:42
 */
@Data
public class DeviceCompositeGroupRelationAddReq implements Serializable {


    private static final long serialVersionUID = -1463063777468772703L;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 通行权限组ID
     */
    private Long groupId;
    /**
     * 设备ID
     */
    private List<Long> deviceIdList;
    /**
     * 设备组
     */
    private List<Long> deviceCompositeIdList;

    /**
     * 运行模式
     * @see SceneTypeEnums
     */
    private Integer sceneType;
}
