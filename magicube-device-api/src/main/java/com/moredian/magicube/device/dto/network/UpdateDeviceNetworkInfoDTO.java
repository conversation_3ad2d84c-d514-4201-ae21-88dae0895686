package com.moredian.magicube.device.dto.network;

import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import java.io.Serializable;
import lombok.Data;

/**
 * 编辑设备网络信息
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
public class UpdateDeviceNetworkInfoDTO implements Serializable {

    private static final long serialVersionUID = -7761061698855272524L;

    /**
     * 设备网络信息Id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 网络类型 1-有线 2-无线
     */
    private Integer networkType;

    /**
     * 连接类型 1-DHCP 2-静态地址
     */
    private Integer connectType;

    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;

    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;

    /**
     * 有线mac地址
     */
    private String wiredMac;

    /**
     * 无线mac地址
     */
    private String wifiMac;
}
