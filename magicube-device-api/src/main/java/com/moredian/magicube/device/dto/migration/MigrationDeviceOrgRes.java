package com.moredian.magicube.device.dto.migration;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 判断用户选择组织id是否与当前组织id一致
 * @author: wbf
 * @date: 2024/8/12 上午11:05
 */
@Data
public class MigrationDeviceOrgRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 是否相等
     */
    private Boolean isEqual;
    /**
     * 钉钉组织id
     */
    private String corpId;
    /**
     * 钉钉组织id对应的orgId
     */
    private Long corpOrgId;
    /**
     * 设备所在组织id
     */
    private Long orgId;
}
