package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.DeviceCountDTO;
import com.moredian.magicube.device.dto.device.IotDeviceDTO;
import com.moredian.magicube.device.dto.device.IotDevicePropertyDTO;
import com.moredian.magicube.device.dto.device.IotDevicePropertyDTO.Properties;
import com.moredian.magicube.device.dto.device.IotDeviceTypeCountDTO;
import com.moredian.magicube.device.dto.device.PageQueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.UpdateIotDevicePropertyDTO;
import com.moredian.magicube.device.dto.device.iot.DeviceFunctionInvokeDTO;
import com.moredian.magicube.device.dto.device.iot.IotDeviceListDTO;
import com.moredian.magicube.device.dto.device.iot.PageQueryIotDeviceListDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDTO;
import java.util.List;

/**
 * Iot设备服务
 *
 * <AUTHOR>
 */
public interface IotDeviceService {

    /**
     * 查询Iot设备类型信息列表
     *
     * @return
     */
    ServiceResponse<List<DeviceTypeDTO>> listIotDeviceType();

    /**
     * 不分页查询iot设备列表
     *
     * @param dto 查询Iot设备条件
     * @return
     */
    ServiceResponse<List<IotDeviceDTO>> listIotDevice(QueryIotDeviceDTO dto);

    /**
     * 根据条件分页查询Iot设备信息列表
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<Pagination<IotDeviceDTO>> listByConditionPage(PageQueryIotDeviceDTO dto);

    /**
     * 根据条件分页查询设备
     */
    ServiceResponse<Pagination<IotDeviceListDTO>> listByConditionPage(PageQueryIotDeviceListDTO dto);
    /**
     * 查询Iot设备详情
     *
     * @param orgId    机构Id
     * @param deviceId 设备Id
     * @return
     */
    ServiceResponse<IotDeviceDTO> getById(Long orgId, Long deviceId);

    /**
     * IOT设备属性修改
     *
     * @param dto 设备属性信息
     * @return
     */
    ServiceResponse<Boolean> updateDeviceProperties(UpdateIotDevicePropertyDTO dto);

    /**
     * 根据设备Id列表查询不同Iot设备类型数量
     *
     * @param orgId     机构Id
     * @param deviceIds 设备Id列表
     * @return
     */
    ServiceResponse<List<IotDeviceTypeCountDTO>> listDeviceTypeCount(Long orgId,
        List<Long> deviceIds);

    /**
     * 校验是否有可用Iot物联网设备
     *
     * @param orgId     机构Id
     * @param deviceIds 设备Id列表
     * @param modeType  模式类型，1-节能模式，2-用能模式
     * @return
     */
    ServiceResponse<Boolean> checkUsableIotDevice(Long orgId, List<Long> deviceIds,
        Integer modeType);

    /**
     * 获取设备的最新属性值
     */
    ServiceResponse<List<Properties>> latestDeviceProperty(Long orgId, String deviceSn);

    /**
     * 该设备所在空间下的iot设备信息列表
     */
    ServiceResponse<List<IotDeviceDTO>> listIotDeviceByIotDeviceId(Long orgId, Long iotDeviceId);

    /**
     * iot设备功能调用
     */
    ServiceResponse<Boolean> deviceFunctionInvoke(DeviceFunctionInvokeDTO dto);
}