package com.moredian.magicube.device.dto.composite;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @Auther: _AF
 * @Date: 2020/11/25 17:26
 * @Description:人员可选设备的权限组
 */
@Data
public class DeviceCompositeTreeDTO {

    /**
     * 设备组ID
     */
    private Long deviceCompositeId;
    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 类型，使用，0-通用
     */
    private Integer bizType;
    /**
     * 组名
     */
    private String deviceCompositeName;

    private Date gmtCreate;

    private Date gmtModify;

    /**
     * 父组 ID
     */
    private Long parentId;

    /**
     * 组 code
     */
    private String code;

    /**
     * 组code路径 包括自身code
     * /00000000/xx
     * 从虚拟根开始
     */
    private String path;


    /**
     *  子节点
     */
    private List<DeviceCompositeTreeDTO> children = new ArrayList<>();


    /**
     * 添加子节点的方法
     * @param child
     */
    public void addChild(DeviceCompositeTreeDTO child) {
        this.children.add(child);
    }
}
