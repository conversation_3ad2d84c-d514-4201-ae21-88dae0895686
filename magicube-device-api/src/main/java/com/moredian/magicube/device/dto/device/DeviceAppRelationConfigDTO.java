package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备关联应用配置表 DTO
 */
@Setter
@Getter
public class DeviceAppRelationConfigDTO implements Serializable {

private static final long serialVersionUID = -0L;

	/** 主键id */
	private Long id;
	/** 空间类型 */
	private Integer spaceType;
	/** app类型 */
	private Integer appType;
	/** 版本号 */
	private Integer versionCode;
	/** 默认应用列表 */
	private String defaultAppCode;
	/** 可承载应用列表 */
	private String availableAppCodeList;
	/** 创建时间 */
	private Date gmtCreate;
	/** 修改时间 */
	private Date gmtModify;


    @Override
    public String toString() {
        return "DeviceAppRelationConfig{" +
				"id=" + id +
						",spaceType='" + spaceType + "'" + 
						",appType='" + appType + "'" + 
						",versionCode='" + versionCode + "'" + 
						",defaultAppCode='" + defaultAppCode + "'" + 
						",availableAppCodeList='" + availableAppCodeList + "'" + 
						",gmtCreate='" + gmtCreate + "'" + 
						",gmtModify='" + gmtModify + "'" + 
		                '}';
    }
	
}