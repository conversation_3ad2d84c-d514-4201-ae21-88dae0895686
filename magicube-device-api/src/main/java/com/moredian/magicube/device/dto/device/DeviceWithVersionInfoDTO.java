package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/10/11
 */
@Getter
@Setter
@ToString
public class DeviceWithVersionInfoDTO implements Serializable {

    private static final long serialVersionUID = -6190287653930961397L;

    private Long orgId;

    private Long deviceId;

    private String deviceSn;

    private String deviceName;

    private Integer deviceType;

    private Boolean online;

    private Integer appType;

    private Integer appVersionCode;

    private Integer romType;

    private Integer romVersionCode;

}
