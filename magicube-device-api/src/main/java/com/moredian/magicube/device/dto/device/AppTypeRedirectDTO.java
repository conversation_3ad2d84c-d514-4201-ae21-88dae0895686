package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/19
 */
@Getter
@Setter
@ToString
public class AppTypeRedirectDTO implements Serializable {

    private static final long serialVersionUID = 5279193828084847955L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 跳转链接
     */
    private String redirectUrl;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
