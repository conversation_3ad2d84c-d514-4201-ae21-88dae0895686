package com.moredian.magicube.device.enums.iot;

import com.moredian.bee.common.exception.BizAssert;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-03 16:51
 */
@Getter
public enum MessageStatusEnum {
    AWAIT_STATUS(0, "待执行"),
    SUCCESS_STATUS(1, "成功"),
    FAIL_STATUS(2, "失败"),
    TIMEOUT_STATUS(3, "超时"),
    ;

    /**
     * 功能调用
     */
    private final Integer type;
    private final String desc;

    MessageStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MessageStatusEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (MessageStatusEnum messageStatusEnum : MessageStatusEnum.values()) {
            if (messageStatusEnum.getType().equals(type)) {
                return messageStatusEnum;
            }
        }
        return null;
    }

    public static void validateStatus(Integer type) {
        BizAssert.isTrue(getByType(type) != null, "messageStatus is not legal");
    }

}
