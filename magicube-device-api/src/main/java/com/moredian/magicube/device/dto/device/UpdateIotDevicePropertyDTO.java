package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * iot设备属性修改
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateIotDevicePropertyDTO implements Serializable {

    private static final long serialVersionUID = 7116290589405971715L;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备属性map
     * eg: {"power":"1","k1_power":"0"}
     */
    private Map<String,Object> deviceProperties;
}
