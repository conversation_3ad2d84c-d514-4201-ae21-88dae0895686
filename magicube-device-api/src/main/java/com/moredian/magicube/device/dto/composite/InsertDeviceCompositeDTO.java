package com.moredian.magicube.device.dto.composite;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 新增设备分组
 *
 * <AUTHOR>
 */

@Data
public class InsertDeviceCompositeDTO implements Serializable {

    private static final long serialVersionUID = 600234723004812614L;

    /**
     * 分组Id
     */
    private Long deviceCompositeId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 分组类型，使用，0-通用
     */
    private Integer bizType;

    /**
     * 分组名
     */
    private String deviceCompositeName;

    /**
     * 如果添加设备的组还不存在的时候，要创建组，可以在这里指定要创建的组在那个组目录下，不指定就默认在虚拟根下
     * 父设备组id
     */
    private Long parentId;

    /**
     * 设备id
     */
    private List<Long> deviceIdList;
}
