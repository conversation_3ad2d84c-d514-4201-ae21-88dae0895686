package com.moredian.magicube.device.dto.migration;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 待迁移设备列表返回结果
 * @author: wbf
 * @date: 2024/8/12 上午10:20
 */
@Data
public class MigrationDeviceRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 组织id
     */
    private Long orgId;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 待迁移设备数
     */
    private Integer deviceCount;
    /**
     * 设备列表
     */
    private List<MigrationDeviceList> deviceList;
}
