package com.moredian.magicube.device.dto.peripherals;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class PeripheralsWhiteListNewInfoDTO implements Serializable {

    private static final long serialVersionUID = -2212716518231713873L;

    private Long id;

    /**
     * 外设sn
     */
    private String peripheralsSn;
    /**
     * 关联设备类型，全部类型为0
     */
    private Integer deviceType;
    /**
     * 关联appType类型，全部为0
     */
    private Integer appType;
    /**
     * 外设类型
     */
    private String peripheralsType;

    private Date gmtCreate;

    private Date gmtModify;
}
