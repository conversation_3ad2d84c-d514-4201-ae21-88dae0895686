package com.moredian.magicube.device.constant;

/**
 * <AUTHOR>
 * @since 2023-05-16
 */
public class DeviceTypeConstants {

    //------------------------------------------- List -------------------------------------------
    /**
     * 安卓主板设备
     */
    public static final String BOARD_LIST = "BOARD_LIST";

    /**
     * 安卓主板识别设备
     */
    public static final String VERIFY_BOARD_LIST = "VERIFY_BOARD_LIST";

    /**
     * 需要绑定群组的设备
     */
    public static final String NEED_GROUP_LIST = "NEED_GROUP_LIST";

    /**
     * 需要绑定默认群组的设备
     */
    public static final String NEED_DEFAULT_GROUP_LIST = "NEED_DEFAULT_GROUP_LIST";

    /**
     * 需要同步云眼的设备
     */
    public static final String CLOUDEYE_NEED_LIST = "CLOUDEYE_NEED_LIST";

    /**
     * 需要布控的设备
     */
    public static final String NEED_DEPLOY_LIST = "NEED_DEPLOY_LIST";

    /**
     * 需要标准位置的设备
     */
    public static final String NEED_POSITION_LIST = "NEED_POSITION_LIST";

    /**
     * 门禁设备
     */
    public static final String DOOR_DEVICE_LIST = "DOOR_DEVICE_LIST";

    /**
     * 需要绑定主题
     */
    public static final String SUBJECT_NEED_LIST = "SUBJECT_NEED_LIST";

    /**
     * 需要控制板Extrainfo
     */
    public static final String EXTRA_INFO_NEED_LIST = "EXTRA_INFO_NEED_LIST";

    /**
     * 指纹设备类型
     */
    public static final String FINGERPRINT_DEVICE_TYPE_LIST = "FINGERPRINT_DEVICE_TYPE_LIST";

    /**
     * 人脸设备类型
     */
    public static final String FACE_DEVICE_TYPE_LIST = "FACE_DEVICE_TYPE_LIST";

    /**
     * 需要转换类型
     */
    public static final String NEED_CONVERT_LIST = "NEED_CONVERT_LIST";

    /**
     * 需要转换设备型号
     */
    public static final String NEED_DEFAULT_SPU_LIST = "NEED_DEFAULT_SPU_LIST";

    /**
     * M3系列设备
     */
    public static final String M3_SERIES_LIST = "M3_SERIES_LIST";

    /**
     * 访客设备列表
     */
    public static final String VISITOR_DEVICE_TYPE_LIST = "VISITOR_DEVICE_TYPE_LIST";

    /**
     * 支持三方应用设备列表
     */
    public static final String APP_DEVICE_TYPE_LIST = "APP_DEVICE_TYPE_LIST";

    /**
     * 海思产品设备
     */
    public static final String HISILICON_LIST = "HISILICON_LIST";

    /**
     * 支持第三方鉴权设备列表
     */
    public static final String THIRD_PARTY_AUTH_LIST = "THIRD_PARTY_AUTH_LIST";

    /**
     * 支持考勤的设备
     */
    public static final String ROOSTER_DEVICE_LIST = "ROOSTER_DEVICE_LIST";

    /**
     * 防疫设备列表
     */
    public static final String EPIDEMIC_DEVICE_TYPE_LIST = "EPIDEMIC_DEVICE_TYPE_LIST";

    /**
     * 可刷卡的设备列表
     */
    public static final String ENABLE_CARD_DEVICE_TYPE_LIST = "ENABLE_CARD_DEVICE_TYPE_LIST";

    /**
     * 访客机设备列表
     */
    public static final String SINGLE_VISITOR_DEVICE_TYPE_LIST = "SINGLE_VISITOR_DEVICE_TYPE_LIST";

    /**
     * 校园设备列表
     */
    public static final String CAMPUS_DEVICE_TYPE_LIST = "CAMPUS_DEVICE_TYPE_LIST";

    /**
     * 无需刷新配置的设备列表
     */
    public static final String NOT_REFRESH_CONFIG_DEVICE_LIST = "NOT_REFRESH_CONFIG_DEVICE_LIST";

    /**
     * 服务器设备列表
     */
    public static final String SERVER_DEVICE_LIST = "SERVER_DEVICE_LIST";

    /**
     * 摄像机设备列表
     */
    public static final String CAMERA_DEVICE_LIST = "CAMERA_DEVICE_LIST";

    /**
     * 开门设备列表
     */
    public static final String OPEN_DOOR_DEVICE_LIST = "OPEN_DOOR_DEVICE_LIST";

    /**
     * 梯控设备
     */
    public static final String ELEVATOR_DEVICE_TYPE_LIST = "ELEVATOR_DEVICE_TYPE_LIST";

    /**
     * 门禁盒子设备列表
     */
    public static final String BOX_DEVICE_LIST = "BOX_DEVICE_LIST";

    /**
     * 特殊命名设备
     */
    public static final String SPECIAL_DEVICE_NAME_LIST = "SPECIAL_DEVICE_NAME_LIST";

    /**
     * 无需绑组设备
     */
    public static final String NOT_BIND_DEFAULT_GROUP_DEVICE_LIST = "NOT_NEED_BIND_GROUP_DEVICE_LIST";

    /**
     * 外设关联设备
     */
    public static final String PERIPHERAL_ASSOCIATED_DEVICE_LIST = "PERIPHERAL_ASSOCIATED_DEVICE_LIST";

    /**
     * 需要通知激活人员的设备
     */
    public static final String NEED_NOTICE_ACTIVATE_USER_DEVICE_LIST = "NEED_NOTICE_ACTIVATE_USER_DEVICE_LIST";

    /**
     * 需要MD5值的设备
     */
    public static final String NEED_MD5_DEVICE_LIST = "NEED_MD5_DEVICE_LIST";

    /**
     * 支持设备主题的设备类型
     */
    public static final String SUBJECT_DEVICE_LIST = "SUBJECT_DEVICE_LIST";


    /**
     * 第三方设备列表
     *
     */
    public static final String DEVICE_THIRD_LIST = "DEVICE_THIRD_LIST";

    //------------------------------------------- Map -------------------------------------------
    /**
     * 需要对应关系
     */
    public static String CONVERT_NAME_MAP = "CONVERT_NAME_MAP";

    /**
     * 需要转换设备型号Map
     */
    public static String DEFAULT_SPU_MAP = "DEFAULT_SPU_MAP";

    /**
     * 不同设备类型对应的超限容量
     */
    public static String DEVICE_CAPACITY_MAP = "DEVICE_CAPACITY_MAP";

    /**
     * 设备Sn对应设备类型
     */
    public static String DEVICE_SN_TO_TYPE_MAP = "DEVICE_SN_TO_TYPE_MAP";


    /**
     * 会议水牌设备
     */
    public static String DEVICE_WATER_CARD = "DEVICE_WATER_CARD";

    /**
     * 开放-设备第三方鉴权
     *
     */
    public static String DEVICE_THRID_AUTH = "DEVICE_THRID_AUTH";

    /**
     * iot设备类型
     */
    public static String IOT_DEVICE_LIST = "IOT_DEVICE_LIST";

    /**
     * 需要拆分的设备类型
     */
    public static String NEED_SPLIT_IOT_DEVICE_LIST = "NEED_SPLIT_IOT_DEVICE_LIST";

    /**
     * 门锁设备类型列表
     */
    public static String DOOR_LOCK_DEVICE_LIST = "DOOR_LOCK_DEVICE_LIST";

    /**
     * iot节能设备类型
     */
    public static final String IOT_ENERGY_SAVE_DEVICE_TYPE_LIST = "IOT_ENERGY_SAVE_DEVICE_TYPE_LIST";

    /**
     * iot节能触发设备类型
     */
    public static final String IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST = "IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST";

    /**
     * 虚拟IOT设备类型列表
     */
    public static final String IOT_VIRTUAL_DEVICE_TYPE_LIST = "IOT_VIRTUAL_DEVICE_TYPE_LIST";


    /**
     * 支持 权限配置 进入 设备管理后台的设备列表
     */
    public static final String SUPPORT_AUTH_DEVICE_MANAGEMENT_LIST = "SUPPORT_AUTH_DEVICE_MANAGEMENT_LIST";

    /**
     * 可迁移设备类型列表
     */
    public static final String MIGRATION_DEVICE_TYPE = "MIGRATION_DEVICE_TYPE";

    /**
     * 支持酒店门显的设备
     */
    public static final String HOTEL_DOOR_DEVICE = "HOTEL_DOOR_DEVICE";

    /**
     * 支持生态应用的设备
     */
    public static final String ECOLOGY_DEVICE_LIST = "ECOLOGY_DEVICE_LIST";

    /**
     * 非钉SDK激活的设备集合
     */
    public static final String NO_DING_SDK_ACTIVATE_LIST = "NO_DING_SDK_ACTIVATE_LIST";

    /**
     * IOT设备-能耗管理列表展示的设备类型
     */
    public static final String IOT_DEVICE_TYPE_ENERGY_LIST = "IOT_DEVICE_TYPE_ENERGY_LIST";

    /**
     * 需要初始化会议室空间的设备类型（有版本Version版本控制）
     */
    public static final String MEETING_DEVICE_INITIAL_MEETING_ROOM = "MEETING_DEVICE_INITIAL_MEETING_ROOM";


    /**
     * 查询设备状态是从魔链iot平台查
     */
    public static final String DEVICE_ONLINE_FROM_MORE_LINKS = "DEVICE_ONLINE_FROM_MORE_LINKS";
}
