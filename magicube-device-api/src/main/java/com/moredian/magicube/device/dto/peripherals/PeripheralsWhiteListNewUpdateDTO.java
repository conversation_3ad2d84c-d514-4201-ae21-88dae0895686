package com.moredian.magicube.device.dto.peripherals;

import java.io.Serializable;
import lombok.Data;

/**
 * 新配件白名单修改对象
 */
@Data
public class PeripheralsWhiteListNewUpdateDTO implements Serializable {

    private static final long serialVersionUID = 250841300964787426L;

    /**
     * 不能为空
     */
    private Long id;

    /**
     * 外设sn，不能为空
     */
    private String peripheralsSn;
    /**
     * 关联设备类型，全部类型为0，不能为空
     */
    private Integer deviceType;
    /**
     * 关联appType类型，全部为0，不能为空
     */
    private Integer appType;
    /**
     * 外设类型
     *
     * @see com.moredian.fishnet.device.enums.PeripheralsType
     */
    private Integer peripheralsType;

}
