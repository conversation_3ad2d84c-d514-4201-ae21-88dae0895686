package com.moredian.magicube.device.dto.device;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by xxu on 2017/3/27.
 */

@Data
public class TransferMessageDTO<T> implements Serializable {

    private String seqId;

    //Event Type
    private Integer eventType;

    //Event discription.描述事件的信息
    private String message;

    //数据，根据需要自己组装内容
    private T data;

    //事件紧急程度
    private Integer Severity;

    //事件版本号，用于重复更新的case
    private Integer version;

}