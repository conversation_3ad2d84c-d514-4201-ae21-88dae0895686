package com.moredian.magicube.device.dto.type;

import lombok.Data;

import java.io.Serializable;

/**
 * 编辑设备类型
 *
 * <AUTHOR>
 */

@Data
public class UpdateDeviceTypeDTO implements Serializable {

    private static final long serialVersionUID = -934469901419370654L;

    /**
     * 设备类型Id
     */
    private Long deviceTypeId;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备类型描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 设备类型图片
     */
    private String deviceTypeUrl;

    /**
     * 产品型号（子型号）编码
     */
    private String productModelCode;

    /**
     * 设备类型内部名称
     */
    private String deviceTypeInnerName;

    /**
     * 元石行业设备类型名称
     */
    private String deviceTypeNameHy;

    /**
     * 钉钉分销设备类型名称
     */
    private String deviceTypeNameDing;

    /**
     * 魔蓝分销设备类型名称
     */
    private String deviceTypeNameMl;

    /**
     * 元石行业设备类型展示名称
     */
    private String deviceTypeDisplayNameHy;

    /**
     * 钉钉分销设备类型展示名称
     */
    private String deviceTypeDisplayNameDing;

    /**
     * 魔蓝分销设备类型展示名称
     */
    private String deviceTypeDisplayNameMl;

    /**
     * 设备类型拓展配置
     */
    private String config;

    /**
     * 操作人姓名
     */
    private String operatorName;
}