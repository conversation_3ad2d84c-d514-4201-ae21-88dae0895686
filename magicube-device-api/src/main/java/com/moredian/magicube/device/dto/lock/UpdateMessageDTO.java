package com.moredian.magicube.device.dto.lock;

import com.moredian.magicube.device.enums.iot.MessageStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-25 17:12
 */
@Data
public class UpdateMessageDTO implements Serializable {

    private Long messageId;

    /**
     * @see MessageStatusEnum
     */
    private Integer messageStatus;

    private String messageResponse;
}
