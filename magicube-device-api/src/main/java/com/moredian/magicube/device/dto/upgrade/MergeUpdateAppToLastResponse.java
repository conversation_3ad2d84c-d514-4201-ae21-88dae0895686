package com.moredian.magicube.device.dto.upgrade;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MergeUpdateAppToLastResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    private String deviceSn;
    private String deviceId;
    private RomUpdateInfo romUpdateInfo;
    private AppUpdateInfo appUpdateInfo;

    @Getter
    @Setter
    @ToString
    public static class RomUpdateInfo implements Serializable {
        private static final long serialVersionUID = -5367031446610965760L;
        private String serialNumber;
        private String deviceId;
        private Integer appType;
        private Integer preVersionCode;
        private Integer versionCode;
        private Boolean isIncre;
        private String url;
    }

    @Getter
    @Setter
    @ToString
    public static class AppUpdateInfo implements Serializable {
        private static final long serialVersionUID = -5367031446610965760L;
        private String serialNumber;
        private String deviceId;
        private Boolean force = true;
        private int appType;
        private int versionCode;
        private String apkUrl;
        private Integer upgradeEventType;
    }

}

