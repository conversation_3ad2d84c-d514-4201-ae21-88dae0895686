package com.moredian.magicube.device.dto.device.iot;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-12-28 13:50
 */
@Data
public class PageQueryIotDeviceListDTO implements Serializable {

    private static final long serialVersionUID = 828006734002472282L;

    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 设备类型
     */
    private List<Integer> deviceTypes;
    /**
     * 在离线
     */
    private Boolean online;

    /**
     * 空间树Id列表
     */
    private List<Long> treeIds;

    /**
     * 模糊搜索的设备Id列表(用或条件来查)
     */
    private List<Long> deviceIds;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 每页显示数目
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNo;

    /**
     * 是否查询设备得属性值
     */
    private Boolean propertyValue;
}
