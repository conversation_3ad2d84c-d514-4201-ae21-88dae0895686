package com.moredian.magicube.device.dto.peripherals;

import lombok.Data;

import java.io.Serializable;

/**
 * 更新外设信息
 *
 * <AUTHOR>
 */
@Data
public class UpdatePeripheralsDTO implements Serializable {

    private static final long serialVersionUID = 6143893804946304903L;

    /**
     * 外设Id
     */
    private Long peripheralsId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 外设sn
     */
    private String peripheralsSn;

    /**
     * 外设名称
     */
    private String peripheralsName;

    /**
     * 关联设备sn
     */
    private String deviceSn;

    /**
     * 绑定设备的类型
     */
    private Integer deviceType;

    /**
     * 关联设备名称
     */
    private String deviceName;

    /**
     * 设备状态  -1- 解绑  0-断开，1-绑定
     */
    private Integer status;

    /**
     * 启动状态  0-不启用；1-启用
     */
    private Integer available;

    /**
     * 外设的设备类型
     */
    private Integer peripheralsType;

    //手机imei
    private String peripheralsConnectId;

    //系统类型 1-ios ，2-android
    private Integer mobileSysType;

    /**
     * 匹配类型，1-匹配，0-不匹配
     */
    private Integer matchStatus;
}
