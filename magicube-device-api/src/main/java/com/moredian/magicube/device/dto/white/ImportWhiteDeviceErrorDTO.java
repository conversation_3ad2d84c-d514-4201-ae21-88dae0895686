package com.moredian.magicube.device.dto.white;

import lombok.Data;

import java.io.Serializable;


/**
 * 批量导入设备白名单错误信息
 *
 * <AUTHOR>
 */

@Data
public class ImportWhiteDeviceErrorDTO implements Serializable {

    private static final long serialVersionUID = -6906899348837175108L;

    /**
     * 行数
     */
    private Integer line;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 无线mac地址
     */
    private String macAddress;

    /**
     * 有线mac地址
     */
    private String macAddress2;

    /**
     * 设备激活私钥
     */
    private String privateKey;

    public ImportWhiteDeviceErrorDTO(Integer line, String message, InsertWhiteDeviceDTO insertWhiteDeviceDTO) {
        this.line = line;
        this.message = message;
        this.deviceSn = insertWhiteDeviceDTO.getSerialNumber();
        this.macAddress = insertWhiteDeviceDTO.getMacAddress();
        this.macAddress2 = insertWhiteDeviceDTO.getMacAddress2();
        this.privateKey = insertWhiteDeviceDTO.getPrivateKey();
    }
}
