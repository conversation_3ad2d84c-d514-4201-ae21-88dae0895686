package com.moredian.magicube.device.dto.peripherals;

import com.moredian.magicube.common.enums.PeripheralsType;
import com.moredian.magicube.device.dto.PageDate;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;


/**
 * @Auther: _AF
 * @Date: 2/23/22 17:02
 * @Description: 新的白名单查询
 */
@Data
public class PeripheralsWhiteListNewQueryDTO extends PageDate implements Serializable {

    private static final long serialVersionUID = 2187923671214784481L;
    /**
     * 外设sn
     */
    private String peripheralsSn;
    /**
     * 关联设备类型，全部类型为0
     */
    private Integer deviceType;
    /**
     * aPP类型，全部类型为0
     */
    private Integer appType;

    /**
     * 外设类型
     * @see PeripheralsType
     */
    private Integer peripheralsType;

    /**
     * 开始时间，与截止时间搭配使用，要么都为空，要么都不为空
     */
    private Date beginTime;
    /**
     * 截止时间
     */
    private Date endTime;
}
