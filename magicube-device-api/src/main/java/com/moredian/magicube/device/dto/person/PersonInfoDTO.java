package com.moredian.magicube.device.dto.person;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 应终端要求，由于拆分包机制问题，导致数据需要放在一包中下发。需要小于4K，所以名字缩写
 * @Date 2023/12/25
 */
@Getter
@Setter
@ToString
public class PersonInfoDTO implements Serializable {

    private static final long serialVersionUID = 1316804983214731255L;

    /**
     * 人员id
     */
    private Long pI;

    /**
     * 人员类型
     */
    private Integer pT;

    /**
     * 特征值
     */
    private String ft;

    /**
     * 卡列表
     */
    private List<SimpleCardInfoDTO> cL;

    /**
     * 组列表
     */
    private List<Long> gL;

    //由于终端是异步请求，所以请求无法知道自己的入参，为了方便后续的业务处理将入参也一并返回

    /**
     * 是否需要特征值
     */
    private Boolean nF = Boolean.FALSE;

    /**
     * 是否需要卡片
     */
    private Boolean nC = Boolean.FALSE;

    /**
     * 是否需要组
     */
    private Boolean nG = Boolean.FALSE;
}
