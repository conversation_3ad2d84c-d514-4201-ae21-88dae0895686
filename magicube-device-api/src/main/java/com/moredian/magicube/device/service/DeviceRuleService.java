package com.moredian.magicube.device.service;


import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.common.valid.BeeValid;
import com.moredian.magicube.device.dto.device.*;
import com.moredian.magicube.device.dto.device.rule.QueryRuleConfigDTO;
import com.moredian.magicube.device.dto.device.rule.RuleConfigDTO;
import com.moredian.magicube.device.dto.device.rule.RuleLockDTO;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleService.java, v 1.0 Exp $
 */
@BeeValid
public interface DeviceRuleService {

    /***
     * 1、显示空间关联规则
     * 2、显示机构所有规则，空间关联状态
     * 3、显示机构所有规则
     */
    ServiceResponse<List<DeviceRuleDTO>> getRuleList(@Valid RuleQueryDTO dto);

    /**
     * 获取规则的默认模板列表
     */
    ServiceResponse<List<DeviceRuleTemplateDTO>> getRuleDefaultTemplate(@Valid RuleTemplateQueryDTO dto);

    /**
     * 获取规则的支持设备类型列表
     * @param dto
     * @return
     */
    ServiceResponse<List<IotDeviceTypeDTO>> getRuleDeviceTypes(@Valid RuleDeviceTypeQueryDTO dto);

    /**
     * 更新空间关联规则信息
     * @return
     */
    ServiceResponse<Boolean> updateSpaceRule(@Valid SpaceRuleUpdateDTO dto);

    /**
     * 查询规则可关联的空间列表
     * @param dto
     * @return
     */
    ServiceResponse<List<SpaceDTO>> getRuleRelatableSpace(@Valid RuleSpaceQueryDTO dto);

    /**
     * 删除规则
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> deleteRule(@Valid RuleDeleteDTO dto);

    /**
     * 规则关联空间
     */
    ServiceResponse<Boolean> ruleBingSpace(@Valid RuleBindSpaceDTO dto);


    /**
     * 规则编排规则加/解锁
     */
    ServiceResponse<Boolean> ruleLock(RuleLockDTO dto);

    /**
     * 查询规则相关配置
     */
    ServiceResponse<RuleConfigDTO> queryRuleConfig(QueryRuleConfigDTO dto);
}
