package com.moredian.magicube.device.dto.device;

import lombok.Data;

import java.io.Serializable;

/**
 * 更新设备信息
 *
 * <AUTHOR>
 */

@Data
public class UpdateDeviceDTO implements Serializable {

    private static final long serialVersionUID = -5732441731651640182L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 位置Id
     */
    private Long positionId;

    /**
     * 位置
     */
    private String position;

    /**
     * 设备容量超限：默认0-没有超限，1-超限
     */
    private Integer capacityExceeded;

    /**
     * 联应用列表
     */
    private String appCode;

    /**
     * 联应用列表
     */
    private String appCodeList;


}
