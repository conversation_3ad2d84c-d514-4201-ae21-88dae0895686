package com.moredian.magicube.device.dto.group;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/9/22
 */
@Getter
@Setter
@ToString
public class ResetGroupRelationDTO implements Serializable {

    private static final long serialVersionUID = -5482191912812844965L;

    /**
     * 处理结果
     */
    private Boolean result;

    /**
     * 新增绑定的deviceId列表
     */
    private List<Long> addedDeviceIdList;

    /**
     * 删除绑定的deviceId列表
     */
    private List<Long> removedDeviceIdList;
}
