package com.moredian.magicube.device.dto.activate;

import java.io.Serializable;
import lombok.Data;

/**
 * 解绑设备信息
 *
 * <AUTHOR>
 */

@Data
public class UnbindDeviceDTO implements Serializable {

    private static final long serialVersionUID = -8678090250374303674L;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 机构号
     */
    private Long orgId;
}