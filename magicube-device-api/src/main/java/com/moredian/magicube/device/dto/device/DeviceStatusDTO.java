package com.moredian.magicube.device.dto.device;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 设备状态信息
 *
 * <AUTHOR>
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeviceStatusDTO implements Serializable {

    private static final long serialVersionUID = -1592346599182755284L;

    /**
     * 设备条码
     */
    private String serialNumber;

    /**
     * 状态：默认管理状态
     */
    private Byte status;

    /**
     * 命令类型 心跳(1) 回复(4) 升级(5) 告警(6) 重启(7) 升级(10) 状态(8) 透传(9) 失联(10000) 重启(20001)
     */
    private Integer eventType;

    /**
     * progress进度  重启(7) ：{1 100}  升级(10)：{1~100}
     */
    private Byte progress;

    /**
     * 是否在线
     */
    private Boolean online;

    /**
     * 最后更新时间
     */
    private Long lastTimeStamp;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("serialNumber:[" + serialNumber + "],");
        sb.append("status:[" + status + ",online:" + online + ",progress:" + progress + ",eventType:" + eventType + "]");
        return sb.toString();
    }
}