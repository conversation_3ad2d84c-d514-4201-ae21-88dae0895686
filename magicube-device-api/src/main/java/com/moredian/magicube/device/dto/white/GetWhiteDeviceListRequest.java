package com.moredian.magicube.device.dto.white;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/7/25
 */
@Getter
@Setter
public class GetWhiteDeviceListRequest implements Serializable {

    private static final long serialVersionUID = 5954796885460873018L;

    /**
     * 设备sn列表
     */
    @NotEmpty(message = "设备sn列表不能为空")
    private List<String> deviceSns;

}
