package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.carme.InsertPeopleNumStatisticDTO;
import com.moredian.magicube.device.dto.carme.PeopleNumberDTO;
import java.util.List;

/**
 * 摄像头人数统计
 *
 * <AUTHOR>
 */
public interface PeopleNumberStatisticService {

    /**
     * 新增摄像头人数统计信息
     *
     * @param dto 人数信息
     * @return
     */
    ServiceResponse<Long> insert(InsertPeopleNumStatisticDTO dto);

    /**
     * 根据设备sn列表查询摄像头人数统计信息
     *
     * @param orgId     机构Id
     * @param deviceSns 设备sn列表
     * @return
     */
    ServiceResponse<List<PeopleNumberDTO>> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns);

}
