package com.moredian.magicube.device.dto.snapshot;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/29
 */
@Getter
@Setter
@ToString
public class DeviceSnapshotDTO implements Serializable {

    private static final long serialVersionUID = 1743763458515036755L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 同步时间戳
     */
    private Long timestamp;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;
}
