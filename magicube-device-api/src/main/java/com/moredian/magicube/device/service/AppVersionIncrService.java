package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.upgrade.GetIncrOrFullNewAppRequest;
import com.moredian.magicube.device.dto.upgrade.GetRomUpgradeRequest;
import com.moredian.magicube.device.dto.version.AppVersionIncrDTO;
import com.moredian.magicube.device.dto.version.AppVersionIncreWithAppVersionInfoDTO;

/**
 * 增量升级包Service
 */
public interface AppVersionIncrService {

    /**
     * 查询增量升级包
     *
     * @return
     */
    ServiceResponse<AppVersionIncrDTO> getNewAppVersionIncrByVersionIdAndOldVersion(
        Integer oldVersionCode, Long versionId);

    /**
     * 查询增量升级包,返回是否需要升级，版本详情等状态
     *
     * @return
     */

    ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncrByAppTypeAndOldVersion(
        Integer systemType, Integer appType, Integer oldVersionCode);

    /**
     * 查询增量升级包,返回是否需要升级，版本详情等状态
     *
     * @return
     */

    ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncrByAppTypeAndOldVersion(GetRomUpgradeRequest request);

    /**
     * 查询增量升级包,返回是否需要升级，版本详情等状态
     * 支持灰度版本检测
     *
     * @param orgId
     * @param systemType
     * @param appType
     * @param oldVersionCode
     * @return
     */
    ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncrByAppTypeAndOldVersion(Long orgId, Integer systemType, Integer appType, Integer oldVersionCode);

    /**
     * 查询增量升级包,返回是否需要升级，版本详情等状态
     * 支持灰度版本检测
     *
     * @param request
     * @return
     */
    ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncreByAppTypeAndOldVersion(GetIncrOrFullNewAppRequest request);


}