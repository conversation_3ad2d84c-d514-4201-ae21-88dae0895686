package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.DeviceAuthDetailDTO;
import com.moredian.magicube.device.dto.white.*;

import java.util.List;
import javax.validation.constraints.NotBlank;

/**
 * 设备白名单服务
 *
 * <AUTHOR>
 */
public interface WhiteDeviceService {

    /**
     * 新增白名单
     *
     * @param dto 设备白名单信息
     * @return
     */
    ServiceResponse<Boolean> insert(InsertWhiteDeviceDTO dto);

    /**
     * 批量新增白名单
     *
     * @param list 设备白名单信息列表
     * @return
     */
    ServiceResponse<List<ImportWhiteDeviceErrorDTO>> batchInsert(List<InsertWhiteDeviceDTO> list);

    /**
     * 批量添加白单（不进行参数校验）
     * 批量新增前需 {@linkplain WhiteDeviceService#batchInsert(List)} (List<InsertWhiteDeviceDTO>) }, 进行校验
     *
     * @param request
     * @return
     */
    ServiceResponse<Boolean> batchInsertWithoutCheck(BatchInsertWhiteListDeviceRequest request);

    /**
     * 分页查询设备白名单信息
     *
     * @param dto 分页条件
     * @return
     */
    ServiceResponse<Pagination<WhiteDeviceDTO>> listPage(QueryWhiteDeviceDTO dto);

    /**
     * 根据设备Sn查询白名单设备信息
     *
     * @param deviceSn 设备Sn
     * @return
     */
    ServiceResponse<WhiteDeviceDTO> getByDeviceSn(String deviceSn);

    /**
     * 根据设备Sn列表查询白名单设备信息列表
     *
     * @param deviceSns 设备Sn列表
     * @return
     */
    ServiceResponse<List<WhiteDeviceDTO>> listByDeviceSns(List<String> deviceSns);

    /**
     * 删除白名单
     *
     * @param deviceSn 设备sn
     * @return
     */
    ServiceResponse<Boolean> delete(String deviceSn);

    /**
     * 批量更新白名单设备信息
     *
     * @param dto 更新白名单信息列表
     * @return
     */
    ServiceResponse<Boolean> batchUpdate(List<UpdateWhiteDeviceDTO> dto);

    /**
     * 批量校验白名单
     *
     * @param list 设备白名单信息列表
     * @return
     */
    ServiceResponse<List<ImportWhiteDeviceErrorDTO>> batchCheckWhiteListDevice(List<InsertWhiteDeviceDTO> list);

    /**
     * 批量校验白名单
     *
     * @param list 设备白名单信息列表
     * @return
     */
    ServiceResponse<Boolean> batchInsertWithoutCheck(List<InsertWhiteDeviceDTO> list);

    /**
     * 获取设备白单，版本号，激活状态信息
     *
     * @param request
     * @return
     */
    ServiceResponse<List<SimpleWhiteDeviceResponse>> getBatchSimpleDeviceInfo(GetWhiteDeviceListRequest request);


    /**
     * 根据设备sn获取设备对应的sdk（钉、非钉）
     */
    ServiceResponse<DeviceAuthDetailDTO> getSdkBaseBySN(@NotBlank String deviceSn);

    /**
     * 根据设备sn修改白单
     */
    ServiceResponse<Boolean> updateByDeviceSn(UpdateWhiteDeviceDTO dto);
}
