package com.moredian.magicube.device.dto.project;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/11
 */
@Getter
@Setter
@ToString
public class PullDeviceProjectInfoRequest implements Serializable {

    private static final long serialVersionUID = 4690878436600569684L;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 项目Id
     */
    private String projectId;

    /**
     * 由后端下发mqtt中携带的参数，默认为false，true:不校验projectId，false:校验
     */
    private Boolean enforce = Boolean.FALSE;
}
