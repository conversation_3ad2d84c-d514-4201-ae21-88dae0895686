package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description：设备对应的sdk
 * @date ：2024/09/24 17:36
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceAuthDetailDTO implements Serializable {

    private static final long serialVersionUID = 7775369399086422643L;

    /**
     * 设备编号
     */
    private String  deviceSn;
    /**
     * 授标标识(0,1)
     */
    private Boolean hasDeviceAuthFlag;
    /**
     * 授权机构
     */
    private Long    authOrg;
    /**
     * 机构BASE  0魔蓝 1钉钉
     */
    private Integer base;
    /**
     * 机构公/私有
     */
    private String  authOrgTpId;
    /**
     * 机构公/私有 机构类型1分销,2行业,3子节点,4私有化
     */
    private Boolean orgPublicOrPrivateFlag;
    /**
     * 跳转APPcode
     */
    private String  appCode;
    /**
     * 跳转链接
     */
    private String  appRedirectUrl;
    /**
     * 设备激活oaBase 1-非钉 2-钉钉
     */
    private Integer oaBase;

    /**
     * 设备激活sdkBase 1-非钉 2-钉钉
     */
    private Integer sdkBase;

    /**
     * 设备是否在托管白单内，如果在前端判断跳过，返回false
     */
    private Boolean authFilter;

    /**
     * 是否是渠道商设备
     */
    private Boolean isAgentDevice;
}
