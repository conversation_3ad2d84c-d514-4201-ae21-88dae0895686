package com.moredian.magicube.device.dto.activate;

import com.moredian.magicube.device.constant.ModuleType;
import lombok.Data;

import java.io.Serializable;

/**
 * 激活设备信息
 *
 * <AUTHOR>
 */

@Data
public class ActivationDeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = -307221713821713950L;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 识别通道
     */
    private Integer verifyChannel;

    /**
     * 如果是慧眼，这里设置识别算法,1:旷世，2:依图
     */
    private Integer verifyArithmetic;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 业务类型
     *
     * @see ModuleType
     */
    private Integer modelType;

    /**
     * 第三方机构类型
     */
    private Integer orgTpType;

    /**
     * 第三方机构Id
     */
    private String orgTpId;
}