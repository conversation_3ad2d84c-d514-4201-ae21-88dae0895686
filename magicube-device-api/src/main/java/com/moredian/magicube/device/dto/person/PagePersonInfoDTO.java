package com.moredian.magicube.device.dto.person;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 应终端要求，由于拆分包机制问题，导致数据需要放在一包中下发。需要小于4K，所以名字缩写
 * @Date 2023/12/25
 */
@Getter
@Setter
@ToString
public class PagePersonInfoDTO implements Serializable {

    private static final long serialVersionUID = -4579750550359450651L;

    /**
     * 机构id
     */
    private Long oI;

    /**
     * 设备Id
     */
    private Long dI;

    /**
     * 设备Sn
     */
    private String dS;

    /**
     * 权限组列表
     */
    private List<Long> gL;

    /**
     * 是否需要特征值
     */
    private Boolean nF = Boolean.FALSE;

    /**
     * 是否需要卡片
     */
    private Boolean nC = Boolean.FALSE;

    /**
     * 是否需要组
     */
    private Boolean nG = Boolean.FALSE;

    /**
     * 魔点算法模型版本号
     */
    private String mV;

    /**
     * 1:大模型 2:小模型
     */
    private Integer fT;

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 页数
     */
    private Integer pageSize = 10;
}
