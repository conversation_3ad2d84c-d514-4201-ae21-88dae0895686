package com.moredian.magicube.device.constant;


/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/18
 */
public class RedisLockConstants {

    /**
     * 缓存key公共前缀
     */
    public static final String COMMON_CACHE_KEY_PREFIX = "moredian:device:cache:";

    /**
     * app跳转链接
     */
    public static final String APP_TYPE_REDIRECT = COMMON_CACHE_KEY_PREFIX + "app.type.redirect:%s";

    public static String getKey(String keyFormat, Object... params) {
        return String.format(keyFormat, params);
    }
}
