package com.moredian.magicube.device.dto.peripherals;

import lombok.Data;

import java.io.Serializable;

/**
 * 外设白名单信息
 *
 * @author: fangJ
 */

@Data
public class PeripheralsWhiteListDTO implements Serializable {

    private static final long serialVersionUID = 8257131782256805305L;

    /**
     * 外设白名单Id
     */
    private Long peripheralsWhiteListId;

    /**
     * 外设sn
     */
    private String peripheralsSn;

    /**
     * 外设的设备类型
     */
    private Integer peripheralsType;

    /**
     * 状态：0-断开 1-连接
     */
    private Integer status;
}
