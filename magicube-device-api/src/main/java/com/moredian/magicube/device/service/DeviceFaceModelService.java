package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.register.SaveDeviceFaceModelRequest;

/**
 * <AUTHOR>
 * @since 2023-06-26
 */
public interface DeviceFaceModelService  {

    /**
     * 保存设备人脸模型版本
     *
     * @param request
     * @return
     */
    ServiceResponse<Boolean> saveDeviceFaceModel(SaveDeviceFaceModelRequest request);

}