package com.moredian.magicube.device.constant;

public class HiveConst {

    public static final String MEMCACHED_ACTIVE_CODE_FRO_EQUIPMENT_ACTIVE = "ACTIVE_CODE_FOR_EQUIPMENT_ACTIVE";
    public static final String MEMCACHED_EQUIPMENT_QRCODE_KEY = "EQUIPMENT_QRCODE_KEY";
    public static final String MEMCACHED_EQUIPMENT_QRCODE_STATUS_KEY = "EQUIPMENT_QRCODE_STATUS_KEY";
    public static final String MEMCACHED_EQUIPMENT_QRCODE_SN = "EQUIPMENT_QRCODE_DEVICE_SN_KEY";

    public static final String MEMCACHED_EQUIPMENT_ACTIVE_STATUS_KEY = "EQUIPMENT_ACTIVE_STATUS_KEY";
    public static final String MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY = "THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY";

    public static final String MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_INFO_KEY = "THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_INFO_KEY";

    /**
     * openapi 通过sn 激活的设备
     */
    public static final String ACTIVE_SN = "ACTIVESN_SN";

    public static final long THIRD_PARTY_TIME_OUT = 60L;

    public static final long ACTIVE_DEVICE_TIME_OUT = 300L;
}