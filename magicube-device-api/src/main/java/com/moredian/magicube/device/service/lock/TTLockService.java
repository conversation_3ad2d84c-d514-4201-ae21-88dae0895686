package com.moredian.magicube.device.service.lock;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.lock.InitialLockDTO;
import com.moredian.magicube.device.dto.lock.LockInfoDTO;
import com.moredian.magicube.device.dto.lock.NetworkInfoDTO;
import com.moredian.magicube.device.dto.lock.UpdateLockDTO;

/**
 * <AUTHOR>
 * @Description 通通锁相关接口
 * @create 2025-02-21 14:22
 */
public interface TTLockService {


    /**
     * 初始化锁在通通锁云平台的数据，返回初始化信息
     */
    ServiceResponse<LockInfoDTO> initialLock(InitialLockDTO initialLockDTO);

    /**
     * 通通锁解绑设备（1、平台上设备解绑；2、魔链上设备删除）
     * @param lockInfoDTO 通通锁设备信息。deviceSn--> not null
     */
    ServiceResponse<Boolean> unbindLock(LockInfoDTO lockInfoDTO);

    /**
     * 激活通通锁设备。
     * 1.平台设备激活，魔链数据保存
     * 2.运行模式上报，默认上报0
     * 3.根空间绑定
     * @param lockInfoDTO 锁信息，deviceSn--> not null
     *                          orgId--> not null
     *                          deviceType--> not null
     */
    ServiceResponse<Boolean> activate(LockInfoDTO lockInfoDTO);

    /**
     * (通通锁专用)
     * 通通锁上报wifi网络状态信息，上报网络状态信息到 redis 缓存，等设备真正激活，再写到表中
     * @param deviceSn 设备sn
     * @param networkInfoDTO 网络状态信息
     */
    ServiceResponse<Boolean> uploadNetworkCache(String deviceSn, NetworkInfoDTO networkInfoDTO);


    /**
     * 根据SN获取通通锁详情信息
     */
    ServiceResponse<LockInfoDTO> lockInfoBySn(String deviceSn);


    /**
     * 添加、更新、删除通行密码
     * 添加：如果enablePassword是开启，但是锁不存在密码则是添加密码
     * 更新：如果enablePassword是开启，但是锁存在密码则是更新密码
     * 删除：如果enablePassword是关闭，则是删除密码
     */
    ServiceResponse<Long> updatePassword(UpdateLockDTO dto);

    /**
     * 上报通通锁在离线状态
     */
    ServiceResponse<Boolean> uploadLockOnlineState(UpdateLockDTO dto);
}
