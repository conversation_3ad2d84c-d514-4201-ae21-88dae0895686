package com.moredian.magicube.device.dto.dahua;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：大华摄像头人数统计
 * @date ：2024/10/08 13:51
 */
@Data
public class CameraPeopleNumDTO implements Serializable {

    private static final long serialVersionUID = 6306528393948916551L;

    /**
     * 是否有摄像头设备，没有前端不展示
     */
    private Boolean hasDevice;

    /**
     * 摄像头人数
     */
    private List<DataList> dataList;

    @Data
    public static class DataList implements Serializable{
        private static final long serialVersionUID = 3081582308191119719L;
        /**
         * 设备sn
         */
        private String deviceSn;

        /**
         * 当前摄像头下的人数
         */
        private Integer peopleNum;

    }
}
