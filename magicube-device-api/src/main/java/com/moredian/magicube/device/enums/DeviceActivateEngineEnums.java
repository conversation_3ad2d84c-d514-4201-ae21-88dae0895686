package com.moredian.magicube.device.enums;

/**
 * 设备激活引擎枚举
 *
 * <AUTHOR>
 */
public enum DeviceActivateEngineEnums {

    MO_DEVICE_QR_CODE_ACTIVATE(1, "魔蓝设备二维码激活"),
    DING_DEVICE_QR_CODE_ACTIVATE(2, "钉钉设备二维码激活"),
    NO_ENABLE_DING_DEVICE_QR_CODE_ACTIVATE(3, "没开通魔点门禁微应用激活的钉钉设备二维码激活"),
    ACTIVATION_CODE_ACTIVATE(4, "激活码激活"),
    DING_REVERSE_ACTIVATE(5, "钉钉反扫二维码激活"),
    MO_REVERSE_ACTIVATE(6, "魔蓝渠道反扫二维码激活"),
    DING_REVERSE_REMOVE_SPACE_ACTIVATE(7, "钉钉反扫二维码去空间激活"),
    MO_REVERSE_REMOVE_SPACE_ACTIVATE(8, "非钉反扫二维码去空间激活"),
    THIRD_DEVICE_ACTIVATE(9, "三方设备激活流程"),
    ;

    private Integer code;
    private String desc;

    DeviceActivateEngineEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DeviceActivateEngineEnums getByCode(Integer code) {

        for (DeviceActivateEngineEnums each : values()) {
            if (code.equals(each.code)) {
                return each;
            }
        }

        throw new RuntimeException("engine code error");
    }

}
