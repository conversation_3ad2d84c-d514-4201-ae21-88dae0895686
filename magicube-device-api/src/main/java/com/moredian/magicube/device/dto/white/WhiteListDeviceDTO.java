package com.moredian.magicube.device.dto.white;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 新增设备白名单
 *
 * <AUTHOR>
 * @since 2022/7/20
 */
@Getter
@Setter
public class WhiteListDeviceDTO implements Serializable {

    private static final long serialVersionUID = 673377712776638446L;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备Sn 必填
     */
    @NotBlank(message = "设备sn不能为空")
    private String serialNumber;

    /**
     * mac地址 必填
     */
    @NotBlank(message = "无线mac地址不能为空")
    private String macAddress;

    /**
     * mac地址
     */
    @NotBlank(message = "有线mac地址不能为空")
    private String macAddress2;

    /**
     * 设备激活私钥 必填
     */
    @NotBlank(message = "设备激活私钥不能为空")
    private String privateKey;

    /**
     * 是否开放平台激活
     */
    private Boolean openPlat;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 设备类型
     */
    private Integer deviceType;

}
