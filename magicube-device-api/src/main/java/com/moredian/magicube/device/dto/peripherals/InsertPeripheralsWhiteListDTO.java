package com.moredian.magicube.device.dto.peripherals;

import java.io.Serializable;
import lombok.Data;

/**
 * 外设白名单
 *
 * @author: fangJ
 */

@Data
public class InsertPeripheralsWhiteListDTO implements Serializable {

    private static final long serialVersionUID = 3841598038572685498L;

    /**
     * 外设sn
     */
    private String peripheralsSn;

    /**
     * 外设的设备类型
     */
    private Integer peripheralsType;

    /**
     * 状态：0-断开 1-连接
     */
    private Integer status;
}
