package com.moredian.magicube.device.dto.device;

import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-10 16:03
 */
@Data
public class DeviceActiveStateChangeDTO implements Serializable {

    private static final long serialVersionUID = -2119981446228917452L;

    private String deviceSn;

    /**
     * 激活状态
     * @see com.moredian.magicube.device.enums.DeviceActivateEnum
     */
    private Integer state;

    /**
     * 钉钉绑定团队url(设备上报)
     */
    private String bindOrgUrl;

    /**
     * 非钉绑定机构id
     */
    private Long bindOrgId;
}
