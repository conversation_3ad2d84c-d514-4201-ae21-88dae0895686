package com.moredian.magicube.device.dto.subject;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 更新设备主题
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
public class UpdateDeviceSubjectDTO implements Serializable {

    private static final long serialVersionUID = 5305084884202465830L;

    /**
     * 设备主题id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备主题名称
     */
    private String name;

    /**
     * 主题图片url列表
     */
    private List<String> imgUrls;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业logo图片url
     */
    private String logoImgUrl;

    /**
     * 设备Id列表
     */
    private List<Long> deviceIds;


    /**
     * 空间Id列表
     */
    private List<Long> spaceIds;

    /**
     * 应用所有设备标识 0-不应用 1-应用所有设备
     */
    private Integer allDeviceFlag;

    /**
     * 模板类型：1-模板一 2-模板二
     */
    private Integer templateType;


    /**
     * 1壁纸，2屏保
     */
    private Integer type=1;

    /**
     * 1开，0关闭
     */
    private Integer enable=1;


    /**
     * 屏保相关业务参数
     */
    private ScreenSaver screenSaver;
}
