package com.moredian.magicube.device.dto.blu;

import java.io.Serializable;

public class DeviceBluConnectDeCryptResp implements Serializable {

    private static final long serialVersionUID = 2277977289201142169L;
    /**
     * 解密字符串
     */
    private String decryptStr;

    public String getDecryptStr() {
        return decryptStr;
    }

    public void setDecryptStr(String decryptStr) {
        this.decryptStr = decryptStr;
    }
}

