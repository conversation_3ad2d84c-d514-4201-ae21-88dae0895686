package com.moredian.magicube.device.dto.activate;

import java.io.Serializable;
import lombok.Data;

/**
 * 查询设备激活信息条件
 *
 * <AUTHOR>
 */
@Data
public class QueryActivateInfoDTO implements Serializable {

    private static final long serialVersionUID = -4668866212914498712L;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 校验code码
     */
    private String checkCode;

    /**
     * 是否需要离线密码
     */
    private Boolean isNeedOfflinePassword;
}
