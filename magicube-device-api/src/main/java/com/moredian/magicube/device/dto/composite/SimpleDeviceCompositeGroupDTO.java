package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/11/25 17:32
 */

@Getter
@Setter
@ToString
public class SimpleDeviceCompositeGroupDTO implements Serializable {

    private static final long serialVersionUID = -2405288701596509185L;

    /**
     * 组id
     */
    private Long groupId;

    /**
     * 设备组
     */
    private List<SimpleDeviceCompositeGroup> deviceCompositeGroupList;

    /**
     * 设备列表
     */
    private List<SimpleDevice> deviceList;

    @Getter
    @Setter
    @ToString
    public static class SimpleDevice implements Serializable {


        private static final long serialVersionUID = -6323858528550182503L;
        /**
         * 设备id
         */
        private Long deviceId;

        /**
         * 设备SN
         */
        private String deviceSn;
    }


    @Getter
    @Setter
    @ToString
    public static class SimpleDeviceCompositeGroup implements Serializable {


        private static final long serialVersionUID = -5329299611061915003L;
        /**
         * 设备组id
         */
        private Long deviceCompositeId;

        /**
         * 设备组名称
         */
        private String deviceCompositeName;

        /**
         * 业务类型
         */
        private Integer bizType;
        /**
         * 父路径
         */
        private Long parentDeviceCompositeId;

        /**
         * 名称,
         */
        private String pathName;

        /**
         * ID path
         */
        private String idPath;

        /**
         * 设备数量
         */
        private Integer deviceSize;

        /**
         * 设备Id列表
         */
        private List<Long> deviceIdList;
    }
}