package com.moredian.magicube.device.dto.qrcode;

import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import java.io.Serializable;
import lombok.Data;

/**
 * @Classname： ActivateQrCodeCreateDto
 * @Date: 2023/1/4 11:44 上午
 * @Author: fangJ
 * @Description: 编辑配网信息
 */
@Data
public class UpdateNetworkInfoDTO implements Serializable {

    private static final long serialVersionUID = -1987611981680195935L;

    private Long id;

    private Long orgId;

    /**
     * 1-有线，默认
     * 2-无线
     * @see ActivateQrCodeConstant.NetworkTypeEnum
     */
    private Integer networkType = 1;

    /**
     * 1-DHCP
     * 2-静态，默认
     * @see  ActivateQrCodeConstant.ConnectTypeEnum
     */
    private Integer connectType = 2;

    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;

    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;

    /**
     * 设备配网码状态 0-待处理 1-处理中 2-处理完成
     */
    private Integer status;

    /**
     * 绑定机构页面url（仅用于钉钉SDK激活流程）
     */
    private String bindOrgUrl;
}
