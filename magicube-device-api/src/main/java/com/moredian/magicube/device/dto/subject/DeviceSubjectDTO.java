package com.moredian.magicube.device.dto.subject;

import com.moredian.magicube.device.dto.PageDate;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 设备主题信息
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
public class DeviceSubjectDTO extends PageDate implements Serializable {

    private static final long serialVersionUID = 7592160907716617071L;

    /**
     * 设备主题id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备主题名称
     */
    private String name;

    /**
     * 主题图片url列表
     */
    private List<String> imgUrls;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业logo图片url
     */
    private String logoImgUrl;

    /**
     * 主题类型 1-壁纸 2-屏保
     */
    private Integer type;

    /**
     * 模板类型：1-模板一 2-模板二
     */
    private Integer templateType;


    /**
     * 开关，1开0关
     */
    private Integer enable;

    /**
     * 设备信息列表
     */
    private List<DeviceInfoDTO> devices;


    private List<DeviceInfoDTO> allDevices;
    /**
     * 空间Id列表
     */
    private List<Long> spaceIds;

    /**
     * 屏保视频信息
     */
    private ScreenSaver screenSaver;

    /**
     * 数据权限过滤ID
     */
    private List<Long> subjectIdList;

}
