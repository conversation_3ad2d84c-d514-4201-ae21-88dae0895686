package com.moredian.magicube.device.dto.dahua;

import java.io.Serializable;
import lombok.Data;

/**
 * 大华摄像头设备信息
 *
 * <AUTHOR>
 */

@Data
public class CameraDeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = -9133782172006889938L;

    /**
     * 摄像头设备Id
     */
    private Long cameraDeviceId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备ip
     */
    private String deviceIp;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 设备用户名
     */
    private String userName;

    /**
     * 设备密码
     */
    private String password;

    /**
     * 注册类型 1-平台注册 2-设备注册
     */
    private Integer registerType;

    /**
     * 本地设备ip
     */
    private String localDeviceIp;

    /**
     * 本地端口号
     */
    private Integer localPort;
}