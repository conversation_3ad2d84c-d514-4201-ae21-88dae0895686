package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Auther: _AF
 * @Date: 2020/11/26 16:51
 * @Description:
 */
@Data
public class DeviceCompositeCreateDTO implements Serializable {

    private static final long serialVersionUID = 747549162511500468L;

    /**
     * 机构id
     */
    private Long orgId;
    /**
     * 类型，使用，0-通用
     */
    private Integer bizType;

    /**
     * 分组名
     */
    private String deviceCompositeName;
    /**
     * id
     */
    private Long deviceCompositeId;


    /**
     * 如果添加设备的组还不存在的时候，要创建组，可以在这里指定要创建的组在那个组目录下，不指定就默认在虚拟根下
     * 父设备组id
     */
    private Long parentId;

    /**
     * 设备id
     */
    private List<Long> deviceIdList;

}
