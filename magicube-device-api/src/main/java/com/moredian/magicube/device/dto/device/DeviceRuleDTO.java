package com.moredian.magicube.device.dto.device;

import com.moredian.magicube.common.enums.DeviceType;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleDTO.java, v 1.0 Exp $
 */
@Data
public class DeviceRuleDTO implements Serializable {
    private static final long serialVersionUID = -9051298689032467153L;

    /**
     * 规则Id
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * ${@link com.moredian.magicube.device.enums.ModeTypeEnum}
     */
    private Integer modeType;

    /**
     * ${@link DeviceType}
     */
    private List<Integer> deviceTypes;

    /**
     * 触发类型，1-定时触发，2-设备触发
     */
    private Integer triggerType;

    /**
     * 触发值
     */
    private String triggerValue;

    /**
     * 开关状态，0-关闭，1-开启
     */
    private Integer state;

    /**
     * 绑定的空间列表
     */
    private List<SpaceDTO> spaceList;

    /**
     * 场景名称
     */
    private String modeName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 时间描述
     */
    private String timeDesc;

    /**
     * 设备描述
     */
    private String deviceDesc;

    /**
     * 触发描述
     */
    private String triggerDescription;

    /**
     * 触发提醒
     */
    private String triggerTip;

    private String picUrl;

    private String picDetailUrl;

    /**
     * 人为优先
     */
    private Boolean humanPriority;
}
