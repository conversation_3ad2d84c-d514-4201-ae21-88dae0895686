package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.version.ApkAndRomInfoDTO;
import com.moredian.magicube.device.dto.version.AppUpgradeInfoDTO;
import com.moredian.magicube.device.dto.version.AppUpgradeVersionDTO;
import com.moredian.magicube.device.dto.version.AppUpgradeVersionInfoDTO;
import com.moredian.magicube.device.dto.version.VersionDTO;
import java.util.List;

/**
 * app版本信息服务
 *
 * <AUTHOR>
 */

public interface AppVersionService {

    /**
     * 增加app版本信息
     *
     * @param dto
     * @return
     */
    ServiceResponse<Long> addAppVersion(VersionDTO dto);

    /**
     * 修改app版本信息
     *
     * @param dto
     * @return
     */
    ServiceResponse<Integer> updateAppVersion(VersionDTO dto);

    /**
     * 修改app版本信息,只修改属性不为null的字段
     *
     * @param dto
     * @return
     */
    ServiceResponse<Integer> updateAppVersionSelective(VersionDTO dto);

    /**
     * 根据ID删除app版本信息
     *
     * @param id 版本Id
     * @return
     */
    ServiceResponse<Integer> removeAppVersionById(Long id);

    /**
     * 根据id获取app版本信息
     *
     * @param id
     * @return
     */
    ServiceResponse<VersionDTO> getAppVersionById(Long id);

    /**
     * (added by zc)
     * 根据app适用系统类型、app类型、版本编码获取最新的app版本信息
     *
     * @param systemType  app适用系统类型
     * @param appType     app类型
     * @param versionCode 版本编码
     * @return
     */
    ServiceResponse<VersionDTO> getAppVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode);

    /**
     * (added by zc)
     * 根据app适用系统类型、rom类型、版本编码获取最新的rom版本信息
     *
     * @param systemType  app适用系统类型
     * @param appType     rom类型
     * @param versionCode 版本编码
     * @param apkAppType  apk类型
     * @return
     */
    ServiceResponse<VersionDTO> getRomVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode, Integer apkAppType);

    /**
     * 根据app适用系统类型、app类型获取最新的app版本信息
     *
     * @param systemType app适用系统类型
     * @param appType    app类型
     * @return
     */
    ServiceResponse<VersionDTO> getNewAppVersionBySCType(Integer systemType, Integer appType);

    /**
     * 根据app适用系统类型、app类型获取最新的允许马上升级的app版本信息
     *
     * @param systemType app适用系统类型
     * @param appType    app类型
     * @return
     */
    ServiceResponse<VersionDTO> getNewAppVersionBySCTypeAndIsActive(Integer systemType, Integer appType);

    /**
     * 根据rom适用系统类型、rom类型获取最新的允许马上升级的rom版本信息
     *
     * @param systemType app适用系统类型
     * @param appType    rom类型
     * @param apkAppType apk类型
     * @return
     */
    ServiceResponse<VersionDTO> getNewRomVersionBySCTypeAndIsActive(Integer systemType, Integer appType, Integer apkAppType);

    /**
     * 根据app适用系统类型、app类型获取最新的允许马上升级的app版本信息
     *
     * @param orgId
     * @param systemType
     * @param clientType
     * @return
     */
    ServiceResponse<VersionDTO> getNewAppVersionBySCTypeAndIsActiveAndOrgId(Long orgId, Integer systemType, Integer clientType);

    /**
     * 分页查询app版本信息
     *
     * @param paginationDto
     * @param dto
     * @return
     */
    ServiceResponse<Pagination<VersionDTO>> getPaginationAppVersion(Pagination<VersionDTO> paginationDto, VersionDTO dto);

    /**
     * 获取app版本信息数量
     *
     * @param dto
     * @return
     */
    ServiceResponse<Integer> getAppVersionCount(VersionDTO dto);

    /**
     * 把app包设置成所有都升级。
     *
     * @param
     * @return
     */
    ServiceResponse changeAppversionToActive(Long appVersionId);

    ServiceResponse<Integer> getLowVersionCount(Integer versionCode, Integer appType);

    ServiceResponse<VersionDTO> getAppVersionInfo(VersionDTO VersionDTO);

    /**
     * 分页查询设备上传app包记录(微云特殊接口)
     *
     * @param pageNo   当前页数
     * @param pageSize 每页显示数目
     * @return
     */
    ServiceResponse<Pagination<VersionDTO>> listPage(Integer pageNo, Integer pageSize);

    /**
     * 获取上电强制升级apk/rom信息详情
     *
     * @param dto apk/rom版本信息
     * @return
     */
    ServiceResponse<AppUpgradeInfoDTO> getAppUpgradeInfo(ApkAndRomInfoDTO dto);

    /**
     * 判断是否有比当前版本更新的用户可见的全量包
     *
     * @param dto
     * @return
     */
    ServiceResponse<List<AppUpgradeVersionInfoDTO>> judgeExistHigherApk(List<AppUpgradeVersionDTO> dto);
}