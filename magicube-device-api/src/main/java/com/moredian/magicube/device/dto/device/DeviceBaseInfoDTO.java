package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description：获取设备初始化配置信息
 * @date ：2024/09/24 17:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceBaseInfoDTO implements Serializable {

    private static final long serialVersionUID = -8538847628074218770L;

    /**
     * 设备后台地址
     */
    private String deviceBackGroundUrl;

    /**
     * 临时权限地址
     */
    private String deviceTempAuthUrl;

    /**
     * 设备激活oaBase 1-非钉 2-钉钉
     */
    private Integer oaBase;

    /**
     * 设备激活sdkBase 1-非钉 2-钉钉
     */
    private Integer sdkBase;
}
