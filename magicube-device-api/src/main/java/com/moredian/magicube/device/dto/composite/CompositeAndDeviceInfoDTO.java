package com.moredian.magicube.device.dto.composite;

import com.moredian.magicube.device.dto.device.DeviceDTO;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 设备组信息列表
 *
 * <AUTHOR>
 * @date 2024/11/20 09:59
 */
@Data
public class CompositeAndDeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = -1982617924808546363L;

    /**
     * 机构 ID
     */
    private Long orgId;

    /**
     * 设备组信息列表
     */
    private List<DeviceCompositeDTO> compositeSearchList;

    /**
     * 【设备】列表
     */
    private List<DeviceDTO> deviceSearchList;
}
