package com.moredian.magicube.device.dto.network;

import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import java.io.Serializable;
import lombok.Data;

/**
 * 设备网络信息
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
public class DeviceNetworkInfoDTO implements Serializable {

    private static final long serialVersionUID = 8579873532388395182L;

    /**
     * 设备网络信息Id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 网络类型 1-有线 2-无线
     */
    private Integer networkType;

    /**
     * 连接类型 1-DHCP 2-静态地址
     */
    private Integer connectType;

    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;

    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;

    /**
     * 有线mac地址
     */
    private String wiredMac;

    /**
     * 无线mac地址
     */
    private String wifiMac;

    /**
     * 最近一次联网时间
     */
    private Long lastTime;

    /**
     * 设备重新配网状态 1-网络切换中 2-网络切换成功 3-网络切换失败
     */
    private Integer deviceResetNetworkStatus;
}
