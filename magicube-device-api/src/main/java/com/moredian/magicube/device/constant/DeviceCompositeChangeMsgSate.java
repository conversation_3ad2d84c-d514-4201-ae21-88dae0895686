package com.moredian.magicube.device.constant;

/**
 * @Auther: _AF
 * @Date: 2021/5/8 16:22
 * @Description:
 */
public class DeviceCompositeChangeMsgSate {
    /**
     * 设备组 删除
     * 直接删除设备组
     * 直接删除设备组包括子组，只会发一条消息，也就是最上级被删除的组
     */
    public static final Integer COMPOSITE_DELETE = 0;
    /**
     * 设备组 更新
     * ①绑定的设备更新了
     * ②名字更新了
     */
    public static final Integer COMPOSITE_UPDATE = 1;

    /**
     * 设备组 添加
     * ①创建了设备组并且关联了设备
     * ②创建了设备组没关联设备
     */
    public static final Integer COMPOSITE_ADD = 2;
}
