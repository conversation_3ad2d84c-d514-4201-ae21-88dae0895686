package com.moredian.magicube.device.dto.register;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-06-26
 */
@Data
public class SaveDeviceFaceModelRequest implements Serializable {

    private static final long serialVersionUID = 2795143471563146067L;

    /**
     * 机构ID
     */
    @NotNull(message = "orgId must not be null")
    private Long orgId;

    /**
     * 设备ID
     */
    @NotNull(message = "deviceId must not be null")
    private Long deviceId;

    /**
     * 人脸模型
     */
    @NotBlank(message = "faceModel must not be null")
    private String faceModel;

    /**
     * 1服务识别用的端特征值，2Android终端识别用的特征值
     */
    private Integer modelType;
}