package com.moredian.magicube.device.dto.migration;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 待迁移设备列表
 * @author: wbf
 * @date: 2024/8/12 上午10:25
 */
@Data
public class MigrationDeviceList implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 设备sn
     */
    private String deviceSn;
    /**
     * 设备类型
     */
    private Integer deviceType;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备位置
     */
    private String position;
    /**
     * 是否在线
     */
    private Boolean online;
}
