package com.moredian.magicube.device.dto.activate;

import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import lombok.Data;

import java.io.Serializable;

/**
 * 激活设备信息
 *
 * <AUTHOR>
 */

@Data
public class ActivateDeviceDTO implements Serializable {

    private static final long serialVersionUID = -8804959664017432847L;


    /************************************设备激活公共参数*****************************************/

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备激活类型
     *
     * @see DeviceActivateEngineEnums
     */
    private Integer deviceActivateType;

    /**
     * cpu
     */
    private String cpuId;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 软件版本号
     */
    private Integer version;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 识别服务：默认为云眼 null 或其他不包含1  慧眼:1
     */
    private Integer regCoreType;

    /**
     * 人脸识别算法
     */
    private Integer regArithmetic;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 是否需要离线密码
     */
    private Boolean isNeedOfflinePassword;

    /************************************二维码激活参数*****************************************/

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 钉钉的corpId
     */
    private String corpId;

    /**
     * 第三方设备Id
     */
    private String thirdDeviceId;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 激活管理员的dingId
     */
    private String userId;

    /************************************反扫激活参数*****************************************/

    /**
     * 激活码id
     */
    private Long qrCodeId;

    /**
     * 位置
     */
    private String position;

    /**
     * jetLinks侧的产品id
     */
    private String jetLinkProductId;

    private Integer deviceFlag;
}