package com.moredian.magicube.device.dto.type;

import java.io.Serializable;
import lombok.Data;

/**
 * 新增设备类型和属性关系
 *
 * <AUTHOR>
 */
@Data
public class InsertDeviceTypePropertyRelationDTO implements Serializable {

    private static final long serialVersionUID = -1184783187182281950L;

    /**
     * 设备类型属性Id
     */
    private Long deviceTypePropertyId;

    /**
     *  设备类型
     */
    private Integer deviceType;
}
