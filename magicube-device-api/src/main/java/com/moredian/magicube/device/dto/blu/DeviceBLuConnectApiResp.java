package com.moredian.magicube.device.dto.blu;

import java.io.Serializable;

public class DeviceBLuConnectApiResp implements Serializable {

    private static final long serialVersionUID = 3960332750447416245L;
    private Auth auth;
    /**
     * 请求新加密串
     */
    private String req;
    /**
     * 设备sn
     */
    private String deviceSn;

    public static class Auth {
        /**
         * "时间戳（秒）"
         */
        private Long ts;
        /**
         * 随机数
         */
        private Long nounce;
        /**
         * 新签名
         */
        private String signature;

        public Long getTs() {
            return ts;
        }

        public void setTs(Long ts) {
            this.ts = ts;
        }

        public Long getNounce() {
            return nounce;
        }

        public void setNounce(Long nounce) {
            this.nounce = nounce;
        }

        public String getSignature() {
            return signature;
        }

        public void setSignature(String signature) {
            this.signature = signature;
        }
    }

    public Auth getAuth() {
        return auth;
    }

    public void setAuth(Auth auth) {
        this.auth = auth;
    }

    public String getReq() {
        return req;
    }

    public void setReq(String req) {
        this.req = req;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
}
