package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.notice.NoticeRecordDTO;
import com.moredian.magicube.device.dto.notice.StatisticsModelDTO;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
public interface DeviceNoticeService {

    /**
     * 新增工作通知记录
     *
     * @param noticeRecord
     */
    ServiceResponse<Long> addRecord(NoticeRecordDTO noticeRecord);

    /**
     * 统计工作通知记录数
     *
     * @param statisticsModel
     * @return
     */
    ServiceResponse<Integer> countRecords(StatisticsModelDTO statisticsModel);

    /**
     * 查询最近一次离线时间
     *
     * @param orgId
     * @param deviceSn
     * @param noticeEvent
     * @return
     */
    ServiceResponse<Long> getLastOfflineTime(Long orgId, String deviceSn, Integer noticeEvent);
}
