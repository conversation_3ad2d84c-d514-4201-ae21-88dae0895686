package com.moredian.magicube.device.dto.device;

import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/10/11
 */
@Getter
@Setter
@ToString
public class QueryDeviceWithVersionDTO implements Serializable {

    private static final long serialVersionUID = -7016792312210322462L;

    private Long orgId;

    private List<Integer> deviceTypeList;

    private Boolean online;

    /**
     * 设备名称或sn
     */
    private String keywords;
}
