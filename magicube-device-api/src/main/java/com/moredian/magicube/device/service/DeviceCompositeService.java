package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.composite.*;

import java.util.List;

/**
 * 设备分组服务
 *
 * <AUTHOR>
 */

public interface DeviceCompositeService {

    /**
     * 新增或更新设备分组
     *
     * @param dto 设备分组信息
     * @return
     */
    ServiceResponse<DeviceCompositeDTO> insertOrUpdate(InsertDeviceCompositeDTO dto);

    /**
     * 创建组，非互斥
     * 全量接口，每次加入设备之前会清空当前组下面的老设备（适用于更新组下面的设备场景）
     * ⭐完成改造  （支持挂载某个设备组下）
     */
    ServiceResponse<Long> insertOrUpdateNoMutex(InsertDeviceCompositeDTO dto);

    /**
     * 添加设备到设备组，增量添加，如果存在了会报错存在
     * ⭐新增接口
     *
     * @param dto DTO
     * @return
     */
    ServiceResponse<Boolean> addDeviceInc(DeviceBindCompositeDTO dto);

    /**
     * 查询机构所有的分组信息，包括组下的设备信息
     *
     * @param orgId   机构号
     * @param bizType 业务类型
     * @return
     */
    ServiceResponse<List<DeviceCompositeDTO>> listByOrgIdAndBizType(Long orgId, Integer bizType);

    /**
     * 删除设备分组
     *
     * @param dto 删除的分组信息
     * @return
     */
    ServiceResponse<Boolean> delete(DeleteDeviceCompositeDTO dto);

    /**
     * 获取已经绑定的设备Id列表
     *
     * @param orgId   机构号
     * @param bizType 业务类型
     * @return
     */
    ServiceResponse<List<Long>> listBindDeviceIdByOrgIdAndBizType(Long orgId, Integer bizType);

    /**
     * 设备解绑删除
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @param bizType  业务类型
     * @return
     */
    ServiceResponse<Boolean> deleteCompositeItemByDeviceId(Long orgId, Long deviceId, Integer bizType);

    /**
     * 查询设备所属设备组名称
     *
     * @param orgId
     * @param deviceIdList
     * @param bizType
     * @param compositeIds 有权限的组ID列表
     * @return
     */
    ServiceResponse<List<DeviceCompositeNameListDTO>> findDeviceCompositeNameList(Long orgId,
        List<Long> deviceIdList, Integer bizType, List<Long> compositeIds);

    /**
     * 开启管理员，子管理员查询根据自己的设备查询分组信息
     *
     * @param dto 查询条件
     * @return
     */
    ServiceResponse<CompositeAndDeviceInfoDTO> listByCondition(QueryDeviceCompositeDTO dto);


    /**
     * 获取设备组列表以及下面的设备台数
     *
     * @param orgId   机构号
     * @param bizType 业务类型
     * @return
     */
    ServiceResponse<List<DeviceCompositeDTO>> listDetailByOrgIdAndBizType(Long orgId, Integer bizType);

    /**
     * 获取组下面的设备列表
     *
     * @param orgId             机构号
     * @param deviceCompositeId 设备分组Id
     * @return
     */
    ServiceResponse<List<DeviceCompositeItemDTO>> listCompositeItemByCompositeId(Long orgId, Long deviceCompositeId);

    /**
     * 获取组信息以及下面的设备信息
     *
     * @param orgId             机构号
     * @param deviceCompositeId 设备分组Id
     * @return
     */
    ServiceResponse<DeviceCompositeDTO> getDetailByOrgIdAndId(Long orgId, Long deviceCompositeId);

    /**
     * 根据设备组ID批量查询设备组实体
     * ⭐新增接口
     *
     * @param orgId        组织 ID
     * @param compositeIds 复合 ID
     * @param bizType      业务类型
     * @return {@link ServiceResponse }<{@link List }<{@link DeviceCompositeDTO }>>
     */
    ServiceResponse<List<DeviceCompositeDTO>> listByDeviceCompositeIds(Long orgId,
        List<Long> compositeIds, Integer bizType);

    /**
     * 根据code获取设备组名字
     * ⭐新增接口
     *
     * @param orgId    组织 ID
     * @param codeList 代码列表
     * @param bizType  业务类型
     * @return {@link ServiceResponse }<{@link List }<{@link DeviceCompositeDTO }>>
     */
    ServiceResponse<List<DeviceCompositeDTO>> getCompositeNameByCode(Long orgId,List<String> codeList,Integer bizType);

    /**
     * 获取这批设备的设备组路径列表
     * ⭐新增接口
     *
     * @param dto 请求
     * @return {@link ServiceResponse }<{@link DeviceCompositePathInfoDTO }>
     */
    ServiceResponse<DeviceCompositePathInfoDTO> getDeviceInCompositePathList(
        QueryDeviceCompositePathDTO dto);

    /**
     * 获取多个设备组的树结构，如果在一棵树上会合并成一颗树
     * （会深入到组的子节点）
     * ⭐新增接口
     *
     * @param orgId        组织 ID
     * @param bizType      业务类型
     * @param compositeIds 复合 ID
     * @return {@link ServiceResponse }<{@link List }<{@link DeviceCompositeAndDeviceCountDTO }>>
     */
    ServiceResponse<List<DeviceCompositeAndDeviceCountDTO>> getTreeAndDeviceByIds(
        Long orgId,
        Integer bizType, List<Long> compositeIds);

    /**
     * 根据设备组ID批量查询组下设备列表
     * @param orgId
     * @param compositeIds
     * @return
     */
    ServiceResponse<List<Long>> listDeviceIdByCompositeIds(Long orgId, List<Long> compositeIds);

    /**
     * 根据parentId查询设备组Id列表
     * @param orgId 机构Id
     * @param parentId 父设备组Id
     */
    ServiceResponse<List<Long>> listCompositeIdsByParentId(Long orgId, Long parentId);

    /**
     * 查出当前设备组的所有子组信息，根据code+path高效查询
     * 根据code+path查出
     * ⭐新增接口
     *
     * @param orgId             组织 ID
     * @param deviceCompositeId 设备复合 ID
     * @return {@link List }
     */
    ServiceResponse<List<Long>> findAllSubDeviceComposite(Long orgId, Long deviceCompositeId);

    /**
     * 获取设备组以及设备组下面的设备数量(无树结构)
     * ⭐新增接口
     *
     * @param orgId        组织 ID
     * @param bizType      业务类型
     * @param compositeIds 复合 ID
     * @return {@link ServiceResponse }<{@link List }<{@link CompositeDeepDeviceSizeDTO }>>
     */
    ServiceResponse<List<CompositeDeepDeviceSizeDTO>> getCompositeDeepDeviceSize(Long orgId,
        Integer bizType, List<Long> compositeIds);

    /**
     * 根据多个设备组id找出设备组以及子组下面涉及的设备ids
     * ⭐新增接口
     *
     * @param orgId             组织 ID
     * @param deviceCompositeIds 设备组 IDs
     * @param sceneType         场景类型
     * @return {@link ServiceResponse }<{@link List }<{@link Long }>>
     */
    ServiceResponse<List<Long>> getDeviceIdsByCompositeDeepBatch(Long orgId, List<Long> deviceCompositeIds, Integer sceneType);

    /**
     * 根据多个设备组id找出设备组以及子组下的设备组ids
     * @param orgId
     * @param deviceCompositeIds
     * @return
     */
    ServiceResponse<List<Long>> findAllSubDeviceCompositeIdBatch(Long orgId, List<Long> deviceCompositeIds);
}
