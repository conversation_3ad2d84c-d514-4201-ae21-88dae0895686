package com.moredian.magicube.device.enums;

/**
 * <AUTHOR>
 * @date : 2024/4/25
 */
public enum DeviceSourceEnum {
    MD(1,"魔点设备"),
    THIRD(2,"三方设备")
    ;

    private Integer code;
    private String msg;

    DeviceSourceEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (DeviceSourceEnum deviceSourceEnum : DeviceSourceEnum.values()) {
            if (deviceSourceEnum.getCode().equals(code)) {
                return deviceSourceEnum.getMsg();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
