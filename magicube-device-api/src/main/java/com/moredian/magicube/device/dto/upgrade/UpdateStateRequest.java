/**
 * 
 */
package com.moredian.magicube.device.dto.upgrade;

import java.io.Serializable;

public class UpdateStateRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8948784478252801176L;

	private String serialNumber;
	
	private int systemType;
	
	private int appType;

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public int getSystemType() {
		return systemType;
	}

	public void setSystemType(int systemType) {
		this.systemType = systemType;
	}

	public int getAppType() {
		return appType;
	}

	public void setAppType(int appType) {
		this.appType = appType;
	}
	
}
