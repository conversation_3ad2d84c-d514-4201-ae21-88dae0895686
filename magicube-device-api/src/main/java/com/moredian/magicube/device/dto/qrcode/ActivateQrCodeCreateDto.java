package com.moredian.magicube.device.dto.qrcode;

import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import com.moredian.magicube.device.enums.PlatformEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * @Classname： ActivateQrCodeCreateDto
 * @Date: 2023/1/4 11:44 上午
 * @Author: _AF
 * @Description: 创建激活二维码对象
 */
@Data
public class ActivateQrCodeCreateDto implements Serializable {

    private static final long serialVersionUID = -4448106425873133361L;
    private Long orgId;

    private Long memberId;
    /**
     * 二维码名称
     */
    private String qrCodeName;

    private String deviceAddressName;
    /**
     * 1-有线，默认
     * 2-无线
     *
     * @see ActivateQrCodeConstant.NetworkTypeEnum
     */
    private Integer networkType = 1;
    /**
     * 1-DHCP
     * 2-静态，默认
     *
     * @see ActivateQrCodeConstant.ConnectTypeEnum
     */
    private Integer connectType = 2;
    /**
     * 私有云，默认关
     *
     * @see ActivateQrCodeConstant.PrivateCloudSwitchEnum
     */
    private Integer privateCloudSwitch = 0;


    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;
    /**
     * wifi配置
     */
    private WifiInfo wifiInfo;
    /**
     * 私有云配置
     */
    private PrivateCloud privateCloud;
    /**
     * 安装师傅
     *
     * @see ActivateQrCodeConstant.RoleTypeEnum
     */
    private Integer roleType;

    /**
     * 类别
     *
     * @see ActivateQrCodeConstant.FunctionTypeEnum
     */
    private Integer functionType = 1;

    /**
     * 来源
     *
     * @see ActivateQrCodeConstant.AppletEnum
     */
    private Integer source = 1;

    /**
     * 空间id
     */
    private Long treeId;

    /**
     * 机构网络Id
     */
    private Long orgNetworkId;

    /**
     * 出入方向 1入口 2出口
     */
    private Integer direction;

    /**
     * 空间属性id
     */
    private Long propertyTreeNodeId;

    /**
     * 激活平台
     *  @see PlatformEnum
     */
    private Integer platformCode;
}
