package com.moredian.magicube.device.dto.device;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/20
 */
@Getter
@Setter
@ToString
public class PageAppTypeRedirectDTO implements Serializable {

    private static final long serialVersionUID = 2491541778261809248L;

    /**
     * 主键
     */
    private Long id;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 跳转链接
     */
    private String redirectUrl;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页数
     */
    private Integer pageSize;
}
