package com.moredian.magicube.device.dto.carme;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description: 区域人员数量统计
 * @Author: fangj
 * @Date: 2024-03-05
 */
@Setter
@Getter
@ToString
public class PeopleNumberDTO implements Serializable {

    private static final long serialVersionUID = -6628562881168598170L;

    /**
     * id
     */
    private Long id;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 空间ID
     */
    private Long treeId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备sn号
     */
    private String deviceSn;

    /**
     * 区域内人数
     */
    private Integer insidePeopleNum;
}
