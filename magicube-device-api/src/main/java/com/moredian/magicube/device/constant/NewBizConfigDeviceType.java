package com.moredian.magicube.device.constant;

import com.moredian.magicube.common.enums.DeviceType;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/4/25 9:59 上午
 * 新设备配置初始化
 */
public class NewBizConfigDeviceType {

    private static final long serialVersionUID = -6046479728637693752L;

    public static final Set<Integer> deviceTypeSet = new HashSet<>();

    static {
        deviceTypeSet.add(DeviceType.BOARD_ELEVATOR_D2.getValue());
        deviceTypeSet.add(DeviceType.BOARD_ATTENDANCE_DUALEYE2_MINI.getValue());
        deviceTypeSet.add(DeviceType.BOARD_D2C.getValue());
        deviceTypeSet.add(DeviceType.BOARD_D2plus.getValue());
        deviceTypeSet.add(DeviceType.BOARD_Y2A.getValue());
        deviceTypeSet.add(DeviceType.BOARD_Y2.getValue());
        deviceTypeSet.add(DeviceType.BOARD_Y2B.getValue());
        deviceTypeSet.add(DeviceType.BOARD_Y3.getValue());
        deviceTypeSet.add(DeviceType.BOARD_Y3s.getValue());
        deviceTypeSet.add(DeviceType.BOARD_MY3.getValue());
        deviceTypeSet.add(DeviceType.BOARD_MY3A.getValue());
        deviceTypeSet.add(DeviceType.BOARD_S5.getValue());
        deviceTypeSet.add(DeviceType.BOARD_MX1A.getValue());
        deviceTypeSet.add(DeviceType.BOARD_ATTENDANCE_DUALEYE2.getValue());
        deviceTypeSet.add(DeviceType.BOARD_D3.getValue());
        deviceTypeSet.add(DeviceType.BOARD_M3Mini.getValue());
        deviceTypeSet.add(DeviceType.BOARD_M3Pro.getValue());
        deviceTypeSet.add(DeviceType.BOARD_M3Max.getValue());
        deviceTypeSet.add(DeviceType.BOARD_MG3.getValue());
        deviceTypeSet.add(DeviceType.BOARD_ELEVATOR_D2C.getValue());
        deviceTypeSet.add(DeviceType.BOARD_X1.getValue());
        deviceTypeSet.add(DeviceType.BOARD_W4.getValue());
        deviceTypeSet.add(DeviceType.BOARD_MOS745S.getValue());
    }
}
