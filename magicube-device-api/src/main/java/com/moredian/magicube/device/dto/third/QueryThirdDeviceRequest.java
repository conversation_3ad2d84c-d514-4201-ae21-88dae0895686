package com.moredian.magicube.device.dto.third;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description
 * @author: xiesj
 * @date 2022/12/29 16:15
 */
@Getter
@Setter
@ToString
public class QueryThirdDeviceRequest implements Serializable {

    private static final long serialVersionUID = -350660292526156860L;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备SN
     */
    private String deviceSn;
}
