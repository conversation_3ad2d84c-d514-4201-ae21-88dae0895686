package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.type.DeviceCapacityDTO;
import com.moredian.magicube.device.dto.type.DeviceSnTypeMappingDTO;
import com.moredian.magicube.device.dto.type.SaveDeviceCapacityDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-18
 */
public interface DeviceTypeMapService {

    /**
     * 获取map值
     *
     * @param mapName
     * @param mapKey
     * @return
     */
    ServiceResponse<String> getValue(String mapName, String mapKey);

    /**
     * 获取设备容量
     *
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<Integer> getDeviceCapacity(Integer deviceType);

    /**
     * 获取多个设备的容量
     *
     * @param deviceTypeList 设备类型
     * @return
     */
    ServiceResponse<List<DeviceCapacityDTO>> getDeviceListCapacity(List<Integer> deviceTypeList);

    /**
     * 修改设备容量
     *
     * @param dto 设备类型及人脸容量
     * @return
     */
    ServiceResponse<Boolean> saveDeviceCapacity(SaveDeviceCapacityDTO dto);

    /**
     * 获取设备容量
     *
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<String> getDefaultSpu(Integer deviceType);

    /**
     * 获取设备转换名
     *
     * @param deviceType 设备类型
     * @return
     */
    ServiceResponse<String> getConvertName(Integer deviceType);

    /**
     * 获取设备转换名
     *
     * @param deviceSn 设备SN
     * @return
     */
    ServiceResponse<Integer> getTypeByDeviceSn(String deviceSn);

    /**
     * 获取设备转换名
     *
     * @param deviceSnList 设备SN列表
     * @return
     */
    ServiceResponse<List<DeviceSnTypeMappingDTO>> getTypeByDeviceSnList(List<String> deviceSnList);

}
