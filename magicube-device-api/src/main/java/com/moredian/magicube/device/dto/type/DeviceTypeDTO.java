package com.moredian.magicube.device.dto.type;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备类型
 *
 * <AUTHOR>
 */

@Data
public class DeviceTypeDTO implements Serializable {

    private static final long serialVersionUID = -6263075707598708806L;

    /**
     * 设备类型Id
     */
    private Long deviceTypeId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备类型描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 设备类型-设备类型名称
     */
    private String deviceTypeAndName;

    /**
     * 设备类型图片
     */
    private String deviceTypePic;

    /**
     * 产品型号（子型号）编码
     */
    private String productModelCode;

    /**
     * 产品型号（子型号）编码
     */
    private String productModelName;

    /**
     * 产品系列编码
     */
    private String modelSerialCode;

    /**
     * 产品系列编码-产品系列名称
     */
    private String modelSerialCodeAndName;

    /**
     * 设备类型内部名称
     */
    private String deviceTypeInnerName;

    /**
     * 元石行业设备类型名称
     */
    private String deviceTypeNameHy;

    /**
     * 钉钉分销设备类型名称
     */
    private String deviceTypeNameDing;

    /**
     * 魔蓝分销设备类型名称
     */
    private String deviceTypeNameMl;

    /**
     * 元石行业设备类型展示名称
     */
    private String deviceTypeDisplayNameHy;

    /**
     * 钉钉分销设备类型展示名称
     */
    private String deviceTypeDisplayNameDing;

    /**
     * 魔蓝分销设备类型展示名称
     */
    private String deviceTypeDisplayNameMl;

    /**
     * 设备类型拓展配置
     */
    private String config;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;
}