/**
 * 
 */
package com.moredian.magicube.device.service;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.upgrade.MergeUpdateAppToLastResponse;
import com.moredian.magicube.device.dto.upgrade.MergeUpgradeDeviceRequest;
import com.moredian.magicube.device.dto.upgrade.MergeUpgradeDeviceStatusRequest;
import com.moredian.magicube.device.dto.upgrade.MergeUpgradeDeviceStatusResponse;
import com.moredian.magicube.device.dto.upgrade.UpdateAppToLastRequest;
import com.moredian.magicube.device.dto.upgrade.UpdateStateRequest;
import com.moredian.magicube.device.dto.upgrade.UpdateStateResponse;

/**
 * 设备升级服务
 * 
 * <AUTHOR> 2018年5月17日
 */
public interface DeviceUpgradeService {

	/**
	 * <pre>
	 * 升级设备上的app到最新版本。暂不支持跨级升级。比如：设备 门禁app 当前版本为3，最新版本为5不支持；
	 * 目前主要还有以下两方面没有考虑和支持到：
	 * 1、跨级升级可能会存在缺少必须的升级步骤而导致app出现异常
	 * 2、跨多版本升级时的交互协议还没来得及和终端确认
	 * </pre>
	 * @param request
	 * @return
	 */
	 ServiceResponse<Boolean> updateAppToLast(UpdateAppToLastRequest request) ;
	
	 ServiceResponse<UpdateStateResponse> updateState(UpdateStateRequest request);

	 ServiceResponse<Boolean> mergeUpdateAppToLast(
		MergeUpgradeDeviceRequest request) ;

	ServiceResponse<MergeUpdateAppToLastResponse> mergeUpdateAppToLastReturn(MergeUpgradeDeviceRequest request) ;
	
	 ServiceResponse<MergeUpgradeDeviceStatusResponse> mergeUpdateState(
		MergeUpgradeDeviceStatusRequest request);
}
