package com.moredian.magicube.device.dto.device;

import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/1/24
 */
@Getter
@Setter
@ToString
public class ReportDeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = -944242945213649598L;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 电量
     */
    private Integer electric;

    /**
     * 本地时间
     */
    private Long localTime;

    /**
     * -----------------------------版本信息---------------------------
     * <p>
     * /**
     * 全局app类型
     */
    private Integer appType;

    /**
     * 全局app版本
     */
    private Integer appVersion;

    /**
     * 全局rom类型
     */
    private Integer romType;

    /**
     * 全局rom版本
     */
    private Integer romVersion;

    /**
     * 锁板3861L版本
     */
    private Integer lockBoard3861LVersion;

    /**
     * 锁板804版本
     */
    private Integer lockBoard804Version;

    /**
     * 锁板805版本
     */
    private Integer lockBoard805Version;

    /**
     * 模型版本
     */
    private Integer modelVersion;

    /**
     * 锁版本
     */
    private Integer lockVersion;

    /**
     * --------------------------网络信息---------------------------
     * <p>
     * /**
     * 网络类型 1-有线 2-无线
     */
    private Integer networkType;

    /**
     * 连接类型 1-DHCP 2-静态地址
     */
    private Integer connectType;

    /**
     * 静态ip配置
     */
    private CableStatic cableStatic;

    /**
     * wifi配置
     */
    private WifiInfoDTO wifiInfo;

    /**
     * 有线mac地址
     */
    private String wiredMac;

    /**
     * 无线mac地址
     */
    private String wifiMac;

    /**
     * 设备重新配网状态 1-网络切换中 2-网络切换成功 3-网络切换失败
     */
    private Integer deviceResetNetworkStatus;

    /**
     * 0-定时上报，1-收到服务端通知后上报
     */
    private Integer reportType;

    /**
     * 蓝牙mac地址
     */
    private String bluetoothMac;
}
