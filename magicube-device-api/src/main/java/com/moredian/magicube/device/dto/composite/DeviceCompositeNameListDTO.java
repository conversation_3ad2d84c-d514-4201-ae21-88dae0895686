package com.moredian.magicube.device.dto.composite;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/6/17
 */
@Data
@NoArgsConstructor
public class DeviceCompositeNameListDTO implements Serializable {

    private static final long serialVersionUID = 5918396898375651060L;

    private Long orgId;

    private Long deviceId;

    private List<DeviceCompositeDTO> compositeNameList;
}
