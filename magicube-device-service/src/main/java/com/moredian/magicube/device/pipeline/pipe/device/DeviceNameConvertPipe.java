package com.moredian.magicube.device.pipeline.pipe.device;


import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.hephaestus.request.relation.GetRelationBySerialNumberRequest;
import com.moredian.hephaestus.response.relation.SimpleSpuInventoryRelationResponse;
import com.moredian.hephaestus.service.SpuInventoryRelationService;
import com.moredian.magicube.common.enums.VerifyChannel;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.manager.DeviceTypeMapManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 设备名称转换管道(只针对魔点需要转换名称的设备)
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceNameConvertPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    private static final Integer DEVICE_SN_MIN_LENGTH = 6;

    private static final String SEPARATOR = "-";

    private static final String REGEX_CHINESE = "[\u4e00-\u9fa5]";

    @SI
    private SpuInventoryRelationService spuInventoryRelationService;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return StringUtils.isBlank(dto.getDeviceName());
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        if (context.getVerifyChannel() != null && VerifyChannel.THIRDPART.getValue() != context.getVerifyChannel()
            && deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_CONVERT_LIST, dto.getDeviceType())) {
            if (dto.getDeviceSn().length() >= DEVICE_SN_MIN_LENGTH) {
                String sn = dto.getDeviceSn();
                //先查询SPU服务，如果存在SN对应的商品名称，则采用，否则采用枚举中定义的设备类型
                String deviceNamePrefix = "";
                GetRelationBySerialNumberRequest getRelationBySerialNumberRequest = new GetRelationBySerialNumberRequest();
                getRelationBySerialNumberRequest.setSerialNumberList(Arrays.asList(sn));
                ServiceResponse<List<SimpleSpuInventoryRelationResponse>> spuRelationServiceResp = spuInventoryRelationService.findSpuInventoryRelationBySerialNumberList(getRelationBySerialNumberRequest);
                if (spuRelationServiceResp.isSuccess() && spuRelationServiceResp.isExistData() && CollectionUtils.isNotEmpty(spuRelationServiceResp.getData())) {
                    SimpleSpuInventoryRelationResponse spuRelation = spuRelationServiceResp.getData().get(0);
                    if (spuRelation != null) {
                        deviceNamePrefix = spuRelation.getSpuDisplayName();
                    }
                }
                String suffixName = sn.substring(sn.length() - DEVICE_SN_MIN_LENGTH);
                if (StringUtils.isBlank(deviceNamePrefix)) {
                    String deviceConvertName = deviceTypeMapManager.getConvertName(dto.getDeviceType());
                    //将转换后的名字去除中文
                    deviceNamePrefix = deviceConvertName.replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                }
                if (StringUtils.isBlank(deviceNamePrefix) && deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_SPU_LIST, dto.getDeviceType())) {
                    deviceNamePrefix = deviceTypeMapManager.getDefaultSpu(dto.getDeviceType());
                }
                dto.setDeviceName(deviceNamePrefix + SEPARATOR + suffixName);
            } else {
                String deviceConvertName = deviceTypeMapManager.getConvertName(dto.getDeviceType());
                //将转换后的名字去除中文
                String deviceName = deviceConvertName.replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                if (StringUtils.isBlank(deviceName) && deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_SPU_LIST, dto.getDeviceType())) {
                    deviceName = deviceTypeMapManager.getDefaultSpu(dto.getDeviceType());
                }
                dto.setDeviceName(deviceName);
            }
        }
    }
}
