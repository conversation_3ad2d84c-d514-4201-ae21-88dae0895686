package com.moredian.magicube.device.helper;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import com.moredian.magicube.device.dao.entity.DeviceVersion;
import com.moredian.magicube.device.dao.mapper.DeviceApkVersionMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceRelateAppFixedBIzTypeEnum;
import com.moredian.magicube.device.manager.DeviceAppRelationConfigManager;
import com.moredian.magicube.device.manager.DeviceAppRelationFixedConfigManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceTreeService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *  设备应用关系helper类
 */

@Slf4j
@Component
public class DeviceAppRelationHandler {

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceApkVersionMapper deviceApkVersionMapper;

    @Autowired
    private DeviceAppRelationFixedConfigManager deviceAppRelationFixedConfigManager;

    @Autowired
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;

    @SI
    private SpaceTreeService spaceTreeService;

    @SI
    private DeviceService deviceService;

    public DeviceAppRelationConfig getDeviceAppCode(Long orgId, String deviceSn,
        Integer spaceType) {
        if (deviceRelateFixedApp(orgId, deviceSn)) {
            return null;
        }

        //查询设备appType和versionCode
        DeviceVersion deviceVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(deviceSn);
        if (deviceVersion == null) {
            log.info("设备应用关系helper,设备版本信息为空,不做关联应用处理，deviceSn:{},timestamp:{}",
                deviceSn, System.currentTimeMillis());
            return null;
        }
        Integer appType = deviceVersion.getAppType();
        Integer versionCode = deviceVersion.getVersionCode();
        if (spaceType == null){
            DeviceInfoDTO deviceInfoDTO = deviceService.getByOrgAndDeviceSn(orgId, deviceSn)
                .pickDataThrowException();
            if (deviceInfoDTO == null) {
                log.info("设备应用关系helper,设备关联空间信息为空,不做关联应用处理，deviceSn:{},timestamp:{}"
                    , deviceSn, System.currentTimeMillis());
                return null;
            }
            if (deviceInfoDTO.getTreeId() == null) {
                spaceType = -1;
            } else {
                //查询空间类型
                TreeDTO treeDTO = spaceTreeService.getById(deviceInfoDTO.getTreeId(), orgId)
                    .pickDataThrowException();
                if (treeDTO != null) {
                    spaceType = treeDTO.getTags().get(0).getSpaceType();
                }
            }
        }
        DeviceAppRelationConfig deviceAppRelationConfig = getDeviceAppRelationConfig(spaceType, appType, versionCode);
        if (deviceAppRelationConfig == null) {
            log.info("设备应用关系helper,设备激活更新设备关联应用配置不存在,orgId:{},spaceType:{},appType:{},versionCode:{}", orgId, spaceType, appType, versionCode);
            return null;
        }
        return deviceAppRelationConfig;
    }

    /**
     * 设备关联固定配置应用
     * @param orgId 机构id
     * @param deviceSn 设备sn
     * @return 是否存在固定配置
     */
    public boolean deviceRelateFixedApp(Long orgId, String deviceSn) {
        //先判断固定配置是否存在 如果存在就直接设置固定配置的appCode即可
        DeviceAppRelationFixedConfig fixedConfig = deviceAppRelationFixedConfigManager.getByBizTypeAndId(
                DeviceRelateAppFixedBIzTypeEnum.DEVICE_SN.getCode(), deviceSn);

        if (fixedConfig != null) {
            Device device = deviceManager.getByDeviceSn(deviceSn);
            if (device != null) {
                //更新设备相关信息
                UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
                updateDeviceDTO.setOrgId(orgId);
                updateDeviceDTO.setDeviceId(device.getDeviceId());
                updateDeviceDTO.setAppCode(fixedConfig.getDefaultAppCode());
                updateDeviceDTO.setAppCodeList(fixedConfig.getAvailableAppCodeList());
                boolean update = deviceManager.update(updateDeviceDTO);
                log.info("设备更新固定关联应用结果:{}", update);
                return true;
            }
        }
        return false;
    }

    /**
     * 获取最新的版本配置
     *
     * @param spaceType   spaceType
     * @param appType     appType
     * @param versionCode versionCode
     * @return DeviceAppRelationConfig
     */
    private DeviceAppRelationConfig getDeviceAppRelationConfig(Integer spaceType, Integer appType, Integer versionCode) {
        if (spaceType == null){
            spaceType = -1;
        }
        List<DeviceAppRelationConfig> deviceAppRelationConfigs = deviceAppRelationConfigManager
                .selectBySpaceTypeAndAppTypeAndVersionCode(spaceType, appType, versionCode);
        if (CollectionUtils.isEmpty(deviceAppRelationConfigs)) {
            log.warn("未找到对应用配置信息,spaceType:{},appType:{},versionCode:{}", spaceType, appType, versionCode);
            return null;
        }
        return deviceAppRelationConfigs.get(0);
    }
}
