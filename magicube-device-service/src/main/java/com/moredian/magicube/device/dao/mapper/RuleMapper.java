package com.moredian.magicube.device.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.moredian.magicube.device.dao.entity.Rule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface RuleMapper extends BaseMapper<Rule> {

    List<Rule> getRuleList(@Param("orgId") Long orgId, @Param("modeType") Integer modeType,
        @Param("spaceType") Integer spaceType, @Param("ruleIdList") List<Long> ruleIdList);

    List<Long> getRuleTemplateIdByOrgId(@Param("orgId") Long orgId);

    int batchInsert(@Param("ruleList") List<Rule> ruleList);

    void updateRule(@Param("orgId") Long orgId, @Param("rule") Rule rule);

    Rule getRule(@Param("orgId") Long orgId, @Param("ruleId") Long ruleId);

    Rule getByRuleId(Long ruleId);
}