package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.qrcode.QrCodeActivateRecord;
import com.moredian.magicube.device.dto.qrcode.QrCodeActivateRecordResponse;
import java.util.List;

/**
 * @Classname： QrCodeActivateRecordManager
 * @Date: 2023/1/12 11:09 上午
 * @Author: _AF
 * @Description:
 */
public interface QrCodeActivateRecordManager {

    /**
     * 插入
     *
     * @param qrCodeActivateRecord
     * @return
     */
    Long insert(QrCodeActivateRecord qrCodeActivateRecord);

    /**
     * 集合查询
     *
     * @param orgId
     * @param qrCodeId
     * @return
     */
    List<QrCodeActivateRecordResponse> listByQrCodeId(Long orgId, Long qrCodeId);

    /**
     * 激活记录
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    QrCodeActivateRecordResponse getByDeviceId(Long orgId, Long deviceId);

    /**
     * 查询机构下最新的反扫激活记录
     *
     * @param orgId
     * @return
     */
    QrCodeActivateRecordResponse getNewestRecordByOrgId(Long orgId);

}
