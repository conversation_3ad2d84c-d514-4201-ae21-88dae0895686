package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.eden.whale.person.service.PersonEventService;
import com.moredian.magicube.core.person.model.SimpleCardInfo;
import com.moredian.magicube.core.person.request.QueryPersonInfoRequest;
import com.moredian.magicube.core.person.response.PersonResponse;
import com.moredian.magicube.core.person.service.PersonService;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceGroup;
import com.moredian.magicube.device.dao.entity.GroupPersonSnapshot;
import com.moredian.magicube.device.dto.person.PagePersonInfoDTO;
import com.moredian.magicube.device.dto.person.PersonInfoDTO;
import com.moredian.magicube.device.dto.person.QueryPersonInfoDTO;
import com.moredian.magicube.device.dto.person.SimpleCardInfoDTO;
import com.moredian.magicube.device.dto.snapshot.DeviceSnapshotDTO;
import com.moredian.magicube.device.dto.snapshot.PaginationDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceGroupManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceSnapshotManager;
import com.moredian.magicube.device.manager.DeviceSyncPersonInfoManager;
import com.moredian.magicube.device.manager.GroupPersonSnapshotManager;
import com.moredian.magicube.device.model.GenerateDeviceSnapshotModel;
import com.moredian.magicube.device.model.PersonIdAndGroupIdSnapshot;
import com.moredian.magicube.device.model.QueryDeviceSnapshotModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/25
 */
@Component
@Slf4j
public class DeviceSyncPersonInfoManagerImpl implements DeviceSyncPersonInfoManager {

    @SI
    private PersonService personService;

    @SI
    private PersonEventService personEventService;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceGroupManager deviceGroupManager;

    @Autowired
    private DeviceSnapshotManager deviceSnapshotManager;

    @Autowired
    private GroupPersonSnapshotManager groupPersonSnapshotManager;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaginationDTO<PersonInfoDTO> pagePersonInfo(PagePersonInfoDTO dto) {
        //由于core中无法调用设备服务，所以有设备服务查询出来后透传到magicube-core
        BizAssert.notNull(dto, "request must not be null");
        BizAssert.notBlank(dto.getDS(), "deviceSn must not be blank");
        Device exist = deviceManager.getByDeviceSn(dto.getDS());
        BizAssert.notNull(exist, "device not exist");
        //查询设备绑定的组
        List<Long> deviceIdList = new ArrayList<>(1);
        deviceIdList.add(exist.getDeviceId());
        // TODO兼容多租户：第一步，查看设备绑定了哪些组
        List<DeviceGroup> deviceGroupList = deviceGroupManager.listByOrgIdAndDeviceIds(dto.getOI(), deviceIdList);
        PaginationDTO<PersonInfoDTO> personInfoDTOPagination = new PaginationDTO<>();
        personInfoDTOPagination.setPageNo(dto.getPageNo());
        personInfoDTOPagination.setPageSize(dto.getPageSize());
        if (CollectionUtils.isEmpty(deviceGroupList)) {
            log.info("设备[{}]未绑定组，本次pagePersonInfo查询不返回数据", dto.getDI());
            personInfoDTOPagination.setData(Collections.emptyList());
            personInfoDTOPagination.setTotalCount(0);
            return personInfoDTOPagination;
        }
        List<Long> bondGroupIdList = deviceGroupList.stream().map(DeviceGroup::getGroupId).collect(Collectors.toList());
        //查询是否查询首页
        if (FIRST_PAGE.equals(dto.getPageNo())) {
            // TODO兼容多租户：第二步，查看是否存在设备快照
            //查询是否存在设备快照
            QueryDeviceSnapshotModel queryDeviceSnapshotModel = new QueryDeviceSnapshotModel();
            queryDeviceSnapshotModel.setOrgId(dto.getOI());
            queryDeviceSnapshotModel.setDeviceId(dto.getDI());
            DeviceSnapshotDTO deviceSnapshot = deviceSnapshotManager.getDeviceSnapshot(queryDeviceSnapshotModel);
            if (deviceSnapshot != null) {
                //删除老快照
                deviceSnapshotManager.deleteById(deviceSnapshot.getId());
            }
            // TODO兼容多租户：第三步，生成设备快照，core里有根据组id查出属于哪个机构id的接口
            //重新生成快照
            generateSnapshot(dto, bondGroupIdList);
        }
        //查询快照
        QueryDeviceSnapshotModel queryDeviceSnapshotModel = new QueryDeviceSnapshotModel();
        queryDeviceSnapshotModel.setOrgId(dto.getOI());
        queryDeviceSnapshotModel.setDeviceId(dto.getDI());
        DeviceSnapshotDTO deviceSnapshot = deviceSnapshotManager.getDeviceSnapshot(queryDeviceSnapshotModel);
        BizAssert.notNull(deviceSnapshot, DeviceErrorCode.DEVICE_SNAPSHOT_NOT_EXIST, DeviceErrorCode.DEVICE_SNAPSHOT_NOT_EXIST.getMessage());
        // TODO兼容多租户：第四步，获取快照权限组的总人数
        int distinctPersonSize = groupPersonSnapshotManager.getDistinctPersonSize(dto.getOI(), deviceSnapshot.getId(), dto.getGL());
        personInfoDTOPagination.setTotalCount(distinctPersonSize);
        if (distinctPersonSize == 0) {
            personInfoDTOPagination.setData(Collections.emptyList());
        } else {
            // TODO兼容多租户：第五步，分页去重查询快照组内人员
            List<GroupPersonSnapshot> groupPersonSnapshotList = groupPersonSnapshotManager
                    .pageGroupDistinctPerson(dto.getOI(), deviceSnapshot.getId(), dto.getGL(), dto.getPageNo(), dto.getPageSize());
            if (CollectionUtils.isNotEmpty(groupPersonSnapshotList)) {
                List<Long> personIdList = groupPersonSnapshotList.stream().map(GroupPersonSnapshot::getPersonId).collect(Collectors.toList());
                //查询组信息
                Map<Long/*personId*/, List<Long>/*groupIdList*/> personIdToGroupIdListMap = new HashMap<>();
                if (dto.getNG()) {
                    // TODO兼容多租户：第六步，根据personId查快照组
                    List<PersonIdAndGroupIdSnapshot> personIdAndGroupIdList = groupPersonSnapshotManager
                            .findGroupIdByPersonIds(dto.getOI(), deviceSnapshot.getId(), bondGroupIdList, personIdList);
                    personIdToGroupIdListMap = personIdAndGroupIdList.stream().collect(Collectors.groupingBy(PersonIdAndGroupIdSnapshot::getPersonId,
                            Collectors.mapping(PersonIdAndGroupIdSnapshot::getGroupId, Collectors.toList())));

                }
                // TODO兼容多租户：第七步，查询卡及特征值，机构id人员分组（core可支持）
                //查询卡及特征值
                List<PersonResponse> personResponseList = queryPersonCardListAndFeature(dto, bondGroupIdList, personIdList);
                //组合数据
                for (PersonResponse personResponse : personResponseList) {
                    if (dto.getNG() && personIdToGroupIdListMap.containsKey(personResponse.getPersonId())) {
                        personResponse.setGroupList(personIdToGroupIdListMap.get(personResponse.getPersonId()));
                    }
                }
                personInfoDTOPagination.setData(convertToPersonInfoDTOList(personResponseList));
                personInfoDTOPagination.setTs(deviceSnapshot.getTimestamp());
            } else {
                personInfoDTOPagination.setTotalCount(0);
                personInfoDTOPagination.setData(Collections.emptyList());
            }
        }
        return personInfoDTOPagination;
    }

    @Override
    public List<PersonInfoDTO> queryPersonInfo(QueryPersonInfoDTO dto) {
        //由于core中无法调用设备服务，所以有设备服务查询出来后透传到magicube-core
        BizAssert.notNull(dto, "request must not be null");
        BizAssert.notBlank(dto.getDS(), "deviceSn must not be blank");
        Device exist = deviceManager.getByDeviceSn(dto.getDS());
        BizAssert.notNull(exist, "device not exist");
        //查询设备绑定的组
        List<Long> deviceIdList = new ArrayList<>(1);
        deviceIdList.add(exist.getDeviceId());
        // TODO兼容多租户：第一步，查看设备绑定了哪些组
        List<DeviceGroup> deviceGroupList = deviceGroupManager.listByOrgIdAndDeviceIds(dto.getOI(), deviceIdList);
        if (CollectionUtils.isEmpty(deviceGroupList)) {
            log.info("设备[{}]未绑定组，本次queryPersonInfo查询不返回数据", dto.getDI());
            return Collections.emptyList();
        }
        List<Long> bondGroupIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceGroupList)) {
            bondGroupIdList = deviceGroupList.stream().map(DeviceGroup::getGroupId).collect(Collectors.toList());
        }
        QueryPersonInfoRequest request = convertQueryPersonInfoDTOToRequest(dto, bondGroupIdList);
        // TODO兼容多租户：第二步，支持不同的人机构划分（core可支持）
        List<PersonResponse> personResponseList = personService.queryPersonInfo(request).pickDataThrowException();
        List<PersonInfoDTO> personInfoDTOList = convertToPersonInfoDTOList(personResponseList);
        return personInfoDTOList;
    }

    private void generateSnapshot(PagePersonInfoDTO dto, List<Long> bondGroupIdList) {
        GenerateDeviceSnapshotModel generateDeviceSnapshotModel = new GenerateDeviceSnapshotModel();
        generateDeviceSnapshotModel.setOrgId(dto.getOI());
        generateDeviceSnapshotModel.setDeviceId(dto.getDI());
        generateDeviceSnapshotModel.setBondGroupIdList(bondGroupIdList);
        generateDeviceSnapshotModel.setTimestamp(System.currentTimeMillis());
        generateDeviceSnapshotModel.setDeviceSn(dto.getDS());
        deviceSnapshotManager.generateSnapshot(generateDeviceSnapshotModel);
    }

    private List<PersonResponse> queryPersonCardListAndFeature(PagePersonInfoDTO dto, List<Long> bondGroupIdList, List<Long> personIdList) {
        QueryPersonInfoRequest queryPersonInfoRequest = new QueryPersonInfoRequest();
        queryPersonInfoRequest.setOrgId(dto.getOI());
        queryPersonInfoRequest.setDeviceId(dto.getDI());
        queryPersonInfoRequest.setDeviceSn(dto.getDS());
        queryPersonInfoRequest.setBondGroupIdList(bondGroupIdList);
        queryPersonInfoRequest.setFeatureType(dto.getFT());
        queryPersonInfoRequest.setModelVersion(dto.getMV());
        queryPersonInfoRequest.setNeedCard(dto.getNC());
        queryPersonInfoRequest.setNeedFeature(dto.getNF());
        queryPersonInfoRequest.setNeedGroup(dto.getNG());
        queryPersonInfoRequest.setPersonIdList(personIdList);
        List<PersonResponse> personResponseList = personService.queryPersonCardListAndFeature(queryPersonInfoRequest).pickDataThrowException();
        return personResponseList;
    }

    private QueryPersonInfoRequest convertQueryPersonInfoDTOToRequest(QueryPersonInfoDTO dto, List<Long> bondGroupIdList) {
        if (dto == null) {
            return null;
        }
        QueryPersonInfoRequest request = new QueryPersonInfoRequest();
        request.setOrgId(dto.getOI());
        request.setDeviceId(dto.getDI());
        request.setDeviceSn(dto.getDS());
        request.setPersonIdList(dto.getPL());
        request.setNeedGroup(dto.getNG());
        request.setNeedFeature(dto.getNF());
        request.setNeedCard(dto.getNC());
        request.setModelVersion(dto.getMV());
        request.setFeatureType(dto.getFT());
        request.setBondGroupIdList(bondGroupIdList);
        return request;
    }

    private List<PersonInfoDTO> convertToPersonInfoDTOList(List<PersonResponse> personResponseList) {
        if (CollectionUtils.isEmpty(personResponseList)) {
            return Collections.emptyList();
        }
        return personResponseList.stream().filter(personResponse -> personResponse != null).map(personResponse -> {
            PersonInfoDTO personInfoDTO = convertToPersonInfoDTO(personResponse);
            return personInfoDTO;
        }).collect(Collectors.toList());
    }

    private PersonInfoDTO convertToPersonInfoDTO(PersonResponse personResponse) {
        if (personResponse == null) {
            return null;
        }
        PersonInfoDTO personInfoDTO = new PersonInfoDTO();
        personInfoDTO.setCL(convertToSimpleCardInfoDTOList(personResponse.getCardList()));
        personInfoDTO.setFt(personResponse.getFeature());
        personInfoDTO.setGL(personResponse.getGroupList());
        personInfoDTO.setNC(personResponse.getNeedCard());
        personInfoDTO.setNF(personResponse.getNeedFeature());
        personInfoDTO.setNG(personResponse.getNeedGroup());
        personInfoDTO.setPI(personResponse.getPersonId());
        personInfoDTO.setPT(personResponse.getPersonType());
        return personInfoDTO;
    }

    private List<SimpleCardInfoDTO> convertToSimpleCardInfoDTOList(List<SimpleCardInfo> simpleCardInfoList) {
        if (CollectionUtils.isEmpty(simpleCardInfoList)) {
            return Collections.emptyList();
        }
        return simpleCardInfoList.stream().filter(simpleCardInfo -> simpleCardInfo != null).map(simpleCardInfo -> {
            SimpleCardInfoDTO dto = new SimpleCardInfoDTO();
            dto.setCN(simpleCardInfo.getCardNo());
            dto.setCS(simpleCardInfo.getCardStatus());
            return dto;
        }).collect(Collectors.toList());
    }
}
