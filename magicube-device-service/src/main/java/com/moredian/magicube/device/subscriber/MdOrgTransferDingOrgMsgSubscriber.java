package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.model.msg.MdOrgTransferDingOrgMsg;
import com.moredian.magicube.device.manager.MigrationDeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description: 微信组织开通设备迁移钉钉功能
 * @author: wbf
 * @date: 2024/8/13 下午4:00
 */
@Component
@Slf4j
public class MdOrgTransferDingOrgMsgSubscriber {
    @Resource
    private MigrationDeviceManager migrationDeviceManager;

    @Subscribe
    public void mdOrgTransferDingOrgMsg(MdOrgTransferDingOrgMsg msg) {
        log.info("收到微信组织开通设备迁移钉钉功能事件消息: " + JsonUtils.toJson(msg));
        if (msg != null){
            migrationDeviceManager.mdOrgTransferDingOrgMsg(msg.getOrgId());
        }
    }
}
