package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.blu.DeviceBLuCheckApiReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuCheckReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectApiReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectApiResp;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectReq;
import com.moredian.magicube.device.dto.blu.DeviceBLuConnectResp;
import com.moredian.magicube.device.dto.blu.DeviceBluConnectDeCryptReq;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import com.moredian.magicube.device.service.DeviceBluService;
import com.moredian.magicube.device.utils.AESUtils;
import com.moredian.magicube.device.utils.Coder;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@SI
@Slf4j
public class DeviceBluServiceImpl implements DeviceBluService {


    @Autowired
    private WhiteDeviceManager whiteDeviceManager;


    @Override
    public ServiceResponse<DeviceBLuConnectApiResp> checkSignApi(
        DeviceBLuConnectApiReq deviceBLuConnectApiReq) {
        ServiceResponse serviceResponse =ServiceResponse.createSuccessResponse();

        DeviceBLuConnectReq deviceBLuConnectReq = new DeviceBLuConnectReq();
        deviceBLuConnectReq.setDeviceSn(deviceBLuConnectApiReq.getDeviceSn());
        deviceBLuConnectReq.setVersion(deviceBLuConnectApiReq.getVersion());
        deviceBLuConnectReq.setMsg(deviceBLuConnectApiReq.getMsg());
        deviceBLuConnectReq.setTimeStamp(deviceBLuConnectApiReq.getTimeStamp());
        deviceBLuConnectReq.setSign(deviceBLuConnectApiReq.getSignature());
        DeviceBLuConnectResp deviceBLuConnectResp = this.checkSign(deviceBLuConnectReq).pickDataThrowException();

        DeviceBLuConnectApiResp deviceBLuConnectApiResp = new DeviceBLuConnectApiResp();
        deviceBLuConnectApiResp.setReq(deviceBLuConnectResp.getEncryptStr());
        deviceBLuConnectApiResp.setDeviceSn(deviceBLuConnectApiReq.getDeviceSn());
        DeviceBLuConnectApiResp.Auth auth = new DeviceBLuConnectApiResp.Auth();
        deviceBLuConnectApiResp.setAuth(auth);
        auth.setTs(deviceBLuConnectResp.getTimestamp());
        auth.setNounce(deviceBLuConnectResp.getNonce());
        auth.setSignature(deviceBLuConnectResp.getSign());

        serviceResponse.setData(deviceBLuConnectApiResp);

        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceBLuConnectResp> checkSign(DeviceBLuConnectReq deviceBLuConnectReq) {
        ServiceResponse serviceResponse =ServiceResponse.createSuccessResponse();
        InventoryDevice exitInventoryDevice = whiteDeviceManager.getByDeviceSn(deviceBLuConnectReq.getDeviceSn());
        BizAssert.notNull(exitInventoryDevice , DeviceErrorCode.DEVICE_NOT_WHITE, DeviceErrorCode.DEVICE_NOT_WHITE.getMessage());

        DeviceBLuCheckReq deviceBLuCheckReq = new DeviceBLuCheckReq();
        deviceBLuCheckReq.setSign(deviceBLuConnectReq.getSign());
        deviceBLuCheckReq.setDeviceSn(deviceBLuConnectReq.getDeviceSn());
        deviceBLuCheckReq.setVersion(deviceBLuConnectReq.getVersion());
        deviceBLuCheckReq.setTimeStamp(deviceBLuConnectReq.getTimeStamp());
        //签名校验时，无需时间校验
        BizAssert.isTrue(isCheckFlag(deviceBLuCheckReq,exitInventoryDevice,false), DeviceErrorCode.DEVICE_BLU_INVALID_SIGNATURE, DeviceErrorCode.DEVICE_BLU_INVALID_SIGNATURE.getMessage());

        long timestamp = System.currentTimeMillis() / 1000;
        Random random = new Random(1000000000);
        long nonce = random.nextInt();

        try {
            //加密
            String encrypt = AESUtils.encrypt(deviceBLuConnectReq.getMsg(), exitInventoryDevice.getPrivateKey());
            StringBuilder sb = new StringBuilder().append(timestamp).append(nonce);

            if(deviceBLuConnectReq.getTimeStamp() != null && deviceBLuConnectReq.getTimeStamp() != 0) {
                sb.append(exitInventoryDevice.getSerialNumber());
            }
            sb.append(encrypt);
            String sign = Coder.HmacSHA1EncryptBase64(sb.toString(), exitInventoryDevice.getPrivateKey());

            DeviceBLuConnectResp deviceBLuConnectResp = new DeviceBLuConnectResp();
            deviceBLuConnectResp.setTimestamp(timestamp);
            deviceBLuConnectResp.setNonce(nonce);
            deviceBLuConnectResp.setSign(sign);

            deviceBLuConnectResp.setEncryptStr(encrypt);
            serviceResponse.setData(deviceBLuConnectResp);
        } catch (Exception e) {
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_BLU_DECRYPT_ERROR,DeviceErrorCode.DEVICE_BLU_DECRYPT_ERROR.getMessage()));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<String> decrypt(DeviceBluConnectDeCryptReq deviceBluConnectDeCryptReq) {
        ServiceResponse response = ServiceResponse.createSuccessResponse();
        InventoryDevice exitInventoryDevice = whiteDeviceManager.getByDeviceSn(deviceBluConnectDeCryptReq.getDeviceSn());
        BizAssert.notNull(exitInventoryDevice , DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION, DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION.getMessage());


        try {
            String aesDecrypt = AESUtils.decrypt(deviceBluConnectDeCryptReq.getEncryptStr(), exitInventoryDevice.getPrivateKey());
            response.setData(aesDecrypt);
        } catch (Exception e) {
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_BLU_DECRYPT_ERROR, DeviceErrorCode.DEVICE_BLU_DECRYPT_ERROR.getMessage()));
        }
        return response;
    }
    @Override
    public ServiceResponse<Boolean> checValid(DeviceBLuCheckApiReq deviceBLuCheckApiReq) {
        ServiceResponse serviceResponse =ServiceResponse.createSuccessResponse();
        DeviceBLuCheckReq deviceBLuCheckReq = new DeviceBLuCheckReq();
        deviceBLuCheckReq.setSign(deviceBLuCheckApiReq.getSignature());
        deviceBLuCheckReq.setDeviceSn(deviceBLuCheckApiReq.getDeviceSn());
        deviceBLuCheckReq.setVersion(deviceBLuCheckApiReq.getVersion());
        deviceBLuCheckReq.setTimeStamp(deviceBLuCheckApiReq.getTimeStamp());
        
        serviceResponse.setData(this.checValid(deviceBLuCheckReq).pickDataThrowException());
        return serviceResponse;
    }
    @Override
    public ServiceResponse<Boolean> checValid(DeviceBLuCheckReq bLuCheckReq) {
        ServiceResponse serviceResponse =ServiceResponse.createSuccessResponse();
        InventoryDevice exitInventoryDevice = whiteDeviceManager.getByDeviceSn(bLuCheckReq.getDeviceSn());

        BizAssert.notNull(exitInventoryDevice , DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION, DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION.getMessage());
        //设备合法性时，需要蓝牙校验
        boolean checkFlag = isCheckFlag(bLuCheckReq, exitInventoryDevice,true);

        serviceResponse.setData(checkFlag);

        return serviceResponse;
    }

    private boolean isCheckFlag(DeviceBLuCheckReq bLuCheckReq, InventoryDevice exitInventoryDevice, Boolean flag) {
        BizAssert.notNull(exitInventoryDevice , DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION, DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION.getMessage());

        if(flag && bLuCheckReq.getTimeStamp() != null && bLuCheckReq.getTimeStamp() != 0 ){
            BizAssert.isTrue(
                System.currentTimeMillis() - bLuCheckReq.getTimeStamp()*1000 <= 10 * 60 * 1000, DeviceErrorCode.DEVICE_BLU_SIGN_EXPIRE,DeviceErrorCode.DEVICE_BLU_SIGN_EXPIRE.getMessage());
        }
        boolean checkFlag = false;
        try {
            String newKey = "";
            if(bLuCheckReq.getTimeStamp() != null && bLuCheckReq.getTimeStamp() != 0 ) {
                newKey = Coder.HmacSHA1EncryptToHexStr(bLuCheckReq.getDeviceSn()+ bLuCheckReq.getTimeStamp(), exitInventoryDevice.getPrivateKey(), 0, 4);
            }else{
                newKey = Coder.HmacSHA1EncryptToHexStr(bLuCheckReq.getDeviceSn(), exitInventoryDevice.getPrivateKey(), 0, 4);
            }
            log.info("/checkSign- newKey:{},req:{}", newKey, bLuCheckReq);
            checkFlag = bLuCheckReq.getSign().equals(newKey);

        } catch (Exception e) {
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_BLU_DECRYPT_ERROR,DeviceErrorCode.DEVICE_BLU_DECRYPT_ERROR.getMessage()));
        }
        return checkFlag;
    }
}
