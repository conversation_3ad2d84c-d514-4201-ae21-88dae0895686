package com.moredian.magicube.device.service.impl;


import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.common.utils.RandomUtil;
import com.moredian.bee.filemanager.ImageFileManager;
import com.moredian.bee.filemanager.enums.FilePathType;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceProperty.IIotDevicePropertyService;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyInfoDTO;
import com.moredian.conf.core.client.BeeConfNewClient;
import com.moredian.device.pipeline.api.DevicePipelineStateService;
import com.moredian.device.pipeline.dto.request.DevicePipelineStateSelectReq;
import com.moredian.device.pipeline.dto.response.DevicePipelineStateListResp;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.GroupAppType;
import com.moredian.magicube.common.enums.SpaceTreeNodeType;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.common.model.msg.device.DeviceNameChangeEvent;
import com.moredian.magicube.core.member.enums.MemberErrorCode;
import com.moredian.magicube.core.member.service.GroupRelationService;
import com.moredian.magicube.device.config.IndustryProtectedConfigs;
import com.moredian.magicube.device.config.ParamConstants;
import com.moredian.magicube.device.constant.BeeConfConfigConstants;
import com.moredian.magicube.device.constant.DeviceIotPropertyConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dao.mapper.InventoryDeviceMapper;
import com.moredian.magicube.device.dto.device.AddDeviceDTO;
import com.moredian.magicube.device.dto.device.DeviceActiveJumpAppConfigDTO;
import com.moredian.magicube.device.dto.device.DeviceCountDTO;
import com.moredian.magicube.device.dto.device.DeviceDTO;
import com.moredian.magicube.device.dto.device.DeviceElectricDTO;
import com.moredian.magicube.device.dto.device.DeviceHasPersonDTO;
import com.moredian.magicube.device.dto.device.DeviceIndustryAppTypeDTO;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.DeviceRelateAppDTO;
import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.dto.device.DeviceWithVersionInfoDTO;
import com.moredian.magicube.device.dto.device.ImagePathDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceWithVersionDTO;
import com.moredian.magicube.device.dto.device.QuerySpecificDeviceDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceElectricDTO;
import com.moredian.magicube.device.dto.device.SaveImageDTO;
import com.moredian.magicube.device.dto.device.SimpleDeviceInfoDTO;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.dto.device.VersionInfoDTO;
import com.moredian.magicube.device.dto.device.ViewDeviceDTO;
import com.moredian.magicube.device.dto.lock.LockInfoDTO;
import com.moredian.magicube.device.dto.project.DeviceProjectInfoResponse;
import com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.type.DeviceCapacityDTO;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionInfoDTO;
import com.moredian.magicube.device.dto.white.QueryWhiteDeviceDTO;
import com.moredian.magicube.device.enums.DeviceRelateAppFixedBIzTypeEnum;
import com.moredian.magicube.device.enums.EnvConfigEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.DeviceTypeNameHelper;
import com.moredian.magicube.device.manager.*;
import com.moredian.magicube.device.service.DeviceGroupRelationService;
import com.moredian.magicube.device.service.DeviceProjectInfoService;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceVersionService;
import com.moredian.magicube.device.service.lock.TTLockService;
import com.moredian.magicube.device.utils.DeviceSn;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;
import com.moredian.space.service.v2.SpaceTreeDeviceRelationServiceV2;
import com.moredian.space.service.v2.SpaceTreeServiceV2;
import com.xier.ding.api.corp.dto.CorpDto;
import com.xier.ding.api.corp.service.CorpHelperService;
import com.xier.sesame.common.web.BaseResponse;

import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@SI
@Slf4j
public class DeviceServiceImpl implements DeviceService {

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceIotManager deviceIotManager;

    @Autowired
    private InventoryDeviceMapper inventoryDeviceMapper;

    @Autowired
    private WhiteDeviceManager whiteDeviceManager;

    @Autowired
    private IndustryProtectedConfigs industryProtectedConfigs;

    @Autowired
    private DeviceVersionService deviceVersionService;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Autowired
    private DeviceProjectInfoService deviceProjectInfoService;


    @Autowired
    private ImageFileManager imageFileManager;

    @SI
    private DeviceGroupRelationService deviceGroupRelationService;

    @SI
    private GroupRelationService groupRelationService;

    @SI
    private CorpHelperService corpHelperService;

    @SI
    private SpaceTreeDeviceRelationService treeDeviceRelationService;

    @SI
    private SpaceTreeDeviceRelationServiceV2 treeDeviceRelationServiceV2;

    @SI
    private SpaceTreeService spaceTreeService;

    @SI
    private SpaceTreeServiceV2 spaceTreeServiceV2;

    @SI
    private DevicePipelineStateService devicePipelineStateService;

    @Autowired
    private DeviceTypeMapManager getDeviceTypeMapManager;

    @Resource
    private DeviceAppRelationFixedConfigManager deviceAppRelationFixedConfigManager;

    @Resource
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;

    @Resource
    private DeviceTypeNameHelper deviceTypeNameHelper;

    @SI
    private IIotDevicePropertyService iotDevicePropertyService;

    @Resource
    private TTLockService ttLockService;

    private static final int DEFAULT_MAX_COUNT = 100;

    @Override
    public ServiceResponse<DeviceInfoDTO> getByDeviceSn(String deviceSn) {
        ServiceResponse<DeviceInfoDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        Device device = deviceManager.getByDeviceSn(deviceSn);
        if (device != null) {
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            BeanUtils.copyProperties(device, deviceInfoDTO);
            if (device.getActiveTime() != null){
                deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
            }
            if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
                deviceInfoDTO.setTpId(device.getThirdDeviceId());
            }
            List<TreeDeviceRelationDTO> relationDTOs = treeDeviceRelationServiceV2.listByDeviceIdsWithoutOnlieStatus(
                device.getOrgId(), Lists.newArrayList(device.getDeviceId()))
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOs)){
                TreeDeviceRelationDTO relationDTO = relationDTOs.get(0);
                deviceInfoDTO.setTreeId(relationDTO.getTreeId());
                if (StringUtils.isNotBlank(relationDTO.getPathTreeName())){
                    deviceInfoDTO.setPathTreeName(relationDTO.getPathTreeName());
                    deviceInfoDTO.setPath(relationDTO.getPath());
                }
                deviceInfoDTO.setPropertyTreeNodeId(relationDTO.getPropertyTreeNodeId());
                deviceInfoDTO.setPropertyTreeName(relationDTO.getPropertyTreeName());
                deviceInfoDTO.setDirection(relationDTO.getDirection());
                deviceInfoDTO.setTreeName(relationDTO.getTreeName());
                deviceInfoDTO.setParentTreeNodeId(relationDTO.getParentTreeNodeId());
            }
            DevicePipelineStateListResp dps = devicePipelineStateService.getDeviceByDeviceSn(device.getOrgId(),deviceSn).pickDataThrowException();
            if (dps != null){
                deviceInfoDTO.setSceneType(dps.getSceneType());
            }
            DeviceElectricDTO deviceElectricDTO = deviceManager.queryElectricByDeviceId(device.getDeviceId());
            if (deviceElectricDTO != null) {
                deviceInfoDTO.setElectric(deviceElectricDTO.getElectric());
                deviceInfoDTO.setLatestReportElectric(deviceElectricDTO.getGmtModify());
            }
            deviceInfoDTO.setDeviceTypeName(deviceTypeNameHelper.getDisplayDeviceTypeName(deviceSn));
            handlePathTreeName(Lists.newArrayList(deviceInfoDTO));
            serviceResponse.setData(deviceInfoDTO);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceInfoDTO> getByOrgAndDeviceSn(Long orgId, String deviceSn) {
        ServiceResponse<DeviceInfoDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        Device device = deviceManager.getByOrgAndDeviceSn(orgId, deviceSn);
        if (device != null) {
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            BeanUtils.copyProperties(device, deviceInfoDTO);
            if (device.getActiveTime() != null){
                deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
            }
            if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
                deviceInfoDTO.setTpId(device.getThirdDeviceId());
            }
            List<TreeDeviceRelationDTO> relationDTOs = treeDeviceRelationServiceV2.listByDeviceIdsWithoutOnlieStatus(
                device.getOrgId(), Lists.newArrayList(device.getDeviceId()))
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOs)){
                TreeDeviceRelationDTO relationDTO = relationDTOs.get(0);
                deviceInfoDTO.setTreeId(relationDTO.getTreeId());
                if (StringUtils.isNotBlank(relationDTO.getPathTreeName())){
                    deviceInfoDTO.setPathTreeName(relationDTO.getPathTreeName());
                    deviceInfoDTO.setPath(relationDTO.getPath());
                }
                deviceInfoDTO.setPropertyTreeNodeId(relationDTO.getPropertyTreeNodeId());
                deviceInfoDTO.setPropertyTreeName(relationDTO.getPropertyTreeName());
                deviceInfoDTO.setDirection(relationDTO.getDirection());
                deviceInfoDTO.setParentTreeNodeId(relationDTO.getParentTreeNodeId());
                DevicePipelineStateListResp dps = devicePipelineStateService.getDeviceByDeviceSn(device.getOrgId(),deviceSn).pickDataThrowException();
                if (dps != null){
                    deviceInfoDTO.setSceneType(dps.getSceneType());
                }
            }
            deviceInfoDTO.setDeviceTypeName(deviceTypeNameHelper.getDisplayDeviceTypeName(deviceSn));
            handlePathTreeName(Lists.newArrayList(deviceInfoDTO));
            serviceResponse.setData(deviceInfoDTO);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByDeviceSns(List<String> deviceSns) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByDeviceSns(deviceSns);
        if (CollectionUtils.isNotEmpty(devices)) {
            if (CollectionUtils.isNotEmpty(devices)) {
                serviceResponse.setData(deviceToDeviceInfoDTO(devices));
            }
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<SimpleDeviceInfoDTO>> listBaseByDeviceSns(List<String> deviceSns) {
        ServiceResponse<List<SimpleDeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByDeviceSns(deviceSns);
        if (CollectionUtils.isNotEmpty(devices)) {
            List<SimpleDeviceInfoDTO> deviceInfoDTOs = new ArrayList<>();
            Map<String, String> deviceNameMap = deviceTypeNameHelper.ListDisplayDeviceTypeName(deviceSns);
            for (Device device : devices) {
                SimpleDeviceInfoDTO deviceInfoDTO = new SimpleDeviceInfoDTO();
                BeanUtils.copyProperties(device, deviceInfoDTO);
                deviceInfoDTO.setDeviceTypeName(deviceNameMap.getOrDefault(device.getDeviceSn(), ""));
                deviceInfoDTOs.add(deviceInfoDTO);
            }
            serviceResponse.setData(deviceInfoDTOs);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndDeviceSns(Long orgId,
        List<String> deviceSns) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByOrgIdAndDeviceSns(orgId, deviceSns);
        if (CollectionUtils.isNotEmpty(devices)) {
            if (CollectionUtils.isNotEmpty(devices)) {
                serviceResponse.setData(deviceToDeviceInfoDTO(devices));
            }
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceInfoDTO> getById(Long deviceId) {
        ServiceResponse<DeviceInfoDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        Device device = deviceManager.getById(deviceId);
        if (device != null) {
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            BeanUtils.copyProperties(device, deviceInfoDTO);
            if (device.getActiveTime() != null){
                deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
            }
            if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
                deviceInfoDTO.setTpId(device.getThirdDeviceId());
            }
            List<TreeDeviceRelationDTO> relationDTOs = treeDeviceRelationServiceV2.listByDeviceIdsWithoutOnlieStatus(
                device.getOrgId(), Lists.newArrayList(deviceId)).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOs)){
                TreeDeviceRelationDTO relationDTO = relationDTOs.get(0);
                deviceInfoDTO.setTreeId(relationDTO.getTreeId());
                deviceInfoDTO.setPath(relationDTO.getPath());
                if (StringUtils.isNotBlank(relationDTO.getPathTreeName())){
                    deviceInfoDTO.setPathTreeName(relationDTO.getPathTreeName());
                    deviceInfoDTO.setPath(relationDTO.getPath());
                }
                if (StringUtils.isNotBlank(relationDTO.getTreeName())) {
                    deviceInfoDTO.setTreeName(relationDTO.getTreeName());
                }
                deviceInfoDTO.setDirection(relationDTO.getDirection());
                deviceInfoDTO.setPropertyTreeName(relationDTO.getPropertyTreeName());
                deviceInfoDTO.setPropertyTreeNodeId(relationDTO.getPropertyTreeNodeId());
                deviceInfoDTO.setParentTreeNodeId(relationDTO.getParentTreeNodeId());
                deviceInfoDTO.setSpaceId(relationDTO.getSpaceId());
            }
            //设置关联应用
            deviceInfoDTO.setDeviceRelatedApp(buildDeviceRelateApp(device));

            DevicePipelineStateListResp dps = devicePipelineStateService.getDeviceByDeviceSn(device
                .getOrgId(),device.getDeviceSn()).pickDataThrowException();
            if (dps != null){
                deviceInfoDTO.setSceneType(dps.getSceneType());
            }
            if (StringUtils.isNotBlank(device.getDeviceSn())) {
                String deviceSn = device.getDeviceSn();
                QueryDeviceProjectInfoRequest request = new QueryDeviceProjectInfoRequest();
                request.setDeviceSnList(Lists.newArrayList(deviceSn));
                List<DeviceProjectInfoResponse> responses = deviceProjectInfoService
                    .findDeviceProjectInfo(request).pickDataThrowException();
                setDeviceCapacity(responses, deviceInfoDTO);
                if (deviceInfoDTO.getDeviceCapacity() == null) {
                    //从数据库中查询人脸容量
                    deviceInfoDTO.setDeviceCapacity(getDeviceTypeMapManager
                        .getDeviceCapacity(deviceInfoDTO.getDeviceType()));
                }
            }

            // 如果是通通锁，电量在魔链查询
            Integer deviceType = device.getDeviceType();
            if (deviceType != null && deviceType == DeviceType.TTLOCK.getValue()) {
                LockInfoDTO lockInfoDTO = ttLockService.lockInfoBySn(device.getDeviceSn()).pickDataThrowException();
                if (lockInfoDTO != null && lockInfoDTO.getLockInfo() != null) {
                    LockInfoDTO.LockInfo lockInfo = lockInfoDTO.getLockInfo();
                    deviceInfoDTO.setElectric(lockInfo.getElectricQuantity());
                    deviceInfoDTO.setLatestReportElectric(new Date());
                }
            }else {
                DeviceElectricDTO deviceElectricDTO = deviceManager
                        .queryElectricByDeviceId(device.getDeviceId());
                if (deviceElectricDTO != null) {
                    deviceInfoDTO.setElectric(deviceElectricDTO.getElectric());
                    deviceInfoDTO.setLatestReportElectric(deviceElectricDTO.getGmtModify());
                }
            }
            deviceInfoDTO.setDeviceTypeName(deviceTypeNameHelper.getDisplayDeviceTypeName(device.getDeviceSn()));
            handlePathTreeName(Lists.newArrayList(deviceInfoDTO));

            //获取设备SN对应的appType和version
            List<DeviceVersionInfoDTO> deviceVersionInfoDTOS = deviceVersionService
                .getBatchDeviceApkVersionFromDatabase(Collections
                    .singletonList(device.getDeviceSn())).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceVersionInfoDTOS)
                && deviceVersionInfoDTOS.get(0) != null) {
                DeviceVersionInfoDTO deviceVersionInfoDTO = deviceVersionInfoDTOS.get(0);
                Integer appType = deviceVersionInfoDTO.getAppType();
                Integer versionCode = deviceVersionInfoDTO.getVersionCode();

                //获取设备运行环境
                String environment = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE
                    , BeeConfConfigConstants.GROUP_ENVIRONMENT, appType.toString());
                if (StringUtils.isNotBlank(environment)) {
                    deviceInfoDTO.setEnvironment(Integer.valueOf(environment));
                    //元石分销环境 pathTreeName设置为空,
                    if (EnvConfigEnum.FX.getCode().equals(Integer.valueOf(environment))) {
                        deviceInfoDTO.setPathTreeName("");
                        deviceInfoDTO.setPosition(StringUtils.isNotBlank(deviceInfoDTO.getPosition(
                        )) ? deviceInfoDTO.getPosition() : deviceInfoDTO.getDeviceName());
                    }
                } else {
                    //设备默认为行业环境
                    deviceInfoDTO.setEnvironment(EnvConfigEnum.HY.getCode());
                }

                //BeeConfNewClient 配置详解 BeeConfConfigConstants
                //查询appType 和 version对应的设备模式
                String deviceModelJson = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE
                    , BeeConfConfigConstants.GROUP_DEVICE_MODULE,
                    appType.toString(), versionCode.toString());
                if (StringUtils.isEmpty(deviceModelJson)) {
                    serviceResponse.setData(deviceInfoDTO);
                    return serviceResponse;
                }
                List<Integer> deviceModelList = JSON.parseArray(deviceModelJson, Integer.class);
                Set<String> deviceModelAppCodeSet = new HashSet<>();
                if (deviceModelList.contains(deviceInfoDTO.getSceneType())) {
                    //根据设备当前模式去查找模式对应的应用
                    Integer deviceCurrentModel = deviceInfoDTO.getSceneType();
                    String deviceAppJson = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE
                        , BeeConfConfigConstants.GROUP_DEVICE_APP,
                        appType + "-" + versionCode, deviceCurrentModel.toString());
                    List<String> deviceAppList = JSON.parseArray(deviceAppJson, String.class);
                    if (CollectionUtils.isNotEmpty(deviceAppList)) {
                        deviceModelAppCodeSet.addAll(deviceAppList);
                    }
                    deviceInfoDTO.setDeviceModelAppCodeList(new ArrayList<>(deviceModelAppCodeSet));
                }
            }
            serviceResponse.setData(deviceInfoDTO);
        }
        return serviceResponse;
    }

    private void handlePathTreeName(List<DeviceInfoDTO> deviceInfoDTOList) {
        if (CollectionUtils.isEmpty(deviceInfoDTOList)) {
            return;
        }
        List<String> deviceSnList = deviceInfoDTOList.stream().map(DeviceInfoDTO::getDeviceSn).collect(Collectors.toList());
        //获取设备SN对应的appType
        List<DeviceVersionInfoDTO> deviceVersionInfoDTOS = deviceVersionService
                .getBatchDeviceApkVersionFromDatabase(deviceSnList)
                .pickDataThrowException();
        Map<String, Integer> deviceSnToAppTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceVersionInfoDTOS)) {
            deviceSnToAppTypeMap = deviceVersionInfoDTOS.stream().collect(Collectors.toMap(DeviceVersionInfoDTO::getDeviceSn,
                    DeviceVersionInfoDTO::getAppType));
        }
        for (DeviceInfoDTO deviceInfoDTO : deviceInfoDTOList) {
            String deviceSn = deviceInfoDTO.getDeviceSn();
            if (!deviceSnToAppTypeMap.containsKey(deviceSn)) {
                continue;
            }
            Integer appType = deviceSnToAppTypeMap.get(deviceSn);
            //获取设备运行环境
            String environment = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE, BeeConfConfigConstants.GROUP_ENVIRONMENT,
                    appType.toString());
            if (StringUtils.isNotBlank(environment)) {
                deviceInfoDTO.setEnvironment(Integer.valueOf(environment));
                //元石分销环境 pathTreeName设置为空,
                if (EnvConfigEnum.FX.getCode().equals(Integer.valueOf(environment))) {
                    deviceInfoDTO.setPathTreeName("");
                    deviceInfoDTO.setPosition(StringUtils.isNotBlank(deviceInfoDTO.getPosition()) ? deviceInfoDTO.getPosition() : deviceInfoDTO.getDeviceName());
                }
            }
        }
    }

    @Override
    public ServiceResponse<DeviceInfoDTO> getByOrgIdAndId(Long orgId, Long deviceId) {
        ServiceResponse<DeviceInfoDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        Device device = deviceManager.getByOrgIdAndId(orgId, deviceId);
        if (device != null) {
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            BeanUtils.copyProperties(device, deviceInfoDTO);
            if (device.getActiveTime() != null){
                deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
            }
            if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
                deviceInfoDTO.setTpId(device.getThirdDeviceId());
            }
            List<TreeDeviceRelationDTO> relationDTOs = treeDeviceRelationServiceV2.listByDeviceIdsWithoutOnlieStatus(
                device.getOrgId(), Lists.newArrayList(deviceId)).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOs)){
                TreeDeviceRelationDTO relationDTO = relationDTOs.get(0);
                deviceInfoDTO.setTreeId(relationDTO.getTreeId());
                deviceInfoDTO.setPath(relationDTO.getPath());
                if (StringUtils.isNotBlank(relationDTO.getPathTreeName())){
                    deviceInfoDTO.setPathTreeName(relationDTO.getPathTreeName());
                    deviceInfoDTO.setPath(relationDTO.getPath());
                }
                if (StringUtils.isNotBlank(relationDTO.getTreeName())) {
                    deviceInfoDTO.setTreeName(relationDTO.getTreeName());
                }
                deviceInfoDTO.setDirection(relationDTO.getDirection());
                deviceInfoDTO.setPropertyTreeName(relationDTO.getPropertyTreeName());
                deviceInfoDTO.setPropertyTreeNodeId(relationDTO.getPropertyTreeNodeId());
                deviceInfoDTO.setParentTreeNodeId(relationDTO.getParentTreeNodeId());
                deviceInfoDTO.setSpaceId(relationDTO.getSpaceId());
            }
            //设置关联应用
            deviceInfoDTO.setDeviceRelatedApp(buildDeviceRelateApp(device));

            DevicePipelineStateListResp dps = devicePipelineStateService.getDeviceByDeviceSn(device.getOrgId(),device.getDeviceSn()).pickDataThrowException();
            if (dps != null){
                deviceInfoDTO.setSceneType(dps.getSceneType());
            }
            if (StringUtils.isNotBlank(device.getDeviceSn())) {
                String deviceSn = device.getDeviceSn();
                QueryDeviceProjectInfoRequest request = new QueryDeviceProjectInfoRequest();
                request.setDeviceSnList(Lists.newArrayList(deviceSn));
                List<DeviceProjectInfoResponse> responses = deviceProjectInfoService.findDeviceProjectInfo(request).pickDataThrowException();
                setDeviceCapacity(responses, deviceInfoDTO);
                if (deviceInfoDTO.getDeviceCapacity() == null) {
                    //从数据库中查询人脸容量
                    deviceInfoDTO.setDeviceCapacity(getDeviceTypeMapManager.getDeviceCapacity(deviceInfoDTO.getDeviceType()));
                }
            }
            DeviceElectricDTO deviceElectricDTO = deviceManager.queryElectricByDeviceId(device.getDeviceId());
            if (deviceElectricDTO != null) {
                deviceInfoDTO.setElectric(deviceElectricDTO.getElectric());
                deviceInfoDTO.setLatestReportElectric(deviceElectricDTO.getGmtModify());
            }

            //获取设备SN对应的appType和version
            List<DeviceVersionInfoDTO> deviceVersionInfoDTOS = deviceVersionService
                    .getBatchDeviceApkVersionFromDatabase(Collections.singletonList(device.getDeviceSn()))
                    .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceVersionInfoDTOS) && deviceVersionInfoDTOS.get(0) != null) {
                DeviceVersionInfoDTO deviceVersionInfoDTO = deviceVersionInfoDTOS.get(0);
                Integer appType = deviceVersionInfoDTO.getAppType();
                Integer versionCode = deviceVersionInfoDTO.getVersionCode();

                //获取设备运行环境
                String environment = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE, BeeConfConfigConstants.GROUP_ENVIRONMENT,
                        appType.toString());
                if (StringUtils.isNotBlank(environment)) {
                    deviceInfoDTO.setEnvironment(Integer.valueOf(environment));
                    //元石分销环境 pathTreeName设置为空,
                    if (EnvConfigEnum.FX.getCode().equals(Integer.valueOf(environment))) {
                        deviceInfoDTO.setPathTreeName("");
                        deviceInfoDTO.setPosition(StringUtils.isNotBlank(deviceInfoDTO.getPosition()) ? deviceInfoDTO.getPosition() : deviceInfoDTO.getDeviceName());
                    }
                } else {
                    //设备默认为行业环境
                    deviceInfoDTO.setEnvironment(EnvConfigEnum.HY.getCode());
                }

                //BeeConfNewClient 配置详解 BeeConfConfigConstants
                //查询appType 和 version对应的设备模式
                String deviceModelJson = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE, BeeConfConfigConstants.GROUP_DEVICE_MODULE,
                        appType.toString(), versionCode.toString());
                if (StringUtils.isEmpty(deviceModelJson)) {
                    serviceResponse.setData(deviceInfoDTO);
                    return serviceResponse;
                }
                List<Integer> deviceModelList = JSON.parseArray(deviceModelJson, Integer.class);
                Set<String> deviceModelAppCodeSet = new HashSet<>();
                if (deviceModelList.contains(deviceInfoDTO.getSceneType())) {
                    //根据设备当前模式去查找模式对应的应用
                    Integer deviceCurrentModel = deviceInfoDTO.getSceneType();
                    String deviceAppJson = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE, BeeConfConfigConstants.GROUP_DEVICE_APP,
                            appType + "-" + versionCode, deviceCurrentModel.toString());
                    List<String> deviceAppList = JSON.parseArray(deviceAppJson, String.class);
                    if (CollectionUtils.isNotEmpty(deviceAppList)) {
                        deviceModelAppCodeSet.addAll(deviceAppList);
                    }
                    deviceInfoDTO.setDeviceModelAppCodeList(new ArrayList<>(deviceModelAppCodeSet));
                }
            }
            deviceInfoDTO.setDeviceTypeName(deviceTypeNameHelper.getDisplayDeviceTypeName(device.getDeviceSn()));
            handlePathTreeName(Lists.newArrayList(deviceInfoDTO));

            serviceResponse.setData(deviceInfoDTO);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<SimpleDeviceInfoDTO> getSimpleByOrgIdAndId(Long orgId, Long deviceId) {
        ServiceResponse<SimpleDeviceInfoDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        SimpleDeviceInfoDTO simpleDeviceInfo = new SimpleDeviceInfoDTO();
        serviceResponse.setData(simpleDeviceInfo);
        Device device = deviceManager.getByOrgIdAndId(orgId, deviceId);
        if (device != null) {
            simpleDeviceInfo.setOrgId(device.getOrgId());
            simpleDeviceInfo.setDeviceId(device.getDeviceId());
            simpleDeviceInfo.setDeviceSn(device.getDeviceSn());
            simpleDeviceInfo.setDeviceName(device.getDeviceName());
            simpleDeviceInfo.setDeviceType(device.getDeviceType());
            simpleDeviceInfo.setPosition(device.getPosition());
            simpleDeviceInfo.setParentDeviceSn(device.getParentDeviceSn());
            DevicePipelineStateListResp dps = devicePipelineStateService.getDeviceByDeviceSn(device
                .getOrgId(), device.getDeviceSn()).pickDataThrowException();
            if (dps != null){
                simpleDeviceInfo.setSceneType(dps.getSceneType());
            }
            simpleDeviceInfo.setDeviceTypeName(deviceTypeNameHelper.getDisplayDeviceTypeName(device.getDeviceSn()));
        }
        return serviceResponse;
    }


    /**
     * 获取设备容量
     * @param responses 设备项目信息
     * @param deviceInfoDTO 返回参数
     */
    private void setDeviceCapacity(List<DeviceProjectInfoResponse> responses, DeviceInfoDTO deviceInfoDTO) {
        if (CollectionUtils.isNotEmpty(responses)) {
            DeviceProjectInfoResponse datum = responses.get(0);
            if (StringUtils.isNotBlank(datum.getProjectInfo())) {
                JSONObject jsonObject = JSON.parseObject(datum.getProjectInfo());
                if (jsonObject.containsKey(ParamConstants.CUSTOM_PARAMS)) {
                    JSONObject customParams = jsonObject.getJSONObject(ParamConstants.CUSTOM_PARAMS);
                    if (customParams != null && customParams.containsKey(ParamConstants.FACE_LIMIT)) {
                        String faceLimit = customParams.getString(ParamConstants.FACE_LIMIT);
                        if (StringUtils.isNotBlank(faceLimit)) {
                            deviceInfoDTO.setDeviceCapacity(Integer.valueOf(faceLimit));
                        }
                    }
                }
            }
        }
    }
    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByIds(List<Long> deviceIds) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return serviceResponse;
        }
        List<Device> devices = deviceManager.listByIds(deviceIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            serviceResponse.setData(deviceToDeviceInfoDTO(devices));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<SimpleDeviceInfoDTO>> listBaseByIds(List<Long> deviceIds) {
        ServiceResponse<List<SimpleDeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByIds(deviceIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            List<SimpleDeviceInfoDTO> deviceInfoDTOs = new ArrayList<>();
            for (Device device : devices) {
                SimpleDeviceInfoDTO deviceInfoDTO = new SimpleDeviceInfoDTO();
                BeanUtils.copyProperties(device, deviceInfoDTO);
                deviceInfoDTOs.add(deviceInfoDTO);
            }
            serviceResponse.setData(deviceInfoDTOs);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndIds(Long orgId,
        List<Long> deviceIds) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return serviceResponse;
        }
        List<Device> devices = deviceManager.listByOrgIdAndIds(orgId, deviceIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            serviceResponse.setData(deviceToDeviceInfoDTO(devices));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listSimpleByOrgIdAndIds(Long orgId,
        List<Long> deviceIds) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return serviceResponse;
        }
        List<Device> devices = deviceManager.listByOrgIdAndIds(orgId, deviceIds);
        List<DeviceInfoDTO> deviceInfoDTOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(devices)) {
            List<String> deviceSns = devices.stream().map(Device::getDeviceSn)
                .distinct().collect(Collectors.toList());
            Map<String, String> map = deviceTypeNameHelper.ListDisplayDeviceTypeName(deviceSns);
            for (Device device : devices) {
                DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
                BeanUtils.copyProperties(device, deviceInfoDTO);
                if (device.getActiveTime() != null) {
                    deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
                }
                String deviceTypeName = map.get(device.getDeviceSn());
                if (StringUtils.isNotBlank(deviceTypeName)) {
                    deviceInfoDTO.setDeviceTypeName(deviceTypeName);
                }
                deviceInfoDTOs.add(deviceInfoDTO);
            }
        }
        serviceResponse.setData(deviceInfoDTOs);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByOrgId(Long orgId) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(devices)) {
            serviceResponse.setData(deviceToDeviceInfoDTO(devices));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Long> addDevice(AddDeviceDTO dto) {
        Long deviceId = deviceManager.add(dto);
        return new ServiceResponse<>(true, null, deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Boolean> update(UpdateDeviceDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        boolean result = deviceManager.update(dto);
        serviceResponse.setData(result);
        // 发送更新设备名称的事件
        if (result) {
            DeviceInfoDTO deviceInfoDTO = this.getById(dto.getDeviceId()).pickDataThrowException();
            if (deviceInfoDTO != null) {
                //通知设备名称被修改的事件，主要是通知调用钉钉开放平台接口修改
                DeviceNameChangeEvent deviceNameChangeEvent = new DeviceNameChangeEvent();
                BeanUtils.copyProperties(deviceInfoDTO, deviceNameChangeEvent);
                deviceNameChangeEvent.setName(deviceInfoDTO.showName());
                deviceNameChangeEvent.setDeviveType(deviceInfoDTO.getDeviceType());
                InventoryDevice inventoryDevice = inventoryDeviceMapper
                    .getByDeviceSn(deviceInfoDTO.getDeviceSn());
                if (inventoryDevice != null) {
                    deviceNameChangeEvent.setTpDevId(inventoryDevice.getThirdDeviceId());
                }
                log.info("发送设备名称变更消息deviceNameChangeEvent:{}", deviceNameChangeEvent);
                EventBus.publish(deviceNameChangeEvent);
            }
        }
        return serviceResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Boolean> batchUpdate(List<UpdateDeviceDTO> list) {
        boolean updateSuccess = Boolean.FALSE;
        if (CollectionUtils.isNotEmpty(list)) {
            Boolean result = deviceManager.batchUpdate(list);
            if (result) {
                List<Long> deviceIds = Lists.newArrayList();
                for (UpdateDeviceDTO deviceDTO : list) {
                    deviceIds.add(deviceDTO.getDeviceId());
                }
                if (CollectionUtils.isNotEmpty(deviceIds)) {
                    List<DeviceInfoDTO> devices = this.listByIds(deviceIds).pickDataThrowException();
                    if (CollectionUtils.isNotEmpty(devices)) {
                        List<String> deviceSns = devices.stream().map(DeviceInfoDTO::getDeviceSn)
                            .collect(Collectors.toList());
                        List<InventoryDevice> inventoryDevices;
                        Map<String, String> deviceSnToTpDeviceIdMap = new HashMap<>(
                            deviceSns.size());
                        if (CollectionUtils.isNotEmpty(deviceSns)) {
                            inventoryDevices = inventoryDeviceMapper.listByDeviceSns(deviceSns);
                            for (InventoryDevice inventoryDevice : inventoryDevices) {
                                if (!deviceSnToTpDeviceIdMap
                                    .containsKey(inventoryDevice.getSerialNumber())) {
                                    deviceSnToTpDeviceIdMap.put(inventoryDevice.getSerialNumber(),
                                        inventoryDevice.getThirdDeviceId());
                                }
                            }
                        }

                        for (DeviceInfoDTO device : devices) {
                            //通知设备名称被修改的事件，主要是通知调用钉钉开放平台接口修改
                            DeviceNameChangeEvent deviceNameChangeEvent = new DeviceNameChangeEvent();
                            BeanUtils.copyProperties(device, deviceNameChangeEvent);
                            deviceNameChangeEvent
                                .setTpDevId(deviceSnToTpDeviceIdMap.get(device.getDeviceSn()));
                            deviceNameChangeEvent.setName(device.showName());
                            deviceNameChangeEvent.setDeviveType(device.getDeviceType());
                            log.info("批量更新,发送设备名称变更消息deviceNameChangeEvent:{}",
                                deviceNameChangeEvent);
                            EventBus.publish(deviceNameChangeEvent);
                        }
                    }
                }
            }
            updateSuccess = Boolean.TRUE;
        }
        return new ServiceResponse<>(updateSuccess);
    }

    @Override
    public ServiceResponse<Boolean> deleteById(Long orgId, Long deviceId) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceManager.deleteById(orgId, deviceId));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Pagination<DeviceInfoDTO>> listPage(QueryDeviceDTO dto) {
       return getDeviceInfo(dto, Boolean.FALSE);
    }

    @Override
    public ServiceResponse<Pagination<DeviceInfoDTO>> listAllDevicePage(QueryDeviceDTO dto) {
        return getDeviceInfo(dto, Boolean.TRUE);
    }


    private ServiceResponse<Pagination<DeviceInfoDTO>> getDeviceInfo(QueryDeviceDTO dto, Boolean allDevice){
        ServiceResponse<Pagination<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        Pagination<DeviceInfoDTO> page = new Pagination<>();
        Pagination<Device> pa = deviceManager.listPage(dto, allDevice);
        if (CollectionUtils.isNotEmpty(pa.getData())) {
            //查询设备电量
            List<Long> deviceIdList = pa.getData().stream().map(Device::getDeviceId).collect(Collectors.toList());
            List<DeviceElectricDTO> deviceElectricDTOList = deviceManager.findElectricByDeviceIdList(deviceIdList);
            List<DeviceInfoDTO> deviceInfoDTOS = deviceToDeviceInfoDTO(pa.getData(), deviceElectricDTOList);
            page.setTotalCount(pa.getTotalCount());
            page.setData(deviceInfoDTOS);
        }
        page.setPageNo(pa.getPageNo());
        page.setPageSize(pa.getPageSize());
        serviceResponse.setData(page);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Pagination<DeviceInfoDTO>> listPageV2(QueryDeviceDTO dto) {
        ServiceResponse<Pagination<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        Pagination<DeviceInfoDTO> page = new Pagination<>();
        Pagination<Device> pa = deviceManager.listPageV2(dto);
        if (CollectionUtils.isNotEmpty(pa.getData())) {
            //查询设备电量
            List<Long> deviceIdList = pa.getData().stream().map(Device::getDeviceId).collect(Collectors.toList());
            List<DeviceElectricDTO> deviceElectricDTOList = deviceManager.findElectricByDeviceIdList(deviceIdList);
            List<DeviceInfoDTO> deviceInfoDTOS = deviceToDeviceInfoDTO(pa.getData(), deviceElectricDTOList);
            page.setTotalCount(pa.getTotalCount());
            page.setData(deviceInfoDTOS);
        }
        page.setPageNo(pa.getPageNo());
        page.setPageSize(pa.getPageSize());
        serviceResponse.setData(page);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Pagination<DeviceInfoDTO>> parkPage(QueryDeviceDTO dto) {
        ServiceResponse<Pagination<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        Pagination<DeviceInfoDTO> page = new Pagination<>();
        Pagination<Device> pa = deviceManager.parkPage(dto);
        if (CollectionUtils.isNotEmpty(pa.getData())) {
            //查询设备电量
            List<Long> deviceIdList = pa.getData().stream().map(Device::getDeviceId).collect(Collectors.toList());
            List<DeviceElectricDTO> deviceElectricDTOList = deviceManager.findElectricByDeviceIdList(deviceIdList);
            List<DeviceInfoDTO> deviceInfoDTOS = deviceToDeviceInfoDTO(pa.getData(), deviceElectricDTOList);
            page.setTotalCount(pa.getTotalCount());
            page.setData(deviceInfoDTOS);
        }
        page.setPageNo(pa.getPageNo());
        page.setPageSize(pa.getPageSize());
        serviceResponse.setData(page);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceInfoDTO> getByOrgIdAndDeviceName(Long orgId, String deviceName) {
        ServiceResponse<DeviceInfoDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        Device device = deviceManager.getByOrgIdAndDeviceName(orgId, deviceName);
        if (device != null) {
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            BeanUtils.copyProperties(device, deviceInfoDTO);
            if (device.getActiveTime() != null){
                deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
            }
            deviceInfoDTO.setDeviceTypeName(deviceTypeNameHelper.getDisplayDeviceTypeName(device.getDeviceSn()));
            serviceResponse.setData(deviceInfoDTO);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Long>> listDeviceIdByLikeName(Long orgId, String keywords) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceManager.listDeviceIdByLikeName(orgId, keywords));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<String>> listDeviceNameByIds(List<Long> deviceIds) {
        ServiceResponse<List<String>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceManager.listDeviceNameByIds(deviceIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Long>> listDeviceIdByOrgIdAndType(Long orgId, Integer deviceType) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceManager.listDeviceIdByOrgIdAndType(orgId, deviceType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByType(Integer deviceType) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByType(deviceType);
        if (CollectionUtils.isNotEmpty(devices)) {
            serviceResponse.setData(deviceToDeviceInfoDTO(devices));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndType(Long orgId, Integer deviceType) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByOrgIdAndType(orgId, deviceType);
        if (CollectionUtils.isNotEmpty(devices)) {
            serviceResponse.setData(deviceToDeviceInfoDTO(devices));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByOrgIdAndTypes(Long orgId,
        List<Integer> deviceTypes) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByOrgIdAndTypes(orgId, deviceTypes);
        if (CollectionUtils.isNotEmpty(devices)) {
            serviceResponse.setData(deviceToDeviceInfoDTO(devices));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByLikeCondition(QueryDeviceDTO queryDeviceDTO) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> devices = deviceManager.listByLikeCondition(queryDeviceDTO);
        if (CollectionUtils.isNotEmpty(devices)) {
            serviceResponse.setData(deviceToDeviceInfoDTO(devices));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> doDeviceCapacityExceeded(Long orgId, List<Long> deviceIds) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(Boolean.TRUE);
        deviceCapacityExceeded(orgId, deviceIds);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceIndustryAppTypeDTO>> listDeviceIndustryAppTypeByDeviceSns(
        List<String> deviceSns) {
        ServiceResponse<List<DeviceIndustryAppTypeDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        QueryWhiteDeviceDTO queryWhiteDeviceDTO = new QueryWhiteDeviceDTO();
        queryWhiteDeviceDTO.setSerialNumbers(deviceSns);
        queryWhiteDeviceDTO.setPageNo(1);
        queryWhiteDeviceDTO.setPageSize(deviceSns.size());
        List<DeviceIndustryAppTypeDTO> list = new ArrayList<>();
        Pagination<InventoryDevice> pageData = whiteDeviceManager.listPage(queryWhiteDeviceDTO);
        log.info("获取设备行业app列表时,对应的设备白名单信息:{}", pageData);
        if (pageData == null || CollectionUtils.isEmpty(pageData.getData())) {
            return serviceResponse;
        }
        InventoryDevice inventoryDevice = pageData.getData().get(0);
        com.xier.sesame.common.rpc.ServiceResponse<CorpDto> corpDto = corpHelperService
            .getCorpByOrgId(inventoryDevice.getOrgId());
        if (corpDto == null || corpDto.getData() == null) {
            return serviceResponse;
        }
        String industry = corpDto.getData().getIndustry();
        for (InventoryDevice inventory : pageData.getData()) {
            //教育行业处理
            if (inventory.getIsEduDevice()) {
                DeviceSn deviceSn = new DeviceSn(inventory.getSerialNumber());
                if (!industryProtectedConfigs.edu.enable) {
                    continue;
                }
                Integer industryAppType = industryProtectedConfigs.edu
                    .getAppType(industry, deviceSn.getDeviceType());
                if (industryAppType != null) {
                    DeviceIndustryAppTypeDTO deviceIndustryAppTypeDTO = new DeviceIndustryAppTypeDTO();
                    deviceIndustryAppTypeDTO
                        .setIndustryName(industryProtectedConfigs.edu.getName());
                    deviceIndustryAppTypeDTO.setDeviceSn(inventory.getSerialNumber());
                    deviceIndustryAppTypeDTO.setAppType(industryAppType);
                    list.add(deviceIndustryAppTypeDTO);
                }
            }
        }
        serviceResponse.setData(list);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceInfoDTO> getByOrgIdAndTpId(Long orgId, String thirdDeviceId) {
        ServiceResponse<DeviceInfoDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        InventoryDevice inventoryDevice = whiteDeviceManager
            .getByOrgIdAndThirdDeviceId(orgId, thirdDeviceId);
        if (inventoryDevice != null) {
            Device device = deviceManager.getByDeviceSn(inventoryDevice.getSerialNumber());
            if (device != null) {
                DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
                BeanUtils.copyProperties(device, deviceInfoDTO);
                if (device.getActiveTime() != null){
                    deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
                }
                deviceInfoDTO.setTpId(inventoryDevice.getThirdDeviceId());
                deviceInfoDTO.setDeviceTypeName(deviceTypeNameHelper.getDisplayDeviceTypeName(device.getDeviceSn()));
                serviceResponse.setData(deviceInfoDTO);
            }
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> listByDeviceSnListAndDeviceName(
        QuerySpecificDeviceDTO dto) {
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> deviceList = deviceManager.listByDeviceSnListAndDeviceName(dto);
        if (CollectionUtils.isEmpty(deviceList)) {
            serviceResponse.setData(Collections.emptyList());
        } else {
            serviceResponse.setData(deviceToDeviceInfoDTO(deviceList));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceDTO> getByOrgIdAndDeviceId(Long orgId, Long deviceId) {
        ServiceResponse<DeviceDTO> successResponse = ServiceResponse.createSuccessResponse();
        Device device = deviceManager.getByOrgIdAndId(orgId, deviceId);
        if (device != null) {
            DeviceDTO deviceDTO = new DeviceDTO();
            deviceDTO.setDeviceId(device.getDeviceId());
            deviceDTO.setOrgId(device.getOrgId());
            deviceDTO.setDeviceType(device.getDeviceType());
            deviceDTO.setDeviceName(device.getDeviceName());
            deviceDTO.setDeviceSn(device.getDeviceSn());
            if (device.getActiveTime() != null) {
                deviceDTO.setActiveTime(device.getActiveTime().getTime());
            }
            deviceDTO.setStatus(device.getStatus());
            deviceDTO.setBluetoothMac(device.getBluetoothMac());
            successResponse.setData(deviceDTO);
        }
        return successResponse;
    }

    @Override
    public ServiceResponse<List<DeviceDTO>> listAllDevices() {
        ServiceResponse<List<DeviceDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceDTO> devices = deviceManager.listAllDevices();
        serviceResponse.setData(devices);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceDTO>> listAllDevicesWithOnlineStatus(boolean onlineStatus) {
        List<DeviceDTO> devices = deviceManager.listAllDevices();
        if (CollectionUtils.isEmpty(devices)) {
            return new ServiceResponse<>(Collections.emptyList());
        }
        List<String> deviceSns = devices.stream().map(DeviceDTO::getDeviceSn)
            .collect(Collectors.toList());
        List<DeviceStatusDTO> deviceStatusDTOS = deviceIotManager
            .listDeviceStateByDeviceSns(deviceSns);
        Map<String, List<DeviceStatusDTO>> networkInfoList = deviceStatusDTOS.stream()
            .collect(Collectors.groupingBy(DeviceStatusDTO::getSerialNumber));
        List<DeviceDTO> result = new ArrayList<>();
        for (DeviceDTO device : devices) {
            List<DeviceStatusDTO> deviceStatusDTOS1 = networkInfoList.get(device.getDeviceSn());
            if (CollectionUtils.isEmpty(deviceStatusDTOS1)) {
                continue;
            }
            Boolean online = deviceStatusDTOS1.get(0).getOnline();
            if (!Objects.equals(online, onlineStatus)) {
                continue;
            }
            device.setOnline(online);
            device.setLastTimeStamp(deviceStatusDTOS1.get(0).getLastTimeStamp());
            result.add(device);
        }
        return new ServiceResponse<>(result);
    }

    @Override
    public ServiceResponse<List<SimpleDeviceInfoDTO>> findAllSimpleDevices(List<Long> orgIds) {
        BizAssert.notEmpty(orgIds);
        List<Device> allDevices = deviceManager.listDevicesByOrgIds(orgIds);
        if (CollectionUtils.isEmpty(allDevices)) {
            return new ServiceResponse<>(Collections.emptyList());
        }
        List<SimpleDeviceInfoDTO> collect = allDevices.stream().map(x -> {
            SimpleDeviceInfoDTO simpleDeviceInfo = new SimpleDeviceInfoDTO();
            simpleDeviceInfo.setOrgId(x.getOrgId());
            simpleDeviceInfo.setDeviceId(x.getDeviceId());
            simpleDeviceInfo.setDeviceSn(x.getDeviceSn());
            simpleDeviceInfo.setDeviceName(x.getDeviceName());
            simpleDeviceInfo.setDeviceType(x.getDeviceType());
            return simpleDeviceInfo;
        }).collect(Collectors.toList());
        return new ServiceResponse<>(collect);
    }

    @Override
    public ServiceResponse<List<Long>> filterViewDeviceIds(ViewDeviceDTO dto) {
        List<Long> deviceIds = dto.getDeviceIds();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ServiceResponse<>(Collections.emptyList());
        }
        Map<Integer, Integer> deviceTypeVersionMap = dto.getDeviceTypeVersionMap();
        if (deviceTypeVersionMap == null || deviceTypeVersionMap.isEmpty()) {
            return new ServiceResponse<>(deviceIds);
        }
        Long orgId = dto.getOrgId();
        List<Device> deviceList = deviceManager.listByOrgIdAndIds(orgId, deviceIds);
        if (CollectionUtils.isEmpty(deviceList)) {
            return new ServiceResponse<>(deviceIds);
        }

        List<Long> returnList = Lists.newArrayList();
        Map<Long, Integer> typeMap = deviceList.stream()
            .collect(Collectors.toMap(Device::getDeviceId, Device::getDeviceType, (o, n) -> n));
        List<Long> ids = deviceList.stream().map(Device::getDeviceId).collect(Collectors.toList());
        if (ids.size() <= DEFAULT_MAX_COUNT) {
            filterDeviceIdsByRules(orgId, deviceTypeVersionMap, typeMap, ids, returnList);
        } else {
            List<List<Long>> partitions = ListUtil.partition(ids, DEFAULT_MAX_COUNT);
            for (List<Long> partition : partitions) {
                filterDeviceIdsByRules(orgId, deviceTypeVersionMap, typeMap, partition, returnList);
            }
        }
        return new ServiceResponse<>(returnList);
    }

    @Override
    public ServiceResponse<List<DeviceWithVersionInfoDTO>> listDeviceWithVersionInfo(
        QueryDeviceWithVersionDTO dto) {
        BizAssert.notNull(dto, "QueryDeviceWithVersionRequest must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        return new ServiceResponse<>(deviceManager.listDeviceWithVersionInfo(dto));
    }

    @Override
    public ServiceResponse<List<Integer>> findAllDeviceTypesByOrgId(Long orgId) {
        List<Integer> deviceTypes = deviceManager.findAllDeviceTypesByOrgId(orgId);
        return new ServiceResponse<>(deviceTypes);
    }

    @Override
    public ServiceResponse<VersionInfoDTO> getVersionInfoByDeviceId(Long orgId,
        Long deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        Device device = deviceManager.getByOrgIdAndId(orgId, deviceId);
        BizAssert.notNull(device, DeviceErrorCode.DEVICE_NOT_EXIST,
            DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());

        VersionInfoDTO versionInfoResponse = new VersionInfoDTO();
        List<DeviceVersionDTO> appInfo = deviceVersionService.listApkByOrgIdAndDeviceIds(
            orgId, Lists.newArrayList(deviceId)).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(appInfo)) {
            DeviceVersionDTO appVersionDto = appInfo.get(0);
            versionInfoResponse.setAppType(appVersionDto.getAppType());
            versionInfoResponse.setAppVersionCode(appVersionDto.getVersionCode());
        }
        List<DeviceVersionDTO> roomInfo = deviceVersionService.listApkByOrgIdAndDeviceIds(
            orgId, Lists.newArrayList(deviceId)).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(roomInfo)) {
            DeviceVersionDTO versionDto = roomInfo.get(0);
            versionInfoResponse.setRomType(versionDto.getAppType());
            versionInfoResponse.setRomVersionCode(versionDto.getVersionCode());
        }
        return new ServiceResponse<>(versionInfoResponse);
    }

    private void filterDeviceIdsByRules(Long orgId, Map<Integer, Integer> deviceTypeVersionMap,
        Map<Long, Integer> typeMap, List<Long> ids, List<Long> returnList) {
        List<DeviceVersionDTO> versions = deviceVersionService
            .listApkByOrgIdAndDeviceIds(orgId, ids).pickDataThrowException();
        Map<Long, Integer> versionMap = versions.stream().filter(x -> x.getVersionCode() != null)
            .collect(Collectors
                .toMap(DeviceVersionDTO::getDeviceId, DeviceVersionDTO::getVersionCode,
                    (o, n) -> n));
        for (Entry<Long, Integer> entry : typeMap.entrySet()) {
            Long key = entry.getKey();
            Integer type = entry.getValue();
            if (deviceTypeVersionMap.containsKey(type)) {
                Integer compareVersion = deviceTypeVersionMap.get(type);
                Integer currentVersion = Optional.ofNullable(versionMap.get(key)).orElse(0);
                if (currentVersion > compareVersion) {
                    returnList.add(key);
                }
            }
        }
    }

    @Override
    public ServiceResponse<Boolean> reportDeviceElectric(ReportDeviceElectricDTO deviceElectricDto) {
        BizAssert.notNull(deviceElectricDto, "dto must not be null");
        BizAssert.notNull(deviceElectricDto.getDeviceId(), "deviceId must not be blank");
        BizAssert.notBlank(deviceElectricDto.getDeviceSn(), "deviceSn must not be blank");
        BizAssert.notNull(deviceElectricDto.getElectric(), "deviceElectric must not be null");
        Device exist = deviceManager.getById(deviceElectricDto.getDeviceId());
        BizAssert.notNull(exist, "device not exist");
        BizAssert.isTrue(exist.getDeviceSn().equals(deviceElectricDto.getDeviceSn()), "deviceSn error");
        return new ServiceResponse<>(deviceManager.reportDeviceElectric(deviceElectricDto));
    }

    @Override
    public ServiceResponse<List<DeviceElectricDTO>> findElectricByDeviceIdList(List<Long> deviceIdList) {
        return new ServiceResponse<>(deviceManager.findElectricByDeviceIdList(deviceIdList));
    }

    /**
     * 对象转换
     *
     * @param devices 设备信息列表
     * @return
     */
    private List<DeviceInfoDTO> deviceToDeviceInfoDTO(List<Device> devices) {
        List<DeviceInfoDTO> deviceInfoDTOs = new ArrayList<>();
        Long orgId = devices.get(0).getOrgId();
        List<Long> deviceIds = devices.stream()
            .map(Device::getDeviceId).collect(Collectors.toList());
        Map<Long, TreeDeviceRelationDTO> map = new HashMap<>();
        Map<Long, Integer> deviceIdToSceneTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceIds)){
            List<TreeDeviceRelationDTO> relationDTOS = treeDeviceRelationServiceV2
                .listByDeviceIdsWithoutOnlieStatus(orgId, deviceIds).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOS)) {
                map = relationDTOS.stream().collect(Collectors.toMap(TreeDeviceRelationDTO::getDeviceId,
                    Function.identity()));
            }

            DevicePipelineStateSelectReq req = new DevicePipelineStateSelectReq();
            req.setOrgId(orgId);
            req.setDeviceIdList(deviceIds);
            List<DevicePipelineStateListResp> dps = devicePipelineStateService.selectDeviceByCondition(req).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(dps)){
                deviceIdToSceneTypeMap = dps.stream().collect(Collectors.toMap(DevicePipelineStateListResp::getDeviceId, DevicePipelineStateListResp::getSceneType));
            }
        }
        //查询设备类型名称
        List<String> deviceSns = devices.stream().map(Device::getDeviceSn).distinct().collect(Collectors.toList());
        Map<String, String> deviceTypeNameMap = deviceTypeNameHelper.ListDisplayDeviceTypeName(deviceSns);
        for (Device device : devices) {
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            BeanUtils.copyProperties(device, deviceInfoDTO);
            if (device.getActiveTime() != null){
                deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
            }
            if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
                deviceInfoDTO.setTpId(device.getThirdDeviceId());
            }
            TreeDeviceRelationDTO relationDTO = map.get(device.getDeviceId());
            if (relationDTO != null) {
                deviceInfoDTO.setTreeId(relationDTO.getTreeId());
                deviceInfoDTO.setPath(relationDTO.getPath());
                if (StringUtils.isNotBlank(relationDTO.getPathTreeName())){
                    deviceInfoDTO.setPathTreeName(relationDTO.getPathTreeName());
                    deviceInfoDTO.setPropertyTreeName(relationDTO.getPropertyTreeName());
                }
                deviceInfoDTO.setPropertyTreeNodeId(relationDTO.getPropertyTreeNodeId());
                deviceInfoDTO.setPropertyTreeName(relationDTO.getPropertyTreeName());
                deviceInfoDTO.setDirection(relationDTO.getDirection());
                deviceInfoDTO.setParentTreeNodeId(relationDTO.getParentTreeNodeId());
            }
            deviceInfoDTO.setSceneType(deviceIdToSceneTypeMap.get(device.getDeviceId()));
            deviceInfoDTO.setPosition(device.getPosition() != null ? device.getPosition() : "");
            String deviceTypeName = deviceTypeNameMap.get(device.getDeviceSn());
            if (StringUtils.isNotBlank(deviceTypeName)) {
                deviceInfoDTO.setDeviceTypeName(deviceTypeName);
            }
            deviceInfoDTOs.add(deviceInfoDTO);
        }
        //元石分销环境处理pathTreeName
        handlePathTreeName(deviceInfoDTOs);
        Map<String, Integer> deviceIdToDeviceCapicatyMap = Maps.newHashMap();
        List<String> deviceSnList = devices.stream().map(Device::getDeviceSn).collect(Collectors.toList());
        QueryDeviceProjectInfoRequest request = new QueryDeviceProjectInfoRequest();
        request.setDeviceSnList(deviceSnList);
        List<DeviceProjectInfoResponse> responses = deviceProjectInfoService.findDeviceProjectInfo(request).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(responses)) {
            for (DeviceProjectInfoResponse datum : responses) {
                if (StringUtils.isNotBlank(datum.getProjectInfo())) {
                    JSONObject jsonObject = JSON.parseObject(datum.getProjectInfo());
                    if (jsonObject.containsKey(ParamConstants.CUSTOM_PARAMS)) {
                        JSONObject customParams = jsonObject.getJSONObject(ParamConstants.CUSTOM_PARAMS);
                        if (customParams != null && customParams.containsKey(ParamConstants.FACE_LIMIT)) {
                            String faceLimit = customParams.getString(ParamConstants.FACE_LIMIT);
                            if (StringUtils.isNotBlank(faceLimit)) {
                                deviceIdToDeviceCapicatyMap.put(datum.getDeviceSn(), Integer.parseInt(faceLimit));
                            }
                        }
                    }
                }
            }
        }
        List<DeviceCapacityDTO> deviceListCapacity = deviceTypeMapManager.getDeviceListCapacity(
            devices.stream().map(Device::getDeviceType).distinct().collect(
                Collectors.toList()));
        Map<Integer, Integer> capacityMap = ObjectUtils.isEmpty(deviceListCapacity) ? Maps.newHashMap() :
            deviceListCapacity.stream().collect(Collectors.toMap(DeviceCapacityDTO::getDeviceType,
                DeviceCapacityDTO::getDeviceCapacity, (o1, o2) -> o1));
        for (DeviceInfoDTO dto : deviceInfoDTOs) {
            // 先去projectInfo 再取device_type_map表 再取枚举类
            dto.setDeviceCapacity(Optional.ofNullable(deviceIdToDeviceCapicatyMap.get(dto.getDeviceSn()))
                    .orElse(Optional.ofNullable(capacityMap.get(dto.getDeviceType()))
                        .orElse(DeviceType.getDeviceCapacity(dto.getDeviceType()))));
        }
        return deviceInfoDTOs;
    }

    /**
     * 对象转换(带电量)
     *
     * @param devices 设备信息列表
     * @return
     */
    private List<DeviceInfoDTO> deviceToDeviceInfoDTO(List<Device> devices, List<DeviceElectricDTO> deviceElectricList) {
        Map<String, DeviceElectricDTO> deviceSnToElectricMap = null;
        if (CollectionUtils.isNotEmpty(deviceElectricList)) {
            deviceSnToElectricMap = deviceElectricList.stream().collect(Collectors.toMap(DeviceElectricDTO::getDeviceSn, Function.identity()));
        }
        List<DeviceInfoDTO> deviceInfoDTOs = new ArrayList<>();
        Long orgId = devices.get(0).getOrgId();
        List<Long> deviceIds = devices.stream().map(Device::getDeviceId).collect(Collectors.toList());
        Map<Long, TreeDeviceRelationDTO> map = new HashMap<>();
        Map<Long, Integer> deviceIdToSceneTypeMap = new HashMap<>();
        Map<Long, TreeDTO> treeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            List<TreeDeviceRelationDTO> relationDTOS = treeDeviceRelationServiceV2
                    .listByDeviceIdsWithoutOnlieStatus(orgId, deviceIds).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOS)) {
                map = relationDTOS.stream().collect(Collectors.toMap(TreeDeviceRelationDTO::getDeviceId,
                        Function.identity()));
                List<Long> treeIds = relationDTOS.stream().map(TreeDeviceRelationDTO::getTreeId)
                    .filter(treeId -> !Objects.isNull(treeId)).distinct().collect(
                    Collectors.toList());
                if (!ObjectUtils.isEmpty(treeIds)) {
                    List<TreeDTO> treeDTOS = spaceTreeServiceV2.listByIdsAndOrgId(orgId, treeIds)
                        .pickDataThrowException();
                    if (CollectionUtils.isEmpty(treeDTOS)) {
                        treeMap = new HashMap<>();
                    } else {
                        for (TreeDTO treeDTO : treeDTOS) {
                            if (SpaceTreeNodeType.TREE.getCode().equals(treeDTO.getNodeType())) {
                                treeMap.put(treeDTO.getTreeId(), treeDTO);
                            } else if (SpaceTreeNodeType.PROPERTY.getCode().equals(treeDTO.getNodeType())) {
                                treeMap.put(treeDTO.getPropertyTreeNodeId(), treeDTO);
                            }
                        }
                    }
//                    treeMap = ObjectUtils.isEmpty(treeDTOS) ? new HashMap<>() :
//                        treeDTOS.stream().collect(Collectors.toMap(TreeDTO::getTreeId, Function.identity()));
                }
            }
            DevicePipelineStateSelectReq req = new DevicePipelineStateSelectReq();
            req.setOrgId(orgId);
            req.setDeviceIdList(deviceIds);
            List<DevicePipelineStateListResp> dps = devicePipelineStateService.selectDeviceByCondition(req).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(dps)){
                deviceIdToSceneTypeMap = dps.stream().collect(Collectors.toMap(DevicePipelineStateListResp::getDeviceId, DevicePipelineStateListResp::getSceneType));
            }
        }
        List<DeviceCapacityDTO> deviceListCapacity = deviceTypeMapManager.getDeviceListCapacity(
            devices.stream().map(Device::getDeviceType).distinct().collect(
                Collectors.toList()));
        Map<Integer, Integer> capacityMap = ObjectUtils.isEmpty(deviceListCapacity) ? Maps.newHashMap() :
            deviceListCapacity.stream().collect(Collectors.toMap(DeviceCapacityDTO::getDeviceType,
                DeviceCapacityDTO::getDeviceCapacity, (o1, o2) -> o1));
        Map<String, Integer> deviceIdToDeviceCapicatyMap = Maps.newHashMap();
        List<String> deviceSnList = devices.stream().map(Device::getDeviceSn).collect(Collectors.toList());
        QueryDeviceProjectInfoRequest request = new QueryDeviceProjectInfoRequest();
        request.setDeviceSnList(deviceSnList);
        List<DeviceProjectInfoResponse> responses = deviceProjectInfoService.findDeviceProjectInfo(request).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(responses)) {
            for (DeviceProjectInfoResponse datum : responses) {
                if (StringUtils.isNotBlank(datum.getProjectInfo())) {
                    JSONObject jsonObject = JSON.parseObject(datum.getProjectInfo());
                    if (jsonObject.containsKey(ParamConstants.CUSTOM_PARAMS)) {
                        JSONObject customParams = jsonObject.getJSONObject(ParamConstants.CUSTOM_PARAMS);
                        if (customParams != null && customParams.containsKey(ParamConstants.FACE_LIMIT)) {
                            String faceLimit = customParams.getString(ParamConstants.FACE_LIMIT);
                            if (StringUtils.isNotBlank(faceLimit)) {
                                deviceIdToDeviceCapicatyMap.put(datum.getDeviceSn(), Integer.parseInt(faceLimit));
                            }
                        }
                    }
                }
            }
        }

        //查询设备类型名称
        List<String> deviceSns = devices.stream().map(Device::getDeviceSn).distinct().collect(Collectors.toList());
        Map<String, String> deviceTypeNameMap = deviceTypeNameHelper.ListDisplayDeviceTypeName(deviceSns);
        for (Device device : devices) {
            DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
            BeanUtils.copyProperties(device, deviceInfoDTO);
            if (device.getActiveTime() != null) {
                deviceInfoDTO.setActiveTime(device.getActiveTime().getTime());
            }
            if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
                deviceInfoDTO.setTpId(device.getThirdDeviceId());
            }
            TreeDeviceRelationDTO relationDTO = map.get(device.getDeviceId());
            if (relationDTO != null) {
                deviceInfoDTO.setTreeId(relationDTO.getTreeId());
                deviceInfoDTO.setPathTreeName(relationDTO.getPathTreeName());
                TreeDTO treeDTO = treeMap.get(relationDTO.getTreeId());
                if (!Objects.isNull(treeDTO)) {
                    deviceInfoDTO.setPath(treeDTO.getPath());
                }
                //设置出入口方向
                deviceInfoDTO.setDirection(relationDTO.getDirection());
                deviceInfoDTO.setPropertyTreeName(relationDTO.getPropertyTreeName());
                deviceInfoDTO.setPropertyTreeNodeId(relationDTO.getPropertyTreeNodeId());
                deviceInfoDTO.setParentTreeNodeId(relationDTO.getParentTreeNodeId());
            }
            if (deviceSnToElectricMap != null && deviceSnToElectricMap.containsKey(device.getDeviceSn())) {
                DeviceElectricDTO deviceElectricDTO = deviceSnToElectricMap.get(device.getDeviceSn());
                if (deviceElectricDTO != null) {
                    deviceInfoDTO.setElectric(deviceElectricDTO.getElectric());
                    deviceInfoDTO.setLatestReportElectric(deviceElectricDTO.getGmtModify());
                }
            }
            // 先去projectInfo 再取device_type_map表 再取枚举类
            deviceInfoDTO.setDeviceCapacity(Optional.ofNullable(deviceIdToDeviceCapicatyMap.get(device.getDeviceSn()))
                .orElse(Optional.ofNullable(capacityMap.get(device.getDeviceType()))
                    .orElse(DeviceType.getDeviceCapacity(device.getDeviceType()))));
            deviceInfoDTO.setSceneType(deviceIdToSceneTypeMap.get(device.getDeviceId()));
            deviceInfoDTO.setPosition(device.getPosition() != null ? device.getPosition() : "");
            //设置关联应用
            deviceInfoDTO.setDeviceRelatedApp(buildDeviceRelateApp(device));
            String deviceTypeName = deviceTypeNameMap.get(device.getDeviceSn());
            if (StringUtils.isNotBlank(deviceTypeName)) {
                deviceInfoDTO.setDeviceTypeName(deviceTypeName);
            }
            deviceInfoDTOs.add(deviceInfoDTO);
        }

        List<DeviceVersionInfoDTO> deviceVersionInfoDTOS = new ArrayList<>();
        //获取设备SN对应的appType和version
        if (CollectionUtils.isNotEmpty(deviceSnList)) {
            deviceVersionInfoDTOS = deviceVersionService.getBatchDeviceApkVersionFromDatabase(deviceSnList).pickDataThrowException();
        }
        Map<String, DeviceVersionInfoDTO> deviceSnToAppTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceVersionInfoDTOS)) {
            deviceSnToAppTypeMap = deviceVersionInfoDTOS.stream().collect(Collectors.toMap(DeviceVersionInfoDTO::getDeviceSn, Function.identity()));
        }
        for (DeviceInfoDTO deviceInfoDTO : deviceInfoDTOs) {
            String deviceSn = deviceInfoDTO.getDeviceSn();
            if (!deviceSnToAppTypeMap.containsKey(deviceSn)) {
                continue;
            }
            DeviceVersionInfoDTO deviceVersionInfoDTO = deviceSnToAppTypeMap.get(deviceSn);
            Integer appType = deviceVersionInfoDTO.getAppType();
            Integer versionCode = deviceVersionInfoDTO.getVersionCode();

            //获取设备配置的运行环境
            String environment = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE, BeeConfConfigConstants.GROUP_ENVIRONMENT, appType.toString());
            if (StringUtils.isNotBlank(environment)) {
                deviceInfoDTO.setEnvironment(Integer.valueOf(environment));
                //元石分销环境 pathTreeName设置为空
                if (EnvConfigEnum.FX.getCode().equals(Integer.valueOf(environment))) {
                    deviceInfoDTO.setPathTreeName("");
                    deviceInfoDTO.setPosition(StringUtils.isNotBlank(deviceInfoDTO.getPosition()) ? deviceInfoDTO.getPosition() : deviceInfoDTO.getDeviceName());
                }
            } else {
                deviceInfoDTO.setEnvironment(EnvConfigEnum.HY.getCode());
            }
            // BeeConfNewClient 配置详解 @see BeeConfConfigConstants
            //查询appType 和 version对应的设备模式 BeeConfConfigConstants数据在内存中，可用for循环获取
            String deviceModel = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE, BeeConfConfigConstants.GROUP_DEVICE_MODULE,
                    appType.toString(), versionCode.toString());
            if (StringUtils.isNotBlank(deviceModel)) {
                List<Integer> deviceModelList = JSON.parseArray(deviceModel, Integer.class);
                //设备当前运行场景类型
                if (deviceModelList.contains(deviceInfoDTO.getSceneType())) {
                    //查询设备模式对应的应用
                    Integer deviceCurrentModel = deviceInfoDTO.getSceneType();
                    String appListJson = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE, BeeConfConfigConstants.GROUP_DEVICE_APP,
                            appType + "-" + versionCode, deviceCurrentModel.toString());
                    List<String> appList = JSON.parseArray(appListJson, String.class);
                    if (CollectionUtils.isNotEmpty(appList)) {
                        deviceInfoDTO.setDeviceModelAppCodeList(appList);
                    }
                }
            }
        }
        return deviceInfoDTOs;
    }

    @Override
    public ServiceResponse<ImagePathDTO> saveImage(SaveImageDTO saveImageDTO) {
        BizAssert.notNull(saveImageDTO, "Byte picture arrays cannot be null");
        byte[] image = saveImageDTO.getImage();
        String imagePath = this.storeImage(image, FilePathType.TYPE_TEMPORARY,
            StringUtils.isBlank(saveImageDTO.getSuffix()) ? ".jpg" : saveImageDTO.getSuffix());
        ImagePathDTO imagePathDTO = new ImagePathDTO();
        imagePathDTO.setImagePath(imagePath);
        return new ServiceResponse<>(imagePathDTO);
    }

    @Override
    public ServiceResponse<DeviceHasPersonDTO> checkDeviceHasPerson(Long deviceId, Long orgId) {
        BizAssert.notNull(deviceId, "deviceId must not be null");
        BizAssert.notNull(orgId, "orgId must not be null");
        ServiceResponse<DeviceHasPersonDTO> result = ServiceResponse.createSuccessResponse();
        DeviceHasPersonDTO dto = new DeviceHasPersonDTO();
        dto.setDeviceId(deviceId);
        dto.setOrgId(orgId);
        TreeDeviceRelationDTO relationDTO = treeDeviceRelationService.getByOrgIdAndDeviceId(
            orgId, String.valueOf(deviceId)).pickDataThrowException();
        if (Objects.isNull(relationDTO)) {
            log.info("找不到对应空间关系，deviceId:[{}]，orgId:[{}]", deviceId, orgId);
            dto.setHasPerson(null);
            result.setData(dto);
            return result;
        }
        List<TreeDeviceRelationDTO> relations = treeDeviceRelationService.listByTreeIdAndSource(
            orgId, relationDTO.getTreeId(), 1).pickDataThrowException();
        if (ObjectUtils.isEmpty(relations)) {
            log.info("空间下无设备，treeId:[{}]，orgId:[{}]", relationDTO.getTreeId(), orgId);
            dto.setHasPerson(null);
            result.setData(dto);
            return result;
        }
        dto.setHasPerson(checkHasPerson(relations, deviceId, orgId));
        result.setData(dto);
        return result;
    }


    @Override
    public ServiceResponse<DeviceActiveJumpAppConfigDTO> getDeviceJumpLinkAfterActive(Long orgId, String deviceSn) {

        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceSn, "deviceSn must not be null");

        DeviceActiveJumpAppConfigDTO deviceActiveJumpAppConfigDTO = new DeviceActiveJumpAppConfigDTO();

        //获取设备信息
        Device device = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.notNull(device, "设备不存在");
        //先看设备的主表里面是否已经存在appcode等信息，如果存在就直接根据appcode去查找跳转链接返回即可
        if (StringUtils.isNotBlank(device.getAppCode())) {
//            DeviceActiveJumpAppConfig config = deviceActiveJumpAppConfigManager.getByAppCode(device.getAppCode());
//            BizAssert.notNull(config, "设备默认跳转应用链接未配置!");
            deviceActiveJumpAppConfigDTO.setAppCode(device.getAppCode());
//            deviceActiveJumpAppConfigDTO.setJumpLink(config.getJumpLink());
            return new ServiceResponse<>(deviceActiveJumpAppConfigDTO);
        }

        //如果设备主表里面不存在,再去固定配置表里找一遍
        DeviceAppRelationFixedConfig fixedConfig = deviceAppRelationFixedConfigManager.getByBizTypeAndId(
                DeviceRelateAppFixedBIzTypeEnum.DEVICE_SN.getCode(),device.getDeviceSn());
        if (fixedConfig != null) {
            //存在固定配置
//            DeviceActiveJumpAppConfig config = deviceActiveJumpAppConfigManager.getByAppCode(fixedConfig.getDefaultAppCode());
            deviceActiveJumpAppConfigDTO.setAppCode(fixedConfig.getDefaultAppCode());
//            deviceActiveJumpAppConfigDTO.setJumpLink(config.getJumpLink());
            return new ServiceResponse<>(deviceActiveJumpAppConfigDTO);
        }

        //如果固定配置表为空，从默认模版表里面去找
        //查询设备版本信息
        List<DeviceCurrVersionDTO> deviceCurrVersionDTOS = deviceVersionService.listByOrgIdAndDeviceIds(orgId,
                Collections.singletonList(device.getDeviceId())).pickDataThrowException();

        //版本不存在，则直接返回空
        if (CollectionUtils.isNotEmpty(deviceCurrVersionDTOS)) {
            return new ServiceResponse<>(deviceActiveJumpAppConfigDTO);
        }
        DeviceCurrVersionDTO deviceCurrVersionDTO = deviceCurrVersionDTOS.get(0);
        //查找空间类型
        Integer spaceType = getSpaceTypeByDeviceId(orgId, device.getDeviceId());
        //如果spaceType 、appType versionCode都不为空
        if (spaceType != null && deviceCurrVersionDTO.getCurrAppVersion().getAppType() != null
                && deviceCurrVersionDTO.getCurrAppVersion().getVersionCode() != null) {

            List<DeviceAppRelationConfig> deviceAppRelationConfigs = deviceAppRelationConfigManager.selectBySpaceTypeAndAppTypeAndVersionCode(
                    spaceType, deviceCurrVersionDTO.getCurrAppVersion().getAppType(),
                    deviceCurrVersionDTO.getCurrAppVersion().getVersionCode());

            if (CollectionUtils.isNotEmpty(deviceAppRelationConfigs)) {
                //取第一条
                DeviceAppRelationConfig deviceAppRelationConfig = deviceAppRelationConfigs.get(0);
                //查询跳转链接
//                DeviceActiveJumpAppConfig config = deviceActiveJumpAppConfigManager.getByAppCode(deviceAppRelationConfig.getDefaultAppCode());

                deviceActiveJumpAppConfigDTO.setAppCode(deviceAppRelationConfig.getDefaultAppCode());
//                deviceActiveJumpAppConfigDTO.setJumpLink(config.getJumpLink());
            }
        }
        return new ServiceResponse<>(deviceActiveJumpAppConfigDTO);
    }

    @Override
    public ServiceResponse<List<String>> supportAuthDeviceManagementList(Long orgId) {
        // 只有支持通过权限配置进入设备管理后台的设备才可以发送消息
        List<Integer> deviceTypes = deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.SUPPORT_AUTH_DEVICE_MANAGEMENT_LIST);
        if (CollectionUtils.isEmpty(deviceTypes)) {
            // 没有一个设备类型配置了支持权限勾选进入设备管理后台功能
            return new ServiceResponse<>(Collections.emptyList());
        }
        List<Device> devices = deviceManager.listByOrgIdAndTypes(orgId,deviceTypes);
        if (CollectionUtils.isEmpty(devices)) {
            return new ServiceResponse<>(Collections.emptyList());
        }
        // 只返回SN即可
        List<String> deviceSnList = devices.stream().map(Device::getDeviceSn).distinct()
            .collect(Collectors.toList());
        return new ServiceResponse<>(deviceSnList);
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    private void deviceCapacityExceeded(Long orgId, List<Long> deviceIds) {
        if (deviceIds.isEmpty()) {
            log.info("设备Id列表不存在,放弃处理超限deviceIds:{}", deviceIds);
            return;
        }
        List<Device> devices = deviceManager.listByIds(deviceIds);
        if (devices.isEmpty()) {
            log.info("设备信息不存在,放弃处理超限devices:{}", devices);
            return;
        }

        //考勤机只统计考勤权限组的设备容量
        List<Long> attendanceDeviceIdList = devices.stream()
            .filter(d -> deviceTypePropertyManager.getDeviceTypeByPropertyKey(
                DeviceTypeConstants.ROOSTER_DEVICE_LIST).contains(d.getDeviceType()))
            .map(Device::getDeviceId).collect(Collectors.toList());
        List<Long> otherDeviceIdList = devices.stream()
            .filter(d -> !deviceTypePropertyManager.getDeviceTypeByPropertyKey(
                DeviceTypeConstants.ROOSTER_DEVICE_LIST).contains(d.getDeviceType()))
            .map(Device::getDeviceId).collect(Collectors.toList());
        //考勤机设备id->考勤组
        Map<Long/*deviceId*/, List<Long>/*groupId*/> attendanceMap = deviceGroupRelationService
            .getDeviceIdToGroupIdsByOrgIdAndDeviceIds(orgId, attendanceDeviceIdList,
                GroupAppType.MO_ATTENDANCE.getValue()).pickDataThrowException();
        //其它设备id->权限组
        Map<Long/*deviceId*/, List<Long>/*groupId*/> otherMap = deviceGroupRelationService
            .getDeviceIdToGroupIdsByOrgIdAndDeviceIds(orgId, otherDeviceIdList,
                GroupAppType.GATE.getValue()).pickDataThrowException();
        List<UpdateDeviceDTO> deviceDTOS = new ArrayList<>();
        for (Device device : devices) {
            Integer personSize = 0;
            List<Long> groupIdList = null;
            if (deviceTypePropertyManager
                .getDeviceTypeByPropertyKey(DeviceTypeConstants.ROOSTER_DEVICE_LIST)
                .contains(device.getDeviceType())) {
                if (attendanceMap != null) {
                    groupIdList = attendanceMap.get(device.getDeviceId());
                }
            } else {
                if (otherMap != null) {
                    groupIdList = otherMap.get(device.getDeviceId());
                }
            }
            if (CollectionUtils.isNotEmpty(groupIdList)) {
                personSize = groupRelationService
                    .getDistinctPersonSize(orgId, groupIdList)
                    .pickDataThrowException();
            }
            Integer deviceCapacity = deviceTypeMapManager.getDeviceCapacity(device.getDeviceType());
            if (deviceCapacity != null) {
                UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
                updateDeviceDTO.setOrgId(device.getOrgId());
                updateDeviceDTO.setDeviceId(device.getDeviceId());
                if (deviceCapacity < personSize) {
                    //对数据库中非超限设备进行超限状态的变更
                    if (device.getCapacityExceeded() == null || YesNoFlag.YES.getValue() != device
                        .getCapacityExceeded()) {
                        updateDeviceDTO.setCapacityExceeded(YesNoFlag.YES.getValue());
                        deviceDTOS.add(updateDeviceDTO);
                    }
                } else {
                    //对数据库中超限设备进行超限状态的变更
                    if (device.getCapacityExceeded() == null || YesNoFlag.YES.getValue() == device
                        .getCapacityExceeded()) {
                        updateDeviceDTO.setCapacityExceeded(YesNoFlag.NO.getValue());
                        deviceDTOS.add(updateDeviceDTO);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(deviceDTOS)) {
            deviceManager.batchUpdate(deviceDTOS);
            log.info("设备超限处理完成:{}", deviceDTOS);
        }
    }

    /**
     *
     * @param orgId orgId
     * @param deviceId deviceId
     * @return spaceType，如果不存在返回null
     */
     Integer getSpaceTypeByDeviceId(Long orgId,Long deviceId){
         //查询空间类型
         List<TreeDeviceRelationDTO> relationDTOs = treeDeviceRelationServiceV2.listByDeviceIdsWithoutOnlieStatus(
                 orgId, Lists.newArrayList(deviceId)).pickDataThrowException();
         //设备未绑空间 不进行修改
         if (CollectionUtils.isEmpty(relationDTOs)){
             log.warn("设备未绑空间");
             return null;
         }
         TreeDeviceRelationDTO relationDTO = relationDTOs.get(0);
         TreeDTO treeDTO = spaceTreeService.getById(relationDTO.getTreeId(), orgId).pickDataThrowException();
         return treeDTO.getTags().get(0).getSpaceType();
     }


    /**
     * 设备关联应用
     * @param device 设备
     * @return 应用集合
     */
     private List<DeviceRelateAppDTO> buildDeviceRelateApp(Device device) {
         String appCodeList = device.getAppCodeList();
         if (StringUtils.isNotBlank(appCodeList)) {
             List<String> deviceAppCodeList = Arrays.stream(StringUtils.split(appCodeList, ",")).collect(Collectors.toList());
             return deviceAppCodeList.stream().map(appCode -> {
                 DeviceRelateAppDTO deviceRelateAppDTO = new DeviceRelateAppDTO();
                 deviceRelateAppDTO.setDefaultFlag(appCode.equals(device.getAppCode()));
                 deviceRelateAppDTO.setAppCode(appCode);
                 return deviceRelateAppDTO;
             }).collect(Collectors.toList());
         }
         return new ArrayList<>();
     }

    /**
     * 检查人体感应设备是否监测到有人
     * @param relations
     * @return
     */
    private Boolean checkHasPerson(List<TreeDeviceRelationDTO> relations, Long deviceId, Long orgId) {
        if (ObjectUtils.isEmpty(relations)) {
            return null;
        }
        List<Long> deviceIds = relations.stream().map(TreeDeviceRelationDTO::getDeviceId).collect(
            Collectors.toList());
        deviceIds.remove(deviceId);
        if (ObjectUtils.isEmpty(deviceIds)) {
            return null;
        }
        List<Device> devices = deviceManager.listByOrgIdAndIds(orgId, deviceIds);
        if (ObjectUtils.isEmpty(devices)) {
            return null;
        }
        List<Device> bodyInductionDevices = devices.stream().filter(device ->
            DeviceType.isBodyInductionDevice(device.getDeviceType())).collect(
            Collectors.toList());
        if (ObjectUtils.isEmpty(bodyInductionDevices)) {
            log.info("空间下无人体感应设备，orgId:[{}]，treeId:[{}]", orgId, relations.get(0).getTreeId());
            return null;
        }
        for (Device device : bodyInductionDevices) {
            List<IotDevicePropertyInfoDTO> properties = iotDevicePropertyService.getLatestDevicePropertyListByDeviceId(
                orgId, device.getParentDeviceSn()).pickDataThrowException();
            Long noMotionTimeStamp = 0L;
            Long motionTimeStamp = 0L;
            if (ObjectUtils.isEmpty(properties)) {
                continue;
            }
            for (IotDevicePropertyInfoDTO property : properties) {
                if (!StringUtils.isEmpty(property.getProperty()) &&
                    Objects.equals(DeviceIotPropertyConstants.MOTION_PROPERTY, property.getProperty()) &&
                    Objects.equals(DeviceIotPropertyConstants.MOTION_FORMAT_VALUE, property.getFormatValue())) {
                    motionTimeStamp = property.getTimestamp();
                }
                if (!StringUtils.isEmpty(property.getProperty()) &&
                    Objects.equals(DeviceIotPropertyConstants.NO_MOTION_PROPERTY, property.getProperty()) &&
                    Objects.equals(DeviceIotPropertyConstants.NO_MOTION_FORMAT_VALUE, property.getFormatValue())) {
                    noMotionTimeStamp = property.getTimestamp();
                }
            }
            if (motionTimeStamp > 0L && motionTimeStamp >= noMotionTimeStamp) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    private String storeImage(byte[] image, FilePathType filePathType,String suffix) {

        Map<String, Object> map = null;
        String fileName = RandomUtil.getUUID() + suffix;
        try {
            map = imageFileManager.saveImage(image, filePathType, fileName, null);
        } catch (Exception e) {
            log.error(e.getMessage());
            ExceptionUtils.throwException(MemberErrorCode.SAVE_IMAGE_ERROR,
                MemberErrorCode.SAVE_IMAGE_ERROR.getMessage());
        }
        if (!BaseResponse.SUCCESS_RESULT.equals(map.get("result"))) {
            log.error("SAVE_IMAGE_ERROR......");
            ExceptionUtils.throwException(MemberErrorCode.SAVE_IMAGE_WRONG,
                MemberErrorCode.SAVE_IMAGE_WRONG.getMessage());
        }

        return (String) map.get("url");
    }
}