<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceNetworkInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceNetworkInfo">
        <id column="id" property="id" />
        <result column="org_id" property="orgId" />
        <result column="device_id" property="deviceId" />
        <result column="network_type" property="networkType" />
        <result column="connect_type" property="connectType" />
        <result column="cable_static" property="cableStatic" />
        <result column="wifi_info" property="wifiInfo" />
        <result column="wired_mac" property="wiredMac" />
        <result column="wifi_mac" property="wifiMac" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <sql id="sql_table">
        hive_device_network_info
    </sql>

    <sql id="sql_columns">
        id,
        org_id,
        device_id,
        network_type,
        connect_type,
        cable_static,
        wifi_info,
        wired_mac,
        wifi_mac,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{id},
        #{orgId},
        #{deviceId},
        #{networkType},
        #{connectType},
        #{cableStatic},
        #{wifiInfo},
        #{wiredMac},
        #{wifiMac},
        now(3),
        now(3)
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceNetworkInfo">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceNetworkInfo">
        update
        <include refid="sql_table"/>
        <set>
            <if test="networkType != null">
                network_type = #{networkType},
            </if>
            <if test="connectType != null">
                connect_type = #{connectType},
            </if>
            <if test="cableStatic != null">
                cable_static = #{cableStatic},
            </if>
            <if test="wifiInfo != null">
                wifi_info = #{wifiInfo},
            </if>
            <if test="wiredMac != null">
                wired_mac = #{wiredMac},
            </if>
            <if test="wifiMac != null">
                wifi_mac = #{wifiMac},
            </if>
            gmt_modify = now(3)
        </set>
        where org_id = #{orgId}
        and id = #{id}
    </update>

    <select id="getByOrgIdAndDeviceId" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_id = #{deviceId}
    </select>
</mapper>
