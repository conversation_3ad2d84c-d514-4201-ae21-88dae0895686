package com.moredian.magicube.device.helper;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.log.DeviceLogDTO;
import com.moredian.magicube.device.service.DeviceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 设备日志helper
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeviceLogHelper {

    @SI
    private DeviceLogService deviceLogService;

    @Async
    public void asyncInsertActiveDeviceLog(DeviceLogDTO deviceLogDTO) {
        deviceLogService.insert(deviceLogDTO);
    }
}
