package com.moredian.magicube.device.jetlinks.ruleArrange;

import com.moredian.magicube.device.manager.DeviceTypePropertyManager;

/**
 * <AUTHOR>
 * @version $Id: DeviceMessageSenderNode.java, v 1.0 Exp $
 */
public class DeviceMessageSenderNode extends Node{

    public DeviceMessageSenderNode(String modelId, Flow flow, DeviceTypePropertyManager deviceTypePropertyManager) {
        super(modelId, flow, deviceTypePropertyManager);
    }

    public void wrapperContent() {
        flow.addNode("{\\\"id\\\":\\\""+id+"\\\",\\\"type\\\":\\\"device-message-sender\\\",\\\"z\\\":\\\""+modelId+"\\\",\\\"stateOperator\\\":\\\"ignoreOffline\\\",\\\"waitType\\\":\\\"sync\\\",\\\"from\\\":\\\"pre-node\\\",\\\"name\\\":\\\"设备指令\\\",\\\"timeout\\\":\\\"10s\\\",\\\"message\\\":\\\"{\\\\n  \\\\\\\"messageType\\\\\\\": \\\\\\\"READ_PROPERTY\\\\\\\",\\\\n  \\\\\\\"properties\\\\\\\": []\\\\n}\\\",\\\"noerr\\\":0,\\\"selector\\\":\\\"\\\",\\\"concurrency\\\":64,\\\"x\\\":660,\\\"y\\\":2100,\\\"wires\\\":[[]]}");
    }
}
