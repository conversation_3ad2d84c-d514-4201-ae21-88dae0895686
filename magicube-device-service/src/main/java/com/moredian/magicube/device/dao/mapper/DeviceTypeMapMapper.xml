<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceTypeMapMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceTypeMap">
        <id column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
        <result column="map_name" property="mapName"/>
        <result column="map_key" property="mapKey"/>
        <result column="map_value" property="mapValue"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, map_name, map_key, map_value, status, gmt_create, gmt_modify
    </sql>

    <sql id="sql_values">
        #{id}, #{mapName}, #{mapKey}, #{mapValue}, 1, now(3), now(3)
    </sql>

    <sql id="sql_table">
        hive_device_type_map
    </sql>

    <insert id="insert">
        insert into <include refid="sql_table"/>
        (<include refid="Base_Column_List"/>)
        values (<include refid="sql_values"/>)
    </insert>

    <select id="getValue" resultType="java.lang.String">
        select map_value from <include refid="sql_table"/>
        where map_name = #{mapName} and map_key = #{mapKey} and status = 1
    </select>

    <select id="getValues" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="sql_table"/>
        where map_name = #{mapName} and map_key in
        <foreach collection="mapKeyList" item="mapKey" open="(" close=")" separator=",">
            #{mapKey}
        </foreach>
        and status = 1
    </select>

    <update id="updateDeviceCapacity">
        update <include refid="sql_table"/>
        set map_value = #{deviceCapacity}
        where map_name = 'DEVICE_CAPACITY_MAP' and map_key = #{deviceType} and status = 1
    </update>
</mapper>
