package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备分组条目
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_composite_item")
public class DeviceCompositeItem extends TimedEntity {

    private static final long serialVersionUID = 477141900637633535L;

    /**
     * 设备分组条目
     */
    private Long deviceCompositeItemId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备分组Id
     */
    private Long deviceCompositeId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 业务类型 0-门禁，1-访客
     */
    private Integer bizType;

    /**
     * 设备类型
     */
    private Integer deviceType;
}
