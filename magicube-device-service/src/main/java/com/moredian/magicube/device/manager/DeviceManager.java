package com.moredian.magicube.device.manager;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.AddDeviceDTO;
import com.moredian.magicube.device.dto.device.DeviceDTO;
import com.moredian.magicube.device.dto.device.DeviceElectricDTO;
import com.moredian.magicube.device.dto.device.DeviceWithVersionInfoDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceWithVersionDTO;
import com.moredian.magicube.device.dto.device.QuerySpecificDeviceDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceElectricDTO;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.manager.bo.QueryPageDeviceBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceManager {

    /**
     * 根据设备sn获取设备信息
     *
     * @param deviceSn 设备sn
     * @return
     */
    Device getByDeviceSn(String deviceSn);

    /**
     * 根据机构号和设备sn获取设备信息
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    Device getByOrgAndDeviceSn(Long orgId, String deviceSn);

    /**
     * 根据设备sn列表获取设备信息
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    List<Device> listByDeviceSns(List<String> deviceSns);

    /**
     * 根据机构号和设备sn列表获取设备信息
     *
     * @param orgId     机构号
     * @param deviceSns 设备sn列表
     * @return
     */
    List<Device> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns);

    /**
     * 根据设备Id获取设备信息
     *
     * @param deviceId 设备Id
     * @return
     */
    Device getById(Long deviceId);

    /**
     * 根据机构号和设备Id获取设备信息
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    Device getByOrgIdAndId(Long orgId, Long deviceId);

    /**
     * 根据设备Id列表查询设备信息
     *
     * @param deviceIds 设备Id列表
     * @return
     */
    List<Device> listByIds(List<Long> deviceIds);

    /**
     * 根据机构号和设备Id列表获取设备信息列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<Device> listByOrgIdAndIds(Long orgId, List<Long> deviceIds);

    /**
     * 查询该机构下所有设备信息
     *
     * @param orgId 机构号
     * @return
     */
    List<Device> listByOrgId(Long orgId);

    /**
     * 新增设备信息
     *
     * @param device 设备信息
     * @return
     */
    boolean insert(Device device);

    /**
     * 新增设备信息
     *
     * @param dto 设备信息
     * @return
     */
    Long add(AddDeviceDTO dto);

    /**
     * 修改设备信息
     *
     * @param dto 设备信息
     * @return
     */
    boolean update(UpdateDeviceDTO dto);

    /**
     * 批量修改设备
     *
     * @param list 设备信息列表
     * @return
     */
    Boolean batchUpdate(List<UpdateDeviceDTO> list);

    /**
     *
     * @param list
     * @return
     */
    Boolean batchUpdateAppCodeSelective(List<UpdateDeviceDTO> list);
    /**
     * 删除设备
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    Boolean deleteById(Long orgId, Long deviceId);

    /**
     * 分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    Pagination<Device> listPage(QueryDeviceDTO dto, Boolean allDevice);

    /**
     * 分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    Pagination<Device> listPageV2(QueryDeviceDTO dto);

    /**
     * 多租户分页查询设备信息
     *
     * @param dto 查询条件
     * @return
     */
    Pagination<Device> parkPage(QueryDeviceDTO dto);

    /**
     * 按设备名称查询设备详情
     *
     * @param orgId      机构号
     * @param deviceName 设备名称
     * @return
     */
    Device getByOrgIdAndDeviceName(Long orgId, String deviceName);

    /**
     * 按设备名称查询模糊查询设备ID列表
     *
     * @param orgId    机构号
     * @param keywords 设备名称
     * @return
     */
    List<Long> listDeviceIdByLikeName(Long orgId, String keywords);

    /**
     * 根据设备Id列表查询设备名称列表
     *
     * @param deviceIds 设备Id列表
     * @return
     */
    List<String> listDeviceNameByIds(List<Long> deviceIds);

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param orgId      机构号
     * @param deviceType 设备类型
     * @return
     */
    List<Long> listDeviceIdByOrgIdAndType(Long orgId, Integer deviceType);

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param deviceType 设备类型
     * @return
     */
    List<Device> listByType(Integer deviceType);

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param orgId      机构号
     * @param deviceType 设备类型
     * @return
     */
    List<Device> listByOrgIdAndType(Long orgId, Integer deviceType);

    /**
     * 根据设备类型列表查询设备信息列表
     *
     * @param orgId       机构号
     * @param deviceTypes 设备类型列表
     * @return
     */
    List<Device> listByOrgIdAndTypes(Long orgId, List<Integer> deviceTypes);

    /**
     * 根据条件模糊查询设备信息列表
     *
     * @param queryDeviceDTO 查询条件
     * @return
     */
    List<Device> listByLikeCondition(QueryDeviceDTO queryDeviceDTO);

    /**
     * 根据设备Sn，设备名称查询设备列表
     *
     * @param dto
     * @return
     */
    List<Device> listByDeviceSnListAndDeviceName(QuerySpecificDeviceDTO dto);

    /**
     * 查询所有设备
     *
     * @return
     */
    List<DeviceDTO> listAllDevices();

    List<Device> listDevicesByOrgIds(List<Long> orgIds);

    List<DeviceWithVersionInfoDTO> listDeviceWithVersionInfo(QueryDeviceWithVersionDTO dto);

    List<Integer> findAllDeviceTypesByOrgId(Long orgId);

    /**
     * 设备上报电量
     *
     * @param deviceElectricDto
     * @return
     */
    Boolean reportDeviceElectric(ReportDeviceElectricDTO deviceElectricDto);

    /**
     * 根据设备Id查询设备电量
     *
     * @param deviceId
     * @return
     */
    DeviceElectricDTO queryElectricByDeviceId(Long deviceId);

    /**
     * 根据设备Id查询设备电量
     *
     * @param deviceIdList
     * @return
     */
    List<DeviceElectricDTO> findElectricByDeviceIdList(List<Long> deviceIdList);

    /**
     * 修改蓝牙mac
     *
     * @param bluetoothMac
     * @param deviceId
     * @return
     */
    Boolean updateBluetoothMac(String bluetoothMac, Long deviceId);

    /**
     * 处理设备容量超限
     *
     * @param orgId     机构Id
     * @param deviceIds 设备Id列表
     * @return
     */
    Boolean doDeviceCapacityExceeded(Long orgId, List<Long> deviceIds);

    /**
     * 同步iot设备
     */
    void syncIotDeviceByOrgId(Long orgId);

    List<String> listParentSnByIotParentDeviceSns(List<String> parentDeviceSns);

    List<Device> listByOrgIdAndParentDeviceSns(Long orgId, List<String> parentDeviceSns);

    /**
     * 根据机构id和设备id批量删除设备
     */
    Boolean batchDeleteByOrgIdAndDeviceId(Long orgId, List<Long> deviceId);

    /**
     * 分页查询设备列表，orgIds 不能为空
     */
    Pagination<Device> listPageByCondition(QueryPageDeviceBO bo);
}