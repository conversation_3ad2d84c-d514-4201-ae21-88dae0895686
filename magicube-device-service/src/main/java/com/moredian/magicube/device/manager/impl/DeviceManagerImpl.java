package com.moredian.magicube.device.manager.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.IIotDevicePropertyService;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyDefineDTO;
import com.moredian.bridge.api.service.mapping.IOrgIotClientMappingService;
import com.moredian.deliver.api.holiday.HolidayService;
import com.moredian.deliver.dto.holiday.HolidayDTO;
import com.moredian.device.pipeline.api.DevicePipelineStateService;
import com.moredian.device.pipeline.dto.request.DevicePipelineStateListReq;
import com.moredian.hephaestus.request.relation.GetRelationBySerialNumberRequest;
import com.moredian.hephaestus.response.relation.SimpleSpuInventoryRelationResponse;
import com.moredian.hephaestus.service.SpuInventoryRelationService;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.DeviceStateRequest;
import com.moredian.iothub.control.api.v1.response.DeviceStateResponse;
import com.moredian.magicube.common.enums.*;
import com.moredian.magicube.common.model.msg.RemoveDeviceExtraInfoMsg;
import com.moredian.magicube.core.member.service.GroupRelationService;
import com.moredian.magicube.core.org.response.PositionInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.core.org.service.PositionService;
import com.moredian.magicube.device.constant.*;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceCompositeItem;
import com.moredian.magicube.device.dao.entity.DeviceElectric;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeItemMapper;
import com.moredian.magicube.device.dao.mapper.DeviceConfigMapper;
import com.moredian.magicube.device.dao.mapper.DeviceElectricMapper;
import com.moredian.magicube.device.dao.mapper.DeviceMapper;
import com.moredian.magicube.device.dto.composite.DeviceCompositeItemDTO;
import com.moredian.magicube.device.dto.device.*;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.*;
import com.moredian.magicube.device.manager.bo.QueryPageDeviceBO;
import com.moredian.magicube.device.manager.helper.DeviceManagerHelper;
import com.moredian.magicube.device.service.DeviceVersionService;
import com.moredian.space.dto.device.BindSpaceTreeDto;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.dto.tree.QueryTreeDTO;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.enums.SpaceTreeDeviceTypeEnum;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;
import com.moredian.space.service.v2.SpaceTreeDeviceRelationServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.google.common.collect.Sets.newHashSet;
import static org.apache.commons.collections.CollectionUtils.intersection;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class DeviceManagerImpl implements DeviceManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @SI
    private PositionService positionService;

    @SI
    private SpuInventoryRelationService spuInventoryRelationService;

    @SI
    private OrgService orgService;

    @SI
    private SpaceTreeDeviceRelationService treeDeviceRelationService;

    @SI
    private SpaceTreeDeviceRelationServiceV2 treeDeviceRelationServiceV2;

    @SI
    private SpaceTreeService treeService;

    @SI
    private DevicePipelineStateService devicePipelineStateService;

    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;

    @SI
    private IIotDevicePropertyService iotDevicePropertyService;

    @SI
    private IOrgIotClientMappingService orgIotClientMappingService;

    @SI
    private SpaceTreeService spaceTreeService;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private DeviceConfigMapper deviceConfigMapper;

    @Autowired
    private DeviceGroupManager deviceGroupManager;

    @Autowired
    private DeviceVersionService deviceVersionService;

    @Autowired
    private DeviceTypeManager deviceTypeManager;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Autowired
    private DeviceFaceModelManager deviceFaceModelManager;

    @Autowired
    private DeviceElectricMapper deviceElectricMapper;

    @SI
    private GroupRelationService groupRelationService;

    @Autowired
    private DeviceManager deviceManager;

    @Resource
    private DeviceManagerHelper deviceManagerHelper;

    @Autowired
    private DeviceCompositeManager deviceCompositeManager;

    @Autowired
    private DeviceCompositeItemMapper deviceCompositeItemMapper;

    @SI
    private HolidayService holidayService;

    private static final String REGEX_CHINESE = "[\u4e00-\u9fa5]";

    @Override
    public Device getByDeviceSn(String deviceSn) {
        BizAssert.notBlank(deviceSn, "deviceSn must not be null");
        Device device = deviceMapper.getByDeviceSn(deviceSn);
        if (device != null) {
            fillDeviceTimeZoneAndLanguageDefaults(device);
        }
        return device;
    }

    @Override
    public Device getByOrgAndDeviceSn(Long orgId, String deviceSn) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notBlank(deviceSn, "deviceSn must not be null");
        Device device = deviceMapper.getByOrgAndDeviceSn(orgId, deviceSn);
        if (device != null) {
            fillDeviceTimeZoneAndLanguageDefaults(device);
        }
        return device;
    }


    @Override
    public List<Device> listByDeviceSns(List<String> deviceSns) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceSns), "deviceSns must not be null");
        List<Device> devices = deviceMapper.listByDeviceSns(deviceSns);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<Device> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceSns), "deviceSns must not be null");
        List<Device> devices = deviceMapper.listByOrgIdAndDeviceSns(orgId, deviceSns);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public Device getById(Long deviceId) {
        BizAssert.notNull(deviceId, "deviceId must not be null");
        Device device = deviceMapper.getById(deviceId);
        if (device != null) {
            fillDeviceTimeZoneAndLanguageDefaults(device);
        }
        return device;
    }

    @Override
    public Device getByOrgIdAndId(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        Device device = deviceMapper.getByOrgIdAndId(orgId, deviceId);
        if (device != null) {
            fillDeviceTimeZoneAndLanguageDefaults(device);
        }
        return device;
    }

    @Override
    public List<Device> listByIds(List<Long> deviceIds) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        List<Device> devices = deviceMapper.listByIds(deviceIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<Device> listByOrgIdAndIds(Long orgId, List<Long> deviceIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        List<Device> devices = deviceMapper.listByOrgIdAndIds(orgId, deviceIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<Device> listByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        List<Device> devices = deviceMapper.listByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public boolean insert(Device device) {
        BizAssert.notNull(device.getOrgId(), "orgId must not be null");
        BizAssert.notNull(device.getDeviceSn(), "deviceSn must not be null");
        BizAssert.notNull(device.getDeviceName(), "deviceName must not be null");
        BizAssert.notNull(device.getDeviceType(), "deviceType must not be null");
        //判断设备是否已经激活
        Device oldDevice = getByOrgAndDeviceSn(device.getOrgId(), device.getDeviceSn());
        BizAssert.isTrue(oldDevice == null, DeviceErrorCode.DEVICE_ALEADY_USE,
            DeviceErrorCode.DEVICE_ALEADY_USE.getMessage());
        device.setDeviceId(
            idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE).pickDataThrowException());
        int result = deviceMapper.insert(device);
        return (result > 0);
    }

    @Override
    @Transactional
    public Long add(AddDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getDeviceType(), "deviceType must not be null");
        BizAssert.notBlank(dto.getDeviceSn(), "deviceSn must not be blank");

        Device device = this.deviceAddRequestToDevice(dto);
        deviceMapper.insert(device);

        // 设备绑定默认群组
        deviceGroupManager.insertDefaultDeviceGroup(device.getOrgId(), device.getDeviceId());

        this.enableBiz(device);
        return device.getDeviceId();
    }

    @Override
    public boolean update(UpdateDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getDeviceId(), "deviceId must not be null");
        if (dto.getDeviceName() != null) {
            BizAssert.isTrue(dto.getDeviceName().length() < 51, "deviceName too long");
        }
        if (StringUtils.isNotBlank(dto.getPosition())) {
            BizAssert.isTrue(dto.getPosition().length() <= 100, DeviceErrorCode.DEVICE_POSITION_TOOLONG.getCode(),
                    DeviceErrorCode.DEVICE_POSITION_TOOLONG.getMessage());
        }
        Device device = new Device();
        BeanUtils.copyProperties(dto, device);
        int result = deviceMapper.update(device);
        return (result > 0);
    }

    @Override
    public Boolean batchUpdate(List<UpdateDeviceDTO> list) {
        Boolean updateSuccess = Boolean.FALSE;
        if (CollectionUtils.isNotEmpty(list)) {
            List<Device> devices = Lists.newArrayList();
            for (UpdateDeviceDTO updateDeviceDTO : list) {
                BizAssert.notNull(updateDeviceDTO.getDeviceId(), "deviceId must not be null");
                BizAssert.notNull(updateDeviceDTO.getOrgId(), "orgId must not be null");
                if (updateDeviceDTO.getDeviceName() != null) {
                    BizAssert.isTrue(updateDeviceDTO.getDeviceName().length() < 51,
                        "deviceName too long");
                }
                Device device = new Device();
                BeanUtils.copyProperties(updateDeviceDTO, device);
                devices.add(device);
            }
            int result = deviceMapper.batchUpdate(devices);
            if (result > 0) {
                updateSuccess = Boolean.TRUE;
            }
        }
        return updateSuccess;
    }

    @Override
    public Boolean batchUpdateAppCodeSelective(List<UpdateDeviceDTO> list) {
        Boolean updateSuccess = Boolean.FALSE;
        if (CollectionUtils.isNotEmpty(list)) {
            List<Device> devices = Lists.newArrayList();
            for (UpdateDeviceDTO updateDeviceDTO : list) {
                BizAssert.notNull(updateDeviceDTO.getDeviceId(), "deviceId must not be null");
                BizAssert.notNull(updateDeviceDTO.getOrgId(), "orgId must not be null");
                Device device = new Device();
                BeanUtils.copyProperties(updateDeviceDTO, device);
                devices.add(device);
            }
            int result = deviceMapper.batchUpdateDeviceRelateApp(devices);
            if (result > 0) {
                updateSuccess = Boolean.TRUE;
            }
        }
        return updateSuccess;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        Device device = deviceMapper.getByOrgIdAndId(orgId, deviceId);
        BizAssert.notNull(device, DeviceErrorCode.DEVICE_NOT_EXIST,
            DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
        deviceConfigMapper.deleteBySn(device.getDeviceSn());
        deviceFaceModelManager.deleteByDevice(orgId, deviceId);
        if (deviceTypePropertyManager
            .containsDeviceType(DeviceTypeConstants.DOOR_DEVICE_LIST, device.getDeviceType())
            || deviceTypePropertyManager
            .containsDeviceType(DeviceTypeConstants.ROOSTER_DEVICE_LIST, device.getDeviceType())
            || deviceTypePropertyManager
            .containsDeviceType(DeviceTypeConstants.EPIDEMIC_DEVICE_TYPE_LIST,
                device.getDeviceType())) {
            deviceGroupManager
                .deleteByCondition(device.getOrgId(), Collections.singletonList(deviceId), null);
            //删除设备和设备组关系
            deviceCompositeManager.deleteCompositeItemByDeviceId(device.getOrgId(), deviceId,
                DeviceCompositeBizType.COMMON);
            RemoveDeviceExtraInfoMsg removeDeviceExtraInfoMsg = new RemoveDeviceExtraInfoMsg();
            removeDeviceExtraInfoMsg.setOrgId(device.getOrgId());
            removeDeviceExtraInfoMsg.setDeviceId(device.getDeviceId());
            removeDeviceExtraInfoMsg.setDeviceSn(device.getDeviceSn());
            EventBus.publish(removeDeviceExtraInfoMsg);
            log.info("发出消息RemoveDeviceExtraInfoMsg:" + JsonUtils.toJson(removeDeviceExtraInfoMsg));
        }
        deviceMapper.deleteById(orgId, deviceId);
        return Boolean.TRUE;
    }

    @Override
    public Pagination<Device> listPage(QueryDeviceDTO dto, Boolean allDevice) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        Pagination<Device> pa = new Pagination<>();
        pa.setPageNo(dto.getPageNo());
        pa.setPageSize(dto.getPageSize());

        Set<Long> oSets = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
            oSets = newHashSet(dto.getDeviceIds());
        }
        //根据在线或者离线状态查询设备sn列表
        if (dto.getOnlineStatus() != null) {
            List<Long> deviceIds = listDeviceIdsByOnlineStatus(Lists.newArrayList(dto.getOrgId()),
                dto.getOnlineStatus());
            if (CollectionUtils.isEmpty(deviceIds)) {
                return pa;
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(deviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(deviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }
        if (null != dto.getStatus()) {
            dto.setStatusList(Collections.singletonList(dto.getStatus()));
        }

        //位置条件查询
        if (CollectionUtils.isNotEmpty(dto.getTreeIds())) {
            List<TreeDeviceRelationDTO> relationDTOS;
            if (dto.getSubTreeFlag() != null && !dto.getSubTreeFlag()) {
                relationDTOS = treeDeviceRelationService.listByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            } else {
                relationDTOS = treeDeviceRelationService.listAllByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            }
            //没有关联设备直接返回
            if (CollectionUtils.isEmpty(relationDTOS)) {
                return pa;
            }
            List<Long> deviceIdList = relationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId)
                .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> deviceIdSet = newHashSet(deviceIdList);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, deviceIdSet)));
            } else {
                dto.setDeviceIds(deviceIdList);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //根据设备组id进行过滤
        if (dto.getDeviceCompositeId() != null) {
            List<Long> deviceIdList = new ArrayList<>();
            //如果设备组Id传-1，代表查询未分组的设备(用户能够看到说明一定有这个设备组的权限)
            if (dto.getDeviceCompositeId() != -1L) {
                List<DeviceCompositeItemDTO> dci = deviceCompositeManager.
                    listCompositeItemByCompositeId(dto.getOrgId(),
                        dto.getDeviceCompositeId(), dto.getSubTreeFlag());
                //设备组下面没有设备直接返回
                if (CollectionUtils.isEmpty(dci)) {
                    return pa;
                }
                deviceIdList = dci.stream().map(DeviceCompositeItemDTO::getDeviceId)
                    .distinct().collect(Collectors.toList());
            } else {
                List<Device> totalDevices;
                QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
                queryDeviceDTO.setOrgId(dto.getOrgId());
                if (dto.getSceneType() != null) {
                    queryDeviceDTO.setSceneTypes(Lists.newArrayList(dto.getSceneType()));
                }
                if (dto.getAllDeviceFlag() != null && dto.getAllDeviceFlag() == 0) {
                    queryDeviceDTO.setDeviceIds(dto.getDeviceIds());
                }
                totalDevices = listByLikeCondition(queryDeviceDTO);
                if (CollectionUtils.isEmpty(totalDevices)) {
                    return pa;
                }
                List<Long> hasPermissionDeviceIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(totalDevices)) {
                    hasPermissionDeviceIds = totalDevices.stream().map(Device::getDeviceId)
                        .distinct().collect(Collectors.toList());
                }
                //有组的设备id列表
                List<Long> deviceIds = new ArrayList<>();
                List<Long> existDeviceIds = new ArrayList<>();
                if (dto.getAllDeviceCompositeFlag() != null && dto.getAllDeviceCompositeFlag()) {
                    List<DeviceCompositeItem> items = deviceCompositeItemMapper.listByOrgId(dto.
                        getOrgId(),DeviceCompositeBizType.COMMON);
                    if (CollectionUtils.isNotEmpty(items)) {
                        //有组的设备id列表
                        existDeviceIds = items.stream().map(DeviceCompositeItem::getDeviceId)
                            .distinct().collect(Collectors.toList());
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(dto.getDeviceCompositeIds())) {
                        existDeviceIds = deviceCompositeManager.getDeviceIdsByCompositeDeepBatch
                            (dto.getOrgId(), dto.getDeviceCompositeIds(), dto.getSceneType());
                    }
                }

                if (CollectionUtils.isNotEmpty(existDeviceIds)) {
                    //需要去除没有权限的设备Id
                    for (Long existDeviceId : existDeviceIds) {
                        if (hasPermissionDeviceIds.contains(existDeviceId)) {
                            deviceIds.add(existDeviceId);
                        }
                    }
                    //判断这些设备的运行模式是否匹配(所有设备组下的设备都为0)
                    queryDeviceDTO.setDeviceIds(deviceIds);
                    List<Device> devices = listByLikeCondition(queryDeviceDTO);
                    if(CollectionUtils.isEmpty(devices)) {
                        deviceIdList = totalDevices.stream().map(Device::getDeviceId)
                            .distinct().collect(Collectors.toList());
                    } else {
                        deviceIds = devices.stream().map(Device::getDeviceId)
                            .distinct().collect(Collectors.toList());
                        for (Device device : totalDevices) {
                            if (!deviceIds.contains(device.getDeviceId())) {
                                deviceIdList.add(device.getDeviceId());
                            }
                        }
                    }
                } else {
                    deviceIdList.addAll(totalDevices.stream().map(Device::getDeviceId).collect(
                        Collectors.toList()));
                }
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> deviceIdSet = newHashSet(deviceIdList);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, deviceIdSet)));
            } else {
                dto.setDeviceIds(deviceIdList);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //场景类型过滤
        if (dto.getSceneType() != null || CollectionUtils.isNotEmpty(dto.getSceneTypes())) {
            DevicePipelineStateListReq req = new DevicePipelineStateListReq();
            req.setOrgId(dto.getOrgId());
            req.setSceneType(dto.getSceneType());
            req.setSceneTypeList(dto.getSceneTypes());
            List<Long> sceneDeviceIds = devicePipelineStateService.getAllDeviceIdByCondition(req)
                .pickDataThrowException();
            if (CollectionUtils.isEmpty(sceneDeviceIds)) {
                return pa;
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(sceneDeviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(sceneDeviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
        }

        //模糊搜索位置（空间全路径+设备位置）
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            List<Long> deviceIds = treeService
                .listDeviceIdByLikeTreeName(dto.getOrgId(), dto.getKeywords())
                .pickDataThrowException();
            dto.setQueryDeviceIds(deviceIds);
        }
        if (!allDevice){
            // 过滤iot设备
            dto.setIotDeviceTypes(deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
        }
        log.info("分页查询设备信息参数queryDeviceDTO:{}", dto);
        Page<Device> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(),
            dto.getPageSize() == null ? 20 : dto.getPageSize())
            .doSelectPage(() -> deviceMapper.listPageByCondition(dto));
        if (CollectionUtils.isEmpty(page)) {
            return pa;
        }
        List<Device> devices = page.getResult();
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        pa.setData(devices);
        pa.setTotalCount((int) page.getTotal());
        return pa;
    }

    @Override
    public Pagination<Device> listPageV2(QueryDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        Pagination<Device> pa = new Pagination<>();
        pa.setPageNo(dto.getPageNo());
        pa.setPageSize(dto.getPageSize());

        Set<Long> oSets = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
            oSets = newHashSet(dto.getDeviceIds());
        }
        //根据在线或者离线状态查询设备sn列表
        if (dto.getOnlineStatus() != null) {
            List<Long> deviceIds = listDeviceIdsByOnlineStatus(Lists.newArrayList(dto.getOrgId()),
                dto.getOnlineStatus());
            if (CollectionUtils.isEmpty(deviceIds)) {
                return pa;
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(deviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(deviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }
        if (null != dto.getStatus()) {
            dto.setStatusList(Collections.singletonList(dto.getStatus()));
        }

        //位置条件查询
        if (CollectionUtils.isNotEmpty(dto.getTreeIds())) {
            List<TreeDeviceRelationDTO> relationDTOS;
            if (dto.getSubTreeFlag() != null && !dto.getSubTreeFlag()) {
                relationDTOS = treeDeviceRelationService.listByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            } else {
                relationDTOS = treeDeviceRelationService.listAllByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            }
            //没有关联设备直接返回
            if (CollectionUtils.isEmpty(relationDTOS)) {
                return pa;
            }
            List<Long> deviceIdList = relationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId)
                .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> deviceIdSet = newHashSet(deviceIdList);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, deviceIdSet)));
            } else {
                dto.setDeviceIds(deviceIdList);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //场景类型过滤
        if (dto.getSceneType() != null || CollectionUtils.isNotEmpty(dto.getSceneTypes())) {
            DevicePipelineStateListReq req = new DevicePipelineStateListReq();
            req.setOrgId(dto.getOrgId());
            req.setSceneType(dto.getSceneType());
            dto.setSceneTypes(dto.getSceneTypes());
            List<Long> sceneDeviceIds = devicePipelineStateService.getAllDeviceIdByCondition(req)
                .pickDataThrowException();
            if (CollectionUtils.isEmpty(sceneDeviceIds)) {
                return pa;
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(sceneDeviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(sceneDeviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
        }

        //模糊搜索位置（空间全路径+设备位置）
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            List<Long> deviceIds = treeService
                .listDeviceIdByLikeTreeName(dto.getOrgId(), dto.getKeywords())
                .pickDataThrowException();
            dto.setQueryDeviceIds(deviceIds);
        }
        // 过滤iot设备
        dto.setIotDeviceTypes(deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
        log.info("分页查询设备信息参数queryDeviceDTO:{}", dto);
        Page<Device> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(),
                dto.getPageSize() == null ? 20 : dto.getPageSize())
            .setOrderBy("gmt_create DESC")
            .doSelectPage(() -> deviceMapper.listPageByConditionV2(dto));
        if (CollectionUtils.isEmpty(page)) {
            return pa;
        }
        List<Device> devices = page.getResult();
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        pa.setData(devices);
        pa.setTotalCount((int) page.getTotal());
        return pa;
    }

    @Override
    public Pagination<Device> parkPage(QueryDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        Pagination<Device> pa = new Pagination<>();
        pa.setPageNo(dto.getPageNo());
        pa.setPageSize(dto.getPageSize());
        Set<Long> oSets = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
            oSets = newHashSet(dto.getDeviceIds());
        }
        if (null != dto.getStatus()) {
            dto.setStatusList(Collections.singletonList(dto.getStatus()));
        }
        //多租户可能存在园区空间下2台设备的机构不一样 A园区空间-A机构-A设备 A园区空间-B机构-B设备
        List<Long> orgIds = new ArrayList<>();
        //位置条件查询
        if (CollectionUtils.isNotEmpty(dto.getTreeIds())) {
            List<TreeDeviceRelationDTO> relationDTOS;
            int queryType = SpaceTreeDeviceTypeEnum.ALL.getValue();
            if (dto.getType() != null ) {
                if (dto.getType() == 3) {
                    queryType = SpaceTreeDeviceTypeEnum.TENANT.getValue();
                } else {
                    queryType = dto.getType();
                }
            }
            if (dto.getSubTreeFlag() != null && !dto.getSubTreeFlag()) {
                relationDTOS = treeDeviceRelationServiceV2.listByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), queryType).pickDataThrowException();
            } else {
                relationDTOS = treeDeviceRelationServiceV2.listAllByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), queryType).pickDataThrowException();
            }
            //没有关联设备直接返回
            if (CollectionUtils.isEmpty(relationDTOS)) {
                return pa;
            }
            List<Long> deviceIdList = relationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId)
                .distinct().collect(Collectors.toList());
            List<Device> devices = deviceManager.listByIds(deviceIdList);
            if (CollectionUtils.isEmpty(devices)) {
                return pa;
            }
            List<Long> deviceOrgIds = devices.stream().map(Device::getOrgId).distinct()
                .collect(Collectors.toList());
            //我的设备只能显示激活在本机构的设备
            if (dto.getType() != null && dto.getType() == 1) {
                if (!deviceOrgIds.contains(dto.getOrgId())) {
                    return pa;
                }
                orgIds.add(dto.getOrgId());
            } else if (dto.getType() != null && dto.getType() == 2){
                orgIds.add(dto.getOrgId());
            } else {
                orgIds.addAll(deviceOrgIds);
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> deviceIdSet = newHashSet(deviceIdList);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, deviceIdSet)));
            } else {
                dto.setDeviceIds(deviceIdList);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //根据在线或者离线状态查询设备sn列表
        if (dto.getOnlineStatus() != null) {
            List<Long> deviceIds = listDeviceIdsByOnlineStatus(orgIds, dto.getOnlineStatus());
            if (CollectionUtils.isEmpty(deviceIds)) {
                return pa;
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(deviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(deviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //场景类型过滤
        if (dto.getSceneType() != null || CollectionUtils.isNotEmpty(dto.getSceneTypes())) {
            DevicePipelineStateListReq req = new DevicePipelineStateListReq();
            if (CollectionUtils.isEmpty(orgIds)) {
                req.setOrgId(dto.getOrgId());
            } else {
                req.setOrgIdList(orgIds);
            }
            req.setSceneType(dto.getSceneType());
            req.setSceneTypeList(dto.getSceneTypes());
            List<Long> sceneDeviceIds = devicePipelineStateService.getAllDeviceIdByCondition(req)
                .pickDataThrowException();
            if (CollectionUtils.isEmpty(sceneDeviceIds)) {
                return pa;
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(sceneDeviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(sceneDeviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pa;
            }
        }

        //模糊搜索位置（空间全路径+设备位置）
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            List<Long> deviceIds = treeService.listDeviceIdByLikeTreeName(dto.getOrgId()
                    , dto.getKeywords()).pickDataThrowException();
            dto.setQueryDeviceIds(deviceIds);
        }
        // 过滤iot设备
        dto.setIotDeviceTypes(deviceTypePropertyManager
            .getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
        dto.setOrgIds(orgIds);
        log.info("多租户分页查询设备信息参数queryDeviceDTO:{}", dto);
        Page<Device> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(),
                dto.getPageSize() == null ? 20 : dto.getPageSize()).setOrderBy("gmt_create DESC")
            .doSelectPage(() -> deviceMapper.parkPageByCondition(dto));
        if (CollectionUtils.isEmpty(page)) {
            return pa;
        }
        List<Device> devices = page.getResult();
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        pa.setData(devices);
        pa.setTotalCount((int) page.getTotal());
        return pa;
    }

    @Override
    public Device getByOrgIdAndDeviceName(Long orgId, String deviceName) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notBlank(deviceName, "deviceName must not be null");
        Device device = deviceMapper.getByOrgIdAndDeviceName(orgId, deviceName);
        if (device != null) {
            fillDeviceTimeZoneAndLanguageDefaults(device);
        }
        return device;
    }

    @Override
    public List<Long> listDeviceIdByLikeName(Long orgId, String keywords) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notBlank(keywords, "keywords must not be null");
        return deviceMapper.listDeviceIdByLikeName(orgId, keywords, deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
    }

    @Override
    public List<String> listDeviceNameByIds(List<Long> deviceIds) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        return deviceMapper.listDeviceNameByIds(deviceIds);
    }

    @Override
    public List<Long> listDeviceIdByOrgIdAndType(Long orgId, Integer deviceType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceType, "deviceType must not be null");
        return deviceMapper.listDeviceIdByOrgIdAndType(orgId, deviceType);
    }

    @Override
    public List<Device> listByType(Integer deviceType) {
        BizAssert.notNull(deviceType, "deviceType must not be null");
        List<Device> devices = deviceMapper.listByType(deviceType);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<Device> listByOrgIdAndType(Long orgId, Integer deviceType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceType, "deviceType must not be null");
        List<Device> devices = deviceMapper.listByOrgIdAndType(orgId, deviceType);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<Device> listByOrgIdAndTypes(Long orgId, List<Integer> deviceTypes) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceTypes), "deviceTypes must not be null");
        List<Device> devices = deviceMapper.listByOrgIdAndTypes(orgId, deviceTypes);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<Device> listByLikeCondition(QueryDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        Set<Long> oSets = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
            oSets = newHashSet(dto.getDeviceIds());
        }
        //根据在线或者离线状态查询设备sn列表
        if (dto.getOnlineStatus() != null) {
            List<Long> deviceIds = listDeviceIdsByOnlineStatus(Lists.newArrayList(dto.getOrgId()),
                dto.getOnlineStatus());
            if (CollectionUtils.isEmpty(deviceIds)) {
                return Collections.emptyList();
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(deviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(deviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return Collections.emptyList();
            }
            oSets = newHashSet(dto.getDeviceIds());
        }
        if (null != dto.getStatus()) {
            dto.setStatusList(Collections.singletonList(dto.getStatus()));
        }

        //根据设备组id进行过滤
        if (dto.getDeviceCompositeId() != null) {
            List<Long> deviceIdList = new ArrayList<>();
            //如果设备组Id传-1，代表查询未分组的设备
            if (dto.getDeviceCompositeId() != -1L) {
                List<DeviceCompositeItemDTO> dci = deviceCompositeManager.
                    listCompositeItemByCompositeId(dto.getOrgId(),
                        dto.getDeviceCompositeId(), dto.getSubTreeFlag());
                //设备组下面没有设备直接返回
                if (CollectionUtils.isEmpty(dci)) {
                    return Collections.emptyList();
                }
                deviceIdList = dci.stream().map(DeviceCompositeItemDTO::getDeviceId)
                    .distinct().collect(Collectors.toList());
            } else {
                List<Device> totalDevices;
                if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                    totalDevices = deviceManager.listByOrgIdAndIds(dto.getOrgId()
                        , dto.getDeviceIds());
                } else {
                    totalDevices = deviceManager.listByOrgId(dto.getOrgId());
                }
                List<DeviceCompositeItem> items = deviceCompositeItemMapper
                    .listByOrgId(dto.getOrgId(), DeviceCompositeBizType.COMMON);
                if (CollectionUtils.isNotEmpty(items)) {
                    //有组的设备id列表
                    List<Long> deviceIds = items.stream().map(DeviceCompositeItem::getDeviceId)
                        .distinct().collect(Collectors.toList());
                    for (Device device : totalDevices) {
                        if (!deviceIds.contains(device.getDeviceId())) {
                            deviceIdList.add(device.getDeviceId());
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> deviceIdSet = newHashSet(deviceIdList);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, deviceIdSet)));
            } else {
                dto.setDeviceIds(deviceIdList);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return Collections.emptyList();
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //位置条件查询
        if (CollectionUtils.isNotEmpty(dto.getTreeIds())) {
            List<TreeDeviceRelationDTO> relationDTOS;
            if (dto.getSubTreeFlag() != null && !dto.getSubTreeFlag()) {
                relationDTOS = treeDeviceRelationService.listByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            } else {
                relationDTOS = treeDeviceRelationService.listAllByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            }
            //没有关联设备直接返回
            if (CollectionUtils.isEmpty(relationDTOS)) {
                return Collections.emptyList();
            }
            List<Long> deviceIdList = relationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId)
                .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> deviceIdSet = newHashSet(deviceIdList);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, deviceIdSet)));
            } else {
                dto.setDeviceIds(deviceIdList);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return Collections.emptyList();
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //场景类型过滤
        if (dto.getSceneType() != null || CollectionUtils.isNotEmpty(dto.getSceneTypes())) {
            DevicePipelineStateListReq req = new DevicePipelineStateListReq();
            req.setOrgId(dto.getOrgId());
            req.setSceneType(dto.getSceneType());
            req.setSceneTypeList(dto.getSceneTypes());
            List<Long> sceneDeviceIds = devicePipelineStateService.getAllDeviceIdByCondition(req)
                .pickDataThrowException();
            if (CollectionUtils.isEmpty(sceneDeviceIds)) {
                return Collections.emptyList();
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(sceneDeviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(sceneDeviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return Collections.emptyList();
            }
        }

        //模糊搜索位置（空间全路径+设备位置）
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            List<Long> deviceIds = treeService
                .listDeviceIdByLikeTreeName(dto.getOrgId(), dto.getKeywords())
                .pickDataThrowException();
            dto.setQueryDeviceIds(deviceIds);
        }
        // 过滤iot设备
        dto.setIotDeviceTypes(deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
        List<Device> devices = deviceMapper.listByLikeCondition(dto);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<Device> listByDeviceSnListAndDeviceName(QuerySpecificDeviceDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        List<Device> devices = deviceMapper.listByDeviceSnListAndDeviceName(dto);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<DeviceDTO> listAllDevices() {
        List<Device> devices = deviceMapper.listAllDevices();
        if (CollectionUtils.isEmpty(devices)) {
            return Collections.emptyList();
        }
        return devices.stream().map(x -> {
            DeviceDTO deviceDTO = new DeviceDTO();
            deviceDTO.setDeviceId(x.getDeviceId());
            deviceDTO.setOrgId(x.getOrgId());
            deviceDTO.setDeviceType(x.getDeviceType());
            deviceDTO.setDeviceName(x.getDeviceName());
            deviceDTO.setDeviceSn(x.getDeviceSn());
            if (x.getActiveTime() != null) {
                deviceDTO.setActiveTime(x.getActiveTime().getTime());
            }
            deviceDTO.setStatus(x.getStatus());
            deviceDTO.setBluetoothMac(x.getBluetoothMac());
            return deviceDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Device> listDevicesByOrgIds(List<Long> orgIds) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(orgIds), "orgIds must not be null");
        List<Device> devices = deviceMapper.listDevicesByOrgIds(orgIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            fillDeviceTimeZoneAndLanguageDefaults(devices);
        }
        return devices;
    }

    @Override
    public List<DeviceWithVersionInfoDTO> listDeviceWithVersionInfo(
        QueryDeviceWithVersionDTO request) {
        List<Device> deviceList = deviceMapper
            .findDeviceByOrgIdAndDeviceTypeList(request.getOrgId(), request.getDeviceTypeList(),
                request.getKeywords());
        if (CollectionUtils.isEmpty(deviceList)) {
            return Collections.emptyList();
        }
        List<Long> deviceIdList = deviceList.stream().map(Device::getDeviceId)
            .collect(Collectors.toList());
        List<List<Long>> subDeviceIdLists = subList(deviceIdList, 100);
        Map<Long/*deviceId*/, DeviceVersionDTO> deviceIdToRomVersionMap = new HashMap<>();
        Map<Long/*deviceId*/, DeviceVersionDTO> deviceIdToAppVersionMap = new HashMap<>();
        for (List<Long> subDeviceIdList : subDeviceIdLists) {
            //查询版本信息
            List<DeviceVersionDTO> deviceRomVersionList = deviceVersionService
                .listApkByOrgIdAndDeviceIds(request.getOrgId(), subDeviceIdList)
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceRomVersionList)) {
                deviceRomVersionList.stream().forEach(deviceRomVersion -> {
                    if (deviceRomVersion.getDeviceId() != null) {
                        deviceIdToRomVersionMap
                            .putIfAbsent(deviceRomVersion.getDeviceId(), deviceRomVersion);
                    }
                });
            }
            List<DeviceVersionDTO> deviceAppVersionList = deviceVersionService
                .listApkByOrgIdAndDeviceIds(request.getOrgId(), subDeviceIdList)
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceAppVersionList)) {
                deviceAppVersionList.stream().forEach(deviceAppVersion -> {
                    if (deviceAppVersion.getDeviceId() != null) {
                        deviceIdToAppVersionMap
                            .putIfAbsent(deviceAppVersion.getDeviceId(), deviceAppVersion);
                    }
                });
            }
        }
        //查询在线状态
        Map<String/*deviceSn*/, DeviceStateRequest> map = new HashMap<>();
        deviceList.stream().forEach(device -> {
            DeviceStateRequest deviceStateRequest = new DeviceStateRequest();
            deviceStateRequest.setSerialNumber(device.getDeviceSn());
            map.put(device.getDeviceSn(), deviceStateRequest);
        });
        Map<String/*deviceSn*/, DeviceStateResponse> deviceStateMap = hubControlServiceV1
            .getDeviceListState(map).pickDataThrowException();
        List<DeviceWithVersionInfoDTO> deviceWithVersionInfoList = new ArrayList<>(
            deviceList.size());
        for (Device device : deviceList) {
            DeviceWithVersionInfoDTO info = new DeviceWithVersionInfoDTO();
            info.setOrgId(device.getOrgId());
            info.setDeviceId(device.getDeviceId());
            info.setDeviceName(device.getDeviceName());
            info.setDeviceSn(device.getDeviceSn());
            info.setDeviceType(device.getDeviceType());
            if (deviceIdToRomVersionMap != null && deviceIdToRomVersionMap
                .containsKey(device.getDeviceId())) {
                DeviceVersionDTO deviceRomVersionDto = deviceIdToRomVersionMap
                    .get(device.getDeviceId());
                if (deviceRomVersionDto != null) {
                    info.setRomType(deviceRomVersionDto.getAppType());
                    info.setRomVersionCode(deviceRomVersionDto.getVersionCode());
                }
            }
            if (deviceIdToAppVersionMap != null && deviceIdToAppVersionMap
                .containsKey(device.getDeviceId())) {
                DeviceVersionDTO deviceAppVersionDto = deviceIdToAppVersionMap
                    .get(device.getDeviceId());
                if (deviceAppVersionDto != null) {
                    info.setAppType(deviceAppVersionDto.getAppType());
                    info.setAppVersionCode(deviceAppVersionDto.getVersionCode());
                }
            }
            if (deviceStateMap != null && deviceStateMap.containsKey(device.getDeviceSn())) {
                DeviceStateResponse deviceStateResponse = deviceStateMap.get(device.getDeviceSn());
                if (deviceStateResponse != null) {
                    info.setOnline(deviceStateResponse.getOnline());
                }
            }
            deviceWithVersionInfoList.add(info);
        }
        if (request.getOnline() == null) {
            return deviceWithVersionInfoList;
        } else if (request.getOnline()) {
            return deviceWithVersionInfoList.stream()
                .filter(deviceWithVersionInfo -> deviceWithVersionInfo.getOnline())
                .collect(Collectors.toList());
        } else if (!request.getOnline()) {
            return deviceWithVersionInfoList.stream()
                .filter(deviceWithVersionInfo -> !deviceWithVersionInfo.getOnline())
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<Integer> findAllDeviceTypesByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        return deviceMapper.listAllDeviceTypeInOrg(orgId);
    }

    @Override
    public Boolean reportDeviceElectric(ReportDeviceElectricDTO deviceElectricDto) {
        DeviceElectric deviceElectric = deviceElectricMapper.selectOneByDeviceSn(deviceElectricDto.getDeviceSn());
        if (deviceElectric == null) {
            Long id = idgeneratorService.getNextIdByTypeName(DeviceElectric.class.getName()).pickDataThrowException();
            deviceElectric = new DeviceElectric();
            deviceElectric.setId(id);
            deviceElectric.setDeviceId(deviceElectricDto.getDeviceId());
            deviceElectric.setDeviceSn(deviceElectricDto.getDeviceSn());
            deviceElectric.setElectric(deviceElectricDto.getElectric());
            deviceElectricMapper.insert(deviceElectric);
        } else {
            deviceElectric.setElectric(deviceElectricDto.getElectric());
            deviceElectric.setDeviceId(deviceElectricDto.getDeviceId());
            deviceElectricMapper.updateById(deviceElectric);
        }
        return Boolean.TRUE;
    }

    @Override
    public DeviceElectricDTO queryElectricByDeviceId(Long deviceId) {
        BizAssert.notNull(deviceId, "deviceSn must not be blank");
        DeviceElectric deviceElectric = deviceElectricMapper.selectOne(deviceId);
        if (deviceElectric != null) {
            DeviceElectricDTO deviceElectricDTO = new DeviceElectricDTO();
            deviceElectricDTO.setDeviceId(deviceElectric.getDeviceId());
            deviceElectricDTO.setDeviceSn(deviceElectric.getDeviceSn());
            deviceElectricDTO.setElectric(deviceElectric.getElectric());
            deviceElectricDTO.setGmtCreate(deviceElectric.getGmtCreate());
            deviceElectricDTO.setGmtModify(deviceElectric.getGmtModify());
            return deviceElectricDTO;
        }
        return null;
    }

    @Override
    public List<DeviceElectricDTO> findElectricByDeviceIdList(List<Long> deviceIdList) {
        if (CollectionUtils.isEmpty(deviceIdList)) {
            return Collections.emptyList();
        }
        List<DeviceElectric> deviceElectricList = deviceElectricMapper.selectList(deviceIdList);
        if (CollectionUtils.isEmpty(deviceElectricList)) {
            return Collections.emptyList();
        }
        return deviceElectricList.stream().map(deviceElectric -> {
            DeviceElectricDTO deviceElectricDTO = new DeviceElectricDTO();
            deviceElectricDTO.setDeviceId(deviceElectric.getDeviceId());
            deviceElectricDTO.setDeviceSn(deviceElectric.getDeviceSn());
            deviceElectricDTO.setElectric(deviceElectric.getElectric());
            deviceElectricDTO.setGmtCreate(deviceElectric.getGmtCreate());
            deviceElectricDTO.setGmtModify(deviceElectric.getGmtModify());
            return deviceElectricDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean updateBluetoothMac(String bluetoothMac, Long deviceId) {
        BizAssert.notNull(deviceId, "deviceId must not be null");
        deviceMapper.updateBluetoothMac(bluetoothMac, deviceId);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean doDeviceCapacityExceeded(Long orgId, List<Long> deviceIds) {
        Boolean result = Boolean.TRUE;
        if (deviceIds.isEmpty()) {
            log.info("设备Id列表不存在,放弃处理超限deviceIds:{}", deviceIds);
            return result;
        }
        List<Device> devices = this.listByIds(deviceIds);
        if (devices.isEmpty()) {
            log.info("设备信息不存在,放弃处理超限devices:{}", devices);
            return result;
        }

        //考勤机只统计考勤权限组的设备容量
        List<Long> attendanceDeviceIdList = devices.stream()
            .filter(d -> deviceTypePropertyManager.getDeviceTypeByPropertyKey(
                DeviceTypeConstants.ROOSTER_DEVICE_LIST).contains(d.getDeviceType()))
            .map(Device::getDeviceId).collect(Collectors.toList());
        List<Long> otherDeviceIdList = devices.stream()
            .filter(d -> !deviceTypePropertyManager.getDeviceTypeByPropertyKey(
                DeviceTypeConstants.ROOSTER_DEVICE_LIST).contains(d.getDeviceType()))
            .map(Device::getDeviceId).collect(Collectors.toList());
        //考勤机设备id->考勤组
        Map<Long/*deviceId*/, List<Long>/*groupId*/> attendanceMap = deviceGroupManager
            .getDeviceIdToGroupIdsByOrgIdAndDeviceIds(orgId, attendanceDeviceIdList,
                GroupAppType.MO_ATTENDANCE.getValue());
        //其它设备id->权限组
        Map<Long/*deviceId*/, List<Long>/*groupId*/> otherMap = deviceGroupManager
            .getDeviceIdToGroupIdsByOrgIdAndDeviceIds(orgId, otherDeviceIdList,
                GroupAppType.GATE.getValue());
        List<UpdateDeviceDTO> deviceDTOS = new ArrayList<>();
        for (Device device : devices) {
            Integer personSize = 0;
            List<Long> groupIdList = null;
            if (deviceTypePropertyManager
                .getDeviceTypeByPropertyKey(DeviceTypeConstants.ROOSTER_DEVICE_LIST)
                .contains(device.getDeviceType())) {
                if (attendanceMap != null) {
                    groupIdList = attendanceMap.get(device.getDeviceId());
                }
            } else {
                if (otherMap != null) {
                    groupIdList = otherMap.get(device.getDeviceId());
                }
            }
            if (CollectionUtils.isNotEmpty(groupIdList)) {
                personSize = groupRelationService
                    .getDistinctPersonSize(orgId, groupIdList)
                    .pickDataThrowException();
            }
            Integer deviceCapacity = deviceTypeMapManager.getDeviceCapacity(device.getDeviceType());
            if (deviceCapacity != null) {
                if (deviceCapacity < personSize) {
                    //对数据库中非超限设备进行超限状态的变更
                    if (device.getCapacityExceeded() == null || YesNoFlag.YES.getValue() != device
                        .getCapacityExceeded()) {
                        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
                        updateDeviceDTO.setDeviceId(device.getDeviceId());
                        updateDeviceDTO.setCapacityExceeded(YesNoFlag.YES.getValue());
                        updateDeviceDTO.setOrgId(device.getOrgId());
                        deviceDTOS.add(updateDeviceDTO);
                    }
                } else {
                    //对数据库中超限设备进行超限状态的变更
                    if (device.getCapacityExceeded() == null || YesNoFlag.YES.getValue() == device
                        .getCapacityExceeded()) {
                        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
                        updateDeviceDTO.setDeviceId(device.getDeviceId());
                        updateDeviceDTO.setCapacityExceeded(YesNoFlag.NO.getValue());
                        updateDeviceDTO.setOrgId(device.getOrgId());
                        deviceDTOS.add(updateDeviceDTO);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(deviceDTOS)) {
            deviceManager.batchUpdate(deviceDTOS);
            log.info("设备超限处理完成:{}", deviceDTOS);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncIotDeviceByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");

        // 查询魔链设备
        List<IotDeviceInfoDTO> iotDeviceList = iotDeviceInstanceService.queryDeviceInfoList(
            orgId).pickDataThrowException();
        if (ObjectUtils.isEmpty(iotDeviceList)) {
            log.info("机构id:{}，无iot设备", orgId);
            return;
        }
        List<String> iotDeviceSn = iotDeviceList.stream()
            .map(IotDeviceInfoDTO::getId).collect(Collectors.toList());

        // 魔链不存在的设备魔蓝存在，需要同步删除魔蓝上的设备
        List<Integer> iotDeviceType = deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.IOT_DEVICE_LIST);
        List<Device> devices = deviceManager.listByOrgIdAndTypes(orgId, iotDeviceType);

        List<Long> removeDeviceIds = devices.stream().filter(e -> {
            // 先判断是否是虚拟设备
            Integer virtualFlag = e.getVirtualFlag();
            if (!ObjectUtils.isEmpty(virtualFlag) && YesNoFlag.YES.getValue() == virtualFlag) {
                // 如果是虚拟设备，判断父设备是否在同步列表中，如果不存在就删除，如果存在就不删除
                return !iotDeviceSn.contains(e.getParentDeviceSn());
            } else {
                return !iotDeviceSn.contains(e.getDeviceSn());
            }
        }).map(Device::getDeviceId).collect(Collectors.toList());
        deviceManager.batchDeleteByOrgIdAndDeviceId(orgId, removeDeviceIds);

        // 同步新设备
        List<String> existDeviceSns = getExistDeviceSnList(iotDeviceSn);
        List<IotDeviceInfoDTO> notExistDeviceList = iotDeviceList.stream().filter(device ->
            !existDeviceSns.contains(device.getId())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(notExistDeviceList)) {
            log.info("机构id:{} 无新增的iot设备", orgId);
            return;
        }
        iotDeviceListSaveToEquipment(orgId, notExistDeviceList);
    }

    @Override
    public List<String> listParentSnByIotParentDeviceSns(List<String> parentDeviceSns) {
        if (ObjectUtils.isEmpty(parentDeviceSns)) {
            return Lists.newArrayList();
        }
        return deviceMapper.listParentSnByIotParentDeviceSns(parentDeviceSns,
            deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
    }

    @Override
    public List<Device> listByOrgIdAndParentDeviceSns(Long orgId, List<String> parentDeviceSns) {
        if (ObjectUtils.isEmpty(parentDeviceSns) || Objects.isNull(orgId)) {
            return Lists.newArrayList();
        }
        return deviceMapper.listByOrgIdAndParentDeviceSns(orgId, parentDeviceSns,
            deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
    }

    @Override
    public Boolean batchDeleteByOrgIdAndDeviceId(Long orgId, List<Long> deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        if (ObjectUtils.isEmpty(deviceId)){
            return Boolean.FALSE;
        }
        deviceMapper.batchDeleteByOrgIdAndDeviceId(orgId, deviceId);
        return Boolean.TRUE;
    }


    @Override
    public Pagination<Device> listPageByCondition(QueryPageDeviceBO bo) {
        Long orgId = bo.getOrgId();
        BizAssert.notNull(orgId, "orgId must be not null");
        Pagination<Device> pa = new Pagination<>();
        List<Long> deviceIds = new ArrayList<>();
        List<Long> conditionDeviceIds = bo.getDeviceIds();
        if (CollectionUtils.isNotEmpty(conditionDeviceIds)) {
            deviceIds.addAll(conditionDeviceIds);
        }

        // 查询机构下所有满足在离线的设备id，反查设备表
        if (bo.getOnline() != null) {
            List<Long> deviceStatusIds = deviceManagerHelper.listDeviceIdByOrgIdsAndStatus(orgId, bo.getOnline(), bo.getDeviceTypes());
            if (CollectionUtils.isEmpty(deviceStatusIds)) {
                return pa;
            }
            // deviceId求交集
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                deviceIds.retainAll(deviceStatusIds);
                if (CollectionUtil.isEmpty(deviceIds)) {
                    return pa;
                }
            }else {
                deviceIds.addAll(deviceStatusIds);
            }
        }

        // 位置条件查询
        if (CollectionUtils.isNotEmpty(bo.getTreeIds())) {
            List<Long> treeDeviceIds = deviceManagerHelper.listDeviceIdsByTreeIds(orgId, bo.getTreeIds(), bo.getSubTreeFlag());
            if (CollectionUtil.isEmpty(treeDeviceIds)) {
                return pa;
            }
            // deviceId求交集
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                deviceIds.retainAll(treeDeviceIds);
                if (CollectionUtil.isEmpty(deviceIds)) {
                    return pa;
                }
            }else {
                deviceIds.addAll(treeDeviceIds);
            }
        }

        // 模糊搜索位置（空间全路径+设备位置）
        if (StringUtils.isNotBlank(bo.getKeywords())) {
            List<Long> treeNameLikeDeviceIds = treeService.listDeviceIdByLikeTreeName(orgId, bo.getKeywords())
                    .pickDataThrowException();
            bo.setLikeTreeNameDeviceId(treeNameLikeDeviceIds);
        }
        bo.setDeviceIds(deviceIds);
        Page<Device> page = PageHelper.startPage(bo.getPageNo() != null ? bo.getPageNo() : 1,
                        bo.getPageSize() != null ? bo.getPageSize() : 20)
                .doSelectPage(() -> deviceMapper.listDeviceByConditionLike(bo));
        pa.setData(page.getResult());
        pa.setPageNo(page.getPageNum());
        pa.setPageSize(page.getPageSize());
        pa.setData(page.getResult());
        pa.setTotalCount((int) page.getTotal());
        return pa;
    }

    private List<String> getExistDeviceSnList(List<String> deviceSns) {
        Set<String> existDeviceSns = new HashSet<>();
        if (ObjectUtils.isEmpty(deviceSns)) {
            return new ArrayList<>(existDeviceSns);
        }

        // 查出真实设备的设备sn列表
        List<Device> devices = deviceMapper.listByDeviceSns(deviceSns);
        if (CollectionUtils.isNotEmpty(devices)) {
            existDeviceSns
                .addAll(devices.stream().map(Device::getDeviceSn).collect(Collectors.toSet()));
        }

        // 查出虚拟设备的设备sn列表
        List<String> existVarDeviceSns = deviceMapper.listParentSnByIotParentDeviceSns(deviceSns,
            deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants
                .NEED_SPLIT_IOT_DEVICE_LIST));
        if (CollectionUtils.isNotEmpty(existVarDeviceSns)) {
            existDeviceSns.addAll(existVarDeviceSns);
        }
        return new ArrayList<>(existDeviceSns);
    }

    private void iotDeviceListSaveToEquipment(Long orgId, List<IotDeviceInfoDTO> iotDeviceList) {
        List<String> needSplitDeviceIds = Lists.newArrayList();
        Map<String/*deviceId*/, Integer/*deviceType*/> deviceIdTypeMap = Maps.newHashMap();
        Map<String/*deviceId*/, IotDeviceInfoDTO/*deviceInfo*/> deviceInfoMap = Maps.newHashMap();
        PositionInfo positionInfo = positionService.getRootPosition(orgId)
            .pickDataThrowException();

        QueryTreeDTO treeDTO = new QueryTreeDTO();
        treeDTO.setParentId(0L);
        treeDTO.setOrgId(orgId);
        List<TreeDTO> treeDTOS = spaceTreeService.listByCondition(treeDTO).pickDataThrowException();
        Long rootTreeId;
        if (!ObjectUtils.isEmpty(treeDTOS)){
            TreeDTO item = treeDTOS.get(0);
            rootTreeId = item.getTreeId();
        } else {
            rootTreeId = null;
        }
        Long positionId = Objects.isNull(positionInfo) ? null : positionInfo.getPositionId();
        List<Device> deviceList = Lists.newArrayList();
        iotDeviceList.forEach(iotDevice -> {
            Integer deviceType = DeviceType.iotDeviceTypeByProductName(iotDevice.getName());
            if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants
                .NEED_SPLIT_IOT_DEVICE_LIST, deviceType) && !iotDevice.getName().contains("vensi")) {
                // 如果是需要拆分的设备 需要查询属性生成虚拟设备
                needSplitDeviceIds.add(iotDevice.getId());
                deviceIdTypeMap.put(iotDevice.getId(), deviceType);
                deviceInfoMap.put(iotDevice.getId(), iotDevice);
                log.info("拆分iot设备，iot设备id:{}, iot设备名称:{},设备类型:{}", iotDevice.getId(),
                    iotDevice.getName(), deviceType);
            }
            Device deviceByIotDevice = createDeviceByIotDevice(orgId, iotDevice, deviceType,
                positionId);
            // 非虚拟设备
            deviceByIotDevice.setVirtualFlag(0);
            deviceList.add(deviceByIotDevice);
        });
        if (!ObjectUtils.isEmpty(needSplitDeviceIds)) {
            List<BatchIotDevicePropertyInfoDTO> properties = iotDevicePropertyService.batchGetDevicePropertyDefineList(
                orgId, needSplitDeviceIds).pickDataThrowException();
            if (!ObjectUtils.isEmpty(properties)) {
                // 虚拟设备
                List<Device> virtualDevice = createDeviceListByIotProperties(orgId,
                    properties, deviceIdTypeMap, deviceInfoMap, positionId);
                virtualDevice.forEach(e-> e.setVirtualFlag(1));
                deviceList.addAll(virtualDevice);
            } else {
                log.info("需要拆分的iot设备:{},未查询到任何属性", needSplitDeviceIds);
            }
        }
        log.info("本次生成设备数量:{}，设备列表:{}", deviceList.size(), deviceList);
        if (!ObjectUtils.isEmpty(deviceList)) {
            BatchIdDto batchIdDto = idgeneratorService.getNextIdBatchBytypeName(
                BeanConstants.DEVICE, deviceList.size()).pickDataThrowException();
            deviceList.forEach(device -> {
                device.setPosition(device.getDeviceName());
                device.setDeviceId(batchIdDto.nextId());
                if (rootTreeId != null){
                    // 默认将同步设备绑定到更空间下
                    BindSpaceTreeDto dto = new BindSpaceTreeDto();
                    dto.setOrgId(orgId);
                    dto.setDeviceId(String.valueOf(device.getDeviceId()));
                    dto.setTreeId(rootTreeId);
                    dto.setDeviceSource(1);
                    dto.setSendMsgFlag(Boolean.TRUE);
                    treeDeviceRelationServiceV2.bindSpace(dto).pickDataThrowException();
                }
            });
            deviceMapper.batchInsert(deviceList);
        }
    }

    private List<Device> createDeviceListByIotProperties(Long orgId, List<BatchIotDevicePropertyInfoDTO> properties,
        Map<String, Integer> deviceIdTypeMap,
        Map<String, IotDeviceInfoDTO> deviceInfoMap, Long positionId) {
        List<Device> devices = Lists.newArrayList();
        properties.forEach(property -> {
            IotDeviceInfoDTO iotDeviceInfo = deviceInfoMap.get(property.getId());
            Integer deviceType = deviceIdTypeMap.get(property.getId());
            if (ObjectUtils.isEmpty(property.getProperties())) {
                devices.add(createDefaultIotDevice(orgId, positionId, deviceType, property, iotDeviceInfo));
            } else {
                List<String> groups = Lists.newArrayList();
                for (IotDevicePropertyDefineDTO p : property.getProperties()) {
                    if (!p.getId().contains(DeviceIotSplitConstants.UNDERLINE)) {
                        continue;
                    }
                    String group = getGroup(p, deviceType);
                    if (StringUtils.isEmpty(group)) {
                        continue;
                    }
                    if (!groups.contains(group)) {
                        groups.add(group);
                    }
                }
                if (ObjectUtils.isEmpty(groups)) {
                    devices.add(createDefaultIotDevice(orgId, positionId, deviceType, property, iotDeviceInfo));
                } else {
                    // 如果是空调控制器
                    if (DeviceType.AIR_CONDITIONING_GATEWAY.getValue() == deviceType
                    || DeviceType.AIR_CONDITIONING_GATEWAY_64.getValue() == deviceType){
                        deviceType = DeviceType.AIR_CONDITIONER.getValue();
                    }else if (DeviceType.TURN_ON_OFF_THREE_BUTTON.getValue() == deviceType
                        || DeviceType.TURN_ON_OFF_TWO_BUTTON.getValue() == deviceType){ // 如果是三键、二键开关
                        deviceType = DeviceType.SWITCH.getValue();
                    }
                    Integer finalDeviceType = deviceType;
                    groups.forEach(group -> {
                        Device device = new Device();
                        device.setOrgId(orgId);
                        device.setPositionId(positionId);
                        device.setDeviceName(property.getName() + DeviceIotSplitConstants.UNDERLINE + group);
                        device.setDeviceType(finalDeviceType);
                        device.setDeviceSn(property.getId() + DeviceIotSplitConstants.UNDERLINE + group);
                        device.setStatus(1);
                        device.setActiveTime(Objects.isNull(iotDeviceInfo) ||
                            Objects.isNull(iotDeviceInfo.getRegisterTime()) ? null :
                            new Date(iotDeviceInfo.getRegisterTime()));
                        device.setParentDeviceSn(getIoTDeviceParentId(deviceIdTypeMap.get(property.
                            getId()), iotDeviceInfo.getParentId(), iotDeviceInfo.getId(),Boolean.TRUE));
                        devices.add(device);
                    });
                }
            }
        });
        return devices;
    }

    private String getGroup(IotDevicePropertyDefineDTO property, Integer deviceType) {
        String group = "";
        // 分组标识 目前只考虑二键开关、三件开关、空调网关
        // todo 配置优化
        if (Objects.equals(deviceType, DeviceType.TURN_ON_OFF_TWO_BUTTON.getValue()) ||
            Objects.equals(deviceType, DeviceType.TURN_ON_OFF_THREE_BUTTON.getValue())) {
            // 二键开关、三件开关分组标识在最前面
            group = property.getId().substring(0, property.getId().indexOf("_"));
        } else if (Objects.equals(deviceType, DeviceType.AIR_CONDITIONING_GATEWAY.getValue())
            || Objects.equals(deviceType, DeviceType.AIR_CONDITIONING_GATEWAY_64.getValue())) {
            // 空调网关分组标识在最后面
            group = property.getId().substring(property.getId().lastIndexOf(DeviceIotSplitConstants.UNDERLINE) + 1);
            if (DeviceIotPropertyConstants.NOT_SPLIT_PROPERTIES.contains(group)) {
                group = "";
            }
        }
        return group;
    }

    private Device createDeviceByIotDevice(Long orgId, IotDeviceInfoDTO iotDevice, Integer deviceType, Long positionId) {
        Device device = new Device();
        device.setOrgId(orgId);
        device.setPositionId(positionId);
        device.setDeviceName(iotDevice.getName());
        device.setDeviceType(deviceType);
        device.setDeviceSn(iotDevice.getId());
        device.setStatus(1);
        device.setActiveTime(Objects.isNull(iotDevice.getRegisterTime()) ? new Date() :
            new Date(iotDevice.getRegisterTime()));
        device.setParentDeviceSn(getIoTDeviceParentId(deviceType, iotDevice.getParentId(),
            iotDevice.getId(), Boolean.FALSE));
        return device;
    }

    private Device createDefaultIotDevice(Long orgId, Long positionId, Integer deviceType,
        BatchIotDevicePropertyInfoDTO property, IotDeviceInfoDTO iotDeviceInfo) {
        // 如果是空调控制器
        if (DeviceType.AIR_CONDITIONING_GATEWAY.getValue() == deviceType
            || DeviceType.AIR_CONDITIONING_GATEWAY_64.getValue() == deviceType){
            deviceType = DeviceType.AIR_CONDITIONER.getValue();
        }else if (DeviceType.TURN_ON_OFF_THREE_BUTTON.getValue() == deviceType
            || DeviceType.TURN_ON_OFF_TWO_BUTTON.getValue() == deviceType){ // 如果是三键、二键开关
            deviceType = DeviceType.SWITCH.getValue();
        }
        Device device = new Device();
        device.setOrgId(orgId);
        device.setPositionId(positionId);
        device.setDeviceName(property.getName());
        device.setDeviceType(deviceType);
        device.setDeviceSn(property.getId());
        device.setStatus(1);
        device.setActiveTime(Objects.isNull(iotDeviceInfo) ||
            Objects.isNull(iotDeviceInfo.getRegisterTime()) ? new Date() :
            new Date(iotDeviceInfo.getRegisterTime()));
        device.setParentDeviceSn(getIoTDeviceParentId(deviceType, iotDeviceInfo.getParentId(),
            iotDeviceInfo.getId(), Boolean.FALSE));
        return device;
    }

    /**
     * 获取iot设备的父设备Id
     *
     * @return
     */
    private String getIoTDeviceParentId(Integer deviceType, String parentId,
        String Id, Boolean isVirtual) {
        String parentDeviceSn;
        //如果是需要拆分的设备，该虚拟设备的父sn就是id
        if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants
            .NEED_SPLIT_IOT_DEVICE_LIST, deviceType)) {
            if (isVirtual) {
                parentDeviceSn = Id;
            } else {
                parentDeviceSn = parentId;
            }
        } else {
            parentDeviceSn = parentId;
        }
        return parentDeviceSn;
    }

    /**
     * 根据设备在线状态获取设备Id列表
     *
     * @param orgIds        机构号列表
     * @param onlineStatus 设备在线状态 0-离线 1-在线
     * @return
     */
    private List<Long> listDeviceIdsByOnlineStatus(List<Long> orgIds, Integer onlineStatus) {
        List<Long> deviceIds = new ArrayList<>();
        List<Device> devices = deviceMapper.listDevicesByOrgIds(orgIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            Map<String, DeviceStateRequest> map = new HashMap<>(devices.size());
            for (Device device : devices) {
                if (!map.containsKey(device.getDeviceSn())) {
                    DeviceStateRequest deviceStateRequest = new DeviceStateRequest();
                    deviceStateRequest.setSerialNumber(device.getDeviceSn());
                    map.put(device.getDeviceSn(), deviceStateRequest);
                }
            }
            Map<String, DeviceStateResponse> dsMap = hubControlServiceV1.getDeviceListState(map)
                .pickDataThrowException();
            List<String> onlineDeviceSn = new ArrayList<>();
            List<String> OfflineDeviceSn = new ArrayList<>();
            List<String> deviceSns = new ArrayList<>();
            if (dsMap != null) {
                for (DeviceStateResponse deviceStateResponse : dsMap.values()) {
                    if (deviceStateResponse.getOnline()) {
                        onlineDeviceSn.add(deviceStateResponse.getSerialNumber());
                    } else {
                        OfflineDeviceSn.add(deviceStateResponse.getSerialNumber());
                    }
                }
                if (onlineStatus.equals(YesNoFlag.YES.getValue())) {
                    deviceSns.addAll(onlineDeviceSn);
                } else {
                    deviceSns.addAll(OfflineDeviceSn);
                }
            }
            if (CollectionUtils.isNotEmpty(deviceSns)) {
                List<Device> deviceList = deviceMapper.listByDeviceSns(deviceSns);
                if (CollectionUtils.isNotEmpty(deviceList)) {
                    deviceIds = deviceList.stream().map(Device::getDeviceId)
                        .collect(Collectors.toList());
                }
            }
        }
        return deviceIds;
    }

    private <T> List<List<T>> subList(List<T> list, int splitSize) {
        List<List<T>> lists = new ArrayList<>();
        int size = list.size();
        if (size <= splitSize) {
            lists.add(list);
            return lists;
        }
        int number = size / splitSize;
        //完整的分隔部分
        for (int i = 0; i < number; i++) {
            int startIndex = i * splitSize;
            int endIndex = (i + 1) * splitSize;
            lists.add(list.subList(startIndex, endIndex));
        }
        //最后分隔剩下的部分直接放入list
        if (number * splitSize == size) {
            return lists;
        }
        lists.add(list.subList(number * splitSize, size));
        return lists;
    }

    private Device deviceAddRequestToDevice(AddDeviceDTO dto) {
        Device existDevice = deviceMapper.getByDeviceSn(dto.getDeviceSn());
        if (existDevice != null) {
            ExceptionUtils.throwException(DeviceErrorCode.DEVICE_EXIST,
                String.format(DeviceErrorCode.DEVICE_EXIST.getMessage(), dto.getDeviceSn()));
        }

        Device device = new Device();
        Long id = idgeneratorService
            .getNextIdByTypeName("com.moredian.magicube.device.dao.entity.Device").getData();
        device.setDeviceId(id);
        device.setOrgId(dto.getOrgId());
        if (dto.getPositionId() == null) {
            device.setPositionId(this.getRootPosition(dto.getOrgId()).getPositionId());
        } else {
            device.setPositionId(dto.getPositionId());
        }
        device.setPosition(dto.getPosition());
        device.setDeviceType(dto.getDeviceType());
        if (StringUtils.isBlank(dto.getDeviceName())) {
            if (StringUtils.isNotEmpty(dto.getDeviceSn()) && dto.getDeviceSn().length() >= 6) {
                String sn = dto.getDeviceSn();
                String prefixName = "";
                GetRelationBySerialNumberRequest spuRequest = new GetRelationBySerialNumberRequest();
                spuRequest.setSerialNumberList(Arrays.asList(sn));
                ServiceResponse<List<SimpleSpuInventoryRelationResponse>> serviceResponse = spuInventoryRelationService
                    .findSpuInventoryRelationBySerialNumberList(spuRequest);
                if (serviceResponse.isSuccess() && serviceResponse.isExistData()) {
                    List<SimpleSpuInventoryRelationResponse> spuList = serviceResponse.getData();
                    if (CollectionUtils.isNotEmpty(spuList) && spuList.get(0) != null) {
                        SimpleSpuInventoryRelationResponse simpleSpuInventoryRelationResponse = spuList
                            .get(0);
                        prefixName = simpleSpuInventoryRelationResponse.getSpuDisplayName();
                    }
                }
                if (StringUtils.isBlank(prefixName)) {
                    String deviceEnumName = deviceTypeManager.getName(dto.getDeviceType());
                    prefixName = deviceEnumName
                        .replaceAll(REGEX_CHINESE, org.apache.commons.lang.StringUtils.EMPTY);
                }
                if (StringUtils.isBlank(prefixName) && deviceTypePropertyManager
                    .containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_SPU_LIST,
                        dto.getDeviceType())) {
                    prefixName = deviceTypeMapManager.getDefaultSpu(dto.getDeviceType());
                }
                String suffixName = sn.substring(sn.length() - 6);
                device.setDeviceName(prefixName + "-" + suffixName);
            } else {
                String deviceName = deviceTypeManager.getName(dto.getDeviceType())
                    .replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                if (StringUtils.isBlank(deviceName) && deviceTypePropertyManager
                    .containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_SPU_LIST,
                        dto.getDeviceType())) {
                    deviceName = deviceTypeMapManager.getDefaultSpu(dto.getDeviceType());
                }
                device.setDeviceName(deviceName);
            }
        } else {
            device.setDeviceName(dto.getDeviceName());
        }
        device.setDeviceSn(dto.getDeviceSn());
        device.setActiveTime(new Date());
        device.setStatus(DeviceStatus.USABLE.getValue());
        return device;
    }

    private PositionInfo getRootPosition(Long orgId) {
        return positionService.getRootPosition(orgId).pickDataThrowException();
    }

    private void enableBiz(Device device) {
        Integer deviceType = device.getDeviceType();
        //需要绑定默认组的，开通RECOGNIZE和OPENDOOR 目前来说是这样，以后可能有变更
        if (deviceTypePropertyManager
            .containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_GROUP_LIST, deviceType)) {
            orgService.enableBizList(device.getOrgId(),
                Collections.singletonList(BizType.RECOGNIZE.getValue()));
            orgService.enableBizList(device.getOrgId(),
                Collections.singletonList(BizType.OPENDOOR.getValue()));
        } else if (deviceTypePropertyManager
            .containsDeviceType(DeviceTypeConstants.SINGLE_VISITOR_DEVICE_TYPE_LIST, deviceType)) {
            orgService.enableBizList(device.getOrgId(),
                Collections.singletonList(BizType.RECOGNIZE.getValue()));
            orgService.enableBizList(device.getOrgId(),
                Collections.singletonList(BizType.VISITOR.getValue()));
        }
    }

    /**
     * 填充设备时区和语言的默认值
     * 当设备的时区或语言为空时，从机构配置中获取默认值
     *
     * @param device 设备实体
     */
    private void fillDeviceTimeZoneAndLanguageDefaults(Device device) {
        if (device == null || device.getOrgId() == null) {
            return;
        }

        // 如果设备时区和语言都不为空，则不需要处理
        if (device.getDeviceTimeZone() != null && device.getDeviceLanguage() != null) {
            return;
        }

        try {
            // 调用HolidayService获取机构的默认配置
            HolidayDTO holidayConfig = holidayService.getHolidayConfigByOrgId(device.getOrgId()).pickDataThrowException();
            // 如果设备时区为空，使用默认时区
            if (device.getDeviceTimeZone() == null && holidayConfig.getTimeZoneCode() != null) {
                device.setDeviceTimeZone(holidayConfig.getTimeZoneCode());
            }

            // 如果设备语言为空，使用默认语言
            if (device.getDeviceLanguage() == null && holidayConfig.getLanguageCode() != null) {
                device.setDeviceLanguage(holidayConfig.getLanguageCode());
            }
        } catch (Exception e) {
            log.warn("获取机构默认时区和语言配置失败, orgId: {}, error: {}", device.getOrgId(), e.getMessage());
        }
    }

    /**
     * 批量填充设备时区和语言的默认值
     *
     * @param devices 设备列表
     */
    private void fillDeviceTimeZoneAndLanguageDefaults(List<Device> devices) {
        if (CollectionUtils.isEmpty(devices)) {
            return;
        }

        // 按机构ID分组，减少接口调用次数
        Map<Long, List<Device>> orgDeviceMap = devices.stream()
            .filter(device -> device.getOrgId() != null)
            .collect(Collectors.groupingBy(Device::getOrgId));

        for (Map.Entry<Long, List<Device>> entry : orgDeviceMap.entrySet()) {
            Long orgId = entry.getKey();
            List<Device> orgDevices = entry.getValue();

            try {
                // 调用HolidayService获取机构的默认配置
                HolidayDTO holidayConfig = holidayService.getHolidayConfigByOrgId(orgId).pickDataThrowException();
                for (Device device : orgDevices) {
                    // 如果设备时区为空，使用默认时区
                    if (device.getDeviceTimeZone() == null && holidayConfig.getTimeZoneCode() != null) {
                        device.setDeviceTimeZone(holidayConfig.getTimeZoneCode());
                    }

                    // 如果设备语言为空，使用默认语言
                    if (device.getDeviceLanguage() == null && holidayConfig.getLanguageCode() != null) {
                        device.setDeviceLanguage(holidayConfig.getLanguageCode());
                    }
                }
            } catch (Exception e) {
                log.warn("获取机构默认时区和语言配置失败, orgId: {}, error: {}", orgId, e.getMessage());
            }
        }
    }
}