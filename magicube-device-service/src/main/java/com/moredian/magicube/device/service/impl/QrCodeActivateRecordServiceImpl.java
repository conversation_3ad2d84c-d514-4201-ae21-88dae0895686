package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;

import com.moredian.magicube.device.dto.qrcode.QrCodeActivateRecordResponse;
import com.moredian.magicube.device.manager.QrCodeActivateRecordManager;
import com.moredian.magicube.device.service.QrCodeActivateRecordService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Classname： QrCodeActivateRecordServiceImpl
 * @Date: 2023/1/12 11:18 上午
 * @Author: _AF
 * @Description:
 */
@SI
public class QrCodeActivateRecordServiceImpl implements QrCodeActivateRecordService {
    @Autowired
    private QrCodeActivateRecordManager qrCodeActivateRecordManager;

    @Override
    public ServiceResponse<List<QrCodeActivateRecordResponse>> listByQrCodeId(Long orgId, Long qrCodeId) {
        return new ServiceResponse<>(qrCodeActivateRecordManager.listByQrCodeId(orgId, qrCodeId));
    }

    @Override
    public ServiceResponse<QrCodeActivateRecordResponse> getByDeviceId(Long orgId, Long deviceId) {
        return new ServiceResponse<>(qrCodeActivateRecordManager.getByDeviceId(orgId, deviceId));
    }

    @Override
    public ServiceResponse<QrCodeActivateRecordResponse> getNewestRecordByOrgId(Long orgId) {
        return new ServiceResponse<>(qrCodeActivateRecordManager.getNewestRecordByOrgId(orgId));
    }
}
