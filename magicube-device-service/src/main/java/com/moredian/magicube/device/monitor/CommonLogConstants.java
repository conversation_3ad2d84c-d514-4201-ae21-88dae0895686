package com.moredian.magicube.device.monitor;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/31
 */
public interface CommonLogConstants {

    String LOG_SYSTEM = "fishnet-device-service";

    String ACTIVATE_UNBIND_DEVICE_LOG_STORE = "activate-unbind-device";
//    String UNBIND_DEVICE_LOG_STORE = "fishnet-device-service-monitor-log-unbind-device";

    //激活解绑
    String MONITOR_BIZ_ACTIVE_DEVICE = "ACTIVE";
    String MONITOR_BIZ_UNBIND_DEVICE = "UNBIND";

    /**
     * 上传日志
     */
    String LOG_STORE = "device-monitor-log";

}
