package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.SpaceRuleRelation;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: SpaceRuleRelationManager.java, v 1.0 Exp $
 */
public interface SpaceRuleRelationManager {
    List<SpaceRuleRelation> getRelationByOrgIdAndRuleId(Long orgId, Long ruleId);

    List<Long> getSpaceIdByOrgIdAndRuleId(Long orgId, Long ruleId);

    List<SpaceRuleRelation> getRelationByOrgId(Long orgId);

    List<Long> getRuleIdByOrgIdAndSpaceId(Long orgId, Long spaceId);

    List<SpaceRuleRelation> getRelationByOrgIdAndSpaceId(Long orgId, Long ruleId, Long spaceId);

    void ruleRelateSpace(Long orgId, Long ruleId, List<Long> spaceIdList);

    void addOrUpdate(List<SpaceRuleRelation> updateSpaceRuleRelationList);

    void delete(Long orgId, List<Long> spaceRuleRelationIdList);
}
