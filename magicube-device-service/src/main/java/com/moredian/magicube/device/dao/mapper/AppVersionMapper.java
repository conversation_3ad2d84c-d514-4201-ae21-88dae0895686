package com.moredian.magicube.device.dao.mapper;

import com.github.pagehelper.Page;
import com.moredian.magicube.device.dao.entity.AppVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * app版本信息Dao
 *
 * <AUTHOR>
 */

@Mapper
public interface AppVersionMapper {

    /**
     * 增加app版本信息
     *
     * @param appVersion
     * @return
     */
    void addAppVersion(AppVersion appVersion);

    /**
     * 修改app版本信息
     *
     * @param appVersion
     * @return 修改记录条数
     */
    int updateAppVersion(AppVersion appVersion);

    /**
     * 修改app版本信息,只修改属性不为null的字段
     *
     * @param appVersion
     * @return 修改记录条数
     */
    int updateAppVersionSelective(AppVersion appVersion);

    /**
     * 根据ID删除app版本信息
     *
     * @param id
     * @return 删除记录条数
     */
    int removeAppVersionById(Long id);

    /**
     * 根据id获取app版本信息
     *
     * @param id
     * @return
     */
    AppVersion getAppVersionById(Long id);

    /**
     * (added by zc)
     * 根据app适用系统类型、app类型、版本编码获取最新的app/rom版本信息
     *
     * @param appVersion
     * @return
     */
    AppVersion getAppVersionBySCTypeCode(AppVersion appVersion);

    /**
     * 根据app适用系统类型、app类型获取最新的app版本信息
     *
     * @param appVersion
     * @return
     */
    AppVersion getNewAppVersionBySCType(AppVersion appVersion);

    /**
     * 根据app适用系统类型、app适用终端类型获取最新的是全部升级状态的app版本信息.
     *
     * @param appVersion
     * @return
     */
    AppVersion getNewAppVersionBySCTypeAndIsActive(AppVersion appVersion);

    /**
     * 根据id获取app版本信息,同时加锁
     *
     * @param id
     * @return
     */
    AppVersion getAppVersionByIdForUpdate(Long id);

    /**
     * 分页查询app版本信息
     *
     * @param values
     * @return
     */
    List<AppVersion> getPaginationAppVersion(Map values);

    /**
     * 获取app版本信息数量
     *
     * @param appVersion
     * @return
     */
    int getAppVersionCount(AppVersion appVersion);

    /**
     * 获取强制升级app版本数量
     *
     * @param appVersion
     * @return
     */
    int getEnforceUpdateAppVersionCount(AppVersion appVersion);

    int getLowVersionCount(@Param("versionCode") Integer versionCode, @Param("appType") Integer appType);

    AppVersion getAppVersionInfo(AppVersion appVersion);

    List<AppVersion> list();
}