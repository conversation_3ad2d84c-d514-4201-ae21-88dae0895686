package com.moredian.magicube.device.jetlinks.rule.arrange;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 规则编排HTTP接口监听
 * @create 2024-12-24 11:40
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class HttpInNode extends Node{

    public static final String TYPE = "http in";

    private String method;

    /**
     * 监听HTTP服务监听的id，在网络组件中创建
     */
    private String serverId;

    private Boolean upload = false;

    private String url;

    public HttpInNode() {
        super(TYPE);
    }
}
