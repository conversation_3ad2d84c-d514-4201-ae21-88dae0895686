package com.moredian.magicube.device.service.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.filemanager.FileManager;
import com.moredian.bee.filemanager.enums.FilePathType;
import com.moredian.bee.filemanager.model.SaveFileResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceSubject;
import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import com.moredian.magicube.device.dao.entity.SpaceSubjectReleation;
import com.moredian.magicube.device.dao.mapper.SpaceSubjectRelationMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.subject.*;
import com.moredian.magicube.device.enums.DeviceSourceEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.MqttMessageComponent;
import com.moredian.magicube.device.manager.DeviceSubjectManager;
import com.moredian.magicube.device.manager.DeviceSubjectRelationManager;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceSubjectService;
import com.moredian.magicube.device.subscriber.TransferEventType;
import com.moredian.magicube.device.utils.UUIDValidator;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备主题服务实现
 *
 * <AUTHOR>
 * @since 2023-08-28
 */

@SI
@Slf4j
public class DeviceSubjectServiceImpl implements DeviceSubjectService {

    @Autowired
    private FileManager fileManager;

    @Autowired
    @Lazy
    private DeviceSubjectManager deviceSubjectManager;

    @Autowired
    private DeviceSubjectRelationManager deviceSubjectRelationManager;

    @Autowired
    private SpaceSubjectRelationMapper spaceSubjectRelationMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private MqttMessageComponent mqttMessageComponent;

    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;


    @Value("${moredian.attence.screen.path:/postImage/2023/4/10/11}")
    private String screenPath;

    private static int DEVICE_SUBJECT_MAX = 10;

    @Override
    public ServiceResponse<List<String>> listTemplate() {
        ServiceResponse<List<String>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<String> templates = new ArrayList<>();
        templates.add(fileManager
                .getUrlByRelativePath(screenPath + File.separator + "website_resource_1681095685922.png"));
//        templates.add(fileManager
//            .getUrlByRelativePath("/postImage/2023/4/10/11/website_resource_1681095630252.png"));
        templates.add(fileManager
                .getUrlByRelativePath(screenPath + File.separator + "website_resource_1681095645102.png"));
        templates.add(fileManager
                .getUrlByRelativePath(screenPath + File.separator + "website_resource_1681095654100.png"));
        templates.add(fileManager
                .getUrlByRelativePath(screenPath + File.separator + "website_resource_1681095665634.png"));
        templates.add(fileManager
                .getUrlByRelativePath(screenPath + File.separator + "website_resource_1681095676636.png"));
        templates.add(fileManager
                .getUrlByRelativePath(screenPath + File.separator + "website_resource_1681095685921.png"));
        serviceResponse.setData(templates);
        return serviceResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Long> insert(InsertDeviceSubjectDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        if (dto.getType() != null && dto.getType() == 1) {
//            int count = deviceSubjectManager.countByOrgIdAndType(dto.getOrgId(), dto.getType());
//            if (count >= DEVICE_SUBJECT_MAX) {
//                ExceptionUtils.throwException(DeviceErrorCode.DEVICE_SUBJECT_NUMBER_RESTRICTION,
//                    DeviceErrorCode.DEVICE_SUBJECT_NUMBER_RESTRICTION.getMessage());
//            }
        } else if (dto.getType() != null && dto.getType() == 2 && StringUtils
            .isBlank(dto.getName())) {
            //屏保默认随机生成名称
            dto.setName(UUID.randomUUID().toString());
        }
        DeviceSubject deviceSubject = new DeviceSubject();
        BeanUtils.copyProperties(dto, deviceSubject);
        if (dto.getScreenSaver()!=null){
            deviceSubject.setImgUrls("");
            String expend = JSONObject.toJSONString(dto.getScreenSaver());
            for (ScreenSaverItem screenSaverItem : dto.getScreenSaver().getScreenSaverItemList()) {
                screenSaverItem.setUrl(screenSaverItem.getUrl());
            }
            deviceSubject.setExpend(expend);
        }else{
            this.temporaryToPermanent(dto.getImgUrls(), dto.getLogoImgUrl(), deviceSubject);
        }
        Long subjectId = deviceSubjectManager.insert(deviceSubject);
        this.applyToDevice(dto.getOrgId(), subjectId, dto.getAllDeviceFlag(), dto.getDeviceIds(),dto.getSpaceIds());
        serviceResponse.setData(subjectId);
        return serviceResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Boolean> update(UpdateDeviceSubjectDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        if (StringUtils.isBlank(dto.getName())){
            dto.setName(UUID.randomUUID().toString());
        }
        DeviceSubject deviceSubject = new DeviceSubject();
        BeanUtils.copyProperties(dto, deviceSubject);
        if (dto.getScreenSaver()!=null){
            for (ScreenSaverItem screenSaverItem : dto.getScreenSaver().getScreenSaverItemList()) {
                screenSaverItem.setUrl(screenSaverItem.getUrl());
            }
            String expend = JSONObject.toJSONString(dto.getScreenSaver());
            deviceSubject.setExpend(expend);
        }else{
            this.temporaryToPermanent(dto.getImgUrls(), dto.getLogoImgUrl(), deviceSubject);
        }
        Boolean result = deviceSubjectManager.update(deviceSubject);
        this.applyToDevice(dto.getOrgId(), dto.getId(), dto.getAllDeviceFlag(), dto.getDeviceIds(),dto.getSpaceIds());
        serviceResponse.setData(result);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceSubjectDTO> getByOrgIdAndSubjectId(Long orgId, Long subjectId) {
        ServiceResponse<DeviceSubjectDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceSubject deviceSubject = deviceSubjectManager.getById(orgId, subjectId);
        if (deviceSubject == null) {
            return serviceResponse;
        }
        DeviceSubjectDTO deviceSubjectDTO = new DeviceSubjectDTO();
        BeanUtils.copyProperties(deviceSubject, deviceSubjectDTO);
        if (StringUtils.isNotBlank(deviceSubject.getName()) && UUIDValidator.isValidUUID(deviceSubject.getName())){
            deviceSubjectDTO.setName("屏保");
        }

        List<Long> spaceIds = spaceSubjectRelationMapper.listSpaceIdByOrgIdAndSubjectId(orgId, deviceSubject.getId());
        deviceSubjectDTO.setSpaceIds(spaceIds);
        List<DeviceSubjectRelation> relations = deviceSubjectRelationManager
            .listByOrgIdAndSubjectIds(orgId, Lists.newArrayList(deviceSubject.getId()));
        Map<Long, DeviceInfoDTO> deviceIdToDeviceInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            List<Long> deviceIds = relations.stream().map(DeviceSubjectRelation::getDeviceId)
                .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                List<DeviceInfoDTO> deviceInfoDTOS = deviceService.listByIds(deviceIds).pickDataThrowException();
                if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
                    deviceSubjectDTO.setAllDevices(deviceInfoDTOS);
                    deviceIdToDeviceInfoMap = deviceInfoDTOS.stream()
                        .collect(Collectors.toMap(DeviceInfoDTO::getDeviceId, x -> x));
                }
            }
            List<DeviceInfoDTO> devices = new ArrayList<>();
            for (DeviceSubjectRelation relation : relations) {
                if (deviceIdToDeviceInfoMap.get(relation.getDeviceId()) != null) {
                    DeviceInfoDTO deviceInfoDTO = deviceIdToDeviceInfoMap.get(relation.getDeviceId());
                    String path = deviceInfoDTO.getPath();
                    if (StringUtils.isNotBlank(path)){
                        String[] split = path.split("/");
                        Set<Long> longSet = Arrays.stream(split)
                                .map(Long::parseLong)
                                .collect(Collectors.toSet());
                        longSet.retainAll(spaceIds);
                        if (CollectionUtils.isEmpty(longSet)){
                            devices.add(deviceInfoDTO);
                        }
                    }

                }
            }
            deviceSubjectDTO.setDevices(devices);
        }
        if (StringUtils.isNotBlank(deviceSubject.getExpend())){
            ScreenSaver screenSaver = JSONObject.parseObject(deviceSubject.getExpend(), ScreenSaver.class);
            if (screenSaver.getDisplayType()==null){
                screenSaver.setDisplayType(3);
                screenSaver.setStartType(1);
                screenSaver.setStartTime(15);
                screenSaver.setEndType(1);
                screenSaver.setEndTime(10);
            }
            deviceSubjectDTO.setScreenSaver(screenSaver);
        }else if (deviceSubject.getType()==2){
            // 兼容线上原来老数据（默认全天）
            deviceSubjectDTO.setScreenSaver(initOlderData(deviceSubject.getImgUrls()));
        }
        getPathUrl(deviceSubject, deviceSubjectDTO);
        serviceResponse.setData(deviceSubjectDTO);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceSubjectSimpleDTO>> listSimpleByOrgIdAndSubjectIds(Long orgId,
        List<Long> subjectIds) {
        ServiceResponse<List<DeviceSubjectSimpleDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceSubject> deviceSubjects = deviceSubjectManager
            .listByOrgIdAndIds(orgId, subjectIds);
        if (CollectionUtils.isEmpty(deviceSubjects)) {
            return serviceResponse;
        }
        List<DeviceSubjectSimpleDTO> dss = new ArrayList<>();
        for (DeviceSubject ds : deviceSubjects) {
            if (ds!=null && ds.getEnable()==0){
                continue;
            }
            DeviceSubjectSimpleDTO deviceSubjectSimpleDTO = new DeviceSubjectSimpleDTO();
            if (StringUtils.isNotBlank(ds.getExpend())){
                ScreenSaver screenSaver = JSONObject.parseObject(ds.getExpend(), ScreenSaver.class);
                if (screenSaver.getDisplayType()==null){
                    screenSaver.setDisplayType(3);
                    screenSaver.setStartType(1);
                    screenSaver.setStartTime(15);
                    screenSaver.setEndType(1);
                    screenSaver.setEndTime(10);
                }
                deviceSubjectSimpleDTO.setScreenSaver(screenSaver);

            }else if (ds.getType()==2){
                // 兼容线上原来老数据（默认全天）
                deviceSubjectSimpleDTO.setScreenSaver(initOlderData(ds.getImgUrls()));
            }
            BeanUtils.copyProperties(ds, deviceSubjectSimpleDTO);
            if (StringUtils.isNotBlank(ds.getImgUrls())) {
                List<String> urls = new ArrayList<>();
                String[] imgUrls = ds.getImgUrls().split(",");
                for (String imgUrl : imgUrls) {
                    urls.add(fileManager.getUrlByRelativePath(imgUrl));
                }
                deviceSubjectSimpleDTO.setImgUrls(urls);
            }
            if (StringUtils.isNotBlank(ds.getLogoImgUrl())){
                deviceSubjectSimpleDTO.setLogoImgUrl(fileManager.getUrlByRelativePath(ds.getLogoImgUrl()));
            }
            dss.add(deviceSubjectSimpleDTO);
        }
        serviceResponse.setData(dss);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceSubjectDTO>> listByOrgIdAndType(Long orgId, Integer type) {
        ServiceResponse<List<DeviceSubjectDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceSubjectDTO> deviceSubjectDTOS = new ArrayList<>();
        serviceResponse.setData(deviceSubjectDTOS);
        DeviceSubject condition = new DeviceSubject();
        condition.setOrgId(orgId);
        condition.setType(type);
        List<DeviceSubject> deviceSubjects = deviceSubjectManager.listByCondition(condition);
        if (CollectionUtils.isEmpty(deviceSubjects)) {
            return serviceResponse;
        }
        List<Long> subjectIds = deviceSubjects.stream().map(DeviceSubject::getId).distinct()
            .collect(Collectors.toList());
        List<DeviceSubjectRelation> relations = deviceSubjectRelationManager
            .listByOrgIdAndSubjectIds(orgId, subjectIds);

        List<SpaceSubjectReleation> spaceRelations = spaceSubjectRelationMapper
                .listByOrgIdAndSubjectIds(orgId, subjectIds);
        List<Long> spaceIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spaceRelations)){
            List<Long> collect = spaceRelations.stream().map(SpaceSubjectReleation::getSpaceId).collect(Collectors.toList());
            spaceIds.addAll(collect);
        }
        List<Long> deviceIdListBySpaceId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spaceIds)){
            List<TreeDeviceRelationDTO> treeDeviceRelationDTOS = spaceTreeDeviceRelationService.listAllByTreeIdsAndSource(orgId, spaceIds, DeviceSourceEnum.MD.getCode()).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(treeDeviceRelationDTOS)){
                deviceIdListBySpaceId = treeDeviceRelationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId).distinct().collect(Collectors.toList());
            }
        }

        Map<Long, DeviceInfoDTO> deviceIdToDeviceInfoMap = new HashMap<>();
        Map<Long, List<SpaceSubjectReleation>> spaceRelationsMap = spaceRelations.stream().collect(Collectors.groupingBy(SpaceSubjectReleation::getSubjectId));
        Map<Long, List<DeviceSubjectRelation>> subjectIdToRelationsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            subjectIdToRelationsMap = relations.stream().collect(Collectors.groupingBy(
                DeviceSubjectRelation::getSubjectId));
            List<Long> deviceIds = relations.stream().map(DeviceSubjectRelation::getDeviceId)
                .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deviceIdListBySpaceId)){
                deviceIds.addAll(deviceIdListBySpaceId);
                deviceIds = new ArrayList<>(new HashSet<>(deviceIds));
            }
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                List<DeviceInfoDTO> deviceInfoDTOS = deviceService
                    .listByIds( deviceIds).pickDataThrowException();
                if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
                    deviceIdToDeviceInfoMap = deviceInfoDTOS.stream()
                        .collect(Collectors.toMap(DeviceInfoDTO::getDeviceId, x -> x));
                }
            }
        }
        for (DeviceSubject ds : deviceSubjects) {
            DeviceSubjectDTO deviceSubjectDTO = new DeviceSubjectDTO();
            BeanUtils.copyProperties(ds, deviceSubjectDTO);
            List<DeviceSubjectRelation> relationList = subjectIdToRelationsMap.get(ds.getId());
            List<DeviceInfoDTO> devices = new ArrayList<>();
            deviceSubjectDTO.setDevices(devices);
            if (CollectionUtils.isNotEmpty(relationList)) {
                for (DeviceSubjectRelation dr : relationList) {
                    Optional.ofNullable(deviceIdToDeviceInfoMap.get(dr.getDeviceId())).ifPresent(item->{
                        devices.add(item);
                    });

                }
            }
            List<SpaceSubjectReleation> spaceSubjectReleations = spaceRelationsMap.get(ds.getId());
            if (CollectionUtils.isNotEmpty(spaceSubjectReleations)){
                List<Long> spaceIdList
                        = spaceSubjectReleations.stream().map(SpaceSubjectReleation::getSpaceId).collect(Collectors.toList());
                deviceSubjectDTO.setSpaceIds(spaceIdList);
            }
            if (StringUtils.isNotBlank(ds.getExpend())){
                ScreenSaver screenSaver = JSONObject.parseObject(ds.getExpend(), ScreenSaver.class);
                deviceSubjectDTO.setScreenSaver(screenSaver);
            }else if (ds.getType()==2){
                // 兼容线上原来老数据（默认全天）
                deviceSubjectDTO.setScreenSaver(initOlderData(ds.getImgUrls()));
            }
            getPathUrl(ds, deviceSubjectDTO);
            deviceSubjectDTOS.add(deviceSubjectDTO);
        }
        return serviceResponse;
    }

    /**
     * 查询设备主题列表
     *
     * @param deviceSubjectDTO    条件
     * @return
     */
    @Override
    public ServiceResponse<Pagination<DeviceSubjectDTO>> listByOrgIdAndTypePage(DeviceSubjectDTO deviceSubjectDTO) {
        deviceSubjectManager.updateOldScreenData(deviceSubjectDTO.getOrgId(),deviceSubjectDTO.getType());
        ServiceResponse<Pagination<DeviceSubjectDTO>> serviceResponse = ServiceResponse
                .createSuccessResponse();

        Pagination<DeviceSubjectDTO> deviceSubjectDTOPagination = new Pagination<>();
        List<DeviceSubjectDTO> deviceSubjectDTOS = new ArrayList<>();
        deviceSubjectDTOPagination.setPageNo(deviceSubjectDTO.getPageNo());
        deviceSubjectDTOPagination.setPageSize(deviceSubjectDTO.getPageSize());
        deviceSubjectDTOPagination.setTotalCount(0);
        serviceResponse.setData(deviceSubjectDTOPagination);
        DeviceSubject condition = new DeviceSubject();
        BeanUtils.copyProperties(deviceSubjectDTO,condition);
        Integer total = deviceSubjectManager.countAll(condition,deviceSubjectDTO.getSubjectIdList());
        deviceSubjectDTOPagination.setTotalCount(total);
        List<DeviceSubject> deviceSubjects = deviceSubjectManager.listByConditionAndPage(condition,deviceSubjectDTO.getPageNo(),deviceSubjectDTO.getPageSize(),deviceSubjectDTO.getSubjectIdList());
        if (CollectionUtils.isEmpty(deviceSubjects)) {
            return serviceResponse;
        }

        List<Long> subjectIds = deviceSubjects.stream().map(DeviceSubject::getId).distinct()
                .collect(Collectors.toList());
        List<DeviceSubjectRelation> relations = deviceSubjectRelationManager
                .listByOrgIdAndSubjectIds(deviceSubjectDTO.getOrgId(), subjectIds);

        List<Long> spaceIds = new ArrayList<>();
        List<SpaceSubjectReleation> spaceRelations = spaceSubjectRelationMapper.listByOrgIdAndSubjectIds(deviceSubjectDTO.getOrgId(), subjectIds);
        if (CollectionUtils.isNotEmpty(spaceRelations)){
            List<Long> collect = spaceRelations.stream().map(SpaceSubjectReleation::getSubjectId).collect(Collectors.toList());
            spaceIds.addAll(collect);
        }
        List<Long> deviceIdListBySpaceId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spaceIds)){
            List<TreeDeviceRelationDTO> treeDeviceRelationDTOS = spaceTreeDeviceRelationService.listAllByTreeIdsAndSource(deviceSubjectDTO.getOrgId(), spaceIds, DeviceSourceEnum.MD.getCode()).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(treeDeviceRelationDTOS)){
                deviceIdListBySpaceId = treeDeviceRelationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId).distinct().collect(Collectors.toList());
            }
        }

        Map<Long, DeviceInfoDTO> deviceIdToDeviceInfoMap = new HashMap<>();
        Map<Long, List<DeviceSubjectRelation>> subjectIdToRelationsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            subjectIdToRelationsMap = relations.stream().collect(Collectors.groupingBy(
                    DeviceSubjectRelation::getSubjectId));
            List<Long> deviceIds = relations.stream().map(DeviceSubjectRelation::getDeviceId)
                    .distinct().collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(deviceIdListBySpaceId)){
                deviceIds.addAll(deviceIdListBySpaceId);
                deviceIds = new ArrayList<>(new HashSet<>(deviceIds));
            }

            if (CollectionUtils.isNotEmpty(deviceIds)) {
                List<DeviceInfoDTO> deviceInfoDTOS = deviceService
                        .listByIds(deviceIds).pickDataThrowException();
                if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
                    deviceIdToDeviceInfoMap = deviceInfoDTOS.stream()
                            .collect(Collectors.toMap(DeviceInfoDTO::getDeviceId, x -> x));
                }
            }
        }
        for (DeviceSubject ds : deviceSubjects) {
            DeviceSubjectDTO deviceSubjectDTOItem = new DeviceSubjectDTO();
            BeanUtils.copyProperties(ds, deviceSubjectDTOItem);
            List<DeviceSubjectRelation> relationList = subjectIdToRelationsMap.get(ds.getId());
            List<DeviceInfoDTO> devices = new ArrayList<>();
            deviceSubjectDTOItem.setDevices(devices);
            if (CollectionUtils.isNotEmpty(relationList)) {
                for (DeviceSubjectRelation dr : relationList) {
                    Optional.ofNullable(deviceIdToDeviceInfoMap.get(dr.getDeviceId())).ifPresent(item->{
                        devices.add(item);
                    });
                }
            }
            if (StringUtils.isNotBlank(ds.getExpend())){
                ScreenSaver screenSaver = JSONObject.parseObject(ds.getExpend(), ScreenSaver.class);
                deviceSubjectDTOItem.setScreenSaver(screenSaver);
            }else if (ds.getType()==2){
                deviceSubjectDTOItem.setScreenSaver(initOlderData(ds.getImgUrls()));
            }
            getPathUrl(ds, deviceSubjectDTOItem);
            deviceSubjectDTOS.add(deviceSubjectDTOItem);
        }
        deviceSubjectDTOPagination.setData(deviceSubjectDTOS);

        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deleteByOrgIdAndIds(Long orgId, List<Long> subjectIds) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceSubjectManager.deleteByOrgIdAndIds(orgId, subjectIds));
        return serviceResponse;
    }

    /**
     * 更新屏保开关（0-关闭，1-开启）
     *
     * @param orgId
     * @param subjectId
     * @param enable
     * @return
     */
    @Transactional
    @Override
    public ServiceResponse<Boolean> updateScreenSwitch(Long orgId, Long subjectId, Integer enable) {
        DeviceSubjectDTO deviceSubjectDTO = deviceSubjectManager.getByOrgIdAndId(orgId, subjectId);
        if (deviceSubjectDTO==null){
            return new ServiceResponse<>(false);
        }
        DeviceSubject deviceSubject = new DeviceSubject();
        deviceSubject.setEnable(enable);
        deviceSubject.setOrgId(orgId);
        deviceSubject.setId(subjectId);
        Boolean update = deviceSubjectManager.update(deviceSubject);
        if (update){
            List<DeviceSubjectRelation> deviceSubjectRelations = deviceSubjectRelationManager.listByOrgIdAndSubjectIds(orgId, Collections.singletonList(subjectId));
            if (CollectionUtils.isNotEmpty(deviceSubjectRelations)){
                List<Long> deviceIdList = deviceSubjectRelations.stream().map(DeviceSubjectRelation::getDeviceId).distinct().collect(Collectors.toList());
                sendMessageToDevice(orgId,deviceIdList);
            }
        }
        return new ServiceResponse<>(true);
    }


    /**
     * 临时图片转存为永久图片
     *
     * @param imgList
     * @param logoImgUrl
     * @param deviceSubject
     */
    private void temporaryToPermanent(List<String> imgList, String logoImgUrl,
        DeviceSubject deviceSubject) {
        try {
            StringBuilder imgUrls = new StringBuilder();
            SaveFileResponse saveFileResponse;
            if (CollectionUtils.isNotEmpty(imgList)) {
                for (String imgUrl : imgList) {
                    String path = imgUrl.trim();
                    String fileName = path.substring(path.lastIndexOf("/") + 1);
                    String relativePath = fileManager.getRelativePathByUrl(imgUrl);
                    if (relativePath.equals(imgUrl)) {
                        // 兼容旧模板背景图片
                        InputStream stream = URLUtil.getStream(URLUtil.url(imgUrl));
                        saveFileResponse = fileManager.saveFile(stream,
                            FilePathType.TYPE_POSTIMAGE, fileName).pickDataThrowException();
                        if (stream != null) {
                            stream.close();
                        }
                        imgUrls.append(",").append(saveFileResponse.getRelativePath());
                    } else {
                        //如果已经存在于永久图片目录，则不用拷贝
                        if (relativePath.contains(FilePathType.TYPE_POSTIMAGE.getFileType())) {
                            imgUrls.append(",").append(relativePath);
                        } else {
                            saveFileResponse = fileManager.copyByRelativePath(relativePath,
                                    FilePathType.TYPE_POSTIMAGE, fileName).pickDataThrowException();
                            imgUrls.append(",").append(saveFileResponse.getRelativePath());
                        }
                    }
                }
                if (StringUtils.isNotBlank(imgUrls.toString())) {
                    imgUrls.deleteCharAt(0);
                }
                deviceSubject.setImgUrls(imgUrls.toString());
            }
            if (StringUtils.isNotBlank(logoImgUrl)) {
                String logoPath = logoImgUrl.trim();
                String logoFileName = logoPath.substring(logoPath.lastIndexOf("/") + 1);
                String logoRelativePath = fileManager.getRelativePathByUrl(logoImgUrl);
                if (logoRelativePath.equals(logoImgUrl)) {
                    // 兼容旧模板背景图片
                    InputStream stream = URLUtil.getStream(URLUtil.url(logoImgUrl));
                    saveFileResponse = fileManager.saveFile(stream,
                        FilePathType.TYPE_POSTIMAGE, logoFileName).pickDataThrowException();
                    if (stream != null) {
                        stream.close();
                    }
                    deviceSubject.setLogoImgUrl(saveFileResponse.getRelativePath());
                } else {
                    //如果已经存在于永久图片目录，则不用拷贝
                    if (logoRelativePath.contains(FilePathType.TYPE_POSTIMAGE.getFileType())) {
                        deviceSubject.setLogoImgUrl(logoRelativePath);
                    } else {
                        saveFileResponse = fileManager.copyByRelativePath(logoRelativePath,
                                FilePathType.TYPE_POSTIMAGE, logoFileName).pickDataThrowException();
                        deviceSubject.setLogoImgUrl(saveFileResponse.getRelativePath());
                    }
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
        }
    }

    /**
     * 临时图片转存为永久图片
     *
     * @param imgUrl
     */
    private String temporaryToPermanent(String imgUrl) {
        try {
            StringBuilder imgUrls = new StringBuilder();
            SaveFileResponse saveFileResponse;
            if (StringUtils.isNotBlank(imgUrl)) {
                    String path = imgUrl.trim();
                    String fileName = path.substring(path.lastIndexOf("/") + 1);
                    String relativePath = fileManager.getRelativePathByUrl(imgUrl);
                    if (relativePath.equals(imgUrl)) {
                        // 兼容旧模板背景图片
                        InputStream stream = URLUtil.getStream(URLUtil.url(imgUrl));
                        saveFileResponse = fileManager.saveFile(stream,
                                FilePathType.TYPE_POSTIMAGE, fileName).pickDataThrowException();
                        if (stream != null) {
                            stream.close();
                        }
                        imgUrls.append(",").append(saveFileResponse.getRelativePath());
                    } else {
                        //如果已经存在于永久图片目录，则不用拷贝
                        if (relativePath.contains(FilePathType.TYPE_POSTIMAGE.getFileType())) {
                            imgUrls.append(",").append(relativePath);
                        } else {
                            saveFileResponse = fileManager.copyByRelativePath(relativePath,
                                    FilePathType.TYPE_POSTIMAGE, fileName).pickDataThrowException();
                            imgUrls.append(",").append(saveFileResponse.getRelativePath());
                        }
                }
                if (StringUtils.isNotBlank(imgUrls.toString())) {
                    imgUrls.deleteCharAt(0);
                }
                return imgUrl;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
        }
        return imgUrl;
    }


    /**
     * 设备主题应用到设备
     *
     * @param orgId         机构Id
     * @param subjectId     主题Id
     * @param allDeviceFlag 应用所有设备标识 0-不应用 1-应用所有设备
     * @param deviceIds     设备Id列表
     */
    private void applyToDevice(Long orgId, Long subjectId, Integer allDeviceFlag,
                               List<Long> deviceIds,List<Long> spaceIds) {
//        List<DeviceInfoDTO> deviceInfoDTOS;

//        if (allDeviceFlag != null && allDeviceFlag == 1) {
//            deviceInfoDTOS = deviceService.listByOrgId(orgId).pickDataThrowException();
//            if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
//                deviceIds = deviceInfoDTOS.stream().map(DeviceInfoDTO::getDeviceId)
//                        .distinct().collect(Collectors.toList());
//            }
//        }
        deviceSubjectRelationManager.resetRelationBySubjectId(orgId, subjectId, deviceIds,spaceIds);
    }

    /**
     * 设备壁纸应用到设备
     *
     * @param orgId         机构Id
     * @param deviceSubject 主题Id列表
     * @param allDeviceFlag 应用所有设备标识 0-不应用 1-应用所有设备
     * @param deviceIds     设备Id列表
     * @return
     */
    private void applyToDeviceByWallpaper(Long orgId, DeviceSubject deviceSubject, Integer allDeviceFlag,
                                          List<Long> deviceIds,Integer type) {
        List<DeviceInfoDTO> deviceInfoDtos = new ArrayList<>();
        if (allDeviceFlag != null && allDeviceFlag == 1) {
            deviceInfoDtos = distinctDeviceInfoDTOByOrgId(orgId);
        }else{
            if (CollectionUtils.isNotEmpty( deviceIds)){
                deviceIds = new ArrayList<>(new HashSet<>(deviceIds));
                deviceInfoDtos = deviceService.listByIds( deviceIds).pickDataThrowException();
            }
        }
        if (CollectionUtils.isEmpty(deviceInfoDtos)){
            return ;
        }
        List<DeviceSubject> deviceSubjectList = getDeviceSubjectList(deviceSubject, deviceInfoDtos.size());
        List<DeviceSubject> deviceSubjects = deviceSubjectManager.batchInsert(deviceSubjectList);
        List<Long> subjectIdList = deviceSubjects.stream().map(DeviceSubject::getId).distinct().collect(Collectors.toList());
        List<Long> deviceIdList = deviceInfoDtos.stream().map(DeviceInfoDTO::getDeviceId).distinct().collect(Collectors.toList());
        deviceSubjectRelationManager.resetRelationBySubjectIdList(orgId, getMeetingRoomSubjectMap(deviceIdList,subjectIdList),deviceIds,type);

    }


    private void getPathUrl(DeviceSubject deviceSubject, DeviceSubjectDTO deviceSubjectDTO) {
        if (StringUtils.isNotBlank(deviceSubject.getImgUrls())) {
            List<String> urls = new ArrayList<>();
            String[] imgUrls = deviceSubject.getImgUrls().split(",");
            for (String imgUrl : imgUrls) {
                urls.add(fileManager.getUrlByRelativePath(imgUrl));
            }
            deviceSubjectDTO.setImgUrls(urls);
        }

        if (StringUtils.isNotBlank(deviceSubject.getLogoImgUrl())) {
            deviceSubjectDTO
                .setLogoImgUrl(fileManager.getUrlByRelativePath(deviceSubject.getLogoImgUrl()));
        }
    }

    public Map<Long,Long> getMeetingRoomSubjectMap(List<Long> deviceIdList,List<Long> subjectIdList){
        BizAssert.notEmpty(subjectIdList,DeviceErrorCode.DEVICE_SUBJECT_INSERT_FIAL,DeviceErrorCode.DEVICE_SUBJECT_INSERT_FIAL.getCode());
        BizAssert.notEmpty(deviceIdList,DeviceErrorCode.DEVICE_SUBJECT_INSERT_FIAL,DeviceErrorCode.DEVICE_SUBJECT_INSERT_FIAL.getCode());

        BizAssert.isTrue(subjectIdList.size()==deviceIdList.size(),DeviceErrorCode.DEVICE_SUBJECT_INSERT_FIAL,DeviceErrorCode.DEVICE_SUBJECT_INSERT_FIAL.getCode());

        HashMap<Long, Long> hashMap = new HashMap<>();
        for (int i=0;i<deviceIdList.size();i++){
            Long deviceId = deviceIdList.get(i);
            Long subjectId = subjectIdList.get(i);
            hashMap.put(deviceId,subjectId);
        }
        return hashMap;
    }

    public List<DeviceSubject> getDeviceSubjectList(DeviceSubject deviceSubject,Integer size){
        List<DeviceSubject> deviceSubjects = new ArrayList<>();
        for (int i=0;i<size;i++){
            DeviceSubject cloneDeviceSubject = null;
            try {
                cloneDeviceSubject = (DeviceSubject) deviceSubject.clone();
            } catch (CloneNotSupportedException e) {
                throw new RuntimeException(e);
            }
            deviceSubjects.add(cloneDeviceSubject);
        }
        return deviceSubjects;
    }

    public List<DeviceInfoDTO> distinctDeviceInfoDTOByOrgId(Long orgId){
        List<DeviceInfoDTO> deviceInfoDTOS = deviceService.listByOrgId(orgId).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
            deviceInfoDTOS = deviceInfoDTOS.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    DeviceInfoDTO::getDeviceId,
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            return deviceInfoDTOS;
        }
        return Collections.emptyList();
    }


    /**
     * 统一MQ消息发送
     * @param orgId
     * @param deviceIdList
     */
    public void sendMessageToDevice(Long orgId,List<Long> deviceIdList){
        if (CollectionUtils.isEmpty(deviceIdList)){
            return ;
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(() -> {
                            List<DeviceInfoDTO> deviceInfoList = deviceService
                                    .listByOrgIdAndIds(orgId, deviceIdList).pickDataThrowException();
                            if (CollectionUtils.isNotEmpty(deviceInfoList)) {
                                for (DeviceInfoDTO deviceInfo : deviceInfoList) {
                                    if (deviceInfo != null) {
                                        log.info("发送设备主题变更消息，deviceid:{},devicesn:{}", deviceInfo.getDeviceId(), deviceInfo.getDeviceSn());
                                        mqttMessageComponent.sendMqttMessage(deviceInfo.getDeviceSn(),
                                                TransferEventType.DEVICE_SUBJECT_SYNC.getEventType(), null,
                                                "DEVICE_SUBJECT_SYNC");
                                    }
                                }
                            }
                        }
                );
            }
        });
    }


    public ScreenSaver initOlderData(String url){
        ScreenSaver screenSaver = new ScreenSaver();
        screenSaver.setWeekdays(Collections.emptyList());
        screenSaver.setScreenSaverItemList(Collections.emptyList());
        screenSaver.setOnlyOne(1);
        screenSaver.setTouchSwtich(0);
        screenSaver.setEnableVoice(0);
        if (StringUtils.isBlank(url)){
            return screenSaver;
        }
        List<String> urlList = Arrays.asList(url.split(","));
        screenSaver.setWeekdays(Arrays.asList(1,2,3,4,5,6,7));
        List<ScreenSaverItem> screenSaverItemList = new ArrayList<>();
        for (String urlItem : urlList) {
            ScreenSaverItem screenSaverItem = new ScreenSaverItem();
            screenSaverItem.setIsVideo(0);
            screenSaverItem.setUrl(fileManager.getUrlByRelativePath(urlItem));
            screenSaverItem.setEnableVoice(0);
            screenSaverItem.setFirstScreenImg(fileManager.getUrlByRelativePath(urlItem));
            screenSaverItem.setFileSize("");
            screenSaverItem.setFileName("");
            screenSaverItem.setIsAllDay(1);
            screenSaverItem.setTimeList(Collections.singletonList("00:00-24:00"));
            screenSaverItemList.add(screenSaverItem);
        }
        screenSaver.setScreenSaverItemList(screenSaverItemList);
        return screenSaver;
    }
}
