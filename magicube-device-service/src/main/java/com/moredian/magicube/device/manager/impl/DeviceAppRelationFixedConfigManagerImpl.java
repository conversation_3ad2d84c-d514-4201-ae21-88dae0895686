package com.moredian.magicube.device.manager.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import javax.annotation.Resource;
import com.moredian.magicube.device.dao.mapper.DeviceAppRelationFixedConfigMapper;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;
import com.moredian.magicube.device.manager.DeviceAppRelationFixedConfigManager;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.idgenerator.service.IdgeneratorService;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * com.moredian.magicube.devicemanagerImpl
 * <AUTHOR>
 */
 
@Service
public class DeviceAppRelationFixedConfigManagerImpl implements DeviceAppRelationFixedConfigManager {

	@SI
	private IdgeneratorService idgeneratorService;
	
	@Resource
	private DeviceAppRelationFixedConfigMapper deviceAppRelationFixedConfigMapper;


	@Override
	public Pagination<DeviceAppRelationFixedConfig> listPage(QueryDeviceAppRelationConfigDTO dto) {

		Pagination<DeviceAppRelationFixedConfig> pa = new Pagination<>();
		pa.setPageNo(dto.getPageNo());
		pa.setPageSize(dto.getPageSize());

		Page<DeviceAppRelationFixedConfig> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(),
						dto.getPageSize() == null ? 20 : dto.getPageSize())
				.setOrderBy("gmt_create DESC")
				.doSelectPage(() -> deviceAppRelationFixedConfigMapper.selectByConditions(dto));
		pa.setTotalCount((int) page.getTotal());
		pa.setData(page.getResult());
		return pa;
	}

	@Override
    public List<DeviceAppRelationFixedConfig> listAll(){
    	return deviceAppRelationFixedConfigMapper.listAll();
    }
    
     @Override
    public DeviceAppRelationFixedConfig getById(Long id){
    	return deviceAppRelationFixedConfigMapper.getById(id);
    }


	@Override
	public DeviceAppRelationFixedConfig getByBizTypeAndId(Integer bizType,String bizId) {
		return deviceAppRelationFixedConfigMapper.getByBizTypeAndId(bizType,bizId);
	}

	@Override
	public Long insert(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig) {
		ServiceResponse<Long> serviceResponse = idgeneratorService.getNextIdByTypeName("com.moredian.magicube.device.entity.DeviceAppRelationFixedConfig");
		Long id = null;
        if (serviceResponse.isSuccess()) {
        	id =  serviceResponse.getData();
			deviceAppRelationFixedConfig.setId(serviceResponse.getData());
			deviceAppRelationFixedConfigMapper.insert(deviceAppRelationFixedConfig);
		}
		return id;
	}
	
	@Override
	public int update(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig) {
		return deviceAppRelationFixedConfigMapper.update(deviceAppRelationFixedConfig);
	}
	
	@Override
	public int updateSelective(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig) {
		return deviceAppRelationFixedConfigMapper.updateSelective(deviceAppRelationFixedConfig);
	}
	
	@Override
	public int delete(Long id) {
		return deviceAppRelationFixedConfigMapper.delete(id);
	}

	@Override
	public int deleteByBizTypeAndId(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig) {
		return deviceAppRelationFixedConfigMapper.deleteByBizTypeAndId(deviceAppRelationFixedConfig);
	}
}
