package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.OrgDeviceMigration;
import com.moredian.magicube.device.dto.migration.*;

/**
 * @Description: 一键上钉
 * @author: wbf
 * @date: 2024/8/12 下午3:32
 */
public interface MigrationDeviceManager {
    /**
     * 查询待迁移设备列表
     *
     * @param orgId
     * @return
     */
    MigrationDeviceRes getList(Long orgId);

    /**
     * 查询绑定组织机构的url
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    MigrationDeviceBindOrgUrlRes getBindOrgUrl(Long orgId, Long deviceId);

    /**
     * 终端同步组织列表页地址
     *
     * @param deviceSn
     * @param bindOrgUrl
     */
    void bindOrgUrlSync(String deviceSn, String bindOrgUrl);

    /**
     * 终端判断用户选择组织id是否与当前组织id一致
     *
     * @param deviceSn
     * @param corpId
     * @return
     */
    MigrationDeviceOrgRes getOrgIsEqual(String deviceSn, String corpId);

    /**
     * 获取待迁移设备信息
     *
     * @param deviceId
     * @return
     */
    OrgDeviceMigration getMigrationDeviceInfo(Long deviceId);

    /**
     * 请求获取组织列表页url
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    boolean reqBindOrgUrl(Long orgId, Long deviceId);

    /**
     * 客户端轮询设备迁移状态
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    MigrationDeviceStatusRes getMigrationDeviceStatus(Long orgId, Long deviceId);

    /**
     * 获取迁移设备数量
     *
     * @param orgId
     * @return
     */
    MigrationDeviceOrgCountRes getMigrationDeviceCount(Long orgId);

    /**
     * 终端同步迁移结果
     *
     * @param orgId
     * @param deviceSn
     * @param isSuccess
     */
    void migrationDeviceStatusSync(Long orgId, String deviceSn, Boolean isSuccess);

    /**
     * 设备激活消息
     *
     * @param orgId
     * @param deviceId
     * @param deviceSn
     */
    void subDeviceActiveMsg(long orgId, long deviceId, String deviceSn);

    /**
     * 设备解绑消息
     *
     * @param orgId
     * @param deviceId
     */
    void subDeviceUnbindMsg(long orgId, long deviceId);

    /**
     * 微信组织开通设备迁移钉钉功能
     *
     * @param orgId
     */
    void mdOrgTransferDingOrgMsg(Long orgId);
}
