package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceTypePropertyRelation;
import com.moredian.magicube.device.dto.type.InsertDeviceTypePropertyRelationDTO;
import com.moredian.magicube.device.manager.DeviceTypePropertyRelationManager;
import com.moredian.magicube.device.service.DeviceTypePropertyRelationService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class DeviceTypePropertyRelationServiceImpl implements DeviceTypePropertyRelationService {

    @Autowired
    private DeviceTypePropertyRelationManager manager;

    @Override
    public ServiceResponse<List<Long>> listPropertyIdByDeviceType(Integer deviceType) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(manager.listPropertyIdByDeviceType(deviceType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Integer>> listDeviceTypeByPropertyId(Long deviceTypePropertyId) {
        ServiceResponse<List<Integer>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(manager.listDeviceTypeByPropertyId(deviceTypePropertyId));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Long> insert(InsertDeviceTypePropertyRelationDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceTypePropertyRelation relation = new DeviceTypePropertyRelation();
        BeanUtils.copyProperties(dto, relation);
        serviceResponse.setData(manager.insert(relation));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> resetRelationByDeviceType(Integer deviceType,
        List<Long> propertyIds) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse
            .setData(manager.resetRelationByDeviceType(deviceType, propertyIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> resetRelationByPropertyId(Long propertyId,
        List<Integer> deviceTypes) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse
            .setData(manager.resetRelationByPropertyId(propertyId, deviceTypes));
        return serviceResponse;
    }
}