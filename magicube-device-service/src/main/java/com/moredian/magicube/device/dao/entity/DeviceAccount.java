package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备-账户表 设备与使用设备的账户关联(HiveDeviceAccount)实体类
 *
 * <AUTHOR>
 * @since 2020-04-10 11:14:02
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_composite")
public class DeviceAccount extends TimedEntity {

    private static final long serialVersionUID = -5552570931481596943L;

    /**
     * Id
     */
    private Long deviceAccountId;

    /**
     * 账户Id
     */
    private Long accountId;

    /**
     * 设备Sn
     */
    private String sn;

    /**
     * 1-有效,0-无效
     */
    private Object status;
}