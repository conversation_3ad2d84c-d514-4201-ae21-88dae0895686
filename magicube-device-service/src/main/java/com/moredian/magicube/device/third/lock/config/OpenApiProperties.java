package com.moredian.magicube.device.third.lock.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-02-21 16:21
 */
@Configuration
@ConfigurationProperties(prefix = "tt.lock.config")
@Getter
@Setter
public class OpenApiProperties {

    private String apiUrl = "https://cnapi.ttlock.com";

    private String clientId = "2fa182565b9549e5b290908451a324c9";

    private String clientSecret = "b54ff52e3b41068182310f49f3a66098";

    private String username = "eegjh_c042f4db68f23406c6cecf84a7ebb0fe";

    private String password = "a4a176912c6dcb4f85ade96fe4491e31";
}
