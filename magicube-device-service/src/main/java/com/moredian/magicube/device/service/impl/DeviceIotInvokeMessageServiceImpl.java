package com.moredian.magicube.device.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceIotInvokeMessage;
import com.moredian.magicube.device.dto.lock.InvokeMessageDTO;
import com.moredian.magicube.device.dto.lock.MessageInfoDTO;
import com.moredian.magicube.device.dto.lock.UpdateMessageDTO;
import com.moredian.magicube.device.manager.DeviceIotInvokeMessageManager;
import com.moredian.magicube.device.service.lock.DeviceIotInvokeMessageService;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;

import static com.moredian.magicube.device.enums.iot.MessageStatusEnum.validateStatus;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-03 16:37
 */
@SI
@Slf4j
public class DeviceIotInvokeMessageServiceImpl implements DeviceIotInvokeMessageService {

    @Resource
    private DeviceIotInvokeMessageManager deviceIotInvokeMessageManager;


    @Override
    public ServiceResponse<Long> invoke(InvokeMessageDTO invokeMessageDTO) {
        InvokeMessageDTO.validateData(invokeMessageDTO);

        ServiceResponse<Long> res = ServiceResponse.createSuccessResponse();
        Long messageId = deviceIotInvokeMessageManager.invoke(invokeMessageDTO);
        res.setData(messageId);
        return res;
    }

    @Override
    public ServiceResponse<Boolean> updateStatus(UpdateMessageDTO dto) {
        Long messageId = dto.getMessageId();
        Integer messageStatus = dto.getMessageStatus();

        BizAssert.isTrue(ObjectUtil.isNotEmpty(messageId), "messageId must be not null");
        BizAssert.isTrue(ObjectUtil.isNotEmpty(messageStatus), "messageStatus must be not null");
        validateStatus(messageStatus);

        DeviceIotInvokeMessage message = new DeviceIotInvokeMessage();
        message.setMessageId(messageId);
        message.setMessageStatus(messageStatus);
        message.setResponse(dto.getMessageResponse());
        deviceIotInvokeMessageManager.updateById(message);
        ServiceResponse<Boolean> res = ServiceResponse.createSuccessResponse();
        res.setData(Boolean.TRUE);
        return res;
    }

    @Override
    public ServiceResponse<MessageInfoDTO> getByMessageId(Long orgId, Long messageId) {
        BizAssert.isTrue(ObjectUtil.isNotEmpty(messageId), "messageId must be not null");
        BizAssert.isTrue(orgId != null, "orgId must be not null");

        QueryWrapper<DeviceIotInvokeMessage> wrapper = new QueryWrapper<>();
        wrapper.eq("message_id", messageId);
        wrapper.eq("org_id", orgId);

        DeviceIotInvokeMessage message = deviceIotInvokeMessageManager.getOne(wrapper);
        ServiceResponse<MessageInfoDTO> res = ServiceResponse.createSuccessResponse();
        MessageInfoDTO infoDTO = buildMessageInfoDTO(message);
        res.setData(infoDTO);
        return res;
    }

    /**
     * 构建消息详情信息
     */
    private MessageInfoDTO buildMessageInfoDTO(DeviceIotInvokeMessage message) {
        return MessageInfoDTO.builder()
                .messageId(message.getMessageId())
                .orgId(message.getOrgId())
                .deviceId(message.getDeviceId())
                .deviceSn(message.getDeviceSn())
                .messageType(message.getMessageType())
                .messageStatus(message.getMessageStatus())
                .gmtCreate(message.getGmtCreate())
                .build();
    }
}
