<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceRuleRelationMapper">

    <resultMap id="baseMap" type="com.moredian.magicube.device.dao.entity.DeviceRuleRelation">
        <result property="deviceRuleRelationId" column="device_rule_relation_id"/>
        <result property="orgId" column="org_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
    </resultMap>

    <sql id="sql_table">
        hive_device_rule_relation
    </sql>

    <sql id="sql_columns">
        device_rule_relation_id,
        org_id,
        rule_id,
        device_id,
        gmt_create,
        gmt_modify
    </sql>
    <delete id="deleteDeviceRule">
        delete from <include refid="sql_table"/>
        where org_id = #{orgId} and rule_id = #{ruleId}
    </delete>
    <delete id="deleteByOrgIdAndDeviceId">
        delete from <include refid="sql_table"/>
        where org_id = #{orgId}
        and rule_id = #{ruleId}
        and device_id in
        <foreach collection="deviceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="getRuleIdByOrgIdAndDeviceId"
      resultType="java.lang.Long">
        select  rule_id from  <include refid="sql_table"/>
        where org_id = #{orgId} and device_id = #{deviceId}
    </select>

    <insert id="batchInsert">
        insert into
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        ) values
        <foreach collection="list" item="item" separator="," >
            (#{item.deviceRuleRelationId},
            #{item.orgId},
            #{item.ruleId},
            #{item.deviceId},
            now(3),
            now(3))
        </foreach>
    </insert>

</mapper>