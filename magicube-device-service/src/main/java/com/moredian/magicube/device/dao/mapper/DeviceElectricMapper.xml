<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceElectricMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceElectric">
        <id column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="electric" property="electric"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="sql_columns">
        id, device_id, device_sn, electric,gmt_create,gmt_modify
    </sql>

    <sql id="sql_values">
        #{id},
        #{deviceId},
        #{deviceSn},
        #{electric},
        now(3),
        now(3)
    </sql>

    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from hive_device_electric where device_id = #{deviceId}
    </select>

    <select id="selectOneByDeviceSn" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from hive_device_electric where device_sn = #{deviceSn}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from hive_device_electric where
        <if test="deviceIdList != null and deviceIdList.size() > 0">
            device_id in
            <foreach collection="deviceIdList" index="index" item="deviceId" open="(" separator="," close=")">
                #{deviceId}
            </foreach>
        </if>
    </select>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceElectric">
        INSERT INTO
        hive_device_electric
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
        ON DUPLICATE KEY UPDATE electric= #{electric},gmt_modify = now(6)
    </insert>

    <update id="updateById" parameterType="com.moredian.magicube.device.dao.entity.DeviceElectric">
        update hive_device_electric set device_id = #{deviceId}, electric = #{electric},gmt_modify = now(3) where id = #{id}
    </update>
</mapper>
