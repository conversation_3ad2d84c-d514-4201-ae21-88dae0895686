package com.moredian.magicube.device.config;

import com.moredian.magicube.device.enums.ModeTriggerEnum;
import com.moredian.magicube.device.service.strategy.ModeTriggerTemplateParseStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version $Id: ModeTriggerTemplateStrategyReader.java, v 1.0 Exp $
 */
@Slf4j
@Component
public class ModeTriggerTemplateStrategyReader implements InitializingBean {

    private final Map<ModeTriggerEnum, ModeTriggerTemplateParseStrategy> map = new ConcurrentHashMap<>();

    @Resource
    private List<ModeTriggerTemplateParseStrategy> templateParseStrategyList;

    @Override
    public void afterPropertiesSet() {
        templateParseStrategyList.forEach(v-> map.put(v.getTemplate(), v));
    }

    public ModeTriggerTemplateParseStrategy getStrategyByTemplateEnum(ModeTriggerEnum templateEnum) {
        return map.get(templateEnum);
    }
}
