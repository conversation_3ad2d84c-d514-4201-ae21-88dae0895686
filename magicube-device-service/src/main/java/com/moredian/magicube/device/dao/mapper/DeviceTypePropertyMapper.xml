<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceTypePropertyMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceTypeProperty">
    <id column="id" property="id"/>
    <result column="property_key" property="propertyKey"/>
    <result column="property_value" property="propertyValue"/>
    <result column="description" property="description"/>
    <result column="status" property="status"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <sql id="sql_table">
        hive_device_type_property
    </sql>

  <sql id="sql_columns">
        id,
        property_key,
        property_value,
        description,
        status,
        gmt_create,
        gmt_modify
	</sql>

  <sql id="sql_values">
        #{id},
        #{propertyKey},
        #{propertyValue},
        #{description},
        #{status},
        now(3),
        now(3)
    </sql>

  <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceTypeProperty">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>

  <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceTypeProperty">
    update
    <include refid="sql_table"/>
    <set>
      <if test="propertyKey != null">
        property_key = #{propertyKey},
      </if>
      <if test="propertyValue != null">
        property_value = #{propertyValue},
      </if>
      <if test="description != null">
        description = #{description},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      gmt_modify = now(3)
    </set>
    where id = #{id}
  </update>

  <select id="getById" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where id = #{id}
  </select>

  <select id="getByKey" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where property_key = #{propertyKey}
    and status = 1
  </select>

  <select id="list" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
  </select>

  <select id="listByIds" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where
    <if test="ids != null and ids.size() > 0">
      id in
      <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>
</mapper>
