<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.RuleMapper">

    <resultMap id="baseMap" type="com.moredian.magicube.device.dao.entity.Rule">
        <result property="ruleId" column="rule_id"/>
        <result property="orgId" column="org_id"/>
        <result property="ruleName" column="rule_name"/>
        <result property="ruleType" column="rule_type"/>
        <result property="modeType" column="mode_type"/>
        <result property="deviceType" column="device_type"/>
        <result property="description" column="description"/>
        <result property="triggerType" column="trigger_type"/>
        <result property="triggerValue" column="trigger_value"/>
        <result property="templateId" column="template_id"/>
        <result property="ruleJson" column="rule_json"/>
        <result property="delFlag" column="del_flag"/>
        <result property="sceneId" column="scene_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="spaceType" column="space_type"/>
        <result property="humanPriority" column="human_priority"/>
    </resultMap>

    <sql id="sql_table">
        hive_rule
    </sql>

    <sql id="sql_columns">
        rule_id,
        org_id,
        rule_name,
        rule_type,
        mode_type,
        device_type,
        description,
        trigger_type,
        trigger_value,
        template_id,
        del_flag,
        rule_json,
        gmt_create,
        gmt_modify,
        space_type,
        human_priority
    </sql>
    <update id="updateRule">
        update <include refid="sql_table"/>
        <set>
            <if test="rule.triggerValue != null">
                trigger_value = #{rule.triggerValue},
            </if>
            <if test="rule.ruleName != null">
                rule_name = #{rule.ruleName},
            </if>
            <if test="rule.deviceType != null">
                device_type = #{rule.deviceType},
            </if>
            <if test="rule.ruleJson != null">
                rule_json = #{rule.ruleJson},
            </if>
            <if test="rule.sceneId != null">
                scene_id = #{rule.sceneId},
            </if>
            <if test="rule.delFlag != null">
                del_flag = #{rule.delFlag},
            </if>
            <if test="rule.humanPriority != null">
                human_priority = #{rule.humanPriority},
            </if>
        </set>
        where org_id = #{orgId} and rule_id = #{rule.ruleId}
    </update>


    <select id="getRuleList" resultMap="baseMap">
        select
        rule_id,
        org_id,
        rule_name,
        rule_type,
        mode_type,
        device_type,
        description,
        trigger_type,
        trigger_value,
        template_id,
        del_flag,
        scene_id,
        space_type,
        human_priority
        from <include refid="sql_table"/>
        <where>
            org_id = #{orgId}
            <if test="modeType != null">
                and mode_type = #{modeType}
            </if>
            <if test="spaceType != null">
                and space_type = #{spaceType}
            </if>
            <if test="ruleIdList != null and ruleIdList.size() > 0">
                and rule_id in
                <foreach collection="ruleIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag = 0
        </where>
        order by gmt_create desc
    </select>

    <select id="getRuleTemplateIdByOrgId" resultType="java.lang.Long">
        select  template_id from <include refid="sql_table"/>
        where org_id = #{orgId}
    </select>
    <select id="getRule" resultMap="baseMap">
        select rule_id,
        org_id,
        rule_name,
        rule_type,
        mode_type,
        device_type,
        description,
        trigger_type,
        trigger_value,
        template_id,
        del_flag,
        scene_id,
        space_type,
        human_priority
        from <include refid="sql_table"/>
        where org_id = #{orgId} and rule_id = #{ruleId}
    </select>

    <select id="getByRuleId" resultMap="baseMap">
        SELECT <include refid="sql_columns"/>
        FROM <include refid="sql_table"/>
        WHERE rule_id = #{ruleId}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        <foreach collection="ruleList" item="item" separator=",">
            (
            #{item.ruleId},
            #{item.orgId},
            #{item.ruleName},
            #{item.ruleType},
            #{item.modeType},
            #{item.deviceType},
            #{item.description},
            #{item.triggerType},
            #{item.triggerValue},
            #{item.templateId},
            #{item.delFlag},
            #{item.ruleJson},
            now(3),
            now(3),
            #{item.spaceType},
            #{item.humanPriority}
            )
        </foreach>
    </insert>
</mapper>