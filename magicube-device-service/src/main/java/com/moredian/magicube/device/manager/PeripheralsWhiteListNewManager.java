package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddBatchDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewErrorDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewInfoDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewQueryDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewUpdateDTO;
import com.xier.sesame.common.rpc.dto.PaginationDto;
import java.util.List;

/**
 * @Auther: _AF
 * @Date: 2/23/22 17:02
 * @Description:
 */
public interface PeripheralsWhiteListNewManager {

    /**
     * 根据条件查询
     *
     * @param dto
     * @return
     */
    PaginationDto<PeripheralsWhiteListNewInfoDTO> pageByCondition(
        PeripheralsWhiteListNewQueryDTO dto);

    /**
     * 根据id集合删除
     *
     * @param ids
     * @return
     */
    Boolean removeByIds(List<Long> ids);


    /**
     * 修改
     *
     * @param dto
     * @return
     */
    Boolean updateById(PeripheralsWhiteListNewUpdateDTO dto);

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    Boolean addPeripheralsWhiteListNew(PeripheralsWhiteListNewAddDTO dto);

    /**
     * 批量导入
     *
     * @param dto
     * @return
     */
    List<PeripheralsWhiteListNewErrorDTO> addByBatch(List<PeripheralsWhiteListNewAddBatchDTO> dto);

    /**
     * 根据sn和type查询
     *
     * @param peripheralsSn
     * @param peripheralsType
     * @return
     */
    PeripheralsWhiteListNewInfoDTO getBySnAndType(String peripheralsSn, Integer peripheralsType);

    /**
     * 根据sn集合查询
     *
     * @param peripheralsSnList
     * @return
     */
    List<PeripheralsWhiteListNewInfoDTO> getBySnList(List<String> peripheralsSnList);

    /**
     * 旧表数据同步到新表
     */
    void syncOldDataToNewData();

}
