package com.moredian.magicube.device.manager;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleRelationManager.java, v 1.0 Exp $
 */
public interface DeviceRuleRelationManager {
    void ruleRelateDevice(Long orgId, Long ruleId, List<Long> deviceIdList);

    void deleteByOrgIdAndDeviceId(Long orgId, Long ruleId, List<Long> deviceIdList);

    List<Long> getRuleIdByOrgIdAndDeviceId(Long orgId, Long deviceId);
}
