package com.moredian.magicube.device.subscriber;

import cn.hutool.core.util.StrUtil;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.UUID;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.ControllerErrorCode;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.common.enums.TransferEventType;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.enums.CardSyncMethod;
import com.moredian.magicube.device.enums.DeviceAdminSyncMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/10 09:40
 */
@Slf4j
@Component
public class SendMQTTBase {

    /**
     * 卡同步方式 old = 老同步方式 new = 新同步方式 all = 老 + 新 同时触发 （考虑到有的设备没接如这个全量消息时）
     *
     * @see CardSyncMethod
     */
    @Value("${card.sync.method:all}")
    private String methodCard;

    /**
     * 可进入设备后台人员同步 old = 老同步方式 new = 新同步方式 all = 老 + 新 同时触发 （考虑到有的设备没接如这个全量消息时）
     *
     * @see DeviceAdminSyncMethod
     */
    @Value("${admin.sync.method:all}")
    private String methodAdmin;


    @SI
    private HubControlServiceV1 hubControlServiceV1;


    public void doSwitchCardSyncSendMQTT(Device device, String msgName,
        TransferEventType transferEventTypeNew) {
        CardSyncMethod enumByCode = CardSyncMethod.getEnumByCode(methodCard);
        BizAssert.isTrue(enumByCode != null,
            String.format("非法的card.sync.method配置值{%s}", methodCard));
        switch (enumByCode) {
            case CARD_OLD_SYNC:
                // 伪全量同步，增量转全量，只是多拉下数据，不删除未拉取的
                this.sendMQTTMsg(msgName, device,
                    TransferEventType.MEMBERCARD_SYNC.getValue(),
                    TransferEventType.MEMBERCARD_SYNC.getDesc());
                break;
            case CARD_NEW_SYNC:
                // 真删除，拉到多少，最后存多少，没拉到的都删掉
                this.sendMQTTMsg(msgName, device,
                    transferEventTypeNew.getValue(), transferEventTypeNew.getDesc());
                break;
            case CARD_ALL_SYNC:
                this.sendMQTTMsg(msgName, device,
                    TransferEventType.MEMBERCARD_SYNC.getValue(),
                    TransferEventType.MEMBERCARD_SYNC.getDesc());
                this.sendMQTTMsg(msgName, device,
                    transferEventTypeNew.getValue(), transferEventTypeNew.getDesc());
                break;
            default:
                log.info("无任务处理的card.sync.method配置={}", methodCard);
                break;
        }
    }


    /**
     * do switch admin sync send mqtt （切换管理员同步发送 mqtt）
     *
     * @param device               装置
     * @param msgName              msg 名称
     * @param transferEventTypeNew 转移事件类型 New
     * @param appointMethod        指定方法 强制指定
     */
    public void doSwitchAdminSyncSendMQTT(Device device, String msgName,
        com.moredian.magicube.device.subscriber.TransferEventType transferEventTypeNew,String appointMethod) {
        // 直接只读配置的，因为终端老消息可能没对接了
        DeviceAdminSyncMethod enumByCode = DeviceAdminSyncMethod.getEnumByCode(methodAdmin);
//        if (!StrUtil.isEmpty(appointMethod)){
//            enumByCode = DeviceAdminSyncMethod.getEnumByCode(appointMethod);
//        }
        BizAssert.isTrue(enumByCode != null,
            String.format("非法的admin.sync.method配置值{%s}", methodAdmin));
        switch (enumByCode) {
            case DEVICE_ADMIN_OLD_SYNC:
                // 老的同步
                this.sendMQTTMsg(msgName, device,
                    TransferEventType.SYNCHRONOUS_DEVICE_ADMIN.getValue(),TransferEventType.SYNCHRONOUS_DEVICE_ADMIN.getDesc());
                break;
            case DEVICE_ADMIN_NEW_SYNC:
                // 新的同步
                this.sendMQTTMsg(msgName, device,
                    transferEventTypeNew.getEventType(),transferEventTypeNew.getEventName());
                break;
            case DEVICE_ADMIN_ALL_SYNC:
                // 新 + 老 同步
                this.sendMQTTMsg(msgName, device,
                    TransferEventType.SYNCHRONOUS_DEVICE_ADMIN.getValue(),TransferEventType.SYNCHRONOUS_DEVICE_ADMIN.getDesc());
                this.sendMQTTMsg(msgName, device,
                    transferEventTypeNew.getEventType(),transferEventTypeNew.getEventName());
                break;
            default:
                log.info("无任务处理的admin.sync.method配置={}", methodAdmin);
                break;
        }
    }


    public boolean sendMQTTMsg(String msgName, Device device,
        int value, String desc) {
        // 发送命令
        String deviceSn = device.getDeviceSn();
        TransferMessageInfo<String> transferMessageInfo = new TransferMessageInfo<>();
        transferMessageInfo.setEventType(
            value);
        transferMessageInfo.setSeverity(3);
        transferMessageInfo.setSeqId(UUID.random19()
            + desc);
        transferMessageInfo.setMessage(
            desc);
        String jsonStr = JsonUtils.toJson(transferMessageInfo);
        String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
        TransferRequest transferRequest = new TransferRequest();
        transferRequest.setSerialNumber(deviceSn);
        transferRequest.setBody(base64Message);
        transferRequest.setCommand(0);
        ServiceResponse<TransferResponse> response = hubControlServiceV1.transfer(
            transferRequest);
        if (!response.isSuccess() && ControllerErrorCode.DEVICE_OFFLINE.getCode()
            .equals(response.getErrorContext().getCode())) {
            log.info(
                "Notify device {}，msg={},设备已离线，本次不发送通知，[deviceSn={},deviceId={}]",
                msgName,
                desc,
                device.getDeviceSn(), device.getDeviceId());
            return Boolean.TRUE;
        }
        TransferResponse result = response.pickDataThrowException();
        log.info("Notify device {}，msg={},[deviceSn={},deviceId={}],result:{}",
            msgName,
            desc,
            device.getDeviceSn(), device.getDeviceId(), JsonUtils.toJson(result));
        return Boolean.FALSE;
    }
}
