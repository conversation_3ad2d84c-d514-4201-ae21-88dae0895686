package com.moredian.magicube.device.pipeline.pipe.ding;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.DeviceUnbindMsg;
import com.moredian.magicube.device.config.IndustryProtectedConfigs;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.constant.StatusCode;
import com.moredian.magicube.device.dao.entity.Distributor;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.DistributorManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.utils.DeviceSn;
import com.xier.ding.api.corp.dto.CampusDto;
import com.xier.ding.api.corp.dto.CorpDto;
import com.xier.ding.api.corp.dto.SuiteAppCorpDto;
import com.xier.ding.api.corp.service.CorpHelperService;
import com.xier.ding.api.corp.service.SuiteCorpUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 钉钉设备校验管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DingDeviceCheckPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    @Autowired
    private DistributorManager distributorManager;

    @Autowired
    private IndustryProtectedConfigs industryProtectedConfigs;

    @Autowired
    private DeviceService deviceService;

    @SI
    private CorpHelperService corpHelperService;

    @SI
    private SuiteCorpUserService suiteCorpUserService;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        //1.判断是否缺少参数
        if (StringUtils.isEmpty(dto.getThirdDeviceId()) || StringUtils.isEmpty(dto.getCorpId())) {
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.THIRD_PARTY_ACTIVE_FAILED, HiveConst.THIRD_PARTY_TIME_OUT);
            ErrorContext errorContext = new ErrorContext("FD0020171202", "未取得第三方确认信息");
            BizAssert.notNull(null, errorContext, errorContext.getMessage());
        }

        //2.判断钉机构信息
        com.xier.sesame.common.rpc.ServiceResponse<CorpDto> corpDto = corpHelperService
            .getCorpByDingCorpId(dto.getCorpId());
        if (!corpDto.isSuccess() || corpDto.getData() == null) {
            log.error("设备激活中获取第三方机构信息失败{}", corpDto);
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.THIRD_PARTY_ACTIVE_FAILED, HiveConst.THIRD_PARTY_TIME_OUT);
            ErrorContext errorContext = new ErrorContext("FD0020171201", "设备未经过认证:未获取到机构信息！");
            BizAssert.notNull(null, errorContext, errorContext.getMessage());
        }
        Long orgId = corpDto.getData().getOrgId();
        context.setOrgId(orgId);
        String orgName = corpDto.getData().getCorpName();

        //3.对于班牌设备判断是否存在校区
        if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.CAMPUS_DEVICE_TYPE_LIST, dto.getDeviceType())) {
            SuiteAppCorpDto suiteAppCorpDto = new SuiteAppCorpDto();
            suiteAppCorpDto.setOrgId(orgId);
            com.xier.sesame.common.rpc.ServiceResponse<List<CampusDto>> serviceResponse = suiteCorpUserService
                .getCampusList(suiteAppCorpDto);
            if (!serviceResponse.isSuccess() || CollectionUtils
                .isEmpty(serviceResponse.getData())) {
                log.error("班牌设备:{},在机构:{}不存在校区", dto.getDeviceSn(), dto.getCorpId());
                redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                        + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                    StatusCode.THIRD_PARTY_ACTIVE_FAILED, HiveConst.THIRD_PARTY_TIME_OUT);
                ErrorContext errorContext = new ErrorContext("FD0020171207", "班牌设备所属机构不存在校区：无法激活");
                BizAssert.notNull(null, errorContext, errorContext.getMessage());
            }
        }

        //4.经销商区域锁定校验
        distributorCheck(orgName, dto.getDeviceSn(), dto.getThirdDeviceId());

        InventoryDevice inventoryDevice = context.getInventoryDevice();
        //5.教育保护校验
        if (!eduProtectCheck(corpDto.getData(), inventoryDevice)) {
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.ACTIVE_FAIL, HiveConst.THIRD_PARTY_TIME_OUT);
            ErrorContext errorContext = new ErrorContext("ME01010140122", "设备和机构行业不匹配，禁止激活");
            BizAssert.notNull(null, errorContext, errorContext.getMessage());
        }

        //6.校验是否需要重新激活
        if (inventoryDevice.getThirdDeviceId() == null || !dto.getThirdDeviceId()
            .equals(inventoryDevice.getThirdDeviceId())) {
            //如果是已激活设备，先清除设备，再重新激活
            DeviceInfoDTO deviceInfoDTO = deviceService.getByDeviceSn(dto.getDeviceSn())
                .pickDataThrowException();
            if (deviceInfoDTO != null) {
                ServiceResponse<Boolean> serviceResponse = deviceService
                    .deleteById(orgId, deviceInfoDTO.getDeviceId());
                if (serviceResponse == null || !serviceResponse.isSuccess() || !serviceResponse
                    .getData()) {
                    redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                            + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                        StatusCode.THIRD_PARTY_ACTIVE_FAILED,HiveConst.THIRD_PARTY_TIME_OUT);
                    ErrorContext errorContext = new ErrorContext("FD0020171204", "设备已存在");
                    BizAssert.notNull(null, errorContext, errorContext.getMessage());
                }
                //旧设备删除，发送RMq消息
                DeviceUnbindMsg deviceUnbindMsg = new DeviceUnbindMsg();
                deviceUnbindMsg.setName(deviceInfoDTO.getDeviceName());
                deviceUnbindMsg.setTimeStamp(System.currentTimeMillis());
                deviceUnbindMsg.setTpId(inventoryDevice.getThirdDeviceId());
                deviceUnbindMsg.setDeviceType(deviceInfoDTO.getDeviceType());
                deviceUnbindMsg.setPosition(deviceInfoDTO.getPosition());
                if (deviceInfoDTO.getDeviceId() != null) {
                    deviceUnbindMsg.setDeviceId(deviceInfoDTO.getDeviceId());
                }
                deviceUnbindMsg.setDeviceSn(deviceInfoDTO.getDeviceSn());
                if (deviceInfoDTO.getOrgId() != null) {
                    deviceUnbindMsg.setOrgId(deviceInfoDTO.getOrgId());
                }
                if (deviceInfoDTO.getTreeId() != null) {
                    deviceUnbindMsg.setTreeId(deviceInfoDTO.getTreeId());
                }

                log.info("Ding发送设备解绑消息:[{}]", JsonUtils.toJson(deviceUnbindMsg));
                EventBus.publish(deviceUnbindMsg);
            }
        }
    }

    private void distributorCheck(String orgName, String deviceSn, String thirdDeviceId) {
        //todo 感觉数据库设计存在缺陷,等待统一修改替换
        if (orgName != null) {
            Distributor distributor = distributorManager.getByDeviceSn(deviceSn);
            if (distributor != null) {
                if (StringUtils.isBlank(distributor.getDistributorName())) {
                    redissonCacheComponent.setObjectCache(thirdDeviceId + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                        StatusCode.THIRD_PARTY_ACTIVE_FAILED,HiveConst.THIRD_PARTY_TIME_OUT);
                    ErrorContext errorContext = new ErrorContext("FD0020171206", "设备和机构不属于同一区域");
                    BizAssert.notNull(null, errorContext, errorContext.getMessage());
                } else {
                    String[] orgNameArray = distributor.getOrgNameList().split(",");
                    List<String> orgNames = Arrays.asList(orgNameArray);
                    if (!orgNames.contains(orgName)) {
                        redissonCacheComponent.setObjectCache(thirdDeviceId
                                + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                            StatusCode.THIRD_PARTY_ACTIVE_FAILED,HiveConst.THIRD_PARTY_TIME_OUT);
                        ErrorContext errorContext = new ErrorContext("FD0020171206",
                            "设备和机构不属于同一区域");
                        BizAssert.notNull(null, errorContext, errorContext.getMessage());
                    }
                }
            }
        }
    }

    private boolean eduProtectCheck(CorpDto corpDto, InventoryDevice inventoryDevice) {
        //配置未启用，不做激活保护检查
        if (!industryProtectedConfigs.edu.enable) {
            return true;
        }
        DeviceSn deviceSn = new DeviceSn(inventoryDevice.getSerialNumber());
        //在保护期之外，不做激活保护检查
        if (industryProtectedConfigs.edu.getDeviceProductDay().after(deviceSn.getProductDay())) {
            return true;
        }
        //教育行业设备只有在教育机构激活， 非教育行业设备只能在非教育行业激活，不能串货
        boolean isEduIndustry = industryProtectedConfigs.edu.containIndustry(corpDto.getIndustry());
        Boolean isEduDevice = inventoryDevice.getIsEduDevice();
        if (isEduDevice == null){
            return true;
        }
        return isEduIndustry && isEduDevice || !isEduIndustry && !isEduDevice;
    }
}
