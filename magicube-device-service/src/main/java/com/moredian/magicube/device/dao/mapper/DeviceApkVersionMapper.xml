<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceApkVersionMapper">

    <resultMap id="deviceVersionResultMap" type="com.moredian.magicube.device.dao.entity.DeviceVersion">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="app_type" property="appType"/>
        <result column="version_code" property="versionCode"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>
    <sql id="sql_columns">
        id,
        org_id,
        device_id,
        device_sn,
        app_type,
        version_code,
        gmt_create,
        gmt_modify
    </sql>
    <sql id="sql_table">
        hive_device_apk_version
    </sql>
    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceVersion">
        INSERT INTO
        <include refid="sql_table"/>
        (<include refid="sql_columns"/>)
        VALUES
        (#{id}, #{orgId}, #{deviceId}, #{deviceSn}, #{appType}, #{versionCode}, now(3), now(3))
    </insert>

    <select id="getDeviceApkVersionBySn" resultMap="deviceVersionResultMap">
        SELECT
        <include refid="sql_columns"/>
        FROM
        <include refid="sql_table"/>
        where device_sn = #{deviceSn}
    </select>

    <select id="getBatchDeviceApkVersion" resultMap="deviceVersionResultMap">
        SELECT
        <include refid="sql_columns"/>
        FROM
        <include refid="sql_table"/>
        WHERE device_sn in
        <if test="deviceSnList != null and deviceSnList.size() > 0">
            <foreach collection="deviceSnList" index="index" item="deviceSn" open="(" separator="," close=")">
                #{deviceSn}
            </foreach>
        </if>
    </select>
    <select id="findDeviceApkVersionByOrgIdListAndVersion" resultMap="deviceVersionResultMap">
        SELECT
        <include refid="sql_columns"/>
        FROM
        <include refid="sql_table"/>
        WHERE org_id in
        <if test="orgIdList != null and orgIdList.size() > 0">
            <foreach collection="orgIdList" index="index" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        and app_type = #{appType}
        and version_code &lt; #{versionCode}
    </select>

    <update id="updateDeviceApkVersionByDeviceSn" parameterType="com.moredian.magicube.device.dao.entity.DeviceVersion">
        UPDATE
        <include refid="sql_table"/>
        <set>
            <if test="orgId != null">
                org_id = #{orgId},
            </if>
            <if test="deviceId != null">
                device_id = #{deviceId},
            </if>
            <if test="appType != null">
                app_type = #{appType},
            </if>
            <if test="versionCode != null">
                version_code = #{versionCode},
            </if>
            gmt_modify = now(3)
            where device_sn = #{deviceSn}
        </set>
    </update>

    <select id="getBatchDeviceApkVersionByOrgIdAndDeviceIds" resultMap="deviceVersionResultMap">
        SELECT
        <include refid="sql_columns"/>
        FROM
        <include refid="sql_table"/>
        WHERE org_id=#{orgId} and device_id in
        <foreach collection="deviceIdList" index="index" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </select>

</mapper>