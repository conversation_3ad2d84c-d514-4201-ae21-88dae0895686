package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dao.entity.GroupPersonSnapshot;
import com.moredian.magicube.device.dao.mapper.GroupPersonSnapshotMapper;
import com.moredian.magicube.device.dto.snapshot.GroupPersonSnapshotDTO;
import com.moredian.magicube.device.manager.GroupPersonSnapshotManager;
import com.moredian.magicube.device.model.PersonIdAndGroupIdSnapshot;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/29
 */
@Component
public class GroupPersonSnapshotManagerImpl implements GroupPersonSnapshotManager {

    @Autowired
    private GroupPersonSnapshotMapper groupPersonSnapshotMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsert(List<GroupPersonSnapshotDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Boolean.FALSE;
        }
        List<List<GroupPersonSnapshotDTO>> subList = new ArrayList<>();
        //计算切分次数
        int limit = (list.size() + perSubListSize - 1) / perSubListSize;
        Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
            subList.add(list.stream().skip(i * perSubListSize).limit(perSubListSize).collect(Collectors.toList()));
        });
        for (List<GroupPersonSnapshotDTO> sublist : subList) {
            List<GroupPersonSnapshot> groupPersonSnapshotList = convertToGroupPersonSnapshotList(sublist);
            if (CollectionUtils.isNotEmpty(groupPersonSnapshotList)) {
                groupPersonSnapshotMapper.batchInsert(groupPersonSnapshotList);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBySnapshotId(Long orgId, Long snapshotId) {
        BizAssert.notNull(snapshotId, "snapshotId must not be null");
        groupPersonSnapshotMapper.deleteBySnapshotId(orgId, snapshotId);
        return null;
    }

    @Override
    public int getDistinctPersonSize(Long orgId, Long snapshotId, List<Long> groupIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        if (CollectionUtils.isEmpty(groupIds)) {
            return 0;
        }
        return groupPersonSnapshotMapper.getDistinctPersonSize(orgId, snapshotId, groupIds);
    }

    @Override
    public List<GroupPersonSnapshot> pageGroupDistinctPerson(Long orgId, Long snapshotId, List<Long> groupIds, Integer pageNo, Integer pageSize) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(groupIds), "groupIds must not be empty");
        BizAssert.isTrue(pageNo != null && pageNo > 0, "pageNo must not be null");
        BizAssert.isTrue(pageSize != null && pageSize > 0, "pageSize must not be null");
        return groupPersonSnapshotMapper.pageGroupDistinctPerson(orgId, snapshotId, groupIds, (pageNo - 1) * pageSize, pageSize);
    }

    @Override
    public List<PersonIdAndGroupIdSnapshot> findGroupIdByPersonIds(Long orgId, Long snapshotId, List<Long> groupIdList, List<Long> personIdList) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(snapshotId, "snapshotId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(personIdList), "personIdList must not be empty");
        return groupPersonSnapshotMapper.findGroupIdByPersonIds(orgId, snapshotId, groupIdList, personIdList);
    }

    private List<GroupPersonSnapshot> convertToGroupPersonSnapshotList(List<GroupPersonSnapshotDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().filter(dto -> dto != null).map(dto -> convertToGroupPersonSnapshot(dto)).collect(Collectors.toList());
    }

    public GroupPersonSnapshot convertToGroupPersonSnapshot(GroupPersonSnapshotDTO dto) {
        if (dto == null) {
            return null;
        }
        GroupPersonSnapshot snapshot = new GroupPersonSnapshot();
        snapshot.setId(dto.getId());
        snapshot.setGroupId(dto.getGroupId());
        snapshot.setOrgId(dto.getOrgId());
        snapshot.setPersonId(dto.getPersonId());
        snapshot.setPersonType(dto.getPersonType());
        snapshot.setSnapshotId(dto.getSnapshotId());
        return snapshot;
    }
}
