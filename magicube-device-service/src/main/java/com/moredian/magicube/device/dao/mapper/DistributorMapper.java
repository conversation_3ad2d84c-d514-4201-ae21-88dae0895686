package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.Distributor;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DistributorMapper {

    /**
     * 新增渠道商设备锁定
     *
     * @param distributor
     * @return
     */
    int insert(Distributor distributor);

    /**
     * 根据机构名模糊查询
     *
     * @param orgName
     * @return
     */
    List<Distributor> listByOrgName(String orgName);

    /**
     * 根据设备sn模糊查询
     *
     * @param deviceSn
     * @return
     */
    Distributor getByDeviceSn(String deviceSn);
}
