package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.PerBindDeviceType;
import com.moredian.magicube.common.enums.PeripheralConnectionStatusEnums;
import com.moredian.magicube.common.enums.PeripheralsType;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.Peripherals;
import com.moredian.magicube.device.dao.mapper.PeripheralsMapper;
import com.moredian.magicube.device.dto.peripherals.InsertPeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.QueryPeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.UpdatePeripheralsDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.PeripheralsManager;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Description
 * @Date 2020/2/14 15:14
 */

@Service
@Slf4j
public class PeripheralsManagerImpl implements PeripheralsManager {

    @Autowired
    private PeripheralsMapper peripheralsMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(InsertPeripheralsDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getPeripheralsSn(), "peripheralsSn must not be null");
        BizAssert.notNull(dto.getDeviceSn(), "deviceSn must not be null");
        //1.先查询之前的老设备--进行删除
        QueryPeripheralsDTO condition = new QueryPeripheralsDTO();
        condition.setOrgId(dto.getOrgId());
        condition.setPeripheralsSn(dto.getPeripheralsSn());
        condition.setStatus(PeripheralConnectionStatusEnums.BIND.getValue());
        List<Peripherals> oldPeripheralsList = peripheralsMapper.listByCondition(condition);
        for (Peripherals oldPeripherals : oldPeripheralsList) {
            Peripherals perUp = new Peripherals();
            perUp.setStatus(PeripheralConnectionStatusEnums.DISCONNECT.getValue());
            perUp.setPeripheralsId(oldPeripherals.getPeripheralsId());
            perUp.setOrgId(oldPeripherals.getOrgId());
            perUp.setMatchStatus(dto.getMatchStatus());
            peripheralsMapper.update(perUp);
        }

        //2.先把改deviceSn相关的设备的status都改为0--目的是为了一台门禁同事只能连接一台
        //三种情况  额-额，额-红，红-额
        //如果当前连接的是额温枪，则要处理上一台额温枪，红外不处理
        if (PeripheralsType.FOREHEAD_PER.getType().equals(dto.getPeripheralsType())) {
            //查询目前已经连接的额温情，然后给断掉
            disConnectPeripherals(dto, PeripheralsType.FOREHEAD_PER.getType());
        } else {
            //如果当前连接的是红外或者腕温，则处理未断开的腕温和红外
            disConnectPeripherals(dto, dto.getPeripheralsType());
        }

        //3.执行插入操作,先根据两个sn查询
        QueryPeripheralsDTO perSearch = new QueryPeripheralsDTO();
        perSearch.setDeviceSn(dto.getDeviceSn());
        perSearch.setPeripheralsSn(dto.getPeripheralsSn());
        perSearch.setOrgId(dto.getOrgId());
        //该条件只会查询到1条,历史代码（不敢轻易改动）
        List<Peripherals> peripheralsList = peripheralsMapper.listByCondition(perSearch);
        if (CollectionUtils.isNotEmpty(peripheralsList)) {
            for (Peripherals peripherals : peripheralsList) {
                //更改
                peripherals.setStatus(PeripheralConnectionStatusEnums.BIND.getValue());
                peripherals.setPeripheralsType(dto.getPeripheralsType());
                //设置sn
                peripherals.setPeripheralsSn(dto.getPeripheralsSn());
                peripherals.setMatchStatus(dto.getMatchStatus());
                int res = peripheralsMapper.update(peripherals);
                if (res > 0) {
                    return Boolean.TRUE;
                }
                log.info("更新外设信息失败peripherals：{}", peripherals);
                BizAssert.notNull(null, DeviceErrorCode.PERIPHREALS_ADD_FAIL,
                    DeviceErrorCode.PERIPHREALS_ADD_FAIL.getMessage());
            }
        } else {
            Peripherals insertPeripherals = new Peripherals();
            BeanUtils.copyProperties(dto, insertPeripherals);
            insertPeripherals.setPeripheralsId(
                idgeneratorService.getNextIdByTypeName(BeanConstants.PERIPHERALS)
                    .pickDataThrowException());
            insertPeripherals.setStatus(PeripheralConnectionStatusEnums.BIND.getValue());
            int res = peripheralsMapper.insert(insertPeripherals);
            if (res > 0) {
                return Boolean.TRUE;
            }
            log.info("新增外设信息失败peripherals：{}", insertPeripherals);
            BizAssert.notNull(null, DeviceErrorCode.PERIPHREALS_ADD_FAIL,
                DeviceErrorCode.PERIPHREALS_ADD_FAIL.getMessage());
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(UpdatePeripheralsDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getPeripheralsId(), "peripheralsId must not be null");
        Peripherals updatePeripherals = new Peripherals();
        BeanUtils.copyProperties(dto, updatePeripherals);
        int i = peripheralsMapper.update(updatePeripherals);
        if (i > 0) {
            return Boolean.TRUE;
        }
        log.info("更新外设信息失败updatePeripherals：{}", updatePeripherals);
        BizAssert.notNull(null, DeviceErrorCode.PERIPHREALS_UPDATE_FAIL,
            DeviceErrorCode.PERIPHREALS_UPDATE_FAIL.getMessage());
        return Boolean.TRUE;
    }

    @Override
    public List<PeripheralsDTO> listByCondition(QueryPeripheralsDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        if (dto.getStatus() == null) {
            dto.setStatus(PeripheralConnectionStatusEnums.BIND.getValue());
        }
        List<PeripheralsDTO> peripheralsDTOs = new ArrayList<>();
        List<Peripherals> peripheralsList = peripheralsMapper.listByCondition(dto);
        if (CollectionUtils.isEmpty(peripheralsList)) {
            return peripheralsDTOs;
        }
        for (Peripherals per : peripheralsList) {
            PeripheralsDTO peripheralsDto = new PeripheralsDTO();
            if (dto.getDeviceType() != null && dto.getDeviceType()
                .equals(PerBindDeviceType.MOBILE_PHONE.getType())) {
                //只显示手机的
                if (per.getDeviceType() != null && per.getDeviceType()
                    .equals(PerBindDeviceType.MOBILE_PHONE.getType())) {
                    BeanUtils.copyProperties(per, peripheralsDto);
                    peripheralsDTOs.add(peripheralsDto);
                }
            } else {
                //只显示门禁的(门禁开始的deviceType可能为null)
                if (per.getDeviceType() == null || per.getDeviceType()
                    .equals(PerBindDeviceType.ATTENCT_DEVICE.getType())) {
                    BeanUtils.copyProperties(per, peripheralsDto);
                    peripheralsDTOs.add(peripheralsDto);
                }
            }
        }
        return peripheralsDTOs;
    }

    @Override
    public List<PeripheralsDTO> listByOrgIdAndDeviceSn(Long orgId, String deviceSn) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        List<PeripheralsDTO> peripheralsDtoList = new ArrayList<>();
        List<Peripherals> peripheralsList = peripheralsMapper
            .listByOrgIdAndDeviceSn(orgId, deviceSn);
        if (CollectionUtils.isNotEmpty(peripheralsList)) {
            for (Peripherals per : peripheralsList) {
                PeripheralsDTO peripheralsDto = new PeripheralsDTO();
                BeanUtils.copyProperties(per, peripheralsDto);
                peripheralsDtoList.add(peripheralsDto);
            }
        }
        return peripheralsDtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disConnect(UpdatePeripheralsDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getDeviceSn(), "deviceSn must not be null");
        Peripherals peripherals = new Peripherals();
        peripherals.setDeviceSn(dto.getDeviceSn());
        peripherals.setOrgId(dto.getOrgId());
        peripherals.setStatus(PeripheralConnectionStatusEnums.DISCONNECT.getValue());
        if (StringUtils.isNotBlank(dto.getPeripheralsSn())) {
            peripherals.setPeripheralsSn(dto.getPeripheralsSn());
        }
        peripheralsMapper.updateByCondition(peripherals);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Boolean> mobileConnect(UpdatePeripheralsDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getPeripheralsSn(), "peripheralsSn must not be null");
        BizAssert.notNull(dto.getDeviceSn(), "deviceSn must not be null");
        //历史原因，修改这个接口会影响业务,所以保持不变
        //绑定前，先判断有没有跟其他机构绑定
        Peripherals perOther = new Peripherals();
        perOther.setDeviceType(PerBindDeviceType.MOBILE_PHONE.getType());
        perOther.setPeripheralsSn(dto.getPeripheralsSn());
        perOther.setOrgId(dto.getOrgId());
        List<Peripherals> peripheralsListOtherOrg = peripheralsMapper.listByOtherOrg(perOther);
        if (CollectionUtils.isNotEmpty(peripheralsListOtherOrg)) {
            //标识还跟其他机构有绑定
            //该设备还绑定在上一个机构，所以目前这个新绑定操作不能成功
            log.info("其他机构的外设信息peripheralsListOtherOrg{}", peripheralsListOtherOrg);
            //返回错误以及其他机构的信息
            PeripheralsDTO dtoRes = new PeripheralsDTO();
            BeanUtils.copyProperties(peripheralsListOtherOrg.get(0), dtoRes);
            return new ServiceResponse(
                new ErrorContext(DeviceErrorCode.PERIPHREALS_BIND_PERIOUS_ORG), dtoRes);
        }

        //没有绑定其他机构 先根据两个sn查询
        QueryPeripheralsDTO perSearch = new QueryPeripheralsDTO();
        perSearch.setDeviceSn(dto.getDeviceSn());
        perSearch.setPeripheralsSn(dto.getPeripheralsSn());
        perSearch.setOrgId(dto.getOrgId());
        perSearch.setDeviceType(PerBindDeviceType.MOBILE_PHONE.getType());
        List<Peripherals> peripheralsList = peripheralsMapper.listByCondition(perSearch);
        if (CollectionUtils.isNotEmpty(peripheralsList)) {
            //更改
            Peripherals peripherals = peripheralsList.get(0);
            peripherals.setStatus(PeripheralConnectionStatusEnums.BIND.getValue());
            peripherals.setAvailable(dto.getAvailable());
            //设置sn
            peripherals.setOrgId(dto.getOrgId());
            peripherals.setPeripheralsSn(peripherals.getPeripheralsSn());
            peripherals.setPeripheralsId(peripherals.getPeripheralsId());
            int res = peripheralsMapper.update(peripherals);
            if (res > 0) {
                return new ServiceResponse<>(Boolean.TRUE);
            }
            log.info("手机端更新外设信息peripherals：{}", peripherals);
            return new ServiceResponse(new ErrorContext(DeviceErrorCode.PERIPHREALS_ADD_FAIL),
                null);
        } else {
            dto.setPeripheralsId(idgeneratorService.getNextIdByTypeName(BeanConstants.PERIPHERALS)
                .pickDataThrowException());
            Peripherals peripherals = new Peripherals();
            BeanUtils.copyProperties(dto, peripherals);
            if (peripherals.getStatus() == null) {
                peripherals.setStatus(PeripheralConnectionStatusEnums.BIND.getValue());
            }
            int res = peripheralsMapper.insert(peripherals);
            if (res > 0) {
                return new ServiceResponse<>(Boolean.TRUE);
            }
            log.info("手机端新增外设信息peripherals：{}", peripherals);
            return new ServiceResponse(new ErrorContext(DeviceErrorCode.PERIPHREALS_ADD_FAIL),
                null);
        }
    }

    @Override
    @Transactional
    public Boolean mobileDisConnect(UpdatePeripheralsDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getStatus(), "status must not be null");
        //先查询看有没有，有的话在执行删除操作
        Peripherals peripherals = new Peripherals();
        peripherals.setDeviceSn(dto.getDeviceSn());
        peripherals.setStatus(dto.getStatus());
        peripherals.setOrgId(dto.getOrgId());
        peripherals.setPeripheralsSn(dto.getPeripheralsSn());
        peripheralsMapper.updateByCondition(peripherals);
        return Boolean.TRUE;
    }

    @Override
    public Boolean existHistoryPeripherals(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        Boolean peripheralsExists = Boolean.FALSE;
        List<Long> peripheralsIds = peripheralsMapper.listByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(peripheralsIds)) {
            peripheralsExists = Boolean.TRUE;
        }
        return peripheralsExists;
    }

    @Override
    public List<PeripheralsDTO> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceSns), "deviceSns must not be null");
        List<Peripherals> peripheralsList = peripheralsMapper
            .listByOrgIdAndDeviceSns(orgId, deviceSns);
        List<PeripheralsDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(peripheralsList)) {
            for (Peripherals peripherals : peripheralsList) {
                PeripheralsDTO dto = new PeripheralsDTO();
                BeanUtils.copyProperties(peripherals, dto);
                list.add(dto);
            }
        }
        return list;
    }

    /**
     * 断开外设的连接
     *
     * @param dto  外设信息
     * @param type 外设的设备类型
     */
    private void disConnectPeripherals(InsertPeripheralsDTO dto, Integer type) {
        QueryPeripheralsDTO condition = new QueryPeripheralsDTO();
        condition.setOrgId(dto.getOrgId());
        condition.setStatus(PeripheralConnectionStatusEnums.BIND.getValue());
        condition.setDeviceSn(dto.getDeviceSn());
        condition.setPeripheralsType(type);
        List<Peripherals> peripheralsList = peripheralsMapper.listByCondition(condition);
        if (CollectionUtils.isNotEmpty(peripheralsList)) {
            for (Peripherals peripherals : peripheralsList) {
                Peripherals perUp = new Peripherals();
                perUp.setStatus(PeripheralConnectionStatusEnums.DISCONNECT.getValue());
                perUp.setPeripheralsId(peripherals.getPeripheralsId());
                perUp.setOrgId(peripherals.getOrgId());
                peripheralsMapper.update(perUp);
            }
        }
    }
}
