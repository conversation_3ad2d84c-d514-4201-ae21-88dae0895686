package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dto.person.PagePersonInfoDTO;
import com.moredian.magicube.device.dto.person.PersonInfoDTO;
import com.moredian.magicube.device.dto.person.QueryPersonInfoDTO;
import com.moredian.magicube.device.dto.snapshot.PaginationDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/25
 */
public interface DeviceSyncPersonInfoManager {

    Integer FIRST_PAGE = 1;

    /**
     * 门锁全量同步人员特征值、卡片、权限组
     *
     * @param dto
     * @return
     */
    PaginationDTO<PersonInfoDTO> pagePersonInfo(PagePersonInfoDTO dto);

    /**
     * 门锁查询人员特征值、卡片、权限组
     *
     * @param dto
     * @return
     */
    List<PersonInfoDTO> queryPersonInfo(QueryPersonInfoDTO dto);
}
