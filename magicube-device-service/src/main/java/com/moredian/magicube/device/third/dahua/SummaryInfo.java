package com.moredian.magicube.device.third.dahua;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class SummaryInfo {

    private int inChannelID;

    private String stuTime;

    private int stuEnteredSubtotalToday;

    private int stuEnteredSubtotalHour;

    private int stuEnteredSubtotalTotal;

    private int stuExitedSubtotalToday;

    private int stuExitedSubtotalHour;

    private int stuExitedSubtotalTotal;

    private int nInsidePeopleNum;

    private int emRuleType;

    public SummaryInfo(int nChannelID, String stuTime, int nToday, int nHour, int nTotal, int nToday1, int nHour1, int nTotal1,
        int nInsidePeopleNum, int emRuleType) {
        this.inChannelID = nChannelID;
        this.stuTime = stuTime;
        this.stuEnteredSubtotalToday = nToday;
        this.stuEnteredSubtotalHour = nHour;
        this.stuEnteredSubtotalTotal = nTotal;
        this.stuExitedSubtotalToday = nToday1;
        this.stuExitedSubtotalHour = nHour1;
        this.stuExitedSubtotalTotal = nTotal1;
        this.nInsidePeopleNum = nInsidePeopleNum;
        this.emRuleType = emRuleType;
    }
}
