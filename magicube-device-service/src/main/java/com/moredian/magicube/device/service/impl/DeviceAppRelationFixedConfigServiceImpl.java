package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import com.moredian.magicube.device.dto.device.DeviceAppRelationFixedConfigDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;
import com.moredian.magicube.device.enums.DeviceRelateAppFixedBIzTypeEnum;
import com.moredian.magicube.device.manager.DeviceAppRelationFixedConfigManager;
import javax.annotation.Resource;
import com.moredian.magicube.device.convertor.DeviceAppRelationFixedConfigConvertor;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.service.DeviceAppRelationFixedConfigService;
import org.apache.commons.collections.CollectionUtils;

import java.util.stream.Collectors;
import java.util.List;


@SI
public class DeviceAppRelationFixedConfigServiceImpl implements DeviceAppRelationFixedConfigService {

    @Resource
    private DeviceAppRelationFixedConfigManager deviceAppRelationFixedConfigManager;

    @Override
    public ServiceResponse<Pagination<DeviceAppRelationFixedConfigDTO>> listPage(QueryDeviceAppRelationConfigDTO dto) {
        Pagination<DeviceAppRelationFixedConfig> pagination = deviceAppRelationFixedConfigManager.listPage(dto);
        Pagination<DeviceAppRelationFixedConfigDTO> result = new Pagination<>();
        result.setTotalCount(pagination.getTotalCount());
        result.setPageNo(pagination.getPageNo());
        result.setPageSize(pagination.getPageSize());
        if (CollectionUtils.isNotEmpty(pagination.getData())){
            List<DeviceAppRelationFixedConfigDTO> collect = pagination.getData()
                    .stream()
                    .map(DeviceAppRelationFixedConfigConvertor::entityToDto)
                    .collect(Collectors.toList());
            result.setData(collect);
        }
        return new ServiceResponse<>(result);
    }

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    @Override
    public ServiceResponse<List<DeviceAppRelationFixedConfigDTO>> listAll() {
    
    	List<DeviceAppRelationFixedConfig> deviceAppRelationFixedConfigList = deviceAppRelationFixedConfigManager.listAll();
        //转换成DTO
        List<DeviceAppRelationFixedConfigDTO> collect = deviceAppRelationFixedConfigList
                .stream()
                .map(DeviceAppRelationFixedConfigConvertor::entityToDto)
                .collect(Collectors.toList());
    	return new ServiceResponse<>(collect);
    }


    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    @Override
    public ServiceResponse<DeviceAppRelationFixedConfigDTO> getById(Long id) {
        BizAssert.notNull(id, "id must not null");
        DeviceAppRelationFixedConfig deviceAppRelationFixedConfig = deviceAppRelationFixedConfigManager.getById(id);
    	DeviceAppRelationFixedConfigDTO deviceAppRelationFixedConfigDTO = DeviceAppRelationFixedConfigConvertor.entityToDto(deviceAppRelationFixedConfig);
        return new ServiceResponse<>(deviceAppRelationFixedConfigDTO);
    }
	
    /**
     * 新增
     *
     * @param deviceAppRelationFixedConfigDTO 新增的记录
     * @return 主键id
     */
    @Override
    public ServiceResponse<Long> insert(DeviceAppRelationFixedConfigDTO deviceAppRelationFixedConfigDTO) {
    	DeviceAppRelationFixedConfig deviceAppRelationFixedConfig = DeviceAppRelationFixedConfigConvertor.dtoToEntity(deviceAppRelationFixedConfigDTO);
    	return new ServiceResponse<>(deviceAppRelationFixedConfigManager.insert(deviceAppRelationFixedConfig));
    }
	
    /**
     * 修改所有字段
     *
     * @param deviceAppRelationFixedConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    @Override
    public ServiceResponse<Boolean> update(DeviceAppRelationFixedConfigDTO deviceAppRelationFixedConfigDTO) {
    	DeviceAppRelationFixedConfig deviceAppRelationFixedConfig = DeviceAppRelationFixedConfigConvertor.dtoToEntity(deviceAppRelationFixedConfigDTO);
    	int flag = deviceAppRelationFixedConfigManager.update(deviceAppRelationFixedConfig);
    	return new ServiceResponse<>(flag == 1);
    }
	
    /**
     * 修改不为null字段
     *
     * @param deviceAppRelationFixedConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    @Override
    public ServiceResponse<Boolean> updateSelective(DeviceAppRelationFixedConfigDTO deviceAppRelationFixedConfigDTO) {
    	DeviceAppRelationFixedConfig deviceAppRelationFixedConfig = DeviceAppRelationFixedConfigConvertor.dtoToEntity(deviceAppRelationFixedConfigDTO);
    	int flag = deviceAppRelationFixedConfigManager.updateSelective(deviceAppRelationFixedConfig);
    	return new ServiceResponse<>(flag == 1);
    }
	
    /**
     * 删除
     *
     * @param id 待删除的记录id
     * @return 是否成功true/false
     */
    @Override
    public ServiceResponse<Boolean> delete(Long id) {
        BizAssert.notNull(id, "id must not null");
    	int flag = deviceAppRelationFixedConfigManager.delete(id);
    	return new ServiceResponse<>(flag == 1);
    }

    @Override
    public ServiceResponse<Boolean> deleteByDeviceSn(String deviceSn) {
        BizAssert.notNull(deviceSn, "deviceSn must not null");
        DeviceAppRelationFixedConfigDTO fixedConfig = new DeviceAppRelationFixedConfigDTO();
        fixedConfig.setBizId(deviceSn);
        fixedConfig.setBizType(DeviceRelateAppFixedBIzTypeEnum.DEVICE_SN.getCode());
        return this.deleteByBizTypeAndId(fixedConfig);
    }

    @Override
    public ServiceResponse<Boolean> deleteByBizTypeAndId(DeviceAppRelationFixedConfigDTO dto) {
        DeviceAppRelationFixedConfig deviceAppRelationFixedConfig = DeviceAppRelationFixedConfigConvertor.dtoToEntity(dto);
        int flag = deviceAppRelationFixedConfigManager.deleteByBizTypeAndId(deviceAppRelationFixedConfig);
        return new ServiceResponse<>(flag == 1);

    }
}