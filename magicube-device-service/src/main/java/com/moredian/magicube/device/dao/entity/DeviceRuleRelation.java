package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_rule_relation")
public class DeviceRuleRelation extends TimedEntity {
    private Long deviceRuleRelationId;

    private Long orgId;

    private Long ruleId;

    private Long deviceId;
}