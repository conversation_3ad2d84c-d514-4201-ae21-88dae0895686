package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dto.device.DeviceReportInfoDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceInfoDTO;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/1/24
 */
public interface DeviceReportManager {

    /**
     * 通知设备上报信息
     *
     * @param dto
     * @return
     */
    Boolean notifyDeviceReportInfo(DeviceReportInfoDTO dto);

    /**
     * 门锁设备上报电量，wifi强度，版本细腻，本地时间接口
     *
     * @param dto
     * @return
     */
    Boolean reportDeviceInfo(ReportDeviceInfoDTO dto);
}
