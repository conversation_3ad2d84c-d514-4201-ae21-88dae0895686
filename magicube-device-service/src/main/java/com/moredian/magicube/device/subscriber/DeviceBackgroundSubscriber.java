package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.UUID;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.ControllerErrorCode;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.common.model.msg.DeviceBackGroundMsg;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设备后台订阅者
 *
 * <AUTHOR>
 * @date 2024/06/28
 */
@Slf4j
@Component
public class DeviceBackgroundSubscriber {



    @Autowired
    private DeviceManager deviceManager;

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Subscribe
    public void subscribeDeviceBackGroundMsg(DeviceBackGroundMsg msg) {
        log.info("subscribe DeviceBackGroundMsg:{}", msg);
        // 只有支持通过权限配置进入设备管理后台的设备才可以发送消息
        List<Device> devices = deviceManager.listByOrgIdAndTypes(msg.getOrgId(), deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.SUPPORT_AUTH_DEVICE_MANAGEMENT_LIST));
        if (CollectionUtils.isEmpty(devices)) {
            log.warn("Notify device DeviceBackGroundMsg,no device is found");
        } else {
            log.info("Notify device DeviceBackGroundMsg,deviceCount:{}", devices.size());
            for (Device device : devices) {
                try {
                    // 发送命令
                    String deviceSn = device.getDeviceSn();
                    TransferMessageInfo<String> transferMessageInfo = new TransferMessageInfo<>();
                    transferMessageInfo
                        .setEventType(TransferEventType.DEVICE_BACKGROUND_AUTH_ALL_SYNC.getEventType());
                    transferMessageInfo.setSeverity(3);
                    transferMessageInfo.setSeqId(UUID.random19()
                        + TransferEventType.DEVICE_BACKGROUND_AUTH_ALL_SYNC.getEventName());
                    transferMessageInfo
                        .setMessage(TransferEventType.DEVICE_BACKGROUND_AUTH_ALL_SYNC.getEventName());
                    String jsonStr = JsonUtils.toJson(transferMessageInfo);
                    String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
                    TransferRequest transferRequest = new TransferRequest();
                    transferRequest.setSerialNumber(deviceSn);
                    transferRequest.setBody(base64Message);
                    transferRequest.setCommand(0);
                    ServiceResponse<TransferResponse> response = hubControlServiceV1.transfer(
                        transferRequest);
                    if (response != null && !response.isSuccess()
                        && ControllerErrorCode.DEVICE_OFFLINE.getCode()
                        .equals(response.getErrorContext().getCode())) {
                        log.info(
                            "Notify device DeviceBackGroundMsg, 设备已离线，本次不发送通知，[org={},deviceSn={},deviceId={}]",
                            msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                        continue;
                    }

                    TransferResponse result = response.pickDataThrowException();
                    log.info("Notify device DeviceBackGroundMsg, [org={},deviceSn={},deviceId={}]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                } catch (Exception e) {
                    log.error(
                        "Notify device DeviceBackGroundMsg failed. [org={},device [sn={},id={}]]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId(), e);
                }
            }
        }
    }

}
