package com.moredian.magicube.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO.DeviceState;
import com.moredian.bridge.api.service.deviceInstance.req.DeviceFunctionInvokeRequest;
import com.moredian.bridge.api.service.deviceInstance.req.QueryDeviceListRequest;
import com.moredian.bridge.api.service.deviceInstance.req.WriteDevicePropertyRequest;
import com.moredian.bridge.api.service.deviceProperty.IIotDevicePropertyService;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyInfoDTO;
import com.moredian.bridge.api.service.mapping.IOrgIotClientMappingService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.DeviceIotSplitConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dao.entity.DeviceTypePropertyConfig;
import com.moredian.magicube.device.dao.entity.RuleTemplate;
import com.moredian.magicube.device.dao.mapper.DeviceTypePropertyConfigMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.IotDeviceDTO;
import com.moredian.magicube.device.dto.device.IotDevicePropertyDTO;
import com.moredian.magicube.device.dto.device.IotDevicePropertyDTO.DataObject;
import com.moredian.magicube.device.dto.device.IotDevicePropertyDTO.Properties;
import com.moredian.magicube.device.dto.device.IotDeviceTypeCountDTO;
import com.moredian.magicube.device.dto.device.PageQueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.UpdateIotDevicePropertyDTO;
import com.moredian.magicube.device.dto.device.iot.DeviceFunctionInvokeDTO;
import com.moredian.magicube.device.dto.device.iot.IotDeviceListDTO;
import com.moredian.magicube.device.dto.device.iot.PageQueryIotDeviceListDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDTO;
import com.moredian.magicube.device.enums.IotDeviceStatusEnums;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypeManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.IotDeviceManager;
import com.moredian.magicube.device.manager.RuleTemplateManager;
import com.moredian.magicube.device.manager.bo.QueryPageDeviceBO;
import com.moredian.magicube.device.model.DeviceMetadataExtend;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceTypePropertyService;
import com.moredian.magicube.device.service.DeviceTypeService;
import com.moredian.magicube.device.service.IotDeviceService;
import com.moredian.magicube.device.service.helper.IotDeviceServiceHelper;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.util.ObjectUtils;

/**
 * iot设备接口
 *
 * <AUTHOR>
 */

@SI
@Slf4j
public class IotDeviceServiceImpl implements IotDeviceService {

    @Autowired
    private IotDeviceManager iotDeviceManager;

    @Autowired
    private Executor iotDevicePagePool;

    @Autowired
    private DeviceTypeManager deviceTypeManager;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private RuleTemplateManager ruleTemplateManager;

    @Autowired
    private IOrgIotClientMappingService iOrgIotClientMappingService;

    @Resource
    private DeviceTypePropertyConfigMapper deviceTypePropertyConfigMapper;

    @Resource
    private RedissonCacheComponent redissonCacheComponent;
    @Resource
    private IotDeviceServiceHelper iotDeviceServiceHelper;

    @SI
    private SpaceTreeDeviceRelationService treeDeviceRelationService;

    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;

    @SI
    private IIotDevicePropertyService iotDevicePropertyService;

    @SI
    private DeviceTypePropertyService deviceTypePropertyService;

    @SI
    private DeviceTypeService deviceTypeService;

    @SI
    private DeviceService deviceService;

    @Override
    public ServiceResponse<List<DeviceTypeDTO>> listIotDeviceType() {
        ServiceResponse<List<DeviceTypeDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Integer> iotDeviceTypes = deviceTypePropertyService.
            getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST)
            .pickDataThrowException();
        if (CollectionUtils.isEmpty(iotDeviceTypes)) {
            return serviceResponse;
        }
        // 去除虚拟设备类型
        iotDeviceTypes.removeAll(deviceTypePropertyService.
            getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_VIRTUAL_DEVICE_TYPE_LIST)
            .pickDataThrowException());
        List<DeviceTypeDTO> deviceTypes = deviceTypeService
            .listByTypes(iotDeviceTypes).pickDataThrowException();
        serviceResponse.setData(deviceTypes);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<IotDeviceDTO>> listIotDevice(QueryIotDeviceDTO dto) {
        ServiceResponse<List<IotDeviceDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Device> device = iotDeviceManager.listIotDevice(dto);
        if (CollectionUtils.isNotEmpty(device)) {
            List<IotDeviceDTO> iotDeviceDTOS = deviceToIotDeviceInfoDTO(device);
            serviceResponse.setData(iotDeviceDTOS);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Pagination<IotDeviceDTO>> listByConditionPage(PageQueryIotDeviceDTO dto) {
        ServiceResponse<Pagination<IotDeviceDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        Pagination<IotDeviceDTO> page = new Pagination<>();

        Long orgId = dto.getOrgId();
        // 机构是否开通魔链判断，未开通魔链直接返回空
        List<Long> orgIdList = iOrgIotClientMappingService.getAuthorizedOrgIdList()
            .pickDataThrowException();
        if (!orgIdList.contains(orgId)){
            serviceResponse.setData(page);
            return serviceResponse;
        }

        Pagination<Device> pa = iotDeviceManager.listByConditionPage(dto);
        if (CollectionUtils.isNotEmpty(pa.getData())) {
            List<IotDeviceDTO> iotDeviceDTOS = deviceToIotDeviceInfoDTO(pa.getData());

            // 如果列表需要查询实时数据
            Integer searchProperties = dto.getSearchProperties();
            if (!ObjectUtils.isEmpty(searchProperties) && YesNoFlag.YES.getValue() == searchProperties){
                try{
                    buildDeviceListProperties(dto.getOrgId(), iotDeviceDTOS);
                }catch (Exception e){
                    log.error("IotDeviceServiceImpl/listByConditionPage,异步查询设备列表属性异常，m=>{}", e.getMessage());
                    throw new RuntimeException("异步查询设备列表属性异常");
                }
            }
            page.setTotalCount(pa.getTotalCount());
            page.setData(iotDeviceDTOS);
        }
        page.setPageNo(pa.getPageNo());
        page.setPageSize(pa.getPageSize());
        serviceResponse.setData(page);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Pagination<IotDeviceListDTO>> listByConditionPage(PageQueryIotDeviceListDTO dto) {
        Long orgId = dto.getOrgId();
        BizAssert.notNull(orgId, "orgId must be not null");
        Pagination<IotDeviceListDTO> page = iotDeviceManager.listByConditionPage(dto);
        ServiceResponse<Pagination<IotDeviceListDTO>> response = ServiceResponse.createSuccessResponse();
        response.setData(page);
        return response;
    }


    private void buildDeviceListProperties(Long orgId, List<IotDeviceDTO> iotDeviceDTOS)
        throws ExecutionException, InterruptedException {
        List<CompletableFuture<IotDeviceDTO>> futureList = new ArrayList<>();
        for (IotDeviceDTO iotDeviceDTO : iotDeviceDTOS) {
            // 异步查询设备属性
            CompletableFuture<IotDeviceDTO> future = CompletableFuture.supplyAsync(() -> {
                buildDeviceProperties(orgId, iotDeviceDTO.getDeviceSn(), iotDeviceDTO);
                return iotDeviceDTO;
            }, iotDevicePagePool);
            futureList.add(future);
        }
        CompletableFuture<Void> cfAll = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        cfAll.get();

        // 如果是查询的是电表，需要将倍率、开合匝取出来
        for (IotDeviceDTO deviceDTO : iotDeviceDTOS) {
            if (com.moredian.magicube.common.enums.DeviceType.ENERGY_METER.getValue() == deviceDTO.getDeviceType()){
                getRateAndOCST(deviceDTO);
            }
        }
    }

    @Override
    public ServiceResponse<IotDeviceDTO> getById(Long orgId, Long deviceId) {
        ServiceResponse<IotDeviceDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        Device device = iotDeviceManager.getById(orgId, deviceId);
        if (device == null) {
            return null;
        }

        IotDeviceDTO iotDeviceDTO = new IotDeviceDTO();
        BeanUtils.copyProperties(device, iotDeviceDTO);
        if (device.getActiveTime() != null) {
            iotDeviceDTO.setActiveTime(device.getActiveTime().getTime());
        }
        if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
            iotDeviceDTO.setTpId(device.getThirdDeviceId());
        }
        //空间信息查询
        buildSpaceInfo(orgId, device, iotDeviceDTO);
        //判断是否虚拟设备
        buildVirtualFlag(device, iotDeviceDTO);
        //查询设备属性信息
        buildDeviceProperties(orgId, device.getDeviceSn(), iotDeviceDTO);
        //iot设备在离线信息和物模型元数据查询
        buildDeviceStateAndMetadata(orgId, device, iotDeviceDTO);
        //查询父设备Id
        buildParentDeviceInfo(device, iotDeviceDTO);
        //设备类型
        buildDeviceTypeInfo(device, iotDeviceDTO);
        //查询子设备列表
        buildSubDevice(orgId, device, iotDeviceDTO);
        if (Boolean.TRUE.equals(iotDeviceDTO.getVirtualDeviceFlag())){
            // 如果是虚拟设备只保留虚拟设备对应的物模型
            filterMetadata(iotDeviceDTO);
        }
        serviceResponse.setData(iotDeviceDTO);
        return serviceResponse;
    }

    private void filterMetadata(IotDeviceDTO iotDeviceDTO) {
        String metadataStr = iotDeviceDTO.getMetadata();
        String deviceSn = iotDeviceDTO.getDeviceSn();
        String snSuffix = deviceSn.split("_")[1];

        if (!ObjectUtils.isEmpty(metadataStr)){
            JSONObject metadata = JSONObject.parseObject(metadataStr);
            JSONArray metaProperties = metadata.getJSONArray("properties");
            JSONArray resultArray = new JSONArray();
            if (ObjectUtil.isNotEmpty(metaProperties)) {
                for (int i = 0; i < metaProperties.size(); i++) {
                    JSONObject tempObject = metaProperties.getJSONObject(i);
                    String id = tempObject.getString("id");
                    if (id.contains(snSuffix)){
                        resultArray.add(tempObject);
                    }
                }
            }
            metadata.put("properties", resultArray);
            iotDeviceDTO.setMetadata(JSONObject.toJSONString(metadata));
        }
    }

    @Override
    public ServiceResponse<Boolean> updateDeviceProperties(UpdateIotDevicePropertyDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        Device device = deviceManager.getByOrgIdAndId(dto.getOrgId(), dto.getDeviceId());
        BizAssert.notNull(device, DeviceErrorCode.DEVICE_NOT_EXIST,
            DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
        WriteDevicePropertyRequest request = new WriteDevicePropertyRequest();
        request.setOrgId(device.getOrgId());
        request.setDeviceId(parseDeviceSn(device.getDeviceSn()));
        request.setPropertyMap(dto.getDeviceProperties());
        ServiceResponse<Boolean> res = iotDeviceInstanceService.writeDeviceProperty(request);
        ErrorContext errorContext = res.getErrorContext();
        if (errorContext != null){
            String message = errorContext.getMessage();
            if ("Unsupported property format".equals(message)){
                throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DATA_FORMAT_ERROR, DeviceErrorCode.DEVICE_DATA_FORMAT_ERROR.getMessage()));
            }
        }
        serviceResponse.setData(res.getData());
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<IotDeviceTypeCountDTO>> listDeviceTypeCount(Long orgId,
        List<Long> deviceIds) {
        ServiceResponse<List<IotDeviceTypeCountDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        QueryIotDeviceDTO dto = new QueryIotDeviceDTO();
        dto.setOrgId(orgId);
        dto.setDeviceIds(deviceIds);
        List<Device> iotDevices = iotDeviceManager.listDeviceByCondition(dto);
        if (CollectionUtils.isEmpty(iotDevices)) {
            return serviceResponse;
        }
        List<Integer> needFilterDevice = deviceTypePropertyManager
            .getDeviceTypeByPropertyKey(DeviceTypeConstants.NEED_SPLIT_IOT_DEVICE_LIST);
        boolean existVirtualDeviceType = Boolean.FALSE;
        if (CollectionUtils.isNotEmpty(needFilterDevice)) {
            for (Device device : iotDevices) {
                if (needFilterDevice.contains(device.getDeviceType())) {
                    existVirtualDeviceType = Boolean.TRUE;
                    break;
                }
            }
        }
        // 有虚拟设备逻辑：以关联的虚拟设备数量进行统计和展示
        if (existVirtualDeviceType) {
            iotDevices = iotDevices.stream()
                .filter(e-> !needFilterDevice.contains(e.getDeviceType())).collect(Collectors.toList());
        }
        Map<Integer, DeviceType> deviceTypMap = getDeviceTypeMap(iotDevices);
        Map<Integer, List<Device>> deviceTypeToDeviceMap = iotDevices.stream().collect(Collectors
            .groupingBy(Device::getDeviceType));
        List<Device> buttonDevices = deviceTypeToDeviceMap
            .get(com.moredian.magicube.common.enums.DeviceType.TURN_ON_OFF_ONE_BUTTON.getValue());
        List<IotDeviceTypeCountDTO> list = new ArrayList<>();
        for (int deviceType : deviceTypeToDeviceMap.keySet()) {
            IotDeviceTypeCountDTO iotDeviceTypeCountDTO = new IotDeviceTypeCountDTO();
            if (com.moredian.magicube.common.enums.DeviceType.TURN_ON_OFF_ONE_BUTTON.getValue() == deviceType) {
                continue;
            }
            iotDeviceTypeCountDTO.setDeviceType(deviceType);
            DeviceType deviceTypeInfo = deviceTypMap.get(deviceType);
            if (deviceTypeInfo != null) {
                iotDeviceTypeCountDTO.setDeviceTypeName(deviceTypeInfo.getDeviceTypeName());
            }
            List<Device> devices = deviceTypeToDeviceMap.get(deviceType);
            if (CollectionUtils.isNotEmpty(devices)) {
                iotDeviceTypeCountDTO.setDeviceCount(devices.size());
                if (com.moredian.magicube.common.enums.DeviceType.SWITCH.getValue()
                    == deviceType && CollectionUtils.isNotEmpty(buttonDevices)){
                    iotDeviceTypeCountDTO.setDeviceCount(iotDeviceTypeCountDTO.getDeviceCount() + buttonDevices.size());
                }
            }
            list.add(iotDeviceTypeCountDTO);
        }
        serviceResponse.setData(list);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> checkUsableIotDevice(Long orgId, List<Long> deviceIds,
        Integer modeType) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        List<Device> devices = deviceManager.listByOrgIdAndIds(orgId, deviceIds);
        if (CollectionUtils.isEmpty(devices)) {
            serviceResponse.setData(Boolean.FALSE);
            return serviceResponse;
        }
        List<RuleTemplate> ruleTemplates = ruleTemplateManager.listByModeType(modeType);
        if (CollectionUtils.isEmpty(ruleTemplates)) {
            log.info("Iot规则模板未初始化orgId:{},deviceIds:{},modeType:{}",orgId,deviceIds,modeType);
            serviceResponse.setData(Boolean.FALSE);
            return serviceResponse;
        }
        for (Device device : devices) {
            for (RuleTemplate ruleTemplate : ruleTemplates) {
                if (StringUtils.isNotBlank(ruleTemplate.getTriggerDeviceTypeProperty())) {
                    List<Integer> triggerDeviceTypes = deviceTypePropertyManager
                        .getDeviceTypeByPropertyKey(ruleTemplate.getTriggerDeviceTypeProperty());
                    if (triggerDeviceTypes.contains(device.getDeviceType())) {
                        serviceResponse.setData(Boolean.TRUE);
                        return serviceResponse;
                    }
                }
                if (StringUtils.isNotBlank(ruleTemplate.getImplementDeviceTypeProperty())) {
                    List<Integer> implementDeviceTypes = deviceTypePropertyManager
                        .getDeviceTypeByPropertyKey(ruleTemplate.getImplementDeviceTypeProperty());
                    if (implementDeviceTypes.contains(device.getDeviceType())) {
                        serviceResponse.setData(Boolean.TRUE);
                        return serviceResponse;
                    }
                }
            }
        }
        serviceResponse.setData(Boolean.FALSE);
        return serviceResponse;
    }


    @Override
    public ServiceResponse<List<Properties>> latestDeviceProperty(Long orgId, String deviceSn) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        ServiceResponse<List<Properties>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<IotDevicePropertyInfoDTO> propertyInfoDTOS = iotDevicePropertyService.getLatestDevicePropertyListByDeviceId(
            orgId, deviceSn).pickDataThrowException();
        serviceResponse.setData(getPropertyResList(propertyInfoDTOS));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<IotDeviceDTO>> listIotDeviceByIotDeviceId(Long orgId,
        Long iotDeviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(iotDeviceId, "deviceId must not be null");
        ServiceResponse<List<IotDeviceDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        //根据iotDeviceSn查询所在空间Id
        TreeDeviceRelationDTO relationDTO = treeDeviceRelationService.getByOrgIdAndDeviceId(orgId
            , String.valueOf(iotDeviceId)).pickDataThrowException();
        if (relationDTO == null) {
            return serviceResponse;
        }
        //根据空间Id查询所有IOT设备列表
        List<TreeDeviceRelationDTO> iotDeviceRelation = treeDeviceRelationService
            .listByTreeIdAndSource(orgId, relationDTO.getTreeId(),1)
            .pickDataThrowException();
        if (iotDeviceRelation == null) {
            return serviceResponse;
        }
        List<Long> deviceIds = iotDeviceRelation.stream().map(TreeDeviceRelationDTO::getDeviceId)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceIds)) {
            return serviceResponse;
        }
        //查询IOT信息列表
        PageQueryIotDeviceDTO dto = new PageQueryIotDeviceDTO();
        dto.setOrgId(orgId);
        dto.setDeviceIds(deviceIds);
        dto.setPageNo(0);
        dto.setPageSize(1000);
        dto.setSearchProperties(YesNoFlag.YES.getValue());
        dto.setVirtualFlag(YesNoFlag.YES.getValue());
        List<IotDeviceDTO> devices = this.listByConditionPage(dto)
            .pickDataThrowException().getData();
        if (CollectionUtils.isEmpty(devices)) {
            return serviceResponse;
        }
        serviceResponse.setData(devices);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deviceFunctionInvoke(DeviceFunctionInvokeDTO dto) {
        String deviceSn = dto.getDeviceSn();
        String functionId = dto.getFunctionId();
        BizAssert.notBlank(deviceSn, "deviceSn must be not null");
        BizAssert.notBlank(functionId, "functionId must be not null");
        Device device = deviceManager.getByDeviceSn(deviceSn);

        DeviceFunctionInvokeRequest request = new DeviceFunctionInvokeRequest();
        request.setDeviceId(deviceSn);
        request.setFunctionId(dto.getFunctionId());
        request.setParams(dto.getParams());
        ServiceResponse<Boolean> res = iotDeviceInstanceService.deviceFunctionInvoke(device.getOrgId(), request);
        ErrorContext errorContext = res.getErrorContext();
        if (errorContext != null){
            String message = errorContext.getMessage();
            if ("Unsupported property format".equals(message)){
                throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_FUNCTION_NOT_SUPPORT, DeviceErrorCode.DEVICE_FUNCTION_NOT_SUPPORT.getMessage()));
            }
        }
        return res;
    }


    /**
     * 对象转换
     *
     * @param devices 设备信息列表
     * @return
     */
    private List<IotDeviceDTO> deviceToIotDeviceInfoDTO(List<Device> devices) {
        List<IotDeviceDTO> deviceInfoDTOs = new ArrayList<>();
        Long orgId = devices.get(0).getOrgId();
        Map<Long, TreeDeviceRelationDTO> map = getDeviceRelationMap(devices, orgId);

        List<String> parentDeviceSn = devices.stream().map(Device::getDeviceSn)
            .filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        // 根据设备 deviceSn 查询子设备(需要排除虚拟设备)
        List<Device> subDevice = deviceManager.listByOrgIdAndParentDeviceSns(orgId, parentDeviceSn);
        subDevice = subDevice.stream().filter(e-> ObjectUtil.isEmpty(e.getVirtualFlag())
                || e.getVirtualFlag() == YesNoFlag.NO.getValue())
            .collect(Collectors.toList());
        Map<String, List<Device>> subDeviceMap = subDevice.stream()
            .collect(Collectors.groupingBy(Device::getParentDeviceSn));

        Map<String, DeviceState> deviceSnToDeviceState = getIotDeviceStateMap(devices, orgId);

        for (Device device : devices) {
            String deviceSn;
            if (deviceTypePropertyService.containsDeviceType(DeviceTypeConstants.
                NEED_SPLIT_IOT_DEVICE_LIST, device.getDeviceType()).pickDataThrowException()) {
                deviceSn = device.getDeviceSn().split(DeviceIotSplitConstants.UNDERLINE)[0];
            } else {
                deviceSn = device.getDeviceSn();
            }
            IotDeviceDTO iotDeviceDTO = new IotDeviceDTO();
            BeanUtils.copyProperties(device, iotDeviceDTO);
            device.setDeviceSn(deviceSn);
            if (device.getActiveTime() != null) {
                iotDeviceDTO.setActiveTime(device.getActiveTime().getTime());
            }
            if (StringUtils.isNotBlank(device.getThirdDeviceId())) {
                iotDeviceDTO.setTpId(device.getThirdDeviceId());
            }

            //填充空间信息
            TreeDeviceRelationDTO relationDTO = map.get(device.getDeviceId());
            if (relationDTO != null) {
                iotDeviceDTO.setTreeId(relationDTO.getTreeId());
                if (StringUtils.isNotBlank(relationDTO.getPathTreeName())) {
//                    iotDeviceDTO.setPathTreeName(relationDTO.getPathTreeName().replace("/", ""));
                    iotDeviceDTO.setPathTreeName(relationDTO.getPathTreeName());
                }
            }

            //填充设备在离线信息
            DeviceState deviceState = deviceSnToDeviceState.get(deviceSn);
            if (deviceState != null) {
                iotDeviceDTO
                    .setOnline(deviceState.getValue().equals(IotDeviceStatusEnums.online.getMsg()));
            }
            iotDeviceDTO.setPosition(device.getPosition() != null ? device.getPosition() : "");
            // 设备类型
            buildDeviceTypeInfo(device, iotDeviceDTO);
            List<Device> subList = subDeviceMap.getOrDefault(device.getDeviceSn(), Lists.newArrayList());
            // 判断是否子设备
            iotDeviceDTO.setExistSubDevice(ObjectUtil.isNotEmpty(subList));
            iotDeviceDTO.setSubDeviceNum(subList.size());
            // 判断是否是虚拟设备
            iotDeviceDTO.setVirtualDeviceFlag(ObjectUtil.isNotEmpty(device.getVirtualFlag())
                && YesNoFlag.YES.getValue() == device.getVirtualFlag());
            deviceInfoDTOs.add(iotDeviceDTO);
        }
        return deviceInfoDTOs;
    }

    private Map<Integer, DeviceType> getDeviceTypeMap(List<Device> iotDevices) {
        List<Integer> deviceTypes = iotDevices.stream().map(Device::getDeviceType)
            .distinct().collect(Collectors.toList());
        List<DeviceType> deviceTypeInfos = deviceTypeManager.listByTypes(deviceTypes);
        Map<Integer, DeviceType> deviceTypMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceTypeInfos)){
            deviceTypMap = deviceTypeInfos.stream().collect(Collectors
                .toMap(DeviceType::getDeviceType, x -> x));
        }
        return deviceTypMap;
    }

    private void buildSpaceInfo(Long orgId, Device device, IotDeviceDTO iotDeviceDTO) {
        TreeDeviceRelationDTO relationDTO = treeDeviceRelationService
                .getByOrgIdAndDeviceId(orgId, String.valueOf(device.getDeviceId()))
                .pickDataThrowException();
        if (relationDTO != null) {
            iotDeviceDTO.setTreeId(relationDTO.getTreeId());
            iotDeviceDTO.setSpaceId(relationDTO.getSpaceId());
            iotDeviceDTO.setPath(relationDTO.getPath());
            iotDeviceDTO.setTreeName(iotDeviceDTO.getTreeName());
            if (StringUtils.isNotBlank(relationDTO.getPathTreeName())) {
                String path = iotDeviceDTO.getPath();
                if ("0".equals(path)) {
                    iotDeviceDTO.setPathTreeName(relationDTO.getPathTreeName());
                }else {
                    iotDeviceDTO.setPathTreeName(
                            relationDTO.getPathTreeName() + "/" + relationDTO.getTreeName());
                }
            }
        }
    }

    private void buildDeviceStateAndMetadata(Long orgId, Device device, IotDeviceDTO iotDeviceDTO) {
        String deviceSn = parseDeviceSn(device);
        // 查询设备状态信息
        IotDeviceInfoDTO iotDevice = iotDeviceInstanceService.getDeviceInstanceDetail(orgId
            , deviceSn).pickDataThrowException();
        if (ObjectUtil.isNotEmpty(iotDevice)) {
            iotDeviceDTO.setOnline(IotDeviceStatusEnums.online.getMsg()
                .equals(iotDevice.getState().value));
        }
        if (iotDevice != null) {
            buildPropertyConfig(iotDevice, iotDeviceDTO, device.getDeviceType());
        }
    }


    /**
     * 组装设备属性前端展示样式
     */
    private void buildPropertyConfig(IotDeviceInfoDTO iotDevice, IotDeviceDTO iotDeviceDTO,
        Integer deviceType) {
        String metadata = iotDevice.getMetadata();
        if (ObjectUtil.isEmpty(metadata)){
            return;
        }
        String properties = JSONObject.parseObject(metadata).getString("properties");
        if(ObjectUtil.isNotEmpty(properties)) {
            List<DeviceMetadataExtend> metadataExtends = JSON.parseArray(properties,
                DeviceMetadataExtend.class);

            // 查询缓存的属性配置并按照propertyKey分组
            String key = RedisConstants.getKey(RedisConstants.DEVICE_TYPE_PROPERTY_CONFIG,
                deviceType);

            List<DeviceTypePropertyConfig> configList = (List<DeviceTypePropertyConfig>)
                redissonCacheComponent.getObjectCache(key);
            Map<String, DeviceTypePropertyConfig> configMap = Maps.newHashMap();
            if(ObjectUtil.isNotEmpty(configList)){
                configMap = configList.stream()
                    .collect(Collectors.toMap(DeviceTypePropertyConfig::getPropertyKey,
                        Function.identity()));
            }

            List<DeviceMetadataExtend> resultList = Lists.newArrayList();

            // 设备已上报的物模型数据
            List<IotDevicePropertyDTO> upProperties = iotDeviceDTO.getDeviceProperties();
            Map<String, String> upPropertiesMap = upProperties.stream()
                .collect(Collectors.toMap(e1 -> e1.getData().getValue().getProperty(),
                    e2 -> e2.getData().getValue().getValue()));

            List<String> readKeys = Lists.newArrayList("PAP_R", "PAP_R1", "PAP_R2", "PAP_R3", "PAP_R4");

            // 获取设备上报最新的时间，计算读数时间
            Long time = upProperties.stream().filter(e-> {
                    DataObject data = e.getData();
                    Properties value = data.getValue();
                    String property = value.getProperty();
                    return readKeys.contains(property);
                }).map(e -> e.getData().getTimestamp()).max(Long::compare)
                .orElse(null);
            String timeStr = getReadTimeStr(time);
            upPropertiesMap.put("time", timeStr);

            // 组装数据
            for (DeviceMetadataExtend metadataExtend : metadataExtends) {
                String propertyKey = metadataExtend.getId();
                DeviceTypePropertyConfig config = configMap.get(propertyKey);
                if (ObjectUtil.isEmpty(config) || Boolean.FALSE.equals(config.getVisible())){
                    continue;
                }
                String propertyJson = config.getPropertyJson();
                // 虚拟设备物模型名称去掉数字
                String newName = metadataExtend.getName().replaceAll("\\d+", StringUtils.EMPTY);
                metadataExtend.setName(newName);

                // 如果存在特殊变量需要替换
                if (ObjectUtil.isNotEmpty(propertyJson) && propertyJson.contains("#")){
                        ExpressionParser parser = new SpelExpressionParser();
                        TemplateParserContext parserContext = new TemplateParserContext();
                        propertyJson = parser.parseExpression(propertyJson,
                            parserContext).getValue(upPropertiesMap, String.class);
                }
                metadataExtend.setPropertyConfig(JSONObject.parseObject(propertyJson));
                metadataExtend.setSort(config.getSort());
                resultList.add(metadataExtend);
            }
            resultList.sort(Comparator.comparingInt(DeviceMetadataExtend::getSort));

            Map<String, Object> result = Maps.newHashMap();
            result.put("properties", resultList);
            // 设备物模型元数据
            iotDeviceDTO.setMetadata(JSON.toJSONString(result));
        }

    }


    /**
     *最小时间颗粒︰分钟
     * 1分钟内显示:刚刚
     * 1小时内显示︰N分钟前
     * 1~2小时:1小时前
     * 2~3小时显示∶2小时前以此类推到显示:23小时前
     * 超过1天～2天显示:1天前
     * 超过2天～3天显示:2天前超过3天显示:未知
     * @param time
     * @return
     */
    private String getReadTimeStr(Long time) {
        if(time == null){
            return "未知";
        }

        long minute = 60 * 1000L;
        long hour = 60 * 60 * 1000L;
        long day = 24 * 60 * 60 * 1000L;

        long ago = System.currentTimeMillis() - time;
        if (ago <= minute){
            return "刚刚";
        } else if (ago <= hour) {
            return (int) Math.floor(ago / 60.0 / 1000.0) + "分钟前";
        } else if (ago <= day) {
            return (int) Math.floor(ago / 60.0 / 60.0 / 1000.0) + "小时前";
        } else if (ago <= day * 3) {
            return (int) Math.floor(ago / 24.0 / 60.0 / 60.0 / 1000.0) + "天前";
        }else {
            return "未知";
        }
    }




    private void buildParentDeviceInfo(Device device, IotDeviceDTO iotDeviceDTO) {
        if (StringUtils.isNotBlank(device.getParentDeviceSn())) {
            DeviceInfoDTO deviceInfoDTO = deviceService.getByDeviceSn(device.getParentDeviceSn())
                .pickDataThrowException();
            if (deviceInfoDTO != null) {
                iotDeviceDTO.setParentDeviceSn(deviceInfoDTO.getDeviceSn());
                iotDeviceDTO.setParentDeviceId(deviceInfoDTO.getDeviceId());
                iotDeviceDTO.setParentPathTreeName(deviceInfoDTO.getPathTreeName());
                iotDeviceDTO.setParentPosition(deviceInfoDTO.getPosition());
            }
        }
    }

    private void buildVirtualFlag(Device device, IotDeviceDTO iotDeviceDTO) {
        if (device == null) {
            return;
        }
        if (device.getVirtualFlag() == null) {
            iotDeviceDTO.setVirtualDeviceFlag(Boolean.FALSE);
        } else {
            iotDeviceDTO.setVirtualDeviceFlag(device.getVirtualFlag()
                .equals(YesNoFlag.YES.getValue()));
        }
    }

    private void buildDeviceTypeInfo(Device device, IotDeviceDTO iotDeviceDTO) {
        iotDeviceDTO.setDeviceTypeName(deviceTypeManager.getName(device.getDeviceType()));
    }


    private void buildSubDevice(Long orgId, Device device, IotDeviceDTO iotDeviceDTO) {
        List<Device> subDevice = deviceManager.listByOrgIdAndParentDeviceSns(orgId,
            Lists.newArrayList(device.getDeviceSn()));
        if (CollectionUtils.isNotEmpty(subDevice)) {
            List<Long> deviceIds = subDevice.stream().map(Device::getDeviceId).distinct()
                .collect(Collectors.toList());
            List<DeviceInfoDTO> deviceInfos = deviceService.listByOrgIdAndIds(orgId,
                deviceIds).pickDataThrowException();

            // 查询子设备魔链在离线状态
            QueryDeviceListRequest request = new QueryDeviceListRequest();
            request.setOrgId(orgId);
            List<String> deviceSn = subDevice.stream().map(this::parseDeviceSn)
                .collect(Collectors.toList());
            request.setIds(deviceSn);
            List<IotDeviceInfoDTO> iotDeviceInfoDTOS = iotDeviceInstanceService.queryDeviceInfoListByCondition(
                request).pickDataThrowException();
            Map<String, DeviceState> deviceStateMap = iotDeviceInfoDTOS.stream()
                .collect(Collectors.toMap(IotDeviceInfoDTO::getId, IotDeviceInfoDTO::getState));

            for (DeviceInfoDTO deviceInfo : deviceInfos) {
                String sn = parseDeviceSn(deviceInfo.getDeviceSn());
                deviceInfo.setOnline(IotDeviceStatusEnums.online.getMsg()
                    .equals(deviceStateMap.get(sn).getValue()));
            }

            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                iotDeviceDTO.setSubDevices(deviceInfos);
            }
        }
    }

    private String parseDeviceSn(String deviseSn) {
        return deviseSn.split(DeviceIotSplitConstants.UNDERLINE)[0];
    }

    private String parseDeviceSn(Device device) {
        Integer virtualFlag = device.getVirtualFlag();
        String deviceSn = device.getDeviceSn();
        // 如果是虚拟设备，需要处理设备的deviceSN
        if (virtualFlag != null && virtualFlag == YesNoFlag.YES.getValue()) {
            deviceSn = parseDeviceSn(deviceSn);
        }
        return deviceSn;
    }

    private void buildDeviceProperties(Long orgId, String deviceSn, IotDeviceDTO iotDeviceDTO) {
        String sn = parseDeviceSn(deviceSn);
        List<IotDevicePropertyInfoDTO> properties = iotDevicePropertyService
            .getLatestDevicePropertyListByDeviceId(orgId, sn).pickDataThrowException();
        List<IotDevicePropertyDTO> iotDeviceProperties = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(properties)) {
            for (IotDevicePropertyInfoDTO property : properties) {
                // 如果是虚拟设备只保存虚拟设备对应的属性，sn_suffix
                Boolean virtualDeviceFlag = iotDeviceDTO.getVirtualDeviceFlag();
                if (!ObjectUtils.isEmpty(virtualDeviceFlag) && virtualDeviceFlag){
                    String[] split = deviceSn.split("_");
                    if (split.length<2){
                        continue;
                    }else {
                        String snSuffix = split[1];
                        if (!property.getProperty().contains(snSuffix)){
                            continue;
                        }
                    }
                }
                IotDevicePropertyDTO iotDeviceProperty = new IotDevicePropertyDTO();
                DataObject dataObject = new DataObject();
                Properties tempProperties = new Properties();
                BeanUtils.copyProperties(property, tempProperties);
                tempProperties.setDeviceSn(property.getDeviceId());
                dataObject.setValue(tempProperties);
                Long timestamp = property.getTimestamp();
                if (timestamp != null){
                    dataObject.setTimestamp(timestamp);
                    String timeString = DateFormatUtils.format(timestamp, "yyyy-MM-dd HH:mm:ss");
                    dataObject.setTimeString(timeString);
                }
                iotDeviceProperty.setData(dataObject);
                iotDeviceProperties.add(iotDeviceProperty);
            }
        }
        iotDeviceDTO.setDeviceProperties(iotDeviceProperties);

        // 如果是查询的是电表、水表，需要将倍率、开合匝取出来
        if (com.moredian.magicube.common.enums.DeviceType.ENERGY_METER.getValue() == iotDeviceDTO.getDeviceType()
        || com.moredian.magicube.common.enums.DeviceType.WATER_METER.getValue() == iotDeviceDTO.getDeviceType()){
            getRateAndOCST(iotDeviceDTO);
        }
    }


    private void getRateAndOCST(IotDeviceDTO iotDeviceDTO){
        List<IotDevicePropertyDTO> meterProperties = iotDeviceDTO.getDeviceProperties();
        Map<String, String> propertiesMap = meterProperties.stream().map(e -> e.getData().getValue())
            .collect(Collectors.toMap(Properties::getProperty, Properties::getValue));
        String rate = propertiesMap.getOrDefault("rate", "1");
        String OC_ST = propertiesMap.getOrDefault("OC_ST", "0");
        iotDeviceDTO.setRate(Double.parseDouble(rate));
        iotDeviceDTO.setMeterSwitch(Integer.parseInt(OC_ST));
    }


    private List<Properties> getPropertyResList(List<IotDevicePropertyInfoDTO> properties){
        List<Properties> iotDeviceProperties = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(properties)) {
            for (IotDevicePropertyInfoDTO property : properties) {
                Properties tempProperties = new Properties();
                BeanUtils.copyProperties(property, tempProperties);
                tempProperties.setDeviceSn(property.getDeviceId());
                iotDeviceProperties.add(tempProperties);
            }
        }
        return iotDeviceProperties;
    }


    /**
     * 获取设备Id和空间关系
     *
     * @param devices
     * @param orgId
     * @return
     */
    private Map<Long, TreeDeviceRelationDTO> getDeviceRelationMap(List<Device> devices,
        Long orgId) {
        List<String> deviceIds = devices.stream().map(device -> String.valueOf(device
            .getDeviceId())).collect(Collectors.toList());
        Map<Long, TreeDeviceRelationDTO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            List<TreeDeviceRelationDTO> relationDTOS = treeDeviceRelationService
                .listByDeviceIds(orgId, deviceIds).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOS)) {
                map = relationDTOS.stream().collect(Collectors.
                    toMap(TreeDeviceRelationDTO::getDeviceId, Function.identity()));
            }
        }
        return map;
    }

    /**
     * 获取iot设备sn和设备状态map
     *
     * @param devices
     * @param orgId
     * @return
     */
    private Map<String, DeviceState> getIotDeviceStateMap(List<Device> devices, Long orgId) {
        Set<String> deviceSns = devices.stream().map(this::parseDeviceSn).collect(Collectors.toSet());
        QueryDeviceListRequest request = new QueryDeviceListRequest();
        request.setOrgId(orgId);
        request.setIds(Lists.newArrayList(deviceSns));
        List<IotDeviceInfoDTO> list = iotDeviceInstanceService.
            queryDeviceInfoListByCondition(request).pickDataThrowException();
        Map<String, DeviceState> deviceSnToDeviceState = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            deviceSnToDeviceState = list.stream().collect(Collectors
                .toMap(IotDeviceInfoDTO::getId, IotDeviceInfoDTO::getState));
        }
        return deviceSnToDeviceState;
    }
}