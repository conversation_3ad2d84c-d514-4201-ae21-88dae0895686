package com.moredian.magicube.device.manager.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceInstance.req.QueryDeviceListRequest;
import com.moredian.bridge.api.service.deviceProperty.IIotDevicePropertyService;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyDefineDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyInfoDTO;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.core.enums.CommonErrorCode;
import com.moredian.magicube.device.constant.DeviceIotSplitConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dto.device.iot.IotDeviceListDTO;
import com.moredian.magicube.device.enums.IotDeviceStatusEnums;
import com.moredian.magicube.device.manager.DeviceTypeManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description iot设备管理辅助类
 * @create 2024-12-30 17:53
 */
@Slf4j
@Component
public class IotDeviceManagerHelper {

    @Resource
    private Executor iotDevicePagePool;
    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;
    @Resource
    private DeviceTypeManager deviceTypeManager;

    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;
    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;
    @SI
    private IIotDevicePropertyService iotDevicePropertyService;

    /**
     * 格式化 IOT 虚拟设备的sn，以下划线结尾，第一个元素
     */
    public String parseIotDeviceSn(String deviceSn) {
        BizAssert.notBlank(deviceSn, "deviceSn must not be blank");
        String[] split = deviceSn.split(DeviceIotSplitConstants.UNDERLINE);
        return split[0];
    }


    /**
     * 批量获取iot在魔链定义的物模型
     */
    public Map<String, List<IotDevicePropertyDefineDTO>> getDeviceDefineProperty(Long orgId, List<String> deviceSn) {
        Map<String, List<IotDevicePropertyDefineDTO>> res = new HashMap<>();
        if (CollectionUtil.isNotEmpty(deviceSn)) {
            deviceSn = deviceSn.stream().map(this::parseIotDeviceSn).collect(Collectors.toList());
            List<BatchIotDevicePropertyInfoDTO> iotDeviceProperty = iotDevicePropertyService
                    .batchGetDevicePropertyDefineList(orgId, deviceSn).pickDataThrowException();
            if (CollectionUtil.isNotEmpty(iotDeviceProperty)) {
                res = iotDeviceProperty.stream().collect(Collectors.toMap(BatchIotDevicePropertyInfoDTO::getId,
                        BatchIotDevicePropertyInfoDTO::getProperties));
            }
        }
        return res;
    }


    /**
     * 获取设备Id和空间关系
     */
    public Map<Long, TreeDeviceRelationDTO> getDeviceTreeRelation(Long orgId, List<Long> deviceIds) {
        Map<Long, TreeDeviceRelationDTO> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            List<String> deviceIdsStr = deviceIds.stream().map(String::valueOf).distinct().collect(Collectors.toList());
            List<TreeDeviceRelationDTO> relationDTOS = spaceTreeDeviceRelationService.listByDeviceIds(orgId, deviceIdsStr)
                    .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationDTOS)) {
                map = relationDTOS.stream().collect(Collectors.toMap(TreeDeviceRelationDTO::getDeviceId, Function.identity()));
            }
        }
        return map;
    }


    /**
     * 获取iot设备sn和设备状态map
     */
    public Map<String, Boolean> getIotDeviceStateMap(Long orgId, List<String> deviceSns) {
        deviceSns = deviceSns.stream().map(this::parseIotDeviceSn).distinct().collect(Collectors.toList());
        QueryDeviceListRequest request = new QueryDeviceListRequest();
        request.setOrgId(orgId);
        request.setIds(deviceSns);

        List<IotDeviceInfoDTO> list = iotDeviceInstanceService.queryDeviceInfoListByCondition(request).pickDataThrowException();
        Map<String, Boolean> deviceSnToDeviceState = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(IotDeviceInfoDTO::getId,
                    e-> IotDeviceStatusEnums.online.getMsg().equals(e.getState().value)));
        }
        return deviceSnToDeviceState;
    }


    /**
     * 转换设备为列表出参
     */
    public IotDeviceListDTO deviceConvertIotDeviceList(Device device) {
        Integer virtualFlag = device.getVirtualFlag();
        List<Integer> iotDeviceType = deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST);
        List<DeviceType> deviceTypes = deviceTypeManager.listByTypes(iotDeviceType);
        Map<Integer, String> deviceTypeMap = deviceTypes.stream().collect(Collectors.toMap(DeviceType::getDeviceType, DeviceType::getDeviceTypeName));
        return IotDeviceListDTO.builder()
                .deviceId(device.getDeviceId())
                .deviceSn(device.getDeviceSn())
                .orgId(device.getOrgId())
                .virtualFlag(virtualFlag != null && virtualFlag == YesNoFlag.YES.getValue())
                .deviceType(device.getDeviceType())
                .deviceTypeName(deviceTypeMap.getOrDefault(device.getDeviceType(), StringUtil.EMPTY_STRING))
                .build();
    }


    /**
     * 获取魔链设备的最新属性，并将数据根据值转换成对应的对象
     * (需要将对象的字段与魔链上的物模型相对应)，不支持复杂类型
     */
    public <T> T getIotDeviceLastProperties(Long orgId, String deviceSn, Class<T> tClass) {
        BizAssert.notBlank(deviceSn, "deviceSn must not be blank");
        BizAssert.notNull(orgId, "orgId must not be blank");
        BizAssert.notNull(tClass, "tClass must not be blank");

        List<IotDevicePropertyInfoDTO> iotProperties = iotDevicePropertyService
                .getLatestDevicePropertyListByDeviceId(orgId, deviceSn).pickDataThrowException();

        if (CollectionUtil.isNotEmpty(iotProperties)) {
            Map<String, String> iotPropertyMap = iotProperties.stream()
                    .collect(Collectors.toMap(IotDevicePropertyInfoDTO::getProperty, IotDevicePropertyInfoDTO::getFormatValue));
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(iotPropertyMap, tClass);
        }

        try {
            return tClass.newInstance();
        }catch (Exception e) {
            log.error("反射创建对象失败,[IotDeviceManagerHelper.getIotDeviceLastProperties],[{}]", e.getMessage());
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_UNKNOWN_ERROR, DeviceErrorCode.DEVICE_UNKNOWN_ERROR.getMessage()));
        }
    }


    /**
     * 获取IOT设备最新属性（异步批量获取）
     */
    public Map<String, List<IotDevicePropertyInfoDTO>> getIotDeviceLastProperties(Long orgId, List<String> deviceSn) {
        Map<String, List<IotDevicePropertyInfoDTO>> iotPropertyMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(deviceSn)) {
            deviceSn = deviceSn.stream().map(this::parseIotDeviceSn).distinct().collect(Collectors.toList());
            try {
                List<CompletableFuture<List<IotDevicePropertyInfoDTO>>> futureList = new ArrayList<>();
                for (String sn : deviceSn) {
                    // 异步查询设备属性
                    CompletableFuture<List<IotDevicePropertyInfoDTO>> future = CompletableFuture.supplyAsync(
                    () -> iotDevicePropertyService.getLatestDevicePropertyListByDeviceId(orgId, sn).pickDataThrowException(),
                    iotDevicePagePool).exceptionally(ex-> {
                        log.error("获取IOT设备最新属性异常,sn=>{},msg=>{}", sn, ex.getMessage());
                        return new ArrayList<>();
                    });
                    futureList.add(future);
                }
                CompletableFuture<Void> cfAll = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
                // 等待所有异步任务完成
                cfAll.get();
                // 收集返回结果
                for (CompletableFuture<List<IotDevicePropertyInfoDTO>> future : futureList) {
                    List<IotDevicePropertyInfoDTO> item = future.get();
                    if (CollectionUtil.isNotEmpty(item)) {
                        IotDevicePropertyInfoDTO propertyInfoDTO = item.get(0);
                        iotPropertyMap.put(propertyInfoDTO.getDeviceId(), item);
                    }
                }
            } catch (ExecutionException | InterruptedException e) {
                log.error("获取IOT设备最新属性异常，msg=>{}", e.getMessage());
            }
        }
        return iotPropertyMap;
    }
}
