package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.DisableDingOrgMsg;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.service.ActivityService;
import java.util.List;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrgMsgSubscriber {

    private Logger logger = LoggerFactory.getLogger(OrgMsgSubscriber.class);

    @Autowired
    private ActivityService activityService;

    @Autowired
    private DeviceManager deviceManager;

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @Subscribe
    public void subscribeDisableOrgMsg(DisableDingOrgMsg msg) {
        logger.info("subscribe disable ding org msg,msg:{}", msg);
        // 钉钉企业解散后，同时解绑钉钉相关的智能硬件
        List<Device> devices = deviceManager.listByOrgId(msg.getOrgId());
        if (CollectionUtils.isNotEmpty(devices)) {
            for (Device device : devices) {
                try {
                    // 发送解绑命令
                    String deviceSn = device.getDeviceSn();
                    TransferMessageInfo<String> transferMessageInfo = new TransferMessageInfo<>();
                    // 祥见TransferEventType.DEVICE_UNBIND定义
                    transferMessageInfo.setEventType(141);
                    transferMessageInfo.setSeverity(3);
                    transferMessageInfo
                        .setSeqId(com.moredian.bee.common.utils.UUID.random19() + "UNBIND");
                    transferMessageInfo.setMessage("UNBIND");
                    transferMessageInfo.setData("deviceSn");
                    String jsonStr = JsonUtils.toJson(transferMessageInfo);
                    String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
                    TransferRequest transferRequest = new TransferRequest();
                    transferRequest.setSerialNumber(deviceSn);
                    transferRequest.setBody(base64Message);
                    transferRequest.setCommand(0);
                    TransferResponse result = hubControlServiceV1.transfer(transferRequest)
                        .pickDataThrowException();
                    logger.debug("unbind org[{}] device[{}],send device msg {}", msg.getOrgId(),
                        device.getDeviceSn(), device.getDeviceId(), result.getSeqId());
                } catch (Exception e) {
                    logger.error("unbind org[" + msg.getOrgId() + "] device[" + device.getDeviceSn()
                        + "] send device msg fail.", e);
                }

                try {
                    Boolean result = activityService.unbindDevice(device.getDeviceSn()).pickDataThrowException();
                    logger.info("unbind org[{}] device[{}]{}", msg.getOrgId(), device.getDeviceSn(),
                        device.getDeviceId(), result);
                } catch (Exception e) {
                    logger.error("unbind org[" + msg.getOrgId() + "] device[" + device.getDeviceSn()
                        + "] fail.", e);
                }
            }
        }
    }
}