package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.peripherals.InsertPeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.QueryPeripheralsDTO;
import com.moredian.magicube.device.dto.peripherals.UpdatePeripheralsDTO;
import com.moredian.magicube.device.manager.PeripheralsManager;
import com.moredian.magicube.device.service.PeripheralsService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */

@SI
public class PeripheralsServiceImpl implements PeripheralsService {

    @Autowired
    private PeripheralsManager peripheralsManager;

    @Override
    public ServiceResponse<Boolean> insert(InsertPeripheralsDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.insert(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> update(UpdatePeripheralsDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.update(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<PeripheralsDTO>> listByCondition(QueryPeripheralsDTO dto) {
        ServiceResponse<List<PeripheralsDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.listByCondition(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<PeripheralsDTO>> listByOrgIdAndDeviceSn(Long orgId, String deviceSn) {
        ServiceResponse<List<PeripheralsDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.listByOrgIdAndDeviceSn(orgId, deviceSn));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> disConnect(UpdatePeripheralsDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.disConnect(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> mobileConnect(UpdatePeripheralsDTO dto) {
        return peripheralsManager.mobileConnect(dto);
    }

    @Override
    public ServiceResponse<Boolean> mobileDisConnect(UpdatePeripheralsDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.mobileDisConnect(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> existHistoryPeripherals(Long orgId) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.existHistoryPeripherals(orgId));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<PeripheralsDTO>> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns) {
        ServiceResponse<List<PeripheralsDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsManager.listByOrgIdAndDeviceSns(orgId, deviceSns));
        return serviceResponse;
    }
}
