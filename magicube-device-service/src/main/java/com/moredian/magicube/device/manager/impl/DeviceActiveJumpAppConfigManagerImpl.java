package com.moredian.magicube.device.manager.impl;

import com.moredian.magicube.device.dao.entity.DeviceActiveJumpAppConfig;
import javax.annotation.Resource;
import com.moredian.magicube.device.dao.mapper.DeviceActiveJumpAppConfigMapper;
import com.moredian.magicube.device.manager.DeviceActiveJumpAppConfigManager;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.idgenerator.service.IdgeneratorService;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * com.moredian.magicube.devicemanagerImpl
 * <AUTHOR>
 */
 
@Service
public class DeviceActiveJumpAppConfigManagerImpl implements DeviceActiveJumpAppConfigManager {

	@SI
	private IdgeneratorService idgeneratorService;
	
	@Resource
	private DeviceActiveJumpAppConfigMapper deviceActiveJumpAppConfigMapper;
	
    
    @Override
    public List<DeviceActiveJumpAppConfig> listAll(){
    	return deviceActiveJumpAppConfigMapper.listAll();
    }
    
     @Override
    public DeviceActiveJumpAppConfig getById(Long id){
    	return deviceActiveJumpAppConfigMapper.getById(id);
    }



	@Override
	public DeviceActiveJumpAppConfig getByAppCode(String appCode) {
		return null;
	}

	@Override
	public Long insert(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig) {
		ServiceResponse<Long> serviceResponse = idgeneratorService.getNextIdByTypeName("com.moredian.magicube.device.entity.DeviceActiveJumpAppConfig");
		Long id = null;
        if (serviceResponse.isSuccess()) {
        	id =  serviceResponse.getData();
			deviceActiveJumpAppConfig.setId(serviceResponse.getData());
			deviceActiveJumpAppConfigMapper.insert(deviceActiveJumpAppConfig);
		}
		return id;
	}
	
	@Override
	public int update(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig) {
		return deviceActiveJumpAppConfigMapper.update(deviceActiveJumpAppConfig);
	}
	
	@Override
	public int updateSelective(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig) {
		return deviceActiveJumpAppConfigMapper.updateSelective(deviceActiveJumpAppConfig);
	}
	
	@Override
	public int delete(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig) {
		return deviceActiveJumpAppConfigMapper.delete(deviceActiveJumpAppConfig);
	}
	
}
