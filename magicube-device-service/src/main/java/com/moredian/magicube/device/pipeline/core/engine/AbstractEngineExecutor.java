package com.moredian.magicube.device.pipeline.core.engine;

import com.moredian.bee.common.exception.BizException;
import com.moredian.magicube.device.pipeline.core.pipeline.InvocationChain;
import com.moredian.magicube.device.pipeline.core.pipeline.Pipeline;
import com.moredian.magicube.device.pipeline.core.pipeline.rollback.RollBack;
import com.moredian.magicube.device.pipeline.core.pipeline.success.Success;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 抽象引擎
 *
 * @param <T> 入参
 * @param <S> 上下文信息
 * @param <R> 接口返回值
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractEngineExecutor<T, S, R> implements InitializingBean {

    @Autowired
    protected Pipeline pipeline;

    /**
     * 通用参数校验
     *
     * @param t
     */
    protected abstract void validCommonParameter(T t);

    /**
     * 自定义参数校验
     *
     * @param t
     */
    protected abstract void validCustomParameter(T t);

    /**
     * 全局唯一标识（便于日志排查）
     *
     * @param t
     * @param s
     * @return
     */
    protected abstract String setKey(T t, S s);

    /**
     * 执行管道
     *
     * @param t
     * @param s
     */
    protected void doPipeline(T t, S s) {

        validCommonParameter(t);

        validCustomParameter(t);

        InvocationChain<T, S> invocationChain = pipeline.newInvocation(t, s);

        invocationChain.setKey(setKey(t, s));

        try {

            invocationChain.invoke();

        } catch (BizException exception) {

            doRollback(invocationChain);

            throw new BizException(exception.getErrorContext());
        }

        doSuccess(invocationChain);

    }

    /**
     * 执行失败回滚
     *
     * @param invocationChain 入参
     */
    protected void doRollback(InvocationChain<T, S> invocationChain) {
        List<RollBack<T, S>> rollBackList = invocationChain.getRollBackList();
        if (CollectionUtils.isNotEmpty(rollBackList)) {
            for (RollBack<T, S> rollBack : rollBackList) {
                rollBack.rollBack(invocationChain);
            }
        }
    }

    /**
     * 执行成功之后的逻辑处理
     *
     * @param invocationChain 入参
     */
    protected void doSuccess(InvocationChain<T, S> invocationChain) {
        List<Success<T, S>> successList = invocationChain.getSuccessList();
        if (CollectionUtils.isNotEmpty(successList)) {
            for (Success<T, S> success : successList) {
                try {
                    success.success(invocationChain);
                } catch (Exception e) {
                    log.error("执行成功之后的逻辑处理异常", e);
                }
            }
        }
    }
}
