package com.moredian.magicube.device.xxljob;

import static com.moredian.magicube.device.constant.RedisConstants.DEVICE_TYPE_PROPERTY_CONFIG;

import cn.hutool.core.util.ObjectUtil;
import com.moredian.bee.job.annotation.BeeXxlJob;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.manager.DeviceTypePropertyConfigManager;
import com.moredian.magicube.device.utils.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Arrays;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description：定时更新设备属性配置到redis中
 * @date ：2024/07/25 16:52
 */
@Component
@Slf4j
public class DeviceTypePropertyConfigSyncJob {

    @Resource
    private DeviceTypePropertyConfigManager deviceTypePropertyConfigManager;

    @Resource
    private RedissonCacheComponent redissonCacheComponent;


    @BeeXxlJob(value = "deviceTypePropertyConfigSync", name = "定时缓存设备属性配置")
    public ReturnT<String> cacheSync(String deviceType) {
        ReturnT<String> success = ReturnT.SUCCESS;

        if (ObjectUtil.isEmpty(deviceType)) {
            deviceTypePropertyConfigManager.refreshCache(null);
            return success;
        }

        Arrays.stream(deviceType.split(StringUtils.COMMA))
            .mapToInt(Integer::parseInt)
            .forEach(e-> deviceTypePropertyConfigManager.refreshCache(e));
        return success;
    }


    @BeeXxlJob(value = "deviceTypePropertyConfigDeleteCache", name = "删除设备配置缓存")
    public ReturnT<String> deleteCache(String deviceType) {
        ReturnT<String> success = ReturnT.SUCCESS;

        if (ObjectUtil.isEmpty(deviceType)) {
            String key = RedisConstants.getKey(DEVICE_TYPE_PROPERTY_CONFIG, "*");
            redissonCacheComponent.delObjectCache(key);
            return success;
        }

        Arrays.stream(deviceType.split(StringUtils.COMMA))
            .mapToInt(Integer::parseInt)
            .forEach(e-> {
                String key =  RedisConstants.getKey(DEVICE_TYPE_PROPERTY_CONFIG, e);
                redissonCacheComponent.delObjectCache(key);
            });
        return success;
    }
}
