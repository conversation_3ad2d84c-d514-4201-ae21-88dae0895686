package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 组织设备迁移表
 * @author: wbf
 * @date: 2024/8/12 下午4:09
 */
@TableName("hive_org_device_migration")
@Data
public class OrgDeviceMigration extends TimedEntity {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;
    /**
     * 组织id
     */
    private Long orgId;
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 迁移状态
     *
     * @see com.moredian.magicube.device.enums.MigrationStatusEnum
     */
    private Integer migrationStatus;
    /**
     * 设备返回钉钉组织列表页url
     */
    private String dingTalkUrl;
}
