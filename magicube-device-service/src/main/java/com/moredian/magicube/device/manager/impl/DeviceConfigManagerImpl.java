package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.model.msg.RefreshDeviceConfigMsg;
import com.moredian.magicube.common.model.msg.device.DeviceConfigXmlChangeMsg;
import com.moredian.magicube.device.config.ParamConstants;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceConfig;
import com.moredian.magicube.device.dao.mapper.DeviceConfigMapper;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceConfigManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.utils.Coder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceConfigManagerImpl implements DeviceConfigManager {

    @Autowired
    private DeviceConfigMapper deviceConfigMapper;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public boolean insertOrUpdate(Long orgId, Long deviceId, String xml) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        BizAssert.notBlank(xml, "xml must not be blank");
        DeviceConfig deviceConfig = new DeviceConfig();
        Long id = idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_CONFIG)
                                    .pickDataThrowException();
        deviceConfig.setDeviceConfigId(id);
        Device device = deviceManager.getById(deviceId);
        BizAssert.notNull(device, DeviceErrorCode.DEVICE_NOT_EXIST,
                          DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
        deviceConfig.setDeviceSn(device.getDeviceSn());

        String existSignature = deviceConfigMapper.getConfigSignatureByDeviceSn(device.getDeviceSn());
        // xml无任何变动，不更新版本号
        if (StringUtils.isNotBlank(existSignature)) {
            String signature = Coder.MD5(xml);
            if (existSignature.equals(signature)) {
                log.info("设备XML配置更新无变化，无需升级版本号，设备SN={}", device.getDeviceSn());
                return true;
            }
        }
        // 更新xml版本号
        String xmlUp = modifyDeviceConfigVersion(device.getDeviceId(), xml);
        deviceConfig.setXmlConfig(xmlUp);
        String finalSignature = Coder.MD5(xmlUp);
        deviceConfig.setConfigSignature(finalSignature);
        deviceConfigMapper.insertOrUpdate(deviceConfig);
        log.info("设备XML配置更新变化，需升级版本号，设备SN={}", device.getDeviceSn());

        try{
            DeviceConfigXmlChangeMsg deviceConfigXmlChangeMsg = new DeviceConfigXmlChangeMsg();
            deviceConfigXmlChangeMsg.setOrgId(orgId);
            deviceConfigXmlChangeMsg.setDeviceSn(device.getDeviceSn());
            deviceConfigXmlChangeMsg.setDeviceId(deviceId);
            EventBus.publish(deviceConfigXmlChangeMsg);
        }catch (Exception e){
            log.warn("设备XML配置更新消息发送异常, SN:{}", device.getDeviceSn(), e);
        }

        return true;
    }

    private String modifyDeviceConfigVersion(Long deviceId, String xml) {
        // 使用正则表达式匹配并替换 configVersion
        String pattern = ParamConstants.CONFIG_VERSION + "=\"\\d+\"";
        String replacement = ParamConstants.CONFIG_VERSION + "=\"" + incrementRedisVersion(deviceId) + "\"";
        xml = xml.replaceFirst(pattern, replacement);
        return xml;
    }

    private long incrementRedisVersion(Long deviceId) {
        RedisAtomicLong counter = new RedisAtomicLong(RedisKeys.getKey(RedisKeys.VERSION_KEY_PRE, deviceId), redisTemplate.getConnectionFactory());
        return counter.incrementAndGet();
    }

    @Override
    public String getXmlConfig(String deviceSn) {
        DeviceConfig deviceConfig = deviceConfigMapper.getByDeviceSn(deviceSn);
        if (deviceConfig == null) {
            Device device = deviceManager.getByDeviceSn(deviceSn);
            if (null != device) {
                RefreshDeviceConfigMsg msg = new RefreshDeviceConfigMsg();
                msg.setOrgId(device.getOrgId());
                msg.setDeviceSn(deviceSn);
                msg.setOperationType(1);
                msg.setTimeStamp(System.currentTimeMillis());
                EventBus.publish(msg);
                log.debug("#######Resend RefreshDeviceConfigMsg deviceSn is{},orgId is {}", deviceSn, device.getOrgId());
            } else {
                log.debug("#######getXmlConfig Failed,device is not found deviceSn is{}", deviceSn);
            }
            return "";
        } else {
            return deviceConfig.getXmlConfig();
        }
    }
}
