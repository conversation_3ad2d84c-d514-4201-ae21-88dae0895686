package com.moredian.magicube.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.device.dao.entity.dahua.CameraDeviceInfo;
import com.moredian.magicube.device.dto.dahua.QueryCameraDTO;
import com.moredian.magicube.device.dto.third.AddThirdDeviceRequest;
import com.moredian.magicube.device.dto.third.AddThirdDeviceTypeRequest;
import com.moredian.magicube.device.dto.third.QueryThirdDeviceRequest;
import com.moredian.magicube.device.dto.third.ThirdDeviceResponse;
import com.moredian.magicube.device.dto.third.ThirdDeviceTypeResponse;
import com.moredian.magicube.device.dto.third.UpdateThirdDeviceRequest;
import com.moredian.magicube.device.manager.dahua.CameraDeviceManager;
import com.moredian.magicube.device.service.ThirdDeviceService;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @description：三方设备接口
 * @date ：2024/11/14 13:57
 */
@SI
@RequiredArgsConstructor
public class ThirdDeviceServiceImpl implements ThirdDeviceService {

    private final CameraDeviceManager cameraDeviceManager;

    @Override
    public ServiceResponse<List<ThirdDeviceTypeResponse>> listThirdDeviceType() {
        return null;
    }

    @Override
    public ServiceResponse<Boolean> addThirdDeviceType(AddThirdDeviceTypeRequest request) {
        return null;
    }

    @Override
    public ServiceResponse<Boolean> deleteThirdDeviceType(Integer deviceType) {
        return null;
    }

    @Override
    public ServiceResponse<Pagination<ThirdDeviceResponse>> paginationThirdDevice(
        Pagination<ThirdDeviceResponse> pagination, QueryThirdDeviceRequest request) {
        return null;
    }

    @Override
    public ServiceResponse<Boolean> addThirdDevice(AddThirdDeviceRequest request) {
        return null;
    }

    @Override
    public ServiceResponse<Boolean> deleteThirdDevice(Long thirdDeviceId) {
        return null;
    }

    @Override
    public ServiceResponse<Boolean> updateThirdDevice(UpdateThirdDeviceRequest request) {
        return null;
    }

    /**
     * 根据机构id、设备id查询三方设备接口
     * todo：可能存在其它的三方设备，这里只查询了大华摄像头设备（hive_camera_info）
     */
    @Override
    public ServiceResponse<List<ThirdDeviceResponse>> getThirdDeviceByIdList(Long orgId,
        List<Long> thirdDeviceIdList) {
        ServiceResponse<List<ThirdDeviceResponse>> res = ServiceResponse.createSuccessResponse();
        QueryCameraDTO dto = new QueryCameraDTO();
        dto.setOrgId(orgId);
        dto.setDeviceIds(thirdDeviceIdList);
        List<CameraDeviceInfo> cameraDeviceInfos = cameraDeviceManager.listByCondition(dto);
        List<ThirdDeviceResponse> list = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cameraDeviceInfos)){
            list = cameraDeviceInfos.stream().map(e -> {
                ThirdDeviceResponse item = new ThirdDeviceResponse();
                item.setThirdDeviceId(e.getCameraDeviceId());
                item.setDeviceSn(item.getDeviceSn());
                item.setDeviceType(DeviceType.BOARD_DAHUA_CAMERA.getValue());
                item.setGmtCreate(e.getGmtCreate());
                item.setOrgId(e.getOrgId());
                return item;
            }).collect(Collectors.toList());
        }
        res.setData(list);
        return res;
    }
}
