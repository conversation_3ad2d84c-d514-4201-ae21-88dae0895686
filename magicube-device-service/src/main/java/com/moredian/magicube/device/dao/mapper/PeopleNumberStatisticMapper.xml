<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.PeopleNumberStatisticMapper">

  <resultMap type="com.moredian.magicube.device.dao.entity.PeopleNumberStatistic" id="baseMap">
    <result property="id" column="id"/>
    <result property="orgId" column="org_id"/>
    <result property="deviceId" column="device_id"/>
    <result property="deviceSn" column="device_sn"/>
    <result property="insidePeopleNum" column="inside_people_num"/>
    <result property="gmtCreate" column="gmt_create"/>
    <result property="gmtModify" column="gmt_modify"/>
  </resultMap>

  <sql id="sql_table">
        hive_device_people_statistic
    </sql>

  <sql id="sql_columns">
        id,
        org_id,
        device_id,
        device_sn,
        inside_people_num,
        gmt_create,
        gmt_modify
    </sql>

  <sql id="sql_values">
        #{id},
        #{orgId},
        #{deviceId},
        #{deviceSn},
        #{insidePeopleNum},
        now(3),
        now(3)
    </sql>

  <select id="getByOrgIdAndDeviceSn" parameterType="map" resultMap="baseMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and device_sn = #{deviceSn}
  </select>

  <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.PeopleNumberStatistic">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>

  <update id="update" parameterType="com.moredian.magicube.device.dao.entity.PeopleNumberStatistic">
    update
    <include refid="sql_table"/>
    set inside_people_num = #{insidePeopleNum},gmt_modify = now(3)
    where org_id = #{orgId}
    and id = #{id}
  </update>

  <select id="listByOrgIdAndDeviceSns" parameterType="map" resultMap="baseMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceSns != null and deviceSns.size() > 0">
      and device_sn in
      <foreach collection="deviceSns" index="index" item="deviceSn" open="(" separator=","
        close=")">
        #{deviceSn}
      </foreach>
    </if>
  </select>
  <select id="listByOrgIdAndDeviceIds" resultMap="baseMap">
    SELECT <include refid="sql_columns"/>
    FROM <include refid="sql_table"/>
    WHERE org_id = #{orgId}
    <if test="deviceIds != null and deviceIds.size() > 0">
      AND device_id IN
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator="," close=")">
        #{deviceId}
      </foreach>
    </if>
  </select>
</mapper>