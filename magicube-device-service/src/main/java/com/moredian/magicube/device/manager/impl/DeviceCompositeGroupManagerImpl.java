package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.GroupAppType;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.core.org.model.GroupInfo;
import com.moredian.magicube.core.org.service.GroupService;
import com.moredian.magicube.device.constant.DeviceCompositeBizType;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceCompositeGroup;
import com.moredian.magicube.device.dao.entity.DeviceGroup;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeGroupMapper;
import com.moredian.magicube.device.dto.composite.CompositeDeepDeviceSizeDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositeDTO;
import com.moredian.magicube.device.dto.composite.SimpleDeviceCompositeGroupDTO;
import com.moredian.magicube.device.dto.group.SimpleDeviceGroupDTO;
import com.moredian.magicube.device.enums.DeviceCompositeGroupRangeType;
import com.moredian.magicube.device.manager.DeviceCompositeGroupManager;
import com.moredian.magicube.device.manager.DeviceCompositeManager;
import com.moredian.magicube.device.manager.DeviceGroupManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.utils.DeviceCompositeBizUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/25 17:59
 */

@Slf4j
@Service
public class DeviceCompositeGroupManagerImpl implements DeviceCompositeGroupManager {

    @Autowired
    private DeviceCompositeGroupMapper deviceCompositeGroupMapper;
    @Autowired
    private DeviceManager deviceManager;
    @Autowired
    private DeviceCompositeManager deviceCompositeManager;
    @Autowired
    private DeviceGroupManager deviceGroupManager;
    @SI
    private GroupService groupService;

    @Override
    public List<SimpleDeviceCompositeGroupDTO> findDeviceCompositeGroupByGroupIds(Long orgId, List<Long> groupIds) {
        List<SimpleDeviceCompositeGroupDTO> data = Lists.newArrayList();
        List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByGroupIdList(orgId, groupIds, null);
        if (CollectionUtils.isEmpty(deviceCompositeGroupList)) {
            //兼容老的数据
            List<SimpleDeviceGroupDTO> simpleDeviceGroupResponseList = deviceGroupManager.findDeviceGroupByGroupIds(orgId, groupIds);
            if (CollectionUtils.isNotEmpty(simpleDeviceGroupResponseList)) {
                for (SimpleDeviceGroupDTO simpleDeviceGroupResponse : simpleDeviceGroupResponseList) {
                    SimpleDeviceCompositeGroupDTO simpleDeviceCompositeGroupResponse = new SimpleDeviceCompositeGroupDTO();
                    simpleDeviceCompositeGroupResponse.setGroupId(simpleDeviceGroupResponse.getGroupId());
                    List<SimpleDeviceCompositeGroupDTO.SimpleDevice> deviceList = new ArrayList<>();
                    simpleDeviceGroupResponse.getDeviceList().forEach(e -> {
                        SimpleDeviceCompositeGroupDTO.SimpleDevice simpleDevice = new SimpleDeviceCompositeGroupDTO.SimpleDevice();
                        simpleDevice.setDeviceId(e.getDeviceId());
                        simpleDevice.setDeviceSn(e.getDeviceSn());
                        deviceList.add(simpleDevice);
                    });
                    simpleDeviceCompositeGroupResponse.setDeviceList(deviceList);
                    data.add(simpleDeviceCompositeGroupResponse);
                }
            }
            return data;
        } else {
            Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup>> compositeMap = findDeviceCompositeGroupByGroupList(orgId,
                    deviceCompositeGroupList);
            Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> deviceMap = findDeviceByGroupIdList(orgId, groupIds, deviceCompositeGroupList);
            for (Long groupId : groupIds) {
                SimpleDeviceCompositeGroupDTO simpleDeviceCompositeGroupResponse = new SimpleDeviceCompositeGroupDTO();
                simpleDeviceCompositeGroupResponse.setGroupId(groupId);
                // 设备
                List<SimpleDeviceCompositeGroupDTO.SimpleDevice> simpleDeviceList = deviceMap.get(groupId);
                if (CollectionUtils.isNotEmpty(simpleDeviceList)) {
                    simpleDeviceCompositeGroupResponse.setDeviceList(simpleDeviceList);
                }
                // 设备组
                List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup> simpleDeviceCompositeGroupList = compositeMap.get(groupId);
                if (CollectionUtils.isNotEmpty(simpleDeviceCompositeGroupList)) {
                    simpleDeviceCompositeGroupResponse.setDeviceCompositeGroupList(simpleDeviceCompositeGroupList);
                }
                data.add(simpleDeviceCompositeGroupResponse);
            }
        }
        return data;
    }

    @Override
    public SimpleDeviceCompositeGroupDTO findDeviceCompositeGroupByGroupId(Long orgId, Long groupId) {
        List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByGroupId(orgId, groupId, null);
        SimpleDeviceCompositeGroupDTO simpleDeviceCompositeGroupResponse = new SimpleDeviceCompositeGroupDTO();
        simpleDeviceCompositeGroupResponse.setGroupId(groupId);
        if (CollectionUtils.isEmpty(deviceCompositeGroupList)) {
            //兼容老的数据
            List<DeviceGroup> deviceGroupList = deviceGroupManager.findRelationByGroupId(orgId, groupId);
            if (CollectionUtils.isNotEmpty(deviceGroupList)) {
                List<Long> deviceIdList = deviceGroupList.stream()
                        .map(DeviceGroup::getDeviceId)
                        .distinct()
                        .collect(Collectors.toList());
                buildSingleGroupDeviceList(orgId, deviceIdList, simpleDeviceCompositeGroupResponse);
            }
            return simpleDeviceCompositeGroupResponse;
        }
        // 设备数据
        List<Long> deviceIdList = deviceCompositeGroupList.stream()
                .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                .map(DeviceCompositeGroup::getRangeId)
                .distinct()
                .collect(Collectors.toList());
        buildSingleGroupDeviceList(orgId, deviceIdList, simpleDeviceCompositeGroupResponse);
        // 设备组信息
        Integer sceneType = null;
        GroupInfo groupInfo = groupService.getGroupInfo(orgId, groupId).pickDataThrowException();
        if (groupInfo != null && groupInfo.getAppType() != null) {
            if (groupInfo.getAppType() == GroupAppType.GATE.getValue()
                || groupInfo.getAppType() == GroupAppType.ATTENDANCE.getValue()
                || groupInfo.getAppType() == GroupAppType.BLACK_LIST.getValue()) {
                sceneType = SceneTypeEnums.STANDARD_MODEL.getValue();
            } else if (groupInfo.getAppType() == GroupAppType.ELEVATOR.getValue()) {
                sceneType = SceneTypeEnums.ELEVATOR_MODEL.getValue();
            } else if (groupInfo.getAppType() == GroupAppType.MEETING.getValue()) {
                sceneType = SceneTypeEnums.MEETING_ROOM_MODEL.getValue();
            } else if (groupInfo.getAppType() == GroupAppType.CLASS_ROM.getValue()) {
                sceneType = SceneTypeEnums.CLASSROOM_MODEL.getValue();
            }
        }
        buildSingleGroupDeviceCompositeGroupList(orgId, deviceCompositeGroupList, simpleDeviceCompositeGroupResponse, sceneType);
        return simpleDeviceCompositeGroupResponse;
    }

    @Override
    public List<SimpleDeviceCompositeGroupDTO> findDeviceCompositeGroupByGroupIdsNonAllDevice(Long orgId, List<Long> groupIds) {
        List<SimpleDeviceCompositeGroupDTO> data = Lists.newArrayList();
        List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByGroupIdList(orgId, groupIds, null);
        if (CollectionUtils.isEmpty(deviceCompositeGroupList)) {
            return matchOldGroupSimpleDeviceCompositeGroupResponse(orgId, groupIds, data);
        } else {
            Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup>> compositeMap = findDeviceCompositeGroupByGroupList(orgId,
                deviceCompositeGroupList);
            // 组和设备数据
            Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> groupIdDeviceListMap = getGroupIdDeviceListMap(orgId, deviceCompositeGroupList);
            buildGroupComposite(groupIds, groupIdDeviceListMap, compositeMap, data);
        }
        return data;
    }

    @Override
    public boolean deleteByDeviceId(Long orgId, Long deviceId) {
        if (deviceId != null) {
            List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByDeviceIdList(orgId, Lists.newArrayList(deviceId));
            if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
                deviceCompositeGroupMapper.deleteByIds(orgId, deviceCompositeGroupList.stream().map(DeviceCompositeGroup::getDeviceCompositeGroupId).collect(Collectors.toList()));
            }
        }
        return true;
    }

    private void buildSingleGroupDeviceCompositeGroupList(Long orgId,
                                                          List<DeviceCompositeGroup> deviceCompositeGroupList,
        SimpleDeviceCompositeGroupDTO simpleDeviceCompositeGroupResponse, Integer sceneType) {
        List<Long> deviceCompositeIdList = deviceCompositeGroupList.stream()
                .filter(e -> DeviceCompositeGroupRangeType.GROUP.getCode().equals(e.getRangeType()))
                .map(DeviceCompositeGroup::getRangeId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deviceCompositeIdList)) {
            List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup> simpleDeviceCompositeGroupList = new ArrayList<>();
            List<DeviceCompositeDTO> deviceCompositeList = deviceCompositeManager.listByDeviceCompositeIds(orgId, deviceCompositeIdList, DeviceCompositeBizType.COMMON);
            if (CollectionUtils.isNotEmpty(deviceCompositeList)) {
                List<CompositeDeepDeviceSizeDTO> deepDeviceSize = deviceCompositeManager.getCompositeDeepDeviceSize(
                    orgId, deviceCompositeIdList, DeviceCompositeBizType.COMMON, sceneType);
                Map<Long/*设备组*/, List<Long>/*设备Id列表*/> map = deepDeviceSize.stream().collect(Collectors.toMap(
                    CompositeDeepDeviceSizeDTO::getDeviceCompositeId, CompositeDeepDeviceSizeDTO::getDeviceIds));
                Map<String, String> idNameMap = getCompositeCodeNameMap(orgId, deviceCompositeIdList, deviceCompositeList);
                deviceCompositeList.forEach(e -> buildSimpleDeviceCompositeGroup(idNameMap, simpleDeviceCompositeGroupList, e,map));
                simpleDeviceCompositeGroupResponse.setDeviceCompositeGroupList(simpleDeviceCompositeGroupList);
            }
        }
    }


    private void buildSingleGroupDeviceList(Long orgId,
                                            List<Long> deviceIdList,
        SimpleDeviceCompositeGroupDTO simpleDeviceCompositeGroupResponse) {
        if (CollectionUtils.isNotEmpty(deviceIdList)) {
            List<Device> deviceList = deviceManager.listByOrgIdAndIds(orgId, deviceIdList);
            if (CollectionUtils.isNotEmpty(deviceList)) {
                List<SimpleDeviceCompositeGroupDTO.SimpleDevice> simpleDeviceList = new ArrayList<>();
                deviceList.forEach(e -> {
                    SimpleDeviceCompositeGroupDTO.SimpleDevice simpleDevice = new SimpleDeviceCompositeGroupDTO.SimpleDevice();
                    simpleDevice.setDeviceId(e.getDeviceId());
                    simpleDevice.setDeviceSn(e.getDeviceSn());
                    simpleDeviceList.add(simpleDevice);
                });
                simpleDeviceCompositeGroupResponse.setDeviceList(simpleDeviceList);
            }
        }
    }

    private Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> findDeviceByGroupIdList(Long orgId,
                                                                                                     List<Long> groupIds,
                                                                                                     List<DeviceCompositeGroup> deviceCompositeGroupList) {
        Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> groupIdDeviceListMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
            // 新通行组
            List<Long> newGroupIdList = deviceCompositeGroupList.stream()
                    .map(DeviceCompositeGroup::getGroupId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Long> deviceIdList = deviceCompositeGroupList.stream()
                    .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                    .map(DeviceCompositeGroup::getRangeId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Device> deviceList = deviceManager.listByOrgIdAndIds(orgId, deviceIdList);
            if (CollectionUtils.isNotEmpty(deviceList)) {
                Map<Long/*deviceId*/, String/*deviceSn*/> deviceIdToDeviceSnMap = deviceList.stream().collect(Collectors.toMap(Device::getDeviceId, Device::getDeviceSn));
                Map<Long, List<DeviceCompositeGroup>> groupIdDeviceCompositeGroupListMap = deviceCompositeGroupList.stream()
                        .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                        .collect(Collectors.groupingBy(DeviceCompositeGroup::getGroupId));
                for (Map.Entry<Long, List<DeviceCompositeGroup>> entry : groupIdDeviceCompositeGroupListMap.entrySet()) {
                    List<SimpleDeviceCompositeGroupDTO.SimpleDevice> newDeviceList = new ArrayList<>();
                    entry.getValue().forEach(e -> {
                        String deviceSn = deviceIdToDeviceSnMap.get(e.getRangeId());
                        if (StringUtils.isNotEmpty(deviceSn)) {
                            SimpleDeviceCompositeGroupDTO.SimpleDevice simpleDevice = new SimpleDeviceCompositeGroupDTO.SimpleDevice();
                            simpleDevice.setDeviceId(e.getRangeId());
                            simpleDevice.setDeviceSn(deviceSn);
                            newDeviceList.add(simpleDevice);
                        }
                    });
                    groupIdDeviceListMap.put(entry.getKey(), newDeviceList);
                }
            }
            // 老权限组
            List<Long> oldGroupIdList = groupIds.stream().filter(e -> !newGroupIdList.contains(e)).collect(Collectors.toList());
            List<SimpleDeviceGroupDTO> simpleDeviceGroupResponseList = deviceGroupManager.findDeviceGroupByGroupIds(orgId, oldGroupIdList);
            if (CollectionUtils.isNotEmpty(simpleDeviceGroupResponseList)) {
                for (SimpleDeviceGroupDTO simpleDeviceGroupResponse : simpleDeviceGroupResponseList) {
                    List<SimpleDeviceCompositeGroupDTO.SimpleDevice> oldDeviceList = new ArrayList<>();
                    simpleDeviceGroupResponse.getDeviceList().forEach(e -> {
                        SimpleDeviceCompositeGroupDTO.SimpleDevice simpleDevice = new SimpleDeviceCompositeGroupDTO.SimpleDevice();
                        simpleDevice.setDeviceId(e.getDeviceId());
                        simpleDevice.setDeviceSn(e.getDeviceSn());
                        oldDeviceList.add(simpleDevice);
                    });
                    groupIdDeviceListMap.put(simpleDeviceGroupResponse.getGroupId(), oldDeviceList);
                }
            }

        }
        return groupIdDeviceListMap;
    }

    /**
     * 获取所有设备组信息
     *
     * @param orgId                    机构ID
     * @param deviceCompositeGroupList 通行组设备组关系数据
     * @return key:groupId,value=设备组信息
     */
    private Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup>> findDeviceCompositeGroupByGroupList(Long orgId,
                                                                                                                               List<DeviceCompositeGroup> deviceCompositeGroupList) {
        Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup>> data = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
            List<Long> deviceCompositeIdList = deviceCompositeGroupList.stream()
                    .filter(e -> DeviceCompositeGroupRangeType.GROUP.getCode().equals(e.getRangeType()))
                    .map(DeviceCompositeGroup::getRangeId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deviceCompositeIdList)) {
                // 查询设备组信息
                List<DeviceCompositeDTO> deviceCompositeList = deviceCompositeManager.listByDeviceCompositeIds(orgId, deviceCompositeIdList, DeviceCompositeBizType.COMMON);
                // 设备组Map
                Map<Long, DeviceCompositeDTO> deviceCompositeMap = getCompositeIdDeviceCompositeDtoMap(deviceCompositeList);
                Map<String, String> idNameMap = getCompositeCodeNameMap(orgId, deviceCompositeIdList, deviceCompositeList);
                // 通信组设备组列表转化成map
                Map<Long, List<DeviceCompositeGroup>> groupIdDeviceCompositeGroupListMap = deviceCompositeGroupList.stream()
                        .filter(e -> DeviceCompositeGroupRangeType.GROUP.getCode().equals(e.getRangeType()))
                        .collect(Collectors.groupingBy(DeviceCompositeGroup::getGroupId));

                List<CompositeDeepDeviceSizeDTO> deepDeviceSize = deviceCompositeManager.getCompositeDeepDeviceSize(
                    orgId, deviceCompositeIdList, DeviceCompositeBizType.COMMON, null);
                Map<Long/*设备组*/, List<Long>/*设备Id列表*/> map = deepDeviceSize.stream().collect(Collectors.toMap(
                    CompositeDeepDeviceSizeDTO::getDeviceCompositeId,
                    CompositeDeepDeviceSizeDTO::getDeviceIds));
                for (Map.Entry<Long, List<DeviceCompositeGroup>> entry : groupIdDeviceCompositeGroupListMap.entrySet()) {
                    // 设备组信息
                    List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup> simpleDeviceCompositeGroupList = new ArrayList<>();
                    entry.getValue().forEach(e -> {
                        DeviceCompositeDTO deviceCompositeDto = deviceCompositeMap.get(e.getRangeId());
                        if (deviceCompositeDto != null) {
                            buildSimpleDeviceCompositeGroup(idNameMap, simpleDeviceCompositeGroupList, deviceCompositeDto,map);
                        }
                    });
                    data.put(entry.getKey(), simpleDeviceCompositeGroupList);
                }
            }
        }
        return data;
    }

    private void buildSimpleDeviceCompositeGroup(Map<String, String> idNameMap,
                                                 List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup> simpleDeviceCompositeGroupList,
                                                 DeviceCompositeDTO deviceCompositeDto, Map<Long/*设备组*/, List<Long>/*设备Id列表*/> map) {
        SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup simpleDeviceCompositeGroup = new SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup();
        simpleDeviceCompositeGroup.setDeviceCompositeId(deviceCompositeDto.getDeviceCompositeId());
        simpleDeviceCompositeGroup.setDeviceCompositeName(deviceCompositeDto.getDeviceCompositeName());
        simpleDeviceCompositeGroup.setParentDeviceCompositeId(deviceCompositeDto.getParentId());
        simpleDeviceCompositeGroup.setIdPath(deviceCompositeDto.getPath());
        simpleDeviceCompositeGroup.setBizType(deviceCompositeDto.getBizType());
        simpleDeviceCompositeGroup.setPathName(DeviceCompositeBizUtil.codePathToNamePath(deviceCompositeDto.getDeviceCompositeName(), deviceCompositeDto.getPath(), idNameMap));
        List<Long> deviceIdList = map.get(deviceCompositeDto.getDeviceCompositeId());
        if (CollectionUtils.isNotEmpty(deviceIdList)) {
            simpleDeviceCompositeGroup.setDeviceIdList(deviceIdList);
            simpleDeviceCompositeGroup.setDeviceSize(deviceIdList.size());
        } else {
            simpleDeviceCompositeGroup.setDeviceSize(0);
        }
        simpleDeviceCompositeGroupList.add(simpleDeviceCompositeGroup);
    }

    private Map<Long, DeviceCompositeDTO> getCompositeIdDeviceCompositeDtoMap(List<DeviceCompositeDTO> deviceCompositeList) {
        return deviceCompositeList.stream()
                .collect(Collectors.toMap(DeviceCompositeDTO::getDeviceCompositeId, Function.identity(), (k1, k2) -> k1));
    }

    private Map<String, String> getCompositeCodeNameMap(Long orgId, List<Long> deviceCompositeIdList, List<DeviceCompositeDTO> deviceCompositeList) {
        List<String> parentDeviceCompositeCodeList = deviceCompositeList.stream()
                .filter(Objects::nonNull)
                .flatMap(e -> Arrays.stream(StringUtils.split(e.getPath(), "/")))
                .distinct()
                .collect(Collectors.toList());
        List<DeviceCompositeDTO> nameList = deviceCompositeManager.listByDeviceCompositeCodeList(orgId, parentDeviceCompositeCodeList, DeviceCompositeBizType.COMMON);
        return nameList.stream()
                .filter(e -> !deviceCompositeIdList.contains(e.getDeviceCompositeId()))
                .filter(e -> !Objects.equals(e.getCode(), "00000000"))
                .collect(Collectors.toMap(DeviceCompositeDTO::getCode, DeviceCompositeDTO::getDeviceCompositeName, (k1, k2) -> k1));
    }

    private List<SimpleDeviceCompositeGroupDTO> matchOldGroupSimpleDeviceCompositeGroupResponse(Long orgId, List<Long> groupIds, List<SimpleDeviceCompositeGroupDTO> data) {
        //兼容老的数据
        List<SimpleDeviceGroupDTO> simpleDeviceGroupResponseList = deviceGroupManager.findDeviceGroupByGroupIds(orgId, groupIds);
        if (CollectionUtils.isNotEmpty(simpleDeviceGroupResponseList)) {
            for (SimpleDeviceGroupDTO simpleDeviceGroupResponse : simpleDeviceGroupResponseList) {
                SimpleDeviceCompositeGroupDTO simpleDeviceCompositeGroupResponse = new SimpleDeviceCompositeGroupDTO();
                simpleDeviceCompositeGroupResponse.setGroupId(simpleDeviceGroupResponse.getGroupId());
                List<SimpleDeviceCompositeGroupDTO.SimpleDevice> deviceList = new ArrayList<>();
                simpleDeviceGroupResponse.getDeviceList().forEach(e -> {
                    SimpleDeviceCompositeGroupDTO.SimpleDevice simpleDevice = new SimpleDeviceCompositeGroupDTO.SimpleDevice();
                    simpleDevice.setDeviceId(e.getDeviceId());
                    simpleDevice.setDeviceSn(e.getDeviceSn());
                    deviceList.add(simpleDevice);
                });
                simpleDeviceCompositeGroupResponse.setDeviceList(deviceList);
                data.add(simpleDeviceCompositeGroupResponse);
            }
        }
        return data;
    }

    private Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> getGroupIdDeviceListMap(Long orgId, List<DeviceCompositeGroup> deviceCompositeGroupList) {
        Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> groupIdDeviceListMap = Maps.newHashMap();
        getGroupIdDeviceList(orgId, deviceCompositeGroupList, groupIdDeviceListMap);
        return groupIdDeviceListMap;
    }

    private void getGroupIdDeviceList(Long orgId, List<DeviceCompositeGroup> deviceCompositeGroupList, Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> groupIdDeviceListMap) {
        List<Long> deviceIdList = deviceCompositeGroupList.stream()
            .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
            .map(DeviceCompositeGroup::getRangeId)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceIdList)) {
            return;
        }
        List<Device> deviceList = deviceManager.listByOrgIdAndIds(orgId, deviceIdList);
        if (CollectionUtils.isNotEmpty(deviceList)) {
            Map<Long/*deviceId*/, String/*deviceSn*/> deviceIdToDeviceSnMap = deviceList.stream().collect(Collectors.toMap(Device::getDeviceId, Device::getDeviceSn));
            Map<Long, List<DeviceCompositeGroup>> groupIdDeviceCompositeGroupListMap = deviceCompositeGroupList.stream()
                .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                .collect(Collectors.groupingBy(DeviceCompositeGroup::getGroupId));
            for (Map.Entry<Long, List<DeviceCompositeGroup>> entry : groupIdDeviceCompositeGroupListMap.entrySet()) {
                List<SimpleDeviceCompositeGroupDTO.SimpleDevice> newDeviceList = new ArrayList<>();
                entry.getValue().forEach(e -> {
                    String deviceSn = deviceIdToDeviceSnMap.get(e.getRangeId());
                    if (StringUtils.isNotEmpty(deviceSn)) {
                        SimpleDeviceCompositeGroupDTO.SimpleDevice simpleDevice = new SimpleDeviceCompositeGroupDTO.SimpleDevice();
                        simpleDevice.setDeviceId(e.getRangeId());
                        simpleDevice.setDeviceSn(deviceSn);
                        newDeviceList.add(simpleDevice);
                    }
                });
                groupIdDeviceListMap.put(entry.getKey(), newDeviceList);
            }
        }
    }

    private  void buildGroupComposite(List<Long> groupIds,
        Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDevice>> groupIdDeviceListMap,
        Map<Long, List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup>> compositeMap,
        List<SimpleDeviceCompositeGroupDTO> data) {
        for (Long groupId : groupIds) {
            SimpleDeviceCompositeGroupDTO simpleDeviceCompositeGroupResponse = new SimpleDeviceCompositeGroupDTO();
            simpleDeviceCompositeGroupResponse.setGroupId(groupId);
            // 设备
            List<SimpleDeviceCompositeGroupDTO.SimpleDevice> simpleDeviceList = groupIdDeviceListMap.get(groupId);
            if (CollectionUtils.isNotEmpty(simpleDeviceList)) {
                simpleDeviceCompositeGroupResponse.setDeviceList(simpleDeviceList);
            }
            // 设备组
            List<SimpleDeviceCompositeGroupDTO.SimpleDeviceCompositeGroup> simpleDeviceCompositeGroupList = compositeMap.get(groupId);
            if (CollectionUtils.isNotEmpty(simpleDeviceCompositeGroupList)) {
                simpleDeviceCompositeGroupResponse.setDeviceCompositeGroupList(simpleDeviceCompositeGroupList);
            }
            data.add(simpleDeviceCompositeGroupResponse);
        }
    }
}
