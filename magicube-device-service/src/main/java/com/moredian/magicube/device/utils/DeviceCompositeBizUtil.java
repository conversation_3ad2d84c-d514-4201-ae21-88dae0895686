package com.moredian.magicube.device.utils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/11/28 16:33
 */
public class DeviceCompositeBizUtil {

    /**
     * code path 转化 name path
     *
     * @param deviceCompositeName 设备组叶子名称
     * @param path                路径
     * @param codeNameMap         不包括自己
     * @return name path
     */
    public static String codePathToNamePath(String deviceCompositeName, String path, Map<String, String> codeNameMap) {
        if (StringUtils.isNotEmpty(path)) {
            List<String> namePath = Arrays.stream(StringUtils.split(path, "/"))
                //过滤name为空
                .filter(e -> StringUtils.isNotEmpty(codeNameMap.get(e)))
                //过滤自己
                .filter(e -> !Objects.equals(codeNameMap.get(e), deviceCompositeName))
                .map(codeNameMap::get)
                .collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(namePath) ? StringUtils.join(namePath, "/") + "/" + deviceCompositeName : deviceCompositeName;
        }
        return deviceCompositeName;
    }
}
