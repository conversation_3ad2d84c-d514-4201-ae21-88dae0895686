package com.moredian.magicube.device.service.adapter;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.DEquipmentDTO;
import com.moredian.magicube.device.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@SI
@Slf4j
public class DEquipmentServiceImpl implements DEquipmentService {

    @Autowired
    private DeviceManager deviceManager;

    @Override
    public ServiceResponse<DEquipmentDTO> getDequipmentByUniqueNumber(String uniqueNumber) {
        //logger.debug("################DEquipmentService.getDequipmentByUniqueNumber################"); //for TEST

        Device device = deviceManager.getByDeviceSn(uniqueNumber);
        if (device == null) {
            return new ServiceResponse<DEquipmentDTO>(true, null, null);
        }

        DEquipmentDTO dto = new DEquipmentDTO();
        dto.setDEquipmentId(device.getDeviceId());
        dto.setdEquipmentId(device.getDeviceId());
        dto.setEquipmentName(device.getDeviceName());
        //dEquipment.setCpuId();
        //dEquipment.setIpAddress();
        //dEquipment.setEquipmentToken();
        dto.setEquipmentType(device.getDeviceType());
        dto.setGmtCreate(device.getGmtCreate());
        dto.setGmtModify(device.getGmtModify());
        //dto.setMacAddress(device.getMacAddress());
        dto.setOrgId(device.getOrgId());
        dto.setSubOrgId(device.getPositionId());
        dto.setUniqueNumber(device.getDeviceSn());

        return new ServiceResponse<>(true, null, dto);
    }

}