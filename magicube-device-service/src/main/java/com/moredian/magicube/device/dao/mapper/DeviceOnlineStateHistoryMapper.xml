<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceOnlineStateHistoryMapper">
    <resultMap id="DeviceOnlineStateMap" type="com.moredian.magicube.device.dao.entity.DeviceOnlineStateHistory">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="online_flag" property="onlineFlag"/>
        <result column="msg_timestamp" property="msgTimestamp"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceOnlineStateHistory">
        insert into
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
            <include refid="sql_values"/>
        )
    </insert>
    <sql id="sql_table">
        hive_device_online_state_history
    </sql>

    <sql id="sql_columns">
        id,
        org_id,
        device_id,
        device_sn,
        online_flag,
        msg_timestamp,
        gmt_create,
        gmt_modify
    </sql>
    <sql id="sql_values">
        #{id},
        #{orgId},
        #{deviceId},
        #{deviceSn},
        #{onlineFlag},
        #{msgTimestamp},
        now(3),
        now(3)
    </sql>

</mapper>