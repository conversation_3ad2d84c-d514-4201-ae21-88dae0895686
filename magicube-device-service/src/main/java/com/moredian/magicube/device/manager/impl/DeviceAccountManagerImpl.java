package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.DeviceAccount;
import com.moredian.magicube.device.dao.mapper.DeviceAccountMapper;
import com.moredian.magicube.device.manager.DeviceAccountManager;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 设备-账户表 设备与使用设备的账户关联(HiveDeviceAccount)表DAO接口类 用于参数检查，数据库缓存，ID生成
 *
 * <AUTHOR>
 * @since 2020-04-10 11:14:08
 */

@Repository
public class DeviceAccountManagerImpl implements DeviceAccountManager {

    @Autowired
    private DeviceAccountMapper deviceAccountMapper;

    @SI
    private IdgeneratorService idGeneratorService;

    /**
     * 通过ID查询单条数据
     *
     * @param deviceAccountId 主键
     * @return 实例对象
     */
    @Override
    public DeviceAccount queryById(Long deviceAccountId) {
        BizAssert.notNull(deviceAccountId, "deviceAccountId must not be null");

        DeviceAccount deviceAccount = new DeviceAccount();
        deviceAccount.setDeviceAccountId(deviceAccountId);
        List<DeviceAccount> deviceAccounts = deviceAccountMapper.query(deviceAccount);
        if (!deviceAccounts.isEmpty()) {
            return deviceAccounts.get(0);
        } else {
            return null;
        }
    }

    /**
     * 查询多条数据
     *
     * @param deviceAccount 查询条件
     * @return 对象列表
     */
    @Override
    public List<DeviceAccount> queryByCond(DeviceAccount deviceAccount) {
        BizAssert.notNull(deviceAccount, "hiveDeviceAccount must not be null");

        return deviceAccountMapper.query(deviceAccount);
    }

    /**
     * 新增数据
     *
     * @param deviceAccount 实例对象
     * @return 插入记录数
     */
    @Override
    public int insert(DeviceAccount deviceAccount) {
        BizAssert.notNull(deviceAccount, "hiveDeviceAccount must not be null");
        if (deviceAccount.getDeviceAccountId() == null) {
            Long id = idGeneratorService
                .getNextIdByTypeName(BeanConstants.DEVICE_ACCOUNT).pickDataThrowException();
            deviceAccount.setDeviceAccountId(id);
        }

        return deviceAccountMapper.insert(deviceAccount);
    }

    /**
     * 修改数据
     *
     * @param deviceAccount 实例对象
     * @return 更新的记录数
     */
    @Override
    public int update(DeviceAccount deviceAccount) {
        BizAssert.notNull(deviceAccount, "hiveDeviceAccount must not be null");

        return deviceAccountMapper.update(deviceAccount);
    }

    /**
     * 通过主键删除数据
     *
     * @param deviceAccountId 主键
     * @return 删除的记录数
     */
    @Override
    public int deleteById(Long deviceAccountId) {
        BizAssert.notNull(deviceAccountId, "deviceAccountId must not be null");

        DeviceAccount cond = new DeviceAccount();
        cond.setDeviceAccountId(deviceAccountId);

        return deviceAccountMapper.delete(cond);
    }

    /**
     * 通过条件删除数据
     *
     * @param deviceAccount 删除条件
     * @return 删除的记录数
     */
    @Override
    public int deleteById(DeviceAccount deviceAccount) {
        BizAssert.notNull(deviceAccount, "hiveDeviceAccount must not be null");

        return deviceAccountMapper.delete(deviceAccount);
    }

    @Override
    public int insertBatch(Long accountId, List<String> snList) {
        BizAssert.notNull(accountId, "accountId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(snList), "snList must not be null");

        List<DeviceAccount> list = new ArrayList<>();
        for (String sn : snList) {
            DeviceAccount deviceAccount = new DeviceAccount();
            deviceAccount.setSn(sn);
            deviceAccount.setAccountId(accountId);
            deviceAccount.setStatus(YesNoFlag.YES.getValue());
            if (deviceAccount.getDeviceAccountId() == null) {
                Long id = idGeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_ACCOUNT)
                    .pickDataThrowException();
                deviceAccount.setDeviceAccountId(id);
            }
            list.add(deviceAccount);
        }

        return deviceAccountMapper.insertBatch(list);
    }

}