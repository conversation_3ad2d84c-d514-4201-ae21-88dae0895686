package com.moredian.magicube.device.pipeline.pipe;

import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.conf.core.client.BeeConfNewClient;
import com.moredian.deliver.api.agent.AgentDeviceRelationService;
import com.moredian.deliver.api.agent.AgentOrgRelationService;
import com.moredian.deliver.api.manage.WhiteDeviceService;
import com.moredian.deliver.api.run.AuthorizeDataService;
import com.moredian.deliver.api.run.AuthorizeDeviceDataService;
import com.moredian.deliver.dto.AgentDeviceRelationDTO;
import com.moredian.deliver.dto.AgentOrgRelationDTO;
import com.moredian.deliver.dto.AuthorizeDeviceDTO;
import com.moredian.deliver.dto.AuthorizeOrgDTO;
import com.moredian.deliver.dto.WhiteDeviceDTO;
import com.moredian.deliver.enums.AgentDeviceTypeEnum;
import com.moredian.deliver.enums.AuthorizeConstant;
import com.moredian.deliver.error.AuthorizeErrorCode;
import com.moredian.magicube.core.org.response.CollaborationResponse;
import com.moredian.magicube.core.org.service.CollaborationInfoService;
import com.moredian.magicube.device.constant.BeeConfConfigConstants;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.constant.StatusCode;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceFlagEnum;
import com.moredian.magicube.device.enums.EnvConfigEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date : 2023/12/4
 */
@Component
@Slf4j
public class CheckDeviceAuthorizePipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private AuthorizeDeviceDataService authorizeDeviceDataService;

    @SI
    private WhiteDeviceService deliverWhiteDeviceService;

    @SI
    private AuthorizeDataService authorizeDataService;

    @SI
    private CollaborationInfoService collaborationInfoService;

    @SI
    private AgentDeviceRelationService agentDeviceRelationService;
    @SI
    private AgentOrgRelationService agentOrgRelationService;
    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    private AuthorizeDeviceDTO deviceDTO;

    @Value("${activate.authorize.filter:false}")
    private Boolean filter;
    @Override
    protected boolean isFilter(ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext activateDeviceContext) {
        //分销设备激活,关闭激活管控
        if (activateDeviceDTO.getDeviceFlag() != null && DeviceFlagEnum.FX.getValue() == activateDeviceDTO.getDeviceFlag()) {
            log.info("分销融元 分销设备激活 关闭激活管控 deviceSn:{}", activateDeviceDTO.getDeviceSn());
            return true;
        }
        Integer appType = activateDeviceDTO.getAppType();
        if (appType != null) {
            //根据设备appType查询设备运行环境
            String environment = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE,
                    BeeConfConfigConstants.GROUP_ENVIRONMENT,
                    appType.toString());
            if (StringUtils.isNotBlank(environment) &&
                    EnvConfigEnum.FX.getCode().equals(Integer.valueOf(environment))) {
                //分销设备激活,关闭激活管控
                activateDeviceContext.setEnvironment(Integer.valueOf(environment));
                return true;
            }
        }

        //如果在激活管控白名单机构中，则跳过激活管控
        String result = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE,
            BeeConfConfigConstants.DEVICE_AUTHORIZE_ORG_WHITE,
            activateDeviceContext.getOrgId().toString());
        if (StringUtils.isNotBlank(result) && result.equals("1")) {
            return true;
        }

        //filter=true 开启激活管控 filter=false关闭激活管控
        return !filter;
    }

    /**
     * 设备激活校验优先级
     * 1.设备在脱控白单 && 机构在管控列表
     * 2.设备在管控列表 && 激活机构为设备授权机构
     * 3.设备为钉钉售卖渠道 && 保护期过期
     */
    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        String deviceSn = dto.getDeviceSn();
        Long orgId = context.getOrgId();

        //-----------------------------参数校验------------------------------
        BizAssert.notNull(orgId, "[CheckDeviceAuthorizePipe] orgId must not be null");
        BizAssert.notNull(deviceSn, "[CheckDeviceAuthorizePipe] deviceSn must not be null");

        // ----------------------------激活校验------------------------------
        //1.校验设备在脱控白名单,机构在管控列表
        boolean deliverWhiteDeviceFlag = checkDeliverWhiteDevice(deviceSn);

        //如果激活到园区协作体，需要转换成运营商机构Id（传入租户机构返回null，设备激活在租户机构）
        CollaborationResponse collaborationResponse = collaborationInfoService
            .getById(orgId).pickDataThrowException();
        if (collaborationResponse != null) {
            orgId = collaborationResponse.getMainOrgId();
        }

        //校验设备 如果是渠道商设备&&客户是渠道商下的客户 跳过激活管控
        Boolean agentDeviceFlag = checkAgentDevice(orgId, deviceSn);
        if (agentDeviceFlag) {
            log.info("设备为渠道商下的生态设备 跳过激活管控 deviceSn:{}", deviceSn);
            return;
        }

        boolean orgExistFlag = checkOrg(orgId);
        if (deliverWhiteDeviceFlag && orgExistFlag) {
            return;
        }
        //设备在脱控白单,机构未管控
        if (deliverWhiteDeviceFlag) {
            log.error("[CheckDeviceAuthorizePipe] 机构未在行业管控列表 orgId:{}", orgId);
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.DEVICE_NOT_AUTHORIZATION, HiveConst.THIRD_PARTY_TIME_OUT);
            BizAssert.notNull(null, AuthorizeErrorCode.ORG_NOT_EXIST, AuthorizeErrorCode.ORG_NOT_EXIST.getMessage());
        }

        //2.校验设备授权
        boolean deviceAuthorizeFlag = checkDeviceAuthorize(deviceSn);
        if (!deviceAuthorizeFlag) {
            log.error("[CheckDeviceAuthorizePipe]设备激活，设备未授权：deviceSn{}", deviceSn);
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.DEVICE_NOT_AUTHORIZATION, HiveConst.THIRD_PARTY_TIME_OUT);
            BizAssert.notNull(null, DeviceErrorCode.DEVICE_NOT_AUTHORIZE, DeviceErrorCode.DEVICE_NOT_AUTHORIZE.getMessage());
        }

        //3.校验激活机构是否是当前机构
        boolean currentAuthorizeOrgFlag = checkCurrentAuthorizeOrgFlag(orgId);
        if (deviceAuthorizeFlag && currentAuthorizeOrgFlag) {
            return;
        }

        //4.校验钉钉渠道设备&&商业保护期过期
        boolean dingChannelAndOverProtectTimeFlag = checkDingChannelAndOverProtectTimeFlag();
        if (dingChannelAndOverProtectTimeFlag) {
            log.info("[CheckDeviceAuthorizePipe]设备是钉钉售卖渠道,且商业保护期过期 deviceSn:{}", deviceSn);
            return;
        }
        redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.DEVICE_NOT_AUTHORIZATION, HiveConst.THIRD_PARTY_TIME_OUT);
        log.info("[CheckDeviceAuthorizePipe]设备未授权，设备授权机构与当前机构不一致 deviceSn:{}", deviceSn);
        BizAssert.notNull(null, DeviceErrorCode.INVALID_ACTIVE_INDUSTRY_NOT_MATCH,
                DeviceErrorCode.INVALID_ACTIVE_INDUSTRY_NOT_MATCH.getMessage());
    }

    /**
     * 校验设备是否为渠道商下的生态设备
     * @param orgId 机构id
     * @param deviceSn 设备sn
     * @return
     */
    public Boolean checkAgentDevice(Long orgId, String deviceSn) {
        //查询是否是渠道商设备
        AgentDeviceRelationDTO agentDeviceRelationDTO = agentDeviceRelationService.getByDeviceSn(
                deviceSn).pickDataThrowException();
        if (agentDeviceRelationDTO == null) {
            //设备不是渠道商设备
            log.warn("渠道商校验 设备不是渠道商设备, deviceSn:{}", deviceSn);
            return false;
        }
        if (!agentDeviceRelationDTO.getDeviceType()
                .equals(AgentDeviceTypeEnum.HY.getDeviceType())) {
            log.warn("渠道商校验 设备不是行业类型设备,agentId:{}, deviceSn:{}",
                    agentDeviceRelationDTO.getAgentId(), deviceSn);
            return false;
        }
        AuthorizeOrgDTO orgDTO = authorizeDataService.getByOrgId(orgId).pickDataThrowException();
        if (orgDTO == null) {
            //客户机构未管控
            log.warn("渠道商校验 机构不是渠道商客户机构,agentId:{}, orgId:{}",
                    agentDeviceRelationDTO.getAgentId(), orgId);
            return false;
        }
        //当前激活客户管控id
        Long globalOrgConfigId = orgDTO.getGlobalOrgConfigId();
        //查询激活机构是否是渠道下的客户机构
        List<AgentOrgRelationDTO> orgRelationList = agentOrgRelationService.getByOrgConfigId(
                globalOrgConfigId).pickDataThrowException();
        if (CollectionUtils.isEmpty(orgRelationList)) {
            log.info("机构和渠道商无关联关系,agentId:{},globalOrgConfigId:{}", agentDeviceRelationDTO.getAgentId(), globalOrgConfigId);
            return false;
        }
        boolean orgBelowAgent = orgRelationList.stream()
                .map(AgentOrgRelationDTO::getAgentId)
                .collect(Collectors.toList())
                .contains(agentDeviceRelationDTO.getAgentId());
        if (!orgBelowAgent) {
            log.info(
                    "机构和渠道商无关联关系,机构不在设备所属渠道商下,agentId:{},globalOrgConfigId:{}",
                    agentDeviceRelationDTO.getAgentId(), globalOrgConfigId);
            return false;
        }
        //判断该设备是否特定给某客户
        AuthorizeDeviceDTO authorizeDeviceDTO = authorizeDeviceDataService.getByDeviceSn(deviceSn)
                .pickDataThrowException();
        if (authorizeDeviceDTO != null && !authorizeDeviceDTO.getGlobalOrgConfigId()
                .equals(globalOrgConfigId)) {
            log.info(
                    "机构和渠道商无关联关系，该设备已特定给某机构,agentId:{},sourceConfigId:{},targetConfigId:{}",
                    agentDeviceRelationDTO.getAgentId(), globalOrgConfigId,
                    authorizeDeviceDTO.getGlobalOrgConfigId());
            return false;
        }

        return true;
    }

    /**
     * 校验设备是钉钉售卖渠道&&商业保护期是否过期
     * @return
     */
    private boolean checkDingChannelAndOverProtectTimeFlag() {
        Date now = new Date();
        if (AuthorizeConstant.SaleChannel.DING.equals(this.deviceDTO.getSaleChannel())
                && now.after(this.deviceDTO.getProtectEndTime())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 校验当前激活机构与设备管控授权机构是否相同
     * @param activateOrgId 当前激活机构id
     * @return
     */
    private boolean checkCurrentAuthorizeOrgFlag(Long activateOrgId) {
        BizAssert.notNull(activateOrgId, "[CheckDeviceAuthorizePipe.checkCurrentOrg] orgId must not be null");
        if (this.deviceDTO == null) {
            return Boolean.FALSE;
        }
        return activateOrgId.equals(deviceDTO.getOrgId());
    }

    /**
     * 校验设备是否在管控列表
     *
     * @param deviceSn 设备SN
     * @return
     */
    private boolean checkDeviceAuthorize(String deviceSn) {
        AuthorizeDeviceDTO authorizeDeviceDTO = authorizeDeviceDataService.getByDeviceSn(deviceSn).pickDataThrowException();
        if (authorizeDeviceDTO != null) {
            this.deviceDTO = authorizeDeviceDTO;
        }
        return authorizeDeviceDTO != null;
    }
    /**
     * 校验SN是否在管控白名单
     * @param deviceSn 设备SN
     * @return
     */
    private boolean checkDeliverWhiteDevice(String deviceSn) {
        List<WhiteDeviceDTO> deliverWhiteDeviceList = deliverWhiteDeviceService.listByDeviceSnList(Lists.newArrayList(deviceSn))
                .pickDataThrowException();
        return CollectionUtils.isNotEmpty(deliverWhiteDeviceList);
    }

    /**
     * 校验机构是否在管控列表
     * @param orgId 机构id
     * @return
     */
    private boolean checkOrg(Long orgId) {
        AuthorizeOrgDTO orgDTO = authorizeDataService.getByOrgId(orgId).pickDataThrowException();
        return orgDTO != null;
    }
}
