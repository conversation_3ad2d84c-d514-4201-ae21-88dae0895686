package com.moredian.magicube.device.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.filemanager.ApkFileManager;
import com.moredian.bee.mybatis.domain.PaginationDomain;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.core.gray.model.GrayRelease;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.convertor.AppVersionConvertor;
import com.moredian.magicube.device.dao.entity.AppVersion;
import com.moredian.magicube.device.dto.version.*;
import com.moredian.magicube.device.manager.AppVersionManager;
import com.moredian.magicube.device.service.AppVersionService;
import com.moredian.ota.api.request.apk.*;
import com.moredian.ota.api.response.apk.OtaApkExtraInfoResponse;
import com.moredian.ota.api.response.apk.OtaApkResponse;
import com.moredian.ota.api.response.apk.OtaApkVersionResponse;
import com.moredian.ota.api.response.apk.OtaApkWithExtraInfoResponse;
import com.moredian.ota.api.service.OtaApkExtraInfoService;
import com.moredian.ota.api.service.OtaApkService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class AppVersionServiceImpl implements AppVersionService {

    @Autowired
    private AppVersionManager appVersionManager;

    @SI
    private OrgService orgService;

    @SI
    private OtaApkService otaApkService;

    @SI
    private OtaApkExtraInfoService otaApkExtraInfoService;

    @Autowired
    private ApkFileManager apkFileManager;

    @Override
    public ServiceResponse<Long> addAppVersion(VersionDTO versionDTO) {
        OtaApkInsertRequest request = new OtaApkInsertRequest();
        request.setVersionCode(versionDTO.getVersionCode());
        request.setApkFileUrl(versionDTO.getAppUrl());
        //随便写死一个包名
        request.setPackageName("MD_46_2.1.54.0_20154_20220324.180131.zip");
        request.setAppType(versionDTO.getAppType());
        request.setApkVersion(String.valueOf(versionDTO.getVersionCode()));
        request.setDescription(versionDTO.getVersionDesc());
        request.setIncrFlag(Boolean.FALSE);
        request.setInitDeviceUpgrade(versionDTO.getIsEnforceUpdate());
        request.setOpenForOrgUser(versionDTO.getIsActive());
        Long id = otaApkService.addApk(request).pickDataThrowException();

        //添加设备apk额外配置
        OtaApkExtraInfoSetRequest extraInfoSetRequest = new OtaApkExtraInfoSetRequest();
        extraInfoSetRequest.setAppType(versionDTO.getAppType());
        extraInfoSetRequest.setVersionCode(versionDTO.getVersionCode());
        if (versionDTO.getEnforcementUpgrade() != null) {
            extraInfoSetRequest.setEnforcementUpgrade(versionDTO.getEnforcementUpgrade());
        }
        if (CollectionUtils.isNotEmpty(versionDTO.getFaceModels())) {
            StringBuilder stringBuilder = new StringBuilder();
            for (String faceModel : versionDTO.getFaceModels()) {
                stringBuilder.append(faceModel).append(",");
            }
            StringBuilder sb = stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            extraInfoSetRequest.setFaceModels(sb.toString());
        }
        otaApkExtraInfoService.setExtraInfo(extraInfoSetRequest).pickDataThrowException();
        return new ServiceResponse<>(id);
    }

    @Override
    public ServiceResponse<Integer> updateAppVersion(VersionDTO VersionDTO) {
        Integer count = appVersionManager.updateAppVersion(AppVersionConvertor.versionDTOToAppVersion(VersionDTO));
        return new ServiceResponse<>(true, null, count);
    }

    @Override
    public ServiceResponse<Integer> updateAppVersionSelective(VersionDTO VersionDTO) {
        Integer count = appVersionManager.updateAppVersionSelective(AppVersionConvertor.versionDTOToAppVersion(VersionDTO));
        return new ServiceResponse<>(true, null, count);
    }

    @Override
    public ServiceResponse<Integer> removeAppVersionById(Long id) {
        otaApkService.deleteApkId(id).pickDataThrowException();
        return new ServiceResponse<>(true, null, 1);
    }

    @Override
    public ServiceResponse<VersionDTO> getAppVersionById(Long id) {
        OtaApkResponse otaApkResponse = otaApkService.findApkById(id).pickDataThrowException();
        VersionDTO versionDto = AppVersionConvertor.otaApkResponseToAppVersionDto(otaApkResponse);
        OtaApkExtraInfoGetRequest extraInfoGetRequest = new OtaApkExtraInfoGetRequest();
        extraInfoGetRequest.setAppType(otaApkResponse.getAppType());
        extraInfoGetRequest.setVersionCode(otaApkResponse.getVersionCode());
        OtaApkExtraInfoResponse otaApkExtraInfoResponse =otaApkExtraInfoService.getExtraInfo(extraInfoGetRequest).pickDataThrowException();
        if (otaApkExtraInfoResponse != null){
            if (StringUtils.isNotBlank(otaApkExtraInfoResponse.getFaceModels())) {
                versionDto.setFaceModels(
                    Arrays.asList(otaApkExtraInfoResponse.getFaceModels().split(",")));
            }
            versionDto.setEnforcementUpgrade(otaApkExtraInfoResponse.getEnforcementUpgrade());
        }
        return new ServiceResponse<>(true, null, versionDto);
    }

    @Override
    public ServiceResponse<VersionDTO> getAppVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode) {
        OtaApkGetByCodeRequest otaApkGetByCodeRequest = new OtaApkGetByCodeRequest();
        otaApkGetByCodeRequest.setAppType(appType);
        otaApkGetByCodeRequest.setVersionCode(versionCode);
        OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse = otaApkService.getApkByAppTypeAndVersionCode(otaApkGetByCodeRequest).pickDataThrowException();
        VersionDTO versionDTO = AppVersionConvertor.otaApkWithExtraInfoToAppVersionDto(otaApkWithExtraInfoResponse);
        if (versionDTO == null){
            versionDTO = new VersionDTO();
            versionDTO.setAppType(appType);
            versionDTO.setSystemType(systemType);
        }
        return (new ServiceResponse<>(true, null, versionDTO));
    }

    @Override
    public ServiceResponse<VersionDTO> getRomVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode, Integer apkAppType) {
        OtaApkGetByCodeRequest otaApkGetByCodeRequest = new OtaApkGetByCodeRequest();
        otaApkGetByCodeRequest.setAppType(appType);
        otaApkGetByCodeRequest.setVersionCode(versionCode);
        otaApkGetByCodeRequest.setApkAppType(apkAppType);
        OtaApkResponse response = otaApkService.getRomByAppTypeAndVersionCode(otaApkGetByCodeRequest).pickDataThrowException();
        VersionDTO versionDTO = AppVersionConvertor.otaApkResponseToAppVersionDto(response);
        if (versionDTO == null) {
            versionDTO = new VersionDTO();
            versionDTO.setAppType(appType);
            versionDTO.setSystemType(systemType);
        }
        return (new ServiceResponse<>(true, null, versionDTO));
    }

    @Override
    public ServiceResponse<VersionDTO> getNewAppVersionBySCType(Integer systemType, Integer appType) {
        OtaNewestApkGetRequest newestApkGetRequest = new OtaNewestApkGetRequest();
        newestApkGetRequest.setAppType(appType);
        OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse = otaApkService.getNewestApk(newestApkGetRequest).pickDataThrowException();
        VersionDTO versionDTO = AppVersionConvertor.otaApkWithExtraInfoToAppVersionDto(otaApkWithExtraInfoResponse);
        if (versionDTO == null){
            versionDTO = new VersionDTO();
            versionDTO.setAppType(appType);
            versionDTO.setSystemType(systemType);
        }
        return new ServiceResponse<>(true, null, versionDTO);
    }

    @Override
    public ServiceResponse<VersionDTO> getNewAppVersionBySCTypeAndIsActive(Integer systemType, Integer appType) {
        OtaNewestApkGetRequest newestApkGetRequest = new OtaNewestApkGetRequest();
        newestApkGetRequest.setAppType(appType);
        OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse = otaApkService.getNewestOpenApk(newestApkGetRequest).pickDataThrowException();
        VersionDTO versionDTO = AppVersionConvertor.otaApkWithExtraInfoToAppVersionDto(otaApkWithExtraInfoResponse);
        if (versionDTO == null){
            versionDTO = new VersionDTO();
            versionDTO.setAppType(appType);
            versionDTO.setSystemType(systemType);
        }
        return new ServiceResponse<>(true, null, versionDTO);
    }

    @Override
    public ServiceResponse<VersionDTO> getNewRomVersionBySCTypeAndIsActive(Integer systemType, Integer appType, Integer apkAppType) {
        OtaNewestApkGetRequest newestApkGetRequest = new OtaNewestApkGetRequest();
        newestApkGetRequest.setAppType(appType);
        newestApkGetRequest.setApkAppType(apkAppType);
        OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse = otaApkService.getNewestOpenApk(newestApkGetRequest).pickDataThrowException();
        VersionDTO versionDTO = AppVersionConvertor.otaApkWithExtraInfoToAppVersionDto(otaApkWithExtraInfoResponse);
        if (versionDTO == null) {
            versionDTO = new VersionDTO();
            versionDTO.setAppType(appType);
            versionDTO.setSystemType(systemType);
        }
        return new ServiceResponse<>(true, null, versionDTO);
    }

    @Override
    public ServiceResponse<VersionDTO> getNewAppVersionBySCTypeAndIsActiveAndOrgId(Long orgId, Integer systemType, Integer clientType) {
        AppVersion appVersion = getNewVersion(orgId, systemType, clientType);
        OtaNewestApkGetRequest newestApkGetRequest = new OtaNewestApkGetRequest();
        newestApkGetRequest.setOrgId(orgId);
        newestApkGetRequest.setAppType(clientType);
        OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse = otaApkService.getNewestOpenApkWithOrgGray(newestApkGetRequest).pickDataThrowException();
        VersionDTO versionDTO = AppVersionConvertor.otaApkWithExtraInfoToAppVersionDto(otaApkWithExtraInfoResponse);
        if (versionDTO == null){
            versionDTO = new VersionDTO();
            versionDTO.setAppType(clientType);
            versionDTO.setSystemType(systemType);
        }
        return new ServiceResponse<>(true, null, versionDTO);
    }

    @Override
    public ServiceResponse<Pagination<VersionDTO>> getPaginationAppVersion(Pagination<VersionDTO> paginationDto, VersionDTO VersionDTO) {
        PaginationDomain<AppVersion> paginationDomain = appVersionManager.getPaginationAppVersion(AppVersionConvertor.paginationVersionDTOToPaginationAppVersion(paginationDto), AppVersionConvertor.versionDTOToAppVersion(VersionDTO));
        return new ServiceResponse<>(true, null, AppVersionConvertor.paginationAppVersionToPaginationVersionDTO(paginationDomain));
    }

    @Override
    public ServiceResponse<Integer> getAppVersionCount(VersionDTO VersionDTO) {
        Integer count = appVersionManager.getAppVersionCount(AppVersionConvertor.versionDTOToAppVersion(VersionDTO));
        return new ServiceResponse<>(true, null, count);
    }

    @Override
    public ServiceResponse changeAppversionToActive(Long appVersionId) {
        AppVersion appVersion = new AppVersion();
        appVersion.setVersionId(appVersionId);
        appVersion.setIsActive(true);
        appVersionManager.updateAppVersionSelective(appVersion);
        return ServiceResponse.createSuccessResponse();
    }

    private AppVersion getNewVersion(Long orgId, Integer systemType, Integer appType) {
        // 获取最新全量版本
        AppVersion appFullVersion = appVersionManager.getNewAppVersionBySCTypeAndIsActive(systemType, appType);

        // 获取灰度版本
        AppVersion appGrayVersion = null;
        GrayRelease grayRelease = orgService.getOrgGrayReleaseVersion(orgId, systemType, appType).pickDataThrowException();

        if (grayRelease != null) {
            appGrayVersion = appVersionManager.getAppVersionById(grayRelease.getGrayVersionId());
        }

        if (appGrayVersion != null && appFullVersion != null && (appGrayVersion.getVersionCode() > appFullVersion.getVersionCode())) {
            log.info("升级灰度版本, {}, {}", JsonUtils.toJson(appGrayVersion), JsonUtils.toJson(appFullVersion));
            return appGrayVersion;
        } else {
            return appFullVersion;
        }
    }

    @Override
    public ServiceResponse<Integer> getLowVersionCount(Integer versionCode, Integer appType) {
        OtaLowOpenApkCountRequest otaLowOpenApkCountRequest = new OtaLowOpenApkCountRequest();
        otaLowOpenApkCountRequest.setAppType(appType);
        otaLowOpenApkCountRequest.setVersionCode(versionCode);
        Integer count = otaApkService.getLowOpenApkCount(otaLowOpenApkCountRequest).pickDataThrowException();
        return new ServiceResponse(count);
    }

    @Override
    public ServiceResponse<VersionDTO> getAppVersionInfo(VersionDTO versionDTO) {
        OtaApkGetByCodeRequest otaApkGetByCodeRequest = new OtaApkGetByCodeRequest();
        otaApkGetByCodeRequest.setVersionCode(versionDTO.getVersionCode());
        otaApkGetByCodeRequest.setAppType(versionDTO.getAppType());
        OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse = otaApkService.getApkByAppTypeAndVersionCode(otaApkGetByCodeRequest).pickDataThrowException();
        return new ServiceResponse<>(true, null,
            AppVersionConvertor.otaApkWithExtraInfoToAppVersionDto(otaApkWithExtraInfoResponse));
    }

    @Override
    public ServiceResponse<Pagination<VersionDTO>> listPage(Integer pageNo, Integer pageSize) {
        Page<AppVersion> page = PageHelper.startPage(pageNo == null ? 1 : pageNo, pageSize == null ? 20 : pageSize)
            .setOrderBy("gmt_modify DESC")
            .doSelectPage(() -> appVersionManager.list());
        Pagination<VersionDTO> dtoPagination = new Pagination<>();
        if (CollectionUtils.isNotEmpty(page)) {
            List<VersionDTO> versionDTOList = new ArrayList<>();
            for (AppVersion appVersion : page.getResult()) {
                VersionDTO versionDTO = new VersionDTO();
                BeanUtils.copyProperties(appVersion, versionDTO);
                versionDTOList.add(versionDTO);
            }
            dtoPagination.setPageNo(page.getPageNum());
            dtoPagination.setPageSize(page.getPageSize());
            dtoPagination.setTotalCount((int) page.getTotal());
            dtoPagination.setData(versionDTOList);
        }
        return new ServiceResponse<>(dtoPagination);
    }

    @Override
    public ServiceResponse<AppUpgradeInfoDTO> getAppUpgradeInfo(ApkAndRomInfoDTO dto) {
        ServiceResponse<AppUpgradeInfoDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        AppUpgradeInfoDTO appUpgradeInfoDto = new AppUpgradeInfoDTO();
        //先获取最新的rom上电强制升级信息
        appUpgradeInfoDto
            .setRomUpgradeInfo(getUpgradeInfo(dto.getRomAppType(), dto.getRomVersionCode()));
        //后获取最新的apk上电强制升级信息
        appUpgradeInfoDto
            .setApkUpgradeInfo(getUpgradeInfo(dto.getApkAppType(), dto.getApkVersionCode()));
        serviceResponse.setData(appUpgradeInfoDto);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<AppUpgradeVersionInfoDTO>> judgeExistHigherApk(List<AppUpgradeVersionDTO> list) {
        ServiceResponse<List<AppUpgradeVersionInfoDTO>> successResponse = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(list)) {
            successResponse.setData(Collections.emptyList());
            return successResponse;
        }
        List<OtaApkVersionRequest> otaApkVersionRequestList = new ArrayList<>(list.size());
        for (AppUpgradeVersionDTO appUpgradeVersion : list) {
            OtaApkVersionRequest request = new OtaApkVersionRequest();
            request.setAppType(appUpgradeVersion.getAppType());
            request.setVersionCode(appUpgradeVersion.getVersionCode());
            otaApkVersionRequestList.add(request);
        }
        List<OtaApkVersionResponse> responseList = otaApkService.judgeExistHigherApk(otaApkVersionRequestList).pickDataThrowException();
        if (CollectionUtils.isEmpty(responseList)) {
            successResponse.setData(Collections.emptyList());
        }
        List<AppUpgradeVersionInfoDTO> appUpgradeVersionResponseList = new ArrayList<>(responseList.size());
        for (OtaApkVersionResponse response : responseList) {
            AppUpgradeVersionInfoDTO appUpgradeVersionResponse = new AppUpgradeVersionInfoDTO();
            appUpgradeVersionResponse.setAppType(response.getAppType());
            appUpgradeVersionResponse.setVersionCode(response.getVersionCode());
            appUpgradeVersionResponse.setHaveHigherApk(response.getHaveHigherApk());
            appUpgradeVersionResponseList.add(appUpgradeVersionResponse);
        }
        successResponse.setData(appUpgradeVersionResponseList);
        return successResponse;
    }

    private AppVersionDetailDTO getUpgradeInfo(Integer appType, Integer versionCode) {
        AppVersionDetailDTO appVersionDetailDto = null;
        OtaApkTopExtraInfoGetRequest otaApkTopExtraInfo = new OtaApkTopExtraInfoGetRequest();
        otaApkTopExtraInfo.setAppType(appType);
        otaApkTopExtraInfo.setVersionCode(versionCode);
        List<OtaApkWithExtraInfoResponse> otaApkExtraInfos = otaApkService.findTopVersionExtraInfo(otaApkTopExtraInfo).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(otaApkExtraInfos)) {
            List<OtaApkResponse> otaApkResponses = new ArrayList<>();
            for (OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse : otaApkExtraInfos) {
                OtaApkExtraInfoResponse otaApkExtraInfoResponse = otaApkWithExtraInfoResponse
                    .getOtaApkExtraInfoResponse();
                OtaApkResponse otaApkResponse = otaApkWithExtraInfoResponse.getOtaApkResponse();
                if (otaApkResponse != null && otaApkExtraInfoResponse != null
                    && YesNoFlag.YES.getValue() == otaApkExtraInfoResponse
                    .getEnforcementUpgrade()) {
                    otaApkResponses.add(otaApkResponse);
                }
            }
            //获取最新版本的上电升级包信息
            if (CollectionUtils.isNotEmpty(otaApkResponses)) {
                appVersionDetailDto = new AppVersionDetailDTO();
                appVersionDetailDto.setAppType(appType);
                appVersionDetailDto.setPreVersion(versionCode);
                OtaApkResponse otaApkResponse = otaApkResponses.get(otaApkResponses.size() - 1);
                if (otaApkResponse != null) {
                    appVersionDetailDto.setNewVersion(otaApkResponse.getVersionCode());
                    if (otaApkResponse.getIncrFlag() != null) {
                        appVersionDetailDto.setIncre(otaApkResponse.getIncrFlag());
                    } else {
                        appVersionDetailDto.setIncre(Boolean.FALSE);
                    }
                    appVersionDetailDto
                        .setUrl(apkFileManager.getApkFileUrl(otaApkResponse.getApkFileUrl()));
                    appVersionDetailDto.setExtendInfo(otaApkResponse.getExtendInfo());
                }
            }
        }
        return appVersionDetailDto;
    }
}