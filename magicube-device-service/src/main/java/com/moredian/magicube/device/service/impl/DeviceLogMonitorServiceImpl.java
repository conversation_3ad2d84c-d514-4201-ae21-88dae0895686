package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.monitor.IMonitorService;
import com.moredian.bee.monitor.MonitorData;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.monitor.MonitorLogDto;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceVersionManager;
import com.moredian.magicube.device.model.MonitorLogModel;
import com.moredian.magicube.device.monitor.CommonLogConstants;
import com.moredian.magicube.device.service.DeviceLogMonitorService;
import com.moredian.magicube.device.utils.MonitorCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/10/13
 */
@SI
@Slf4j
public class DeviceLogMonitorServiceImpl implements DeviceLogMonitorService {

    @SI
    private OrgService orgService;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceVersionManager deviceVersionManager;

    @Autowired
    private IMonitorService monitorService;

    @Override
    public ServiceResponse<Integer> syncMonitorLog(String deviceSn, List<MonitorLogDto> logList) {
        BizAssert.notBlank(deviceSn, "deviceSn must not be blank");
        Device deviceInfo = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.notNull(deviceInfo, "device not exist");
        List<DeviceVersionDTO> deviceVersionDTOList = deviceVersionManager.listApkByOrgIdAndDeviceIds(deviceInfo.getOrgId(), Arrays.asList(deviceInfo.getDeviceId()));
        Long timestamp = System.currentTimeMillis();
        for (MonitorLogDto request : logList) {
            //必传值若没有 不记录日志
            if (StringUtils.isEmpty(request.getEventCode()) || request.getEventTime() == null) {
                continue;
            }
            MonitorLogModel monitorLogModel = new MonitorLogModel();
            monitorLogModel.setOrgId(deviceInfo.getOrgId());
            monitorLogModel.setDeviceSn(deviceInfo.getDeviceSn());
            monitorLogModel.setDeviceType(deviceInfo.getDeviceType());
            if (CollectionUtils.isNotEmpty(deviceVersionDTOList)) {
                DeviceVersionDTO deviceVersionDTO = deviceVersionDTOList.get(0);
                if (deviceVersionDTO != null && deviceVersionDTO.getVersionCode() != null) {
                    monitorLogModel.setAppVersion(deviceVersionDTO.getVersionCode().toString());
                }
            }
            monitorLogModel.setDeviceName(deviceInfo.getDeviceName());
            monitorLogModel.setEventCode(request.getEventCode());
            monitorLogModel.setEventData(request.getEventData());
            monitorLogModel.setEventTime(request.getEventTime());
            monitorLogModel.setIndicatorNum(request.getIndicatorNum());
            monitorLogModel.setPersonId(request.getPersonId());
            monitorLogModel.setFiles(request.getFiles());
            //机构名
            MonitorCacheUtil.MonitorCache monitorCache = MonitorCacheUtil.get(deviceInfo.getOrgId());
            if (monitorCache == null) {
                OrgInfo orgInfo = orgService.getOrgInfo(deviceInfo.getOrgId(), Collections.singletonList(OrgStatus.USABLE.getValue())).pickDataThrowException();
                monitorCache = MonitorCacheUtil.put(orgInfo.getOrgId(), orgInfo.getOrgName());
            }
            monitorLogModel.setOrgName(monitorCache.getOrgName());

            //构造打印监控日志所需对象
            MonitorData monitorData = new MonitorData();
            monitorData.setTimestamp(timestamp);
            monitorData.setOrgId(deviceInfo.getOrgId());
            monitorData.setDeviceSn(deviceInfo.getDeviceSn());
            monitorData.setDeviceId(deviceInfo.getDeviceId());
            monitorData.setData(monitorLogModel);

            //日志形式埋点
            monitorService.addMonitorData(CommonLogConstants.LOG_SYSTEM, CommonLogConstants.LOG_STORE, monitorData);
        }
        return new ServiceResponse<>(0);
    }
}
