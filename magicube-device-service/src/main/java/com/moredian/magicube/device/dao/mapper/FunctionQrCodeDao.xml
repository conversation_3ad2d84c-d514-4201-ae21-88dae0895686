<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.FunctionQrCodeDao">

    <resultMap type="com.moredian.magicube.device.dao.entity.qrcode.FunctionQrCode" id="baseMap">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="memberId" column="member_id"/>
        <result property="treeId" column="tree_id"/>
        <result property="treeName" column="tree_name"/>
        <result property="pathTreeName" column="path_tree_name"/>
        <result property="qrCodeName" column="qr_code_name"/>
        <result property="deviceAddressName" column="device_address_name"/>
        <result property="content" column="content"/>
        <result property="networkType" column="network_type"/>
        <result property="connectType" column="connect_type"/>
        <result property="privateCloudSwitch" column="private_cloud_switch"/>
        <result property="cableStatic" column="cable_static"/>
        <result property="wifiInfo" column="wifi_info"/>
        <result property="privateCloud" column="private_cloud"/>
        <result property="url" column="url"/>
        <result property="roleType" column="role_type"/>
        <result property="functionType" column="function_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="newActivateTime" column="new_activate_time"/>
        <result property="source" column="source" />
        <result property="orgNetworkId" column="org_network_id" />
        <result property="direction" column="direction"/>
    </resultMap>

    <sql id="sql_base_column">
        id,
        org_id,
        member_id,
        tree_id,
        tree_name,
        path_tree_name,
        qr_code_name,
        device_address_name,
        content,
        function_type,
        network_type,
        connect_type,
        private_cloud_switch,
        cable_static,
        wifi_info,
        private_cloud,
        url,
        role_type,
        gmt_create,
        gmt_modify,
        new_activate_time,
        source,
        org_network_id,
        direction
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.qrcode.FunctionQrCode">
        insert into hive_function_qr_code(
        <include refid="sql_base_column"/>
        )values (
        #{id},
        #{orgId},
        #{memberId},
        #{treeId},
        #{treeName},
        #{pathTreeName},
        #{qrCodeName},
        #{deviceAddressName},
        #{content},
        #{functionType},
        #{networkType},
        #{connectType},
        #{privateCloudSwitch},
        #{cableStatic},
        #{wifiInfo},
        #{privateCloud},
        #{url},
        #{roleType},
        now(3),
        now(3),
        now(3),
        #{source},
        #{orgNetworkId},
        #{direction}
        )
    </insert>

    <select id="getById" resultMap="baseMap">
        select
        <include refid="sql_base_column"/>
        from hive_function_qr_code
        where member_id = #{memberId} and id = #{id}
    </select>

    <select id="listByMemberId" resultMap="baseMap">
        select
        <include refid="sql_base_column"/>
        from hive_function_qr_code
        where function_type in
        <foreach collection="functionTypeList"  close=")" open="(" separator="," item="functionType">
            #{functionType}
        </foreach>
        and role_type = #{roleType}
        <if test="memberId != null">
            and member_id = #{memberId}
        </if>
        <if test="source != null">
            and source = #{source}
        </if>
        order by new_activate_time desc;
    </select>

    <select id="getByIdAndRoleType" resultMap="baseMap">
        select
        <include refid="sql_base_column"/>
        from hive_function_qr_code
        where id = #{id}
        <if test="roleType != null">
            and role_type = #{roleType}
        </if>

    </select>

    <update id="updateNewActivateTime">
        update
        hive_function_qr_code
        set new_activate_time = #{newActivateTime},gmt_modify = now(3)
        where id = #{id}
    </update>

    <select id="getActivateQrCodeById" resultMap="baseMap">
        select
        <include refid="sql_base_column"/>
        from hive_function_qr_code
        where id = #{qrCodeId}
    </select>

</mapper>