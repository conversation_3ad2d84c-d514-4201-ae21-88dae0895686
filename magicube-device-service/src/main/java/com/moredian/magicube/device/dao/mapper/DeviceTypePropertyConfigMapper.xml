<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceTypePropertyConfigMapper">

  <resultMap type="com.moredian.magicube.device.dao.entity.DeviceTypePropertyConfig" id="BaseResultMap">
    <result property="id" column="id" />
    <result property="deviceType" column="device_type" />
    <result property="propertyKey" column="property_key" />
    <result property="propertyJson" column="property_json" />
    <result property="sort" column="sort" />
    <result property="visible" column="visible" />
    <result property="delFlag" column="del_flag" />
    <result property="gmtCreate" column="gmt_create" />
    <result property="gmtModify" column="gmt_modify" />
  </resultMap>

  <sql id="sql_table">
    hive_device_type_property_config
  </sql>

  <sql  id="sql_columns">
    id,
    device_type,
    property_key,
    property_json,
    sort,
    visible,
    del_flag,
    gmt_create,
    gmt_modify
  </sql>

  <sql  id="sql_values">
    #{id},
    #{deviceType},
    #{propertyKey},
    #{propertyJson},
    #{sort},
    #{visible},
    #{delFlag},
    now(3),
    now(3)
  </sql>

  <sql  id="sql_where">
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="deviceType != null">
        and device_type = #{deviceType}
      </if>
      <if test="propertyKey != null and propertyKey != ''">
        and property_key = #{propertyKey}
      </if>
      <if test="propertyJson != null and propertyJson != ''">
        and property_json = #{propertyJson}
      </if>
      <if test="sort != null">
        and sort = #{sort}
      </if>
      <if test="visible != null">
        and visible = #{visible}
      </if>
      <if test="delFlag != null">
        and del_flag = #{delFlag}
      </if>
      <if test="gmtCreate != null">
        and gmt_create = #{gmtCreate}
      </if>
      <if test="gmtModify != null">
        and gmt_modify = #{gmtModify}
      </if>
    </where>
  </sql>

  <!-- 通过实体作为筛选条件查询-->
  <select id="listByEntity" resultMap="BaseResultMap"  parameterType="com.moredian.magicube.device.dao.entity.DeviceTypePropertyConfig">
    SELECT <include refid="sql_columns"/>
    FROM <include refid="sql_table"/>
    <include refid="sql_where"/>
  </select>
</mapper>
