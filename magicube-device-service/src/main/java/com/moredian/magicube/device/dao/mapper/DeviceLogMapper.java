package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceLogMapper {

    /**
     * 新增设备日志
     *
     * @param deviceLog 设备日志信息
     * @return
     */
    int insert(DeviceLog deviceLog);

    /**
     * 更新设备日志
     *
     * @param deviceLog 设备日志信息
     * @return
     */
    void update(DeviceLog deviceLog);

    /**
     * 根据设备日志Id获取设备日志信息
     *
     * @param orgId       机构号
     * @param deviceLogId 设备日志Id
     * @return
     */
    DeviceLog getByOrgIdAndId(@Param("orgId") Long orgId, @Param("deviceLogId") Long deviceLogId);

    /**
     * 根据设备Id获取设备日志信息列表
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    List<DeviceLog> listByOrgIdAndDeviceId(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId);

    /**
     * 根据设备Id获取设备日志信息列表
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    DeviceLog getLatestByOrgIdAndDeviceSnAndType(@Param("orgId") Long orgId,
        @Param("deviceSn") String deviceSn,
        @Param("eventType") Integer eventType);

    /**
     * 根据设备sn获取设备日志信息列表
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    List<DeviceLog> listByOrgIdAndDeviceSn(@Param("orgId") Long orgId, @Param("deviceSn") String deviceSn);

    /**
     * 根据机构号获取设备日志信息类型去重列表
     *
     * @param orgId 机构号
     * @return
     */
    List<Integer> listDistinctDeviceTypeByOrgId(@Param("orgId") Long orgId);

    /**
     * 根据机构号统计设备日志数量
     *
     * @param orgId 机构号
     * @return
     */
    Integer countByOrgId(@Param("orgId") Long orgId);

    /**
     * 根据设备sn获取设备日志信息列表
     *
     * @param deviceSn 设备sn
     * @return
     */
    List<DeviceLog> listByDeviceSn(@Param("deviceSn") String deviceSn);
}
