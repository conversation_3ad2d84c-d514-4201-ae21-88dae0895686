package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.PeopleNumberStatistic;
import com.moredian.magicube.device.dto.carme.InsertPeopleNumStatisticDTO;
import com.moredian.magicube.device.dto.carme.PeopleNumberDTO;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.PeopleNumberStatisticManager;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.PeopleNumberStatisticService;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */

@SI
public class PeopleNumberStatisticServiceImpl implements PeopleNumberStatisticService {

    @Autowired
    private PeopleNumberStatisticManager peopleNumberStatisticManager;

    @SI
    private SpaceTreeDeviceRelationService treeDeviceRelationService;

    @SI
    private DeviceService deviceService;

    @Override
    public ServiceResponse<Long> insert(InsertPeopleNumStatisticDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        PeopleNumberStatistic peopleNumberStatistic = new PeopleNumberStatistic();
        BeanUtils.copyProperties(dto, peopleNumberStatistic);
        serviceResponse.setData(peopleNumberStatisticManager.insert(peopleNumberStatistic));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<PeopleNumberDTO>> listByOrgIdAndDeviceSns(Long orgId,
        List<String> deviceSns) {
        ServiceResponse<List<PeopleNumberDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<PeopleNumberStatistic> list = peopleNumberStatisticManager
            .listByOrgIdAndDeviceSns(orgId, deviceSns);
        List<PeopleNumberDTO> peopleNumberDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> deviceIds = list.stream().map(device ->
                String.valueOf(device.getDeviceId())).collect(Collectors.toList());
            List<TreeDeviceRelationDTO> relationDTOS = treeDeviceRelationService
                .listByDeviceIds(orgId, deviceIds).pickDataThrowException();
            Map<Long, TreeDeviceRelationDTO> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(relationDTOS)) {
                map = relationDTOS.stream().collect(Collectors.
                    toMap(TreeDeviceRelationDTO::getDeviceId, Function.identity()));
            }

            for (PeopleNumberStatistic peopleNumberStatistic : list) {
                PeopleNumberDTO peopleNumberDTO = new PeopleNumberDTO();
                TreeDeviceRelationDTO relation = map.get(peopleNumberStatistic.getDeviceId());
                if (relation == null) {
                    continue;
                }
                peopleNumberDTO.setTreeId(relation.getTreeId());
                BeanUtils.copyProperties(peopleNumberStatistic, peopleNumberDTO);
                peopleNumberDTOS.add(peopleNumberDTO);
            }
        }
        serviceResponse.setData(peopleNumberDTOS);
        return serviceResponse;
    }
}
