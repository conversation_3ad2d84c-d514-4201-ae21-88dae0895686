<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceConfigMapper">
  <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceConfig">
    <result column="device_config_id" property="deviceConfigId"/>
    <result column="device_sn" property="deviceSn"/>
    <result column="xml_config" property="xmlConfig"/>
    <result column="config_signature" property="configSignature"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <sql id="sql_table">
        hive_device_config
    </sql>

  <sql id="sql_columns">
        device_config_id,
        device_sn,
        xml_config,
        config_signature,
        gmt_create,
        gmt_modify
    </sql>

  <sql id="sql_values">
        #{deviceConfigId},
        #{deviceSn},
        #{xmlConfig},
        #{configSignature},
        now(3),
        now(3)
    </sql>

  <insert id="insertOrUpdate" parameterType="com.moredian.magicube.device.dao.entity.DeviceConfig">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
    ON duplicate KEY UPDATE xml_config = #{xmlConfig}, config_signature=#{configSignature}, gmt_modify=now(3)
  </insert>

  <select id="getByDeviceSn" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where
    device_sn = #{deviceSn}
  </select>

  <delete id="deleteBySn" parameterType="string">
    delete from
    <include refid="sql_table"/>
    where
    device_sn = #{deviceSn}
  </delete>

  <select id="getConfigSignatureByDeviceSn" resultType="java.lang.String">
    select config_signature from
    <include refid="sql_table"/>
    where device_sn = #{deviceSn}
  </select>
</mapper>