package com.moredian.magicube.device.service.impl;

import com.google.common.collect.Lists;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceSubject;
import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.subject.DeviceSubjectDTO;
import com.moredian.magicube.device.dto.subject.DeviceSubjectSimpleDTO;
import com.moredian.magicube.device.manager.DeviceSubjectManager;
import com.moredian.magicube.device.manager.DeviceSubjectRelationManager;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceSubjectRelationService;
import com.moredian.magicube.device.service.DeviceSubjectService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @since 2023-08-28
 */

@SI
public class DeviceSubjectRelationServiceImpl implements DeviceSubjectRelationService {

    @SI
    private DeviceService deviceService;

    @Autowired
    private DeviceSubjectRelationManager deviceSubjectRelationManager;

    @Autowired
    private DeviceSubjectService deviceSubjectService;

    @Override
    public ServiceResponse<Boolean> resetRelationBySubjectId(Long orgId, Long subjectId,
        List<Long> deviceIds) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        Boolean result = deviceSubjectRelationManager.
            resetRelationBySubjectId(orgId, subjectId, deviceIds,new ArrayList<>());
        serviceResponse.setData(result);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceInfoDTO>> checkDevice(Long orgId, List<Long> deviceIds,
        Long subjectId,Integer type) {
        if (type==null){
            type = 1;
        }
        ServiceResponse<List<DeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceSubjectRelation> relations = deviceSubjectRelationManager
            .listByOrgIdAndDeviceIds(orgId, deviceIds, type);
        List<DeviceInfoDTO> devices = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            List<Long> deviceIdList = relations.stream().map(DeviceSubjectRelation::getDeviceId)
                .distinct().collect(Collectors.toList());
            Map<Long, DeviceInfoDTO> deviceIdToDeviceInfoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                List<DeviceInfoDTO> deviceInfoDTOS = deviceService
                    .listByOrgIdAndIds(orgId, deviceIdList).pickDataThrowException();
                if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
                    deviceIdToDeviceInfoMap = deviceInfoDTOS.stream()
                        .collect(Collectors.toMap(DeviceInfoDTO::getDeviceId, x -> x));
                }
            }
            for (DeviceSubjectRelation relation : relations) {
                if (subjectId != null && subjectId.equals(relation.getSubjectId())) {
                    continue;
                }
                if (deviceIdToDeviceInfoMap.get(relation.getDeviceId()) != null) {
                    devices.add(deviceIdToDeviceInfoMap.get(relation.getDeviceId()));
                }
            }
        }
        serviceResponse.setData(devices);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceSubjectSimpleDTO>> getDeviceSubjectByOrgIdAndDeviceId(
        Long orgId, Long deviceId,Integer type) {
        ServiceResponse<List<DeviceSubjectSimpleDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceSubjectRelation> relations = deviceSubjectRelationManager
            .listByOrgIdAndDeviceIds(orgId, Lists.newArrayList(deviceId), type);
        if (CollectionUtils.isEmpty(relations)) {
            return serviceResponse;
        }
        List<Long> subjectIds = relations.stream().map(DeviceSubjectRelation::getSubjectId)
            .distinct().collect(Collectors.toList());
        List<DeviceSubjectSimpleDTO> subjectSimpleDTOS = deviceSubjectService.
            listSimpleByOrgIdAndSubjectIds(orgId, subjectIds).pickDataThrowException();
        serviceResponse.setData(subjectSimpleDTOS);
        return serviceResponse;
    }

}
