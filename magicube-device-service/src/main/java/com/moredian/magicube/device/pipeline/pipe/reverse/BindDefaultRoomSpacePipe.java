package com.moredian.magicube.device.pipeline.pipe.reverse;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.SceneType;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.space.dto.device.ActiveDeviceSceneTreeDTO;
import com.moredian.space.dto.device.ActiveDeviceTreeDTO;
import com.moredian.space.dto.device.BindSpaceTreeDto;
import com.moredian.space.service.SpaceTreeService;
import com.moredian.space.service.v2.SpaceTreeDeviceRelationServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 设备绑定默认会议空间管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BindDefaultRoomSpacePipe extends
    AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private SpaceTreeService spaceTreeService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        Device device = (context.getDevice() == null ? new Device() : context.getDevice());
        ActiveDeviceSceneTreeDTO activeDeviceSceneTreeDTO = new ActiveDeviceSceneTreeDTO();
        activeDeviceSceneTreeDTO.setOrgId(context.getOrgId());
        activeDeviceSceneTreeDTO.setDeviceId(device.getDeviceId());
        activeDeviceSceneTreeDTO.setDeviceSn(device.getDeviceSn());
        activeDeviceSceneTreeDTO.setSceneType(SceneTypeEnums.MEETING_ROOM_MODEL.getValue());
        activeDeviceSceneTreeDTO.setSendMsgFlag(Boolean.FALSE);
        ActiveDeviceTreeDTO info = spaceTreeService.createTreeBySceneTypeWithoutLevel(
            activeDeviceSceneTreeDTO).pickDataThrowException();
        if (info != null) {
            context.setTreeDeviceRelationId(info.getTreeDeviceRelationId());
            context.setNewTreeId(info.getTreeId());
        }
    }
}
