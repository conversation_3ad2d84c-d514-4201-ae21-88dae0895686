package com.moredian.magicube.device.monitor;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * 激活解绑埋点数据
 *@description:
 *@author: gongchang
 *@time: 2023-06-13 14:32
 *
 */
@Getter
@Setter
public class ActiveUnbindMonitorObj implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     * @see CommonLogConstants#MONITOR_BIZ_ACTIVE_DEVICE
     * @see CommonLogConstants#MONITOR_BIZ_UNBIND_DEVICE
     */
    private String bizType;


    public ActiveUnbindMonitorObj(String bizType){
        this.bizType = bizType;
    }
}
