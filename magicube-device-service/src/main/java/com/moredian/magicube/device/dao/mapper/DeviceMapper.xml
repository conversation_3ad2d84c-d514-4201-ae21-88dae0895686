<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceMapper">
  <resultMap id="deviceResultMap" type="com.moredian.magicube.device.dao.entity.Device">
    <result column="orgi_equipment_id" property="deviceId"/>
    <result column="org_id" property="orgId"/>
    <result column="third_device_id" property="thirdDeviceId"/>
    <result column="sub_org_id" property="positionId"/>
    <result column="memo" property="position"/>
    <result column="equipment_type" property="deviceType"/>
    <result column="equipment_name" property="deviceName"/>
    <result column="unique_number" property="deviceSn"/>
    <result column="active_code" property="activeCode"/>
    <result column="gmt_active_code" property="activeTime"/>
    <result column="mac_address" property="macAddress"/>
    <result column="bluetooth_mac" property="bluetoothMac"/>
    <result column="status" property="status"/>
    <result column="extends_info" property="extendsInfo"/>
    <result column="capacity_exceeded" property="capacityExceeded"/>
    <result column="parent_unique_number" property="parentDeviceSn"/>
    <result column="app_code" property="appCode"/>
    <result column="app_code_list" property="appCodeList"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
    <result column="virtual_flag" property="virtualFlag"/>
    <result column="device_flag" property="deviceFlag"/>
    <result column="device_time_zone" property="deviceTimeZone"/>
    <result column="device_language" property="deviceLanguage"/>
  </resultMap>

  <sql id="sql_table">
        hive_orgi_equipment
    </sql>

  <sql id="sql_columns">
        orgi_equipment_id,
        org_id,
        third_device_id,
        sub_org_id,
        memo,
        equipment_type,
        equipment_name,
        unique_number,
        active_code,
        gmt_active_code,
        status,
        extends_info,
        capacity_exceeded,
        bluetooth_mac,
        parent_unique_number,
        app_code,
        app_code_list,
        gmt_create,
        gmt_modify,
        virtual_flag,
        device_flag,
        device_time_zone,
        device_language
    </sql>

  <sql id="sql_values">
        #{deviceId},
        #{orgId},
        #{thirdDeviceId},
        #{positionId},
        #{position},
        #{deviceType},
        #{deviceName},
        #{deviceSn},
        #{activeCode},
        #{activeTime},
        #{status},
        #{extendsInfo},
        #{capacityExceeded},
        #{bluetoothMac},
        #{parentDeviceSn},
        #{appCode},
        #{appCodeList},
        now(3),
        now(3),
        #{virtualFlag},
        #{deviceFlag}
    </sql>

  <sql id="batch_sql_values">
    #{item.deviceId},
    #{item.orgId},
    #{item.thirdDeviceId},
    #{item.positionId},
    #{item.position},
    #{item.deviceType},
    #{item.deviceName},
    #{item.deviceSn},
    #{item.activeCode},
    #{item.activeTime},
    #{item.status},
    #{item.extendsInfo},
    #{item.capacityExceeded},
    #{item.bluetoothMac},
    #{item.parentDeviceSn},
    #{item.appCode},
    #{item.appCodeList},
    now(3),
    now(3),
    #{item.virtualFlag},
    #{item.deviceFlag}
  </sql>

  <sql id="condition_sql_where">
    hive_orgi_equipment
    <where>
      <if test="orgId != null">
        and org_id = #{orgId}
      </if>
      <if test="thirdDeviceId != null">
        and third_device_id = #{thirdDeviceId}
      </if>
      <if test="deviceName != null">
        and equipment_name like CONCAT('%',#{deviceName},'%')
      </if>
      <if test="positionId != null">
        and sub_org_id = #{positionId}
      </if>
      <if test="deviceType != null">
        and equipment_type = #{deviceType}
      </if>
      <if test="deviceSn != null">
        and unique_number like CONCAT('%',#{deviceSn},'%')
      </if>
      <if test="keywords != null">
        and (equipment_name like CONCAT('%',#{keywords},'%')
        or unique_number like CONCAT('%',#{keywords},'%'))
      </if>
      <if test="statusList != null and statusList.size() > 0">
        and status in
        <foreach collection="statusList" index="index" item="status" open="(" separator=","
          close=")">
          #{status}
        </foreach>
      </if>
      <if test="deviceTypeList != null and deviceTypeList.size() > 0">
        and equipment_type in
        <foreach collection="deviceTypeList" index="index" item="deviceType" open="(" separator=","
          close=")">
          #{deviceType}
        </foreach>
      </if>
      <if test="deviceIdList != null and deviceIdList.size() > 0">
        and orgi_equipment_id in
        <foreach collection="deviceIdList" index="index" item="deviceId" open="(" separator=","
          close=")">
          #{deviceId}
        </foreach>
      </if>
      <if test="capacityExceeded != null">
        and capacity_exceeded = #{capacityExceeded}
      </if>
    </where>
  </sql>

  <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.Device">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>
  <insert id="batchInsert">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    <foreach collection="devices" open="" item="item" separator="," close="">
      (
        <include refid="batch_sql_values"/>
      )
    </foreach>
  </insert>

  <update id="update" parameterType="com.moredian.magicube.device.dao.entity.Device">
    update
    <include refid="sql_table"/>
    <set>
      <if test="positionId != null">
        sub_org_id = #{positionId},
      </if>
      <if test="position != null">
        memo = #{position},
      </if>
      <if test="deviceName != null">
        equipment_name = #{deviceName},
      </if>
      <if test="activeCode != null">
        active_code = #{activeCode},
      </if>
      <if test="activeTime != null">
        gmt_active_code = #{activeTime},
      </if>
      <if test="extendsInfo != null">
        extends_info = #{extendsInfo},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="capacityExceeded != null">
        capacity_exceeded = #{capacityExceeded},
      </if>
      <if test="appCode != null">
        app_code = #{appCode},
      </if>
      <if test="appCodeList != null">
        app_code_list = #{appCodeList},
      </if>
      <if test="deviceTimeZone != null">
        device_time_zone = #{deviceTimeZone},
      </if>
      <if test="deviceLanguage != null">
        device_language = #{deviceLanguage},
      </if>
      gmt_modify = now(3)
    </set>
    where orgi_equipment_id = #{deviceId}
  </update>

  <update id="batchUpdate" parameterType="java.util.List">
    update
    <include refid="sql_table"/>
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="equipment_name =case" suffix="end,">
        <foreach collection="devices" item="item" index="index">
          <if test="item.deviceName != null and item.deviceName != ''">
            when orgi_equipment_id = #{item.deviceId} then #{item.deviceName}
          </if>
        </foreach>
      </trim>
      <trim prefix="capacity_exceeded =case" suffix="end,">
        <foreach collection="devices" item="item" index="index">
          when orgi_equipment_id = #{item.deviceId} then #{item.capacityExceeded}
        </foreach>
      </trim>
    </trim>
    where
    <foreach collection="devices" separator="or" item="item" index="index">
      orgi_equipment_id=#{item.deviceId}
    </foreach>
  </update>


  <select id="getByDeviceSn" parameterType="string" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where unique_number = #{deviceSn}
  </select>

  <select id="getByOrgAndDeviceSn" parameterType="map" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and unique_number = #{deviceSn}
  </select>

  <select id="listByDeviceSns" parameterType="string" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where
    <if test="deviceSns != null and deviceSns.size() > 0">
      unique_number in
      <foreach collection="deviceSns" index="index" item="deviceSn" open="(" separator=","
        close=")">
        #{deviceSn}
      </foreach>
    </if>
    order by gmt_create desc
  </select>

  <select id="listByOrgIdAndDeviceSns" parameterType="map" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceSns != null and deviceSns.size() > 0">
      and unique_number in
      <foreach collection="deviceSns" index="index" item="deviceSn" open="(" separator=","
        close=")">
        #{deviceSn}
      </foreach>
    </if>
    order by gmt_create desc
  </select>

  <select id="getById" parameterType="long" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where orgi_equipment_id = #{deviceId}
  </select>

  <select id="getByOrgIdAndId" parameterType="long" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and orgi_equipment_id = #{deviceId}
  </select>

  <select id="listByIds" parameterType="long" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where
    <if test="deviceIds != null and deviceIds.size() > 0">
      orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    order by gmt_create desc
  </select>

  <select id="listByOrgIdAndIds" parameterType="long" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    order by gmt_create desc
  </select>

  <select id="listByOrgId" parameterType="map" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
  </select>

  <delete id="deleteById" parameterType="long">
    delete from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and orgi_equipment_id = #{deviceId}
  </delete>
  <delete id="deleteByOrgIdAndParentSns">
    delete from <include refid="sql_table"/>
    where org_id = #{orgId}
    and parent_unique_number in
    <foreach collection="parentSns" open="(" item="item" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <delete id="batchDeleteByOrgIdAndDeviceId">
    delete from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and orgi_equipment_id in
    <foreach collection="deviceId" index="index" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="listByCondition" resultMap="deviceResultMap"
    parameterType="com.moredian.magicube.device.dto.device.QueryDeviceDTO">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceName != null">
      and equipment_name like CONCAT('%',#{deviceName},'%')
    </if>
    <if test="deviceSn != null">
      and unique_number like CONCAT('%',#{deviceSn},'%')
    </if>
    <if test="macAddress != null">
      and mac_address like CONCAT('%',#{macAddress},'%')
    </if>
    <if test="keywords != null">
      and (equipment_name like CONCAT('%',#{keywords},'%')
      or unique_number like CONCAT('%',#{keywords},'%'))
    </if>
    <if test="statusList != null and statusList.size() > 0">
      and status in
      <foreach collection="statusList" index="index" item="status" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="capacityExceeded != null">
      and capacity_exceeded = #{capacityExceeded}
    </if>
    <if test="sort == 0">
      order by equipment_name asc
    </if>
    <if test="sort == 1">
      order by gmt_create desc
    </if>
    <if test="sort == 2">
      order by gmt_create asc
    </if>
  </select>

  <select id="listPageByCondition" resultMap="deviceResultMap"
    parameterType="com.moredian.magicube.device.dto.device.QueryDeviceDTO">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="keywords != null">
      and (memo like CONCAT('%',#{keywords},'%')
      or unique_number like CONCAT('%',#{keywords},'%')
      <if test="queryDeviceIds != null and queryDeviceIds.size() > 0">
        or orgi_equipment_id in
        <foreach collection="queryDeviceIds" index="index" item="queryDeviceId" open="("
          separator=","
          close=")">
          #{queryDeviceId}
        </foreach>
      </if>
      )
    </if>
    <if test="statusList != null and statusList.size() > 0">
      and status in
      <foreach collection="statusList" index="index" item="status" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type not in
      <foreach collection="iotDeviceTypes" index="index" item="item" open="(" separator=","
        close=")">
        #{item}
      </foreach>
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="capacityExceeded != null">
      and capacity_exceeded = #{capacityExceeded}
    </if>
    <if test="deviceSn != null">
      and unique_number = #{deviceSn}
    </if>
    <if test="appCode != null and appCode != ''">
      and app_code_list like CONCAT('%', #{appCode}, '%')
    </if>
    <if test="sort == 0">
      order by equipment_name asc
    </if>
    <if test="sort == 1">
      order by gmt_create desc
    </if>
    <if test="sort == 2">
      order by gmt_create asc
    </if>

  </select>

  <select id="listIotDevice" resultMap="deviceResultMap"
    parameterType="com.moredian.magicube.device.dto.device.QueryIotDeviceDTO">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId} and (virtual_flag = 0 or virtual_flag is null)
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="iotDeviceTypes" index="index" item="item" open="(" separator=","
        close=")">
        #{item}
      </foreach>
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="parentDeviceId != null and parentDeviceId != ''">
      and parent_unique_number = #{parentDeviceId}
    </if>
    <if test="parentDeviceId == null or parentDeviceId == ''">
      and (parent_unique_number is null or parent_unique_number = '')
    </if>
    <if test="sort == 0">
      order by equipment_name asc
    </if>
    <if test="sort == 1">
      order by gmt_create desc
    </if>
    <if test="sort == 2">
      order by gmt_create asc
    </if>
  </select>

  <select id="listIotByConditionPage" resultMap="deviceResultMap"
    parameterType="com.moredian.magicube.device.dto.device.PageQueryIotDeviceDTO">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="virtualFlag == null || virtualFlag == 0">
      and (virtual_flag = 0 or virtual_flag is null)
    </if>
    <if test="keywords != null">
      and (memo like CONCAT('%',#{keywords},'%')
      or unique_number like CONCAT('%',#{keywords},'%')
      <if test="queryDeviceIds != null and queryDeviceIds.size() > 0">
        or orgi_equipment_id in
        <foreach collection="queryDeviceIds" index="index" item="queryDeviceId" open="("
          separator=","
          close=")">
          #{queryDeviceId}
        </foreach>
      </if>
      )
    </if>
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="iotDeviceTypes" index="index" item="item" open="(" separator=","
        close=")">
        #{item}
      </foreach>
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="sort == 0">
      order by equipment_name asc
    </if>
    <if test="sort == 1">
      order by gmt_create desc
    </if>
    <if test="sort == 2">
      order by gmt_create asc
    </if>
  </select>

  <select id="parkPageByCondition" resultMap="deviceResultMap"
    parameterType="com.moredian.magicube.device.dto.device.QueryDeviceDTO">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id in
    <foreach collection="orgIds" index="index" item="orgId" open="(" separator="," close=")">
      #{orgId}
    </foreach>
    <if test="keywords != null">
      and (memo like CONCAT('%',#{keywords},'%')
      or unique_number like CONCAT('%',#{keywords},'%')
      or equipment_name like CONCAT('%',#{keywords},'%')
      <if test="queryDeviceIds != null and queryDeviceIds.size() > 0">
        or orgi_equipment_id in
        <foreach collection="queryDeviceIds" index="index" item="queryDeviceId" open="("
          separator=","
          close=")">
          #{queryDeviceId}
        </foreach>
      </if>
      )
    </if>
    <if test="statusList != null and statusList.size() > 0">
      and status in
      <foreach collection="statusList" index="index" item="status" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type not in
      <foreach collection="iotDeviceTypes" index="index" item="item" open="(" separator=","
        close=")">
        #{item}
      </foreach>
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="capacityExceeded != null">
      and capacity_exceeded = #{capacityExceeded}
    </if>
    <if test="deviceSn != null">
      and unique_number = #{deviceSn}
    </if>
    <if test="appCode != null and appCode != ''">
      and app_code_list like CONCAT('%', #{appCode}, '%')
    </if>
    <if test="sort == 0">
      order by equipment_name asc
    </if>
    <if test="sort == 1">
      order by gmt_create desc
    </if>
    <if test="sort == 2">
      order by gmt_create asc
    </if>
  </select>

  <select id="listPageByConditionV2" resultMap="deviceResultMap"
    parameterType="com.moredian.magicube.device.dto.device.QueryDeviceDTO">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="keywords != null">
      and (memo like CONCAT('%',#{keywords},'%')
      or unique_number like CONCAT('%',#{keywords},'%')
      or equipment_name like CONCAT('%',#{keywords},'%')
      <if test="queryDeviceIds != null and queryDeviceIds.size() > 0">
        or orgi_equipment_id in
        <foreach collection="queryDeviceIds" index="index" item="queryDeviceId" open="("
          separator=","
          close=")">
          #{queryDeviceId}
        </foreach>
      </if>
      )
    </if>
    <if test="statusList != null and statusList.size() > 0">
      and status in
      <foreach collection="statusList" index="index" item="status" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type not in
      <foreach collection="iotDeviceTypes" index="index" item="item" open="(" separator=","
        close=")">
        #{item}
      </foreach>
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="capacityExceeded != null">
      and capacity_exceeded = #{capacityExceeded}
    </if>
    <if test="deviceSn != null">
      and unique_number = #{deviceSn}
    </if>
    <if test="sort == 0">
      order by equipment_name asc
    </if>
    <if test="sort == 1">
      order by gmt_create desc
    </if>
    <if test="sort == 2">
      order by gmt_create asc
    </if>
  </select>

  <select id="getByOrgIdAndDeviceName" parameterType="map" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and equipment_name = #{deviceName}
  </select>

  <select id="listDeviceIdByLikeName" parameterType="map" resultType="long">
    select orgi_equipment_id
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and equipment_name like CONCAT('%',#{keywords},'%')
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type not in
      <foreach collection="iotDeviceTypes" open="(" item="item" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="listDeviceNameByIds" parameterType="long" resultType="string">
    select equipment_name
    from
    <include refid="sql_table"/>
    where
    <if test="deviceIds != null and deviceIds.size() > 0">
      orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
  </select>

  <select id="listDeviceIdByOrgIdAndType" parameterType="map" resultType="long">
    select orgi_equipment_id
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and equipment_type = #{deviceType}
  </select>

  <select id="listByType" parameterType="int" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where equipment_type = #{deviceType}
  </select>

  <select id="listByOrgIdAndType" parameterType="map" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and equipment_type = #{deviceType}
  </select>

  <select id="listByOrgIdAndTypes" parameterType="map" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
  </select>

  <select id="listByLikeCondition"
    parameterType="com.moredian.magicube.device.dto.device.QueryDeviceDTO"
    resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type not in
      <foreach collection="iotDeviceTypes" index="index" item="item" open="(" separator=","
        close=")">
        #{item}
      </foreach>
    </if>
    <if test="keywords != null">
      and (memo like CONCAT('%',#{keywords},'%')
      or unique_number like CONCAT('%',#{keywords},'%')
      <if test="queryDeviceIds != null and queryDeviceIds.size() > 0">
        or orgi_equipment_id in
        <foreach collection="queryDeviceIds" index="index" item="queryDeviceId" open="("
          separator=","
          close=")">
          #{queryDeviceId}
        </foreach>
      </if>
      )
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
  </select>

  <select id="listByDeviceSnListAndDeviceName"
          parameterType="com.moredian.magicube.device.dto.device.QuerySpecificDeviceDTO" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceSnList != null and deviceSnList.size() > 0">
      and unique_number in
      <foreach collection="deviceSnList" index="index" item="deviceSn" open="(" separator="," close=")">
        #{deviceSn}
      </foreach>
    </if>
    <if test="deviceName != null and deviceName != ''">
      and equipment_name like concat('%',#{deviceName},'%')
    </if>
    order by gmt_create desc
  </select>

  <select id="listAllDevices" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
  </select>

  <select id="listDevicesByOrgIds" resultMap="deviceResultMap">
    select
    <include refid="sql_columns"/>
    from <include refid="sql_table"/>
    where org_id in
    <foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="findDeviceByOrgIdAndDeviceTypeList" resultMap="deviceResultMap">
    select <include refid="sql_columns"/>
    from <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceTypeList != null and deviceTypeList.size() > 0">
      and equipment_type in
      <foreach collection="deviceTypeList" index="index" item="deviceType" open="(" separator="," close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="keywords != null and keywords != ''">
      and (equipment_name like CONCAT('%',#{keywords},'%') or unique_number like CONCAT('%',#{keywords},'%'))
    </if>
  </select>


  <select id="listAllDeviceTypeInOrg" resultType="java.lang.Integer">
			SELECT DISTINCT equipment_type FROM <include refid="sql_table"/> where org_id=#{orgId}
		</select>

  <select id="listParentSnByIotParentDeviceSns" resultType="java.lang.String">
    SELECT DISTINCT parent_unique_number
    FROM <include refid="sql_table"/>
    <where>
      <if test="parentDeviceSns != null and parentDeviceSns.size() > 0">
        and parent_unique_number in
        <foreach collection="parentDeviceSns" close=")" separator="," item="item" open="(">
          #{item}
        </foreach>
      </if>
      <if test="deviceTypes != null and deviceTypes.size() > 0">
        and equipment_type in
        <foreach collection="deviceTypes" close=")" separator="," item="item" open="(">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="listByOrgIdAndParentDeviceSns" resultMap="deviceResultMap">
    select <include refid="sql_columns"/>
    from <include refid="sql_table"/>
    <where>
      <if test="orgId != null">
        and org_id = #{orgId}
      </if>
      <if test="parentDeviceSns != null and parentDeviceSns.size() > 0">
        and parent_unique_number in
        <foreach collection="parentDeviceSns" close=")" separator="," item="item" open="(">
          #{item}
        </foreach>
      </if>
      <if test="deviceTypes != null and deviceTypes.size() > 0">
        and equipment_type in
        <foreach collection="deviceTypes" close=")" separator="," item="item" open="(">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="listDeviceByCondition" resultMap="deviceResultMap"
    parameterType="com.moredian.magicube.device.dto.device.QueryIotDeviceDTO">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="iotDeviceTypes != null and iotDeviceTypes.size() > 0">
      and equipment_type in
      <foreach collection="iotDeviceTypes" index="index" item="item" open="(" separator=","
        close=")">
        #{item}
      </foreach>
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and orgi_equipment_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="parentDeviceId != null and parentDeviceId != ''">
      and parent_unique_number = #{parentDeviceId}
    </if>
    <if test="sort == 0">
      order by equipment_name asc
    </if>
    <if test="sort == 1">
      order by gmt_create desc
    </if>
    <if test="sort == 2">
      order by gmt_create asc
    </if>
  </select>

  <update id="updateBluetoothMac">
    update <include refid="sql_table"/>
    set bluetooth_mac = #{bluetoothMac}
    where orgi_equipment_id = #{deviceId}
  </update>

  <select id="countAllDevices" resultType="int">
    select count(*)
    from <include refid="sql_table"/>;
  </select>

  <update id="batchUpdateDeviceRelateApp" parameterType="java.util.List">
    update
    <include refid="sql_table"/>
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_code =case" suffix="end,">
        <foreach collection="devices" item="item" index="index">
          <if test="item.appCode != null and item.appCode != ''">
            when orgi_equipment_id = #{item.deviceId} then #{item.appCode}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_code_list =case" suffix="end,">
        <foreach collection="devices" item="item" index="index">
          when orgi_equipment_id = #{item.deviceId} then #{item.appCodeList}
        </foreach>
      </trim>
    </trim>
    where
    <foreach collection="devices" separator="or" item="item" index="index">
      orgi_equipment_id=#{item.deviceId}
    </foreach>
  </update>

  <select id="listDeviceByConditionLike" resultMap="deviceResultMap">
    SELECT <include refid="sql_columns"/>
    FROM <include refid="sql_table"/>
    <where>
      org_id = #{orgId}
      <if test="deviceIds != null and deviceIds.size() > 0">
        AND orgi_equipment_id IN
        <foreach collection="deviceIds" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="deviceSn != null and deviceSn.size() > 0">
        AND unique_number IN
        <foreach collection="deviceSn" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="keywords != null and keywords != ''">
        AND (
            unique_number like CONCAT('%',#{keywords},'%')
            <if test="likeTreeNameDeviceId != null and likeTreeNameDeviceId.size() > 0">
              OR orgi_equipment_id IN
              <foreach collection="likeTreeNameDeviceId" index="index" item="deviceId" open="(" separator="," close=")">
                #{deviceId}
              </foreach>
            </if>
        )
      </if>
      <if test="deviceName != null and deviceSn != ''">
        AND memo LIKE CONCAT('%',#{deviceName},'%')
      </if>
      <if test="deviceTypes != null and deviceTypes.size() > 0">
        AND equipment_type IN
        <foreach collection="deviceTypes" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="filterDeviceTypes != null and filterDeviceTypes.size() > 0">
        AND equipment_type NOT IN
        <foreach collection="filterDeviceTypes" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    ORDER BY gmt_active_code DESC
  </select>
</mapper>