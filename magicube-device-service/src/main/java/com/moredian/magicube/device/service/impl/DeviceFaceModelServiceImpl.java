package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.register.SaveDeviceFaceModelRequest;
import com.moredian.magicube.device.manager.DeviceFaceModelManager;
import com.moredian.magicube.device.service.DeviceFaceModelService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023-06-27
 */
@SI
public class DeviceFaceModelServiceImpl implements DeviceFaceModelService {

    @Autowired
    private DeviceFaceModelManager deviceFaceModelManager;

    @Override
    public ServiceResponse<Boolean> saveDeviceFaceModel(SaveDeviceFaceModelRequest request) {
        return new ServiceResponse<>(deviceFaceModelManager.saveDeviceFaceModel(request));
    }

}
