<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.HiveDeviceElectricMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.HiveDeviceElectric">
        <id column="id" property="id" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
        <result column="device_sn" property="deviceSn" />
        <result column="electric" property="electric" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_sn, electric,gmt_create,gmt_modify
    </sql>

</mapper>
