package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.AccessoryCondition;
import com.moredian.magicube.device.dao.entity.AccessoryInfo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2021/12/8
 */
@Mapper
public interface AccessoryMapper {

    /**
     * 新增配件绑定信息
     *
     * @param accessoryInfo 新增参数
     */
    int addAccessory(AccessoryInfo accessoryInfo);

    /**
     * 列表 - 查询配件信息
     *
     * @param accessoryCondition 查询条件
     * @return
     */
    List<AccessoryInfo> findAccessoryInfoByCondition(AccessoryCondition accessoryCondition);

    /**
     * 根据机构id、设备sn、及外设sn删除
     *
     * @return
     */
    int deleteByCondition(@Param("orgId") Long orgId, @Param("deviceSn") String deviceSn, @Param("accessorySn") String accessorySn, @Param("accessoryType") Integer accessoryType);

    /**
     * 根据机构id及设备sn删除外设信息
     *
     * @param orgId    机构id
     * @param deviceSn 设备sn
     * @return
     */
    int deleteByOrgIdAndDeviceSn(@Param("orgId") Long orgId, @Param("deviceSn") String deviceSn);

    /**
     * 根据主键id更新 修改时间
     *
     * @param accessoryId
     * @return
     */
    int updateGmtModifyById(Long accessoryId);

}
