package com.moredian.magicube.device.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.moredian.magicube.device.dao.entity.DeviceIotInvokeMessage;
import com.moredian.magicube.device.dto.lock.InvokeMessageDTO;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-03 16:16
 */
public interface DeviceIotInvokeMessageManager extends IService<DeviceIotInvokeMessage> {

    /**
     * iot 功能调用
     */
    Long invoke(InvokeMessageDTO invokeMessageDTO);

}
