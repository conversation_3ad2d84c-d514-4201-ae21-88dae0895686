package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.enums.TransferEventType;
import com.moredian.magicube.core.msg.LocalCloudIpChangeMsg;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.TransferDTO;
import com.moredian.magicube.device.dto.device.TransferMessageDTO;
import com.moredian.magicube.device.manager.DeviceIotManager;
import com.moredian.magicube.device.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * 说明：本地云上报相关下发通知设备消息订阅
 *
 * <AUTHOR>
 * 创建时间:2024.01.16
 */
@Slf4j
@Component
public class LocalCloudReportMsgSubscriber {

    @Resource
    private DeviceIotManager deviceIotManager;

    @Resource
    private DeviceManager deviceManager;

    /**
     * 本地云上报Ip变更下发所属机构下的所有设备
     *
     * @param msg 本地云机构局域网ip变更下发消息
     */
    @Subscribe
    public void doLocalCloudIpChangeSend(LocalCloudIpChangeMsg msg){
        log.info("LocalCloudIpChangeMsg:{}", msg);
        Long orgId = msg.getOrgId();
        List<Device> deviceList = deviceManager.listByOrgId(orgId);
        for (Device device : deviceList) {
            log.info("对此设备下发ip变更通知{}", device.toString());
            TransferMessageDTO<String> transferMessageDTO = new TransferMessageDTO<>();
            transferMessageDTO.setEventType(TransferEventType.LOCAL_CLOUD_IP_CHANGE_SEND.getValue());
            transferMessageDTO.setSeverity(1);
            transferMessageDTO.setSeqId(UUID.randomUUID().toString());
            transferMessageDTO.setMessage(TransferEventType.LOCAL_CLOUD_IP_CHANGE_SEND.getName());
            String host = "http://" + msg.getLocalCloudLanIp() + ":" + msg.getLocalCloudLanPort() + "/";
            transferMessageDTO.setData(host);
            String jsonStr = JsonUtils.toJson(transferMessageDTO);
            log.info("{}的设备mqtt下发的消息体:{}", device.getDeviceSn(), jsonStr);
            String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
            try {
                TransferDTO transferDTO = new TransferDTO();
                transferDTO.setOrgId(orgId);
                transferDTO.setSerialNumber(device.getDeviceSn());
                transferDTO.setBody(base64Message);
                deviceIotManager.transfer(transferDTO);
            } catch (Exception e) {
                log.error(device.getDeviceSn() + "调用iot下发有错误", e);
            }
        }
    }
}
