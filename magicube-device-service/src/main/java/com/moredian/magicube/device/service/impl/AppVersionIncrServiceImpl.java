package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.convertor.AppVersionIncrConvertor;
import com.moredian.magicube.device.dto.upgrade.GetIncrOrFullNewAppRequest;
import com.moredian.magicube.device.dto.upgrade.GetRomUpgradeRequest;
import com.moredian.magicube.device.dto.version.AppVersionIncrDTO;
import com.moredian.magicube.device.dto.version.AppVersionIncreWithAppVersionInfoDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.service.AppVersionIncrService;
import com.moredian.magicube.device.service.DeviceVersionService;
import com.moredian.ota.api.request.apk.OtaGetIncrApkRequest;
import com.moredian.ota.api.request.apk.OtaGetNewestIncrApkRequest;
import com.moredian.ota.api.response.apk.OtaApkResponse;
import com.moredian.ota.api.service.OtaApkService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;

import java.util.List;

@SI
@Slf4j
public class AppVersionIncrServiceImpl implements AppVersionIncrService {

    @SI
    private OtaApkService otaApkService;

    @SI
    private DeviceVersionService deviceVersionService;

    @Override
    public ServiceResponse<AppVersionIncrDTO> getNewAppVersionIncrByVersionIdAndOldVersion(Integer oldVersionCode, Long versionId) {
        OtaGetIncrApkRequest otaGetIncrApkRequest = new OtaGetIncrApkRequest();
        otaGetIncrApkRequest.setIncrPreVersionCode(oldVersionCode);
        otaGetIncrApkRequest.setIncrApkVersionId(versionId);
        OtaApkResponse otaApkResponse = otaApkService.getIncrApk(otaGetIncrApkRequest).pickDataThrowException();
        return new ServiceResponse<>(AppVersionIncrConvertor.otaApkResponseToAppVersionIncrDto(otaApkResponse));
    }

    @Override
    public ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncrByAppTypeAndOldVersion(Integer systemType, Integer appType, Integer oldVersionCode) {
        AppVersionIncreWithAppVersionInfoDTO appVersionIncreWithAppVersionInfoDto = new AppVersionIncreWithAppVersionInfoDTO();
        if (appType == null || oldVersionCode == null) {
            appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
        } else {
            OtaGetNewestIncrApkRequest otaGetNewestIncrApkRequest = new OtaGetNewestIncrApkRequest();
            otaGetNewestIncrApkRequest.setAppType(appType);
            otaGetNewestIncrApkRequest.setIncrPreVersionCode(oldVersionCode);
            OtaApkResponse otaApkResponse = otaApkService
                .getNewestOpenIncrApkOrNewestOpenApk(otaGetNewestIncrApkRequest)
                .pickDataThrowException();
            if (otaApkResponse == null) {
                appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
            } else {
                Integer newVersionCode = otaApkResponse.getVersionCode();
                if (oldVersionCode == null || newVersionCode == null
                    || oldVersionCode >= newVersionCode) {
                    appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
                } else {
                    appVersionIncreWithAppVersionInfoDto.setIsUpdate(true);
                    appVersionIncreWithAppVersionInfoDto.setVersionCode(newVersionCode);
                    appVersionIncreWithAppVersionInfoDto
                        .setVersionDesc(otaApkResponse.getDescription());
                    appVersionIncreWithAppVersionInfoDto
                        .setIsEnforceUpdate(otaApkResponse.getInitDeviceUpgrade());
                    appVersionIncreWithAppVersionInfoDto
                        .setVersionName(otaApkResponse.getDescription());
                    appVersionIncreWithAppVersionInfoDto.setUrl(otaApkResponse.getApkFileUrl());
                    appVersionIncreWithAppVersionInfoDto.setIsIncre(otaApkResponse.getIncrFlag());
                    appVersionIncreWithAppVersionInfoDto
                        .setPreVersionCode(otaApkResponse.getIncrPreVersionCode());
                }
            }
        }
        return new ServiceResponse<>(appVersionIncreWithAppVersionInfoDto);
    }

    @Override
    public ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncrByAppTypeAndOldVersion(GetRomUpgradeRequest request) {
        AppVersionIncreWithAppVersionInfoDTO appVersionIncreWithAppVersionInfoDto = new AppVersionIncreWithAppVersionInfoDTO();
        OtaGetNewestIncrApkRequest otaGetNewestIncrApkRequest = new OtaGetNewestIncrApkRequest();
        otaGetNewestIncrApkRequest.setAppType(request.getRomType());
        otaGetNewestIncrApkRequest.setIncrPreVersionCode(request.getVersionCode());
        if (request.getOrgId() != null && request.getDeviceId() != null) {
            ServiceResponse<List<DeviceVersionDTO>> srAppResponse = deviceVersionService
                    .listApkByOrgIdAndDeviceIds(request.getOrgId(), Lists.newArrayList(request.getDeviceId()));
            if (srAppResponse != null && srAppResponse.isSuccess() && srAppResponse.isExistData()) {
                DeviceVersionDTO currentAppVersionDto = srAppResponse.getData().get(0);
                if (currentAppVersionDto != null) {
                    otaGetNewestIncrApkRequest.setApkAppType(currentAppVersionDto.getAppType());
                    OtaApkResponse otaApkResponse = otaApkService.getNewestOpenIncrApkOrNewestOpenApk(otaGetNewestIncrApkRequest).pickDataThrowException();
                    if (otaApkResponse == null) {
                        appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
                    } else {
                        Integer newVersionCode = otaApkResponse.getVersionCode();
                        if (request.getVersionCode() == null || newVersionCode == null || request.getVersionCode() >= newVersionCode) {
                            appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
                        } else {
                            appVersionIncreWithAppVersionInfoDto.setIsUpdate(true);
                            appVersionIncreWithAppVersionInfoDto.setVersionCode(newVersionCode);
                            appVersionIncreWithAppVersionInfoDto.setVersionDesc(otaApkResponse.getDescription());
                            appVersionIncreWithAppVersionInfoDto.setIsEnforceUpdate(otaApkResponse.getInitDeviceUpgrade());
                            appVersionIncreWithAppVersionInfoDto.setVersionName(otaApkResponse.getDescription());
                            appVersionIncreWithAppVersionInfoDto.setUrl(otaApkResponse.getApkFileUrl());
                            appVersionIncreWithAppVersionInfoDto.setIsIncre(otaApkResponse.getIncrFlag());
                            appVersionIncreWithAppVersionInfoDto.setPreVersionCode(otaApkResponse.getIncrPreVersionCode());
                        }
                    }
                }
            } else {
                appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
            }
        }
        return new ServiceResponse<>(appVersionIncreWithAppVersionInfoDto);
    }

    @Override
    public ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncrByAppTypeAndOldVersion(Long orgId, Integer systemType, Integer appType, Integer oldVersionCode) {
        AppVersionIncreWithAppVersionInfoDTO appVersionIncreWithAppVersionInfoDto = new AppVersionIncreWithAppVersionInfoDTO();
        if (appType == null || oldVersionCode == null) {
            appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
        } else {
            OtaGetNewestIncrApkRequest otaGetNewestIncrApkRequest = new OtaGetNewestIncrApkRequest();
            otaGetNewestIncrApkRequest.setAppType(appType);
            otaGetNewestIncrApkRequest.setIncrPreVersionCode(oldVersionCode);
            otaGetNewestIncrApkRequest.setOrgId(orgId);
            OtaApkResponse otaApkResponse = otaApkService
                .getNewestOpenIncrApkWithOrgGrayOrNewestOpenApk(otaGetNewestIncrApkRequest)
                .pickDataThrowException();
            if (otaApkResponse == null) {
                appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
            } else {
                Integer newVersionCode = otaApkResponse.getVersionCode();
                if (oldVersionCode == null || newVersionCode == null
                    || oldVersionCode >= newVersionCode) {
                    appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
                } else {
                    appVersionIncreWithAppVersionInfoDto.setIsUpdate(true);
                    appVersionIncreWithAppVersionInfoDto.setVersionCode(newVersionCode);
                    appVersionIncreWithAppVersionInfoDto
                        .setVersionDesc(otaApkResponse.getDescription());
                    appVersionIncreWithAppVersionInfoDto
                        .setIsEnforceUpdate(otaApkResponse.getInitDeviceUpgrade());
                    appVersionIncreWithAppVersionInfoDto
                        .setVersionName(otaApkResponse.getDescription());
                    appVersionIncreWithAppVersionInfoDto.setUrl(otaApkResponse.getApkFileUrl());
                    appVersionIncreWithAppVersionInfoDto.setIsIncre(otaApkResponse.getIncrFlag());
                    appVersionIncreWithAppVersionInfoDto
                        .setPreVersionCode(otaApkResponse.getIncrPreVersionCode());
                    appVersionIncreWithAppVersionInfoDto.setMd5Value(otaApkResponse.getMd5Value());
                    appVersionIncreWithAppVersionInfoDto.setExtendInfo(otaApkResponse.getExtendInfo());
                }
            }
        }
        return new ServiceResponse<>(appVersionIncreWithAppVersionInfoDto);
    }

    @Override
    public ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> getNewAppVersionIncreByAppTypeAndOldVersion(GetIncrOrFullNewAppRequest request) {
        AppVersionIncreWithAppVersionInfoDTO appVersionIncreWithAppVersionInfoDto = new AppVersionIncreWithAppVersionInfoDTO();
        OtaGetNewestIncrApkRequest otaGetNewestIncrApkRequest = new OtaGetNewestIncrApkRequest();
        otaGetNewestIncrApkRequest.setOrgId(request.getOrgId());
        otaGetNewestIncrApkRequest.setAppType(request.getAppType());
        otaGetNewestIncrApkRequest.setIncrPreVersionCode(request.getVersionCode());
        otaGetNewestIncrApkRequest.setApkAppType(request.getApkAppType());
        OtaApkResponse otaApkResponse = otaApkService.getNewestOpenIncrApkWithOrgGrayOrNewestOpenApk(otaGetNewestIncrApkRequest).pickDataThrowException();
        if (otaApkResponse == null) {
            appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
        } else {
            Integer newVersionCode = otaApkResponse.getVersionCode();
            if (request.getVersionCode() == null || newVersionCode == null || request.getVersionCode() >= newVersionCode) {
                appVersionIncreWithAppVersionInfoDto.setIsUpdate(false);
            } else {
                appVersionIncreWithAppVersionInfoDto.setIsUpdate(true);
                appVersionIncreWithAppVersionInfoDto.setVersionCode(newVersionCode);
                appVersionIncreWithAppVersionInfoDto.setVersionDesc(otaApkResponse.getDescription());
                appVersionIncreWithAppVersionInfoDto.setIsEnforceUpdate(otaApkResponse.getInitDeviceUpgrade());
                appVersionIncreWithAppVersionInfoDto.setVersionName(otaApkResponse.getDescription());
                appVersionIncreWithAppVersionInfoDto.setUrl(otaApkResponse.getApkFileUrl());
                appVersionIncreWithAppVersionInfoDto.setIsIncre(otaApkResponse.getIncrFlag());
                appVersionIncreWithAppVersionInfoDto.setPreVersionCode(otaApkResponse.getIncrPreVersionCode());
                if (otaApkResponse.getRomFlag() && !otaApkResponse.getIncrFlag()) {
                    if (otaApkResponse.getApkAppType() != null && otaApkResponse.getApkAppType() > 0) {
                        appVersionIncreWithAppVersionInfoDto.setApkAppType(otaApkResponse.getApkAppType());
                    }
                    if (otaApkResponse.getApkVersionCode() != null && otaApkResponse.getApkVersionCode() > 0) {
                        appVersionIncreWithAppVersionInfoDto.setApkVersionCode(otaApkResponse.getApkVersionCode());
                    }
                }
            }
        }
        return new ServiceResponse<>(appVersionIncreWithAppVersionInfoDto);
    }
}