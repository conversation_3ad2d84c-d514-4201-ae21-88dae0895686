package com.moredian.magicube.device.pipeline.core.pipeline;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.moredian.magicube.device.pipeline.core.pipeline.rollback.RollBack;
import com.moredian.magicube.device.pipeline.core.pipeline.success.Success;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@Scope("prototype")
@Component
public class PipelineImpl<T, S> implements Pipeline<T, S> {

    private List<Pipe> pipeList = new LinkedList<>();

    @Override
    public void addPipe(Pipe pipe) {
        pipeList.add(pipe);
    }

    @Override
    public InvocationChain<T, S> newInvocation(T t, S s) {
        return new InvocationChainImpl(t, s);
    }

    /**
     * 管道流私有一个执行链类
     *
     * @param <T>
     * @param <S>
     */
    private final class InvocationChainImpl<T, S> implements InvocationChain<T, S> {

        private int executedIndex = -1;

        private T parameter;

        private S context;

        private String key;

        private List<RollBack<T, S>> rollBackList = Lists.newArrayList();

        private List<Success<T, S>> successList = Lists.newArrayList();

        public InvocationChainImpl(T parameter, S context) {
            this.parameter = parameter;
            this.context = context;
        }

        @Override
        public T getParameter() {
            return parameter;
        }

        @Override
        public S getContext() {
            return context;
        }

        @Override
        public String getKey() {
            return key;
        }

        @Override
        public void setKey(String key) {
            this.key = key;
        }

        @Override
        public List<RollBack<T, S>> getRollBackList() {
            return rollBackList;
        }

        @Override
        public List<Success<T, S>> getSuccessList() {
            return successList;
        }

        /**
         * 管道流执行入口
         */
        @Override
        public void invoke() {

            executedIndex = -1;
            log.info("{} : begin invoke...request = {}", key, JSON.toJSONString(this.getParameter()));

            invokeNext();
        }

        /**
         * 递归调用管道
         */
        @Override
        public void invokeNext() {

            executedIndex++;
            if (executedIndex < pipeList.size()) {
                Pipe pipe = pipeList.get(executedIndex);

                pipe.invoke(this);
            }

        }

    }
}
