package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.PeripheralsWhiteList;
import com.moredian.magicube.device.dao.entity.PeripheralsWhiteListNew;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewQueryDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Auther: _AF
 * @Date: 2/23/22 17:52
 * @Description: 新配件白名单mapper
 */
@Mapper
public interface PeripheralsWhiteListNewMapper {

    /**
     * 根据条件查询
     *
     * @param dto
     * @return
     */
    List<PeripheralsWhiteListNew> pageByCondition(PeripheralsWhiteListNewQueryDTO dto);

    /**
     * 根据id集合删除
     *
     * @param ids
     * @return
     */
    void removeByIds(@Param("ids") List<Long> ids);


    /**
     * 修改
     *
     * @param request
     * @return
     */
    void updateById(PeripheralsWhiteListNew request);

    /**
     * 新增
     *
     * @param request
     * @return
     */
    void addPeripheralsWhiteNewList(PeripheralsWhiteListNew request);

    /**
     * 批量导入
     *
     * @param requestList
     * @return
     */
    void addByBatch(@Param("requestList") List<PeripheralsWhiteListNew> requestList);


    /**
     * 根据sn和type获取信息
     *
     * @param peripheralsSn
     * @param peripheralsType
     * @return
     */
    PeripheralsWhiteListNew getBySnAndType(@Param("peripheralsSn") String peripheralsSn, @Param("peripheralsType") Integer peripheralsType);


    /**
     * 根据外设sn集合查询
     *
     * @param peripheralsSnList
     * @return
     */
    List<PeripheralsWhiteList> getBySnList(@Param("peripheralsSnList") List<String> peripheralsSnList);
}
