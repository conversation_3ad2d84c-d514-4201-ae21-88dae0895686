package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.AccessoryCondition;
import com.moredian.magicube.device.dao.entity.AccessoryInfo;
import com.moredian.magicube.device.dao.mapper.AccessoryMapper;
import com.moredian.magicube.device.dto.accessory.AccessoryInfoDTO;
import com.moredian.magicube.device.dto.accessory.AccessoryQueryDTO;
import com.moredian.magicube.device.dto.accessory.BindAccessoryDTO;
import com.moredian.magicube.device.dto.accessory.UnbindAccessoryDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.AccessoryManager;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2021/12/8
 */
@Slf4j
@Service
public class AccessoryManagerImpl implements AccessoryManager {

    @Autowired
    private AccessoryMapper accessoryMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    @Transactional
    public Boolean bindAccessory(BindAccessoryDTO dto) {
        // 校验是否已存在
        AccessoryCondition condition = new AccessoryCondition();
        condition.setOrgId(dto.getOrgId());
        condition.setDeviceSn(dto.getDeviceSn());
        condition.setAccessorySn(dto.getAccessorySn());
        condition.setAccessoryType(dto.getAccessoryType());
        List<AccessoryInfo> accessoryInfoList = accessoryMapper.findAccessoryInfoByCondition(condition);

        if (CollectionUtils.isNotEmpty(accessoryInfoList)) {
            // 更新记录变更时间
            if (accessoryMapper.updateGmtModifyById(accessoryInfoList.get(0).getAccessoryId()) <= 0) {
                log.info("AccessoryManagerImpl-saveAccessoryInfo: 外设sn-{}, 外设连接信息保存失败, reason: {}", dto.getAccessorySn(), DeviceErrorCode.PERIPHREALS_UPDATE_FAIL.getMessage());
                ExceptionUtils.throwException(DeviceErrorCode.PERIPHREALS_UPDATE_FAIL, DeviceErrorCode.PERIPHREALS_UPDATE_FAIL.getMessage());
            }
        } else {
            // 新增配件信息
            AccessoryInfo accessoryInfo = convertBindAccessoryToAccessoryInfo(dto);
            Long accessoryId = idgeneratorService.getNextIdByTypeName(AccessoryInfo.class.getName()).pickDataThrowException();
            accessoryInfo.setAccessoryId(accessoryId);
            if (accessoryMapper.addAccessory(accessoryInfo) <= 0) {
                log.info("AccessoryManagerImpl-saveAccessoryInfo: 外设sn-{}, 外设连接信息保存失败, reason: {}", accessoryInfo.getAccessorySn(), DeviceErrorCode.PERIPHREALS_ADD_FAIL.getMessage());
                ExceptionUtils.throwException(DeviceErrorCode.PERIPHREALS_ADD_FAIL, DeviceErrorCode.PERIPHREALS_ADD_FAIL.getMessage());
            }
        }

        log.info("AccessoryManagerImpl-saveAccessoryInfo: 外设sn-{}, 连接设备成功", dto.getAccessorySn());
        return Boolean.TRUE;
    }

    @Override
    @Transactional
    public Boolean unbindAccessory(UnbindAccessoryDTO dto) {
        AccessoryCondition condition = new AccessoryCondition();
        BeanUtils.copyProperties(dto, condition);
        List<AccessoryInfo> accessoryInfoList = accessoryMapper.findAccessoryInfoByCondition(condition);

        if (CollectionUtils.isNotEmpty(accessoryInfoList)) {
            // 删除外设信息
            if (accessoryMapper.deleteByCondition(dto.getOrgId(), dto.getDeviceSn(),
                dto.getAccessorySn(), dto.getAccessoryType()) <= 0) {
                log.info("AccessoryManagerImpl-unbindAccessory: 外设sn-{}, 断开失败", dto.getAccessorySn());
                ExceptionUtils.throwException(DeviceErrorCode.PERIPHREALS_UNBIND_FAIL, DeviceErrorCode.PERIPHREALS_UNBIND_FAIL.getMessage());
            }
            log.info("AccessoryManagerImpl-unbindAccessory: 外设sn-{}, 断开成功", dto.getAccessorySn());
        }
        return Boolean.TRUE;
    }

    @Override
    public List<AccessoryInfoDTO> listAccessoryInfo(AccessoryQueryDTO dto) {
        // 参数转换
        AccessoryCondition condition = convertAccessoryQueryRequestToCondition(dto);
        return buildAccessoryInfoResponseList(accessoryMapper.findAccessoryInfoByCondition(condition));
    }

    @Override
    @Transactional
    public Boolean unbindAccessoryByOrgIdAndDeviceSn(Long orgId, String deviceSn) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notBlank(deviceSn, "deviceSn must not be blank");
        AccessoryCondition condition = new AccessoryCondition();
        condition.setOrgId(orgId);
        condition.setDeviceSn(deviceSn);
        List<AccessoryInfo> accessoryInfoList = accessoryMapper.findAccessoryInfoByCondition(condition);

        // 存在外设 就进行断开操作
        if (CollectionUtils.isNotEmpty(accessoryInfoList)) {
            if (accessoryMapper.deleteByOrgIdAndDeviceSn(orgId, deviceSn) < 0) {
                log.info("AccessoryManagerImpl-batchUnbindAccessoryByDeviceSn: 设备sn-{}下的外设信息删除失败", deviceSn);
                ExceptionUtils.throwException(DeviceErrorCode.PERIPHREALS_UNBIND_FAIL, DeviceErrorCode.PERIPHREALS_UNBIND_FAIL.getMessage());
            }
            log.info("AccessoryManagerImpl-unbindAccessoryByOrgIdAndDeviceSn: 设备sn-{} 连接的外设 断开成功", deviceSn);
        }
        return Boolean.TRUE;
    }

    // ---------------------------------- private start --------------------------------------------------

    /**
     * BindAccessoryRequest -> AccessoryInfo
     *
     * @param dto
     * @return
     */
    private AccessoryInfo convertBindAccessoryToAccessoryInfo(BindAccessoryDTO dto) {
        if (dto == null) {
            return null;
        }
        AccessoryInfo accessoryInfo = new AccessoryInfo();
        BeanUtils.copyProperties(dto, accessoryInfo);
        return accessoryInfo;
    }

    /**
     * AccessoryQueryDTO -> AccessoryInfo
     *
     * @param dto
     * @return
     */
    private AccessoryCondition convertAccessoryQueryRequestToCondition(AccessoryQueryDTO dto) {
        if (dto == null) {
            return null;
        }
        AccessoryCondition condition = new AccessoryCondition();
        BeanUtils.copyProperties(dto, condition);
        return condition;
    }

    /**
     * List<AccessoryInfo> -> List<AccessoryInfoResponse>
     *
     * @param accessoryInfoList
     * @return
     */
    private List<AccessoryInfoDTO> buildAccessoryInfoResponseList(List<AccessoryInfo> accessoryInfoList) {
        List<AccessoryInfoDTO> accessoryInfoResponseList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(accessoryInfoList)) {
            for (AccessoryInfo accessoryInfo : accessoryInfoList) {
                AccessoryInfoDTO response = new AccessoryInfoDTO();
                BeanUtils.copyProperties(accessoryInfo, response);
                accessoryInfoResponseList.add(response);
            }
        }
        return accessoryInfoResponseList;
    }

    // ---------------------------------- private end ----------------------------------------------------

}
