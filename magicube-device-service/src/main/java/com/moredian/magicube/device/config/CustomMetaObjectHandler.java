package com.moredian.magicube.device.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * 苞米豆对象处理(mybatis-plus版本2.0.9+可以使用)
 *
 * <AUTHOR>
 * @Description
 * @Date 2021/9/9 15:40
 */
public class CustomMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        //插入填充- 是否有效 创建时间 修改时间  在插入时自动带上
        Object gmtCreate = getFieldValByName("gmtCreate", metaObject);
        Object gmtModify = getFieldValByName("gmtModify", metaObject);
        if (gmtCreate == null) {
            setFieldValByName("gmtCreate", new Date(), metaObject);
        }
        if (gmtModify == null) {
            setFieldValByName("gmtModify", new Date(), metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        //更新填充
        setFieldValByName("gmtModify", new Date(), metaObject);
    }
}
