package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dto.snapshot.DeviceSnapshotDTO;
import com.moredian.magicube.device.model.GenerateDeviceSnapshotModel;
import com.moredian.magicube.device.model.QueryDeviceSnapshotModel;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/29
 */
public interface DeviceSnapshotManager {

    /**
     * 生成设备快照
     *
     * @param model
     * @return 快照id
     */
    Long generateSnapshot(GenerateDeviceSnapshotModel model);

    /**
     * 查询快照
     *
     * @param model
     * @return
     */
    DeviceSnapshotDTO getDeviceSnapshot(QueryDeviceSnapshotModel model);

    /**
     * 查询超过指定保留时间的快照
     *
     * @param keepDay 保留天数
     * @return
     */
    List<DeviceSnapshotDTO> queryOvertimeDeviceSnapshot(Integer keepDay);

    /**
     * 删除快照
     *
     * @param snapshotId
     * @return
     */
    Boolean deleteById(Long snapshotId);
}
