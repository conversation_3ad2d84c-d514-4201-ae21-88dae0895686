package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceLog;
import com.moredian.magicube.device.dto.log.DeviceLogDTO;
import com.moredian.magicube.device.manager.DeviceLogManager;
import com.moredian.magicube.device.service.DeviceLogService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@SI
public class DeviceLogServiceImpl implements DeviceLogService {

    @Autowired
    private DeviceLogManager deviceLogManager;

    @Override
    public ServiceResponse<Long> insert(DeviceLogDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceLog deviceLog = new DeviceLog();
        BeanUtils.copyProperties(dto, deviceLog);
        serviceResponse.setData(deviceLogManager.insert(deviceLog));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> updateDeviceLogOperatorId(DeviceLogDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setDeviceLogId(dto.getDeviceLogId());
        deviceLog.setOrgId(dto.getOrgId());
        deviceLog.setDeviceId(dto.getDeviceId());
        deviceLog.setDeviceType(dto.getDeviceType());
        deviceLog.setDeviceSn(dto.getDeviceSn());
        deviceLog.setEnvenType(dto.getEnvenType());
        deviceLog.setMemberId(dto.getMemberId());
        serviceResponse.setData(deviceLogManager.updateDeviceLogOperatorId(deviceLog));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceLogDTO> getByOrgIdAndId(Long orgId, Long deviceLogId) {
        ServiceResponse<DeviceLogDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceLog deviceLog = deviceLogManager.getByOrgIdAndId(orgId, deviceLogId);
        if (deviceLog != null) {
            DeviceLogDTO deviceLogDTO = new DeviceLogDTO();
            BeanUtils.copyProperties(deviceLog, deviceLogDTO);
            serviceResponse.setData(deviceLogDTO);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceLogDTO>> listByOrgIdAndDeviceId(Long orgId, Long deviceId) {
        ServiceResponse<List<DeviceLogDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceLog> deviceLogs = deviceLogManager.listByOrgIdAndDeviceId(orgId, deviceId);
        if (CollectionUtils.isNotEmpty(deviceLogs)) {
            List<DeviceLogDTO> deviceLogDTOS = new ArrayList<>();
            for (DeviceLog deviceLog : deviceLogs) {
                DeviceLogDTO deviceLogDTO = new DeviceLogDTO();
                BeanUtils.copyProperties(deviceLog, deviceLogDTO);
                deviceLogDTOS.add(deviceLogDTO);
            }
            serviceResponse.setData(deviceLogDTOS);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceLogDTO>> listByOrgIdAndDeviceSn(Long orgId, String deviceSn) {
        ServiceResponse<List<DeviceLogDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceLog> deviceLogs = deviceLogManager.listByOrgIdAndDeviceSn(orgId, deviceSn);
        if (CollectionUtils.isNotEmpty(deviceLogs)) {
            List<DeviceLogDTO> deviceLogDTOS = new ArrayList<>();
            for (DeviceLog deviceLog : deviceLogs) {
                DeviceLogDTO deviceLogDTO = new DeviceLogDTO();
                BeanUtils.copyProperties(deviceLog, deviceLogDTO);
                deviceLogDTOS.add(deviceLogDTO);
            }
            serviceResponse.setData(deviceLogDTOS);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceLogDTO>> listByDeviceSn(String deviceSn) {
        ServiceResponse<List<DeviceLogDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceLog> deviceLogs = deviceLogManager.listByDeviceSn(deviceSn);
        if (CollectionUtils.isNotEmpty(deviceLogs)) {
            List<DeviceLogDTO> deviceLogDTOS = new ArrayList<>();
            for (DeviceLog deviceLog : deviceLogs) {
                DeviceLogDTO deviceLogDTO = new DeviceLogDTO();
                BeanUtils.copyProperties(deviceLog, deviceLogDTO);
                deviceLogDTOS.add(deviceLogDTO);
            }
            serviceResponse.setData(deviceLogDTOS);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Integer>> listDistinctDeviceTypeByOrgId(Long orgId) {
        ServiceResponse<List<Integer>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceLogManager.listDistinctDeviceTypeByOrgId(orgId));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Integer> countByOrgId(Long orgId) {
        ServiceResponse<Integer> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceLogManager.countByOrgId(orgId));
        return serviceResponse;
    }
}
