package com.moredian.magicube.device.subscriber;

import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.device.manager.DeviceOnlineStateManager;
import com.moredian.ota.api.msg.DeviceOnlineStateMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GropOnlineStateSubscriber {

    private static Logger logger = LoggerFactory.getLogger(GropOnlineStateSubscriber.class);

    @Autowired
    private DeviceOnlineStateManager deviceOnlineStateManager;

    @Subscribe
    public void subscribeGroupOnlineStateMsg(DeviceOnlineStateMsg msg) {
        logger.info("收到设备在离线消息,deviceId:{},deviceSn:{}",msg.getDeviceId(),msg.getDeviceSn());
        deviceOnlineStateManager.insert(msg);
    }
}
