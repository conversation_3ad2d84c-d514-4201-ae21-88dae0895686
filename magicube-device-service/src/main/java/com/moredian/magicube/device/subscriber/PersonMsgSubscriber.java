package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.UUID;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.ControllerErrorCode;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.core.msg.MemberInfoChangeMsg;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 人员相关消息订阅
 *
 * <AUTHOR>
 * @since 2022/5/24
 */
@Slf4j
@Component
public class PersonMsgSubscriber {

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @Subscribe
    public void subscribeMemberInfoChangeMsg(MemberInfoChangeMsg msg) {
        log.info("subscribeMemberInfoChangeMsg receive MemberInfoChangeMsg:{}", msg);
        List<Device> devices = deviceManager.listByOrgIdAndTypes(msg.getOrgId(),
            deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.VERIFY_BOARD_LIST));
        if (CollectionUtils.isEmpty(devices)) {
            log.warn("Notify device MemberInfoChangeMsg,no device is found");
        } else {
            log.info("Notify device MemberInfoChangeMsg,deviceCount:{}", devices.size());
            for (Device device : devices) {
                try {
                    // 发送命令
                    String deviceSn = device.getDeviceSn();
                    TransferMessageInfo<String> transferMessageInfo = new TransferMessageInfo<>();
                    transferMessageInfo
                        .setEventType(TransferEventType.PULL_MEMBER_MOBILE_IDCARD.getEventType());
                    transferMessageInfo.setSeverity(3);
                    transferMessageInfo.setSeqId(UUID.random19()
                        + TransferEventType.PULL_MEMBER_MOBILE_IDCARD.getEventName());
                    transferMessageInfo
                        .setMessage(TransferEventType.PULL_MEMBER_MOBILE_IDCARD.getEventName());
                    String jsonStr = JsonUtils.toJson(transferMessageInfo);
                    String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
                    TransferRequest transferRequest = new TransferRequest();
                    transferRequest.setSerialNumber(deviceSn);
                    transferRequest.setBody(base64Message);
                    transferRequest.setCommand(0);
                    ServiceResponse<TransferResponse> response = hubControlServiceV1.transfer(
                        transferRequest);
                    if (response != null && !response.isSuccess()
                        && ControllerErrorCode.DEVICE_OFFLINE.getCode()
                        .equals(response.getErrorContext().getCode())) {
                        log.info(
                            "Notify device MemberInfoChangeMsg, 设备已离线，本次不发送通知，[org={},deviceSn={},deviceId={}]",
                            msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                        continue;
                    }

                    TransferResponse result = response.pickDataThrowException();
                    log.info("Notify device MemberInfoChangeMsg, [org={},deviceSn={},deviceId={},result={}]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId(),result);
                } catch (Exception e) {
                    log.error(
                        "Notify device MemberInfoChangeMsg failed. [org={},device [sn={},id={}],error={}]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId(), e);
                }
            }
        }
    }
}
