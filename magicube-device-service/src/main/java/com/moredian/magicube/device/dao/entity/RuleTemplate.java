package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_rule_template")
public class RuleTemplate extends TimedEntity {
    private Long ruleTemplateId;

    private String templateName;

    private Integer modeType;

    private String description;

    private Integer triggerType;

    private String triggerValue;

    private String triggerDeviceTypeProperty;

    private String implementDeviceTypeProperty;

    /**
     * 场景规则
     */
    private String ruleJson;

    /**
     * 场景action
     */
    private String actionJson;
}