package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.Peripherals;
import com.moredian.magicube.device.dto.peripherals.QueryPeripheralsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 外设管理dao
 * @Date 2020/2/14 11:48
 */
@Mapper
public interface PeripheralsMapper {

    /**
     * 增加外设
     *
     * @param peripherals 外设信息
     * @return
     */
    int insert(Peripherals peripherals);

    /**
     * 更新外设
     *
     * @param peripherals 外设信息
     * @return
     */
    int update(Peripherals peripherals);

    /**
     * 根据条件查询外设信息列表
     *
     * @param dto 查询条件
     * @return
     */
    List<Peripherals> listByCondition(QueryPeripheralsDTO dto);

    /**
     * 根据设备sn查询该机构下外设信息列表
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    List<Peripherals> listByOrgIdAndDeviceSn(@Param("orgId") Long orgId, @Param("deviceSn") String deviceSn);

    /**
     * 查找
     *
     * @param peripherals
     * @return
     */
    List<Peripherals> listByOtherOrg(Peripherals peripherals);

    /**
     * 物理删除
     *
     * @param peripheralsId
     */
    void cleanPeripherals(Long peripheralsId);

    /**
     * 外设断开
     *
     * @param peripherals
     * @return
     */
    int updateByCondition(Peripherals peripherals);

    /**
     * 查询机构下是否存在外设（包括历史外设）
     *
     * @param orgId 机构号
     * @return
     */
    List<Long> listByOrgId(@Param("orgId") Long orgId);

    /**
     * 根据设备sn查询外设
     *
     * @param orgId     机构号
     * @param deviceSns 设备sn列表
     * @return
     */
    List<Peripherals> listByOrgIdAndDeviceSns(@Param("orgId") Long orgId, @Param("deviceSns") List<String> deviceSns);

}
