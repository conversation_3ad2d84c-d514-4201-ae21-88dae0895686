package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceSubject;
import com.moredian.magicube.device.dao.entity.DeviceType;
import java.util.List;

import com.moredian.magicube.device.dto.subject.DeviceSubjectDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备主题Mapper
 *
 * <AUTHOR>
 * @since 2023-08-28
 */

@Mapper
public interface DeviceSubjectMapper {

    /**
     * 新增设备主题
     *
     * @param deviceSubject 设备主题信息
     * @return
     */
    int insert(DeviceSubject deviceSubject);


    /**
     * 新增设备主题(批量）
     *
     * @param deviceSubjectList 设备主题信息
     * @return
     */
    int batchInsert(@Param("relations")  List<DeviceSubject> deviceSubjectList);


    /**
     * 根据Id查询设备主题信息
     *
     * @param orgId 机构Id
     * @param id    设备主题Id
     * @return
     */
    DeviceSubject getById(@Param("orgId") Long orgId, @Param("id") Long id);

    /**
     * 编辑设备主题信息
     *
     * @param deviceSubject 设备主题信息
     * @return
     */
    int update(DeviceSubject deviceSubject);

    /**
     * 根据机构和主题名称查询设备主题信息
     *
     * @param orgId 机构Id
     * @param name  主题名称
     * @param type  主题类型 1-壁纸 2-屏保
     * @return
     */
    DeviceSubject getByOrgIdAndNameAndType(@Param("orgId") Long orgId, @Param("name") String name,
        @Param("type") Integer type);

    /**
     * 查询设备主题数量
     *
     * @param orgId 机构Id
     * @param type  主题类型 1-壁纸 2-屏保
     * @return
     */
    Integer countByOrgIdAndType(@Param("orgId") Long orgId, @Param("type") Integer type);

    /**
     * 根据机构Id和设备主题Id列表查询设备主题信息列表
     *
     * @param orgId 机构Id
     * @param ids   设备主题Id列表
     * @return
     */
    List<DeviceSubject> listByOrgIdAndIds(@Param("orgId") Long orgId, @Param("ids") List<Long> ids);

    /**
     * 根据条件查询设备主题信息列表
     *
     * @param deviceSubject 设备主题信息
     * @return
     */
    List<DeviceSubject> listByCondition(DeviceSubject deviceSubject);

    /**
     * 根据条件查询设备主题信息列表
     *
     * @return
     */
    List<DeviceSubject> listByPage(@Param("deviceSubject")DeviceSubject deviceSubject, @Param("pageSize")Integer pageSize, @Param("pageNo") Integer pageNo,@Param("subjectIdList") List<Long> subjectIdList);

    /**
     * 根据条件查询总数
     * @param deviceSubject
     * @return
     */
    Integer countAll(@Param("deviceSubject")DeviceSubject deviceSubject,@Param("subjectIdList")List<Long> subjectIdList);

    /**
     * 批量删除设备主题
     *
     * @param orgId 机构Id
     * @param ids   设备主题Id列表
     * @return
     */
    void deleteByOrgIdAndIds(@Param("orgId") Long orgId, @Param("ids") List<Long> ids);


}
