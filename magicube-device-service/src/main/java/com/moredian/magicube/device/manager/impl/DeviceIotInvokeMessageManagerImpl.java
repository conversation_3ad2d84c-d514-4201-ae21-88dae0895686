package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.req.DeviceFunctionInvokeRequest;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.mapper.DeviceIotInvokeMessageMapper;
import com.moredian.magicube.device.dto.lock.InvokeMessageDTO;
import com.moredian.magicube.device.enums.iot.IotFunctionIdEnum;
import com.moredian.magicube.device.enums.iot.MessageStatusEnum;
import com.moredian.magicube.device.manager.DeviceIotInvokeMessageManager;
import com.moredian.magicube.device.manager.DeviceManager;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.moredian.magicube.device.dao.entity.DeviceIotInvokeMessage;
import javax.annotation.Resource;
import java.util.Map;


/**
  *@Description      
  *<AUTHOR>
  *@create          2025-03-03 16:12
  */ 
@Service
public class DeviceIotInvokeMessageManagerImpl extends ServiceImpl<DeviceIotInvokeMessageMapper, DeviceIotInvokeMessage>
        implements DeviceIotInvokeMessageManager {

    @Resource
    private DeviceManager deviceManager;

    @SI
    private IdgeneratorService idgeneratorService;
    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;


    @Override
    public Long invoke(InvokeMessageDTO invokeMessageDTO) {
        String deviceSn = invokeMessageDTO.getDeviceSn();
        Device device = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.isTrue(device != null, "deviceInfo is null");

        Long messageId = idgeneratorService.getNextIdByTypeName(DeviceIotInvokeMessage.class.getName()).pickDataThrowException();

        // 调用 jetlinks 设备功能
        Integer deviceType = device.getDeviceType();
        Long orgId = device.getOrgId();
        // 通通木门锁由于是出生态，对应 jetlinks 开放账号不确定，同时使用 -1 表示通通锁统一开放平台账号
        if (deviceType == DeviceType.TTLOCK.getValue()) {
            orgId = -1L;
        }

        // 构建参数
        DeviceFunctionInvokeRequest invokeRequest = new DeviceFunctionInvokeRequest();
        invokeRequest.setDeviceId(device.getDeviceSn());
        IotFunctionIdEnum messageType = IotFunctionIdEnum.getByCode(invokeMessageDTO.getMessageType());
        BizAssert.isTrue(messageType != null, "messageType must be not null");
        invokeRequest.setFunctionId(messageType.getId());
        Map<String, Object> params = invokeMessageDTO.getParams();
        params.put("operateMessageId", messageId);
        invokeRequest.setParams(params);

        // 设备功能调用
        Boolean b = iotDeviceInstanceService.deviceFunctionInvoke(orgId, invokeRequest).pickDataThrowException();
        BizAssert.isTrue(b, "device invoke fail");

        DeviceIotInvokeMessage message = new DeviceIotInvokeMessage();
        message.setMessageId(messageId);
        message.setDeviceSn(deviceSn);
        message.setMessageType(invokeMessageDTO.getMessageType());
        message.setOrgId(device.getOrgId());
        message.setMessageStatus(MessageStatusEnum.AWAIT_STATUS.getType());
        message.setDeviceId(device.getDeviceId());
        message.setParams(JsonUtils.toJson(invokeMessageDTO.getParams()));
        this.baseMapper.insert(message);
        return messageId;
    }
}
