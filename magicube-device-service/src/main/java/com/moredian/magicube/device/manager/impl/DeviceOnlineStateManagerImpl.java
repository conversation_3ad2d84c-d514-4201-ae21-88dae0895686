package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.DeviceOnlineState;
import com.moredian.magicube.device.dao.entity.DeviceOnlineStateHistory;
import com.moredian.magicube.device.dao.mapper.DeviceOnlineStateHistoryMapper;
import com.moredian.magicube.device.dao.mapper.DeviceOnlineStateMapper;
import com.moredian.magicube.device.manager.DeviceOnlineStateManager;
import com.moredian.ota.api.msg.DeviceOnlineStateMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Slf4j
@Service
public class DeviceOnlineStateManagerImpl implements DeviceOnlineStateManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private DeviceOnlineStateMapper deviceOnlineStateMapper;

    @Autowired
    private DeviceOnlineStateHistoryMapper deviceOnlineStateHistoryMapper;

    /**
     * 插入设备在离线线日志到登陆日志表中
     *
     * @param msg 设备在离线日志
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(DeviceOnlineStateMsg msg) {
        BizAssert.notNull(msg, "deviceOnlineStateMsg must not be null");
        DeviceOnlineState deviceOnlineState = new DeviceOnlineState();
        BeanUtils.copyProperties(msg, deviceOnlineState);
        Long id = idgeneratorService.getNextIdByTypeName(DeviceOnlineState.class.getName())
                .pickDataThrowException();
        deviceOnlineState.setId(id);
        //插入或更新设备在离线状态表
        deviceOnlineStateMapper.insert(deviceOnlineState);

        //插入在离线日志表
        DeviceOnlineStateHistory stateHistory = new DeviceOnlineStateHistory();
        BeanUtils.copyProperties(msg, stateHistory);
        Long historyId = idgeneratorService.getNextIdByTypeName(DeviceOnlineStateHistory.class.getName())
                .pickDataThrowException();
        stateHistory.setId(historyId);
        deviceOnlineStateHistoryMapper.insert(stateHistory);
    }

    @Override
    public List<DeviceOnlineState> selectByOrgIdAndDeviceId(Long orgId, List<Long> deviceIdList) {
        BizAssert.notNull(orgId,"orgId不为null");
        BizAssert.notNull(deviceIdList,"deviceIdList不为null");
        return deviceOnlineStateMapper.selectByOrgIdAndDeviceId(orgId, deviceIdList);
    }

    @Override
    public List<DeviceOnlineState> selectByDeviceSnList(List<String> deviceSnList) {
        BizAssert.notNull(deviceSnList, "deviceSnList must not be null");
        return deviceOnlineStateMapper.selectByDeviceSnList(deviceSnList);
    }

}
