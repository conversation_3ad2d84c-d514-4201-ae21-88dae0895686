package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.model.msg.DingBindMeetingRoomMsg;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.enums.DeviceActiveStateEnum;
import com.moredian.magicube.device.manager.ActivityManager;
import com.moredian.magicube.device.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 设备关联钉会议室消息
 * @create 2025-03-10 11:17
 */
@Slf4j
@Component
public class DingBindMeetingRoomMsgSubscriber {

    @Resource
    private ActivityManager activityManager;
    @Resource
    private DeviceManager deviceManager;

    /**
     * （会议室服务发出）
     */
    @Subscribe
    public void subDeviceUnbindMsg(DingBindMeetingRoomMsg msg) {
        log.info("收到设备关联钉会议室消息: {}", JsonUtils.toJson(msg));

        if (msg != null){
            Long deviceId = msg.getDeviceId();
            Device device = deviceManager.getById(deviceId);
            BizAssert.notNull(device, "device is null");
            Boolean flag = activityManager.deviceActiveStateChange(device.getDeviceSn(),
                    DeviceActiveStateEnum.RELEVANCE_MEETING_SUCCESS.getCode(), null, null);
            if (flag) {
                log.info("设备关联钉会议室成功, deviceSn=>{}", device.getDeviceSn());
            }else {
                log.warn("设备关联钉会议室失败, deviceSn=>{}", device.getDeviceSn());
            }
        }
    }
}
