<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.QrCodeActivateRecordMapper">

    <resultMap id="baseMap" type="com.moredian.magicube.device.dao.entity.qrcode.QrCodeActivateRecord">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="qrCodeId" column="qr_code_id"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceSn" column="device_sn"/>
        <result property="deviceType" column="device_type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
    </resultMap>

    <sql id="sql_base_column">
        id,
        org_id,
        qr_code_id,
        device_id,
        device_sn,
        device_type,
        gmt_create,
        gmt_modify
    </sql>


    <select id="listByQrCodeId" resultMap="baseMap">
        select  <include refid="sql_base_column"/>
        from hive_qr_code_activate_record
        where org_id = #{orgId} and qr_code_id = #{qrCodeId}
        order by gmt_create desc
    </select>

    <select id="getByDeviceId" resultMap="baseMap">
        select  <include refid="sql_base_column"/>
        from hive_qr_code_activate_record
        where org_id = #{orgId} and device_id = #{deviceId}
        order by gmt_create desc

    </select>

    <select id="getNewestRecordByOrgId" resultMap="baseMap">
        select  <include refid="sql_base_column"/>
        from hive_qr_code_activate_record
        where org_id = #{orgId} order by gmt_create desc limit 1
    </select>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.qrcode.QrCodeActivateRecord">
        insert into hive_qr_code_activate_record(
        <include refid="sql_base_column"/>
        )values (
        #{id},
        #{orgId},
        #{qrCodeId},
        #{deviceId},
        #{deviceSn},
        #{deviceType},
        now(3),
        now(3)
        )
    </insert>



</mapper>