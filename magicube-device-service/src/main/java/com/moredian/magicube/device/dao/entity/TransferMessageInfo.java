package com.moredian.magicube.device.dao.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2017/3/27
 */
@Data
public class TransferMessageInfo<T> implements Serializable {

    private static final long serialVersionUID = -2474406520783068804L;

    private String seqId;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 描述事件的信息
     */
    private String message;

    /**
     * 数据，根据需要自己组装内容
     */
    private T data;

    /**
     * 事件紧急程度
     */
    private Integer Severity;

    /**
     * 事件版本号，用于重复更新的case
     */
    private Integer version;
}
