package com.moredian.magicube.device.pipeline.pipe.device;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.activate.ActivationDeviceInfoDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 魔点设备校验管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MoDeviceCheckPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    @Autowired
    private DeviceManager deviceManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        // 1.判断缓存是否正常校验
        ActivationDeviceInfoDTO activationDeviceInfoDTO = (ActivationDeviceInfoDTO) redissonCacheComponent
            .getObjectCache(dto.getQrCode() + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_KEY);
        BizAssert.isTrue(activationDeviceInfoDTO != null, DeviceErrorCode.INVALID_ACTIVE_EXPIRE,
            DeviceErrorCode.INVALID_ACTIVE_EXPIRE.getMessage());
        activationDeviceInfoDTO.setDeviceType(dto.getDeviceType());
        context.setOrgId(activationDeviceInfoDTO.getOrgId());

        // 2.设备是否存在校验
        Device existDevice = deviceManager.getByDeviceSn(dto.getDeviceSn());
        BizAssert.isTrue(existDevice == null, DeviceErrorCode.DEVICE_EXIST,
            DeviceErrorCode.DEVICE_EXIST.getMessage());
    }
}
