package com.moredian.magicube.device.manager.impl;

import static com.google.common.collect.Sets.newHashSet;
import static org.apache.commons.collections.CollectionUtils.intersection;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.FacePicture;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceInstance.req.QueryDeviceListRequest;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyDefineDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyInfoDTO;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.DeviceIotPropertyConstants;
import com.moredian.magicube.device.constant.DeviceIotSplitConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dao.mapper.DeviceMapper;
import com.moredian.magicube.device.dto.device.PageQueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.iot.IotDeviceListDTO;
import com.moredian.magicube.device.dto.device.iot.IotKeyPropertyDTO;
import com.moredian.magicube.device.dto.device.iot.PageQueryIotDeviceListDTO;
import com.moredian.magicube.device.enums.IotDeviceStatusEnums;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypeManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.IotDeviceManager;
import com.moredian.magicube.device.manager.bo.QueryPageDeviceBO;
import com.moredian.magicube.device.manager.helper.IotDeviceManagerHelper;
import com.moredian.magicube.device.service.DeviceTypePropertyService;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class IotDeviceManagerImpl implements IotDeviceManager {

    @Autowired
    private DeviceMapper deviceMapper;
    @Resource
    private DeviceManager deviceManager;
    @Resource
    private IotDeviceManagerHelper iotDeviceManagerHelper;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private SpaceTreeDeviceRelationService treeDeviceRelationService;

    @SI
    private SpaceTreeService treeService;

    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;

    @Override
    public List<Device> listIotDevice(QueryIotDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        dto.setIotDeviceTypes(deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
        log.info("查询根节点iot设备列表，参数queryDeviceDTO:{}", dto);
        return deviceMapper.listIotDevice(dto);
    }

    @Override
    public List<Device> listDeviceByCondition(QueryIotDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        dto.setIotDeviceTypes(deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST));
        log.info("查询iot设备统计列表，参数queryDeviceDTO:{}", dto);
        return deviceMapper.listDeviceByCondition(dto);
    }


    @Override
    public Pagination<Device> listByConditionPage(PageQueryIotDeviceDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        Pagination<Device> pageResult = new Pagination<>();

        Set<Long> oSets = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
            oSets = newHashSet(dto.getDeviceIds());
        }
        //1.根据在线或者离线状态查询设备sn列表
        if (dto.getOnlineStatus() != null) {
            List<Long> deviceIds = listDeviceIdsByOnlineStatus(dto.getOrgId(),
                dto.getOnlineStatus());
            if (CollectionUtils.isEmpty(deviceIds)) {
                return pageResult;
            }
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> nSets = newHashSet(deviceIds);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, nSets)));
            } else {
                dto.setDeviceIds(deviceIds);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pageResult;
            }
            oSets = newHashSet(dto.getDeviceIds());
        }

        //2.位置条件查询
        if (CollectionUtils.isNotEmpty(dto.getTreeIds())) {
            List<TreeDeviceRelationDTO> relationDTOS;
            if (dto.getSubTreeFlag() != null && !dto.getSubTreeFlag()) {
                relationDTOS = treeDeviceRelationService.listByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            } else {
                relationDTOS = treeDeviceRelationService.listAllByTreeIdsAndSource(dto.getOrgId(),
                    dto.getTreeIds(), 1).pickDataThrowException();
            }
            //没有关联设备直接返回
            if (CollectionUtils.isEmpty(relationDTOS)) {
                return pageResult;
            }
            List<Long> deviceIdList = relationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId)
                .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                Set<Long> deviceIdSet = newHashSet(deviceIdList);
                dto.setDeviceIds(new ArrayList<>(intersection(oSets, deviceIdSet)));
            } else {
                dto.setDeviceIds(deviceIdList);
            }
            if (CollectionUtils.isEmpty(dto.getDeviceIds())) {
                return pageResult;
            }
        }

        //3.模糊搜索位置（空间全路径+设备位置）
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            List<Long> deviceIds = treeService.listDeviceIdByLikeTreeName(dto.getOrgId(),
                dto.getKeywords()).pickDataThrowException();
            dto.setQueryDeviceIds(deviceIds);
        }

        //4.只查询iot设备,并判断是否去除需要拆分的iot设备类型
        dto.setIotDeviceTypes(deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.IOT_DEVICE_LIST));
        if (dto.getFilterSplitDevice() != null && dto.getFilterSplitDevice()){
            dto.setVirtualFlag(YesNoFlag.YES.getValue());
            dto.getIotDeviceTypes().removeAll(deviceTypePropertyManager
                .getDeviceTypeByPropertyKey(DeviceTypeConstants.NEED_SPLIT_IOT_DEVICE_LIST));
        }
        log.info("查询Iot设备信息列表，参数queryDeviceDTO:{}", dto);

        Page<Device> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(),
                         dto.getPageSize() == null ? 20 : dto.getPageSize()).setOrderBy("gmt_create DESC")
                         .doSelectPage(() -> deviceMapper.listIotByConditionPage(dto));
        if (CollectionUtils.isEmpty(page)) {
            return pageResult;
        }
        pageResult.setData(page.getResult());
        pageResult.setTotalCount((int) page.getTotal());
        return pageResult;
    }

    @Override
    public Device getById(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        return deviceMapper.getByOrgIdAndId(orgId, deviceId);
    }

    @Override
    public Pagination<IotDeviceListDTO> listByConditionPage(PageQueryIotDeviceListDTO dto) {
        Long orgId = dto.getOrgId();
        BizAssert.notNull(orgId, "orgId must be not null");

        QueryPageDeviceBO deviceBo = new QueryPageDeviceBO();
        BeanUtils.copyProperties(dto, deviceBo);
        deviceBo.setSubTreeFlag(Boolean.TRUE);
        Pagination<Device> devicePage = deviceManager.listPageByCondition(deviceBo);
        List<Device> devices = devicePage.getData();

        Pagination<IotDeviceListDTO> resPage = new Pagination<>();
        resPage.setPageNo(devicePage.getPageNo());
        resPage.setPageSize(devicePage.getPageSize());
        resPage.setTotalCount(devicePage.getTotalCount());
        resPage.setData(Lists.newArrayList());
        if (CollectionUtil.isNotEmpty(devices)) {
            // 构建基础数据
            List<IotDeviceListDTO> iotDeviceDTOS = buildIotDeviceList(orgId, devices);
            if(Boolean.TRUE.equals(dto.getPropertyValue())) {
                // 构建iot设备属性信息
                buildIotDeviceProperty(orgId, iotDeviceDTOS);
            }
            resPage.getData().addAll(iotDeviceDTOS);
        }
        return resPage;
    }

    /**
     * 构建 iot 设备属性字段
     */
    private void buildIotDeviceProperty(Long orgId, List<IotDeviceListDTO> iotDeviceDTOS) {
        List<String> deviceSns = iotDeviceDTOS.stream()
                .map(IotDeviceListDTO::getDeviceSn)
                .distinct()
                .collect(Collectors.toList());
        Map<String, List<IotDevicePropertyInfoDTO>> lastPropertyMap = iotDeviceManagerHelper.getIotDeviceLastProperties(orgId, deviceSns);
        Map<String, List<IotDevicePropertyDefineDTO>> deviceDefinePropertyMap = iotDeviceManagerHelper.getDeviceDefineProperty(orgId, deviceSns);

        for (IotDeviceListDTO iotDeviceDTO : iotDeviceDTOS) {
            // 这里可能是虚拟设备SN
            final String deviceSn = iotDeviceDTO.getDeviceSn();
            Boolean virtualFlag = iotDeviceDTO.getVirtualFlag();
            Integer deviceType = iotDeviceDTO.getDeviceType();
            String parseDeviceSn = iotDeviceManagerHelper.parseIotDeviceSn(deviceSn);

            // 获取最新属性需要使用解析后的sn
            List<IotDevicePropertyDefineDTO> propertyList = deviceDefinePropertyMap.get(parseDeviceSn);
            List<IotDevicePropertyInfoDTO> lasePropertyList = lastPropertyMap.getOrDefault(parseDeviceSn, Lists.newArrayList());
            Map<String, IotDevicePropertyInfoDTO> deviceLastPropertyMap = lasePropertyList.stream()
                    .collect(Collectors.toMap(IotDevicePropertyInfoDTO::getProperty, Function.identity()));
            List<IotKeyPropertyDTO> keyPropertyList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(propertyList)) {
                 keyPropertyList = propertyList.stream()
                        .filter(e -> {
                            String property = e.getId();
                            // 过滤虚拟属性
                            if (virtualFlag) {
                                String[] split = deviceSn.split(DeviceIotSplitConstants.UNDERLINE);
                                if (split.length < 2) {
                                    return Boolean.FALSE;
                                } else {
                                    String snSuffix = split[1];
                                    if (!property.contains(snSuffix)) {
                                        return Boolean.FALSE;
                                    }
                                }
                            }
                            // 过滤关键属性
                            List<String> keyList = DeviceIotPropertyConstants.IOT_DEVICE_KEY_PROPERTY.get(deviceType);
                            if (CollectionUtil.isNotEmpty(keyList)) {
                                for (String key : keyList) {
                                    if (property.contains(key)) {
                                        return Boolean.TRUE;
                                    }
                                }
                                return Boolean.FALSE;
                            }
                            return Boolean.TRUE;
                        }).map(e -> {
                            String id = e.getId();
                            IotDevicePropertyInfoDTO last = deviceLastPropertyMap.getOrDefault(id, new IotDevicePropertyInfoDTO());
                            IotKeyPropertyDTO keyProperty = new IotKeyPropertyDTO();
                            keyProperty.setPropertyId(e.getId());
                            // 虚拟设备物模型名称去掉数字
                            String newName = e.getName().replaceAll("\\d+", StringUtils.EMPTY);
                            keyProperty.setPropertyName(newName);
                            keyProperty.setValue(last.getValue());
                            keyProperty.setFormatValue(last.getFormatValue());
                            return keyProperty;
                        }).collect(Collectors.toList());
            }
            iotDeviceDTO.setKeyPropertyList(keyPropertyList);
        }
    }

    /**
     * 构建设备列表基础数据
     * @return
     */
    private List<IotDeviceListDTO> buildIotDeviceList(Long orgId, List<Device> devices) {
        List<IotDeviceListDTO> result = new ArrayList<>();

        List<String> deviceSns = new ArrayList<>();
        List<Long> deviceIds = new ArrayList<>();
        devices.forEach(e-> {
            deviceSns.add(e.getDeviceSn());
            deviceIds.add(e.getDeviceId());
        });

        Map<Long, TreeDeviceRelationDTO> deviceTreeMap = iotDeviceManagerHelper.getDeviceTreeRelation(orgId, deviceIds);
        Map<String, Boolean> deviceOnlineMap = iotDeviceManagerHelper.getIotDeviceStateMap(orgId, deviceSns);

        // 构建数据
        for (Device device : devices) {
            IotDeviceListDTO deviceListDTO = iotDeviceManagerHelper.deviceConvertIotDeviceList(device);
            // 填充空间信息
            TreeDeviceRelationDTO relationDTO = deviceTreeMap.get(device.getDeviceId());
            if (relationDTO != null) {
                String pathTreeName = relationDTO.getPathTreeName();
                deviceListDTO.setTreeId(relationDTO.getTreeId());
                if (StringUtil.isNotBlank(pathTreeName)) {
                    deviceListDTO.setPathTreeName(relationDTO.getPathTreeName() + "/" + device.getPosition());
                }else {
                    deviceListDTO.setPathTreeName(device.getPosition());
                }
            }else {
                deviceListDTO.setPathTreeName(device.getPosition());
            }
            //填充设备在离线信息
            String parseDeviceSn = iotDeviceManagerHelper.parseIotDeviceSn(device.getDeviceSn());
            Boolean deviceState = deviceOnlineMap.getOrDefault(parseDeviceSn, Boolean.FALSE);
            deviceListDTO.setOnline(deviceState);
            result.add(deviceListDTO);
        }
        return result;
    }

    /**
     * 根据设备在线状态获取设备Id列表
     *
     * @param orgId        机构号
     * @param onlineStatus 设备在线状态 0-离线 1-在线
     * @return
     */
    private List<Long> listDeviceIdsByOnlineStatus(Long orgId, Integer onlineStatus) {
        List<Long> deviceIds = new ArrayList<>();
        QueryDeviceListRequest request = new QueryDeviceListRequest();
        request.setOrgId(orgId);
        request.setState(IotDeviceStatusEnums.getByCode(onlineStatus));
        List<IotDeviceInfoDTO> iotDeviceInfoDTOS = iotDeviceInstanceService.
            queryDeviceInfoListByCondition(request).pickDataThrowException();
        if (CollectionUtils.isEmpty(iotDeviceInfoDTOS)) {
            return deviceIds;
        }
        List<String> iotDeviceIds = iotDeviceInfoDTOS.stream()
            .map(IotDeviceInfoDTO::getId).distinct().collect(Collectors.toList());
        List<Device> devices = deviceMapper.listByDeviceSns(iotDeviceIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            deviceIds = devices.stream().map(Device::getDeviceId).collect(Collectors.toList());
        }
        return deviceIds;
    }
}