package com.moredian.magicube.device.jetlinks.rule.arrange;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description：规则编排tab展示
 * @date ：2024/08/06 11:56
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class TabNode extends Node{

    public static final String TYPE = "tab";

    private Boolean disabled;

    private String info;

    private String label;

    public TabNode(){
        super(TYPE);
    }
}
