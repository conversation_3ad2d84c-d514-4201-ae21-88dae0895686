package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.dao.entity.DeviceAccount;
import com.moredian.magicube.device.manager.DeviceAccountManager;
import com.moredian.magicube.device.service.DeviceAccountService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Author: limh
 * @Date: 2020/4/10 18:51
 */

@SI
public class DeviceAccountServiceImpl implements DeviceAccountService {

    @Autowired
    DeviceAccountManager deviceAccountManager;

    @Override
    public ServiceResponse<Boolean> addDeviceSnWithAccountId(Long accountId, List<String> snList) {
        ServiceResponse<Boolean> serviceResponse = new ServiceResponse<>(true);
        DeviceAccount deviceAccount = new DeviceAccount();
        deviceAccount.setAccountId(accountId);

        List<DeviceAccount> list = deviceAccountManager.queryByCond(deviceAccount);
        for (DeviceAccount account : list) {
            if (snList.contains(account.getSn())) {
                account.setStatus(YesNoFlag.YES.getValue());
                deviceAccountManager.update(account);
                snList.remove(account.getSn());
            }
        }

        if (CollectionUtils.isNotEmpty(snList)) {
            int i = deviceAccountManager.insertBatch(accountId, snList);
            if (i <= 0) {
                serviceResponse.setData(false);
            }
        }

        return serviceResponse;
    }
}