package com.moredian.magicube.device.third.dahua;

import com.netsdk.demo.module.VideoStateSummaryModule;
import com.netsdk.lib.NetSDKLib;
import com.sun.jna.Pointer;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import lombok.extern.slf4j.Slf4j;

/**
 * 网络连接恢复，设备重连成功回调
 * 通过 CLIENT_SetAutoReconnect 设置该回调函数，当已断线的设备重连成功时，SDK会调用该函数
 */

@Slf4j
public class HaveReConnectCallBack implements NetSDKLib.fHaveReConnect {

    // 人数统计回调事件
    public static PeopleNumberStatisticCallBack peopleNumberStatisticCB = PeopleNumberStatisticCallBack.getInstance();

    @Override
    public void invoke(NetSDKLib.LLong m_hLoginHandle, String pchDVRIP, int nDVRPort, Pointer dwUser) {
        log.info("ReConnect Device[%s] Port[%d]", pchDVRIP, nDVRPort);
        // 断线后需要重新订阅
        ExecutorService service = Executors.newSingleThreadExecutor();
        service.execute(() -> {
                // 重订阅事件
                VideoStateSummaryModule.reAttachAllVideoStatSummary(peopleNumberStatisticCB);
        });
        service.shutdown();
    }
}
