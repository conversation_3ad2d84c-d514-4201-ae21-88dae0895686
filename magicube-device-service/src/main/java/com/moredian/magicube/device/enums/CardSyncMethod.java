package com.moredian.magicube.device.enums;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2024/9/27 11:01
 */
public enum CardSyncMethod {

    CARD_OLD_SYNC("old","卡片老同步"),
    CARD_NEW_SYNC("new","卡片新同步"),
    /**
     * 考虑到有的设备没接入新同步
     */
    CARD_ALL_SYNC("all","卡片老同步+新同步一起发");


    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CardSyncMethod(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CardSyncMethod getEnumByCode(String code) {
        BizAssert.isTrue(StringUtil.isNotBlank(code), "code cannot be empty");

        CardSyncMethod[] values = CardSyncMethod.values();
        for (CardSyncMethod value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
