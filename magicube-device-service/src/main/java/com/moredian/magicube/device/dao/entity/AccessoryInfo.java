package com.moredian.magicube.device.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 配件信息
 *
 * <AUTHOR>
 * @since 2021/12/8
 */
@Data
public class AccessoryInfo implements Serializable {

    private static final long serialVersionUID = -6584625133448638700L;

    /**
     * 设备配件id
     */
    private Long accessoryId;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 配件sn
     */
    private String accessorySn;

    /**
     * 配件名
     */
    private String accessoryName;

    /**
     * 配件类型
     */
    private Integer accessoryType;

    /**
     * 关联设备ID
     */
    private Long deviceId;

    /**
     * 关联设备sn
     */
    private String deviceSn;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModify;
    /**
     * 匹配状态，0-不匹配，1-匹配
     */
    private Integer matchStatus;

}
