package com.moredian.magicube.device.manager;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;

import java.util.List;

public interface DeviceAppRelationConfigManager {



    /**
     * 分页查询信息
     *
     * @param dto 查询条件
     * @return
     */
    Pagination<DeviceAppRelationConfig> listPage(QueryDeviceAppRelationConfigDTO dto);


    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    List<DeviceAppRelationConfig> listAll();

    /**
     * 新增
     * @param config
     * @return
     */
    Long insert(DeviceAppRelationConfig config);

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteById( Long id);

    /**
     *  更新
     * @param config
     * @return
     */
    int updateById(DeviceAppRelationConfig config);

    /**
     * 更新不为空的字段
     * @param config
     * @return
     */
    int updateSelectiveById(DeviceAppRelationConfig config);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    DeviceAppRelationConfig selectById(Long id);

    /**
     * 条件查询
     * @param spaceType
     * @param appType
     * @param versionCode
     * @return
     */
    List<DeviceAppRelationConfig> selectBySpaceTypeAndAppTypeAndVersionCode(
            Integer spaceType,
            Integer appType,
            Integer versionCode
    );

    /**
     * 条件查询
     * @param appType
     * @param versionCode
     * @return
     */
    List<DeviceAppRelationConfig> selectAllByAppTypeAndVersionCode(Integer appType, Integer versionCode);


    /**
     * 更具id主键更新或者新增
     */
    void addOrUpdate(List<DeviceAppRelationConfig> list);
}
