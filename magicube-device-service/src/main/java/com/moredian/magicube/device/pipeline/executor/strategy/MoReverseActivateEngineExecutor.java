package com.moredian.magicube.device.pipeline.executor.strategy;


import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.executor.AbstractActivateDeviceEngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.BindSpaceTreePipe;
import com.moredian.magicube.device.pipeline.pipe.device.DeviceNameConvertPipe;
import com.moredian.magicube.device.pipeline.pipe.ding.BindingGroupPipe;
import com.moredian.magicube.device.pipeline.pipe.md.MdArithmeticPipe;
import com.moredian.magicube.device.pipeline.pipe.reverse.InsertActivationRecordPipe;
import com.moredian.magicube.device.pipeline.pipe.reverse.ReverseOrgIdPipe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 魔蓝渠道反扫二维码激活引擎执行器
 *
 * <AUTHOR>
 */
@Component
public class MoReverseActivateEngineExecutor extends AbstractActivateDeviceEngineExecutor {

    @Autowired
    private MdArithmeticPipe mdArithmeticPipe;

    @Autowired
    private DeviceNameConvertPipe deviceNameConvertPipe;

    @Autowired
    protected BindingGroupPipe bindingGroupPipe;

    @Autowired
    private InsertActivationRecordPipe insertActivationRecordPipe;

    @Autowired
    private BindSpaceTreePipe bindSpaceTreePipe;

    @Autowired
    private ReverseOrgIdPipe reverseOrgIdPipe;

    @Override
    public void afterPropertiesSet() {
        pipeline.addPipe(reverseOrgIdPipe);
        pipeline.addPipe(commonActivateCheckPipe);
        pipeline.addPipe(checkDeviceAuthorizePipe);
        pipeline.addPipe(mdArithmeticPipe);
        pipeline.addPipe(deviceNameConvertPipe);
        pipeline.addPipe(insertDevicePipe);
        pipeline.addPipe(uploadApkVersionPipe);
//        pipeline.addPipe(bindingGroupPipe);
        pipeline.addPipe(enableBizPipe);
        pipeline.addPipe(bindSpaceTreePipe);
        pipeline.addPipe(insertDeviceLogPipe);
        pipeline.addPipe(insertActivationRecordPipe);
        pipeline.addPipe(updateDeviceWhitePipe);
        pipeline.addPipe(monitorDataPipe);
        pipeline.addPipe(publishMsgPipe);
        pipeline.addPipe(buildResultPipe);
    }

    @Override
    protected void validCustomParameter(ActivateDeviceDTO dto) {
    }

    @Override
    protected String setKey(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return "MoReverseActivate_" + dto.getDeviceType();
    }

    @Override
    public Integer getType() {
        return DeviceActivateEngineEnums.MO_REVERSE_ACTIVATE.getCode();
    }

}
