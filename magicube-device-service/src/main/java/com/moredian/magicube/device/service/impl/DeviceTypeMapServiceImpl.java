package com.moredian.magicube.device.service.impl;

import com.google.common.collect.Lists;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.type.DeviceCapacityDTO;
import com.moredian.magicube.device.dto.type.DeviceSnTypeMappingDTO;
import com.moredian.magicube.device.dto.type.SaveDeviceCapacityDTO;
import com.moredian.magicube.device.manager.DeviceTypeMapManager;
import com.moredian.magicube.device.service.DeviceTypeMapService;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023-05-18
 */
@SI
public class DeviceTypeMapServiceImpl implements DeviceTypeMapService {

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Override
    public ServiceResponse<String> getValue(String mapName, String mapKey) {
        return new ServiceResponse<>(deviceTypeMapManager.getValue(mapName, mapKey));
    }

    @Override
    public ServiceResponse<Integer> getDeviceCapacity(Integer deviceType) {
        return new ServiceResponse<>(deviceTypeMapManager.getDeviceCapacity(deviceType));
    }

    @Override
    public ServiceResponse<List<DeviceCapacityDTO>> getDeviceListCapacity(List<Integer> deviceTypeList) {
        return new ServiceResponse<>(deviceTypeMapManager.getDeviceListCapacity(deviceTypeList));
    }

    @Override
    public ServiceResponse<Boolean> saveDeviceCapacity(SaveDeviceCapacityDTO dto) {
        return new ServiceResponse<>(deviceTypeMapManager.saveDeviceCapacity(dto.getDeviceType(), dto.getCapacity()));
    }

    @Override
    public ServiceResponse<String> getDefaultSpu(Integer deviceType) {
        return new ServiceResponse<>(deviceTypeMapManager.getDefaultSpu(deviceType));
    }

    @Override
    public ServiceResponse<String> getConvertName(Integer deviceType) {
        return new ServiceResponse<>(deviceTypeMapManager.getConvertName(deviceType));
    }

    @Override
    public ServiceResponse<Integer> getTypeByDeviceSn(String deviceSn) {
        return new ServiceResponse<>(deviceTypeMapManager.getTypeByDeviceSn(deviceSn));
    }

    @Override
    public ServiceResponse<List<DeviceSnTypeMappingDTO>> getTypeByDeviceSnList(
        List<String> deviceSnList) {
        Map<String/*deviceSn*/, Integer/*deviceType*/> typeByDeviceSnMap = deviceTypeMapManager.getTypeByDeviceSnList(deviceSnList);
        return new ServiceResponse<>(buildDeviceSnTypeMappingDTOList(typeByDeviceSnMap));
    }

    // ---------------------------------------------------------------- private ----------------------------------------------------------------

    /**
     * Map <deviceSn, deviceType> ->  List<DeviceSnTypeMappingDTO>
     *
     * @param typeByDeviceSnMap
     * @return
     */
    private List<DeviceSnTypeMappingDTO> buildDeviceSnTypeMappingDTOList(
        Map<String, Integer> typeByDeviceSnMap) {
        List<DeviceSnTypeMappingDTO> deviceSnTypeMappingDTOList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(typeByDeviceSnMap)) {
            for (String deviceSn : typeByDeviceSnMap.keySet()) {
                DeviceSnTypeMappingDTO deviceSnTypeMappingDTO = new DeviceSnTypeMappingDTO();
                deviceSnTypeMappingDTO.setDeviceSn(deviceSn);
                deviceSnTypeMappingDTO.setDeviceType(typeByDeviceSnMap.get(deviceSn));
                deviceSnTypeMappingDTOList.add(deviceSnTypeMappingDTO);
            }
        }
        return deviceSnTypeMappingDTOList;
    }

}
