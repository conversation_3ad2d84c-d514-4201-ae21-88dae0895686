package com.moredian.magicube.device.dao.mapper;


import com.moredian.magicube.device.dao.entity.DeviceSnapshot;
import com.moredian.magicube.device.model.QueryDeviceSnapshotModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 设备快照表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@Mapper
public interface DeviceSnapshotMapper {

    /**
     * 查询设备快照
     *
     * @param model
     * @return
     */
    DeviceSnapshot getByOrgIdAndDeviceId(QueryDeviceSnapshotModel model);

    /**
     * 保存设备快照
     *
     * @param deviceSnapshot
     * @return
     */
    int save(DeviceSnapshot deviceSnapshot);

    /**
     * 删除快照
     *
     * @param id
     * @return
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询设备快照
     *
     * @param id
     * @return
     */
    DeviceSnapshot getById(@Param("id") Long id);

    /**
     * 查询超过保留时间的快照
     *
     * @param date
     * @return
     */
    List<DeviceSnapshot> queryOvertimeDeviceSnapshot(Date date);
}
