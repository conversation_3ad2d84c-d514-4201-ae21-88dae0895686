package com.moredian.magicube.device.service.strategy;

import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyDefineDTO;
import com.moredian.magicube.device.constant.DeviceIotSplitConstants;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.enums.DeviceTypeSwitchCodeEnum;
import com.moredian.magicube.device.enums.ModeTriggerEnum;
import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: ModeTriggerStrategy.java, v 1.0 Exp $
 */
public interface ModeTriggerTemplateParseStrategy {
    Object getSceneRule(Rule rule, List<TreeDeviceRelationDTO> treeDeviceReationList,
        Map<String, IotDeviceInfoDTO> iotDeviceMap,
        Map<String, BatchIotDevicePropertyInfoDTO> iotDevicePropertyMap,
        List<Integer> selectDeviceTypes);
    ModeTriggerEnum getTemplate();

    default String getPropertyValueName(IotDevicePropertyDefineDTO iotDevicePropertyDefine) {
        Map<String, Object> valueType = iotDevicePropertyDefine.getValueType();
        Object typeObj = valueType.get("type");
        if (Objects.equals(String.valueOf(typeObj), "enum")) {
            Object elementObj = valueType.get("elements");
            if (elementObj != null) {
                List<Map<String, Object>> elementsMap = (List<Map<String, Object>>)elementObj;
                Optional<Map<String, Object>> firstOptional = elementsMap.stream()
                        .filter(v -> Objects.equals(String.valueOf(v.get("value")), String.valueOf(RuleStateEnum.DISABLE.getCode()))).findFirst();
                if (firstOptional.isPresent()) {
                    Map<String, Object> elementMap = firstOptional.get();
                    return String.valueOf(elementMap.get("text"));
                }
            }
        }
        return null;
    }

    default IotDevicePropertyDefineDTO getSwitchPropertyDefine(Map<String, BatchIotDevicePropertyInfoDTO>  iotDevicePropertyMap, IotDeviceInfoDTO iotDevice, String propertyId) {
        BatchIotDevicePropertyInfoDTO batchIotDeviceProperty = iotDevicePropertyMap.get(iotDevice.getId());
        List<IotDevicePropertyDefineDTO> properties = batchIotDeviceProperty.getProperties();
        if (CollectionUtils.isEmpty(properties)) {
            return null;
        }
        List<IotDevicePropertyDefineDTO> propertyDefineList = properties.stream().filter(v -> Objects.equals(v.getId(), propertyId)).collect(Collectors.toList());
        return CollectionUtils.isEmpty(propertyDefineList) ? null : propertyDefineList.get(0);
    }

    default IotDeviceInfoDTO getIotDevice(TreeDeviceRelationDTO treeDeviceRelation, Map<String, IotDeviceInfoDTO> iotDeviceMap) {
        boolean virtualIotDevice = this.virtualIotDevice(treeDeviceRelation.getDeviceType());
        String deviceSn = treeDeviceRelation.getDeviceSn();
        if (virtualIotDevice && !deviceSn.contains(DeviceIotSplitConstants.UNDERLINE)) {
            return null;
        }
        return virtualIotDevice ? iotDeviceMap.get(treeDeviceRelation.getParentDeviceSn()) : iotDeviceMap.get(deviceSn);
    }

    default String getGroup(TreeDeviceRelationDTO treeDeviceRelation) {
        boolean virtualIotDevice = this.virtualIotDevice(treeDeviceRelation.getDeviceType());
        String deviceSn = treeDeviceRelation.getDeviceSn();
        return virtualIotDevice ? deviceSn.substring(deviceSn.lastIndexOf(DeviceIotSplitConstants.UNDERLINE) + 1) : null;
    }

    default String getPropertyId(TreeDeviceRelationDTO treeDeviceRelation, String group) {
        return DeviceTypeSwitchCodeEnum.getSwitchPropertyId(treeDeviceRelation.getDeviceType(), group);
    }

    /**
     * 虚拟设备的sn需要去掉_
     */
    default String parseDeviceSn(String deviceSn){
        return deviceSn.split(DeviceIotSplitConstants.UNDERLINE)[0];
    }

    Boolean virtualIotDevice(Integer deviceType);
}
