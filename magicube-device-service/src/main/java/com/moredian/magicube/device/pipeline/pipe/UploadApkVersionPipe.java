package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.version.ChangeDeviceAppVersionDTO;
import com.moredian.magicube.device.enums.EnvConfigEnum;
import com.moredian.magicube.device.manager.DeviceVersionManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 激活时上报appType及Version
 * <AUTHOR>
 * @date : 2024/5/9
 */
@Slf4j
@Component
public class UploadApkVersionPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private DeviceVersionManager deviceVersionManager;
    @Override
    protected boolean isFilter(ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext activateDeviceContext) {
        return false;
    }
    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        //如果appType或version为空,不进行上报
        if (dto.getAppType() == null || dto.getVersion() == null) {
            return;
        }
        log.info("upload appVersion [deviceSn:{},appType:{},appVersion:{}]", dto.getDeviceSn(),
                dto.getAppType(), dto.getVersion());
        ChangeDeviceAppVersionDTO deviceAppVersionDTO = new ChangeDeviceAppVersionDTO();
        deviceAppVersionDTO.setOrgId(context.getOrgId());
        deviceAppVersionDTO.setDeviceId(context.getDevice().getDeviceId());
        deviceAppVersionDTO.setSn(dto.getDeviceSn());
        deviceAppVersionDTO.setVersionCode(dto.getVersion());
        deviceAppVersionDTO.setAppType(dto.getAppType());
        deviceVersionManager.changeDeviceAppVersion(deviceAppVersionDTO);
    }
}
