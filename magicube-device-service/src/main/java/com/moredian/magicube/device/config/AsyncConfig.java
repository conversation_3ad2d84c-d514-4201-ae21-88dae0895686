package com.moredian.magicube.device.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/1/6
 */
@Configuration
public class AsyncConfig {

    @Value("${iotDevicePage.coreThread:20}")
    private Integer coreThread;

    @Value("${iotDevicePage.maxThread:20}")
    private Integer maxThread;

    @Value("${iotDevicePage.queue:40}")
    private Integer queue;

    @Value("${iotDevicePage.queue:40}")
    private Integer keepAliveSecond;



    @Bean("deviceReportVersionExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int core = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(9);
        executor.setKeepAliveSeconds(3);
        executor.setQueueCapacity(40);
        executor.setThreadNamePrefix("device-report-version-executor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("iotDevicePagePool")
    public Executor getIotDevicePagePool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreThread);
        executor.setMaxPoolSize(maxThread);
        executor.setKeepAliveSeconds(keepAliveSecond);
        executor.setQueueCapacity(queue);
        executor.setThreadNamePrefix("iot-devicePage-executor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
