<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.moredian.magicube.device.dao.mapper.PeripheralsMapper">

    <resultMap id="peripheralsResultMap" type="com.moredian.magicube.device.dao.entity.Peripherals">
        <result column="peripherals_id" property="peripheralsId"/>
        <result column="org_id" property="orgId"/>
        <result column="peripherals_name" property="peripheralsName"/>
        <result column="peripherals_sn" property="peripheralsSn"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="device_name" property="deviceName"/>
        <result column="status" property="status"/>
        <result column="pic_url" property="picUrl"/>
        <result column="model_type" property="modelType"/>
        <result column="available" property="available"/>
        <result column="bind_time" property="bindTime"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
        <result column="device_type" property="deviceType"/>
        <result column="peripherals_type" property="peripheralsType"/>
        <result column="peripherals_connect_id" property="peripheralsConnectId"/>
        <result column="mobile_sys_type" property="mobileSysType"/>
        <result column="match_status" property="matchStatus"/>
    </resultMap>

    <sql id="sql_table">
        hive_peripherals
    </sql>

    <sql id="sql_columns">
        peripherals_id,
        org_id,
        peripherals_name,
        peripherals_sn,
        device_sn,
        device_name,
        status,
        pic_url,
        model_type,
        available,
        device_type,
        peripherals_type,
        peripherals_connect_id,
        mobile_sys_type,
        bind_time,
        gmt_create,
        gmt_modify,
        match_status
    </sql>

    <sql id="sql_values">
        #{peripheralsId},
        #{orgId},
        #{peripheralsName},
        #{peripheralsSn},
        #{deviceSn},
        #{deviceName},
        #{status},
        #{picUrl},
        #{modelType},
        #{available},
        #{deviceType},
        #{peripheralsType},
        #{peripheralsConnectId},
        #{mobileSysType},
        now(3),
        now(3),
        now(3),
        #{matchStatus}
    </sql>

    <sql id="sql_where">
        <where>
            <if test="peripheralsId != null">
                and peripherals_id = #{peripheralsId}
            </if>
            <if test="orgId != null">
                and org_id = #{orgId}
            </if>
            <if test="peripheralsSn != null">
                and peripherals_sn = #{peripheralsSn}
            </if>
            <if test="deviceSn != null">
                and device_sn = #{deviceSn}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="matchStatus != null">
                and match_status = #{matchStatus}
            </if>
        </where>
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.Peripherals">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.Peripherals">
        update
        <include refid="sql_table"/>
        <set>
            <if test="peripheralsName!= null ">
                peripherals_name = #{peripheralsName},
            </if>
            <if test="available !=null">
                available = #{available},
            </if>
            <if test="deviceSn != null ">
                device_sn = #{deviceSn},
            </if>
            <if test="deviceName != null ">
                device_name = #{deviceName},
            </if>
            <if test="status != null ">
                status = #{status},
            </if>
            <if test="peripheralsType != null">
                peripherals_type = #{peripheralsType},
            </if>
            <if test="matchStatus != null">
                match_status = #{matchStatus},
            </if>
            gmt_modify = now(3),bind_time = now(3)
        </set>
        where peripherals_id = #{peripheralsId}
        and org_id = #{orgId}
    </update>

    <select id="listByCondition" parameterType="com.moredian.magicube.device.dto.peripherals.QueryPeripheralsDTO"
            resultMap="peripheralsResultMap">
        SELECT
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        <if test="peripheralsId != null">
            and peripherals_id = #{peripheralsId}
        </if>
        <if test="peripheralsSn != null">
            and peripherals_sn = #{peripheralsSn}
        </if>
        <if test="deviceSn != null">
            and device_sn = #{deviceSn}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="deviceType !=null">
            and device_type = #{deviceType}
        </if>
        <if test="peripheralsType != null">
            and peripherals_type = #{peripheralsType}
        </if>
        <if test="peripheralsConnectId != null">
            and peripherals_connect_id = #{peripheralsConnectId}
        </if>
        <if test="mobileSysType != null">
            and mobile_sys_type = #{mobileSysType}
        </if>
        <if test="deviceSns != null and deviceSns.size > 0">
            and device_sn in
            <foreach collection="deviceSns" index="index" item="deviceSn" open="(" separator="," close=")">
                #{deviceSn}
            </foreach>
        </if>
    </select>


    <select id="listByOrgIdAndDeviceSn" resultMap="peripheralsResultMap">
        SELECT
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_sn = #{deviceSn}
    </select>


    <select id="listByOtherOrg" parameterType="com.moredian.magicube.device.dao.entity.Peripherals"
            resultMap="peripheralsResultMap">
        SELECT
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id != #{orgId}
        and status != -1
        <if test="peripheralsSn != null">
            and peripherals_sn = #{peripheralsSn}
        </if>
        <if test="deviceType !=null">
            and device_type = #{deviceType}
        </if>
    </select>

    <delete id="cleanPeripherals">
        DELETE FROM
        <include refid="sql_table"/>
        where peripherals_id = #{peripheralsId}
        and org_id = #{orgId}
    </delete>

    <update id="updateByCondition" parameterType="com.moredian.magicube.device.dao.entity.Peripherals">
        update
        <include refid="sql_table"/>
        set status = #{status},gmt_modify = now(3)
        where org_id = #{orgId}
        <if test="peripheralsSn !=null">
            and peripherals_sn = #{peripheralsSn}
        </if>
        <if test="deviceSn != null">
            and device_sn = #{deviceSn}
        </if>
    </update>

    <select id="listByOrgIdAndDeviceSns" resultMap="peripheralsResultMap">
        SELECT
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and status = 1
        and device_sn in
        <foreach collection="deviceSns" index="index" item="deviceSn" open="(" separator="," close=")">
            #{deviceSn}
        </foreach>
    </select>

    <select id="listByOrgId" parameterType="long" resultType="long">
        SELECT peripherals_id
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
    </select>
</mapper>