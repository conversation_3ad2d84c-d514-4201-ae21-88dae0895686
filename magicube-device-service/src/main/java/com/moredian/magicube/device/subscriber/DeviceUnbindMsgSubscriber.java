package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.model.msg.DeviceUnbindMsg;
import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import com.moredian.magicube.device.manager.DeviceSubjectRelationManager;
import com.moredian.magicube.device.manager.MigrationDeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DeviceUnbindMsgSubscriber {


    @Autowired
    private DeviceSubjectRelationManager deviceSubjectRelationManager;
    @Resource
    private MigrationDeviceManager migrationDeviceManager;

    @Subscribe
    public void subDeviceUnbindMsg(DeviceUnbindMsg msg) {
        log.info("收到设备解绑消息: " + JsonUtils.toJson(msg));
        if (msg != null){
            long deviceId = msg.getDeviceId();
            List<DeviceSubjectRelation> deviceSubjectRelations = deviceSubjectRelationManager.listByOrgIdAndDeviceIds(msg.getOrgId(), Collections.singletonList(deviceId), null);
            if (CollectionUtils.isNotEmpty(deviceSubjectRelations)){
                List<Long> subjectIdList = deviceSubjectRelations.stream().map(DeviceSubjectRelation::getSubjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(subjectIdList)){
                    deviceSubjectRelationManager.resetRelationByDeviceId(msg.getOrgId(), deviceId,new ArrayList<>(),1);
                }
            }
            // 一键上钉
            migrationDeviceManager.subDeviceUnbindMsg(msg.getOrgId(),msg.getDeviceId());
        }
    }

}
