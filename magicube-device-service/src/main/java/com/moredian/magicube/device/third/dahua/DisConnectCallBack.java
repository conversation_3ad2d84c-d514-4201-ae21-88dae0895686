package com.moredian.magicube.device.third.dahua;

import com.netsdk.lib.NetSDKLib;
import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;

/**
 * 设备断线回调: 通过 CLIENT_Init 设置该回调函数，当设备出现断线时，SDK会调用该函数
 */

@Slf4j
public class DisConnectCallBack implements NetSDKLib.fDisConnect {

    @Override
    public void invoke(NetSDKLib.LLong m_hLoginHandle, String pchDVRIP, int nDVRPort,
        Pointer dwUser) {
        log.info("Device[%s] Port[%d] DisConnectCallBack!\n", pchDVRIP, nDVRPort);
    }
}
