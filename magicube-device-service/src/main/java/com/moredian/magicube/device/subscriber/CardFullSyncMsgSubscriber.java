package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.constant.RedisKeysConstant;
import com.moredian.magicube.common.enums.TransferEventType;
import com.moredian.magicube.common.model.msg.CardFullSyncMsg;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;


/**
 * 卡完全量同步消息 指定某台设备全量同步
 *
 * <AUTHOR>
 * @date 2024/09/26 09:20
 */
@Slf4j
@Component
public class CardFullSyncMsgSubscriber {


    @Resource
    private DeviceManager deviceManager;

    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private SendMQTTBase sendMQTTBase;


    @Subscribe
    public void subPersonCardChange(CardFullSyncMsg msg) {
        log.info("收到指定设备全量同步卡消息: {}", JsonUtils.toJson(msg));
        if (msg == null || StringUtil.isBlank(msg.getDeviceSN())) {
            return;
        }
        // 判断设备是否支持刷卡，不支持刷卡要过滤
        // 可刷卡的设备类型列表
        List<Integer> enableType = deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.ENABLE_CARD_DEVICE_TYPE_LIST);
        Device device = deviceManager.getByDeviceSn(msg.getDeviceSN());
        if (device != null) {
            try {
                if (!enableType.contains(device.getDeviceType())) {
                    log.info("不支持刷卡的设备,无需进行卡的全量同步，deviceSN={}",
                        device.getDeviceSn());
                    return;
                }
                // 1、缓存中指定设备是否需要全量同步，key 1天过期，如果发了消息1天还没来拉取卡，那就无需处理了，每天晚上会自动同步，并且每次设备主动同步的时候会主动消费掉全量同步key
                String key = RedisKeysConstant
                    .getKey(RedisKeysConstant.DEVICE_NEED_FULL_SYNC_CARD, msg.getDeviceSN());
                stringRedisTemplate.opsForValue()
                    .set(key, JsonUtils.toJson(msg), 24, TimeUnit.HOURS);
                log.info("完成CardFullSyncMsg任务缓存-添加，deviceSN={}", msg.getDeviceSN());

                // 2、执行
                sendMQTTBase.doSwitchCardSyncSendMQTT(device, "CardFullSyncMsg",
                    TransferEventType.MEMBER_CARD_FULL_SYNC);
            } catch (Exception e) {
                log.error(
                    "Notify device CardFullSyncMsg failed. [deviceSn={},deviceId={}]，errorMsg={}",
                    device.getDeviceSn(), device.getDeviceId(), e.getMessage());
            }
        }
    }

}
