package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_type")
public class DeviceType extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 设备类型Id
     */
    private Long id;

    /**
     * 设备类型code
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备类型描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 设备类型图片
     */
    private String deviceTypePic;

    /**
     * 产品型号（子型号）编码
     */
    private String productModelCode;

    /**
     * 设备类型内部名称
     */
    private String deviceTypeInnerName;

    /**
     * 元石行业设备类型名称
     */
    private String deviceTypeNameHy;

    /**
     * 钉钉分销设备类型名称
     */
    private String deviceTypeNameDing;

    /**
     * 魔蓝分销设备类型名称
     */
    private String deviceTypeNameMl;

    /**
     * 元石行业设备类型展示名称
     */
    private String deviceTypeDisplayNameHy;

    /**
     * 钉钉分销设备类型展示名称
     */
    private String deviceTypeDisplayNameDing;

    /**
     * 魔蓝分销设备类型展示名称
     */
    private String deviceTypeDisplayNameMl;

    /**
     * 设备类型基础配置
     */
    private String config;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
