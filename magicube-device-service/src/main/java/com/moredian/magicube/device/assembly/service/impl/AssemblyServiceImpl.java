package com.moredian.magicube.device.assembly.service.impl;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.assembly.job.JobSendMsg;
import com.moredian.magicube.device.assembly.request.ObjectAndClassBeanDto;
import com.moredian.magicube.device.assembly.service.AssemblyService;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * @date 2024/12/2 17:08
 */
@Slf4j
@SI
public class AssemblyServiceImpl implements AssemblyService {

    /**
     * 静态map
     */
    public static Map<String,Class<?>> classMaps = new HashMap<String,Class<?>>();


    @Resource
    private RedisTemplate<String, String> redisTemplate;


    @Override
    public Integer publish(Object msg) {
        return JobSendMsg.publish(msg);
    }


    @Override
    public Integer publish(Object msg, Class<?> clazz, Long orgId, String key) {
        return publish(msg, clazz, orgId, key, msg.getClass() + ":" + orgId);
    }

    @Override
    public Integer publish(Object msg, Class<?> clazz, Long orgId, String key, String redisKey) {

        // 1、缓存好降噪键和内容  key=（strkey:降噪key）  value=实体
        ObjectAndClassBeanDto dto = new ObjectAndClassBeanDto();
        dto.setMsgStr(com.moredian.bee.rmq.utils.JsonUtils.serialize(msg));
//        dto.setClazz(clazz);
        dto.setClazzName(clazz.getCanonicalName());
        // 塞入静态map中
        classMaps.put(dto.getClazzName(),clazz);

        redisTemplate.opsForValue()
            .set(JobSendMsg.getStrKey(redisKey) + ":" + key, JsonUtils.toJson(dto), 3L,
                TimeUnit.DAYS);

        // 2、使用 Redis 的 ZSet 结构存储减噪key和当前时间戳  key=zsetkey value=降噪key
        double score = (double) Instant.now().getEpochSecond();
        redisTemplate.opsForZSet().add(JobSendMsg.getZsetKey(redisKey), key, score);
        // 设置过期时间为7天
        redisTemplate.expire(redisKey, 3L, TimeUnit.DAYS);

        // 3、把内容存入到降噪消息任务key中
        redisTemplate.opsForZSet().add(JobSendMsg.NOISE_REDUCTION_MESSAGE, redisKey, score);
        // 设置过期时间为7天
        redisTemplate.expire(JobSendMsg.NOISE_REDUCTION_MESSAGE, 3L, TimeUnit.DAYS);
        return 1;
    }


    @Override
    public Integer publish(Object msg, Class<?> clazz, Long orgId, List<String> keyList,
        String resetField) {
        return publish(msg, clazz, orgId, keyList, msg.getClass().getCanonicalName() + ":" + orgId, resetField);
    }


    @Override
    public Integer publish(Object msg, Class<?> clazz, Long orgId, List<String> keyList,
        String redisKey, String resetField) {

        // 1、缓存好降噪键和内容  key=（strkey:降噪key）  value=实体
        ObjectAndClassBeanDto dto = new ObjectAndClassBeanDto();
        dto.setMsgStr(com.moredian.bee.rmq.utils.JsonUtils.serialize(msg));
//        dto.setClazz(clazz);
        dto.setClazzName(clazz.getCanonicalName());
        // 塞入静态map中
        classMaps.put(dto.getClazzName(),clazz);

        dto.setMerge(Boolean.TRUE);
        dto.setResetField(resetField);
        // 2、使用 Redis 的 ZSet 结构存储减噪key和当前时间戳  key=zsetkey value=降噪key
        double score = (double) Instant.now().getEpochSecond();

        for (String key : keyList) {
            redisTemplate.opsForValue()
                .set(JobSendMsg.getStrKey(redisKey) + ":" + key, JsonUtils.toJson(dto), 3L,
                    TimeUnit.DAYS);
            redisTemplate.opsForZSet().add(JobSendMsg.getZsetKey(redisKey), key, score);
            // 设置过期时间为7天
            redisTemplate.expire(redisKey, 3L, TimeUnit.DAYS);
        }

        // 3、把内容存入到降噪消息任务key中
        try {
            Boolean added = redisTemplate.opsForZSet().add(JobSendMsg.NOISE_REDUCTION_MESSAGE, redisKey, score);
            if (Boolean.FALSE.equals(added)) {
                log.error("ZSet add failed for key={}, member={}", JobSendMsg.NOISE_REDUCTION_MESSAGE, redisKey);
            }
        } catch (Exception e) {
            log.error("ZSet add exception for key={}, member={}, error={}", JobSendMsg.NOISE_REDUCTION_MESSAGE, redisKey, e.getMessage(), e);
        }
        log.info(" -- 降噪消息-写入redisKey:{},score:{}", redisKey, score);
        double maxScore1 = (double) Instant.now().getEpochSecond();
        Set<String> redisKeySet1 = (Set<String>) redisTemplate.opsForZSet()
            .rangeByScore(JobSendMsg.NOISE_REDUCTION_MESSAGE, 0, maxScore1);
        log.info(" -- 降噪消息-读出1 minScore：{},maxScore1：{}, redisKeySet1:{}", 0, maxScore1, redisKeySet1);
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        double maxScore2 = (double) Instant.now().getEpochSecond();
        Set<String> redisKeySet2 = (Set<String>) redisTemplate.opsForZSet()
            .rangeByScore(JobSendMsg.NOISE_REDUCTION_MESSAGE, 0, maxScore2);
        log.info(" -- 降噪消息-读出2 minScore：{},maxScore：{}, redisKeySet:{}", 0, maxScore2, redisKeySet2);
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        double maxScore3 = (double) Instant.now().getEpochSecond();
        Set<String> redisKeySet3 = (Set<String>) redisTemplate.opsForZSet()
            .rangeByScore(JobSendMsg.NOISE_REDUCTION_MESSAGE, 0, maxScore3);
        log.info(" -- 降噪消息-读出3 minScore3：{},maxScore3：{}, redisKeySet3:{}", 0, maxScore3, redisKeySet3);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        double maxScore4 = (double) Instant.now().getEpochSecond();
        Set<String> redisKeySet4 = (Set<String>) redisTemplate.opsForZSet()
            .rangeByScore(JobSendMsg.NOISE_REDUCTION_MESSAGE, 0, maxScore4);
        log.info(" -- 降噪消息-读出4 minScore4：{},maxScore4：{}, redisKeySet4:{}", 0, maxScore4, redisKeySet4);
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        double maxScore5 = (double) Instant.now().getEpochSecond();
        Set<String> redisKeySet5 = (Set<String>) redisTemplate.opsForZSet()
            .rangeByScore(JobSendMsg.NOISE_REDUCTION_MESSAGE, 0, maxScore5);
        log.info(" -- 降噪消息-读出5 minScore5：{},maxScore5：{}, redisKeySet5:{}", 0, maxScore5, redisKeySet5);
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        double maxScore6 = (double) Instant.now().getEpochSecond();
        Set<String> redisKeySet6 = (Set<String>) redisTemplate.opsForZSet()
            .rangeByScore(JobSendMsg.NOISE_REDUCTION_MESSAGE, 0, maxScore6);
        log.info(" -- 降噪消息-读出6 minScore6：{},maxScore6：{}, redisKeySet6:{}", 0, maxScore6, redisKeySet6);
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        double maxScore7 = (double) Instant.now().getEpochSecond();
        Set<String> redisKeySet7 = (Set<String>) redisTemplate.opsForZSet()
            .rangeByScore(JobSendMsg.NOISE_REDUCTION_MESSAGE, 0, maxScore7);
        log.info(" -- 降噪消息-读出7 minScore7：{},maxScore7：{}, redisKeySet7:{}", 0, maxScore7, redisKeySet7);
        // 设置过期时间为7天
//        redisTemplate.expire(JobSendMsg.NOISE_REDUCTION_MESSAGE, 3L, TimeUnit.DAYS);
        return 1;
    }


}
