package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_online_state")
public class DeviceOnlineState extends TimedEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 主鍵id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 需要升级的设备sn
     */
    private String deviceSn;

    /**
     * 是否上线
     */
    private Boolean onlineFlag;

    /**
     * 消息发出时间戳 毫秒
     */
    private Long msgTimestamp;
}
