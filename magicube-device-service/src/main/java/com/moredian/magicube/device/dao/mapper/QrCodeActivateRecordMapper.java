package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.qrcode.QrCodeActivateRecord;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface QrCodeActivateRecordMapper {

    /**
     * 根据激活码id查询列表
     *
     * @param orgId
     * @param qrCodeId
     * @return
     */
    List<QrCodeActivateRecord> listByQrCodeId(@Param("orgId") Long orgId, @Param("qrCodeId") Long qrCodeId);

    /**
     * 查询激活记录
     * @param orgId
     * @param deviceId
     * @return
     */
    QrCodeActivateRecord getByDeviceId(@Param("orgId") Long orgId,@Param("deviceId")Long deviceId);

    /**
     * 插入
     *
     * @param qrCodeActivateRecord
     * @return
     */
    int insert(QrCodeActivateRecord qrCodeActivateRecord);

    /**
     * 查询机构下最新的激活记录
     *
     * @param orgId
     * @return
     */
    QrCodeActivateRecord getNewestRecordByOrgId(@Param("orgId") Long orgId);
}