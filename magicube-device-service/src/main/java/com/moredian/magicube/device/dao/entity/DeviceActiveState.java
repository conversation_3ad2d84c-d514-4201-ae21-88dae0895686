package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import com.moredian.magicube.device.enums.DeviceActiveStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备激活状态
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_active_state")
public class DeviceActiveState extends TimedEntity {

    private static final long serialVersionUID = -1169428545859033507L;

    /**
     * 设备激活状态Id
     */
    private Long id;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * @see DeviceActiveStateEnum 设备激活状态
     */
    private Integer state;

    /**
     * 绑定机构页面url（仅用于钉钉SDK激活流程）
     */
    private String bindOrgUrl;

    /**
     * 非钉绑定的机构id
     */
    private Long bindOrgId;
}