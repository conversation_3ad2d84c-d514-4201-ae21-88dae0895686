package com.moredian.magicube.device;

import com.moredian.bee.boot.BeeStarter;
import com.moredian.bee.config.annotation.Application;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */

@EnableScheduling
@EnableAsync
@Application("magicube-device-service")
@EnableTransactionManagement(proxyTargetClass = true)
@MapperScan("com.moredian.magicube.device.dao.mapper")
public class DeviceServiceStarter extends BeeStarter {
    public static void main(String[] args) {
        run(DeviceServiceStarter.class, args);
    }

}
