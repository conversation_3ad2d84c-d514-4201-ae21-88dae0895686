package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 主题和设备关联关系表Mapper
 *
 * <AUTHOR>
 * @since 2023-08-28
 */

@Mapper
public interface DeviceSubjectRelationMapper {

    /**
     * 新增设备和主题关系
     *
     * @param relation 关系信息
     * @return
     */
    int insert(DeviceSubjectRelation relation);

    /**
     * 批量新增设备和主题关系
     *
     * @param relations 关系信息
     * @return
     */
    void batchInsert(@Param("relations") List<DeviceSubjectRelation> relations);

    /**
     * 根据机构Id和主题Id查询关联的设备Id列表
     *
     * @param orgId     机构Id
     * @param subjectId 主题Id
     * @return
     */
    List<Long> listDeviceIdByOrgIdAndSubjectId(@Param("orgId") Long orgId,
        @Param("subjectId") Long subjectId);

    /**
     * 删除关系
     *
     * @param orgId      机构号
     * @param deviceIds  设备Id列表
     * @param subjectIds 主题Id列表
     * @return
     */
    void deleteByCondition(@Param("orgId") Long orgId, @Param("deviceIds") List<Long> deviceIds,
        @Param("subjectIds") List<Long> subjectIds);

    /**
     * 根据机构Id和设备主题Id列表查询关系信息
     *
     * @param orgId      机构Id
     * @param subjectIds 设备主题Id列表
     * @return
     */
    List<DeviceSubjectRelation> listByOrgIdAndSubjectIds(@Param("orgId") Long orgId,
        @Param("subjectIds") List<Long> subjectIds);

    /**
     * 根据机构Id和设备Id列表查询关系信息
     *
     * @param orgId     机构Id
     * @param deviceIds 设备Id列表
     * @param type      主题类型 1-壁纸 2-屏保
     * @return
     */
    List<DeviceSubjectRelation> listByOrgIdAndDeviceIds(@Param("orgId") Long orgId,
        @Param("deviceIds") List<Long> deviceIds, @Param("type") Integer type);
}
