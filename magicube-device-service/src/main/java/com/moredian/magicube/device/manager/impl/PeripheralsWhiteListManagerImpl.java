package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.PeripheralsWhiteList;
import com.moredian.magicube.device.dao.mapper.PeripheralsWhiteListMapper;
import com.moredian.magicube.device.manager.PeripheralsWhiteListManager;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
public class PeripheralsWhiteListManagerImpl implements PeripheralsWhiteListManager {

    @Autowired
    private PeripheralsWhiteListMapper peripheralsWhiteListMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public void batchInsert(List<PeripheralsWhiteList> peripheralsWhiteListList) {
        if (CollectionUtils.isNotEmpty(peripheralsWhiteListList)) {
            BatchIdDto batchIdDto = idgeneratorService
                .getNextIdBatchBytypeName(BeanConstants.PERIPHERALS_WHITE_LIST,
                    peripheralsWhiteListList.size()).pickDataThrowException();
            for (PeripheralsWhiteList peripheralsWhiteList : peripheralsWhiteListList) {
                peripheralsWhiteList.setPeripheralsWhiteListId(batchIdDto.nextId());
                BizAssert.notNull(peripheralsWhiteList.getPeripheralsSn(),
                    "peripheralsSn must not be null");
            }
            peripheralsWhiteListMapper.batchInsert(peripheralsWhiteListList);
        }
    }

    @Override
    public PeripheralsWhiteList getBySnAndType(String peripheralsSn, Integer peripheralsType) {
        BizAssert.notNull(peripheralsSn, "peripheralsSn must not be null");
        BizAssert.notNull(peripheralsType, "peripheralsType must not be null");
        return peripheralsWhiteListMapper.getBySnAndType(peripheralsSn, peripheralsType);
    }

    @Override
    public List<PeripheralsWhiteList> getBySnList(List<String> peripheralsSnList) {
        if (CollectionUtils.isEmpty(peripheralsSnList)) {
            return null;
        }
        return peripheralsWhiteListMapper.listByPeripheralsSns(peripheralsSnList);
    }

    @Override
    public int updateStatusById(Long peripheralsWhiteListId, Integer status) {
        BizAssert.notNull(peripheralsWhiteListId, "peripheralsWhiteListId must not be null");
        BizAssert.notNull(status, "status must not be null");
        return peripheralsWhiteListMapper.update(peripheralsWhiteListId, status);
    }
}
