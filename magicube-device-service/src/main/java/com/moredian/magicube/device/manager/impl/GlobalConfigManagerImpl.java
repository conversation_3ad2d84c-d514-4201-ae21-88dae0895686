package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dao.entity.GlobalConfig;
import com.moredian.magicube.device.dao.mapper.GlobalConfigMapper;
import com.moredian.magicube.device.manager.GlobalConfigManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: _AF
 * @Date: 12/28/21 15:08
 * @Description:
 */
@Service
public class GlobalConfigManagerImpl implements GlobalConfigManager {

    @Autowired
    private GlobalConfigMapper globalConfigMapper;

    @Override
    public GlobalConfig getByConfigName(String configName) {
        BizAssert.notBlank(configName, "配置名不能为空");
        return globalConfigMapper.getByConfigName(configName);
    }
}
