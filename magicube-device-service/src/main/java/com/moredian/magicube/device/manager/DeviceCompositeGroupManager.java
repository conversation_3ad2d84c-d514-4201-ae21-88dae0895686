package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dto.composite.SimpleDeviceCompositeGroupDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 17:59
 */
public interface DeviceCompositeGroupManager {

    /**
     * 获取通行权限组和设备/设备组关系
     *
     * @param orgId    机构ID
     * @param groupIds 组ID
     * @return SimpleDeviceCompositeGroupResponse 实体
     */
    List<SimpleDeviceCompositeGroupDTO> findDeviceCompositeGroupByGroupIds(Long orgId, List<Long> groupIds);


    /**
     * 获取通行权限组和设备/设备组关系
     *
     * @param orgId   机构ID
     * @param groupId 组ID
     * @return SimpleDeviceCompositeGroupResponse 实体
     */
    SimpleDeviceCompositeGroupDTO findDeviceCompositeGroupByGroupId(Long orgId, Long groupId);

    /**
     *  根据组id列表查询组设备信息(deviceList 非选择设备+组里的设备组合)
     *
     * @param orgId    机构ID
     * @param groupIds 组ID
     * @return SimpleDeviceCompositeGroupResponse 实体
     */
    List<SimpleDeviceCompositeGroupDTO> findDeviceCompositeGroupByGroupIdsNonAllDevice(Long orgId, List<Long> groupIds);

    /**
     * 根据设备删除
     *
     * @param orgId    机构ID
     * @param deviceId 设备ID
     * @return true成功
     */
    boolean deleteByDeviceId(Long orgId, Long deviceId);
}
