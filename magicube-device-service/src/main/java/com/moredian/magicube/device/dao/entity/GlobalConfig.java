package com.moredian.magicube.device.dao.entity;

import java.util.Date;
import lombok.Data;

/**
 * @Auther: _AF
 * @Date: 12/28/21 14:55
 * @Description: 全局配置
 */
@Data
public class GlobalConfig {
    /**
     * id
     */
    private Long id;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 配置值
     */
    private String configValue;
    /**
     * 配置状态，0-停用，1-启用|默认
     */
    private Integer configStatus;

    private Date gmtCreate;

    private Date gmtModify;

}
