package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.version.ChangeDeviceAppVersionDTO;
import com.moredian.magicube.device.dto.version.ChangeLatchDeviceVersionDto;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceEnforceUpdateDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionInfoDTO;
import com.moredian.magicube.device.dto.version.QueryDeviceVersionDTO;
import com.moredian.magicube.device.dto.version.StockDeviceVersionDTO;
import com.moredian.magicube.device.manager.DeviceVersionManager;
import com.moredian.magicube.device.manager.DeviceVersionPersistenceClientManager;
import com.moredian.magicube.device.service.DeviceVersionService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */

@SI
public class DeviceVersionImpl implements DeviceVersionService {

    @Autowired
    private DeviceVersionManager deviceVersionManager;

    @Autowired
    private DeviceVersionPersistenceClientManager deviceVersionPersistenceClientManager;

    @Override
    public ServiceResponse<Void> changeDeviceAppVersion(ChangeDeviceAppVersionDTO dto) {
        deviceVersionManager.changeDeviceAppVersion(dto);
        return ServiceResponse.createSuccessResponse();
    }

    @Override
    public ServiceResponse<Void> changeDeviceRomVersion(ChangeDeviceAppVersionDTO dto) {
        deviceVersionManager.changeDeviceRomVersion(dto);
        return ServiceResponse.createSuccessResponse();
    }

    @Override
    public ServiceResponse<Boolean> changeLatchDeviceVersion(ChangeLatchDeviceVersionDto dto) {
        return new ServiceResponse<>(deviceVersionManager.changeLatchDeviceVersion(dto));
    }

    @Override
    public ServiceResponse<Void> changeDeviceEnforceUpdateStatus(DeviceEnforceUpdateDTO dto) {
        deviceVersionManager.changeDeviceEnforceUpdateStatus(dto);
        return ServiceResponse.createSuccessResponse();
    }

    @Override
    public ServiceResponse<DeviceEnforceUpdateDTO> getDeviceEnforceUpdateStatus(Long deviceId) {
        ServiceResponse<DeviceEnforceUpdateDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceVersionManager.getDeviceEnforceUpdateStatus(deviceId));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceVersionDTO>> listApkByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        ServiceResponse<List<DeviceVersionDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceVersionManager.listApkByOrgIdAndDeviceIds(orgId, deviceIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceVersionDTO>> listRomByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        ServiceResponse<List<DeviceVersionDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceVersionManager.listRomByOrgIdAndDeviceIds(orgId, deviceIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceCurrVersionDTO>> listByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        ServiceResponse<List<DeviceCurrVersionDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceVersionManager.listByOrgIdAndDeviceIds(orgId, deviceIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceVersionDTO>> getBatchDeviceAppVersionFromDatabase(Long orgId, Long[] deviceIds) {
        return deviceVersionPersistenceClientManager.getBatchDeviceAppVersion(orgId, deviceIds);
    }

    @Override
    public ServiceResponse<List<DeviceVersionDTO>> getBatchDeviceRomVersionFromDatabase(Long orgId, Long[] deviceIds) {
        return deviceVersionPersistenceClientManager.getBatchDeviceRomVersion(orgId, deviceIds);
    }

    @Override
    public ServiceResponse<List<DeviceCurrVersionDTO>> getBatchDeviceVersionFromDatabase(Long orgId, Long[] deviceIds) {
        return deviceVersionPersistenceClientManager.getBatchDeviceVersion(orgId, deviceIds);
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceRomVersionFromDatabase(List<String> deviceSnList) {
        return deviceVersionManager.getBatchDeviceRomVersionFromDatabase(deviceSnList);
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceApkVersionFromDatabase(List<String> deviceSnList) {
        return deviceVersionManager.getBatchDeviceApkVersionFromDatabase(deviceSnList);
    }

    @Override
    public ServiceResponse<Boolean> reportDeviceRomVersionBeforeActivation(
        ChangeDeviceAppVersionDTO changeDeviceVersionDto) {
        return deviceVersionManager.reportDeviceRomVersionBeforeActivation(changeDeviceVersionDto);
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceNewestRomVersionFromDatabase(List<String> deviceSnList) {
        return deviceVersionManager.getBatchDeviceNewestRomVersionFromDatabase(deviceSnList);
    }

    @Override
    public ServiceResponse<List<StockDeviceVersionDTO>> getBatchStockDeviceVersionFromDatabase(List<String> deviceSnList) {
        return deviceVersionManager.getBatchStockDeviceVersionFromDatabase(deviceSnList);
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> findDeviceVersionByOrgListAndVersion(
        QueryDeviceVersionDTO dto) {
        return deviceVersionManager.findDeviceVersionByOrgListAndVersion(dto);
    }
}