package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.composite.DeviceCompositeGroupRelationAddReq;
import com.moredian.magicube.device.dto.composite.SimpleDeviceCompositeGroupDTO;
import com.moredian.magicube.device.manager.DeviceCompositeGroupManager;
import com.moredian.magicube.device.manager.DeviceGroupManager;
import com.moredian.magicube.device.service.DeviceCompositeGroupRelationService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/11/25 17:34
 */
@SI
public class DeviceCompositeGroupRelationServiceImpl implements
    DeviceCompositeGroupRelationService {

    @Autowired
    private DeviceCompositeGroupManager deviceCompositeGroupManager;

    @Autowired
    private DeviceGroupManager deviceGroupManager;

    @Override
    public ServiceResponse<List<SimpleDeviceCompositeGroupDTO>> findDeviceCompositeGroupByGroupIds(Long orgId, List<Long> groupIds) {
        return new ServiceResponse<>(deviceCompositeGroupManager.findDeviceCompositeGroupByGroupIds(orgId,groupIds));
    }

    @Override
    public ServiceResponse<SimpleDeviceCompositeGroupDTO> getDeviceCompositeGroupByGroupId(Long orgId, Long groupId) {
        if(orgId==null || groupId==null){
            return ServiceResponse.createSuccessResponse();
        }
        return new ServiceResponse<>(deviceCompositeGroupManager.findDeviceCompositeGroupByGroupId(orgId,  groupId));
    }

    @Override
    public ServiceResponse<Boolean> resetDeviceCompositeGroupRelation(
        DeviceCompositeGroupRelationAddReq req) {
        if (req == null || req.getOrgId() == null || req.getGroupId() == null) {
            new ServiceResponse(true, null, false);
        }
        boolean resetRelationOfDeviceGroup = deviceGroupManager.resetRelationOfDeviceGroup(req);
        return 	new ServiceResponse(true, null, resetRelationOfDeviceGroup);
    }

    @Override
    public ServiceResponse<List<SimpleDeviceCompositeGroupDTO>> findDeviceCompositeGroupByGroupIdsNonAllDevice(Long orgId, List<Long> groupIds) {
        return new ServiceResponse<>(deviceCompositeGroupManager.findDeviceCompositeGroupByGroupIdsNonAllDevice(orgId,groupIds));
    }
}
