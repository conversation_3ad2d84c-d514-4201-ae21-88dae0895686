package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.qrcode.FunctionQrCode;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname： EndAuditMapper
 * @Date: 2022/4/14 5:27 下午
 * @Author: _AF
 * @Description: 手动结束审批信息
 */
@Mapper
public interface FunctionQrCodeDao {


    /**
     * 插入
     *
     * @param functionQrCode
     * @return
     */
    int insert(FunctionQrCode functionQrCode);

    /**
     * 获取
     *
     * @param memberId
     * @param id
     * @return
     */
    FunctionQrCode getById(@Param("memberId") Long memberId, @Param("id") Long id);


    /**
     * 根据域账号查询获取
     *
     * @param memberId
     * @return
     */
    List<FunctionQrCode> listByMemberId(@Param("memberId") Long memberId,
                                         @Param("functionTypeList") List<Integer> functionTypeList,
                                         @Param("roleType") Integer roleType,
                                         @Param("source") Integer source);

    /**
     * 根据id+roleType查询
     *
     * @param id
     * @param roleType
     * @return
     */
    FunctionQrCode getByIdAndRoleType(@Param("id") Long id, @Param("roleType") Integer roleType);

    /**
     * 更新激活时间
     *
     * @param id
     * @return
     */
    int updateNewActivateTime(@Param("id") Long id, @Param("newActivateTime") Date newActivateTime);

    /**
     * 根据id查询激活码
     *
     * @param qrCodeId
     * @return
     */
    FunctionQrCode getActivateQrCodeById(@Param("qrCodeId") Long qrCodeId);
}
