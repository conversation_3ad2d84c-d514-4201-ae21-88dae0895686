package com.moredian.magicube.device.model;


import com.moredian.magicube.device.enums.NoticeEventEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@Data
@NoArgsConstructor
public class StatisticsModel implements Serializable {

    private static final long serialVersionUID = -6805062728718169130L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 触发通知事件
     *
     * @see NoticeEventEnum
     */
    private Integer noticeEvent;

    /**
     * 通知结果，0-失败，1-成功
     */
    private Integer noticeResult;

    /**
     * 开始时间
     */
    private Date startDay;

    /**
     * 结束时间
     */
    private Date endDay;
}
