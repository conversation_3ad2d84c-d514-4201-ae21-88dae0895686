package com.moredian.magicube.device.third.dahua;

import static com.moredian.magicube.device.third.dahua.HaveReConnectCallBack.peopleNumberStatisticCB;

import com.google.common.collect.Lists;
import com.moredian.magicube.device.dao.entity.dahua.CameraDeviceInfo;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.dto.dahua.QueryCameraDTO;
import com.moredian.magicube.device.manager.dahua.CameraDeviceManager;
import com.moredian.magicube.device.service.PeopleNumberStatisticService;
import com.moredian.magicube.device.service.dahua.CameraDeviceService;
import com.netsdk.lib.NetSDKLib;
import com.netsdk.lib.NetSDKLib.LLong;
import com.sun.jna.Pointer;
import java.io.UnsupportedEncodingException;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;


/**
 * 设备主动注册监听服务回调
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ListenServerCallBack implements NetSDKLib.fServiceCallBack {

    private static PeopleNumberStatisticService peopleNumberStatisticService;

    private static CameraDeviceService cameraDeviceService;

    private static CameraDeviceManager cameraDeviceManager;

    // 客流量统计订阅回调
    private PeopleNumberStatisticCallBack peopleNumberStatisticCB = PeopleNumberStatisticCallBack
        .getInstance();

    @Autowired
    public void setCameraDeviceService(CameraDeviceService cameraDeviceService, CameraDeviceManager cameraDeviceManager) {
        ListenServerCallBack.cameraDeviceService = cameraDeviceService;
        ListenServerCallBack.cameraDeviceManager = cameraDeviceManager;
    }

    private static ListenServerCallBack instance = new ListenServerCallBack();

    public static ListenServerCallBack getInstance() {
        return instance;
    }

    @Override
    public int invoke(LLong lLong, String ip, int port, int i1, Pointer pParam, int dwParamLen,
        Pointer pointer1) {
        log.info("收到大华摄像头监听回调，LLong=>{}, s=>{}, i=>{}, i1=>{}, pParam=>{}, dwParamLen=>{}, pointer1=>{}",
            lLong, ip, port, i1, pParam, dwParamLen, pointer1);

        // 将 pParam 转化为序列号
        byte[] buffer = new byte[dwParamLen];
        pParam.read(0, buffer, 0, dwParamLen);
        String deviceId = "";
        try {
            deviceId = new String(buffer, "GBK").trim();
        } catch (UnsupportedEncodingException e) {
            log.error("大华摄像头解析deviceId失败，e=>{}", e.getMessage());
        }

        log.info("大华摄像头回调解析出来数据，deviceId=>{}", deviceId);

        if (ObjectUtils.isEmpty(deviceId)){
            log.warn("监听到大华摄像头设备，deviceId为空，deviceId=>{}", deviceId);
            return 0;
        }

        QueryCameraDTO dto = new QueryCameraDTO();
        dto.setDeviceSns(Lists.newArrayList(deviceId));
        List<CameraDeviceInfo> cameras = cameraDeviceManager.listByCondition(dto);

        if (ObjectUtils.isEmpty(cameras)){
            log.warn("监听到大华摄像头设备，数据库中不存在，deviceSn=>{}", deviceId);
            return 0;
        }
        CameraDeviceInfo deviceInfo = cameras.get(0);

        // 更新大华摄像头表对应的ip地址和端口
        CameraDeviceInfoDTO updateDto = new CameraDeviceInfoDTO();
        updateDto.setDeviceIp(ip);
        updateDto.setPort(port);
        cameraDeviceManager.updateByDeviceSn(deviceId, updateDto);

        //如果是平台向设备注册,开启监听服务 登录摄像头
        boolean result = SdkUtils.login(ip,
            port, deviceInfo.getUserName(),
            deviceInfo.getPassword(),
            deviceId);
        if (!result) {
            log.error("登录摄像头失败deviceInfo:{}", deviceInfo);
        } else {
            // 登录成功后开启人数统计订阅事件
            NetSDKLib.LLong handle = SdkUtils
                .attachVideoStatSummary(0, peopleNumberStatisticCB);
            if (handle.intValue() == 0) {
                log.error("开启人数统计订阅事件失败，deviceInfo={}", deviceInfo);
            } else {
                peopleNumberStatisticCB.getDeviceMap().put(handle.intValue(),
                    ip + ":" + port);
                log.info("开启人数统计订阅事件成功，deviceInfo={}", deviceInfo);
            }
        }
        return 0;
    }
}

