package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.AppTypeRedirect;
import com.moredian.magicube.device.dto.device.PageAppTypeRedirectDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * appType跳转链接
 *
 * <AUTHOR>
 */
@Mapper
public interface AppTypeRedirectMapper {

    /**
     * 新增appType跳转链接
     *
     * @param appTypeRedirect
     * @return
     */
    int add(AppTypeRedirect appTypeRedirect);

    /**
     * 查询详情
     *
     * @param appTypeRedirect
     * @return
     */
    AppTypeRedirect getOne(AppTypeRedirect appTypeRedirect);

    /**
     * 修改
     *
     * @param appTypeRedirect
     * @return
     */
    int update(AppTypeRedirect appTypeRedirect);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteById(Long id);

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    List<AppTypeRedirect> page(PageAppTypeRedirectDTO dto);
}
