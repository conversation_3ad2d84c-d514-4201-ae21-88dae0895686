package com.moredian.magicube.device.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description 同源环境配置开关
 * @Date 2023/3/8
 */
@Getter
@Configuration
public class OneServiceConfig {

    /**
     * 是否为同源环境
     */
    @Value("${fishnet.device.service.enableOneService:false}")
    private Boolean enableOneService;
}
