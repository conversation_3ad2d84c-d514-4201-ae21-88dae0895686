package com.moredian.magicube.device.pipeline.pipe.reverse;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeDto;
import com.moredian.magicube.device.manager.FunctionQrCodeManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceTreeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 机构id从二维码中获取，设备不再透传机构id
 * @Date 2023/10/7
 */
@Slf4j
@Component
public class ReverseOrgIdPipe extends
    AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private SpaceTreeService treeService;

    @Autowired
    private FunctionQrCodeManager qrCodeManager;

    @Override
    protected boolean isFilter(
        ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext activateDeviceContext) {
        return false;
    }

    @Override
    protected void bizHandler(
        ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext context) {
        if (activateDeviceDTO.getQrCodeId() != null) {
            NetworkQrCodeDto networkQrCodeDto = qrCodeManager
                .getNetWorkInfoByQrCodeId(activateDeviceDTO.getQrCodeId());
            if (networkQrCodeDto != null) {
                //多租户场景下，查询空间所在的组织id
                if (networkQrCodeDto.getTreeId() != null && networkQrCodeDto.getTreeId() != 0L) {
                    TreeDTO treeDTO = treeService.getByIdSupportCollaboration(networkQrCodeDto
                        .getTreeId(), networkQrCodeDto.getOrgId()).pickDataThrowException();
                    if (treeDTO != null) {
                        context.setSpaceOrgId(treeDTO.getOrgId());
                    }
                }
                activateDeviceDTO.setPosition(networkQrCodeDto.getDeviceAddressName() == null ? ""
                    : networkQrCodeDto.getDeviceAddressName());
                context.setTreeId(networkQrCodeDto.getTreeId());
                context.setOrgId(networkQrCodeDto.getOrgId());
                context.setDirection(networkQrCodeDto.getDirection());
            }else {
                //没有传激活码id说明是私有化激活，拿不到码在服务端的信息，所有位置字段用设备上传的
                activateDeviceDTO.setPosition(activateDeviceDTO.getDeviceName());
            }
        } else {
            //没有传激活码id说明是私有化激活，拿不到码在服务端的信息，所有位置字段用设备上传的
            activateDeviceDTO.setPosition(activateDeviceDTO.getDeviceName());
        }
    }
}
