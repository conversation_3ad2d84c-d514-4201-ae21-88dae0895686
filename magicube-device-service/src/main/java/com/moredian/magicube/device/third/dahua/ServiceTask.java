package com.moredian.magicube.device.third.dahua;

import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.service.dahua.CameraDeviceService;
import com.netsdk.lib.NetSDKLib;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ServiceTask implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        this.registerCallBack();
    }

    // 设备断线通知回调
    private static DisConnectCallBack disConnectCB = new DisConnectCallBack();

    // 网络连接恢复
    private static HaveReConnectCallBack haveReConnectCB = new HaveReConnectCallBack();

    // 客流量统计订阅回调
    private PeopleNumberStatisticCallBack peopleNumberStatisticCB = PeopleNumberStatisticCallBack
        .getInstance();

    private ListenServerCallBack listenServerCallBack = ListenServerCallBack.getInstance();

    private static boolean bInit = false;

    @Value("${daHua.start.env:false}")
    private Boolean env;

    @Autowired
    private CameraDeviceService cameraDeviceService;

    public void registerCallBack() {
        if (!env) {
            log.info("本地环境不需要初始化大华SDK");
            return;
        }
        //从设备表查询设备信息
        List<CameraDeviceInfoDTO> deviceInfoList = cameraDeviceService.list()
            .pickDataThrowException();
        long currentTimeStamp = System.currentTimeMillis();
        //先初始化
        if (!bInit) {
            bInit = SdkUtils.init(disConnectCB, haveReConnectCB);
        }
        if (!bInit) {
            log.info("初始化SDK失败");
            bInit = false;
        } else {
            if (CollectionUtils.isNotEmpty(deviceInfoList)) {
                for (CameraDeviceInfoDTO deviceInfo : deviceInfoList) {
                    try {
                        //如果是设备向平台注册,开启监听服务
                        if (deviceInfo.getRegisterType() == 2) {
                            SdkUtils.startServer(deviceInfo.getLocalDeviceIp(), deviceInfo.getLocalPort(),
                                listenServerCallBack);
                            continue;
                        }
                        //如果是平台向设备注册,开启监听服务 登录摄像头
                        boolean result = SdkUtils.login(deviceInfo.getDeviceIp(),
                            deviceInfo.getPort(), deviceInfo.getUserName(),
                            deviceInfo.getPassword(),
                            deviceInfo.getDeviceSn());
                        log.info("大华摄像头登录状态信息，result=>{}", result);
                        if (!result) {
                            log.error("登录摄像头失败deviceInfo:{}", deviceInfo);
                        } else {
                            // 登录成功后开启人数统计订阅事件
                            NetSDKLib.LLong handle = SdkUtils
                                .attachVideoStatSummary(0, peopleNumberStatisticCB);
                            log.info("订阅人数信息handle:{}", handle);
                            if (handle.intValue() == 0) {
                                log.error("开启人数统计订阅事件失败，deviceInfo={}", deviceInfo);
                            } else {
                                peopleNumberStatisticCB.getDeviceMap().put(handle.intValue(),
                                    deviceInfo.getDeviceIp() + ":" + deviceInfo.getPort());
                                log.info("开启人数统计订阅事件成功，deviceInfo={}", deviceInfo);
                            }
                        }
                    } catch (Exception e) {
                        log.error("登录摄像头失败deviceInfo:{},e:{}", deviceInfo, e);
                    }
                }
            }
        }
        log.info("结束执行摄像头登录的定时器任务，耗时{}ms", System.currentTimeMillis() - currentTimeStamp);
    }
}

