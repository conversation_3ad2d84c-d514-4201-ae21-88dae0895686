package com.moredian.magicube.device.manager.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.magicube.common.model.msg.DeviceGroupChangeMsg;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.DeviceCompositeBizType;
import com.moredian.magicube.device.constant.DeviceCompositeChangeMsgSate;
import com.moredian.magicube.device.dao.entity.DeviceComposite;
import com.moredian.magicube.device.dao.entity.DeviceCompositeGroup;
import com.moredian.magicube.device.dao.entity.DeviceCompositeItem;
import com.moredian.magicube.device.dao.entity.DeviceGroup;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeGroupMapper;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeItemMapper;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeMapper;
import com.moredian.magicube.device.dao.mapper.DeviceGroupMapper;
import com.moredian.magicube.device.dto.composite.DeviceCompositeDTO;
import com.moredian.magicube.device.enums.DeviceCompositeGroupRangeType;
import com.moredian.magicube.device.manager.DeviceCompositeManager;
import com.moredian.magicube.device.manager.DeviceGroupCompositeComponent;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/5 12:37
 */
@Service
@Slf4j
public class DeviceGroupCompositeComponentImpl implements DeviceGroupCompositeComponent {

    @Autowired
    private DeviceCompositeManager deviceCompositeManager;
    @Autowired
    private DeviceCompositeMapper deviceCompositeMapper;
    @Autowired
    private DeviceCompositeItemMapper deviceCompositeItemMapper;
    @Autowired
    private DeviceCompositeGroupMapper deviceCompositeGroupMapper;
    @Autowired
    private DeviceGroupMapper deviceGroupMapper;
    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public boolean deviceCompositeAdd(Long orgId, Long compositeId, List<Long> deviceIdList) {
        if (CollectionUtils.isNotEmpty(deviceIdList)) {
            DeviceCompositeDTO deviceCompositeDto = getDeviceCompositeDto(orgId, compositeId, DeviceCompositeChangeMsgSate.COMPOSITE_ADD);
            if (deviceCompositeDto == null) {
                return Boolean.TRUE;
            }
            List<Long> needSendDeviceIdList = Lists.newArrayList();
            // 当前设备组路径
            List<String> codeList = getParenCodePath(deviceCompositeDto, true);
            // 父path对应的compositeId
            List<Long> parentCompositeIdList = getParentCompositeIdList(orgId, codeList);
            List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByDeviceCompositeIdList(orgId, parentCompositeIdList);
            if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
                // 添加组设备关系
                List<Long> parentGroupIdList = deviceCompositeGroupList.stream()
                        .map(DeviceCompositeGroup::getGroupId)
                        .distinct()
                        .collect(Collectors.toList());
                Map<Long, List<Long>> groupIdDeviceIdListMap = groupIdDeviceIdListMap(orgId, parentGroupIdList);
                batchCreateDeviceGroupRelation(orgId, parentGroupIdList, deviceIdList, groupIdDeviceIdListMap, needSendDeviceIdList);
                DeviceGroupChangeMsg deviceGroupChangeMsg = new DeviceGroupChangeMsg();
                deviceGroupChangeMsg.setOrgId(orgId);
                deviceGroupChangeMsg.setChangeState(DeviceCompositeChangeMsgSate.COMPOSITE_ADD);
                deviceGroupChangeMsg.setDeviceIdList(deviceIdList.stream().distinct().collect(Collectors.toList()));
                //int publish = EventBus.publish(deviceGroupChangeMsg);
                log.info("[deviceCompositeAdd] send DeviceGroupChangeMsg,content={},publish-value={}", JsonUtils.toJson(deviceGroupChangeMsg), 3);
            }
        } else {
            log.info("[deviceCompositeAdd]no device id list ,orgId={},compositeId={}", orgId, compositeId);
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean deleteGroupDeviceCompositeRelationByCompositeId(Long orgId, Long compositeId) {
        // 设备组信息
        DeviceCompositeDTO deviceCompositeDto = getDeviceCompositeDto(orgId, compositeId, DeviceCompositeChangeMsgSate.COMPOSITE_DELETE);
        if (deviceCompositeDto == null) {
            return Boolean.TRUE;
        }
        List<Long> subCompositeIdList = Lists.newArrayList();
        List<DeviceComposite> subCompositeList = deviceCompositeManager.findAllSubDeviceCompositeWithDelete(orgId, compositeId);
        if (CollectionUtils.isNotEmpty(subCompositeList)) {
            subCompositeIdList = subCompositeList.stream().map(DeviceComposite::getDeviceCompositeId).distinct().collect(Collectors.toList());
        }
        List<Long> curAndSubCompositeIdList = getCurAndSubCompositeIdList(compositeId, subCompositeIdList);
        List<Long> allCompositeIdList = getAllCompositeIdList(orgId, deviceCompositeDto, curAndSubCompositeIdList);
        // 设备组下设备列表
        List<DeviceCompositeItem> deviceCompositeItemList = deviceCompositeItemMapper.getItemListByCompositeIdListWithDelete(orgId, curAndSubCompositeIdList);
        // 通行组和设备组的关系
        List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByDeviceCompositeIdList(orgId, allCompositeIdList);
        if (CollectionUtils.isEmpty(deviceCompositeItemList)) {
            log.info("[compositeDelete] no group relation,orgId={},compositeId={}", orgId, compositeId);
            return Boolean.TRUE;
        }
        // 需要通知通信组设备关系
        List<Long> needSendDeviceIdList = Lists.newArrayList();
        // 涉及的通行组
        List<Long> groupIdList = deviceCompositeGroupList.stream()
                .map(DeviceCompositeGroup::getGroupId)
                .distinct()
                .collect(Collectors.toList());

        // 老的通信组和设备组
        Map<Long, List<DeviceGroup>> oldGroupIdDeviceIdListMap = getOldGroupIdDeviceIdListMap(orgId, groupIdList);
        // 新的通行组和设备的关系
        List<DeviceCompositeGroup> oldDeviceCompositeGroupList = deviceCompositeGroupMapper.selectByGroupIdList(orgId, groupIdList, null);
        // 新通行组和设备关系
        Map<Long, List<Long>> groupIdDeviceIdListMap = getNewGroupRangeDeviceList(oldDeviceCompositeGroupList);
        // 通行组其他设备设备组(排除已删除的组)
        Map<Long, List<Long>> groupIdCompositeDeviceIdListMap = getGroupIdDeviceIdListMapExcludeDeleteComposite(orgId, curAndSubCompositeIdList, oldDeviceCompositeGroupList);
        // 处理
        List<Long> deleteIdList = Lists.newArrayList();
        for (Long groupId : groupIdList) {
            List<DeviceGroup> oldDeviceIdList = oldGroupIdDeviceIdListMap.get(groupId);
            List<Long> newDeviceIdList = getNewDeviceIdList(groupIdDeviceIdListMap, groupIdCompositeDeviceIdListMap, groupId);
            List<Long> delDeviceIdList = CollUtil.subtractToList(oldDeviceIdList.stream().map(DeviceGroup::getDeviceId).collect(Collectors.toList()), newDeviceIdList);
            needSendDeviceIdList.addAll(delDeviceIdList);
            if (CollUtil.isNotEmpty(delDeviceIdList)) {
                deleteIdList.addAll(oldDeviceIdList.stream().filter(f -> delDeviceIdList.contains(f.getDeviceId())).map(DeviceGroup::getDeviceGroupId).collect(Collectors.toList()));
            }
        }

        // 1. 删除通信组和设备组的关系
        List<Long> deviceCompositeGroupIdList = deviceCompositeGroupList.stream()
                .filter(e -> curAndSubCompositeIdList.contains(e.getRangeId()))
                .map(DeviceCompositeGroup::getDeviceCompositeGroupId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deviceCompositeGroupIdList)) {
            deviceCompositeGroupMapper.deleteByIds(orgId, deviceCompositeGroupIdList);
        }
        // 2. 删除通信组和设备的关系
        if (CollectionUtils.isNotEmpty(deleteIdList)) {
            deviceGroupMapper.deleteByDeviceGroupIdList(orgId, deleteIdList);
        }
        // 3. 发送消息
        List<Long> newNeedSendDeviceIdList = needSendDeviceIdList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIdList)) {
            DeviceGroupChangeMsg deviceGroupChangeMsg = new DeviceGroupChangeMsg();
            deviceGroupChangeMsg.setOrgId(orgId);
            deviceGroupChangeMsg.setChangeState(DeviceCompositeChangeMsgSate.COMPOSITE_DELETE);
            deviceGroupChangeMsg.setDeviceIdList(newNeedSendDeviceIdList);
            //int publish = EventBus.publish(deviceGroupChangeMsg);
            log.info("[compositeDelete] send subscribeDeviceCompositeChangeMsg,content={},publish-value={}", JsonUtils.toJson(deviceGroupChangeMsg), 3);
        }
        return true;
    }


    @Override
    public List<Long> updateComposite(Long orgId, Long compositeId, List<Long> deleteDeviceIdList, List<Long> addDeviceIdList) {
        // 需要发消息的设备
        List<Long> needSendDeviceIdList = Lists.newArrayList();
        if (compositeId == null) {
            log.info("[updateComposite] no compositeId,orgId={}", orgId);
            return needSendDeviceIdList;
        }
        DeviceCompositeDTO deviceCompositeDto = getDeviceCompositeDto(orgId, compositeId, DeviceCompositeChangeMsgSate.COMPOSITE_UPDATE);
        if (deviceCompositeDto == null) {
            return needSendDeviceIdList;
        }
        // 关联通行组
        List<Long> groupIdList = Lists.newArrayList();
        // 当前设备组路径
        List<String> codeList = getParenCodePath(deviceCompositeDto, false);
        // 父path对应的compositeId
        List<Long> compositeIdList = getParentCompositeIdList(orgId, codeList);
        List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByDeviceCompositeIdList(orgId, compositeIdList);
        if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
            groupIdList = deviceCompositeGroupList.stream()
                    .map(DeviceCompositeGroup::getGroupId)
                    .distinct()
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(groupIdList)) {
            return needSendDeviceIdList;
        }
        List<DeviceGroup> deviceGroupList = deviceGroupMapper.listByOrgIdAndGroupIds(orgId, groupIdList);
        Map<Long, List<DeviceGroup>> groupIdDeviceListMap = deviceGroupList.stream()
                .collect(Collectors.groupingBy(DeviceGroup::getGroupId));
        Map<Long, List<Long>> deviceIdDeviceGroupIdListMap = deviceGroupList.stream()
            .collect(Collectors.groupingBy(DeviceGroup::getDeviceId, Collectors.mapping(DeviceGroup::getDeviceGroupId, Collectors.toList())));
        Map<Long, List<Long>> groupIdDeviceIdListMap = deviceGroupList.stream()
                .collect(Collectors.groupingBy(DeviceGroup::getGroupId, Collectors.mapping(DeviceGroup::getDeviceId, Collectors.toList())));
        // 新增设备
        if (CollectionUtils.isNotEmpty(addDeviceIdList) && CollectionUtils.isNotEmpty(groupIdList)) {
            batchCreateDeviceGroupRelation(orgId, groupIdList, addDeviceIdList, groupIdDeviceIdListMap, needSendDeviceIdList);
        }
        // 删除设备
        if (CollectionUtils.isNotEmpty(deleteDeviceIdList) && CollectionUtils.isNotEmpty(groupIdList)) {
            // 新的通行组和设备的关系
            List<DeviceCompositeGroup> oldDeviceCompositeGroupList = deviceCompositeGroupMapper.selectByGroupIdList(orgId, groupIdList, null);
            // 新通行组和设备关系
            Map<Long, List<Long>> newGroupIdDeviceIdListMap = getNewGroupRangeDeviceList(oldDeviceCompositeGroupList);
            // 通行组其他设备设备组(排除已删除的组)
            Map<Long, List<Long>> groupIdCompositeDeviceIdListMap = getGroupIdDeviceIdListMapExcludeDeleteComposite(orgId, null, oldDeviceCompositeGroupList);
            // 处理
            List<Long> deleteIdList = Lists.newArrayList();
            for (Long groupId : groupIdList) {
                List<DeviceGroup> oldDeviceIdList = groupIdDeviceListMap.get(groupId);
                List<Long> newDeviceIdList = getNewDeviceIdList(newGroupIdDeviceIdListMap, groupIdCompositeDeviceIdListMap, groupId);
                if (CollectionUtils.isEmpty(newDeviceIdList)){
                    for (Long deviceId : deleteDeviceIdList) {
                        List<Long> deviceGroupIds = deviceIdDeviceGroupIdListMap.get(deviceId);
                        if (CollectionUtils.isNotEmpty(deviceGroupIds)){
                            deleteIdList.addAll(deviceGroupIds);
                        }
                    }
                    needSendDeviceIdList.addAll(deleteDeviceIdList);
                } else {
                    List<Long> delDeviceIdList = CollUtil.subtractToList(
                        oldDeviceIdList.stream().map(DeviceGroup::getDeviceId)
                            .collect(Collectors.toList()), newDeviceIdList);
                    needSendDeviceIdList.addAll(delDeviceIdList);
                    if (CollUtil.isNotEmpty(delDeviceIdList)) {
                        deleteIdList.addAll(oldDeviceIdList.stream()
                            .filter(f -> delDeviceIdList.contains(f.getDeviceId()))
                            .map(DeviceGroup::getDeviceGroupId).collect(Collectors.toList()));
                    }
                }
            }
            // 1. 删除通信组和设备的关系
            if (CollectionUtils.isNotEmpty(deleteIdList)) {
                deviceGroupMapper.deleteByDeviceGroupIdList(orgId, deleteIdList);
            }
            needSendDeviceIdList.addAll(deleteIdList);
        }

        return needSendDeviceIdList;
    }

    private Map<Long, List<DeviceGroup>> getOldGroupIdDeviceIdListMap(Long orgId, List<Long> groupIdList) {
        List<DeviceGroup> oldDeviceGroupList = deviceGroupMapper.listByOrgIdAndGroupIds(orgId, groupIdList);
        Map<Long, List<DeviceGroup>> oldGroupIdDeviceIdListMap = oldDeviceGroupList.stream().collect(Collectors.groupingBy(DeviceGroup::getGroupId));
        return oldGroupIdDeviceIdListMap;
    }

    private List<Long> getAllCompositeIdList(Long orgId, DeviceCompositeDTO deviceCompositeDto, List<Long> curAndSubCompositeIdList) {
        List<Long> allCompositeIdList = Lists.newArrayList();
        allCompositeIdList.addAll(curAndSubCompositeIdList);
        // 当前设备组路径
        List<String> codeList = getParenCodePath(deviceCompositeDto, true);
        // 父path对应的compositeId
        List<Long> parentCompositeIdList = getParentCompositeIdList(orgId, codeList);
        allCompositeIdList.addAll(parentCompositeIdList);
        return allCompositeIdList;
    }

    private void batchCreateDeviceGroupRelation(Long orgId,
                                                List<Long> groupIdList,
                                                List<Long> addDeviceIdList,
                                                Map<Long, List<Long>> groupIdDeviceIdListMap,
                                                List<Long> needSendDeviceIdList) {
        if (CollectionUtils.isNotEmpty(groupIdList) && CollectionUtils.isNotEmpty(addDeviceIdList)) {
            int size = addDeviceIdList.size() * groupIdList.size();
            BatchIdDto dto = idgeneratorService.getNextIdBatchBytypeName("com.moredian.fishnet.device.DeviceGroup", size).getData();
            for (Long groupId : groupIdList) {
                List<Long> existDeviceIdList = groupIdDeviceIdListMap.get(groupId);
                if (existDeviceIdList == null) {
                    existDeviceIdList = Lists.newArrayList();
                }
                for (Long deviceId : addDeviceIdList) {
                    if (!existDeviceIdList.contains(deviceId)) {
                        DeviceGroup deviceGroup = new DeviceGroup();
                        deviceGroup.setDeviceGroupId(dto.nextId());
                        deviceGroup.setOrgId(orgId);
                        deviceGroup.setDeviceId(deviceId);
                        deviceGroup.setGroupId(groupId);
                        deviceGroupMapper.insert(deviceGroup);
                        needSendDeviceIdList.add(deviceId);
                    }
                }
            }
        }
    }

    private DeviceCompositeDTO getDeviceCompositeDto(Long orgId, Long compositeId, Integer changeSate) {
        DeviceCompositeDTO deviceCompositeDto;
        if (DeviceCompositeChangeMsgSate.COMPOSITE_DELETE.equals(changeSate)) {
            DeviceComposite deviceComposite = deviceCompositeManager.getCompositeByIdWithDelete(orgId, compositeId);
            deviceCompositeDto = com.moredian.bee.common.utils.BeanUtils.copyProperties(DeviceCompositeDTO.class, deviceComposite);
        } else {
            deviceCompositeDto = deviceCompositeManager.getCompositeInfo(orgId, compositeId, false);
        }
        return deviceCompositeDto;
    }

    private List<Long> getParentCompositeIdList(Long orgId, List<String> codeList) {
        List<DeviceComposite> deviceCompositeList = deviceCompositeMapper.getCompositeByCodeList(orgId, codeList,
            DeviceCompositeBizType.COMMON);
        return deviceCompositeList.stream().map(DeviceComposite::getDeviceCompositeId).distinct().collect(Collectors.toList());
    }

    private Map<Long, List<Long>> groupIdDeviceIdListMap(Long orgId, List<Long> groupIdList) {
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            List<DeviceGroup> deviceGroupList = deviceGroupMapper.listByOrgIdAndGroupIds(orgId, groupIdList);
            if (CollectionUtils.isNotEmpty(deviceGroupList)) {
                return deviceGroupList.stream()
                        .collect(Collectors.groupingBy(DeviceGroup::getGroupId, Collectors.mapping(DeviceGroup::getDeviceId, Collectors.toList())));
            }
        }
        return Maps.newHashMap();
    }

    private List<String> getParenCodePath(DeviceCompositeDTO deviceCompositeDto, boolean excludeSelf) {
        List<String> codeList = Arrays.stream(deviceCompositeDto.getPath().split("/")).collect(Collectors.toList());
        if (excludeSelf) {
            codeList.removeAll(Lists.newArrayList(deviceCompositeDto.getCode()));
        }
        return codeList;
    }

    private static List<Long> getCurAndSubCompositeIdList(Long compositeId, List<Long> subCompositeIdList) {
        List<Long> curAndSubCompositeIdList = Lists.newArrayList();
        curAndSubCompositeIdList.add(compositeId);
        if (CollectionUtils.isNotEmpty(subCompositeIdList)) {
            curAndSubCompositeIdList.addAll(subCompositeIdList);
        }
        return curAndSubCompositeIdList;
    }


    private static List<Long> getNewDeviceIdList(Map<Long, List<Long>> groupIdDeviceIdListMap,
                                                 Map<Long, List<Long>> groupIdCompositeDeviceIdListMap,
                                                 Long groupId) {
        List<Long> tempDeviceIdList = Lists.newArrayList();
        List<Long> newGroupDeviceIdList = groupIdDeviceIdListMap.get(groupId);
        if (CollectionUtils.isNotEmpty(newGroupDeviceIdList)) {
            tempDeviceIdList.addAll(newGroupDeviceIdList);
        }
        List<Long> newGroupIdCompositeDeviceIdList = groupIdCompositeDeviceIdListMap.get(groupId);
        if (CollectionUtils.isNotEmpty(newGroupIdCompositeDeviceIdList)) {
            tempDeviceIdList.addAll(newGroupIdCompositeDeviceIdList);
        }
        return tempDeviceIdList.stream().distinct().collect(Collectors.toList());
    }

    private Map<Long, List<Long>> getGroupIdDeviceIdListMapExcludeDeleteComposite(Long orgId,
                                                                                  List<Long> curAndSubCompositeIdList,
                                                                                  List<DeviceCompositeGroup> oldDeviceCompositeGroupList) {
        Map<Long, List<Long>> groupIdDeviceIdListMap = Maps.newHashMap();
        Map<Long, List<Long>> groupIdDeviceCompositeIdListMap = oldDeviceCompositeGroupList.stream()
                .filter(e -> DeviceCompositeGroupRangeType.GROUP.getCode().equals(e.getRangeType()))
                .filter(e -> CollectionUtils.isEmpty(curAndSubCompositeIdList) || !curAndSubCompositeIdList.contains(e.getRangeId()))
                .collect(Collectors.groupingBy(DeviceCompositeGroup::getGroupId, Collectors.mapping(DeviceCompositeGroup::getRangeId, Collectors.toList())));
        for (Map.Entry<Long, List<Long>> item : groupIdDeviceCompositeIdListMap.entrySet()) {
            List<Long> groupDeviceCompositeIdExcludeCurCompositeList = deviceCompositeManager.getDeviceIdsByCompositeDeepBatch(orgId, item.getValue(), null);
            if (CollectionUtils.isNotEmpty(groupDeviceCompositeIdExcludeCurCompositeList)) {
                List<DeviceCompositeItem> deviceCompositeItemList = deviceCompositeItemMapper.listByDeviceIds(orgId, groupDeviceCompositeIdExcludeCurCompositeList, DeviceCompositeBizType.COMMON);
                if (CollectionUtils.isNotEmpty(deviceCompositeItemList)) {
                    groupIdDeviceIdListMap.put(item.getKey(), deviceCompositeItemList.stream().map(DeviceCompositeItem::getDeviceId).distinct().collect(Collectors.toList()));
                }
            }
        }
        return groupIdDeviceIdListMap;
    }

    private Map<Long, List<Long>> getNewGroupRangeDeviceList(List<DeviceCompositeGroup> deviceGroupList) {
        return deviceGroupList.stream()
                .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                .collect(Collectors.groupingBy(DeviceCompositeGroup::getGroupId, Collectors.mapping(DeviceCompositeGroup::getRangeId, Collectors.toList())));
    }

}
