<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceSnapshot">
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="timestamp" property="timestamp"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_device_snapshot
    </sql>

    <sql id="sql_columns">
        id,
        org_id,
        device_id,
        device_sn,
        timestamp,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{id},
        #{orgId},
        #{deviceId},
        #{deviceSn},
        #{timestamp},
        now(3),
        now(3)
    </sql>

    <insert id="save">
        INSERT INTO
        <include refid="sql_table"/>
        (<include refid="sql_columns"/>)
        VALUES
        (#{id}, #{orgId}, #{deviceId}, #{deviceSn}, #{timestamp}, now(3), now(3))
    </insert>

    <delete id="deleteById">
        delete from <include refid="sql_table"></include> where id = #{id}
    </delete>

    <select id="getByOrgIdAndDeviceId" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId} and device_id = #{deviceId}
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where id = #{id}
    </select>
    <select id="queryOvertimeDeviceSnapshot" resultMap="BaseResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where gmt_create &lt; #{time}
    </select>
</mapper>
