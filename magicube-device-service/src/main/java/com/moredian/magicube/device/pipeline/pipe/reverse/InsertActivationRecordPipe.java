package com.moredian.magicube.device.pipeline.pipe.reverse;

import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.qrcode.QrCodeActivateRecord;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.manager.QrCodeActivateRecordManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 新增二维码激活记录管道
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class InsertActivationRecordPipe extends
    AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private QrCodeActivateRecordManager qrCodeActivateRecordManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        if (dto.getQrCodeId() == null){
            return true;
        }
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        QrCodeActivateRecord qrCodeActivateRecord = new QrCodeActivateRecord();
        qrCodeActivateRecord.setOrgId(context.getOrgId());
        qrCodeActivateRecord.setDeviceSn(dto.getDeviceSn());
        qrCodeActivateRecord.setQrCodeId(dto.getQrCodeId());
        Device device = context.getDevice();
        qrCodeActivateRecord.setDeviceId(device.getDeviceId());
        qrCodeActivateRecord.setDeviceType(device.getDeviceType());
        qrCodeActivateRecordManager.insert(qrCodeActivateRecord);
    }

}
