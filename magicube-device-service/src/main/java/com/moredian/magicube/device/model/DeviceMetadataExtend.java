package com.moredian.magicube.device.model;

import com.alibaba.fastjson2.JSONObject;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyDefineDTO;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description：设备物模型元数据扩展
 * @date ：2024/07/22 17:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceMetadataExtend extends IotDevicePropertyDefineDTO implements Serializable {

    private static final long serialVersionUID = -123327648723478L;

    private JSONObject propertyConfig;

    /**
     * 未配排序默认最后按照顺序展示
     */
    private Integer sort = 99999;
}
