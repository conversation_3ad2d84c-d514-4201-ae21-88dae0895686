package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.DeviceRuleRelation;
import com.moredian.magicube.device.dao.mapper.DeviceRuleRelationMapper;
import com.moredian.magicube.device.manager.DeviceRuleRelationManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.moredian.magicube.device.constant.BeanConstants.DEVICE_RULE_RELATION;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleRelationManagerImpl.java, v 1.0 Exp $
 */
@Component
public class DeviceRuleRelationManagerImpl implements DeviceRuleRelationManager {

    @Resource
    private DeviceRuleRelationMapper deviceRuleRelationMapper;
    @SI
    IdgeneratorService idgeneratorService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void ruleRelateDevice(Long orgId, Long ruleId, List<Long> deviceIdList) {
        deviceRuleRelationMapper.deleteDeviceRule(orgId, ruleId);

        if (CollectionUtils.isEmpty(deviceIdList)) {
            return;
        }

        ServiceResponse<BatchIdDto> nextIdBatch = idgeneratorService.getNextIdBatchBytypeName(DEVICE_RULE_RELATION, deviceIdList.size());
        BatchIdDto batchId = nextIdBatch.getData();

        List<DeviceRuleRelation> deviceRuleRelationList = deviceIdList.stream()
                .map(deviceId -> new DeviceRuleRelation().setDeviceRuleRelationId(batchId.nextId()).setOrgId(orgId).setDeviceId(deviceId).setRuleId(ruleId))
                .collect(Collectors.toList());
        deviceRuleRelationMapper.batchInsert(deviceRuleRelationList);
    }

    @Override
    public void deleteByOrgIdAndDeviceId(Long orgId, Long ruleId, List<Long> deviceIdList) {
        BizAssert.notNull(orgId);

        if (CollectionUtils.isEmpty(deviceIdList)) {
            return;
        }
        deviceRuleRelationMapper.deleteByOrgIdAndDeviceId(orgId, ruleId, deviceIdList);
    }

    @Override
    public List<Long> getRuleIdByOrgIdAndDeviceId(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId);
        BizAssert.notNull(deviceId);

        return deviceRuleRelationMapper.getRuleIdByOrgIdAndDeviceId(orgId, deviceId);
    }
}
