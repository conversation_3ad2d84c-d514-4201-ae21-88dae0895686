package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.GroupPersonSnapshot;
import com.moredian.magicube.device.dto.snapshot.GroupPersonSnapshotDTO;
import com.moredian.magicube.device.model.PersonIdAndGroupIdSnapshot;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/29
 */
public interface GroupPersonSnapshotManager {

    /**
     * 子列表中的数量
     */
    Integer perSubListSize = 1000;

    /**
     * 批量插入权限组人员快照
     *
     * @param dtoList
     * @return
     */
    Boolean batchInsert(List<GroupPersonSnapshotDTO> dtoList);

    /**
     * 根据设备快照id删除
     *
     * @param orgId
     * @param snapshotId
     * @return
     */
    Boolean deleteBySnapshotId(Long orgId, Long snapshotId);

    /**
     * 获取权限组的总人数(去重)
     *
     * @param orgId
     * @param snapshotId
     * @param groupIds
     * @return
     */
    int getDistinctPersonSize(Long orgId, Long snapshotId, List<Long> groupIds);

    /**
     * 分页去重查询组内人员
     *
     * @param orgId
     * @param snapshotId
     * @param groupIds
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<GroupPersonSnapshot> pageGroupDistinctPerson(Long orgId, Long snapshotId, List<Long> groupIds, Integer pageNo, Integer pageSize);

    /**
     * 根据personId查询组
     *
     * @param orgId
     * @param snapshotId
     * @param groupIdList  指定权限组范围
     * @param personIdList
     * @return
     */
    List<PersonIdAndGroupIdSnapshot> findGroupIdByPersonIds(Long orgId, Long snapshotId, List<Long> groupIdList, List<Long> personIdList);
}
