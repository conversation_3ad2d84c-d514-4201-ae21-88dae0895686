package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.DeviceTypeProperty;
import com.moredian.magicube.device.dao.mapper.DeviceTypePropertyMapper;
import com.moredian.magicube.device.dao.mapper.DeviceTypePropertyRelationMapper;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class DeviceTypePropertyManagerImpl implements DeviceTypePropertyManager {

    @Autowired
    private DeviceTypePropertyMapper deviceTypePropertyMapper;

    @Autowired
    private DeviceTypePropertyRelationMapper deviceTypePropertyRelationMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(DeviceTypeProperty deviceTypeProperty) {
        BizAssert.notNull(deviceTypeProperty.getPropertyKey(), "propertyKey must not be null");
        DeviceTypeProperty dtp = deviceTypePropertyMapper
            .getByKey(deviceTypeProperty.getPropertyKey());
        BizAssert.isTrue(dtp == null, DeviceErrorCode.DEVICE_TYPE_PROPERTY_EXIST,
            DeviceErrorCode.DEVICE_TYPE_PROPERTY_EXIST.getMessage());
        deviceTypeProperty
            .setId(idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_TYPE_PROPERTY)
                .pickDataThrowException());
        deviceTypeProperty.setStatus(YesNoFlag.YES.getValue());
        deviceTypePropertyMapper.insert(deviceTypeProperty);
        return deviceTypeProperty.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(DeviceTypeProperty deviceTypeProperty) {
        BizAssert.notNull(deviceTypeProperty.getId(), "deviceTypePropertyId must not be null");
        DeviceTypeProperty dtp = deviceTypePropertyMapper.getById(deviceTypeProperty.getId());
        BizAssert.isTrue(dtp != null, DeviceErrorCode.DEVICE_TYPE_PROPERTY_NOT_EXIST,
            DeviceErrorCode.DEVICE_TYPE_PROPERTY_NOT_EXIST.getMessage());
        deviceTypePropertyMapper.update(deviceTypeProperty);
        return deviceTypeProperty.getId();
    }

    @Override
    public DeviceTypeProperty getById(Long id) {
        BizAssert.notNull(id, "id must not be null");
        return deviceTypePropertyMapper.getById(id);
    }

    @Override
    public DeviceTypeProperty getByPropertyKey(String propertyKey) {
        BizAssert.notNull(propertyKey, "propertyKey must not be null");
        return deviceTypePropertyMapper.getByKey(propertyKey);
    }

    @Override
    public List<DeviceTypeProperty> list() {
        return deviceTypePropertyMapper.list();
    }

    @Override
    public List<DeviceTypeProperty> listByIds(List<Long> ids) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(ids), "deviceTypePropertyId must not be null");
        return deviceTypePropertyMapper.listByIds(ids);
    }

    @Override
    public List<Integer> getDeviceTypeByPropertyKey(String propertyKey) {
        BizAssert.notBlank(propertyKey, "propertyKey must not be null");
        DeviceTypeProperty property = deviceTypePropertyMapper.getByKey(propertyKey);
        if (property == null) {
            return Collections.emptyList();
        }
        return deviceTypePropertyRelationMapper.listDeviceTypeByPropertyId(property.getId());
    }

    @Override
    public Boolean containsDeviceType(String propertyKey, Integer deviceType) {
        return getDeviceTypeByPropertyKey(propertyKey).contains(deviceType);
    }
}
