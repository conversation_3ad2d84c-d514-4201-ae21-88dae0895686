package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 区域人员数量统计
 * @Author: fangj
 * @Date: 2024-03-05
 */
@Data
@TableName("hive_device_people_statistic")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PeopleNumberStatistic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备sn号
     */
    private String deviceSn;

    /**
     * 区域内人数
     */
    private Integer insidePeopleNum;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
