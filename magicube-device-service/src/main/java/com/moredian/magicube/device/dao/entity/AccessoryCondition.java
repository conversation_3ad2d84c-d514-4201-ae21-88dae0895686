package com.moredian.magicube.device.dao.entity;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 配件信息查询条件
 *
 * <AUTHOR>
 * @since 2021/12/8
 */
@Data
public class AccessoryCondition implements Serializable {

    private static final long serialVersionUID = 6032098721615416087L;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 配件sn
     */
    private String accessorySn;

    /**
     * 配件名
     */
    private String accessoryName;

    /**
     * 配件类型
     */
    private Integer accessoryType;

    /**
     * 关联设备ID
     */
    private Long deviceId;

    /**
     * 关联设备sn
     */
    private String deviceSn;

    /**
     * 关联设备列表
     */
    private List<String> deviceSnList;

    /**
     * 起始行
     */
    private Integer startRow;

    /**
     * 页面大小
     */
    private Integer pageSize;

}
