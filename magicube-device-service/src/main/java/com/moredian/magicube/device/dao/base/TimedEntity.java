package com.moredian.magicube.device.dao.base;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.moredian.magicube.common.util.DateUtils;

/**
 * 数据库映射实体类
 * <AUTHOR>
 * @date 2019-10-21 14:51:21
 */
@Data
public class TimedEntity implements Serializable {

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    @JsonFormat(pattern = DateUtils.SQL_TIMESTAMP, timezone="GMT+8")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = DateUtils.SQL_TIMESTAMP, timezone="GMT+8")
//    注解开启自动成为乐观锁 单表修改时自动添加sql gmt_modify = @gmt_modify
    @Version
    private Date gmtModify;
}
