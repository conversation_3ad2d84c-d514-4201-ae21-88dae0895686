package com.moredian.magicube.device.condition.white;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 白单查询参数
 *
 * <AUTHOR>
 * @since 2022/8/9
 */
@Getter
@Setter
public class QueryWhiteDeviceCondition implements Serializable {

    private static final long serialVersionUID = 7232524292581263088L;

    /**
     * 设备sn
     */
    private String serialNumber;

    /**
     * 设备sn列表
     */
    private List<String> serialNumbers;

    /**
     * Mac地址1
     */
    private String macAddress;

    /**
     * Mac地址2
     */
    private String macAddress2;

    /**
     * 私有key
     */
    private String privateKey;

    /**
     * 批量标记
     */
    private Integer batchFlag;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 激活状态 0-未激活 1-已激活
     */
    private Integer activityStatus;

    /**
     * 第三方设备id
     */
    private String thirdDeviceId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 是否开放平台激活
     */
    private Boolean isOpenPlat;

    /**
     * 是否开放平台激活
     */
    private Boolean isEduDevice;

    /**
     * 设备来源
     */
    private Integer deviceSource;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
