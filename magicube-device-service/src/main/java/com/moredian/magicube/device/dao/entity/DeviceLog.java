package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备日志
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_log")
public class DeviceLog extends TimedEntity {

    private static final long serialVersionUID = -1207345582286763455L;

    /**
     * 设备日志Id
     */
    private Long deviceLogId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 事件类型
     */
    private Integer envenType;

    /**
     * 事件描述
     */
    private String envenDesc;

    /**
     * 操作时间戳
     */
    private Long operTime;

    /**
     * 人员Id
     */
    private Long memberId;
}
