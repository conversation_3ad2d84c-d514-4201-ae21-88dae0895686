package com.moredian.magicube.device.utils.qrcode;

import com.google.zxing.*;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import javax.imageio.ImageIO;

/**
 * @Classname： QrCodeByteUtil
 * @Date: 2023/1/4 11:04 上午
 * @Author: _AF
 * @Description: 二维码生成工具类
 */
public class QrCodeByteUtil {

    private static final Charset UTF8 = Charset.forName("UTF-8");//编码格式
    private static final int MARGIN = 0;//二维码边距
    private static final int WIDTH = 800;//二维码宽
    private static final int HEIGHT = 800;//二维码高
    private static final String SUFFIX = "jpg";//图片后缀名
    private static final String QRPATH = "qrcode.jpg";//二维码生成路径

    /**
     * 压缩字符串为数组
     *
     * @param str
     * @return
     */
    public static byte[] compress(String str) {
        if (str == null || str.length() == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out);) {
            gzip.write(str.getBytes(UTF8));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }

    /**
     * 数组解压缩为字符串
     *
     * @param bytes
     * @return
     */
    public static String uncompress(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(bytes);
        try (GZIPInputStream gzip = new GZIPInputStream(in);) {
            byte[] buffer = new byte[1024];
            int n;
            while ((n = gzip.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new String(out.toByteArray(), UTF8);
    }

    /**
     * 指定位置生成二维码
     *
     * @param data 二维码数据
     * @return
     */
    public static byte[] encodeQRCode(byte[] data) {
        return encodeQRCode(data, ErrorCorrectionLevel.L, MARGIN, WIDTH, HEIGHT, SUFFIX);
    }


    /**
     * 生成二维码
     *
     * @param data   二维码数据
     * @param level  纠错等级
     * @param margin 二维码边距
     * @param width  二维码宽
     * @param height 二维码高
     * @param suffix 二维码图片后缀名
     * @param path   二维码图片路径
     */
    public static byte[] encodeQRCode(byte[] data, ErrorCorrectionLevel level, int margin, int width, int height, String suffix) {
        // 定义二维码的参数
        HashMap<EncodeHintType, Object> hashMap = new HashMap<EncodeHintType, Object>();
        // 设置二维码纠错等级
        hashMap.put(EncodeHintType.ERROR_CORRECTION, level);
        // 设置二维码边距
        hashMap.put(EncodeHintType.MARGIN, margin);
        try {
            // 开始生成二维码
            BitMatrix bitMatrix = new MultiFormatWriter().encodeQRCode(data, width, height, hashMap);
            BufferedImage bufferedImage = MatrixToImageByteWriter.toBufferedImage(bitMatrix);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "jpg", os);
            return os.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    public static void main(String[] args) {
        test11();
    }


    public static String test11() {
        //FF01
//        long type1 = ipTo2("255.1");
//        //long type2 = ipToShort("0");
//        long l2 = ipToLong("*************");
//        ByteBuffer buffer = ByteBuffer.allocate(7);
//        buffer.putShort((short) type1);
//        buffer.put((byte) 4);
//        buffer.putInt((int) l2);
//
//        buffer.rewind();

        //FF02
        long type1 = ipTo2("255.3");
        String content = "donghaha";
        byte length = (byte) content.getBytes().length;
        ByteBuffer buffer = ByteBuffer.allocate(2 + 1 + length);
        buffer.putShort((short) type1);
        buffer.put(length);
        buffer.put(content.getBytes());
        buffer.rewind();

        // buffer.flip();
        //获取buffer中有效大小
        int len = buffer.limit() - buffer.position();

        byte[] bytes = new byte[len];

        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = buffer.get();

        }
        encodeQRCode(bytes);

//        try {
//            encodeQrCode2(bytes);
//        } catch (WriterException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        return null;
    }

    public static long ipTo2(String strIp) {
        long[] ip = new long[2];

        try {
            //先找出字符串中点的位置
            int position1 = strIp.indexOf(".");
            //将点分隔的字符串转换为整数
            ip[0] = Long.parseLong(strIp.substring(0, position1));
            ip[1] = Long.parseLong(strIp.substring(position1 + 1));

            return (ip[0] << 8) + (ip[1]);

        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
