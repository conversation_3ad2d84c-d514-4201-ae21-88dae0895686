<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.OrgDeviceMigrationMapper">

    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.OrgDeviceMigration">
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="device_id" property="deviceId"/>
        <result column="migration_status" property="migrationStatus"/>
        <result column="ding_talk_url" property="dingTalkUrl"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_base_column">
        id,
        org_id,
        device_id,
        migration_status,
        ding_talk_url,
        gmt_create,
        gmt_modify
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.OrgDeviceMigration">
        INSERT INTO hive_org_device_migration (org_id, device_id, migration_status, ding_talk_url, gmt_create,
                                               gmt_modify)
        VALUES (#{orgId}, #{deviceId}, #{migrationStatus}, #{dingTalkUrl}, now(), now())
    </insert>
    <insert id="batchInsert">
        INSERT IGNORE INTO hive_org_device_migration (org_id, device_id, migration_status, ding_talk_url, gmt_create, gmt_modify)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.orgId}, #{item.deviceId}, #{item.migrationStatus}, #{item.dingTalkUrl}, now(), now())
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.moredian.magicube.device.dao.entity.OrgDeviceMigration">
        UPDATE hive_org_device_migration
        SET org_id           = #{orgId},
            device_id        = #{deviceId},
            migration_status = #{migrationStatus},
            ding_talk_url    = #{dingTalkUrl},
            gmt_modify       = #{gmtModify}
        WHERE id = #{id}
    </update>
    <update id="updateOrgUrlByDeviceId">
        UPDATE hive_org_device_migration
        SET ding_talk_url = #{bindOrgUrl}
        WHERE device_id = #{deviceId}
    </update>
    <update id="updateMigrationStatus">
        UPDATE hive_org_device_migration
        SET migration_status = #{migrationStatus}
        WHERE device_id = #{deviceId}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE
        FROM hive_org_device_migration
        WHERE id = #{id}
    </delete>
    <delete id="deleteByOrgIdAndDeviceId">
        DELETE
        FROM hive_org_device_migration
        WHERE org_id = #{orgId}
          and device_id = #{deviceId}
    </delete>

    <select id="selectMigrationListByOrgId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
        <include refid="sql_base_column"/>
        FROM hive_org_device_migration
        WHERE org_id = #{orgId}
        and migration_status != 1
    </select>
    <select id="selectMigrationDeviceInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="sql_base_column"/>
        FROM hive_org_device_migration
        WHERE org_id = #{orgId}
        and device_id = #{deviceId}
    </select>
    <select id="selectMigrationDeviceInfoByDeviceId" resultMap="BaseResultMap">
        SELECT
        <include refid="sql_base_column"/>
        FROM hive_org_device_migration
        WHERE device_id = #{deviceId}
    </select>


</mapper>

