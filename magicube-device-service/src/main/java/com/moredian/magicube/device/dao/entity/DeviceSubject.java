package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备主题表
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_subject")
public class DeviceSubject extends TimedEntity implements Cloneable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备主题id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备主题名称
     */
    private String name;

    /**
     * 主题图片url(逗号分隔)
     */
    private String imgUrls;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业logo图片url
     */
    private String logoImgUrl;

    /**
     * 主题类型 1-壁纸 2-屏保
     */
    private Integer type;

    /**
     * 模板类型：1-模板一 2-模板二
     */
    private Integer templateType;

    /**
     * 是否开关，默认开 1-开，0-关
     */
    private Integer enable;

    /**
     * 扩展信息
     */
    private String expend;


    @Override
    public Object clone() throws CloneNotSupportedException {
        DeviceSubject cloned = (DeviceSubject) super.clone();
        cloned.id =  this.id;
        cloned.orgId =  this.orgId;
        cloned.name =  this.name;
        cloned.imgUrls =  this.imgUrls;
        cloned.companyName =  this.companyName;
        cloned.logoImgUrl =  this.logoImgUrl;
        cloned.type =  this.type;
        cloned.templateType =  this.templateType;
        cloned.enable =  this.enable;
        cloned.expend =  this.expend;
        return cloned;
    }
}
