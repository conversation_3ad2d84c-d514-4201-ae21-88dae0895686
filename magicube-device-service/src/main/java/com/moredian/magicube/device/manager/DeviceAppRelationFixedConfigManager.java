package com.moredian.magicube.device.manager;

import java.util.List;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;

public interface DeviceAppRelationFixedConfigManager {



	/**
	 * 分页查询信息
	 *
	 * @param dto 查询条件
	 * @return
	 */
	Pagination<DeviceAppRelationFixedConfig> listPage(QueryDeviceAppRelationConfigDTO dto);

	/**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
	List<DeviceAppRelationFixedConfig> listAll();

	/**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
	DeviceAppRelationFixedConfig getById(Long id);

	/**
	 * 根据设备sn查询
	 *
	 * @param bizType bizType
	 * @param bizId bizId
	 * @return 返回记录，没有返回null
	 */
	DeviceAppRelationFixedConfig getByBizTypeAndId(Integer bizType,String bizId);
	
	/**
     * 新增
     *
     * @param deviceAppRelationFixedConfig 新增的记录
     * @return 返回主键id
     */
	Long insert(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);
	
	/**
     * 修改所有字段
     *
     * @param deviceAppRelationFixedConfig 修改的记录
     * @return 返回影响行数
     */
	int update(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);
	
	/**
     * 修改不为null字段
     *
     * @param deviceAppRelationFixedConfig 修改的记录
     * @return 返回影响行数
     */
	int updateSelective(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);
	
	/**
     * 删除记录
     *
     * @param id 待删除的记录id
     * @return 返回影响行数
     */
	int delete(Long id);

	/**
	 * 根据type和id删除
	 *
	 * @param deviceAppRelationFixedConfig 待删除的记录
	 * @return 返回影响行数
	 */
	int deleteByBizTypeAndId(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);


}