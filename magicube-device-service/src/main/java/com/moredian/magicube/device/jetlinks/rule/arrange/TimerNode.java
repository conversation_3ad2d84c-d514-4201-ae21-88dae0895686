package com.moredian.magicube.device.jetlinks.rule.arrange;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description：定时任务节点
 * @date ：2024/08/07 11:23
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class TimerNode extends Node{

    public static final String TYPE = "timer";

    private String cron;

    private String scheduleRule;

    public TimerNode() {
        super(TYPE);
    }
}
