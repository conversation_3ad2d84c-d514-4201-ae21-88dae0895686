package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.Distributor;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributorManager {

    /**
     * 新增经销商锁定设备列表
     *
     * @param distributor 经销商设备信息
     * @return
     */
    Long insert(Distributor distributor);

    /**
     * 根据机构名称查询经销商设备信息
     *
     * @param orgName 经销商设备信息
     * @return
     */
    List<Distributor> listByOrgName(String orgName);

    /**
     * 根据设备sn查询经销商设备信息
     *
     * @param deviceSn 经销商设备信息
     * @return
     */
    Distributor getByDeviceSn(String deviceSn);
}
