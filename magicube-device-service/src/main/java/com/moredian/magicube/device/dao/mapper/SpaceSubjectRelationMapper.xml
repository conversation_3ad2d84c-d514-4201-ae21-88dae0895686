<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.SpaceSubjectRelationMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap"
    type="com.moredian.magicube.device.dao.entity.SpaceSubjectReleation">
    <id column="id" property="id"/>
    <result column="org_id" property="orgId"/>
    <result column="subject_id" property="subjectId"/>
    <result column="space_id" property="spaceId"/>
    <result column="type" property="type"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <sql id="sql_table">
        hive_space_subject_relation
    </sql>

  <sql id="sql_columns">
        id,
        org_id,
        subject_id,
        space_id,
        type,
        gmt_create,
        gmt_modify
	</sql>

  <sql id="sql_values">
        #{id},
        #{orgId},
        #{subjectId},
        #{spaceId},
        #{type},
        now(3),
        now(3)
    </sql>

  <insert id="insert"
    parameterType="com.moredian.magicube.device.dao.entity.SpaceSubjectReleation">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>

  <insert id="batchInsert" parameterType="list">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    <foreach collection="relations" item="item" separator=",">
      (
      #{item.id},
      #{item.orgId},
      #{item.subjectId},
      #{item.spaceId},
      #{item.type},
      now(3),
      now(3)
      )
    </foreach>
      on duplicate key update subject_id = values(subject_id) , gmt_modify=values(gmt_modify)
  </insert>

  <select id="listSpaceIdByOrgIdAndSubjectId" parameterType="long" resultType="long">
    select
    DISTINCT(space_id)
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and subject_id = #{subjectId}
  </select>

  <delete id="deleteByCondition" parameterType="long">
    delete from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="subjectIds != null and subjectIds.size() > 0">
      and subject_id in
      <foreach collection="subjectIds" index="index" item="subjectId" open="(" separator=","
        close=")">
        #{subjectId}
      </foreach>
    </if>
    <if test="spaceIds != null and spaceIds.size() > 0">
      and space_id in
      <foreach collection="spaceIds" index="index" item="spaceId" open="(" separator=","
        close=")">
        #{spaceId}
      </foreach>
    </if>
  </delete>

  <select id="listByOrgIdAndSubjectIds" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="subjectIds != null and subjectIds.size() > 0">
      and subject_id in
      <foreach collection="subjectIds" index="index" item="subjectId" open="(" separator=","
        close=")">
        #{subjectId}
      </foreach>
    </if>
  </select>

  <select id="listByOrgIdAndSpaceIds" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="spaceIds != null and spaceIds.size() > 0">
      and space_id in
      <foreach collection="spaceIds" index="index" item="spaceId" open="(" separator=","
        close=")">
        #{spaceId}
      </foreach>
    </if>
    <if test="type != null">
      and type = #{type}
    </if>
  </select>
</mapper>
