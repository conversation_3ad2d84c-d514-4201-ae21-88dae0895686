package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.person.PagePersonInfoDTO;
import com.moredian.magicube.device.dto.person.PersonInfoDTO;
import com.moredian.magicube.device.dto.person.QueryPersonInfoDTO;
import com.moredian.magicube.device.manager.DeviceSyncPersonInfoManager;
import com.moredian.magicube.device.service.DeviceSyncPersonInfoService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/25
 */
@SI
public class DeviceSyncPersonInfoServiceImpl implements DeviceSyncPersonInfoService {

    @Autowired
    private DeviceSyncPersonInfoManager deviceSyncPersonInfoManager;

    @Override
    public ServiceResponse<Pagination<PersonInfoDTO>> pagePersonInfo(PagePersonInfoDTO dto) {
        return new ServiceResponse<>(deviceSyncPersonInfoManager.pagePersonInfo(dto));
    }

    @Override
    public ServiceResponse<List<PersonInfoDTO>> queryPersonInfo(QueryPersonInfoDTO dto) {
        return new ServiceResponse<>(deviceSyncPersonInfoManager.queryPersonInfo(dto));
    }
}
