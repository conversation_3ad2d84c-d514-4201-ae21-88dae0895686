package com.moredian.magicube.device.helper;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.manager.DeviceCompositeManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 设备组code码生成
 *
 * <AUTHOR>
 * @date 2024/11/14 01:54
 */
@Slf4j
@Component
public class CompositeCodeGenHelper {

    @Resource
    private DeviceCompositeManager deviceCompositeManager;

    private final String rootDeptCode = "00000000";

    static char[] chars = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B',
        'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
        'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l',
        'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};

    static List<Character> allChars = new ArrayList<>(chars.length);

    static {
        for (char aChar : chars) {
            allChars.add(aChar);
        }
    }

    static char minChar = chars[0];
    static char maxChar = chars[chars.length - 1];
    static int idx_0 = 0;
    static int idx_A = 10;
    static int idx_a = 36;
    static char char_9 = chars[idx_0 + 9];
    static char char_Z = chars[idx_A + 25];
    static char char_z = chars[idx_a + 25];


    public String getVirtualRootCompositeCode() {
        return rootDeptCode;
    }


    /**
     * <pre>
     *  生成Code
     *  如果一个事务中多次调用，且新code未入库，则会返回相同的code;
     *
     *  ------反例-------------------------------------------------
     *   开启事务：
     *   code1 = genCodes(xxx,1);
     *   dept1.setCode(code1);
     *
     *   code2 = genCodes(xxx,1);
     *   dept2.setCode(code2);
     *   saveDeptToDb(dept1)
     *   saveDeptToDb(dept2)
     *   提交事务；
     *   ---提交事务时，code1和code相同，违反唯一约束；
     *   -------------------------------------------------------
     *
     *  ------正确使用示例一--------------------------------------
     *   开启事务：
     *   code1 = genCodes(xxx,1);
     *   dept1.setCode(code1);
     *   saveDeptToDb(dept1)
     *
     *   code2 = genCodes(xxx,1);
     *   dept2.setCode(code2);
     *   saveDeptToDb(dept2)
     *   提交事务；
     *   -------------------------------------------------------
     *
     *  ------正确使用示例二--------------------------------------
     *   开启事务：
     *   deptCodes = genCodes(xxx,2);
     *   dept1.setCode(deptCodes.next());
     *   dept2.setCode(deptCodes.next());
     *   saveDeptToDb(dept1)
     *   saveDeptToDb(dept2)
     *   提交事务；
     *   -------------------------------------------------------
     *
     *   ------正确使用示例三--------------------------------------
     *   开启事务：
     *   String code1 = genCodes(xxx,1).next();
     *   String code2 = nextCode(code1);
     *   dept2.setCode(code1);
     *   dept2.setCode(code2);
     *   saveDeptToDb(dept1)
     *   saveDeptToDb(dept2)
     *   提交事务；
     *   -------------------------------------------------------
     * </pre>
     */
    public CompositeCodes genCodes(Long orgId, int count, Integer bizType) {
        // 设备组修改是有分布式锁的，这里不加锁；
        // 如果真code重复，db会有uk报错；
        String maxCode = deviceCompositeManager.getMaxDeptCode(orgId, bizType);
        if (maxCode == null) {
            maxCode = rootDeptCode;
        }
        return genCodes(maxCode, count);
    }

    /**
     * 生成Code
     */
    public static CompositeCodes genCodes(String currentCode, int count) {
        List<String> newCodes = new ArrayList<>();
        String nextCode = currentCode;
        for (int i = 0; i < count; i++) {
            nextCode = nextCode(nextCode);
            newCodes.add(nextCode);
        }

        log.debug("dbCode: {} -> newCode:{}", currentCode, newCodes);
        return new CompositeCodes(newCodes);
    }

    public static String nextCode(String code) {
        int idx = -1;
        for (int i = code.length() - 1; i >= 0; i--) {
            char c = code.charAt(i);
            if (c != maxChar) {
                idx = i;
                break;
            }
        }

        if (idx == -1) {
            log.error("code已耗尽");
        }

        if (idx == 0) {
            log.error("code有耗尽风险，请尽快开发重置code功能");
        }

        char[] nextChars = code.toCharArray();
        //匹配位+1;
        char nextChar = nextChar(code.charAt(idx));
        nextChars[idx] = nextChar;

        //匹配位之后置空；
        for (int i = idx + 1; i <= nextChars.length - 1; i++) {
            nextChars[i] = minChar;
        }

        return new String(nextChars);
    }


    /**
     * 效率是binarySearch的3倍
     */
    public static char nextChar(char aChar) {
        if (aChar < char_9) {
            int increment = aChar - '0';
            return chars[increment + idx_0 + 1];
        }
        if (aChar == char_9) {
            return chars[idx_A];
        }
        if (aChar < char_Z) {
            int increment = aChar - 'A';
            return chars[idx_A + increment + 1];
        }
        if (aChar == char_Z) {
            return chars[idx_a];
        }
        if (aChar < char_z) {
            int increment = aChar - 'a';
            return chars[idx_a + increment + 1];
        }
        return minChar;
    }

    public static char nextChar2(char input) {
        int idx = Collections.binarySearch(allChars, input);
        if (idx == allChars.size() - 1) {
            return minChar;
        } else {
            return chars[idx + 1];
        }
    }

    public static class CompositeCodes {

        private final Iterator<String> it;

        public CompositeCodes(List<String> codes) {
            BizAssert.notNull(codes, "codes must not be null");
            this.it = codes.iterator();
        }

        public String next() {
            return it.next();
        }
    }

    public static void main(String[] args) {
        String[][] testData = new String[][]{{"00000000", "00000001"}, {"00000009", "0000000A"},
            {"0000000A", "0000000B"}, {"0000000Z", "0000000a"}, {"0000000a", "0000000b"},
            {"0000000z", "00000010"}, {"0000zazx", "0000zazy"}, {"0000zazz", "0000zb00"},
            {"0000zzzz", "00010000"}, {"1000zzzz", "10010000"},};
        for (String[] pair : testData) {
            String nextCode = nextCode(pair[0]);
            System.out.printf("%s -> %s\n", pair[0], nextCode);
            if (!nextCode.equals(pair[1])) {
                throw new RuntimeException("有错");
            }
        }
    }
}
