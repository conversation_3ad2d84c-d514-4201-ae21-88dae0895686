package com.moredian.magicube.device.helper;

import com.google.common.collect.Maps;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.hephaestus.request.relation.GetRelationBySerialNumberRequest;
import com.moredian.hephaestus.response.relation.SimpleSpuInventoryRelationResponse;
import com.moredian.hephaestus.service.SpuInventoryRelationService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypeManager;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.moredian.magicube.device.config.ParamConstants.REGEX_CHINESE;

/**
 * 设备类型名称helper
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeviceTypeNameHelper {

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceTypeManager deviceTypeManager;

    @Resource
    private WhiteDeviceManager whiteDeviceManager;

    @SI
    private SpuInventoryRelationService spuInventoryRelationService;

    /**
     * 获取展示设备类型名称
     *
     * @param deviceSn 设备sn
     * @return
     */
    public String getDisplayDeviceTypeName(String deviceSn) {
        GetRelationBySerialNumberRequest request = new GetRelationBySerialNumberRequest();
        request.setSerialNumberList(Collections.singletonList(deviceSn));
        Map<String/*deviceSn*/, String/*shortMaterialName*/> shortMaterialNameMap = Maps.newHashMap();
        List<SimpleSpuInventoryRelationResponse> relations = spuInventoryRelationService
            .findSpuInventoryRelationBySerialNumberList(request).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(relations)) {
            for (SimpleSpuInventoryRelationResponse relation : relations) {
                if (StringUtils.isNotBlank(relation.getShortMaterialName())) {
                    shortMaterialNameMap.put(relation.getSerialNumber(),
                        relation.getSpuDisplayName());
                }
            }
        }
        String shortMaterialName = shortMaterialNameMap.get(deviceSn);
        //1.优先使用SPU里面的设备类型展示名称
        if (StringUtils.isNotBlank(shortMaterialName)) {
            return shortMaterialName;
        }
        Device device = deviceManager.getByDeviceSn(deviceSn);
        if (device == null || device.getDeviceType() == null) {
            return null;
        }
        //2.使用设备类型表中的设备类型展示名称
        DeviceType deviceType = deviceTypeManager.getByDeviceType(device.getDeviceType());
        if (deviceType != null) {
            //判断分销设备，执行设备类型名称不同展示
            if (device.getDeviceFlag() != null && device.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
                shortMaterialName = deviceType.getDeviceTypeDisplayNameDing();
            }else {
                shortMaterialName = deviceType.getDeviceTypeDisplayNameHy();
            }
            return shortMaterialName;
        }
        //3.使用枚举中的设备类型展示名称
        if (com.moredian.magicube.common.enums.DeviceType.getName(device.getDeviceType()) != null) {
            shortMaterialName = Objects.requireNonNull(
                com.moredian.magicube.common.enums.DeviceType.getName(
                    device.getDeviceType())).replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
        }
        return shortMaterialName;
    }

    /**
     * 通过sn查询设备类型展示名称(不校验设备激活)
     */
    public Map<String, String> listDeviceNameBySn(List<String> deviceSns) {
        Map<String, String> map = new HashMap<>();

        if (CollectionUtils.isEmpty(deviceSns)) {
            return map;
        }

        GetRelationBySerialNumberRequest request = new GetRelationBySerialNumberRequest();
        request.setSerialNumberList(deviceSns);
        Map<String/*deviceSn*/, String/*shortMaterialName*/> shortMaterialNameMap = Maps.newHashMap();
        List<SimpleSpuInventoryRelationResponse> relations = spuInventoryRelationService
                .findSpuInventoryRelationBySerialNumberList(request).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(relations)) {
            for (SimpleSpuInventoryRelationResponse relation : relations) {
                if (StringUtils.isNotBlank(relation.getShortMaterialName())) {
                    shortMaterialNameMap.put(relation.getSerialNumber(),
                            relation.getSpuDisplayName());
                }
            }
        }
        List<InventoryDevice> inventoryDevices = whiteDeviceManager.listByDeviceSns(deviceSns);
        if (CollectionUtils.isEmpty(inventoryDevices)) {
            log.info("设备白单不存在deviceSns:{}", deviceSns);
            return map;
        }
        List<Integer> deviceTypes = inventoryDevices.stream()
                .map(InventoryDevice::getDeviceType).distinct().collect(Collectors.toList());

        Map<String, InventoryDevice> inventoryDeviceMap = inventoryDevices.stream()
                .collect(Collectors.toMap(InventoryDevice::getSerialNumber, Function.identity()));
        if (CollectionUtils.isEmpty(deviceTypes)) {
            return map;
        }

        List<DeviceType> deviceTypeList = deviceTypeManager.listByTypes(deviceTypes);
        if (CollectionUtils.isEmpty(deviceTypeList)) {
            log.info("设备类型信息不存在deviceTypes:{}", deviceTypes);
            return map;
        }
        Map<Integer, DeviceType> deviceTypMap = deviceTypeList.stream()
                .collect(Collectors.toMap(DeviceType::getDeviceType, x -> x));
        for (String deviceSn : deviceSns) {
            String shortMaterialName = shortMaterialNameMap.get(deviceSn);
            //1.优先使用SPU里面的设备类型展示名称
            if (StringUtils.isNotBlank(shortMaterialName)) {
                map.put(deviceSn, shortMaterialName);
                continue;
            }

            //2.使用设备类型表中的设备类型展示名称
            InventoryDevice inventoryDevice = inventoryDeviceMap.get(deviceSn);
            if (inventoryDevice == null) {
                continue;
            }
            DeviceType deviceType = deviceTypMap.get(inventoryDevice.getDeviceType());
            if (deviceType != null) {
                shortMaterialName = deviceType.getDeviceTypeDisplayNameHy();
                map.put(deviceSn, shortMaterialName);
                continue;
            }

            //3.使用枚举中的设备类型展示名称
            if (inventoryDevice.getDeviceType() != null && com.moredian.magicube.common.enums
                .DeviceType.getName(inventoryDevice.getDeviceType()) != null) {
                shortMaterialName = Objects.requireNonNull(com.moredian.magicube.common.enums
                        .DeviceType.getName(inventoryDevice.getDeviceType()))
                    .replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                map.put(deviceSn, shortMaterialName);
            }
        }
        return map;
    }


    /**
     * 获取展示设备类型名称
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    public Map<String, String> ListDisplayDeviceTypeName(List<String> deviceSns) {
        Map<String, String> map = new HashMap<>();
        GetRelationBySerialNumberRequest request = new GetRelationBySerialNumberRequest();
        request.setSerialNumberList(deviceSns);
        Map<String/*deviceSn*/, String/*shortMaterialName*/> shortMaterialNameMap = Maps.newHashMap();
        List<SimpleSpuInventoryRelationResponse> relations = spuInventoryRelationService
            .findSpuInventoryRelationBySerialNumberList(request).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(relations)) {
            for (SimpleSpuInventoryRelationResponse relation : relations) {
                if (StringUtils.isNotBlank(relation.getShortMaterialName())) {
                    shortMaterialNameMap.put(relation.getSerialNumber(),
                        relation.getSpuDisplayName());
                }
            }
        }
        List<Device> devices = deviceManager.listByDeviceSns(deviceSns);
        if (CollectionUtils.isEmpty(devices)) {
            log.info("设备信息不存在deviceSns:{}", deviceSns);
            return map;
        }
        Map<String, Device> deviceMap = devices.stream()
            .collect(Collectors.toMap(Device::getDeviceSn, x -> x));
        List<Integer> deviceTypes = devices.stream().map(Device::getDeviceType).distinct()
            .collect(Collectors.toList());
        List<DeviceType> deviceTypeList = deviceTypeManager.listByTypes(deviceTypes);
        if (CollectionUtils.isEmpty(deviceTypeList)) {
            log.info("设备类型信息不存在deviceTypes:{}", deviceTypes);
            return map;
        }
        Map<Integer, DeviceType> deviceTypMap = deviceTypeList.stream()
            .collect(Collectors.toMap(DeviceType::getDeviceType, x -> x));
        for (String deviceSn : deviceSns) {
            String shortMaterialName = shortMaterialNameMap.get(deviceSn);
            //1.优先使用SPU里面的设备类型展示名称
            if (StringUtils.isNotBlank(shortMaterialName)) {
                map.put(deviceSn, shortMaterialName);
                continue;
            }

            //2.使用设备类型表中的设备类型展示名称
            Device device = deviceMap.get(deviceSn);
            if (device == null) {
                continue;
            }
            DeviceType deviceType = deviceTypMap.get(device.getDeviceType());
            if (deviceType != null) {
                shortMaterialName = deviceType.getDeviceTypeDisplayNameHy();
                map.put(deviceSn, shortMaterialName);
                continue;
            }

            if (device.getDeviceType() != null && com.moredian.magicube.common.enums.DeviceType
                .getName(device.getDeviceType()) != null) {
                //3.使用枚举中的设备类型展示名称
                shortMaterialName = Objects.requireNonNull(
                    com.moredian.magicube.common.enums.DeviceType.getName(
                        device.getDeviceType())).replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                map.put(deviceSn, shortMaterialName);
            }
        }
        return map;
    }
}
