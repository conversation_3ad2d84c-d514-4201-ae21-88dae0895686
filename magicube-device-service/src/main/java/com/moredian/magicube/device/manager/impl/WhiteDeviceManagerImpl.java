package com.moredian.magicube.device.manager.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dao.mapper.InventoryDeviceMapper;
import com.moredian.magicube.device.condition.white.QueryWhiteDeviceCondition;
import com.moredian.magicube.device.dto.version.DeviceVersionInfoDTO;
import com.moredian.magicube.device.dto.white.BatchInsertWhiteListDeviceRequest;
import com.moredian.magicube.device.dto.white.GetWhiteDeviceListRequest;
import com.moredian.magicube.device.dto.white.QueryWhiteDeviceDTO;
import com.moredian.magicube.device.dto.white.SimpleWhiteDeviceResponse;
import com.moredian.magicube.device.dto.white.UpdateWhiteDeviceDTO;
import com.moredian.magicube.device.dto.white.WhiteListDeviceDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.DeviceTypeNameHelper;
import com.moredian.magicube.device.manager.DeviceTypeMapManager;
import com.moredian.magicube.device.manager.DeviceVersionManager;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class WhiteDeviceManagerImpl implements WhiteDeviceManager {

    @Autowired
    private InventoryDeviceMapper inventoryDeviceMapper;

    @Autowired
    private DeviceVersionManager deviceVersionManager;

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Resource
    private DeviceTypeNameHelper deviceTypeNameHelper;

    @Override
    public Boolean insert(InventoryDevice inventoryDevice) {
        BizAssert.notNull(inventoryDevice.getSerialNumber(), "deviceSn must not be null");
        BizAssert.notNull(inventoryDevice.getMacAddress(), "macAddress must not be null");
        BizAssert.notNull(inventoryDevice.getPrivateKey(), "privateKey must not be null");
        BizAssert.notNull(inventoryDevice.getDeviceType(), "deviceType must not be null");
        InventoryDevice di = inventoryDeviceMapper.getByDeviceSn(inventoryDevice.getSerialNumber());
        BizAssert.isTrue(di == null, DeviceErrorCode.DEVICE_WHITE_LIST_EXIST, DeviceErrorCode.DEVICE_WHITE_LIST_EXIST.getMessage());
        inventoryDevice.setActivityStatus(YesNoFlag.NO.getValue());
        inventoryDeviceMapper.insert(inventoryDevice);
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchInsert(List<InventoryDevice> inventoryDevices) {
        for (InventoryDevice inventoryDevice : inventoryDevices) {
            BizAssert.notNull(inventoryDevice.getSerialNumber(), "deviceSn must not be null");
            BizAssert.notNull(inventoryDevice.getMacAddress(), "macAddress must not be null");
            BizAssert.notNull(inventoryDevice.getPrivateKey(), "privateKey must not be null");
            BizAssert.notNull(inventoryDevice.getDeviceType(), "deviceType must not be null");
            inventoryDevice.setActivityStatus(YesNoFlag.NO.getValue());
        }
        log.info("批量新增设备白名单参数:{}", inventoryDevices);
        inventoryDeviceMapper.batchInsert(inventoryDevices);
        return Boolean.TRUE;
    }

    @Override
    public InventoryDevice getByOrgIdAndThirdDeviceId(Long orgId, String thirdDeviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(thirdDeviceId, "thirdDeviceId must not be null");
        return inventoryDeviceMapper.getByOrgIdAndThirdDeviceId(orgId, thirdDeviceId);
    }

    @Override
    public InventoryDevice getByDeviceSnAndActivationCode(String deviceSn, String activationCode) {
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        BizAssert.notNull(activationCode, "activationCode must not be null");
        return inventoryDeviceMapper.getByDeviceSnAndActivationCode(deviceSn, activationCode);
    }

    @Override
    public Pagination<InventoryDevice> listPage(QueryWhiteDeviceDTO dto) {
        BizAssert.notNull(dto, "query params must not be null");
        //InventoryDevice inventoryDevice = new InventoryDevice();
        //BeanUtils.copyProperties(pagination.getData().get(0), inventoryDevice);
        QueryWhiteDeviceCondition condition = new QueryWhiteDeviceCondition();
        BeanUtils.copyProperties(dto, condition);
        Page<InventoryDevice> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(), dto.getPageSize() == null ? 20 : dto.getPageSize())
                .setOrderBy("created_at DESC").doSelectPage(() -> inventoryDeviceMapper.listByCondition(condition));
        Pagination<InventoryDevice> pt = new Pagination<>();
        if (CollectionUtils.isEmpty(page)) {
            return pt;
        }
        pt.setData(page.getResult());
        pt.setTotalCount((int) page.getTotal());
        pt.setPageSize(page.getPageSize());
        pt.setPageNo(page.getPageNum());
        return pt;
    }

    @Override
    public InventoryDevice getByDeviceSn(String deviceSn) {
        return inventoryDeviceMapper.getByDeviceSn(deviceSn);
    }

    @Override
    public List<InventoryDevice> listByDeviceSns(List<String> deviceSns) {
        List<InventoryDevice> inventoryDevices = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(deviceSns)) {
            inventoryDevices = inventoryDeviceMapper.listByDeviceSns(deviceSns);
        }
        return inventoryDevices;
    }

    @Override
    public Boolean delete(String deviceSn) {
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        inventoryDeviceMapper.delete(deviceSn);
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchUpdate(List<UpdateWhiteDeviceDTO> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return Boolean.FALSE;
        }
        for (UpdateWhiteDeviceDTO updateWhiteDeviceDTO : dto) {
            BizAssert.notNull(updateWhiteDeviceDTO.getSerialNumber(), "deviceSn must not be null");
        }
        inventoryDeviceMapper.batchUpdate(dto);
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchInsertWithoutCheck(BatchInsertWhiteListDeviceRequest request) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(request.getDeviceInventoryList()), "批量导入列表不能为空");

        // 获取设备SN列表
        List<String> deviceSnList = request.getDeviceInventoryList().stream().map(WhiteListDeviceDTO::getSerialNumber).collect(Collectors.toList());
        Map<String/*deviceSn*/, Integer/*deviceType*/> deviceSnToTypeMap = deviceTypeMapManager.getTypeByDeviceSnList(deviceSnList);
        List<InventoryDevice> inventoryDeviceList = Lists.newArrayList();
        for (WhiteListDeviceDTO whiteListDeviceDTO : request.getDeviceInventoryList()) {
            InventoryDevice inventoryDevice = new InventoryDevice();
            inventoryDevice.setSerialNumber(whiteListDeviceDTO.getSerialNumber());
            inventoryDevice.setMacAddress(whiteListDeviceDTO.getMacAddress());
            inventoryDevice.setMacAddress2(whiteListDeviceDTO.getMacAddress2());
            inventoryDevice.setPrivateKey(whiteListDeviceDTO.getPrivateKey());
            inventoryDevice.setOrgId(whiteListDeviceDTO.getOrgId());
            inventoryDevice.setActivityStatus(YesNoFlag.NO.getValue());
            inventoryDevice.setDeviceType(whiteListDeviceDTO.getDeviceType());
            inventoryDevice.setActivationCode(whiteListDeviceDTO.getActivationCode());
            inventoryDevice.setIsOpenPlat(whiteListDeviceDTO.getOpenPlat());
            inventoryDevice.setDeviceType(whiteListDeviceDTO.getDeviceType() == null ? deviceSnToTypeMap.get(whiteListDeviceDTO.getSerialNumber()) : whiteListDeviceDTO.getDeviceType());
            inventoryDeviceList.add(inventoryDevice);
        }
        inventoryDeviceMapper.batchInsert(inventoryDeviceList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean update(InventoryDevice inventoryDevice) {
        BizAssert.notNull(inventoryDevice.getSerialNumber(), "deviceSn must not be null");
        inventoryDeviceMapper.update(inventoryDevice);
        return Boolean.TRUE;
    }

    @Override
    public Boolean batchInsertWithoutCheck(List<InventoryDevice> inventoryDevices) {
        inventoryDeviceMapper.batchInsert(inventoryDevices);
        return Boolean.TRUE;
    }

    @Override
    public List<SimpleWhiteDeviceResponse> getBatchSimpleDeviceInfo(
        GetWhiteDeviceListRequest request) {
        //获取设备白单数据
        List<String> deviceSns = request.getDeviceSns();
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceSns), "deviceSnList must not be empty");
        List<InventoryDevice> existList = listByDeviceSns(deviceSns);
        if (CollectionUtils.isEmpty(existList)) {
            return Collections.EMPTY_LIST;
        }
        //获取设备版本信息
        List<String> existSnList = existList.stream().map(InventoryDevice::getSerialNumber).collect(
            Collectors.toList());
        List<DeviceVersionInfoDTO> deviceApkVersionResponses = deviceVersionManager.getBatchDeviceApkVersionFromDatabase(existSnList).pickDataThrowException();
        Map<String, DeviceVersionInfoDTO> deviceSnToApkVersionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceApkVersionResponses)) {
            deviceSnToApkVersionMap = deviceApkVersionResponses.stream().collect(Collectors.toMap(DeviceVersionInfoDTO::getDeviceSn, Function
                .identity(), (before, after) -> after));
        }
        List<DeviceVersionInfoDTO> deviceRomVersionResponses = deviceVersionManager.getBatchDeviceRomVersionFromDatabase(existSnList).pickDataThrowException();
        Map<String, DeviceVersionInfoDTO> deviceSnToRomVersionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(deviceRomVersionResponses)) {
            deviceSnToRomVersionMap = deviceRomVersionResponses.stream().collect(Collectors.toMap(DeviceVersionInfoDTO::getDeviceSn, Function.identity(), (before, after) -> after));
        }
        List<SimpleWhiteDeviceResponse> simpleWhiteDeviceResponseList = new ArrayList<>();
        Map<String, String> deviceNameMap = deviceTypeNameHelper.listDeviceNameBySn(deviceSns);
        for (InventoryDevice device : existList) {
            SimpleWhiteDeviceResponse response = new SimpleWhiteDeviceResponse();
            response.setDeviceSn(device.getSerialNumber());
            response.setDeviceType(device.getDeviceType());
            if (device.getOrgId() != null) {
                response.setActivated(Boolean.TRUE);
            }
            if (deviceSnToApkVersionMap.containsKey(device.getSerialNumber())) {
                response.setAppType(deviceSnToApkVersionMap.get(device.getSerialNumber()).getAppType());
                response.setAppVersionCode(deviceSnToApkVersionMap.get(device.getSerialNumber()).getVersionCode());
            }
            if (deviceSnToRomVersionMap.containsKey(device.getSerialNumber())) {
                response.setRomType(deviceSnToRomVersionMap.get(device.getSerialNumber()).getAppType());
                response.setRomVersionCode(deviceSnToRomVersionMap.get(device.getSerialNumber()).getVersionCode());
            }
            response.setDeviceTypeName(deviceNameMap.get(device.getSerialNumber()));
            simpleWhiteDeviceResponseList.add(response);
        }
        return simpleWhiteDeviceResponseList;
    }

    @Override
    public Boolean unbindDevice(InventoryDevice inventoryDevice) {
        BizAssert.notNull(inventoryDevice.getSerialNumber(), "deviceSn must not be null");
        inventoryDeviceMapper.unbindDevice(inventoryDevice);
        return true;
    }
}
