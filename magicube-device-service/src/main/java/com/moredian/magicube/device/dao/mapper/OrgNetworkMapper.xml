<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.OrgNetworkMapper">

    <resultMap type="com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork" id="baseMap">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="networkType" column="network_type"/>
        <result property="connectType" column="connect_type"/>
        <result property="cableStatic" column="cable_static"/>
        <result property="wifiInfo" column="wifi_info"/>
        <result property="url" column="url"/>
        <result property="status" column="status"/>
        <result property="bindOrgUrl" column="bind_org_url"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
        <result property="serverAddress" column="server_address"/>
    </resultMap>

    <sql id="sql_base_column">
        id,
        org_id,
        network_type,
        connect_type,
        cable_static,
        wifi_info,
        url,
        status,
        bind_org_url,
        gmt_create,
        gmt_modify,
        server_address
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork">
        insert into hive_org_network(
        <include refid="sql_base_column"/>
        )values (
        #{id},
        #{orgId},
        #{networkType},
        #{connectType},
        #{cableStatic},
        #{wifiInfo},
        #{url},
        #{status},
        #{bindOrgUrl},
        now(3),
        now(3),
        #{serverAddress}
        )
    </insert>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork">
        update hive_org_network
        <set>
            <if test="networkType != null">
                network_type = #{networkType},
            </if>
            <if test="connectType != null">
                connect_type = #{connectType},
            </if>
            <if test="cableStatic != null">
                cable_static = #{cableStatic},
            </if>
            <if test="wifiInfo != null">
                wifi_info = #{wifiInfo},
            </if>
            <if test="url != null">
                url = #{url},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="bindOrgUrl != null">
                bind_org_url = #{bindOrgUrl},
            </if>
            <if test="serverAddress != null">
                server_address = #{serverAddress},
            </if>
            gmt_modify = now(3)
        </set>
        where org_id = #{orgId}
        and id = #{id}
    </update>

    <select id="getById" resultMap="baseMap">
        select
        <include refid="sql_base_column"/>
        from hive_org_network
        where org_id = #{orgId} and id = #{id}
    </select>

    <select id="listNetworkInfoByOrgId" resultMap="baseMap">
        select
        <include refid="sql_base_column"/>
        from hive_org_network
        where org_id = #{orgId}
        order by network_type desc, connect_type,gmt_modify desc
    </select>

    <select id="listByCondition" parameterType="com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork" resultMap="baseMap">
        select
        <include refid="sql_base_column"/>
        from hive_org_network
        where org_Id = #{orgId}
            <if test="networkType != null">
                and network_type = #{networkType}
            </if>
            <if test="connectType != null">
                and connect_type = #{connectType}
            </if>
            <if test="cableStatic != null">
                and cable_static = #{cableStatic}
            </if>
            <if test="wifiInfo != null">
                and wifi_info = #{wifiInfo}
            </if>
    </select>

    <delete id="deleteByNetworkId" parameterType="long">
        delete from
        hive_org_network
        where org_id = #{orgId}
        and id = #{id}
    </delete>
</mapper>