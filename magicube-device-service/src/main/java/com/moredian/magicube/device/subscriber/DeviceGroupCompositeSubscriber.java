package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.model.msg.DeviceCompositeChangeMsg;
import com.moredian.magicube.common.model.msg.DeviceGroupChangeMsg;
import com.moredian.magicube.device.constant.DeviceCompositeBizType;
import com.moredian.magicube.device.constant.DeviceCompositeChangeMsgSate;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.DeviceComposite;
import com.moredian.magicube.device.dao.entity.DeviceCompositeGroup;
import com.moredian.magicube.device.dao.entity.DeviceCompositeItem;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeGroupMapper;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeItemMapper;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeMapper;
import com.moredian.magicube.device.dto.composite.DeviceCompositeDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositeItemDTO;
import com.moredian.magicube.device.manager.DeviceCompositeManager;
import com.moredian.magicube.device.manager.DeviceGroupCompositeComponent;
import com.moredian.magicube.device.manager.bo.DeviceCompositeRelationBO;
import com.moredian.magicube.device.assembly.service.AssemblyService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/27 16:44
 */

@Slf4j
@Component
public class DeviceGroupCompositeSubscriber {

    @Autowired
    private DeviceCompositeManager deviceCompositeManager;
    @Autowired
    private DeviceCompositeMapper deviceCompositeMapper;
    @Autowired
    private DeviceCompositeItemMapper deviceCompositeItemMapper;
    @Autowired
    private DeviceCompositeGroupMapper deviceCompositeGroupMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private AssemblyService assemblyService;
    @Autowired
    private DeviceGroupCompositeComponent deviceGroupCompositeComponent;


    @Subscribe
    public void subscribeDeviceCompositeChangeMsg(DeviceCompositeChangeMsg msg) {
        log.info("subscribeDeviceCompositeChangeMsg msg={}", JsonUtils.toJson(msg));
        if (!DeviceCompositeBizType.COMMON.equals(msg.getBizType())) {
            log.error("非门禁设备组变更不处理");
            return;
        }

        DeviceCompositeDTO deviceCompositeDto = getDeviceCompositeDto(msg);
        if (deviceCompositeDto != null && DeviceCompositeBizType.COMMON.equals(
            (deviceCompositeDto.getBizType()))) {
            // 组新增消息
            if (DeviceCompositeChangeMsgSate.COMPOSITE_ADD.equals(msg.getChangeState())) {
                List<String> codeList = Arrays.stream(deviceCompositeDto.getPath().split("/"))
                    .collect(Collectors.toList());
                codeList.removeAll(Lists.newArrayList(deviceCompositeDto.getCode()));
                // 获取父组id列表
                List<Long> compositeIdList = getCompositeIdList(msg, codeList);
                // 获取新增的本组下面关联的设备id列表
                List<Long> deviceIdList = getDeviceIdList(msg, deviceCompositeDto);
                handelCompositeAdd(msg, codeList, deviceIdList, compositeIdList);
            } else if (DeviceCompositeChangeMsgSate.COMPOSITE_DELETE.equals(msg.getChangeState())) {
                // 组删除消息
                // 本消息涉及的所有设备组
                List<Long> list = new ArrayList<>();
                List<Long> deviceList = new ArrayList<>();
                Long deviceCompositeId = msg.getDeviceCompositeId();
                list.add(deviceCompositeId);
                // 获取当前组的所有子组
                // 1、找出当前设备组的所有子组
                List<DeviceComposite> composite = Optional.ofNullable(
                    deviceCompositeManager.findAllSubDeviceCompositeWithDelete(
                        msg.getOrgId(), deviceCompositeId)).orElse(new ArrayList<>());
                if (CollectionUtils.isNotEmpty(composite)) {
                    list.addAll(composite.stream()
                        .map(DeviceComposite::getDeviceCompositeId).distinct()
                        .collect(Collectors.toList()));
                }
                // find devices
                List<DeviceCompositeItem> idWithDeleteBatch = deviceCompositeItemMapper.getItemListByCompositeIdWithDeleteBatch(
                        msg.getOrgId(), list);
                if (CollectionUtils.isNotEmpty(idWithDeleteBatch)) {
                    // all sub and self devices
                    deviceList = idWithDeleteBatch.stream().map(
                                    DeviceCompositeItem::getDeviceId)
                            .distinct().collect(
                                    Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(deviceList)) {
                    DeviceGroupChangeMsg deviceGroupChangeMsg = new DeviceGroupChangeMsg();
                    deviceGroupChangeMsg.setOrgId(msg.getOrgId());
                    deviceGroupChangeMsg.setChangeState(msg.getChangeState());
                    deviceGroupChangeMsg.setDeviceIdList(deviceList);

//                    List<String> keyList = deviceList.stream().map(String::valueOf)
//                        .collect(Collectors.toList());
                    // 降噪
                    deviceGroupCompositeComponent.deleteGroupDeviceCompositeRelationByCompositeId(msg.getOrgId(),msg.getDeviceCompositeId());
                    int publish = EventBus.publish(deviceGroupChangeMsg);
//                    Integer publish = assemblyService.publish(deviceGroupChangeMsg,
//                        DeviceGroupChangeMsg.class, msg.getOrgId(), keyList, "deviceIdList");
                    log.info(
                        "[compositeDelete] send subscribeDeviceCompositeChangeMsg,content={},publish-value={}",
                        JsonUtils.toJson(deviceGroupChangeMsg), publish);
                } else {
                    log.info(
                        "[compositeDelete] DeviceCompositeChangeMsg msg no deviceCompositeItem");
                }
            } else if (DeviceCompositeChangeMsgSate.COMPOSITE_UPDATE.equals(msg.getChangeState())) {
                List<Long> deviceIdsOld = new ArrayList<>();
                List<Long> deviceIdsNew = getDeviceIdList(msg, deviceCompositeDto);
                // get data from cache
                String strJson = redisTemplate.opsForValue().get(
                    RedisConstants.getKey(RedisConstants.DEVICE_COMPOSITE_LAST_RELATION,
                        msg.getOrgId(), msg.getDeviceCompositeId()));
                if (StringUtils.isNotBlank(strJson)) {
                    // 老的设备组关联设备情况
                    DeviceCompositeRelationBO relationDto = com.moredian.bee.rmq.utils.JsonUtils.deSerialize(
                        strJson,
                        DeviceCompositeRelationBO.class);
                    deviceIdsOld = Optional.ofNullable(relationDto.getDeviceIds())
                        .orElse(new ArrayList<>());
                }
                // 刷新缓存
                DeviceCompositeRelationBO dto = new DeviceCompositeRelationBO();
                dto.setCompositeId(msg.getDeviceCompositeId());
                dto.setDeviceIds(deviceIdsNew);
                String key = RedisConstants.getKey(RedisConstants.DEVICE_COMPOSITE_LAST_RELATION,
                    msg.getOrgId(), msg.getDeviceCompositeId());
                String value = com.moredian.bee.rmq.utils.JsonUtils.serialize(dto);
                redisTemplate.opsForValue().set(key, value);
                // 缓存7天
                redisTemplate.expire(key, 7, TimeUnit.DAYS);
                log.info("刷新缓存成功key={},value={}", key, value);

                // 新老设备交集
                Set<Long> retainList = new HashSet<>(deviceIdsOld);
                retainList.retainAll(deviceIdsNew);
                // 取出差集
                List<Long> needSyncDeviceIdList = new ArrayList<>();
                deviceIdsNew.removeAll(retainList);
                deviceIdsOld.removeAll(retainList);
                needSyncDeviceIdList.addAll(deviceIdsNew);
                needSyncDeviceIdList.addAll(deviceIdsOld);

                if (CollectionUtils.isNotEmpty(needSyncDeviceIdList)) {
                    DeviceGroupChangeMsg deviceGroupChangeMsg = new DeviceGroupChangeMsg();
                    deviceGroupChangeMsg.setOrgId(msg.getOrgId());
                    deviceGroupChangeMsg.setChangeState(msg.getChangeState());
                    deviceGroupChangeMsg.setDeviceIdList(needSyncDeviceIdList);
//                    List<String> keyList = needSyncDeviceIdList.stream().map(String::valueOf)
//                        .collect(Collectors.toList());
                    deviceGroupCompositeComponent.updateComposite(msg.getOrgId(),msg.getDeviceCompositeId(),deviceIdsOld,deviceIdsNew);
                    int publish = EventBus.publish(deviceGroupChangeMsg);
                    // 降噪
//                    Integer publish = assemblyService.publish(deviceGroupChangeMsg,
//                        DeviceGroupChangeMsg.class, msg.getOrgId(), keyList, "deviceIdList");
                    log.info(
                        "[compositeUpdate] send subscribeDeviceCompositeChangeMsg,content={},publish-value={}",
                        JsonUtils.toJson(deviceGroupChangeMsg), publish);
                } else {
                    log.info(
                        "[compositeDelete] DeviceCompositeChangeMsg msg no change deviceId");
                }
            }
        }
    }

    private DeviceCompositeDTO getDeviceCompositeDto(DeviceCompositeChangeMsg msg) {
        DeviceCompositeDTO deviceCompositeDto;
        if (DeviceCompositeChangeMsgSate.COMPOSITE_DELETE.equals(msg.getChangeState())) {
            DeviceComposite deviceComposite = deviceCompositeManager.getCompositeByIdWithDelete(
                msg.getOrgId(), msg.getDeviceCompositeId());
            deviceCompositeDto = com.moredian.bee.common.utils.BeanUtils.copyProperties(
                DeviceCompositeDTO.class, deviceComposite);
        } else {
            deviceCompositeDto = deviceCompositeManager.getCompositeInfo(msg.getOrgId(),
                msg.getDeviceCompositeId(), false);
        }
        return deviceCompositeDto;
    }

    private void handelCompositeAdd(DeviceCompositeChangeMsg msg,
        List<String> codeList,
        List<Long> deviceIdList,
        List<Long> compositeIdList) {
        // 父类没有绑定通信权限组,不发送消息，因为新建的组还没绑定通行组
        if (CollectionUtils.isEmpty(compositeIdList)) {
            log.info("[compositeAdd]DeviceCompositeChangeMsg msg no parent group bind ,code={}",
                JsonUtils.toJson(codeList));
        } else {
            List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByDeviceCompositeIdList(
                msg.getOrgId(), compositeIdList);
            // 就算父组中的设备包含了当前的设备，那么也不好感知，哪些父组包含了，所以直接发设备同步
            if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
                // 关系处理
                deviceGroupCompositeComponent.deviceCompositeAdd(msg.getOrgId(), msg.getDeviceCompositeId(),deviceIdList);
                DeviceGroupChangeMsg deviceGroupChangeMsg = new DeviceGroupChangeMsg();
                deviceGroupChangeMsg.setOrgId(msg.getOrgId());
                deviceGroupChangeMsg.setChangeState(msg.getChangeState());
                deviceGroupChangeMsg.setDeviceIdList(deviceIdList);

                int publish = EventBus.publish(deviceGroupChangeMsg);

//                List<String> keyList = deviceIdList.stream().map(String::valueOf)
//                    .collect(Collectors.toList());
                // 降噪
//                Integer publish = assemblyService.publish(deviceGroupChangeMsg,
//                    DeviceGroupChangeMsg.class, msg.getOrgId(), keyList, "deviceIdList");
                log.info("[compositeAdd] send DeviceGroupChangeMsg,content={},publish-value={}",
                    JsonUtils.toJson(deviceGroupChangeMsg), publish);
            } else {
                log.info("[compositeAdd]DeviceCompositeChangeMsg msg no device ,compositeIdList={}",
                    JsonUtils.toJson(compositeIdList));
            }
        }
    }

    private List<Long> getCompositeIdList(DeviceCompositeChangeMsg msg, List<String> codeList) {
        List<DeviceComposite> deviceCompositeList = deviceCompositeMapper.getCompositeByCodeList(
            msg.getOrgId(), codeList, DeviceCompositeBizType.COMMON);
        return deviceCompositeList.stream().map(DeviceComposite::getDeviceCompositeId).distinct()
            .collect(Collectors.toList());
    }

    private List<Long> getDeviceIdList(DeviceCompositeChangeMsg msg,
        DeviceCompositeDTO deviceCompositeDto) {
        List<DeviceCompositeItemDTO> deviceList = deviceCompositeManager.
            getCompositeItemListByCompositeIds(msg.getOrgId(),
                Lists.newArrayList(deviceCompositeDto.getDeviceCompositeId()));
        return deviceList.stream().map(DeviceCompositeItemDTO::getDeviceId).distinct()
            .collect(Collectors.toList());
    }


}
