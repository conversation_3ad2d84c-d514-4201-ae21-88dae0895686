package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description：设备类型配置前端展示样式
 * @date ：2024/07/25 9:52
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_type_property_config")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceTypePropertyConfig extends TimedEntity {

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @TableField("device_type")
    private Integer deviceType;

    @TableField("property_key")
    private String propertyKey;

    @TableField("property_json")
    private String propertyJson;

    @TableField("sort")
    private Integer sort;

    @TableField("visible")
    private Boolean visible;

    @TableLogic(delval = "1")
    private Boolean delFlag;

}
