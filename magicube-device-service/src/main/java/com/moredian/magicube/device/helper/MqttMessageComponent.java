package com.moredian.magicube.device.helper;

import com.google.gson.Gson;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.device.dto.mqtt.TransferMessageInfoDTO;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description mqtt消息发送组件
 * @Date 2023/7/4
 */
@Component
@Slf4j
public class MqttMessageComponent {

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    /**
     * 同步发送流程
     *
     * @param deviceSn
     * @param eventType
     * @param data
     * @param messageInfo
     * @return 发送结果
     */
    public Boolean sendMqttMessage(String deviceSn, Integer eventType, Object data, String messageInfo) {
        return doSendMqttMsg(deviceSn, eventType, data, messageInfo);
    }

    private Boolean doSendMqttMsg(String deviceSn, Integer eventType, Object data, String messageInfo) {
        Gson gson = new Gson();
        TransferMessageInfoDTO<String> stringTransferMessageInfo = buildTransferMessageWithEventType(eventType, data, messageInfo);
        String jsonStr = gson.toJson(stringTransferMessageInfo);
        String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
        TransferRequest transferRequest = buildTransferRequest(deviceSn, base64Message);
        ServiceResponse<TransferResponse> responseServiceResponse;

        try {
            responseServiceResponse = hubControlServiceV1.transfer(transferRequest);
        } catch (Exception e) {
            log.info("-->>发送MQTTMessage 服务错误-->>enventType={},deviceSn={},data={},result={}", eventType,
                    deviceSn, data == null ? null : data.toString(), e.toString());
            return false;
        }

        if (responseServiceResponse != null && responseServiceResponse.isSuccess() && responseServiceResponse.isExistData()) {
            log.info("-->>发送MQTTMessage 成功-->>enventType={},deviceSn={},data={},result={}", eventType,
                    deviceSn, data == null ? null : data.toString(), JsonUtils.toJson(responseServiceResponse.getData()));
            return true;
        } else {
            log.info("-->>发送MQTTMessage 失败-->>enventType={},deviceSn={},data={},result={}", eventType,
                    deviceSn, data == null ? null : data.toString(), JsonUtils.toJson(responseServiceResponse.getErrorContext().getMessage()));
            return false;
        }
    }

    private TransferRequest buildTransferRequest(String sn, String messageInfo) {
        TransferRequest transferRequest = new TransferRequest();
        transferRequest.setSerialNumber(sn);
        transferRequest.setBody(messageInfo);
        //默认为true
        transferRequest.setForce(true);
        //默认为0
        transferRequest.setCommand(0);
        return transferRequest;
    }

    private static TransferMessageInfoDTO buildTransferMessageWithEventType(Integer eventType, Object data, String messageInfo) {
        TransferMessageInfoDTO transferMessageInfo = new TransferMessageInfoDTO();
        transferMessageInfo.setEventType(eventType);
        transferMessageInfo.setSeverity(3);
        transferMessageInfo.setSeqId(UUID.randomUUID().toString() + messageInfo);
        transferMessageInfo.setMessage(messageInfo);
        transferMessageInfo.setData(data);
        return transferMessageInfo;
    }


}
