package com.moredian.magicube.device.manager.impl;

import com.alibaba.fastjson.JSON;

import com.moredian.bee.common.exception.BizAssert;

import com.moredian.bee.filemanager.FileManager;
import com.moredian.bee.filemanager.enums.FilePathType;
import com.moredian.bee.filemanager.model.SaveFileResponse;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.DirectionEnum;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;

import com.moredian.magicube.device.config.FunctionQrCodeConstants;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.qrcode.FunctionQrCode;
import com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork;
import com.moredian.magicube.device.dao.mapper.FunctionQrCodeDao;
import com.moredian.magicube.device.dao.mapper.OrgNetworkMapper;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeSimpleDto;
import com.moredian.magicube.device.dto.qrcode.AleadyActivateOrgDto;
import com.moredian.magicube.device.dto.qrcode.BindPersonnelRelationDto;
import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.InsertNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.PrivateCloud;
import com.moredian.magicube.device.dto.qrcode.SimpleActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.UpdateNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.UploadNetworkCodeStatusDTO;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant.AppletEnum;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant.ConnectTypeEnum;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant.NetworkTypeEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.MqttMessageComponent;
import com.moredian.magicube.device.manager.FunctionQrCodeManager;
import com.moredian.magicube.device.subscriber.TransferEventType;
import com.moredian.magicube.device.utils.qrcode.QrCodeByteEncryptionUtils;
import com.moredian.magicube.device.utils.qrcode.QrCodeByteUtil;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceTreeService;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Classname： QrCodeManagerImpl
 * @Date: 2023/1/4 1:59 下午
 * @Author: _AF
 * @Description:
 */
@Service
@Slf4j
public class FunctionQrCodeManagerImpl implements FunctionQrCodeManager {

    @Autowired
    private FileManager fileManager;
    @Autowired
    private FunctionQrCodeDao functionQrCodeDao;
    @Autowired
    private OrgNetworkMapper orgNetworkMapper;
    @Autowired
    private RedissonCacheComponent redissonCacheComponent;
    @SI
    private IdgeneratorService idgeneratorService;
    @SI
    private OrgService orgService;
    @SI
    private SpaceTreeService treeService;

    @Autowired
    private MqttMessageComponent mqttMessageComponent;

    @Value("${bee.filemanager.imageServerRootUrl:}")
    private String imageServerRootUrl;

    @Value("${qr.code.prefix:moredianr:}")
    private String qrCodePrefix;

    @Value("${gic.host:https://global.moredian.com/}")
    private String GicHost;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 设备重新配网状态 1-网络切换中 2-网络切换成功 3-网络切换失败
     */
    private final static int DEVICE_RESET_NETWORK_WAIT = 1;
    private final static int DEVICE_RESET_NETWORK_SUCCESS = 2;
    private final static int DEVICE_RESET_NETWORK_FAIL = 3;
    //秘钥信息
    byte[] key = {0x6e,(byte) 0xeb,(byte) 0xc6,0x6f,0x7e,0x3b,0x4e,0x43,(byte) 0x97,0x4b,0x43,(byte) 0x9f,0x52,(byte) 0x98,0x69,(byte) 0xb0};
    byte[] iv = {(byte)0xd8,(byte)0xba,0x31,(byte)0xef,0x05,0x74,0x43,0x38,(byte)0xb1,0x38,0x0d,0x7a,(byte)0xd0,0x61,0x7a,(byte)0xca};

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Long createActivateQrCode(ActivateQrCodeCreateDto dto) {
        //验参
        log.info("FunctionQrCodeManagerImpl.createQrCode param {}", JSON.toJSONString(dto));
        checkCreateActivateParam(dto);

        long orgNetworkId;
        if (dto.getOrgNetworkId() == null) {
            InsertNetworkInfoDTO insertNetworkInfoDTO = new InsertNetworkInfoDTO();
            BeanUtils.copyProperties(dto,insertNetworkInfoDTO);
            orgNetworkId = insertNetworkInfo(insertNetworkInfoDTO);
        } else {
            UpdateNetworkInfoDTO updateNetworkInfoDTO = new UpdateNetworkInfoDTO();
            updateNetworkInfoDTO.setId(dto.getOrgNetworkId());
            updateNetworkInfoDTO.setOrgId(dto.getOrgId());
            updateNetworkInfoDTO.setConnectType(dto.getConnectType());
            updateNetworkInfoDTO.setNetworkType(dto.getNetworkType());
            updateNetworkInfoDTO.setCableStatic(dto.getCableStatic());
            updateNetworkInfoDTO.setWifiInfo(dto.getWifiInfo());
            updateNetworkInfo(updateNetworkInfoDTO);
            orgNetworkId = dto.getOrgNetworkId();
        }

        //激活码对象数据初始化
        FunctionQrCode functionQrCode = new FunctionQrCode();
        functionQrCode.setId(idgeneratorService.getNextIdByTypeName(FunctionQrCode.class.getName()).pickDataThrowException());

        //创建二维码
        byte[] imageBytes = buildActivateBuffer(dto, functionQrCode);
        SaveFileResponse saveFileResponse = fileManager.saveFile(imageBytes, FilePathType.TYPE_SUBJECT, System.currentTimeMillis() + ".jpg").pickDataThrowException();
        //数据存储
        functionQrCode.setOrgId(dto.getOrgId());
        functionQrCode.setQrCodeName(dto.getQrCodeName());
        functionQrCode.setMemberId(dto.getMemberId());
        functionQrCode.setTreeId(dto.getPropertyTreeNodeId() == null ? dto.getTreeId() : dto.getPropertyTreeNodeId());
        functionQrCode.setContent(FunctionQrCodeConstants.DEFAULT_CONTENT);
        functionQrCode.setDeviceAddressName(dto.getDeviceAddressName());
        functionQrCode.setNetworkType(dto.getNetworkType());
        functionQrCode.setConnectType(dto.getConnectType());
        functionQrCode.setPrivateCloudSwitch(dto.getPrivateCloudSwitch());
        functionQrCode.setCableStatic(dto.getCableStatic() == null ? null : JSON.toJSONString(dto.getCableStatic()));
        functionQrCode.setWifiInfo(dto.getWifiInfo() == null ? null : JSON.toJSONString(dto.getWifiInfo()));
        functionQrCode.setPrivateCloud(dto.getPrivateCloud() == null ? null : JSON.toJSONString(dto.getPrivateCloud()));
        functionQrCode.setUrl(saveFileResponse.getRelativePath());
        functionQrCode.setFunctionType(dto.getFunctionType());
        functionQrCode.setRoleType(dto.getRoleType());
        functionQrCode.setSource(dto.getSource());
        functionQrCode.setOrgNetworkId(orgNetworkId);
        functionQrCode.setDirection(dto.getDirection());

        if (dto.getTreeId() != null) {
            TreeDTO treeDTO = treeService.getByIdSupportCollaboration(dto.getTreeId(),
                dto.getOrgId()).pickDataThrowException();
            if (treeDTO != null) {
                functionQrCode.setTreeName(treeDTO.getTreeName());
                functionQrCode.setPathTreeName(treeDTO.getJoinTreePathTreeName());
            }
        }
        functionQrCodeDao.insert(functionQrCode);
        return functionQrCode.getId();
    }

    @Override
    public ActivateQrCodeDto getActivateQrCodeById(Long memberId, Long id) {
        BizAssert.notNull(memberId, "accountId不能为空");
        BizAssert.notNull(id, "id不能为空");
        FunctionQrCode functionQrCode = functionQrCodeDao.getById(memberId, id);
        BizAssert.notNull(functionQrCode, "激活码不存在");
        ActivateQrCodeDto activateQrCodeDto = new ActivateQrCodeDto();
        activateQrCodeDto.setId(id);
        activateQrCodeDto.setOrgId(functionQrCode.getOrgId());
        activateQrCodeDto.setQrCodeName(functionQrCode.getQrCodeName());
        activateQrCodeDto.setAccountId(functionQrCode.getMemberId());
        activateQrCodeDto.setDeviceAddressName(functionQrCode.getDeviceAddressName());
        activateQrCodeDto.setNetworkType(functionQrCode.getNetworkType());
        activateQrCodeDto.setConnectType(functionQrCode.getConnectType());
        activateQrCodeDto.setPrivateCloudSwitch(functionQrCode.getPrivateCloudSwitch());
        activateQrCodeDto.setCableStatic(functionQrCode.getCableStatic() == null ? null : JSON.parseObject(functionQrCode.getCableStatic(), CableStatic.class));
        activateQrCodeDto.setWifiInfo(functionQrCode.getWifiInfo() == null ? null : JSON.parseObject(functionQrCode.getWifiInfo(), WifiInfo.class));
        activateQrCodeDto.setPrivateCloud(functionQrCode.getPrivateCloud() == null ? null : JSON.parseObject(functionQrCode.getPrivateCloud(), PrivateCloud.class));
        activateQrCodeDto.setUrl(imageServerRootUrl + functionQrCode.getUrl());
        activateQrCodeDto.setFunctionType(functionQrCode.getFunctionType());
        activateQrCodeDto.setTreeId(functionQrCode.getTreeId());
        //兼容私有化激活码 无空间id的情况
        if (functionQrCode.getTreeId() != null) {
            TreeDTO treeDTO = treeService.getByIdSupportCollaboration(functionQrCode.getTreeId(),
                functionQrCode.getOrgId()).pickDataThrowException();
            if (treeDTO != null) {
                activateQrCodeDto.setPropertyTreeName(treeDTO.getPropertyTreeName());
            }
        }
        if (StringUtils.isNotBlank(functionQrCode.getTreeName())) {
            activateQrCodeDto.setTreeName(functionQrCode.getTreeName());
        }
        if (StringUtils.isNotBlank(functionQrCode.getPathTreeName())) {
            activateQrCodeDto.setPathTreeName(functionQrCode.getPathTreeName());
        }
        activateQrCodeDto.setOrgNetworkId(functionQrCode.getOrgNetworkId());
        if (functionQrCode.getDirection() != null) {
            activateQrCodeDto.setDirection(functionQrCode.getDirection());
            activateQrCodeDto.setDirectionName(DirectionEnum.getDirectionName(functionQrCode.getDirection()));
        }
        return activateQrCodeDto;
    }

    @Override
    public ActivateQrCodeDto getActivateQrCodeByOrgIdAndId(Long orgId, Long id) {
        BizAssert.notNull(orgId, "orgId不能为空");
        BizAssert.notNull(id, "id不能为空");
        FunctionQrCode functionQrCode = functionQrCodeDao.getActivateQrCodeById(id);
        BizAssert.notNull(functionQrCode, "激活码不存在");
        ActivateQrCodeDto activateQrCodeDto = new ActivateQrCodeDto();
        activateQrCodeDto.setId(id);
        activateQrCodeDto.setOrgId(functionQrCode.getOrgId());
        activateQrCodeDto.setQrCodeName(functionQrCode.getQrCodeName());
        activateQrCodeDto.setAccountId(functionQrCode.getMemberId());
        activateQrCodeDto.setDeviceAddressName(functionQrCode.getDeviceAddressName());
        activateQrCodeDto.setNetworkType(functionQrCode.getNetworkType());
        activateQrCodeDto.setConnectType(functionQrCode.getConnectType());
        activateQrCodeDto.setPrivateCloudSwitch(functionQrCode.getPrivateCloudSwitch());
        activateQrCodeDto.setCableStatic(functionQrCode.getCableStatic() == null ? null
            : JSON.parseObject(functionQrCode.getCableStatic(), CableStatic.class));
        activateQrCodeDto.setWifiInfo(functionQrCode.getWifiInfo() == null ? null
            : JSON.parseObject(functionQrCode.getWifiInfo(), WifiInfo.class));
        activateQrCodeDto.setPrivateCloud(functionQrCode.getPrivateCloud() == null ? null
            : JSON.parseObject(functionQrCode.getPrivateCloud(), PrivateCloud.class));
        activateQrCodeDto.setUrl(imageServerRootUrl + functionQrCode.getUrl());
        activateQrCodeDto.setFunctionType(functionQrCode.getFunctionType());
        activateQrCodeDto.setTreeId(functionQrCode.getTreeId());
        //返回激活码保存的空间树快照信息
        activateQrCodeDto.setTreeName(functionQrCode.getTreeName());
        activateQrCodeDto.setPathTreeName(functionQrCode.getPathTreeName());
        activateQrCodeDto.setOrgNetworkId(functionQrCode.getOrgNetworkId());
        return activateQrCodeDto;
    }

    @Override
    public List<ActivateQrCodeSimpleDto> listByMemberId(Long memberId, Integer roleType, Integer source) {
        List<ActivateQrCodeSimpleDto> activateQrCodeSimpleDtoList = new ArrayList<>();
        List<FunctionQrCode> functionQrCodeList = functionQrCodeDao.listByMemberId(memberId,
                Lists.newArrayList(ActivateQrCodeConstant.FunctionTypeEnum.ACTIVATE.getType(),
                        ActivateQrCodeConstant.FunctionTypeEnum.PRIVATIZATION.getType()),
                roleType, source);
        if (CollectionUtils.isNotEmpty(functionQrCodeList)) {
            for (FunctionQrCode functionQrCode : functionQrCodeList) {
                ActivateQrCodeSimpleDto activateQrCodeSimpleDto = new ActivateQrCodeSimpleDto();
                activateQrCodeSimpleDto.setId(functionQrCode.getId());
                activateQrCodeSimpleDto.setOrgId(functionQrCode.getOrgId());
                activateQrCodeSimpleDto.setQrCodeName(functionQrCode.getQrCodeName());
                activateQrCodeSimpleDto.setAccountId(functionQrCode.getMemberId());
                activateQrCodeSimpleDto.setDeviceAddressName(functionQrCode.getDeviceAddressName());
                activateQrCodeSimpleDto.setGmtCreate(functionQrCode.getNewActivateTime());
                activateQrCodeSimpleDto.setFunctionType(functionQrCode.getFunctionType());
                activateQrCodeSimpleDto.setPrivateCloud(functionQrCode.getPrivateCloud() == null ? null : JSON.parseObject(functionQrCode.getPrivateCloud(), PrivateCloud.class));
                activateQrCodeSimpleDtoList.add(activateQrCodeSimpleDto);
            }
        }
        return activateQrCodeSimpleDtoList;
    }

    @Override
    public List<ActivateQrCodeSimpleDto> listHistoryOrgByMemberId(
        Long memberId, Integer roleType, Integer source) {
        List<ActivateQrCodeSimpleDto> activateQrCodeSimpleDtoList = new ArrayList<>();
        List<FunctionQrCode> functionQrCodeList = functionQrCodeDao.listByMemberId(memberId,
                Lists.newArrayList(ActivateQrCodeConstant.FunctionTypeEnum.ACTIVATE.getType(),
                        ActivateQrCodeConstant.FunctionTypeEnum.PRIVATIZATION.getType()),
                roleType, source);
        if (CollectionUtils.isNotEmpty(functionQrCodeList)) {
            //过滤掉已解散的机构id
            Set<Long> orgIdSet = functionQrCodeList.stream().map(FunctionQrCode::getOrgId).collect(Collectors.toSet());
            List<Long> orgIdList = new ArrayList<>(orgIdSet);
            List<OrgInfo> orgInfoList = orgService.findOrgInfoList(orgIdList, Lists.newArrayList(OrgStatus.USABLE.getValue())).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(orgInfoList)) {
                Set<Long> checkOrgRepeatSet = new HashSet<>();
                List<Long> usableOrgIdList = orgInfoList.stream().map(OrgInfo::getOrgId).collect(Collectors.toList());
                for (FunctionQrCode functionQrCode : functionQrCodeList) {
                    if (usableOrgIdList.contains(functionQrCode.getOrgId()) && !checkOrgRepeatSet.contains(functionQrCode.getOrgId())) {
                        ActivateQrCodeSimpleDto activateQrCodeSimpleDto = new ActivateQrCodeSimpleDto();
                        activateQrCodeSimpleDto.setId(functionQrCode.getId());
                        activateQrCodeSimpleDto.setOrgId(functionQrCode.getOrgId());
                        activateQrCodeSimpleDto.setQrCodeName(functionQrCode.getQrCodeName());
                        activateQrCodeSimpleDto.setAccountId(functionQrCode.getMemberId());
                        activateQrCodeSimpleDto.setDeviceAddressName(functionQrCode.getDeviceAddressName());
                        activateQrCodeSimpleDto.setGmtCreate(functionQrCode.getNewActivateTime());
                        activateQrCodeSimpleDto.setFunctionType(functionQrCode.getFunctionType());
                        activateQrCodeSimpleDto.setPrivateCloud(functionQrCode.getPrivateCloud() == null ? null : JSON.parseObject(functionQrCode.getPrivateCloud(), PrivateCloud.class));
                        activateQrCodeSimpleDtoList.add(activateQrCodeSimpleDto);
                        checkOrgRepeatSet.add(functionQrCode.getOrgId());
                    }
                }
            }
        }
        return activateQrCodeSimpleDtoList;
    }

    @Override
    public ActivateQrCodeSimpleDto getByIdAndRoleType(Long id, Integer roleType) {
        BizAssert.notNull(id);
        //BizAssert.isTrue(ActivateQrCodeConstant.RoleTypeEnum.checkType(roleType));
        FunctionQrCode functionQrCode = functionQrCodeDao.getByIdAndRoleType(id, roleType);
        ActivateQrCodeSimpleDto activateQrCodeSimpleDto = new ActivateQrCodeSimpleDto();
        if (functionQrCode != null) {
            BeanUtils.copyProperties(functionQrCode, activateQrCodeSimpleDto);
        }
        return activateQrCodeSimpleDto;
    }

    @Override
    public void updateNewActivateTime(Long id, Date newActivateTime) {
        BizAssert.notNull(newActivateTime);
        functionQrCodeDao.updateNewActivateTime(id, newActivateTime);
    }

    @Override
    public List<AleadyActivateOrgDto> listByAleadyActivateOrg(Long accountId) {
        BizAssert.notNull(accountId);
        List<AleadyActivateOrgDto> aleadyActivateOrgDtoList = new ArrayList<>();
        List<FunctionQrCode> functionQrCodeList = functionQrCodeDao.listByMemberId(accountId,
                Arrays.asList(ActivateQrCodeConstant.FunctionTypeEnum.ACTIVATE.getType()),
                ActivateQrCodeConstant.RoleTypeEnum.WORKER.getType(),
                ActivateQrCodeConstant.AppletEnum.ACTIVATE_ASSISTANT.getType());
        if (CollectionUtils.isNotEmpty(functionQrCodeList)) {
            List<Long> orgIdList = functionQrCodeList.stream().map(e -> e.getOrgId()).distinct().collect(
                Collectors.toList());
            List<OrgInfo> orgInfoList = orgService.findOrgInfoList(orgIdList, Lists.newArrayList(OrgStatus.USABLE.getValue())).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(orgInfoList)) {
                Map<Long, OrgInfo> orgInfoMap = orgInfoList.stream().collect(
                    Collectors.toMap(OrgInfo::getOrgId, Function.identity()));
                orgInfoList.forEach(e -> {
                    OrgInfo orgInfo = orgInfoMap.get(e.getOrgId());
                    if (orgInfo != null) {
                        AleadyActivateOrgDto aleadyActivateOrgDto = new AleadyActivateOrgDto();
                        aleadyActivateOrgDto.setOrgId(orgInfo.getOrgId());
                        aleadyActivateOrgDto.setOrgName(orgInfo.getOrgName());
                        aleadyActivateOrgDtoList.add(aleadyActivateOrgDto);
                    }
                });
            }
        }
        return aleadyActivateOrgDtoList;
    }

    @Override
    public Integer getPersonnelRelation(Long accountId) {
        BizAssert.notNull(accountId);
        String key = RedisKeys
                .getKey(RedisKeys.ACTIVATE_QR_CODE_ACCOUNT, accountId);
        String value = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)) {
            return YesNoFlag.NO.getValue();
        }
        return Integer.valueOf(value);
    }

    @Override
    public Boolean bindPersonnelRelation(BindPersonnelRelationDto dto) {
        BizAssert.notNull(dto.getMemberId());
        BizAssert.notNull(dto.getPersonnelValue());
        String key = RedisKeys
                .getKey(RedisKeys.ACTIVATE_QR_CODE_ACCOUNT, dto.getMemberId());
        redisTemplate.opsForValue().set(key, String.valueOf(dto.getPersonnelValue()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNetworkQrCode(NetworkQrCodeCreateDto dto) {
        String key = RedisConstants
            .getKey(RedisConstants.DEVICE_RESET_NETWORK_STATUS,dto.getOrgId(),dto.getDeviceSn());
        if (dto.getResetNetworkType() == 1) {
            Integer status = (Integer) redissonCacheComponent.getObjectCache(key);
            if (status != null && DEVICE_RESET_NETWORK_WAIT == status){
                BizAssert.notNull(null,DeviceErrorCode.DEVICE_RESET_NETWORK_EXIST,DeviceErrorCode.DEVICE_RESET_NETWORK_EXIST.getMessage());
            }
        }
        //参数校验
        checkCreateNetworkParam(dto);
        long networkId = idgeneratorService.getNextIdByTypeName(OrgNetwork.class.getName())
            .pickDataThrowException();
        dto.setId(networkId);
        //创建流对象
        byte[] imageBytes = buildNetworkBuffer(dto);
        SaveFileResponse saveFileResponse = fileManager.saveFile(imageBytes, FilePathType.TYPE_SUBJECT, System.currentTimeMillis() + ".jpg").pickDataThrowException();
        InsertNetworkInfoDTO insertNetworkInfoDTO = new InsertNetworkInfoDTO();
        insertNetworkInfoDTO.setUrl(saveFileResponse.getRelativePath());
        BeanUtils.copyProperties(dto, insertNetworkInfoDTO);
        Long id = insertNetworkInfo(insertNetworkInfoDTO);
        if (dto.getResetNetworkType() == 1){
            BizAssert.notNull(dto.getDeviceSn(),"deviceSn must not be null");
            try {
                mqttMessageComponent.sendMqttMessage(dto.getDeviceSn(), TransferEventType.RESET_NETWORK.getEventType(),insertNetworkInfoDTO,"RESET_NETWORK");
            } catch (Exception e) {
                log.error("重新配网异常：{}", e.getMessage());
                BizAssert.notNull(null,DeviceErrorCode.DEVICE_RESET_NETWORK_FAIL,DeviceErrorCode.DEVICE_RESET_NETWORK_FAIL.getMessage());
            }
            redissonCacheComponent.setObjectCache(key, DEVICE_RESET_NETWORK_WAIT, RedisConstants.DEVICE_RESET_NETWORK_TIME_OUT);
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertNetworkInfo(InsertNetworkInfoDTO dto) {
        long id;
        if (dto.getId() == null) {
            id = idgeneratorService.getNextIdByTypeName(OrgNetwork.class.getName())
                .pickDataThrowException();
        }else {
            id = dto.getId();
        }
        OrgNetwork condition = new OrgNetwork();
        condition.setOrgId(dto.getOrgId());
        condition.setConnectType(dto.getConnectType());
        condition.setNetworkType(dto.getNetworkType());
        List<OrgNetwork> orgNetworks = orgNetworkMapper.listByCondition(condition);
        boolean addFlag = Boolean.TRUE;
        OrgNetwork orgNetwork = new OrgNetwork();
        if (CollectionUtils.isNotEmpty(orgNetworks)){
            if (NetworkTypeEnum.WIRED.getType().equals(dto.getNetworkType())) {
                if (ConnectTypeEnum.DHCP.getType().equals(dto.getConnectType())){
                    //有线DHCP判断是否存在相同的 有则更新时间，没有就新增一条
                    if (dto.getCableStatic() != null && dto.getCableStatic().getDns() != null) {
                        for (OrgNetwork exist : orgNetworks) {
                            CableStatic existCableStatic = StringUtils.isBlank(exist.getCableStatic()) ? null : JSON.parseObject(exist.getCableStatic(), CableStatic.class);
                            if (existCableStatic != null && existCableStatic.getDns() != null && existCableStatic.getDns()
                                .equals(dto.getCableStatic().getDns())) {
                                orgNetwork = exist;
                                addFlag = Boolean.FALSE;
                                break;
                            }
                        }
                    } else {
                        for (OrgNetwork exist : orgNetworks) {
                            CableStatic existCableStatic = StringUtils.isBlank(exist.getCableStatic()) ? null : JSON.parseObject(exist.getCableStatic(), CableStatic.class);
                            if (existCableStatic == null || existCableStatic.getDns() == null) {
                                orgNetwork = exist;
                                addFlag = Boolean.FALSE;
                                break;
                            }
                        }
                    }
                } else if (ConnectTypeEnum.STATIC_CONFIG.getType().equals(dto.getConnectType())) {
                    CableStatic cableStatic = dto.getCableStatic();
                    if (cableStatic != null) {
                        for (OrgNetwork exist : orgNetworks) {
                            CableStatic cs = StringUtils.isBlank(exist.getCableStatic()) ? null : JSON.parseObject(exist.getCableStatic(), CableStatic.class);
                            if (cs != null && cs.getIp().equals(cableStatic.getIp())
                                && cs.getGateway().equals(cableStatic.getGateway())
                                && cs.getMask().equals(cableStatic.getMask())
                                && cs.getDns().equals(cableStatic.getDns())){
                                orgNetwork = exist;
                                addFlag = Boolean.FALSE;
                                break;
                            }
                        }
                    }
                }
            } else if (NetworkTypeEnum.WIRELESS.getType().equals(dto.getNetworkType())) {
                CableStatic cableStatic = dto.getCableStatic();
                WifiInfo wifiInfo = dto.getWifiInfo();
                if (wifiInfo != null){
                    for (OrgNetwork exist : orgNetworks) {
                        CableStatic cs = StringUtils.isBlank(exist.getCableStatic()) ? null
                            : JSON.parseObject(exist.getCableStatic(), CableStatic.class);
                        WifiInfo wi = StringUtils.isBlank(exist.getWifiInfo()) ? null
                            : JSON.parseObject(exist.getWifiInfo(), WifiInfo.class);
                        if (cs == null || cableStatic == null) {
                            if (wi != null && wi.getPassword().equals(wifiInfo.getPassword())
                                && wi.getName().equals(wifiInfo.getName())) {
                                orgNetwork = exist;
                                addFlag = Boolean.FALSE;
                                break;
                            }
                        } else {
                            if (wi != null && wi.getPassword().equals(wifiInfo.getPassword()) && wi
                                .getName().equals(wifiInfo.getName()) && cableStatic.getDns()
                                .equals(cs.getDns())) {
                                orgNetwork = exist;
                                addFlag = Boolean.FALSE;
                                break;
                            }
                        }
                    }
                }
            }
        }
        if (StringUtils.isBlank(dto.getUrl())||!addFlag) {
            NetworkQrCodeCreateDto networkQrCodeCreateDto = new NetworkQrCodeCreateDto();
            BeanUtils.copyProperties(dto, networkQrCodeCreateDto);
            if (addFlag) {
                networkQrCodeCreateDto.setId(id);
            } else {
                networkQrCodeCreateDto.setId(orgNetwork.getId());
            }
            byte[] imageBytes = buildNetworkBuffer(networkQrCodeCreateDto);
            SaveFileResponse saveFileResponse = fileManager
                    .saveFile(imageBytes, FilePathType.TYPE_SUBJECT, System.currentTimeMillis() + ".jpg")
                    .pickDataThrowException();
            dto.setUrl(saveFileResponse.getRelativePath());
        }
        if (!addFlag){
            Long oldId = orgNetwork.getId();
            orgNetworkMapper.deleteByNetworkId(orgNetwork.getOrgId(), orgNetwork.getId());
            BeanUtils.copyProperties(dto,orgNetwork);
            orgNetwork.setCableStatic(dto.getCableStatic() == null ? null : JSON.toJSONString(dto.getCableStatic()));
            orgNetwork.setWifiInfo(dto.getWifiInfo() == null ? null : JSON.toJSONString(dto.getWifiInfo()));
            orgNetwork.setId(oldId);
            orgNetwork.setStatus(YesNoFlag.NO.getValue());
            orgNetwork.setBindOrgUrl("");
            orgNetworkMapper.insert(orgNetwork);
            return oldId;
        }
        BeanUtils.copyProperties(dto,orgNetwork);
        orgNetwork.setCableStatic(dto.getCableStatic() == null ? null : JSON.toJSONString(dto.getCableStatic()));
        orgNetwork.setWifiInfo(dto.getWifiInfo() == null ? null : JSON.toJSONString(dto.getWifiInfo()));
        orgNetwork.setId(id);
        orgNetwork.setStatus(YesNoFlag.NO.getValue());
        orgNetwork.setBindOrgUrl("");
        orgNetworkMapper.insert(orgNetwork);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateNetworkInfo(UpdateNetworkInfoDTO dto) {
        BizAssert.notNull(dto.getId(), "id must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        OrgNetwork condition = new OrgNetwork();
        condition.setOrgId(dto.getOrgId());
        condition.setConnectType(dto.getConnectType());
        condition.setNetworkType(dto.getNetworkType());
        List<OrgNetwork> orgNetworks = orgNetworkMapper.listByCondition(condition);
        boolean existFlag = Boolean.FALSE;
        OrgNetwork existingNetwork = null;
        if (CollectionUtils.isNotEmpty(orgNetworks)){
            //过滤掉需要修改的数据
            orgNetworks = orgNetworks.stream().filter(orgNetwork -> !orgNetwork.getId().equals(dto.getId())).collect(Collectors.toList());
            if (NetworkTypeEnum.WIRED.getType().equals(dto.getNetworkType())) {
                if (ConnectTypeEnum.DHCP.getType().equals(dto.getConnectType())) {
                    if (CollectionUtils.isNotEmpty(orgNetworks)) {
                        existFlag = Boolean.TRUE;
                        existingNetwork = orgNetworks.get(0);
                        existingNetwork.setCableStatic("");
                        existingNetwork.setWifiInfo("");
                    }
                } else if (ConnectTypeEnum.STATIC_CONFIG.getType().equals(dto.getConnectType())) {
                    CableStatic cableStatic = dto.getCableStatic();
                    BizAssert.notNull(cableStatic, "CableStatic must not be null");
                    for (OrgNetwork exist : orgNetworks) {
                        CableStatic cs = StringUtils.isBlank(exist.getCableStatic()) ? null : JSON.parseObject(exist.getCableStatic(), CableStatic.class);
                        if (cs != null && cs.getIp().equals(cableStatic.getIp())
                                && cs.getGateway().equals(cableStatic.getGateway())
                                && cs.getMask().equals(cableStatic.getMask())
                                && cs.getDns().equals(cableStatic.getDns())) {
                            existFlag = Boolean.TRUE;
                            existingNetwork = exist;
                            existingNetwork.setWifiInfo("");
                            break;
                        }
                    }
                }
            } else if (NetworkTypeEnum.WIRELESS.getType().equals(dto.getNetworkType())) {
                WifiInfo wifiInfo = dto.getWifiInfo();
                BizAssert.notNull(wifiInfo, "wifiInfo must not be null");
                for (OrgNetwork exist : orgNetworks) {
                    WifiInfo wi = StringUtils.isBlank(exist.getWifiInfo()) ? null
                            : JSON.parseObject(exist.getWifiInfo(), WifiInfo.class);
                    if (wi != null && wi.getPassword().equals(wifiInfo.getPassword()) && wi
                            .getName().equals(wifiInfo.getName())) {
                        existingNetwork = exist;
                        existingNetwork.setCableStatic("");
                        existFlag = Boolean.TRUE;
                        break;
                    }

                }
            }
        }
        if (existFlag && existingNetwork != null) {
            orgNetworkMapper.deleteByNetworkId(existingNetwork.getOrgId(), existingNetwork.getId());
        }
        OrgNetwork updateOrgNetWork = new OrgNetwork();
        BeanUtils.copyProperties(dto, updateOrgNetWork);
        updateOrgNetWork.setCableStatic(dto.getCableStatic() == null ? null : JSON.toJSONString(dto.getCableStatic()));
        updateOrgNetWork.setWifiInfo(dto.getWifiInfo() == null ? null : JSON.toJSONString(dto.getWifiInfo()));
        orgNetworkMapper.update(updateOrgNetWork);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadNetworkCodeStatus(UploadNetworkCodeStatusDTO dto) {
        BizAssert.notNull(dto.getOrgNetworkId(), "orgNetworkId must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        OrgNetwork orgNetwork = orgNetworkMapper.getById(dto.getOrgId(), dto.getOrgNetworkId());
        BizAssert.notNull(orgNetwork, "orgNetwork must not be null");
        orgNetwork.setStatus(dto.getStatus());
        orgNetwork.setBindOrgUrl(dto.getBindOrgUrl());
        orgNetworkMapper.update(orgNetwork);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByNetworkId(Long orgId, Long id) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(id, "id must not be null");
        orgNetworkMapper.deleteByNetworkId(orgId, id);
        return Boolean.TRUE;
    }

    @Override
    public NetworkQrCodeDto getNetworkQrCodeById(Long orgId, Long id) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(id, "id must not be null");
        OrgNetwork orgNetwork = orgNetworkMapper.getById(orgId, id);
        if (orgNetwork == null){
            return null;
        }
        NetworkQrCodeDto networkQrCodeDto = new NetworkQrCodeDto();
        networkQrCodeDto.setNetworkType(orgNetwork.getNetworkType());
        networkQrCodeDto.setConnectType(orgNetwork.getConnectType());
        networkQrCodeDto.setCableStatic(orgNetwork.getCableStatic() == null ? null : JSON.parseObject(orgNetwork.getCableStatic(), CableStatic.class));
        networkQrCodeDto.setWifiInfo(orgNetwork.getWifiInfo() == null ? null : JSON.parseObject(orgNetwork.getWifiInfo(), WifiInfo.class));
        networkQrCodeDto.setUrl(imageServerRootUrl + orgNetwork.getUrl());
        networkQrCodeDto.setBindOrgUrl(orgNetwork.getBindOrgUrl());
        networkQrCodeDto.setStatus(orgNetwork.getStatus());
        networkQrCodeDto.setServerAddress(orgNetwork.getServerAddress());
        return networkQrCodeDto;
    }

    @Override
    public List<OrgNetwork> listNetworkInfoByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        return orgNetworkMapper.listNetworkInfoByOrgId(orgId);
    }

    @Override
    public NetworkQrCodeDto getNetWorkInfoByQrCodeId(Long qrCodeId) {
        BizAssert.notNull(qrCodeId, "qrCodeId must not be null");
        FunctionQrCode functionQrCode = functionQrCodeDao.getActivateQrCodeById(qrCodeId);
        NetworkQrCodeDto networkQrCodeDto = null;
        if (functionQrCode != null) {
            networkQrCodeDto = new NetworkQrCodeDto();
            networkQrCodeDto.setOrgId(functionQrCode.getOrgId());
            networkQrCodeDto.setDeviceAddressName(functionQrCode.getDeviceAddressName());
            networkQrCodeDto.setNetworkType(functionQrCode.getNetworkType());
            networkQrCodeDto.setConnectType(functionQrCode.getConnectType());
            networkQrCodeDto.setPrivateCloudSwitch(functionQrCode.getPrivateCloudSwitch());
            networkQrCodeDto.setCableStatic(functionQrCode.getCableStatic() == null ? null : JSON.parseObject(functionQrCode.getCableStatic(), CableStatic.class));
            networkQrCodeDto.setWifiInfo(functionQrCode.getWifiInfo() == null ? null : JSON.parseObject(functionQrCode.getWifiInfo(), WifiInfo.class));
            networkQrCodeDto.setPrivateCloud(functionQrCode.getPrivateCloud() == null ? null : JSON.parseObject(functionQrCode.getPrivateCloud(), PrivateCloud.class));
            networkQrCodeDto.setTreeId(functionQrCode.getTreeId());
            networkQrCodeDto.setDirection(functionQrCode.getDirection());
        }
        return networkQrCodeDto;
    }

    @Override
    public SimpleActivateQrCodeDto getSimpleQrCodeByQrCodeId(Long qrCodeId) {
        FunctionQrCode exist = functionQrCodeDao.getActivateQrCodeById(qrCodeId);
        BizAssert.notNull(exist, DeviceErrorCode.ACTIVATE_QR_CODE_NOT_EXIST, DeviceErrorCode.ACTIVATE_QR_CODE_NOT_EXIST.getMessage());
        SimpleActivateQrCodeDto dto = new SimpleActivateQrCodeDto();
        dto.setId(exist.getId());
        dto.setOrgId(exist.getOrgId());
        dto.setDeviceAddressName(exist.getDeviceAddressName());
        dto.setGmtCreate(exist.getGmtCreate());
        dto.setQrCodeName(exist.getQrCodeName());
        dto.setSource(exist.getSource());
        dto.setRoleType(exist.getRoleType());
        dto.setMemberId(exist.getMemberId());
        return dto;
    }

    /**
     * 创建配网二维码参数校验
     *
     * @param dto
     */
    private void checkCreateNetworkParam(NetworkQrCodeCreateDto dto) {
        BizAssert.notNull(dto.getOrgId());
        BizAssert.notNull(dto.getResetNetworkType(), "resetNetworkType must not be null");
//        BizAssert.notNull(dto.getMemberId());
//        BizAssert.notBlank(dto.getDeviceSn());
//        BizAssert.notBlank(dto.getDeviceAddressName());
        BizAssert.isTrue(ActivateQrCodeConstant.NetworkTypeEnum.checkType(dto.getNetworkType()));
        BizAssert.isTrue(ActivateQrCodeConstant.ConnectTypeEnum.checkType(dto.getConnectType()));
//        BizAssert.isTrue(ActivateQrCodeConstant.RoleTypeEnum.checkType(dto.getRoleType()));
//        BizAssert.isTrue(ActivateQrCodeConstant.AppletEnum.checkType(dto.getSource()), "network qrCode source error");
        WifiInfo wifiInfo = dto.getWifiInfo();
        if (wifiInfo != null) {
            String name = wifiInfo.getName();
            String password = wifiInfo.getPassword();
            BizAssert.isTrue(name.getBytes().length < 60, DeviceErrorCode.WIFI_NAME_TOO_LONG,
                DeviceErrorCode.WIFI_NAME_TOO_LONG.getMessage());
            BizAssert
                .isTrue(password.getBytes().length < 60, DeviceErrorCode.WIFI_PASSWORD_TOO_LONG,
                    DeviceErrorCode.WIFI_PASSWORD_TOO_LONG.getMessage());
        }

        if (Objects
            .equals(ActivateQrCodeConstant.NetworkTypeEnum.WIRED.getType(), dto.getNetworkType())) {
            //有线
            if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.STATIC_CONFIG.getType(),
                dto.getConnectType())) {
                BizAssert.notNull(dto.getCableStatic());
                BizAssert.notBlank(dto.getCableStatic().getIp());
                BizAssert.notBlank(dto.getCableStatic().getGateway());
                BizAssert.notBlank(dto.getCableStatic().getMask());
                BizAssert.notBlank(dto.getCableStatic().getDns());
            } else if (Objects
                .equals(ActivateQrCodeConstant.ConnectTypeEnum.DHCP.getType(), dto.getConnectType())
                && Objects
                .equals(YesNoFlag.YES.getValue(), dto.getPrivateCloudSwitch())) {
                BizAssert.notNull(dto.getPrivateCloud());
                BizAssert.notBlank(dto.getPrivateCloud().getIp());
            }
        } else {
            //无线
            BizAssert.notNull(dto.getWifiInfo());
            BizAssert.notBlank(dto.getWifiInfo().getName());
        }
    }


    /**
     * 创建激活二维码参数校验
     *
     * @param dto
     */
    private void checkCreateActivateParam(ActivateQrCodeCreateDto dto) {
//        BizAssert.notNull(dto.getMemberId());
//        BizAssert.notBlank(dto.getDeviceAddressName());
        BizAssert.notNull(dto.getFunctionType());
        if (Objects.equals(dto.getFunctionType(),
            ActivateQrCodeConstant.FunctionTypeEnum.PRIVATIZATION.getType())) {
            BizAssert.isTrue(dto.getDeviceAddressName().getBytes().length <= 100, DeviceErrorCode.DEVICE_POSITION_TOO_LONG,
                DeviceErrorCode.DEVICE_POSITION_TOO_LONG.getMessage());
            dto.setQrCodeName(ActivateQrCodeConstant.FunctionTypeEnum.PRIVATIZATION.getDesc());
            dto.setOrgId(0L);
        } else {
            BizAssert.notNull(dto.getOrgId());
            BizAssert.notNull(dto.getQrCodeName());
        }

        //兼容钉私有化
        if (Objects.equals(dto.getFunctionType(),
                ActivateQrCodeConstant.FunctionTypeEnum.DING_PRIVATIZATION.getType())) {
            BizAssert.isTrue(dto.getPrivateCloud() != null && StringUtils
                    .isNotBlank(dto.getPrivateCloud().getIp()), "ip must not be null");
        }

        BizAssert.isTrue(ActivateQrCodeConstant.NetworkTypeEnum.checkType(dto.getNetworkType()));
        BizAssert.isTrue(ActivateQrCodeConstant.ConnectTypeEnum.checkType(dto.getConnectType()));
        BizAssert.isTrue(ActivateQrCodeConstant.RoleTypeEnum.checkType(dto.getRoleType()));
        BizAssert.isTrue(ActivateQrCodeConstant.AppletEnum.checkType(dto.getSource()),
            "activate qrCode source error");
        WifiInfo wifiInfo = dto.getWifiInfo();
        if (wifiInfo != null) {
            String name = wifiInfo.getName();
            String password = wifiInfo.getName();
            BizAssert.isTrue(name.getBytes().length < 60, DeviceErrorCode.WIFI_NAME_TOO_LONG,
                DeviceErrorCode.WIFI_NAME_TOO_LONG.getMessage());
            BizAssert
                .isTrue(password.getBytes().length < 60, DeviceErrorCode.WIFI_PASSWORD_TOO_LONG,
                    DeviceErrorCode.WIFI_PASSWORD_TOO_LONG.getMessage());
        }
        if (Objects
            .equals(ActivateQrCodeConstant.NetworkTypeEnum.WIRED.getType(), dto.getNetworkType())) {
            //有线
            if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.STATIC_CONFIG.getType(),
                dto.getConnectType())) {
                BizAssert.notNull(dto.getCableStatic());
                BizAssert.notBlank(dto.getCableStatic().getIp());
                BizAssert.notBlank(dto.getCableStatic().getGateway());
                BizAssert.notBlank(dto.getCableStatic().getMask());
                BizAssert.notBlank(dto.getCableStatic().getDns());
            } else if (Objects
                .equals(ActivateQrCodeConstant.ConnectTypeEnum.DHCP.getType(), dto.getConnectType())
                && Objects
                .equals(YesNoFlag.YES.getValue(), dto.getPrivateCloudSwitch())) {
                BizAssert.notNull(dto.getPrivateCloud());
                BizAssert.notBlank(dto.getPrivateCloud().getIp());
            }
        } else {
            //无线
            BizAssert.notNull(dto.getWifiInfo());
            BizAssert.notBlank(dto.getWifiInfo().getName());
        }
    }


    /**
     * 创建配网码流对象
     * @param dto
     */
    private byte[] buildNetworkBuffer(NetworkQrCodeCreateDto dto) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(FunctionQrCodeConstants.DEFAULT_BUFFER_CAPACITY);
        byteBuffer.put((byte) 0x5a);
        byteBuffer.put((byte) 0xa5);

        /*long type8 = getType(FunctionQrCodeConstants.FF_08);
        byteBuffer.putShort((short) type8);
        byteBuffer.put((byte) (8 + dto.getDeviceSn().getBytes().length));
        byteBuffer.putLong(dto.getOrgId());
        byteBuffer.put(dto.getDeviceSn().getBytes());
*/
        //添加数据
        if (Objects.equals(ActivateQrCodeConstant.NetworkTypeEnum.WIRED.getType(), dto.getNetworkType())) {
            //有线
            if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.STATIC_CONFIG.getType(), dto.getConnectType())) {
                //FF01
                long type1 = getType(FunctionQrCodeConstants.FF_01);
                byteBuffer.putShort((short) type1);
                disopseIp(byteBuffer, dto.getCableStatic().getIp() + "." + dto.getCableStatic().getGateway() + "."
                        + dto.getCableStatic().getMask() + "." + dto.getCableStatic().getDns());
            } else if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.DHCP.getType(), dto.getConnectType())) {
                //FF02,当dhcp时，给FF02 01 01
                long type2 = getType(FunctionQrCodeConstants.FF_O2);
                byteBuffer.putShort((short) type2);
                byteBuffer.put((byte) 1);
                byteBuffer.put((byte) 1);
            }
        } else {
            //无线 FF03 FF04
            long type3 = getType(FunctionQrCodeConstants.FF_03);
            String name = dto.getWifiInfo().getName();
            byteBuffer.putShort((short) type3);
            byteBuffer.put((byte) name.getBytes().length);
            byteBuffer.put(name.getBytes());

            long type4 = getType(FunctionQrCodeConstants.FF_04);
            if (StringUtils.isNotBlank(dto.getWifiInfo().getPassword())) {
                String password = dto.getWifiInfo().getPassword();
                byteBuffer.putShort((short) type4);
                byteBuffer.put((byte) password.getBytes().length);
                byteBuffer.put(password.getBytes());
            } else {
                byteBuffer.putShort((short) type4);
                byteBuffer.put((byte) 0);
            }
        }

        /*//FF05
        long type5 = getType(FunctionQrCodeConstants.FF_05);
        String deviceAddressName = dto.getDeviceAddressName();
        byteBuffer.putShort((short) type5);
        byteBuffer.put((byte) deviceAddressName.getBytes().length);
        byteBuffer.put(deviceAddressName.getBytes());*/

        //FF05
        long type6 = getType(FunctionQrCodeConstants.FF_0C);
        byteBuffer.putShort((short) type6);
        byteBuffer.put((byte) 8);
        byteBuffer.putLong(dto.getId());

        //FF0D 机构ID
        if (dto.getOrgId() != null) {
            //FF0D
            long type7 = getType(FunctionQrCodeConstants.FF_0D);
            byteBuffer.putShort((short) type7);
            byteBuffer.put((byte) 8);
            byteBuffer.putLong(dto.getOrgId());
        }

        // FF09私有化IP地址
        if (StringUtils.isNotEmpty(dto.getServerAddress())){
            long type_FF09 = getType(FunctionQrCodeConstants.FF_09);
            byteBuffer.putShort((short) type_FF09);
            disopseIp(byteBuffer, dto.getServerAddress());
        }

        //来源
        long type14 = getType(FunctionQrCodeConstants.FF_0E);
        Integer platformCode = dto.getPlatformCode();
        if(Objects.nonNull(platformCode)) {
            byteBuffer.putShort((short) type14);
            byteBuffer.put((byte) 4);
            byteBuffer.putInt(platformCode.byteValue());
        }

        // 二维码来来是哪个小程序
        Integer source = dto.getSource();
        if (Objects.nonNull(source) && source.equals(AppletEnum.SMART_OFFICE.getType())) {
            long type15 = getType(FunctionQrCodeConstants.FF_0F);
            byteBuffer.putShort((short) type15);
            byteBuffer.put((byte) 4);
            byteBuffer.putInt(source.byteValue());
        }

        //创建激活码
        int len = byteBuffer.position();
        byteBuffer.rewind();

        byte[] bytes = new byte[len];

        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = byteBuffer.get();
        }
        log.debug("配网码信息：{}",bytes.toString());
        //bytes加密
        byte[] encryptzeropadding = QrCodeByteEncryptionUtils.encryptzeropadding(bytes, key, iv);

        //合并byte
        byte[] newByte = new byte[qrCodePrefix.getBytes().length + encryptzeropadding.length];
        System.arraycopy(qrCodePrefix.getBytes(), 0, newByte, 0, qrCodePrefix.getBytes().length);
        System.arraycopy(encryptzeropadding, 0, newByte, FunctionQrCodeConstants.RELEASE_TYPE.getBytes().length, encryptzeropadding.length);

        //生成二维码
        byte[] imageBytes = QrCodeByteUtil.encodeQRCode(newByte);
        return imageBytes;
    }

    /**
     * 创建机构网络信息流对象
     * @param dto
     */
    private byte[] buildOrgNetworkBuffer(InsertNetworkInfoDTO dto) {

        ByteBuffer byteBuffer = ByteBuffer.allocate(FunctionQrCodeConstants.DEFAULT_BUFFER_CAPACITY);
        byteBuffer.put((byte) 0x5a);
        byteBuffer.put((byte) 0xa5);

        long type8 = getType(FunctionQrCodeConstants.FF_08);
        byteBuffer.putShort((short) type8);
        byteBuffer.putLong(dto.getOrgId());

        //添加数据
        if (Objects.equals(ActivateQrCodeConstant.NetworkTypeEnum.WIRED.getType(), dto.getNetworkType())) {
            //有线
            if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.STATIC_CONFIG.getType(), dto.getConnectType())) {
                //FF01
                long type1 = getType(FunctionQrCodeConstants.FF_01);
                byteBuffer.putShort((short) type1);
                disopseIp(byteBuffer, dto.getCableStatic().getIp() + "." + dto.getCableStatic().getGateway() + "."
                    + dto.getCableStatic().getMask() + "." + dto.getCableStatic().getDns());
            } else if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.DHCP.getType(), dto.getConnectType())) {
                //FF02,当dhcp时，给FF02 01 01
                long type2 = getType(FunctionQrCodeConstants.FF_O2);
                byteBuffer.putShort((short) type2);
                byteBuffer.put((byte) 1);
                byteBuffer.put((byte) 1);
            }
        } else {
            //无线 FF03 FF04
            long type3 = getType(FunctionQrCodeConstants.FF_03);
            String name = dto.getWifiInfo().getName();
            byteBuffer.putShort((short) type3);
            byteBuffer.put((byte) name.getBytes().length);
            byteBuffer.put(name.getBytes());

            long type4 = getType(FunctionQrCodeConstants.FF_04);
            if (StringUtils.isNotBlank(dto.getWifiInfo().getPassword())) {
                String password = dto.getWifiInfo().getPassword();
                byteBuffer.putShort((short) type4);
                byteBuffer.put((byte) password.getBytes().length);
                byteBuffer.put(password.getBytes());
            } else {
                byteBuffer.putShort((short) type4);
                byteBuffer.put((byte) 0);
            }
        }

        //FF05
        long type5 = getType(FunctionQrCodeConstants.FF_05);
        String deviceAddressName = dto.getDeviceAddressName();
        byteBuffer.putShort((short) type5);
        byteBuffer.put((byte) deviceAddressName.getBytes().length);
        byteBuffer.put(deviceAddressName.getBytes());


        //创建配网码
        int len = byteBuffer.position();
        byteBuffer.rewind();

        byte[] bytes = new byte[len];

        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = byteBuffer.get();
        }
        //bytes加密
        byte[] encryptzeropadding = QrCodeByteEncryptionUtils.encryptzeropadding(bytes, key, iv);

        //合并byte
        byte[] newByte = new byte[qrCodePrefix.getBytes().length + encryptzeropadding.length];
        System.arraycopy(qrCodePrefix.getBytes(), 0, newByte, 0, qrCodePrefix.getBytes().length);
        System.arraycopy(encryptzeropadding, 0, newByte, FunctionQrCodeConstants.RELEASE_TYPE.getBytes().length, encryptzeropadding.length);

        //生成二维码
        byte[] imageBytes = QrCodeByteUtil.encodeQRCode(newByte);
        return imageBytes;
    }

    /**
     * @param dto
     */
    private byte[] buildActivateBuffer(ActivateQrCodeCreateDto dto, FunctionQrCode functionQrCode) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(FunctionQrCodeConstants.DEFAULT_BUFFER_CAPACITY);
        byteBuffer.put((byte) 0x5a);
        byteBuffer.put((byte) 0xa5);
        //添加数据
        //FF0A 私有化DNS
        if (dto.getCableStatic() != null && dto.getCableStatic().getDns() != null) {
            long type10 = getType(FunctionQrCodeConstants.FF_0A);
            byteBuffer.putShort((short) type10);
            byteBuffer.put((byte)4);
            String[] segmentArr = StringUtils.split(dto.getCableStatic().getDns(), '.');
            for (String segment : segmentArr) {
                int segInt = Integer.parseInt(segment);
                byteBuffer.put((byte) segInt);
            }
        }

        //FF0B 全局服务域名地址(余杭移动特殊字段)
        /*long type11 = getType(FunctionQrCodeConstants.FF_0B);
        byteBuffer.putShort((short) type11);
        byteBuffer.put((byte) GicHost.getBytes().length);
        byteBuffer.put(GicHost.getBytes());*/


        if (Objects.equals(ActivateQrCodeConstant.NetworkTypeEnum.WIRED.getType(), dto.getNetworkType())) {
            //有线
            if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.STATIC_CONFIG.getType(), dto.getConnectType())) {
                //FF01
                long type1 = getType(FunctionQrCodeConstants.FF_01);
                byteBuffer.putShort((short) type1);
                disopseIp(byteBuffer, dto.getCableStatic().getIp() + "." + dto.getCableStatic().getGateway() + "."
                        + dto.getCableStatic().getMask() + "." + dto.getCableStatic().getDns());
            } else if (Objects.equals(ActivateQrCodeConstant.ConnectTypeEnum.DHCP.getType(), dto.getConnectType())) {
                    //FF02,当dhcp时，给FF02 01 01
                    long type2 = getType(FunctionQrCodeConstants.FF_O2);
                    byteBuffer.putShort((short) type2);
                    byteBuffer.put((byte) 1);
                    byteBuffer.put((byte) 1);
            }
        } else {
            //无线 FF03 FF04
            long type3 = getType(FunctionQrCodeConstants.FF_03);
            String name = dto.getWifiInfo().getName();
            byteBuffer.putShort((short) type3);
            byteBuffer.put((byte) name.getBytes().length);
            byteBuffer.put(name.getBytes());

            long type4 = getType(FunctionQrCodeConstants.FF_04);
            if (StringUtils.isNotBlank(dto.getWifiInfo().getPassword())) {
                String password = dto.getWifiInfo().getPassword();
                byteBuffer.putShort((short) type4);
                byteBuffer.put((byte) password.getBytes().length);
                byteBuffer.put(password.getBytes());
            } else {
                byteBuffer.putShort((short) type4);
                byteBuffer.put((byte) 0);
            }
        }

        //FF06 删除，通过激活Id服务端自己获取
        /*long type6 = getType(FunctionQrCodeConstants.FF_06);
        byteBuffer.putShort((short) type6);
        //长度
        byteBuffer.put((byte) 24);
        //时间戳
        Date nowDateStr = new Date();
        byteBuffer.putLong(nowDateStr.getTime() + FunctionQrCodeConstants.DEFUALT_TIME);
        //机构id
        byteBuffer.putLong(dto.getOrgId());
        //人员id
        byteBuffer.putLong(dto.getMemberId());*/

        //FF07,激活码id
        if (!Objects.equals(YesNoFlag.YES.getValue(), dto.getPrivateCloudSwitch())){
            long type7 = getType(FunctionQrCodeConstants.FF_07);
            byteBuffer.putShort((short) type7);
            byteBuffer.put((byte) 8);
            byteBuffer.putLong(functionQrCode.getId());
        }

        //FF09 私有化
        if (Objects.equals(YesNoFlag.YES.getValue(), dto.getPrivateCloudSwitch())) {
            //FF05
            long type5 = getType(FunctionQrCodeConstants.FF_05);
            String deviceAddressName = dto.getDeviceAddressName();
            byteBuffer.putShort((short) type5);
            byteBuffer.put((byte) deviceAddressName.getBytes().length);
            byteBuffer.put(deviceAddressName.getBytes());

            long type9 = getType(FunctionQrCodeConstants.FF_09);
            byteBuffer.putShort((short) type9);
            disopseIp(byteBuffer, dto.getPrivateCloud().getIp());
        }

        //FF09 钉私有化
        if (Objects.equals(dto.getFunctionType(),
                ActivateQrCodeConstant.FunctionTypeEnum.DING_PRIVATIZATION.getType())) {
            long type9 = getType(FunctionQrCodeConstants.FF_09);
            byteBuffer.putShort((short) type9);
            disopseIp(byteBuffer, dto.getPrivateCloud().getIp());
        }

        //来源
        long type14 = getType(FunctionQrCodeConstants.FF_0E);
        Integer platformCode = dto.getPlatformCode();
        if(Objects.nonNull(platformCode)) {
            byteBuffer.putShort((short) type14);
            byteBuffer.put((byte) 4);
            byteBuffer.putInt(platformCode.byteValue());
        }

        // 二维码来来是哪个小程序
        Integer source = dto.getSource();
        if (Objects.nonNull(source) && source.equals(AppletEnum.SMART_OFFICE.getType())) {
            long type15 = getType(FunctionQrCodeConstants.FF_0F);
            byteBuffer.putShort((short) type15);
            byteBuffer.put((byte) 4);
            byteBuffer.putInt(source.byteValue());
        }

        //创建激活码
        int len = byteBuffer.position();
        byteBuffer.rewind();

        byte[] bytes = new byte[len];


        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = byteBuffer.get();
        }
        //bytes加密
        byte[] encryptzeropadding = QrCodeByteEncryptionUtils.encryptzeropadding(bytes, key, iv);

        //合并byte
        byte[] newByte = new byte[qrCodePrefix.getBytes().length + encryptzeropadding.length];
        System.arraycopy(qrCodePrefix.getBytes(), 0, newByte, 0, qrCodePrefix.getBytes().length);
        System.arraycopy(encryptzeropadding, 0, newByte,FunctionQrCodeConstants.RELEASE_TYPE.getBytes().length, encryptzeropadding.length);

        //生成二维码
        byte[] imageBytes = QrCodeByteUtil.encodeQRCode(newByte);
        return imageBytes;
    }

    /**
     * 获取获取类别
     *
     * @param strIp
     * @return
     */
    public static long getType(String strIp) {
        long[] ip = new long[2];

        try {
            //先找出字符串中点的位置
            int position1 = strIp.indexOf(".");
            //将点分隔的字符串转换为整数
            ip[0] = Long.parseLong(strIp.substring(0, position1));
            ip[1] = Long.parseLong(strIp.substring(position1 + 1));

            return (ip[0] << 8) + (ip[1]);

        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 处理ip地址相关
     *
     * @param strIp
     * @return
     */
    public void disopseIp(ByteBuffer byteBuffer, String strIp) {
        strIp = strIp.replace("255", "255.0");
        String[] segmentArr = StringUtils.split(strIp, '.');
        byteBuffer.put((byte) segmentArr.length);
        for (String segment : segmentArr) {
            int segInt = Integer.parseInt(segment);
            byteBuffer.put((byte) segInt);
        }
    }
}
