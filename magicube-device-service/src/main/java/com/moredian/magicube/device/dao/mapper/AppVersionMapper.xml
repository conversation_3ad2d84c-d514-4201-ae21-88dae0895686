<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.AppVersionMapper">
    <resultMap id="AppVersionResultMap" type="com.moredian.magicube.device.dao.entity.AppVersion">
        <result column="version_id" property="versionId"/>
        <result column="system_type" property="systemType"/>
        <result column="app_type" property="appType"/>
        <result column="version_code" property="versionCode"/>
        <result column="version_name" property="versionName"/>
        <result column="version_desc" property="versionDesc"/>
        <result column="app_url" property="appUrl"/>
        <result column="is_enforce_update" property="isEnforceUpdate"/>
        <result column="is_active" property="isActive"/>
        <result column="is_del_flag" property="isDelFlag"/>
        <result column="user_create" property="userCreate"/>
        <result column="enforcement_upgrade" property="enforcementUpgrade"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <insert id="addAppVersion" parameterType="com.moredian.magicube.device.dao.entity.AppVersion">
        insert into
		hive_app_version
        (
            version_id,
            system_type,
            app_type,
            version_code,
            version_name,
            version_desc,
            app_url,
            is_enforce_update,
            is_active,
            is_del_flag,
            user_create,
            enforcement_upgrade,
            gmt_create,
            gmt_modify	
        ) values (
            #{versionId},
            #{systemType},
            #{appType},
            #{versionCode},
            #{versionName},
            #{versionDesc},
            #{appUrl},
            #{isEnforceUpdate},
            #{isActive},
            #{isDelFlag},
            #{userCreate},
            #{enforcementUpgrade},
            now(3),
            now(3)
        )
    </insert>

    <update id="updateAppVersion" parameterType="com.moredian.magicube.device.dao.entity.AppVersion">
        update
            hive_app_version
        set
            system_type = #{systemType},
            app_type = #{appType},
            version_code = #{versionCode},
            version_name = #{versionName},
            version_desc = #{versionDesc},
            app_url = #{appUrl},
            is_enforce_update = #{isEnforceUpdate},
            is_active = #{isActive},
            is_del_flag = #{isDelFlag},
            user_create = #{userCreate},
            enforcement_upgrade = #{enforcementUpgrade},
            gmt_modify = now(3)
        where
            version_id=#{versionId}
    </update>

    <update id="updateAppVersionSelective" parameterType="com.moredian.magicube.device.dao.entity.AppVersion">
        update
        hive_app_version
        <set>
            <if test="systemType != null">
                system_type=#{systemType},
            </if>
            <if test="appType != null">
                app_type=#{appType},
            </if>
            <if test="versionCode != null">
                version_code=#{versionCode},
            </if>
            <if test="versionName != null">
                version_name=#{versionName},
            </if>
            <if test="versionDesc != null">
                version_desc=#{versionDesc},
            </if>
            <if test="appUrl != null">
                app_url=#{appUrl},
            </if>
            <if test="isEnforceUpdate != null">
                is_enforce_update=#{isEnforceUpdate},
            </if>
            <if test="isActive != null">
                is_active=#{isActive},
            </if>
            <if test="isDelFlag != null">
                is_del_flag=#{isDelFlag},
            </if>
            <if test="userCreate != null">
                user_create=#{userCreate},
            </if>
            <if test="enforcementUpgrade != null">
                enforcement_upgrade = #{enforcementUpgrade},
            </if>
            gmt_modify = now(3)
        </set>
        where
        version_id = #{versionId}
    </update>

    <select id="getAppVersionById" parameterType="long" resultMap="AppVersionResultMap">
        <include refid="sql_select"/>
        from
        hive_app_version
        where
        version_id = #{versionId}
    </select>

    <select id="getAppVersionBySCTypeCode" parameterType="com.moredian.magicube.device.dao.entity.AppVersion"
            resultMap="AppVersionResultMap">
        <include refid="sql_select"/>
        from
        hive_app_version
        where
        is_del_flag=0
        <if test="systemType != null">
            and system_type = #{systemType}
        </if>
        <if test="appType != null">
            and app_type = #{appType}
        </if>
        <if test="versionCode != null">
            and version_code = #{versionCode}
        </if>
    </select>

    <select id="getNewAppVersionBySCType" parameterType="com.moredian.magicube.device.dao.entity.AppVersion"
            resultMap="AppVersionResultMap">
        <include refid="sql_select"/>
        from
        hive_app_version
        where
        version_id =
        (SELECT MAX(version_id)
        FROM hive_app_version
        WHERE system_type = #{systemType} AND app_type = #{appType} AND is_del_flag = 0
        AND version_code =
        (SELECT MAX(version_code)
        FROM hive_app_version
        WHERE system_type = #{systemType} AND app_type = #{appType} AND is_del_flag = 0))
    </select>

    <select id="getNewAppVersionBySCTypeAndIsActive" parameterType="com.moredian.magicube.device.dao.entity.AppVersion"
            resultMap="AppVersionResultMap">
        <include refid="sql_select"/>
        from
        hive_app_version
        where
        version_id =
        (SELECT MAX(version_id)
        FROM hive_app_version
        WHERE system_type = #{systemType} AND app_type = #{appType} AND is_del_flag = 0 AND is_active = 1
        AND version_code =
        (SELECT MAX(version_code)
        FROM hive_app_version
        WHERE system_type = #{systemType} AND app_type = #{appType} AND is_del_flag = 0 AND is_active = 1))
    </select>

    <select id="getAppVersionByIdForUpdate" parameterType="long" resultMap="AppVersionResultMap">
        <include refid="sql_select"/>
        from
        hive_app_version
        where
        version_id = #{versionId}
        for update
    </select>

    <delete id="removeAppVersionById" parameterType="long">
        delete from
            hive_app_version
        where
            version_id = #{versionId}
 	</delete>

    <select id="getPaginationAppVersion" resultMap="AppVersionResultMap" parameterType="map">
        <include refid="sql_select"/>
        <include refid="sql_where"/>
        order by
        gmt_modify desc
        limit #{startRow}, #{pageSize}
    </select>

    <select id="getAppVersionCount" resultType="int" parameterType="com.moredian.magicube.device.dao.entity.AppVersion">
        select count(*)
        <include refid="sql_where"/>
    </select>

    <select id="getEnforceUpdateAppVersionCount" resultType="int"
            parameterType="com.moredian.magicube.device.dao.entity.AppVersion">
        select
        count(*)
        from
        hive_app_version
        where
        is_del_flag=0
        and is_active=1
        and is_enforce_update = 1
        <if test="systemType != null">
            and system_type = #{systemType}
        </if>
        <if test="appType != null">
            and app_type = #{appType}
        </if>
        <if test="versionCode != null">
            and version_code > #{versionCode}
        </if>
    </select>

    <select id="getLowVersionCount" resultType="int">
        select
        count(*)
        from
        hive_app_version
        where
        is_del_flag=0
        and is_active=1
        and system_type=1
        <if test="appType != null">
            and app_type = #{appType}
        </if>
        <if test="versionCode != null">
            and version_code > #{versionCode}
        </if>
    </select>

    <select id="getAppVersionInfo" resultMap="AppVersionResultMap"
            parameterType="com.moredian.magicube.device.dao.entity.AppVersion">
        <include refid="sql_select"/>
        <include refid="sql_where"/>
    </select>

    <select id="list" resultMap="AppVersionResultMap">
        <include refid="sql_select"/>
        from hive_app_version
        where is_del_flag = 0
    </select>

    <sql id="sql_where">
        from hive_app_version
        <where>
            <if test="versionId != null">
                and version_id = #{versionId}
            </if>
            <if test="systemType != null">
                and system_type = #{systemType}
            </if>
            <if test="appType != null">
                and app_type = #{appType}
            </if>
            <if test="versionCode != null">
                and version_code = #{versionCode}
            </if>
            <if test="versionName != null">
                and version_name = #{versionName}
            </if>
            <if test="versionDesc != null">
                and version_desc = #{versionDesc}
            </if>
            <if test="appUrl != null">
                and app_url = #{appUrl}
            </if>
            <if test="isEnforceUpdate != null">
                and is_enforce_update = #{isEnforceUpdate}
            </if>
            <if test="isActive != null">
                and is_active = #{isActive}
            </if>
            <if test="isDelFlag != null">
                and is_del_flag = #{isDelFlag}
            </if>
            <if test="userCreate != null">
                and user_create = #{userCreate}
            </if>
            <if test="enforcementUpgrade != null">
                and enforcement_upgrade = #{enforcementUpgrade}
            </if>
        </where>
    </sql>

    <sql id="sql_select">
        select
            version_id,
            system_type,
            app_type,
            version_code,
            version_name,
            version_desc,
            app_url,
            is_enforce_update,
            is_active,
            is_del_flag,
            user_create,
            enforcement_upgrade,
            gmt_create,
            gmt_modify	
    </sql>

</mapper>