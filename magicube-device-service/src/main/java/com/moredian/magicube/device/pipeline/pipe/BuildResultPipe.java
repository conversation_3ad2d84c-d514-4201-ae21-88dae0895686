package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.AreaInfo;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.AreaService;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.constant.StatusCode;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.activate.ActivateDeviceResultDTO;
import com.moredian.magicube.device.dto.activate.ActivationDeviceInfoDTO;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import java.util.Collections;

import com.xier.guard.accessKey.dto.AccessKeyDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 返回信息组装管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BuildResultPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private OrgService orgService;

    @SI
    private AreaService areaService;

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        ActivateDeviceResultDTO resultDTO = (context.getResultDTO() == null
            ? new ActivateDeviceResultDTO() : context.getResultDTO());
        AccessKeyDto accessKeyDto = context.getAccessKeyDto();

        if (accessKeyDto != null) {
            resultDTO.setAccessKeySecret(accessKeyDto.getAccessKeySecret());
        }
        //组装返回信息
        Device device = context.getDevice();
        resultDTO.setOrgId(context.getOrgId());
        resultDTO.setSubOrgId(device.getPositionId());
        resultDTO.setEquipmentId(device.getDeviceId());
        resultDTO.setEquipmentType(device.getDeviceType());
        resultDTO.setSerialNumber(device.getDeviceSn());
        OrgInfo orgInfo = orgService
            .getOrgInfo(context.getOrgId(), Collections.singletonList(OrgStatus.USABLE.getValue()))
            .pickDataThrowException();
        if (orgInfo != null) {
            resultDTO.setCityName(getCityName(orgInfo.getCityId()));
            resultDTO.setOrgTpId(orgInfo.getTpId());
            resultDTO.setOrgTpType(orgInfo.getTpType());
        }
        context.setResultDTO(resultDTO);

        //想激活信息写入缓存，供H5查询
        if (StringUtils.isNotBlank(dto.getThirdDeviceId())) {
            ActivationDeviceInfoDTO activationDeviceInfoDTO = new ActivationDeviceInfoDTO();
            BeanUtils.copyProperties(device, activationDeviceInfoDTO);
            InventoryDevice inventoryDevice = context.getInventoryDevice();
            if (inventoryDevice != null) {
                activationDeviceInfoDTO.setMacAddress(inventoryDevice.getMacAddress());
            }
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.THIRD_PARTY_ACTIVE_SUCCESS, HiveConst.THIRD_PARTY_TIME_OUT);
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_INFO_KEY,
                activationDeviceInfoDTO, HiveConst.THIRD_PARTY_TIME_OUT);
        } else if (StringUtils.isNotBlank(dto.getQrCode())) {
            redissonCacheComponent
                .setObjectCache(dto.getQrCode() + HiveConst.MEMCACHED_EQUIPMENT_ACTIVE_STATUS_KEY,
                    StatusCode.ACTIVE_SUCCESS, HiveConst.ACTIVE_DEVICE_TIME_OUT);
        }
    }

    private String getCityName(Integer cityId) {
        if (cityId != null) {
            AreaInfo areaInfo = areaService.getAreaByCode(cityId).pickDataThrowException();
            if (areaInfo != null) {
                return areaInfo.getAreaName();
            }
        }
        return "杭州市";
    }
}
