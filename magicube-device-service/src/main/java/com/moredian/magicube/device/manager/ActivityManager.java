package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.DeviceActiveState;
import com.moredian.magicube.device.dto.activate.ActivateDeviceResultDTO;
import com.moredian.magicube.device.dto.activate.GenerateQrCodeDTO;
import com.moredian.magicube.device.dto.activate.QrCodeDTO;
import com.moredian.magicube.device.dto.activate.QrCodeStatusDTO;
import com.moredian.magicube.device.dto.activate.QueryActivateInfoDTO;
import com.moredian.magicube.device.dto.activate.QueryActivationStatusDTO;
import com.moredian.magicube.device.dto.activate.QueryQrCodeStatusDTO;
import com.moredian.magicube.device.dto.activate.ThirdPartyDeviceInfoDTO;
import com.moredian.magicube.device.dto.activate.UpdateQrCodeStatusDTO;

import java.util.List;

/**
 * 设备激活服务管理接口
 * 提供设备激活、绑定、解绑等相关操作的管理功能
 *
 * <AUTHOR>
 */
public interface ActivityManager {

    /**
     * 生成激活码接口：获取设备激活码接口 设备调用：根据返回的二维码串生成二维码
     * 用于设备初始化时生成用于激活的二维码信息
     *
     * @param dto 生成激活码参数，包含设备相关信息
     * @return QrCodeDTO 返回生成的二维码信息，包含二维码字符串等数据
     */
    QrCodeDTO generateQrCode(GenerateQrCodeDTO dto);

    /**
     * 根据二维码获取对应状态
     * 用于查询设备二维码当前的激活/绑定状态
     *
     * @param dto 二维码查询参数，包含二维码信息
     * @return QrCodeStatusDTO 返回二维码当前状态信息，如是否已扫码、是否已激活等
     */
    QrCodeStatusDTO getStatusByQrCode(QueryQrCodeStatusDTO dto);

    /**
     * 修改扫码状态
     * 用于更新设备二维码的扫描状态，通常在用户扫描二维码后调用
     *
     * @param dto 二维码信息，包含需要更新的状态信息
     * @return QrCodeStatusDTO 返回更新后的二维码状态信息
     */
    QrCodeStatusDTO updateQrCodeStatus(UpdateQrCodeStatusDTO dto);

    /**
     * 根据sn激活设备，开放平台使用
     * 提供给开放平台的设备激活接口，无需扫描二维码即可直接激活设备
     *
     * @param orgId     机构号，设备所属的组织机构ID
     * @param accountId 账号Id，执行激活操作的账号ID
     * @param deviceSn  设备sn，设备唯一序列号
     * @return QrCodeStatusDTO 返回激活后的状态信息
     */
    QrCodeStatusDTO updateQrCodeStatusBySn(Long orgId, Long accountId, String deviceSn);

    /**
     * 根据二维码获取激活状态
     * 查询设备的激活状态信息
     *
     * @param qrCode 二维码字符串
     * @return QueryActivationStatusDTO 返回设备的激活状态详细信息
     */
    QueryActivationStatusDTO getActivityStatusByQrCode(String qrCode);

    /**
     * 根据第三方设备Id获取激活状态
     * 用于查询第三方设备的激活状态
     *
     * @param thirdPartyDeviceId 第三方设备Id，第三方平台的设备唯一标识
     * @return QueryActivationStatusDTO 返回设备的激活状态详细信息
     */
    QueryActivationStatusDTO getThirdPartyActivityStatusByTpId(String thirdPartyDeviceId);

    /**
     * 解绑设备
     * 解除设备与当前账号/组织的绑定关系
     *
     * @param deviceSn 设备sn，设备唯一序列号
     * @return Boolean 解绑操作是否成功
     */
    Boolean unbindDevice(String deviceSn);

    /**
     * 更新第三方设备信息
     * 用于更新第三方平台设备的相关信息
     *
     * @param dto 第三方设备信息，包含需要更新的设备详细信息
     * @return Boolean 更新操作是否成功
     */
    Boolean updateThirdPartyDeviceInfo(ThirdPartyDeviceInfoDTO dto);

    /**
     * 获取激活信息
     * 查询设备的激活详细信息
     *
     * @param dto 查询条件，包含查询所需的参数
     * @return ActivateDeviceResultDTO 返回设备的激活详细信息
     */
    ActivateDeviceResultDTO getActivateInfo(QueryActivateInfoDTO dto);

    /**
     * 设备去激活
     * 取消设备的激活状态，使设备恢复到未激活状态
     *
     * @param deviceSn 设备sn，设备唯一序列号
     * @return Boolean 去激活操作是否成功
     */
    Boolean deActivateDevice(String deviceSn);

    /**
     * 获取设备激活状态
     * 查询设备当前的激活状态信息
     *
     * @param deviceSn 设备sn，设备唯一序列号
     * @return DeviceActiveState 返回设备的激活状态实体
     */
    DeviceActiveState getDeviceActiveState(String deviceSn);

    /**
     * 设备激活状态变更
     * 更新设备的激活状态，可用于设备的激活、去激活等状态变更操作
     *
     * @param deviceSn 设备sn，设备唯一序列号
     * @param state 设备状态，表示要变更的目标状态
     * @param bindOrgUrl 绑定组织的URL，用于关联设备与组织的关系
     * @return Boolean 状态变更操作是否成功
     */
    Boolean deviceActiveStateChange(String deviceSn, Integer state, String bindOrgUrl, Long bindOrgId);
}
