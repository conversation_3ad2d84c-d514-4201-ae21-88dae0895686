package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceVersion;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.BaseMapper;

@Mapper
public interface DeviceApkVersionMapper extends BaseMapper<DeviceVersion> {

    /**
     * 根据设备Sn获取apk版本信息
     *
     * @param deviceSn
     * @return
     */
    DeviceVersion getDeviceApkVersionBySn(@Param("deviceSn") String deviceSn);

    /**
     * 新增设备apk版本信息
     *
     * @param deviceVersion
     * @return
     */
    int insert(DeviceVersion deviceVersion);

    /**
     * 根据设备Sn修改apk版本信息
     *
     * @param deviceVersion
     * @return
     */
    int updateDeviceApkVersionByDeviceSn(DeviceVersion deviceVersion);

    /**
     * 根据设备sn批量获取apk版本信息
     *
     * @param deviceSnList
     * @return
     */
    List<DeviceVersion> getBatchDeviceApkVersion(@Param("deviceSnList") List<String> deviceSnList);

    /**
     * 获取机构下比指定版本低的设备信息
     *
     * @param orgIdList
     * @param appType
     * @param versionCode
     * @return
     */
    List<DeviceVersion> findDeviceApkVersionByOrgIdListAndVersion(@Param("orgIdList") List<Long> orgIdList, @Param("appType") Integer appType, @Param("versionCode") Integer versionCode);

    /**
     * 批量查询设备APK版本
     * @param orgId
     * @param deviceIdList
     * @return
     */
    List<DeviceVersion> getBatchDeviceApkVersionByOrgIdAndDeviceIds(@Param("orgId") Long orgId, @Param("deviceIdList") List<Long> deviceIdList);
}