package com.moredian.magicube.device.xxljob;

import com.moredian.bee.job.annotation.BeeXxlJob;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import com.moredian.magicube.device.dao.entity.DeviceVersion;
import com.moredian.magicube.device.dao.mapper.DeviceApkVersionMapper;
import com.moredian.magicube.device.dto.device.DeviceDTO;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceRelateAppFixedBIzTypeEnum;
import com.moredian.magicube.device.manager.DeviceAppRelationConfigManager;
import com.moredian.magicube.device.manager.DeviceAppRelationFixedConfigManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class DeviceRelateAppCodeJob {

    @Resource
    private DeviceManager deviceManager;
    @Resource
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;
    @Resource
    private DeviceAppRelationFixedConfigManager deviceAppRelationFixedConfigManager;
    @Resource
    private DeviceApkVersionMapper deviceApkVersionMapper;
    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;
    @SI
    private SpaceTreeService spaceTreeService;
    @SI
    private OrgService orgService;


    @BeeXxlJob(value = "deviceRelateAppCode", name = "刷新所有设备关联应用")
    public ReturnT<String> deviceRelateAppCode(String param) throws InterruptedException {
        //失败的机构id集合
        List<Long> failOrgIdList = new ArrayList<>();

        if (StringUtils.isBlank(param)) {
            log.info("机构id参数为空，开始刷新所有设备的关联应用");
            //查询所有设备
            List<DeviceDTO> devices = deviceManager.listAllDevices();

            for (DeviceDTO device : devices) {
                try {
                    //刷新单台设备的关联应用
                    refreshSingleDeviceRelateApp(device.getOrgId(), device.getDeviceId(), device.getDeviceSn());
                } catch (Exception e) {
                    failOrgIdList.add(device.getOrgId());
                }
            }
        } else {
            log.info("机构id参数为{}，开始刷新该机构的关联应用", param);
            long orgId;
            try {
                orgId = Long.parseLong(param);
            } catch (NumberFormatException e) {
                log.warn("orgId参数有误,请输入正确的Long类型数据:{}", param);
                return new ReturnT<>(500, "orgId参数有误,请输入正确的Long类型数据:" + param);
            }

            OrgInfo orgInfo = orgService.getOrgInfo(orgId, Collections.singletonList(OrgStatus.USABLE.getValue())).pickDataThrowException();
            if (orgInfo == null) {
                log.info("机构:{}不存在！", orgId);
                return ReturnT.SUCCESS;
            }
            //查询机构下设备
            List<Device> devices = deviceManager.listByOrgId(orgId);

            for (Device device : devices) {
                try {
                    //刷新单台设备的关联应用
                    refreshSingleDeviceRelateApp(device.getOrgId(), device.getDeviceId(), device.getDeviceSn());
                } catch (Exception e) {
                    failOrgIdList.add(device.getOrgId());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(failOrgIdList)) {
            log.warn("刷新设备关联应用失败的机构id集合:{}", failOrgIdList);
            return new ReturnT<>(500, "刷新设备关联应用失败的机构id集合:" + failOrgIdList);
        } else {
            log.warn("刷新设备关联应用成功");
            return ReturnT.SUCCESS;
        }
    }

    /**
     * 刷新单个设备关联应用
     *
     * @param orgId    机构id
     * @param deviceId 设备id
     * @param deviceSn 设备sn
     */
    private void refreshSingleDeviceRelateApp(Long orgId, Long deviceId, String deviceSn) {

        if (deviceRelateFixedApp(orgId,deviceId,deviceSn)){
            log.info("设备SN:{}关联固定配置应用成功", deviceSn);
            return;
        }
        List<DeviceAppRelationConfig> deviceAppRelationConfigs = null;
        Integer spaceType = -1;
        //查找空间类型
        TreeDeviceRelationDTO treeDeviceRelationDTO = spaceTreeDeviceRelationService.getByOrgIdAndDeviceId(orgId, String.valueOf(deviceId)).pickDataThrowException();

        if (treeDeviceRelationDTO != null) {
            TreeDTO treeDTO = spaceTreeService.getById(treeDeviceRelationDTO.getTreeId(), orgId).pickDataThrowException();
            if (treeDTO != null) {
                spaceType = treeDTO.getTags().get(0).getSpaceType();
            }
        }
        //获取设备版本信息
        DeviceVersion deviceApkVersionBySn = deviceApkVersionMapper.getDeviceApkVersionBySn(deviceSn);
        if (deviceApkVersionBySn == null) {
            log.warn("设备SN:{}版本信息不存在,跳过", deviceSn);
            return;
        }
        Integer appType = deviceApkVersionBySn.getAppType();
        Integer versionCode = deviceApkVersionBySn.getVersionCode();
        deviceAppRelationConfigs = deviceAppRelationConfigManager.selectBySpaceTypeAndAppTypeAndVersionCode(spaceType, appType, versionCode);
        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
        updateDeviceDTO.setOrgId(orgId);
        updateDeviceDTO.setDeviceId(deviceId);
        if (CollectionUtils.isEmpty(deviceAppRelationConfigs)) {
            log.warn("设备SN:{}未找到可用应用配置,关联应用置为空", deviceSn);
            updateDeviceDTO.setAppCode("");
            updateDeviceDTO.setAppCodeList("");
        } else {
            updateDeviceDTO.setAppCode(deviceAppRelationConfigs.get(0).getDefaultAppCode());
            updateDeviceDTO.setAppCodeList(deviceAppRelationConfigs.get(0).getAvailableAppCodeList());
        }
        deviceManager.update(updateDeviceDTO);
    }


    /**
     * 设备关联固定配置应用
     * @param orgId 机构id
     * @param deviceId 设备id
     * @param deviceSn 设备sn
     * @return 是否存在固定配置
     */
    private boolean deviceRelateFixedApp(Long orgId, Long deviceId,String deviceSn) {
        //先判断固定配置是否存在 如果存在就直接设置固定配置的appCode即可
        DeviceAppRelationFixedConfig fixedConfig = deviceAppRelationFixedConfigManager.getByBizTypeAndId(
                DeviceRelateAppFixedBIzTypeEnum.DEVICE_SN.getCode(), deviceSn);

        if (fixedConfig != null) {
            //更新设备相关信息
            UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
            updateDeviceDTO.setOrgId(orgId);
            updateDeviceDTO.setDeviceId(deviceId);
            updateDeviceDTO.setAppCode(fixedConfig.getDefaultAppCode());
            updateDeviceDTO.setAppCodeList(fixedConfig.getAvailableAppCodeList());
            boolean update = deviceManager.update(updateDeviceDTO);
            log.info("设备更新固定关联应用结果:{}", update);
            return true;
        }
        return false;
    }
}

