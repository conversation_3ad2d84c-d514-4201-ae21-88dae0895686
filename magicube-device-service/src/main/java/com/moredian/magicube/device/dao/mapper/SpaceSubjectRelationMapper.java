package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import com.moredian.magicube.device.dao.entity.SpaceSubjectReleation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 主题和空间关联关系表Mapper
 *
 * <AUTHOR>
 * @since 2024-12-12-17
 */

@Mapper
public interface SpaceSubjectRelationMapper {

    /**
     * 新增设备和主题关系
     *
     * @param relation 关系信息
     * @return
     */
    int insert(SpaceSubjectReleation relation);

    /**
     * 批量新增设备和主题关系
     *
     * @param relations 关系信息
     * @return
     */
    void batchInsert(@Param("relations") List<SpaceSubjectReleation> relations);

    /**
     * 根据机构Id和主题Id查询关联的设备Id列表
     *
     * @param orgId     机构Id
     * @param subjectId 主题Id
     * @return
     */
    List<Long> listSpaceIdByOrgIdAndSubjectId(@Param("orgId") Long orgId,
        @Param("subjectId") Long subjectId);

    /**
     * 删除关系
     *
     * @param orgId      机构号
     * @param spaceIds  空间Id列表
     * @param subjectIds 主题Id列表
     * @return
     */
    void deleteByCondition(@Param("orgId") Long orgId, @Param("spaceIds") List<Long> spaceIds,
        @Param("subjectIds") List<Long> subjectIds);

    /**
     * 根据机构Id和设备主题Id列表查询关系信息
     *
     * @param orgId      机构Id
     * @param subjectIds 设备主题Id列表
     * @return
     */
    List<SpaceSubjectReleation> listByOrgIdAndSubjectIds(@Param("orgId") Long orgId,
        @Param("subjectIds") List<Long> subjectIds);

    /**
     * 根据机构Id和设备Id列表查询关系信息
     *
     * @param orgId     机构Id
     * @param spaceIds 空间Id列表
     * @param type      主题类型 1-壁纸 2-屏保
     * @return
     */
    List<SpaceSubjectReleation> listByOrgIdAndSpaceIds(@Param("orgId") Long orgId,
        @Param("spaceIds") List<Long> spaceIds, @Param("type") Integer type);
}
