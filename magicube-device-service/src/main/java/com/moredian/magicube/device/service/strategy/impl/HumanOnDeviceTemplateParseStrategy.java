package com.moredian.magicube.device.service.strategy.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.enums.ModeTriggerEnum;
import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.magicube.device.jetlinks.rule.arrange.*;
import com.moredian.magicube.device.jetlinks.ruleArrange.RuleInstance;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.RuleManager;
import com.moredian.magicube.device.service.strategy.ModeTriggerTemplateParseStrategy;
import com.moredian.magicube.device.utils.StringUtils;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import static com.moredian.magicube.device.constant.DeviceIotPropertyConstants.JET_LINK_RULE_TYPE_RULE_ARRANGE;
import static com.moredian.magicube.device.constant.DeviceTypeConstants.IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST;
import static com.moredian.magicube.device.constant.DeviceTypeConstants.IOT_VIRTUAL_DEVICE_TYPE_LIST;

/**
 * <AUTHOR>
 * @Description 有人开启设备生成 JET links 规则模板
 * @create 2024-12-24 9:44
 */
@Slf4j
@Service(value="humanOnDeviceTemplateParseStrategy")
public class HumanOnDeviceTemplateParseStrategy implements ModeTriggerTemplateParseStrategy {

    private static final ModeTriggerEnum TEMPLATE_ENUM = ModeTriggerEnum.USE_ENERGY_HUMAN_TRIGGER;

    public static final String MODEL_TYPE = "node-red";

    /**
     * 定时检查雷达无人，core表达式
     */
    @Value("${iot.config.rule.humanOn.timeCorn:0/30 * * * * ?}")
    private String timeCorn;

    /**
     * 网络组件HTTP服务
     */
    @Value("${iot.config.rule.humanOn.httpServerId:1868903524457840640}")
    private String humanHttpServerId;

    /**
     * 监听雷达有人触发接口，协议包中调用
     */
    @Value("${iot.config.rule.humanOn.humanHttpUrl:/human/state/motion}")
    private String humanHttpUrl;

    /**
     * 获取规则配置接口，调用moredian-fishnet-web接口
     */
    @Value("${iot.config.rule.humanOn.ruleConfigUrl:http://192.168.4.134:8049/fishnetweb/iot/getRuleConfig}")
    private String ruleConfigUrl;

    /**
     * 释放规则锁接口
     */
    @Value("${iot.config.rule.humanOn.releaseRuleLockUrl:http://192.168.4.134:8049/fishnetweb/iot/ruleLock}")
    private String releaseRuleLockUrl;

    @Resource
    private RuleManager ruleManager;
    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private OrgService orgService;


    /**
     * 有人开启设备，使用 JetLinks 中规则编排实现
     * @param rule 规则
     * @param treeDeviceRelationList 空间所有的IOT设备
     * @param iotDeviceMap  查询 JetLinks 中的设备
     * @param iotDevicePropertyMap 查询 JetLinks 中IOT设备所有物模型
     * @param selectDeviceTypes 规则选择的设备，开关/空调/插座
     */
    @Override
    public Object getSceneRule(Rule rule, List<TreeDeviceRelationDTO> treeDeviceRelationList, Map<String, IotDeviceInfoDTO> iotDeviceMap,
                               Map<String, BatchIotDevicePropertyInfoDTO> iotDevicePropertyMap, List<Integer> selectDeviceTypes) {

        // 按照空间划分设备
        Map<Long, List<TreeDeviceRelationDTO>> treeDeviceMap =  treeDeviceRelationList.stream()
                .collect(Collectors.groupingBy(TreeDeviceRelationDTO::getTreeId));

        // 查询机构信息，用于构建规则名称
        OrgInfo orgInfo = orgService.getOrgInfo(rule.getOrgId(), Lists.newArrayList(OrgStatus.USABLE.getValue()))
                .pickDataThrowException();
        String orgName = ObjectUtils.isEmpty(orgInfo) ? StringUtil.EMPTY_STRING : orgInfo.getOrgName();

        Map<Long, RuleInstance> spaceRuleMetaDataMap = Maps.newHashMap();

        // 规则支持的雷达设备集合
        List<Integer> radarDeviceTypeList = deviceTypePropertyManager.getDeviceTypeByPropertyKey(IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST);
        for (Map.Entry<Long, List<TreeDeviceRelationDTO>> treeDeviceEntry :  treeDeviceMap.entrySet()) {
            // 空间下需要控制的设备集合
            List<TreeDeviceRelationDTO> controlDeviceList = Lists.newArrayList();
            // 空间下雷达设备集合
            List<TreeDeviceRelationDTO> triggerDeviceRelationList = Lists.newArrayList();
            // 空间下的所有设备
            List<TreeDeviceRelationDTO> spaceDeviceList = treeDeviceEntry.getValue();
            for (TreeDeviceRelationDTO treeDeviceRelation : treeDeviceRelationList) {
                Integer deviceType = treeDeviceRelation.getDeviceType();
                if (selectDeviceTypes.contains(deviceType)) {
                    controlDeviceList.add(treeDeviceRelation);
                } else if (radarDeviceTypeList.contains(deviceType)) {
                    triggerDeviceRelationList.add(treeDeviceRelation);
                }
            }

            if (CollectionUtils.isEmpty(controlDeviceList)) {
                log.error("iot控制设备不存在，orgId: {} ,treeId: {}", rule.getOrgId(), spaceDeviceList);
                continue;
            }

            if (CollectionUtils.isEmpty(triggerDeviceRelationList)) {
                log.error("iot触发设备不存在，orgId: {} ,treeId: {}", rule.getOrgId(), spaceDeviceList);
                continue;
            }

            // 设备触发方式全部改成规则编排触发
            RuleInstance ruleInstance = getRuleArrangeInstance(triggerDeviceRelationList,
                    controlDeviceList, rule.getRuleName(),
                    orgName, rule.getTriggerValue());
            spaceRuleMetaDataMap.put(treeDeviceEntry.getKey(), ruleInstance);
        }
        return spaceRuleMetaDataMap;
    }

    /**
     * 获取一个空间下对应设备的规则编排实例
     */
    private RuleInstance getRuleArrangeInstance(List<TreeDeviceRelationDTO> triggerDeviceRelationList,
                                                List<TreeDeviceRelationDTO> controlDeviceList, String ruleName,
                                                String orgName, String triggerValue) {
        // 规则名称：房间名称+规则名称
        ruleName = controlDeviceList.get(0).getTreeName() + StringUtils.CROSS_LINE + ruleName;

        StringJoiner controlDeviceName = new StringJoiner(",", "(", ")");
        controlDeviceList.forEach(e-> controlDeviceName.add(e.getDeviceName()));

        RuleInstance ruleInstance = new RuleInstance();
        ruleInstance.setModelId(StringUtils.uuidStr());
        ruleInstance.setName(ruleName);
        ruleInstance.setDescription(orgName + controlDeviceName);
        ruleInstance.setModelType(MODEL_TYPE);
        String metadata = buildRulMetaData(triggerDeviceRelationList, controlDeviceList, ruleName, triggerValue);
        ruleInstance.setModelMeta(metadata);
        ruleInstance.setRuleType(JET_LINK_RULE_TYPE_RULE_ARRANGE);
        return ruleInstance;
    }

    private String buildRulMetaData(List<TreeDeviceRelationDTO> triggerDeviceRelationList, List<TreeDeviceRelationDTO> controlDeviceList,
                               String ruleName, String triggerValue) {
        List<Node> flows = new ArrayList<>();

        // 主节点
        TabNode tabNode = new TabNode();
        tabNode.setLabel(ruleName);
        tabNode.setDisabled(Boolean.TRUE);
        flows.add(tabNode);

        // 设备指令节点
        DeviceMessageSenderNode deviceMessageSenderNode = new DeviceMessageSenderNode();
        List<List<String>> wires1 = new ArrayList<>();
        deviceMessageSenderNode.setWires(wires1);
        deviceMessageSenderNode.setZ(tabNode.getId());
        deviceMessageSenderNode.setX(1300L);
        deviceMessageSenderNode.setY(100L);
        deviceMessageSenderNode.setName("设备指令");
        flows.add(deviceMessageSenderNode);

        // 是否开灯节点
        FunctionNode onDeviceFlag = new FunctionNode();
        String timeFunc = FunctionNode.RULE_CONFIG;
        onDeviceFlag.setFunc(timeFunc);
        onDeviceFlag.setZ(tabNode.getId());
        onDeviceFlag.setY(100L);
        onDeviceFlag.setX(740L);
        onDeviceFlag.setName("是否开启设备");
        flows.add(onDeviceFlag);

        long y = 100L;
        // 需要控制的设备判断开关状态
        for (TreeDeviceRelationDTO deviceRelation : controlDeviceList) {
            // 获取虚拟设备对应的开关物模型，例如空调:ac_power_01
            String group = getGroup(deviceRelation);
            String powerPropertyId = getPropertyId(deviceRelation, group);
            String deviceSn = parseDeviceSn(deviceRelation.getDeviceSn());

            // 开关判断函数节点
            FunctionNode functionNode = new FunctionNode();
            String powerFunc = FunctionNode.HUMAN_ON_DEVICE.replaceAll("#\\{propertyId}", powerPropertyId)
                    .replaceAll("#\\{deviceSn}", deviceSn)
                    .replaceAll("#\\{propertyValue}", String.valueOf(RuleStateEnum.ACTIVE.getCode()));
            functionNode.setFunc(powerFunc);
            functionNode.nodeJoin(deviceMessageSenderNode);
            functionNode.setZ(tabNode.getId());
            functionNode.setX(1120L);
            functionNode.setY(y);
            functionNode.setName("开启设备");
            flows.add(functionNode);

            // 查询开关状态ReactorQL节点
            ReactorQLNode powerQLNode = new ReactorQLNode();
            String powerQL = ReactorQLNode.DEVICE_POWER_STATUS.replaceAll("#\\{propertyId}",
                    powerPropertyId).replaceAll("#\\{deviceSn}", deviceSn);
            powerQLNode.setSql(powerQL);
            powerQLNode.nodeJoin(functionNode);
            powerQLNode.setZ(tabNode.getId());
            powerQLNode.setY(y);
            powerQLNode.setX(940L);
            powerQLNode.setName("设备开关状态");
            flows.add(powerQLNode);

            y = y + 60;
            onDeviceFlag.nodeJoin(powerQLNode);
        }

        // 获取规则配置节点
        HttpRequestNode httpRequestNode = new HttpRequestNode();
        httpRequestNode.setZ(tabNode.getId());
        httpRequestNode.setY(100L);
        httpRequestNode.setX(550L);
        httpRequestNode.setMethod("POST");
        httpRequestNode.setConnectTimeout(5000);
        httpRequestNode.nodeJoin(onDeviceFlag);
        httpRequestNode.setName("获取规则配置");
        flows.add(httpRequestNode);

        // 判断是否石规则雷达，向下游节点推送获取规则配置请求数据
        StringJoiner radarDeviceIds = new StringJoiner(",");
        for (TreeDeviceRelationDTO deviceRelation : triggerDeviceRelationList) {
            String deviceSn = deviceRelation.getDeviceSn();
            radarDeviceIds.add("'" + deviceSn + "'");
        }
        FunctionNode radarJudeNode = new FunctionNode();
        String radarJudeFunc = FunctionNode.HUMAN_RADAR_JUDGE.replaceAll("#\\{radarDeviceIds}", radarDeviceIds.toString())
                .replaceAll("#\\{url}", ruleConfigUrl);
        radarJudeNode.setFunc(radarJudeFunc);
        radarJudeNode.nodeJoin(httpRequestNode);
        radarJudeNode.setZ(tabNode.getId());
        radarJudeNode.setX(340L);
        radarJudeNode.setY(100L);
        radarJudeNode.setName("是否规则雷达");
        flows.add(radarJudeNode);

        // 雷达监听Http相应
        HttpResponseNode radarResponseNode = new HttpResponseNode();
        radarResponseNode.setStatusCode("200");
        radarResponseNode.setZ(tabNode.getId());
        radarResponseNode.setY(160L);
        radarResponseNode.setX(340L);
        flows.add(radarResponseNode);

        // 监听雷达有人触发
        HttpInNode radarHumanNode = new HttpInNode();
        radarHumanNode.setMethod("post");
        radarHumanNode.setServerId(humanHttpServerId);
        radarHumanNode.setUrl(humanHttpUrl);
        radarHumanNode.setZ(tabNode.getId());
        radarHumanNode.setX(110L);
        radarHumanNode.setY(100L);
        radarHumanNode.setName("雷达有人监听");
        radarHumanNode.nodeJoin(radarJudeNode);
        radarHumanNode.nodeJoin(radarResponseNode);
        flows.add(radarHumanNode);

        /** =========================轮询检查空间持续无人时间，释放规则锁 ========================= */
        // 释放规则锁http请求节点
        y = y + 50;
        HttpRequestNode releaseLockHttpNode = new HttpRequestNode();
        releaseLockHttpNode.setZ(tabNode.getId());
        releaseLockHttpNode.setY(y);
        releaseLockHttpNode.setX(770L);
        releaseLockHttpNode.setMethod("POST");
        releaseLockHttpNode.setConnectTimeout(5000);
        releaseLockHttpNode.setName("释放规则锁");
        flows.add(releaseLockHttpNode);

        // 是否达到无人持续时间，并向下游推送释放锁的请求数据
        FunctionNode releaseLockFuncNode = new FunctionNode();
        String releaseLockFunc = FunctionNode.RULE_RELEASE_LOCK.replaceAll("#\\{triggerValue}", triggerValue)
                        .replaceAll("#\\{url}", releaseRuleLockUrl);
        releaseLockFuncNode.setFunc(releaseLockFunc);
        releaseLockFuncNode.setZ(tabNode.getId());
        releaseLockFuncNode.setY(y);
        releaseLockFuncNode.setX(510L);
        releaseLockFuncNode.nodeJoin(releaseLockHttpNode);
        releaseLockFuncNode.setName("持续无人时间");
        flows.add(releaseLockFuncNode);

        // 获取雷达状态ReactorQL节点
        ReactorQLNode humanBodyStatusQL = buildHumanBodyStatusQL(triggerDeviceRelationList);
        humanBodyStatusQL.nodeJoin(releaseLockFuncNode);
        humanBodyStatusQL.setZ(tabNode.getId());
        humanBodyStatusQL.setY(y);
        humanBodyStatusQL.setX(320L);
        humanBodyStatusQL.setName("雷达状态");
        flows.add(humanBodyStatusQL);

        // 构建定时任务节点
        TimerNode timerNode = new TimerNode();
        timerNode.setCron(timeCorn);
        timerNode.nodeJoin(humanBodyStatusQL);
        timerNode.setZ(tabNode.getId());
        timerNode.setY(y);
        timerNode.setX(110L);
        timerNode.setName("定时任务");
        flows.add(timerNode);
        ArrangeMetaData metaData = new ArrangeMetaData();
        metaData.setFlows(flows);
        return StringUtils.getSpecialJsonStr(metaData);
    }

    /**
     * 构建查询雷达状态节点
     */
    private ReactorQLNode buildHumanBodyStatusQL(List<TreeDeviceRelationDTO> triggerDeviceRelationList) {
        StringJoiner reactorSql = new StringJoiner("");
        StringJoiner select = new StringJoiner("",
                "select collect_list(deviceId, motionStatus, duration) AS devices from( ",
                " ) t");

        StringJoiner humanDeviceSelect = getHumSelectSql(triggerDeviceRelationList);
        select.add(humanDeviceSelect.toString());
        reactorSql.add(select.toString());

        ReactorQLNode reactorQLNode = new ReactorQLNode();
        reactorQLNode.setSql(reactorSql.toString());
        return reactorQLNode;
    }

    private StringJoiner getHumSelectSql(List<TreeDeviceRelationDTO> triggerDeviceRelationList) {
        StringJoiner humanDeviceSelect = new StringJoiner(" union all ");
        TreeDeviceRelationDTO triggerDeviceRelation;
        for (TreeDeviceRelationDTO treeDeviceRelationDTO : triggerDeviceRelationList) {
            triggerDeviceRelation = treeDeviceRelationDTO;
            String selectSql =
                    "select id AS deviceId,device.property.recent(id,'motionStatus') AS motionStatus,\n"
                            + "now() - device.property_time.recent(id, 'motionStatus') AS duration\n"
                            + " from device.selector(device('" + triggerDeviceRelation.getDeviceSn()
                            + "'))";
            humanDeviceSelect.add(selectSql);
        }
        return humanDeviceSelect;
    }

    @Override
    public ModeTriggerEnum getTemplate() {
        return TEMPLATE_ENUM;
    }

    @Override
    public Boolean virtualIotDevice(Integer deviceType) {
        return deviceTypePropertyManager.containsDeviceType(IOT_VIRTUAL_DEVICE_TYPE_LIST, deviceType);
    }
}
