package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dto.type.QueryDeviceTypeDTO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 设备类型相关接口
 *
 * <AUTHOR>
 */
public interface DeviceTypeManager {

    /**
     * 新增设备类型
     *
     * @param deviceType 设备类型信息
     * @return
     */
    Long insert(DeviceType deviceType);

    /**
     * 编辑设备类型信息
     *
     * @param deviceType 设备类型信息
     * @return
     */
    Long update(DeviceType deviceType);

    /**
     * 删除设备类型
     *
     * @param deviceType 设备类型
     * @param status     0:删除 1:正常
     * @return
     */
    int updateStatusByDeviceType(Integer deviceType, Integer status);

    /**
     * 删除设备类型
     *
     * @param deviceType 设备类型
     * @return
     */
    Boolean deleteByDeviceType(Integer deviceType);

    /**
     * 查询设备类型
     *
     * @return
     */
    List<DeviceType> list();

    /**
     * 根据设备类型列表查询设备类型列表
     *
     * @param deviceTypes 设备类型列表
     * @return
     */
    List<DeviceType> listByTypes(List<Integer> deviceTypes);

    /**
     * 根据Id查询设备类型信息
     *
     * @param deviceTypeId 设备类型Id
     * @return
     */
    DeviceType getById(Long deviceTypeId);

    /**
     * 根据设备类型查询设备类型名称
     *
     * @param deviceType 设备类型
     * @return
     */
    String getName(Integer deviceType);

    /**
     * 根据设备类型查询设备类型信息
     *
     * @param deviceType 设备类型
     * @return
     */
    DeviceType getByDeviceType(Integer deviceType);

    /**
     * 根据产品型号查询设备类型信息列表
     *
     * @param productModelCode 产品型号编码
     * @return
     */
    List<DeviceType> getByProductModelCode(String productModelCode);

    /**
     * 根据产品型号列表查询设备类型信息
     *
     * @param productModelCodes 产品型号编码列表
     * @return
     */
    List<DeviceType> listByProductModelCodes(List<String> productModelCodes);

    /**
     * 根据条件查询查询设备类型列表
     *
     * @param dto 设备类型
     * @return
     */
    List<DeviceType> listByCondition(QueryDeviceTypeDTO dto);

    /**
     * 根据设备内部名称模糊查询设备类型列表
     *
     * @param keywords 设备内部
     * @return
     */
    List<DeviceType> listLikeName(String keywords);
}