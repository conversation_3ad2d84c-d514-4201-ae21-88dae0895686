package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceFaceModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-27
 */
@Mapper
public interface DeviceFaceModelMapper  {

    /**
     * 新增（存在即更新）
     *
     * @param deviceFaceModel
     * @return
     */
    int insertOrUpdate(DeviceFaceModel deviceFaceModel);

    /**
     * 删除
     *
     * @param orgId 机构ID
     * @param deviceId 设备ID
     * @return
     */
    int deleteByDevice(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId);

    /**
     * 根据机构ID查询
     *
     * @param orgId
     * @return
     */
    List<Long> selectByOrgId(Long orgId);

}
