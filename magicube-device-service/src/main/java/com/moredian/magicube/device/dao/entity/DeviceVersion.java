package com.moredian.magicube.device.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 设备激活后上报版本信息(rom或者apk)实体类
 * @Date 2022/11/2
 */
@Getter
@Setter
@ToString
@TableName("hive_device_apk_version")
public class DeviceVersion implements Serializable {

    private static final long serialVersionUID = 6260751902401670795L;

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
