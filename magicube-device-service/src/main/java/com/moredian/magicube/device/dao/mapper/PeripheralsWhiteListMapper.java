package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.PeripheralsWhiteList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外设白名单管理
 *
 * <AUTHOR>
 */

@Mapper
public interface PeripheralsWhiteListMapper {

    /**
     * 新增外设白名单
     *
     * @param peripheralsWhiteList
     * @return
     */
    int insert(PeripheralsWhiteList peripheralsWhiteList);

    /**
     * 批量新增外设白名单
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<PeripheralsWhiteList> list);

    /**
     * 更新外设白名单状态
     *
     * @param peripheralsWhiteListId 外设白名单Id
     * @param status                 外设白名单状态
     * @return
     */
    int update(@Param("peripheralsWhiteListId") Long peripheralsWhiteListId, @Param("status") Integer status);

    /**
     * 根据外设sn和外设类型获取外设白名单
     *
     * @param peripheralsSn   外设sn
     * @param peripheralsType 外设类型
     * @return
     */
    PeripheralsWhiteList getBySnAndType(@Param("peripheralsSn") String peripheralsSn, @Param("peripheralsType") Integer peripheralsType);

    /**
     * 根据外设sn列表获取外设白名单列表
     *
     * @param peripheralsSns 外设sn列表
     * @return
     */
    List<PeripheralsWhiteList> listByPeripheralsSns(@Param("peripheralsSns") List<String> peripheralsSns);

    /**
     * 查询总数
     * @return
     */
    int countPeripheralsSn();

    /**
     * 分页查询sn
     */
    List<PeripheralsWhiteList> pagePeripheralsSn(@Param("sizeBegin") int sizeBegin, @Param("pageSize") int pageSize);
}
