package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.DeviceTypePropertyRelation;
import com.moredian.magicube.device.dao.mapper.DeviceTypePropertyRelationMapper;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceTypePropertyRelationManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class DeviceTypePropertyRelationManagerImpl implements DeviceTypePropertyRelationManager {

    @Autowired
    private DeviceTypePropertyRelationMapper mapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public List<Long> listPropertyIdByDeviceType(Integer deviceType) {
        BizAssert.notNull(deviceType, "deviceType must not be null");
        return mapper.listPropertyIdByDeviceType(deviceType);
    }

    @Override
    public List<Integer> listDeviceTypeByPropertyId(Long deviceTypePropertyId) {
        BizAssert.notNull(deviceTypePropertyId, "deviceTypePropertyId must not be null");
        return mapper.listDeviceTypeByPropertyId(deviceTypePropertyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(DeviceTypePropertyRelation relation) {
        BizAssert
            .notNull(relation.getDeviceTypePropertyId(), "deviceTypePropertyId must not be null");
        BizAssert.notNull(relation.getDeviceType(), "deviceType must not be null");
        DeviceTypePropertyRelation dpr = mapper
            .getByPropertyIdAndDeviceType(relation.getDeviceTypePropertyId(),
                relation.getDeviceType());
        BizAssert.isTrue(dpr == null, DeviceErrorCode.DEVICE_TYPE_PROPERTY_RELATION_EXIST,
            DeviceErrorCode.DEVICE_TYPE_PROPERTY_RELATION_EXIST.getMessage());
        relation.setId(
            idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_TYPE_PROPERTY_RELATION)
                .pickDataThrowException());
        mapper.insert(relation);
        return relation.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetRelationByDeviceType(Integer deviceType, List<Long> propertyIds) {
        BizAssert.notNull(deviceType, "deviceType must not be null");
        List<Long> incPropertyIds = new ArrayList<>(propertyIds);
        //该设备类型已存在的属性Id
        List<Long> exitPropertyIds = mapper.listPropertyIdByDeviceType(deviceType);
        //删除多余的属性Id列表
        List<Long> decPropertyIds = new ArrayList<>(exitPropertyIds);
        decPropertyIds.removeAll(propertyIds);
        if (CollectionUtils.isNotEmpty(decPropertyIds)) {
            mapper.deleteByDeviceTypeAndPropertyId(Collections.singletonList(deviceType),
                decPropertyIds);
        }
        incPropertyIds.removeAll(exitPropertyIds);
        if (CollectionUtils.isNotEmpty(incPropertyIds)) {
            BatchIdDto batchIdDto = idgeneratorService
                .getNextIdBatchBytypeName(BeanConstants.DEVICE_TYPE_PROPERTY_RELATION,
                    incPropertyIds.size()).pickDataThrowException();
            List<DeviceTypePropertyRelation> relations = new ArrayList<>();
            for (Long incPropertyId : incPropertyIds) {
                DeviceTypePropertyRelation relation = new DeviceTypePropertyRelation();
                relation.setId(batchIdDto.nextId());
                relation.setDeviceType(deviceType);
                relation.setDeviceTypePropertyId(incPropertyId);
                relations.add(relation);
            }
            mapper.batchInsert(relations);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetRelationByPropertyId(Long propertyId, List<Integer> deviceTypes) {
        BizAssert.notNull(propertyId, "propertyId must not be null");
        List<Integer> incDeviceTypes = new ArrayList<>(deviceTypes);
        //该设备类型已存在的属性Id
        List<Integer> exitDeviceTypes = mapper.listDeviceTypeByPropertyId(propertyId);
        //删除多余的属性Id列表
        List<Integer> decDeviceTypes = new ArrayList<>(exitDeviceTypes);
        decDeviceTypes.removeAll(deviceTypes);
        if (CollectionUtils.isNotEmpty(decDeviceTypes)) {
            mapper.deleteByDeviceTypeAndPropertyId(decDeviceTypes, Lists.newArrayList(propertyId));
        }
        incDeviceTypes.removeAll(exitDeviceTypes);
        if (CollectionUtils.isNotEmpty(incDeviceTypes)) {
            BatchIdDto batchIdDto = idgeneratorService
                .getNextIdBatchBytypeName(BeanConstants.DEVICE_TYPE_PROPERTY_RELATION,
                    deviceTypes.size()).pickDataThrowException();
            List<DeviceTypePropertyRelation> relations = new ArrayList<>();
            for (Integer incDeviceType : incDeviceTypes) {
                DeviceTypePropertyRelation relation = new DeviceTypePropertyRelation();
                relation.setId(batchIdDto.nextId());
                relation.setDeviceType(incDeviceType);
                relation.setDeviceTypePropertyId(propertyId);
                relations.add(relation);
            }
            mapper.batchInsert(relations);
        }
        return Boolean.TRUE;
    }
}
