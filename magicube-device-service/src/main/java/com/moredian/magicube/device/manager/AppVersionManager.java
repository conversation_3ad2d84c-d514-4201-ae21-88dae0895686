package com.moredian.magicube.device.manager;

import com.github.pagehelper.Page;
import com.moredian.bee.mybatis.domain.PaginationDomain;
import com.moredian.magicube.device.dao.entity.AppVersion;
import java.util.List;

/**
 * app版本信息
 *
 * <AUTHOR>
 */
public interface AppVersionManager {

    /**
     * 增加app版本信息
     *
     * @param appVersion
     * @return 主键
     */
    Long addAppVersion(AppVersion appVersion);

    /**
     * 修改app版本信息
     *
     * @param appVersion
     * @return
     */
    int updateAppVersion(AppVersion appVersion);

    /**
     * 修改app版本信息,只修改属性不为null的字段
     *
     * @param appVersion
     * @return 修改记录条数
     */
    int updateAppVersionSelective(AppVersion appVersion);

    /**
     * 根据ID删除app版本信息
     *
     * @param id
     * @return
     */
    int removeAppVersionById(Long id);

    /**
     * 根据id获取app版本信息
     *
     * @param id
     * @return
     */
    AppVersion getAppVersionById(Long id);

    /**
     * (added by zc)
     * 根据id获取app版本信息
     *
     * @param systemType  app适用系统类型
     * @param appType     app类型
     * @param versionCode 版本编码
     * @return
     */
    AppVersion getAppVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode);

    /**
     * 根据app适用系统类型、app类型获取最新的app版本信息
     *
     * @param systemType app适用系统类型
     * @param appType    app类型
     * @return
     */
    AppVersion getNewAppVersionBySCType(Integer systemType, Integer appType);

    /**
     * 根据app适用系统类型、app类型获取最新的允许马上升级的app版本信息
     *
     * @param systemType app适用系统类型
     * @param appType    app类型
     * @return
     */
    AppVersion getNewAppVersionBySCTypeAndIsActive(Integer systemType, Integer appType);

    /**
     * 分页查询app版本信息
     *
     * @param pagination
     * @param appVersion
     * @return
     */
    PaginationDomain<AppVersion> getPaginationAppVersion(PaginationDomain<AppVersion> pagination, AppVersion appVersion);

    /**
     * 获取app版本信息数量
     *
     * @param appVersion
     * @return
     */
    int getAppVersionCount(AppVersion appVersion);

    /**
     * 获取强制升级app版本数量
     *
     * @param appVersion
     * @return
     */
    int getEnforceUpdateAppVersionCount(AppVersion appVersion);

    int getLowVersionCount(Integer versionCode, Integer appType);

    AppVersion getAppVersionInfo(AppVersion appVersion);

    List<AppVersion> list();
}