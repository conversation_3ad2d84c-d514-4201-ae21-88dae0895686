package com.moredian.magicube.device.service.impl;

import com.alibaba.fastjson.JSON;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.common.model.msg.device.DeviceActiveMsg;
import com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeSimpleDto;
import com.moredian.magicube.device.dto.qrcode.AleadyActivateOrgDto;
import com.moredian.magicube.device.dto.qrcode.BindPersonnelRelationDto;
import com.moredian.magicube.device.dto.qrcode.DeviceChooseSpaceDTO;
import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.InsertNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.QrCodeActivateRecordDto;
import com.moredian.magicube.device.dto.qrcode.QrCodeActivateRecordResponse;
import com.moredian.magicube.device.dto.qrcode.SimpleActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.UpdateNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.UploadNetworkCodeStatusDTO;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant.PrivateCloudSwitchEnum;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.FunctionQrCodeManager;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.FunctionQrCodeService;
import com.moredian.magicube.device.service.QrCodeActivateRecordService;
import com.moredian.space.dto.account.InsertMemberTreeDTO;
import com.moredian.space.dto.device.BindSpaceTreeDto;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceMemberTreeService;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;
import com.moredian.space.service.v2.SpaceTreeDeviceRelationServiceV2;
import com.xier.sesame.common.utils.BeanUtils;
import com.xier.sesame.common.utils.JsonUtils;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Classname： QrCodeServiceImpl
 * @Date: 2023/1/5 3:41 下午
 * @Author: _AF
 * @Description:
 */
@SI
@Slf4j
public class FunctionQrCodeServiceImpl implements FunctionQrCodeService {

    @Autowired
    private FunctionQrCodeManager functionQrCodeManager;

    @SI
    private DeviceService deviceService;

    @SI
    private QrCodeActivateRecordService qrCodeActivateRecordService;

    @SI
    private SpaceMemberTreeService memberTreeService;

    @SI
    private SpaceTreeDeviceRelationServiceV2 treeDeviceRelationServiceV2;

    @Autowired
    private DeviceManager deviceManager;


    @SI
    private SpaceTreeService treeService;

    @Override
    public ServiceResponse<Long> createActivateQrCode(ActivateQrCodeCreateDto dto) {
        Long id = functionQrCodeManager.createActivateQrCode(dto);
        //私有化不需要展示上一次绑定的空间
        if (dto.getPrivateCloudSwitch().equals(PrivateCloudSwitchEnum.CLOSE.getValue())){
            InsertMemberTreeDTO insertMemberTreeDTO = new InsertMemberTreeDTO();
            insertMemberTreeDTO.setOrgId(dto.getOrgId());
            insertMemberTreeDTO.setMemberId(dto.getMemberId());
            insertMemberTreeDTO.setTreeId(dto.getTreeId());
            memberTreeService.bindSpace(insertMemberTreeDTO);
        }
        return new ServiceResponse<>(id);
    }

    @Override
    public ServiceResponse<ActivateQrCodeDto> getActivateQrCodeById(Long memberId, Long id) {
        return new ServiceResponse<>(functionQrCodeManager.getActivateQrCodeById(memberId, id));
    }

    @Override
    public ServiceResponse<ActivateQrCodeDto> getActivateQrCodeByOrgIdAndId(Long orgId, Long id) {
        return new ServiceResponse<>(
            functionQrCodeManager.getActivateQrCodeByOrgIdAndId(orgId, id));
    }

    @Override
    public ServiceResponse<List<ActivateQrCodeSimpleDto>> listByMemberId(
        Long memberId, Integer roleType) {
        return new ServiceResponse<>(functionQrCodeManager.listByMemberId(memberId, roleType, ActivateQrCodeConstant.AppletEnum.ACTIVATE_ASSISTANT.getType()));
    }

    @Override
    public ServiceResponse<List<ActivateQrCodeSimpleDto>> listHistoryOrgByMemberId(Long memberId, Integer roleType) {
        return new ServiceResponse<>(functionQrCodeManager.listHistoryOrgByMemberId(memberId, roleType, ActivateQrCodeConstant.AppletEnum.ACTIVATE_ASSISTANT.getType()));
    }

    @Override
    public ServiceResponse<List<QrCodeActivateRecordDto>> listActivateRecord(
        Long orgId, Long qrCodeId) {
        List<QrCodeActivateRecordDto> qrCodeActivateRecordDtoList = new ArrayList<>();
        List<QrCodeActivateRecordResponse> qrCodeActivateRecordResponseList = qrCodeActivateRecordService.listByQrCodeId(orgId, qrCodeId).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(qrCodeActivateRecordResponseList)) {
            List<String> deviceSns = qrCodeActivateRecordResponseList.stream().map(
                QrCodeActivateRecordResponse::getDeviceSn).distinct().collect(Collectors.toList());
            List<DeviceInfoDTO> deviceInfoDTOS = deviceService.listByDeviceSns(deviceSns).pickDataThrowException();
            Map<String, DeviceInfoDTO> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
                for (DeviceInfoDTO deviceInfoDTO : deviceInfoDTOS) {
                    map.put(deviceInfoDTO.getDeviceSn(), deviceInfoDTO);
                }
            }
            qrCodeActivateRecordDtoList = BeanUtils.copyListProperties(QrCodeActivateRecordDto.class, qrCodeActivateRecordResponseList);
            for (QrCodeActivateRecordDto qrCodeActivateRecordDto : qrCodeActivateRecordDtoList) {
                if (qrCodeActivateRecordDto.getDeviceType() != null) {
                    continue;
                }
                DeviceInfoDTO deviceInfoDTO = map.get(qrCodeActivateRecordDto.getDeviceSn());
                if (deviceInfoDTO != null){
                    qrCodeActivateRecordDto.setDeviceType(deviceInfoDTO.getDeviceType());
                }
            }
        }
        return new ServiceResponse<>(qrCodeActivateRecordDtoList);
    }

    @Override
    public ServiceResponse<List<AleadyActivateOrgDto>> listByAleadyActivateOrg(Long accountId) {
        List<AleadyActivateOrgDto> aleadyActivateOrgDtos = functionQrCodeManager.listByAleadyActivateOrg(accountId);
        return new ServiceResponse<>(aleadyActivateOrgDtos);
    }

    @Override
    public ServiceResponse<Integer> getPersonnelRelation(Long accountId) {
        return new ServiceResponse<>(functionQrCodeManager.getPersonnelRelation(accountId));
    }

    @Override
    public ServiceResponse<Boolean> bindPersonnelRelation(BindPersonnelRelationDto dto) {
        return new ServiceResponse<>(functionQrCodeManager.bindPersonnelRelation(dto));
    }

    @Override
    public ServiceResponse<Long> createNetworkQrCode(NetworkQrCodeCreateDto dto) {
        return new ServiceResponse<>(functionQrCodeManager.createNetworkQrCode(dto));
    }

    @Override
    public ServiceResponse<Long> insertNetworkInfo(InsertNetworkInfoDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(functionQrCodeManager.insertNetworkInfo(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> updateNetworkInfo(UpdateNetworkInfoDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(functionQrCodeManager.updateNetworkInfo(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deleteByNetworkId(Long orgId, Long id) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(functionQrCodeManager.deleteByNetworkId(orgId, id));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<NetworkQrCodeDto> getNetworkQrCodeById(Long orgId, Long id) {
        return new ServiceResponse<>(functionQrCodeManager.getNetworkQrCodeById(orgId, id));
    }

    @Override
    public ServiceResponse<NetworkQrCodeDto> getNewestNetWorkInfoByOrgId(Long orgId) {
        ServiceResponse<NetworkQrCodeDto> successResponse = ServiceResponse.createSuccessResponse();
        QrCodeActivateRecordResponse response = qrCodeActivateRecordService.getNewestRecordByOrgId(orgId).pickDataThrowException();
        if (response != null) {
            NetworkQrCodeDto networkQrCodeDto = functionQrCodeManager.getNetWorkInfoByQrCodeId(response.getQrCodeId());
            successResponse.setData(networkQrCodeDto);
            return successResponse;
        }
        successResponse.setData(null);
        return successResponse;
    }

    @Override
    public ServiceResponse<List<NetworkQrCodeDto>> listNetworkInfoByOrgId(Long orgId) {
        ServiceResponse<List<NetworkQrCodeDto>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<OrgNetwork> orgNetworks = functionQrCodeManager.listNetworkInfoByOrgId(orgId);
        if (CollectionUtils.isEmpty(orgNetworks)){
            serviceResponse.setData(new ArrayList<>());
            return serviceResponse;
        }
        List<NetworkQrCodeDto> list = new ArrayList<>();
        for (OrgNetwork orgNetwork : orgNetworks){
            NetworkQrCodeDto networkQrCodeDto = new NetworkQrCodeDto();
            networkQrCodeDto.setId(orgNetwork.getId());
            networkQrCodeDto.setConnectType(orgNetwork.getConnectType());
            networkQrCodeDto.setNetworkType(orgNetwork.getNetworkType());
            networkQrCodeDto.setCableStatic(orgNetwork.getCableStatic() == null ? null : JSON
                .parseObject(orgNetwork.getCableStatic(), CableStatic.class));
            networkQrCodeDto.setWifiInfo(orgNetwork.getWifiInfo() == null ? null : JSON.parseObject(orgNetwork.getWifiInfo(), WifiInfo.class));
            networkQrCodeDto.setGmtCreate(orgNetwork.getGmtCreate());
            networkQrCodeDto.setGmtModify(orgNetwork.getGmtModify());
            list.add(networkQrCodeDto);
        }
        serviceResponse.setData(list);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> judgeQrCodeActivatedDevice(Long orgId, Long qrCodeId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(qrCodeId, "qrCodeId must not be null");
        ServiceResponse<Boolean> successResponse = ServiceResponse.createSuccessResponse();
        ServiceResponse<List<QrCodeActivateRecordResponse>> serviceResponse = qrCodeActivateRecordService.listByQrCodeId(orgId, qrCodeId);
        if (serviceResponse.isSuccess() && CollectionUtils.isNotEmpty(serviceResponse.getData())) {
            successResponse.setData(Boolean.TRUE);
            return successResponse;
        }
        successResponse.setData(Boolean.FALSE);
        return successResponse;
    }

    @Override
    public ServiceResponse<SimpleActivateQrCodeDto> getSimpleQrCodeByQrCodeId(Long qrCodeId) {
        BizAssert.notNull(qrCodeId, "qrCodeId must not be null");
        return new ServiceResponse<>(functionQrCodeManager.getSimpleQrCodeByQrCodeId(qrCodeId));
    }

    @Override
    public ServiceResponse<Boolean> uploadNetworkCodeStatus(UploadNetworkCodeStatusDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(functionQrCodeManager.uploadNetworkCodeStatus(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deviceChooseSpace(DeviceChooseSpaceDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        BindSpaceTreeDto bindSpaceTreeDto = new BindSpaceTreeDto();
        bindSpaceTreeDto.setOrgId(dto.getOrgId());
        bindSpaceTreeDto.setDeviceSource(1);
        bindSpaceTreeDto.setTreeId(dto.getPropertyTreeNodeId() == null ?
                dto.getTreeId() : dto.getPropertyTreeNodeId());
        bindSpaceTreeDto.setDirection(dto.getDirection());
        DeviceInfoDTO deviceInfoDTO = deviceService.getByDeviceSn(dto.getDeviceSn()).pickDataThrowException();
        if (deviceInfoDTO == null){
            serviceResponse.setData(Boolean.FALSE);
            return serviceResponse;
        }
        bindSpaceTreeDto.setDeviceId(String.valueOf(deviceInfoDTO.getDeviceId()));
        treeDeviceRelationServiceV2.bindSpace(bindSpaceTreeDto);

        //修改位置和机构Id（兼容多租户逻辑，非多租户不需要修改,如果租户视角激活到园区，传入的orgId是对应协作体Id）
        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
        updateDeviceDTO.setOrgId(dto.getOrgId());
        updateDeviceDTO.setDeviceId(deviceInfoDTO.getDeviceId());
        updateDeviceDTO.setPosition(dto.getPosition());
        serviceResponse.setData(deviceService.update(updateDeviceDTO).pickDataThrowException());

        // 如果是钉钉SDK激活，在选择空间完成后才发送设备激活消息(选完空间才算整体流程结束)
        DeviceActiveMsg deviceActiveMsg = new DeviceActiveMsg();
        org.springframework.beans.BeanUtils.copyProperties(deviceInfoDTO, deviceActiveMsg);
        deviceActiveMsg.setTpDevId(deviceInfoDTO.getTpId());
        deviceActiveMsg.setTimeStamp(System.currentTimeMillis());
        TreeDTO treeDTO = treeService.getById(dto.getTreeId(),dto.getOrgId()).pickDataThrowException();
        if (treeDTO != null) {
            Integer sceneType = treeService.getSceneTypeByOrgIdAndTreeId(treeDTO.getOrgId(), treeDTO.getTreeId()).pickDataThrowException();
            deviceActiveMsg.setSceneType(sceneType == null ? 0 : sceneType);
            deviceActiveMsg.setSpaceType(treeDTO.getTags().get(0).getSpaceType());
        }
        deviceActiveMsg.setTreeId(dto.getTreeId());
        EventBus.publish(deviceActiveMsg);
        log.info("设备绑定空间-激活消息发送成功：" + JsonUtils.toJson(deviceActiveMsg));
        return serviceResponse;
    }
}
