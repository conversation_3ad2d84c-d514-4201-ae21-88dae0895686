package com.moredian.magicube.device.config;

/**
 * @Classname： FunctionQrCodeConstants
 * @Date: 2023/2/21 10:33 上午
 * @Author: _AF
 * @Description:
 */
public class FunctionQrCodeConstants {

    /**
     * 默认content
     */
    public static final String DEFAULT_CONTENT = "{}";

    public static final Integer DEFAULT_BUFFER_CAPACITY = 400;

    public static final String FF_01 = "255.1";

    public static final String FF_O2 = "255.2";

    public static final String FF_03 = "255.3";

    public static final String FF_04 = "255.4";

    public static final String FF_05 = "255.5";

    public static final String FF_06 = "255.6";

    public static final String FF_07 = "255.7";

    public static final String FF_08 = "255.8";

    public static final String FF_09 = "255.9";

    public static final String FF_0A = "255.10";

    public static final String FF_0B = "255.11";

    public static final String FF_0C = "255.12";

    public static final String FF_0D = "255.13";

    public static final String FF_0E = "255.14";

    public static final String FF_0F = "255.15";

    public static final Long DEFUALT_TIME = 86400 * 1000L;

    public static final String RELEASE_TYPE = "MOREDIANR";

    public static final String DEV_TYPE = "MOREDIAN";

}
