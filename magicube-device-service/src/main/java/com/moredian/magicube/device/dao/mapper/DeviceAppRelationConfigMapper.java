package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceAppRelationConfigMapper {

    /**
     *
     * 条件查询列表
     * @param deviceAppRelationConfigDTO dto
     * @return 符合条件记录
     */
    List<DeviceAppRelationConfig> selectByConditions(QueryDeviceAppRelationConfigDTO deviceAppRelationConfigDTO);

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    List<DeviceAppRelationConfig> listAll();
    /**
     * 新增
     * @param config
     * @return
     */
    int insert(DeviceAppRelationConfig config);

    /**
     * 删除
     * @param id
     * @return
     */
    int deleteById(@Param("id") Long id);

    /**
     *  更新
     * @param config
     * @return
     */
    int updateById(DeviceAppRelationConfig config);

    /**
     * 更新不为空的字段
     * @param config
     * @return
     */
    int updateSelectiveById(DeviceAppRelationConfig config);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    DeviceAppRelationConfig selectById(@Param("id") Long id);

    /**
     * 条件查询
     * @param spaceType
     * @param appType
     * @param versionCode
     * @return
     */
    List<DeviceAppRelationConfig> selectBySpaceTypeAndAppTypeAndVersionCode(
            @Param("spaceType") Integer spaceType,
            @Param("appType") Integer appType,
            @Param("versionCode") Integer versionCode
    );

    List<DeviceAppRelationConfig> selectByAppTypeAndVersionCode(@Param("appType")Integer appType,@Param("versionCode")Integer versionCode);

}
