package com.moredian.magicube.device.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.device.dao.entity.PeopleNumberStatistic;
import com.moredian.magicube.device.dao.entity.dahua.CameraDeviceInfo;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.dto.dahua.CameraPeopleNumDTO;
import com.moredian.magicube.device.dto.dahua.CameraPeopleNumDTO.DataList;
import com.moredian.magicube.device.manager.PeopleNumberStatisticManager;
import com.moredian.magicube.device.manager.dahua.CameraDeviceManager;
import com.moredian.magicube.device.service.dahua.CameraDeviceService;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class CameraDeviceServiceImpl implements CameraDeviceService {

    @Autowired
    private CameraDeviceManager cameraDeviceManager;

    @Resource
    private PeopleNumberStatisticManager peopleNumberStatisticManager;

    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;

    @Override
    public ServiceResponse<List<CameraDeviceInfoDTO>> list() {
        ServiceResponse<List<CameraDeviceInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<CameraDeviceInfo> devices = cameraDeviceManager.list();
        List<CameraDeviceInfoDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(devices)) {
            for (CameraDeviceInfo device : devices) {
                CameraDeviceInfoDTO cameraDeviceInfoDTO = new CameraDeviceInfoDTO();
                BeanUtils.copyProperties(device, cameraDeviceInfoDTO);
                list.add(cameraDeviceInfoDTO);
            }
        }
        serviceResponse.setData(list);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<CameraDeviceInfoDTO> getByIpAndPort(String ip, int port) {
        ServiceResponse<CameraDeviceInfoDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        CameraDeviceInfo cameraDeviceInfo = cameraDeviceManager.getByIpAndPort(ip, port);
        CameraDeviceInfoDTO cameraDeviceInfoDTO = new CameraDeviceInfoDTO();
        if (cameraDeviceInfo != null) {
            BeanUtils.copyProperties(cameraDeviceInfo, cameraDeviceInfoDTO);
        }
        serviceResponse.setData(cameraDeviceInfoDTO);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<CameraPeopleNumDTO> getCameraPeopleNum(Long orgId, String deviceId) {
        BizAssert.notNull(orgId, "orgId must be not null");
        BizAssert.notBlank(deviceId, "deviceId must be not empty");

        // 先根据某一台设备查询空间id
        TreeDeviceRelationDTO deviceRelationDTO = spaceTreeDeviceRelationService.getByOrgIdAndDeviceId(
            orgId, deviceId).pickDataThrowException();

        BizAssert.notNull(deviceRelationDTO, "设备未绑定空间");

        List<Long> treeId = Lists.newArrayList(deviceRelationDTO.getTreeId());
        List<Integer> deviceTypeList = Lists.newArrayList(DeviceType.BOARD_DAHUA_CAMERA.getValue());

        // 查询这个空间下所有的大华摄像头
        List<TreeDeviceRelationDTO> deviceRelationDTOS = spaceTreeDeviceRelationService.listByTreeIdAndDeviceType(
            orgId, treeId, deviceTypeList).pickDataThrowException();

        ServiceResponse<CameraPeopleNumDTO> response = ServiceResponse.createSuccessResponse();
        CameraPeopleNumDTO data = new CameraPeopleNumDTO();
        response.setData(data);
        if (ObjectUtil.isEmpty(deviceRelationDTOS)) {
            data.setHasDevice(Boolean.FALSE);
            return response;
        }
        data.setHasDevice(Boolean.TRUE);
        List<DataList> dataList = Lists.newArrayList();
        data.setDataList(dataList);
        List<Long> deviceIds = deviceRelationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId)
            .collect(Collectors.toList());

        // 大华摄像头人数信息
        List<PeopleNumberStatistic> peopleNumberList = peopleNumberStatisticManager.listByOrgIdAndDeviceId(
            orgId, deviceIds);
        peopleNumberList.forEach(e-> {
            DataList item = new DataList();
            item.setDeviceSn(e.getDeviceSn());
            item.setPeopleNum(e.getInsidePeopleNum());
            dataList.add(item);
        });
        return response;
    }
}
