package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.RandomUtil;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.ActivationStatus;
import com.moredian.magicube.common.enums.ActivityRecCoreType;
import com.moredian.magicube.common.enums.DeviceOperEventType;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.common.enums.VerifyChannel;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.common.model.msg.DeviceUnbindMsg;
import com.moredian.magicube.common.model.msg.RefreshDeviceConfigMsg;
import com.moredian.magicube.core.enums.CommonErrorCode;
import com.moredian.magicube.core.org.model.AreaInfo;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.response.OrgVerifyConfigDTO;
import com.moredian.magicube.core.org.service.AreaService;
import com.moredian.magicube.core.org.service.OrgCollaborationService;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.constant.HiveMessage;
import com.moredian.magicube.device.constant.ModuleType;
import com.moredian.magicube.device.constant.StatusCode;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceAccount;
import com.moredian.magicube.device.dao.entity.DeviceActiveState;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dao.mapper.DeviceActiveStateMapper;
import com.moredian.magicube.device.dto.activate.ActivateDeviceResultDTO;
import com.moredian.magicube.device.dto.activate.ActivationDeviceInfoDTO;
import com.moredian.magicube.device.dto.activate.GenerateQrCodeDTO;
import com.moredian.magicube.device.dto.activate.QrCodeDTO;
import com.moredian.magicube.device.dto.activate.QrCodeStatusDTO;
import com.moredian.magicube.device.dto.activate.QueryActivateInfoDTO;
import com.moredian.magicube.device.dto.activate.QueryActivationStatusDTO;
import com.moredian.magicube.device.dto.activate.QueryQrCodeStatusDTO;
import com.moredian.magicube.device.dto.activate.ThirdPartyDeviceInfoDTO;
import com.moredian.magicube.device.dto.activate.UpdateQrCodeStatusDTO;
import com.moredian.magicube.device.dto.log.DeviceLogDTO;
import com.moredian.magicube.device.dto.peripherals.UpdatePeripheralsDTO;
import com.moredian.magicube.device.enums.DeviceActiveStateEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.DeviceLogHelper;
import com.moredian.magicube.device.manager.AccessoryManager;
import com.moredian.magicube.device.manager.ActivityManager;
import com.moredian.magicube.device.manager.DeviceAccountManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.PeripheralsManager;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import com.moredian.magicube.device.utils.Coder;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.xier.guard.accessKey.dto.AccessKeyDto;
import com.xier.guard.accessKey.service.UserAccessKeyService;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActivityManagerImpl implements ActivityManager {

    @Autowired
    private WhiteDeviceManager whiteDeviceManager;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceAccountManager deviceAccountManager;

    @Autowired
    private DeviceLogHelper deviceLogHelper;

    @Autowired
    private AccessoryManager accessoryManager;

    @Autowired
    private PeripheralsManager peripheralsManager;

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    @Autowired
    private DeviceActiveStateMapper deviceActiveStateMapper;

    @Resource
    private RedissonLockComponent redissonLockComponent;

    @SI
    private OrgService orgService;

    @SI
    private OrgCollaborationService orgCollaborationService;

    @SI
    protected AreaService areaService;

    @SI
    private SpaceTreeDeviceRelationService treeDeviceRelationService;

    @SI
    private UserAccessKeyService userAccessKeyService;

    @SI
    private IdgeneratorService idgeneratorService;

    private static final Integer DEACTIVIATED = 0;

    @Override
    public QrCodeDTO generateQrCode(GenerateQrCodeDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getDeviceSn(), "deviceId must not be null");
        BizAssert.notNull(dto.getCheckCode(), "checkCode must not be null");
        // 1.判断是否合法设备
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(dto.getDeviceSn());
        BizAssert.notNull(inventoryDevice, DeviceErrorCode.DEVICE_WHITE_LIST_NOT_EXIST,
            DeviceErrorCode.DEVICE_WHITE_LIST_NOT_EXIST.getMessage());

        //device has mulit macaddress. need to set the real work macAddress to activyInfo
        String realMacAddress = "";
        // 2.老版本的没有内置秘钥，需要验证checkCode
        if (dto.getIsCheck()) {
            realMacAddress = checkCode(dto.getCheckCode(), inventoryDevice);
        }

        // 3.生成二维码
        String qrCode = RandomUtil.getUUID26() + RandomUtil.getRandom32String(10);
        ActivationDeviceInfoDTO activationDeviceInfoDTO = new ActivationDeviceInfoDTO();
        activationDeviceInfoDTO.setMacAddress(realMacAddress);
        activationDeviceInfoDTO.setPrivateKey(inventoryDevice.getPrivateKey());
        activationDeviceInfoDTO.setDeviceSn(inventoryDevice.getSerialNumber());
        activationDeviceInfoDTO
            .setVerifyChannel(this.activityRecCoreTypeToVerifyChannel(dto.getRegCoreType()));
        activationDeviceInfoDTO.setVerifyArithmetic(dto.getRegArithmetic());

        // 4.放入缓存中
        QrCodeDTO qrCodeDTO = new QrCodeDTO();
        qrCodeDTO.setQrCode(qrCode);
        qrCodeDTO.setIsOpenPlat(inventoryDevice.getIsOpenPlat());
        Device device = deviceManager.getByDeviceSn(inventoryDevice.getSerialNumber());
        log.info("生成二维码时设备信息device:{}", device);
        redissonCacheComponent.setObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_KEY,
            activationDeviceInfoDTO, HiveConst.ACTIVE_DEVICE_TIME_OUT);
        if (device == null) {
            // 扫码操作
            redissonCacheComponent.setObjectCache(qrCode +
                    HiveConst.MEMCACHED_EQUIPMENT_QRCODE_STATUS_KEY, StatusCode.NOT_SCAN,
                HiveConst.ACTIVE_DEVICE_TIME_OUT);
            redissonCacheComponent.setObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_SN,
                dto.getDeviceSn(), HiveConst.ACTIVE_DEVICE_TIME_OUT);
            qrCodeDTO.setGenerate(true);
        } else {
            // 免扫码操作
            redissonCacheComponent.setObjectCache(qrCode +
                    HiveConst.MEMCACHED_EQUIPMENT_QRCODE_STATUS_KEY,
                StatusCode.ACTIVE_WAIT, HiveConst.ACTIVE_DEVICE_TIME_OUT);
            redissonCacheComponent.setObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_SN,
                dto.getDeviceSn(), HiveConst.ACTIVE_DEVICE_TIME_OUT);
            qrCodeDTO.setGenerate(false);
            UpdateQrCodeStatusDTO updateQrCodeStatusDTO = new UpdateQrCodeStatusDTO();
            updateQrCodeStatusDTO.setOrgId(device.getOrgId());
            updateQrCodeStatusDTO.setQrCode(qrCode);
            updateQrCodeStatus(updateQrCodeStatusDTO);
        }
        log.debug("succeed to generate qrcode {},device sn is {}.", qrCodeDTO.getQrCode(),
            dto.getDeviceSn());
        return qrCodeDTO;
    }

    @Override
    public QrCodeStatusDTO getStatusByQrCode(QueryQrCodeStatusDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getQrCode(), "qrCode must not be null");
        String qrCode = dto.getQrCode();
        Integer status = (Integer) redissonCacheComponent.getObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_STATUS_KEY);
        QrCodeStatusDTO qrCodeStatusDTO = new QrCodeStatusDTO();
        if (status == null) {
            qrCodeStatusDTO.setMessage(HiveMessage.QRCODE_EXPIRED);
            qrCodeStatusDTO.setStatusCode(StatusCode.EXPIRED);
        } else if (status == StatusCode.SCAN) {
            redissonCacheComponent.delStrCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_STATUS_KEY);
            qrCodeStatusDTO.setMessage(HiveMessage.OVER_SCAN);
            qrCodeStatusDTO.setStatusCode(StatusCode.SCAN);
        } else {
            qrCodeStatusDTO.setMessage(HiveMessage.NOT_SCAN);
            qrCodeStatusDTO.setStatusCode(StatusCode.NOT_SCAN);
        }
        return qrCodeStatusDTO;
    }

    @Override
    public QrCodeStatusDTO updateQrCodeStatus(UpdateQrCodeStatusDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getQrCode(), "qrCode must not be null");
        String qrCode = dto.getQrCode();
        ActivationDeviceInfoDTO activationDeviceInfoDTO = (ActivationDeviceInfoDTO) redissonCacheComponent
            .getObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_KEY);
        QrCodeStatusDTO qrCodeStatusDTO = new QrCodeStatusDTO();
        if (activationDeviceInfoDTO == null) {
            qrCodeStatusDTO.setMessage(HiveMessage.QRCODE_EXPIRED);
            qrCodeStatusDTO.setStatusCode(StatusCode.EXPIRED);
            return qrCodeStatusDTO;
        }

        Long orgId = dto.getOrgId();
        Integer deviceVerifyChannel = activationDeviceInfoDTO.getVerifyChannel();
        // 存在设备使用的识别通道
        if (deviceVerifyChannel != null) {
            OrgVerifyConfigDTO currentVerifyConfig = orgService.getVerifyConfig(orgId)
                .pickDataThrowException();
            Integer orgVerifyChannel = currentVerifyConfig.getVerifyChannel();
            // 已经设置过识别通道， 并且识别通道不一致，直接返回扫码失败
            if ((VerifyChannel.NO_VERIFY.getValue() != orgVerifyChannel) && !deviceVerifyChannel
                .equals(orgVerifyChannel)) {
                BizAssert.notNull(null, DeviceErrorCode.DEVICE_ACTIVE_CHANGE_VERIFYCHANNLE,
                    DeviceErrorCode.DEVICE_ACTIVE_CHANGE_VERIFYCHANNLE.getMessage());
            }
        }

        OrgInfo orgInfo = orgService
            .getOrgInfo(orgId, Collections.singletonList(OrgStatus.USABLE.getValue()))
            .pickDataThrowException();
        BizAssert.isTrue(orgInfo != null, DeviceErrorCode.ORG_ID_VALIDATE_GET_ERROR,
            DeviceErrorCode.ORG_ID_VALIDATE_GET_ERROR.getMessage());
        Integer cityId = orgInfo.getCityId();
        if (cityId != null) {
            AreaInfo areaInfo = areaService.getAreaByCode(cityId).pickDataThrowException();
            if (areaInfo != null) {
                activationDeviceInfoDTO.setCityName(areaInfo.getAreaName());
            }
        } else {
            activationDeviceInfoDTO.setCityName("杭州市");
        }
        activationDeviceInfoDTO.setModelType(ModuleType.ATTENCE.getValue());
        activationDeviceInfoDTO.setOrgId(dto.getOrgId());
        activationDeviceInfoDTO.setOrgTpType(orgInfo.getTpType());
        activationDeviceInfoDTO.setOrgTpId(orgInfo.getTpId());
        redissonCacheComponent.setObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_KEY,
            activationDeviceInfoDTO, HiveConst.ACTIVE_DEVICE_TIME_OUT);
        redissonCacheComponent.setObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_STATUS_KEY,
                StatusCode.SCAN, HiveConst.ACTIVE_DEVICE_TIME_OUT);
        redissonCacheComponent
            .setObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.ACTIVE_WAIT, HiveConst.ACTIVE_DEVICE_TIME_OUT);
        if (activationDeviceInfoDTO.getDeviceSn() != null) {
            qrCodeStatusDTO.setSn(activationDeviceInfoDTO.getDeviceSn());
        } else {
            String deviceSn = redissonCacheComponent
                .getStrCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_SN);
            qrCodeStatusDTO.setSn(deviceSn);
        }
        qrCodeStatusDTO.setMessage(HiveMessage.SUCCESS);
        qrCodeStatusDTO.setStatusCode(StatusCode.SCAN_SUCCESS);
        log.debug("update qrCode {},device orgId is {},statusCode is {}.", dto.getQrCode(),
            dto.getOrgId(), qrCodeStatusDTO.getStatusCode());
        return qrCodeStatusDTO;
    }

    @Override
    public QrCodeStatusDTO updateQrCodeStatusBySn(Long orgId, Long accountId, String deviceSn) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(accountId, "accountId must not be null");
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        DeviceAccount deviceAccount = new DeviceAccount();
        deviceAccount.setAccountId(accountId);
        deviceAccount.setSn(deviceSn);
        deviceAccount.setStatus(YesNoFlag.YES.getValue());
        List<DeviceAccount> list = deviceAccountManager.queryByCond(deviceAccount);
        BizAssert.isTrue(CollectionUtils.isNotEmpty(list), DeviceErrorCode.ACCOUNT_DEVICE,
            DeviceErrorCode.ACCOUNT_DEVICE.getMessage());
        String qrCode = redissonCacheComponent.getStrCache(deviceSn + HiveConst.ACTIVE_SN);
        if (qrCode == null) {
            QrCodeStatusDTO qrCodeStatusDTO = new QrCodeStatusDTO();
            qrCodeStatusDTO.setMessage(HiveMessage.QRCODE_EXPIRED);
            qrCodeStatusDTO.setStatusCode(StatusCode.EXPIRED);
            return qrCodeStatusDTO;
        }
        UpdateQrCodeStatusDTO dto = new UpdateQrCodeStatusDTO();
        dto.setOrgId(orgId);
        dto.setQrCode(qrCode);
        return updateQrCodeStatus(dto);
    }

    @Override
    public QueryActivationStatusDTO getActivityStatusByQrCode(String qrCode) {
        BizAssert.notNull(qrCode, "qrCode must not be null");
        Integer status = (Integer) redissonCacheComponent
            .getObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_ACTIVE_STATUS_KEY);
        log.info("根据二维码获取激活状态，二维码状态status:{}", status);
        QueryActivationStatusDTO dto = new QueryActivationStatusDTO();
        if (status == null) {
            dto.setMessage(HiveMessage.QRCODE_EXPIRED);
            dto.setStatusCode(StatusCode.ACTIVE_EXPIRED);
        } else if (status == StatusCode.ACTIVE_SUCCESS) {
            ActivationDeviceInfoDTO activationDeviceInfoDTO = (ActivationDeviceInfoDTO) redissonCacheComponent
                .getObjectCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_KEY);
            log.info("根据二维码获取激活状态，激活设备信息activeDeviceInfoDTO:{}", activationDeviceInfoDTO);
            redissonCacheComponent.delStrCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_QRCODE_KEY);
            redissonCacheComponent.delStrCache(qrCode + HiveConst.MEMCACHED_EQUIPMENT_ACTIVE_STATUS_KEY);
            if (activationDeviceInfoDTO.getDeviceSn() != null) {
                Device device = deviceManager.getByDeviceSn(activationDeviceInfoDTO.getDeviceSn());
                if (null != device) {
                    dto.setDeviceId(device.getDeviceId());
                }
            }
            dto.setMessage(HiveMessage.OVER_ACTIVE);
            dto.setStatusCode(StatusCode.ACTIVE_SUCCESS);
            dto.setSerialNumber(activationDeviceInfoDTO.getDeviceSn());
        } else if (status == StatusCode.ACTIVE_FAIL) {
            dto.setMessage(HiveMessage.FAIL);
            dto.setStatusCode(StatusCode.ACTIVE_FAIL);
        } else {
            dto.setMessage(HiveMessage.NOT_ACTIVE);
            dto.setStatusCode(StatusCode.ACTIVE_WAIT);
        }
        return dto;
    }

    @Override
    public QueryActivationStatusDTO getThirdPartyActivityStatusByTpId(String thirdPartyDeviceId) {
        BizAssert.notNull(thirdPartyDeviceId, "thirdPartyDeviceId must not be null");
        QueryActivationStatusDTO dto = new QueryActivationStatusDTO();
        Integer status = (Integer) redissonCacheComponent
            .getObjectCache(thirdPartyDeviceId + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY);
        log.info("根据第三方设备id获取激活状态，第三方设备deviceId:{},状态status:{}", thirdPartyDeviceId, status);
        if (status == null) {
            dto.setMessage(HiveMessage.DEVICE_NOT_RECORDED);
            dto.setStatusCode(StatusCode.DEVICE_NOT_RECORDED);
        } else {
            if (status == StatusCode.THIRD_PARTY_ACTIVE_SUCCESS) {
                redissonCacheComponent.delStrCache(thirdPartyDeviceId
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY);
                ActivationDeviceInfoDTO activationDeviceInfoDTO = (ActivationDeviceInfoDTO) redissonCacheComponent.getObjectCache(
                    thirdPartyDeviceId + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_INFO_KEY);
                if (activationDeviceInfoDTO != null) {
                    dto.setDeviceId(activationDeviceInfoDTO.getDeviceId());
                    dto.setSerialNumber(activationDeviceInfoDTO.getDeviceSn());
                    dto.setDeviceSn(activationDeviceInfoDTO.getDeviceSn());
                }
                dto.setMessage(HiveMessage.OVER_ACTIVE);
                dto.setStatusCode(StatusCode.THIRD_PARTY_ACTIVE_SUCCESS);
            } else if (status == StatusCode.DEVICE_NOT_AUTHORIZATION){
                dto.setStatusCode(StatusCode.DEVICE_NOT_AUTHORIZATION);
                dto.setMessage(HiveMessage.DEVICE_NOT_AUTHORIZATION);
            } else {
                dto.setMessage(HiveMessage.NOT_ACTIVE);
                dto.setStatusCode(StatusCode.THIRD_PARTY_ACTIVE_WAIT);
            }
        }
        return dto;
    }

    @Override
    @Transactional
    public Boolean updateThirdPartyDeviceInfo(ThirdPartyDeviceInfoDTO dto) {
        InventoryDevice condition = new InventoryDevice();
        condition.setSerialNumber(dto.getDeviceSn());
        condition.setOrgId(dto.getOrgId());
        condition.setThirdDeviceId(dto.getThirdDeviceId());
        boolean updateResult = whiteDeviceManager.update(condition);
        if (updateResult) {
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.THIRD_PARTY_ACTIVE_WAIT, HiveConst.THIRD_PARTY_TIME_OUT);
        }
        log.debug("Callback Update third party device,orgId is {},deviceSn is {}, deviceId is {},result is {}", dto.getOrgId(), dto.getDeviceSn(), dto.getThirdDeviceId(), updateResult);
        return updateResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unbindDevice(String deviceSn) {
        BizAssert.notBlank(deviceSn, "deviceSn must not be null");
        // 判断是否合法设备
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(deviceSn);
        BizAssert.isTrue(inventoryDevice != null, DeviceErrorCode.NO_EXIST_EQUIPMENT,
            DeviceErrorCode.NO_EXIST_EQUIPMENT.getMessage());
        Device oldDevice = deviceManager.getByDeviceSn(deviceSn);
        if (oldDevice != null) {
            boolean deleteResult = deviceManager.deleteById(oldDevice.getOrgId(), oldDevice.getDeviceId());
            ErrorContext errorContext = new ErrorContext("FD0020180305", "设备解绑失败");
            BizAssert.isTrue(deleteResult, errorContext, errorContext.getMessage());
            log.info("############^^^^^^^^^^##########unbind remove activated device successfully");

            //清除激活成功信息
            InventoryDevice di = new InventoryDevice();
            di.setSerialNumber(oldDevice.getDeviceSn());
            di.setActivityStatus(DEACTIVIATED);
            whiteDeviceManager.unbindDevice(di);

            // 异步添加设备解绑日志
            DeviceLogDTO deviceLogDTO = new DeviceLogDTO();
            deviceLogDTO.setOrgId(oldDevice.getOrgId());
            deviceLogDTO.setDeviceId(oldDevice.getDeviceId());
            deviceLogDTO.setDeviceSn(oldDevice.getDeviceSn());
            deviceLogDTO.setDeviceType(oldDevice.getDeviceType());
            deviceLogDTO.setEnvenType(DeviceOperEventType.UNBIND_DEVICE.getValue());
            deviceLogDTO.setEnvenDesc(DeviceOperEventType.UNBIND_DEVICE.getDesc());
            deviceLogDTO.setOperTime(System.currentTimeMillis());
            deviceLogHelper.asyncInsertActiveDeviceLog(deviceLogDTO);

            // 断开此设备下的外设连接, try ... catch 为了不影响设备正常的设备解绑服务
            try {
                accessoryManager.unbindAccessoryByOrgIdAndDeviceSn(oldDevice.getOrgId(), deviceSn);
            } catch (Exception e) {
                log.error("设备sn{}下外设连接断开失败，exception: {}", deviceSn, e);
            }

            try {
                UpdatePeripheralsDTO peripheralsDto = new UpdatePeripheralsDTO();
                peripheralsDto.setOrgId(oldDevice.getOrgId());
                peripheralsDto.setDeviceSn(deviceSn);
                peripheralsManager.disConnect(peripheralsDto);
            } catch (Exception e) {
                log.error("设备sn{}下测温外设连接断开失败，exception: {}", deviceSn, e);
            }
            TreeDeviceRelationDTO relationDTO = treeDeviceRelationService
                    .getByOrgIdAndDeviceId(oldDevice.getOrgId(), String.valueOf(oldDevice.getDeviceId())).pickDataThrowException();

            // 发送设备解绑消息
            DeviceUnbindMsg deviceUnbindMsg = new DeviceUnbindMsg();
            deviceUnbindMsg.setDeviceId(oldDevice.getDeviceId());
            deviceUnbindMsg.setDeviceSn(oldDevice.getDeviceSn());
            deviceUnbindMsg.setDeviceType(oldDevice.getDeviceType());
            if (StringUtils.isNotBlank(oldDevice.getPosition())) {
                deviceUnbindMsg.setPosition(oldDevice.getPosition());
            }
            if (relationDTO != null) {
                deviceUnbindMsg.setTreeId(relationDTO.getTreeId());
            }
            deviceUnbindMsg.setName(oldDevice.getDeviceName());
            deviceUnbindMsg.setOrgId(oldDevice.getOrgId());
            deviceUnbindMsg.setTimeStamp(System.currentTimeMillis());
            log.info(" --------> EventBus.publish(DeviceUnbindMsg) >>> msg={}",
                JsonUtils.toJson(deviceUnbindMsg));
            EventBus.publish(deviceUnbindMsg);
        } else {
            // 断开此设备下的外设连接, try ... catch 为了不影响设备正常的设备解绑服务
            try {
                accessoryManager.unbindAccessoryByOrgIdAndDeviceSn(oldDevice.getOrgId(), deviceSn);
            } catch (Exception e) {
                log.error("设备sn{}下外设连接断开失败，exception: {}", deviceSn, e);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public ActivateDeviceResultDTO getActivateInfo(QueryActivateInfoDTO dto) {
        BizAssert.notNull(dto.getDeviceSn(), "deviceSn must not be null");
        ActivateDeviceResultDTO activateDeviceResultDTO = null;
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(dto.getDeviceSn());
        if (inventoryDevice == null) {
            return null;
        }
        Device device = deviceManager.getByDeviceSn(dto.getDeviceSn());
        if (device == null) {
            return null;
        }

        //设备已经激活 返回激活信息
        Long orgId = inventoryDevice.getOrgId();
        Integer status = inventoryDevice.getActivityStatus();
        if (orgId != null && ActivationStatus.ACTIVATED.getValue() == status) {
            activateDeviceResultDTO = new ActivateDeviceResultDTO();
            com.xier.sesame.common.rpc.ServiceResponse<AccessKeyDto> sr = userAccessKeyService
                .issuDeviceAccessKeyAutoRevoke(dto.getDeviceSn());
            // 判断激活码是否有效
            BizAssert.isTrue((sr != null && sr.isSuccess() && sr.isExistData()),
                DeviceErrorCode.ISSU_DEVICE_ACCESS_KEY_FAIL,
                DeviceErrorCode.ISSU_DEVICE_ACCESS_KEY_FAIL.getMessage());
            activateDeviceResultDTO.setAccessKeySecret(sr.getData().getAccessKeySecret());
            activateDeviceResultDTO.setOrgId(orgId);
            activateDeviceResultDTO.setEquipmentType(device.getDeviceType());
            activateDeviceResultDTO.setSerialNumber(inventoryDevice.getSerialNumber());
            OrgInfo orgInfo = orgService
                .getOrgInfo(orgId, Collections.singletonList(OrgStatus.USABLE.getValue()))
                .pickDataThrowException();
            //如果机构不存在，则查协作体（兼容园区多租户逻辑）
            if (orgInfo == null) {
                orgInfo = orgCollaborationService.getOrgInfoByCollaborationOrgId(orgId)
                    .pickDataThrowException();
            }
            activateDeviceResultDTO.setOrgTpType(orgInfo.getTpType());
            activateDeviceResultDTO.setOrgTpId(orgInfo.getTpId());
            log.debug("getActivateInfo successfully,device existed,sn:{},checkCode:{}.",
                dto.getDeviceSn(), dto.getCheckCode());
            //行业中激活逻辑流程已闭环，这段老代码暂时先注释掉。先观察，后续如果激活有问题再放开
            //X3 设备，获取激活信息的时候，不发送RefreshDeviceConfigMsg的消息
//            if(device.getDeviceType() != DeviceType.BOARD_X3.getValue()) {
//                RefreshDeviceConfigMsg msg = new RefreshDeviceConfigMsg();
//                msg.setOrgId(device.getOrgId());
//                msg.setDeviceSn(device.getDeviceSn());
//                msg.setOperationType(1);
//                msg.setTimeStamp(System.currentTimeMillis());
//                EventBus.publish(msg);
//            }
        }
        return activateDeviceResultDTO;
    }

    @Override
    public Boolean deActivateDevice(String deviceSn) {
        InventoryDevice inventoryDevice = new InventoryDevice();
        inventoryDevice.setSerialNumber(deviceSn);
        inventoryDevice.setActivityStatus(DEACTIVIATED);
        inventoryDevice.setOrgId(null);
        return whiteDeviceManager.unbindDevice(inventoryDevice);
    }

    @Override
    public DeviceActiveState getDeviceActiveState(String deviceSn) {
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        return deviceActiveStateMapper.getByDeviceSn(deviceSn);
    }

    @Override
    public Boolean deviceActiveStateChange(String deviceSn, Integer state, String bindOrgUrl, Long bindOrgId) {
        String key = "deviceActiveStateChange:" + deviceSn;
        try {
            if (redissonLockComponent.acquire(key, 5L, 5L)) {
                BizAssert.notNull(deviceSn, "deviceSn must not be null");
                BizAssert.notNull(state, "state must not be null");
                DeviceActiveState activeState = deviceActiveStateMapper.getByDeviceSn(deviceSn);
                if (activeState == null) {
                    activeState = new DeviceActiveState();
                    activeState.setId(idgeneratorService.getNextIdByTypeName(DeviceActiveState
                            .class.getName()).pickDataThrowException());
                    activeState.setDeviceSn(deviceSn);
                    activeState.setState(state);
                    activeState.setBindOrgUrl(bindOrgUrl);
                    activeState.setBindOrgId(bindOrgId);
                    deviceActiveStateMapper.insert(activeState);
                } else {

                    // 如果是团队绑定成功状态，判断前一个状态是否在联网成功之前，如果不是。直接返回不做修改
                    if (DEVICE_BIND_ORG_SUCCESS.getCode() == state && activeState.getState() < DEVICE_NETWORK_CONNECTED.getCode()) {
                        log.warn("团队绑定成功，前一个状态不为团队绑定中状态，不做处理。deviceSn[{}], state[{}]", deviceSn, state);
                        return Boolean.TRUE;
                    }

                    //如果设备状态变为1，则删除已激活的设备，并且激活状态不是激活成功、位置绑定成功
                    if (DeviceActiveStateEnum.DEVICE_CONFIGURE_NETWORK.getCode() == state
                            && DeviceActiveStateEnum.DEVICE_ACTIVATE_SUCCESS.getCode() != activeState.getState()
                            && DeviceActiveStateEnum.RELEVANCE_MEETING_SUCCESS.getCode() != activeState.getState()) {
                        Device device = deviceManager.getByDeviceSn(deviceSn);
                        if (device != null) {
                            unbindDevice(device.getDeviceSn());
                        }
                    }
                    activeState.setState(state);
                    activeState.setDeviceSn(deviceSn);
                    activeState.setBindOrgUrl(bindOrgUrl);
                    activeState.setBindOrgId(bindOrgId);
                    deviceActiveStateMapper.update(activeState);
                }
                redissonLockComponent.release(key);
                return Boolean.TRUE;
            }
        }catch (Exception e){
            log.error("更新设备激活状态出现异常, deviceSn=>{}, msg=>{}", deviceSn, e.getMessage());
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_UNKNOWN_ERROR, DeviceErrorCode.DEVICE_UNKNOWN_ERROR.getMessage()));
        }finally {
            redissonLockComponent.release(key);
        }
        return Boolean.FALSE;
    }

    /**
     * 老版本的没有内置秘钥，验证checkCode
     *
     * @param checkCode       校验码
     * @param inventoryDevice 白名单信息
     * @return
     */
    private String checkCode(String checkCode, InventoryDevice inventoryDevice) {
        String realMacAddress = "";
        try {
            String oldKey = Coder
                .HmacSHA1Encrypt(inventoryDevice.getMacAddress(), inventoryDevice.getMacAddress());
            log.info("generater api,sign:" + oldKey + ",checkCode:" + checkCode);
            if (!checkCode.equals(oldKey)) {
                if (StringUtils.isNotEmpty(inventoryDevice.getMacAddress2())) {
                    String oldKey2 = Coder.HmacSHA1Encrypt(inventoryDevice.getMacAddress2(),
                        inventoryDevice.getMacAddress2());
                    log.info("generater2 api,sign:" + oldKey2 + ",checkCode:" + checkCode);
                    BizAssert.isTrue(checkCode.equals(oldKey2),
                        DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION,
                        DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION.getMessage());
                    realMacAddress = inventoryDevice.getMacAddress2();
                } else {
                    BizAssert.notNull(null, DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION,
                        DeviceErrorCode.CHECK_EQUIPMENT_EXCEPTION.getMessage());
                }
            } else {
                realMacAddress = inventoryDevice.getMacAddress();
            }
        } catch (Exception e) {
            log.error("");
            e.printStackTrace();
        }
        return realMacAddress;
    }

    public Integer activityRecCoreTypeToVerifyChannel(Integer activityRecCoreType) {
        if (activityRecCoreType == null) {
            return null;
        }
        if (ActivityRecCoreType.HUIYAN_THIRDPAD.equals(activityRecCoreType)
            || ActivityRecCoreType.HUIEYE_SELF.equals(activityRecCoreType)) {
            return VerifyChannel.HUIEYE.getValue();
        } else if (ActivityRecCoreType.THIRDPAD_THIRDPAD.equals(activityRecCoreType)) {
            return VerifyChannel.THIRDPART.getValue();
        } else {
            return VerifyChannel.CLOUDEYE.getValue();
        }
    }
}
