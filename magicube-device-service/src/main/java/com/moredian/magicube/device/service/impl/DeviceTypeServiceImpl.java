package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.filemanager.FileManager;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.hephaestus.dto.ProductModelDTO;
import com.moredian.hephaestus.request.relation.GetRelationBySerialNumberRequest;
import com.moredian.hephaestus.response.relation.SimpleSpuInventoryRelationResponse;
import com.moredian.hephaestus.service.ProductModelService;
import com.moredian.hephaestus.service.SpuInventoryRelationService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dto.type.DeviceTypeDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDetailDTO;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypeDTO;
import com.moredian.magicube.device.dto.type.QueryDeviceTypeDTO;
import com.moredian.magicube.device.dto.type.UpdateDeviceTypeDTO;
import com.moredian.magicube.device.manager.DeviceTypeManager;
import com.moredian.magicube.device.service.DeviceTypePropertyRelationService;
import com.moredian.magicube.device.service.DeviceTypePropertyService;
import com.moredian.magicube.device.service.DeviceTypeService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class DeviceTypeServiceImpl implements DeviceTypeService {

    private static final String REGEX_CHINESE = "[\u4e00-\u9fa5]";

    @Autowired
    private DeviceTypeManager deviceTypeManager;

    @Autowired
    private FileManager fileManager;

    @SI
    private DeviceTypePropertyRelationService deviceTypePropertyRelationService;

    @SI
    private DeviceTypePropertyService deviceTypePropertyService;

    @SI
    private ProductModelService productModelService;

    @SI
    private SpuInventoryRelationService spuInventoryRelationService;

    @Override
    public ServiceResponse<Long> insert(InsertDeviceTypeDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceType deviceType = new DeviceType();
        BeanUtils.copyProperties(dto, deviceType);
        serviceResponse.setData(deviceTypeManager.insert(deviceType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Long> update(UpdateDeviceTypeDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceType deviceType = new DeviceType();
        BeanUtils.copyProperties(dto, deviceType);
        deviceType.setId(dto.getDeviceTypeId());
        serviceResponse.setData(deviceTypeManager.update(deviceType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deleteByDeviceType(Integer deviceType) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(
            deviceTypeManager.updateStatusByDeviceType(deviceType, YesNoFlag.NO.getValue()) > 0);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceTypeDTO>> list() {
        ServiceResponse<List<DeviceTypeDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceType> deviceTypes = deviceTypeManager.list();
        List<DeviceTypeDTO> deviceTypeDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            for (DeviceType deviceType : deviceTypes) {
                DeviceTypeDTO deviceTypeDTO = new DeviceTypeDTO();
                BeanUtils.copyProperties(deviceType, deviceTypeDTO);
                deviceTypeDTO.setDeviceTypeId(deviceType.getId());
                if (StringUtils.isNotBlank(deviceType.getDeviceTypePic())) {
                    deviceTypeDTO.setDeviceTypePic(fileManager.getUrlByRelativePath(deviceType
                        .getDeviceTypePic()));
                }
                deviceTypeDTOS.add(deviceTypeDTO);
            }
        }
        serviceResponse.setData(deviceTypeDTOS);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceTypeDTO>> listByTypes(List<Integer> deviceTypes) {
        ServiceResponse<List<DeviceTypeDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceType> deviceTypeList = deviceTypeManager.listByTypes(deviceTypes);
        List<DeviceTypeDTO> deviceTypeDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            for (DeviceType deviceType : deviceTypeList) {
                DeviceTypeDTO deviceTypeDTO = new DeviceTypeDTO();
                BeanUtils.copyProperties(deviceType, deviceTypeDTO);
                deviceTypeDTO.setDeviceTypeId(deviceType.getId());
                if (StringUtils.isNotBlank(deviceType.getDeviceTypePic())) {
                    deviceTypeDTO.setDeviceTypePic(fileManager.getUrlByRelativePath(deviceType
                        .getDeviceTypePic()));
                }
                deviceTypeDTOS.add(deviceTypeDTO);
            }
        }
        serviceResponse.setData(deviceTypeDTOS);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceTypeDetailDTO> getById(Long deviceTypeId) {
        ServiceResponse<DeviceTypeDetailDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        DeviceType deviceType = deviceTypeManager.getById(deviceTypeId);
        DeviceTypeDetailDTO deviceTypeDetailDTO = new DeviceTypeDetailDTO();
        if (deviceType != null) {
            BeanUtils.copyProperties(deviceType, deviceTypeDetailDTO);
            deviceTypeDetailDTO.setDeviceTypeId(deviceType.getId());
            List<Long> propertyIds = deviceTypePropertyRelationService
                .listPropertyIdByDeviceType(deviceType.getDeviceType()).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(propertyIds)) {
                List<DeviceTypePropertyDTO> propertyDTOS = deviceTypePropertyService
                    .listByIds(propertyIds).pickDataThrowException();
                deviceTypeDetailDTO.setPropertyDTOS(propertyDTOS);
            }
        }
        serviceResponse.setData(deviceTypeDetailDTO);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<String> getDeviceTypeName(Integer deviceType) {
        return new ServiceResponse<>(deviceTypeManager.getName(deviceType));
    }

    @Override
    public ServiceResponse<List<DeviceTypeDTO>> listByCondition(QueryDeviceTypeDTO dto) {
        ServiceResponse<List<DeviceTypeDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceType> deviceTypes = deviceTypeManager.listByCondition(dto);
        List<DeviceTypeDTO> deviceTypeDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            List<String> productModelCodes = deviceTypes.stream().map(DeviceType
                ::getProductModelCode).distinct().collect(Collectors.toList());
            Map<String, ProductModelDTO> productModelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(productModelCodes)) {
                List<ProductModelDTO> productModels =  productModelService
                    .listByProductModelCode(productModelCodes).pickDataThrowException();
                if (CollectionUtils.isNotEmpty(productModels)) {
                    productModelMap = productModels.stream().collect(Collectors
                        .toMap(ProductModelDTO::getProductModelCode, Function.identity()));
                }
            }
            for (DeviceType deviceType : deviceTypes) {
                DeviceTypeDTO deviceTypeDTO = new DeviceTypeDTO();
                BeanUtils.copyProperties(deviceType, deviceTypeDTO);
                deviceTypeDTO.setDeviceTypeId(deviceType.getId());
                if (StringUtils.isNotBlank(deviceType.getDeviceTypePic())) {
                    deviceTypeDTO.setDeviceTypePic(fileManager.getUrlByRelativePath(deviceType
                        .getDeviceTypePic()));
                }
                ProductModelDTO productModelDTO = productModelMap.get(deviceType
                    .getProductModelCode());
                if (productModelDTO != null) {
                    deviceTypeDTO.setProductModelName(productModelDTO.getProductModelName());
                    deviceTypeDTO.setModelSerialCode(productModelDTO.getProductModelSerialCode());
                    deviceTypeDTO.setModelSerialCodeAndName(
                        productModelDTO.getProductModelSerialCode() + "-"
                            + productModelDTO.getProductModelSerialName());
                }
                deviceTypeDTOS.add(deviceTypeDTO);
            }
        }
        serviceResponse.setData(deviceTypeDTOS);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceTypeDTO> getByProductModelCode(String productModelCode) {
        ServiceResponse<DeviceTypeDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceType> deviceTypes = deviceTypeManager.getByProductModelCode(productModelCode);
        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            //默认取第一个
            DeviceType deviceType = deviceTypes.get(0);
            DeviceTypeDTO deviceTypeDTO = new DeviceTypeDTO();
            BeanUtils.copyProperties(deviceType, deviceTypeDTO);
            deviceTypeDTO.setDeviceTypeId(deviceType.getId());
            serviceResponse.setData(deviceTypeDTO);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceTypeDTO>> listByProductModelCodes(
        List<String> productModelCodes) {
        ServiceResponse<List<DeviceTypeDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceType> deviceTypes = deviceTypeManager.listByProductModelCodes(productModelCodes);
        List<DeviceTypeDTO> deviceTypeDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            for (DeviceType deviceType : deviceTypes) {
                DeviceTypeDTO deviceTypeDTO = new DeviceTypeDTO();
                BeanUtils.copyProperties(deviceType, deviceTypeDTO);
                deviceTypeDTO.setDeviceTypeId(deviceType.getId());
                deviceTypeDTOS.add(deviceTypeDTO);
            }
            serviceResponse.setData(deviceTypeDTOS);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceTypeDTO>> listLikeName(String keywords) {
        ServiceResponse<List<DeviceTypeDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceTypeDTO> deviceTypeDTOS = new ArrayList<>();
        List<DeviceType> deviceTypes = deviceTypeManager.listLikeName(keywords);
        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            for (DeviceType deviceType : deviceTypes) {
                DeviceTypeDTO deviceTypeDTO = new DeviceTypeDTO();
                deviceTypeDTO.setDeviceType(deviceType.getDeviceType());
                deviceTypeDTO.setDeviceTypeAndName(deviceType.getDeviceType()
                    + "-" + deviceType.getDeviceTypeName());
                deviceTypeDTOS.add(deviceTypeDTO);
            }
        }
        serviceResponse.setData(deviceTypeDTOS);
        return serviceResponse;
    }


    @Override
    public ServiceResponse<DeviceTypeDTO> getDeviceDisplayName(String deviceSn, Integer deviceType) {
        BizAssert.isTrue(StringUtils.isNotEmpty(deviceSn), "deviceSn must be not empty");
        BizAssert.isTrue(deviceType != null, "deviceType must be not empty");

        String deviceNamePrefix = StringUtils.EMPTY;
        String suffixName;
        if (deviceSn.length() > 6) {
            suffixName = deviceSn.substring(deviceSn.length() - 6);
        }else {
            suffixName = deviceSn;
        }
        GetRelationBySerialNumberRequest getRelationBySerialNumberRequest = new GetRelationBySerialNumberRequest();
        getRelationBySerialNumberRequest.setSerialNumberList(Collections.singletonList(deviceSn));
        ServiceResponse<List<SimpleSpuInventoryRelationResponse>> spuRelationServiceResp = spuInventoryRelationService
                .findSpuInventoryRelationBySerialNumberList(getRelationBySerialNumberRequest);
        if (spuRelationServiceResp.isSuccess() && spuRelationServiceResp.isExistData()
                && CollectionUtils.isNotEmpty(spuRelationServiceResp.getData())) {
            SimpleSpuInventoryRelationResponse spuRelation = spuRelationServiceResp.getData().get(0);
            if (spuRelation != null) {
                deviceNamePrefix = spuRelation.getSpuDisplayName();
            }
        }
        DeviceType deviceTypeObj = null;
        if (StringUtils.isBlank(deviceNamePrefix)) {
            deviceTypeObj = deviceTypeManager.getByDeviceType(deviceType);
        }

        String hyDeviceName;
        String ddDeviceName;
        String mlDeviceName;
        // 根据设备类型展示名称
        if (deviceTypeObj != null) {
            hyDeviceName = deviceTypeObj.getDeviceTypeDisplayNameHy() + "-" + suffixName;
            ddDeviceName = deviceTypeObj.getDeviceTypeDisplayNameDing() + "-" + suffixName;
            mlDeviceName = deviceTypeObj.getDeviceTypeDisplayNameMl() + "-" + suffixName;
        }else {
            hyDeviceName = deviceNamePrefix + "-" + suffixName;
            ddDeviceName = deviceNamePrefix + "-" + suffixName;
            mlDeviceName = deviceNamePrefix + "-" + suffixName;
        }

        DeviceTypeDTO resData = new DeviceTypeDTO();
        resData.setDeviceType(deviceType);
        resData.setDeviceTypeDisplayNameHy(hyDeviceName);
        resData.setDeviceTypeDisplayNameDing(ddDeviceName);
        resData.setDeviceTypeDisplayNameMl(mlDeviceName);
        ServiceResponse<DeviceTypeDTO> res = ServiceResponse.createSuccessResponse();
        res.setData(resData);
        return res;
    }
}
