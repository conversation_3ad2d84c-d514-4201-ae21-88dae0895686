package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.model.msg.GroupFaceChangedMessage;
import com.moredian.iothub.control.ControllerErrorCode;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.core.org.service.OrgConfigService;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import java.util.List;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class GroupFaceMsgSubscriber {

    private static Logger logger = LoggerFactory.getLogger(OrgMsgSubscriber.class);

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @SI
    private OrgConfigService orgConfigService;

    @Subscribe
    public void subscribeDisableOrgMsg(GroupFaceChangedMessage msg) {
        logger.info("subscribeDisableOrgMsg receive groupFaceChangedMessage:{}", msg);
        // 人脸组加人脸，删除脸时，通知终端做底库增量同步
//        Integer faceSource = orgConfigService.getOrgFaceSource(msg.getOrgId()).pickDataThrowException();
//        if (!OrgFaceSourceEnum.SELF.getValue().equals(faceSource)) {
//            return;
//        }
        List<Device> devices = deviceManager.listByOrgIdAndTypes(msg.getOrgId(), deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.VERIFY_BOARD_LIST));
        if (CollectionUtils.isEmpty(devices)) {
            logger.warn("Notify device GroupFaceChangedMessage, no device is found");
        } else {
            logger.debug("Notify device GroupFaceChangedMessage, deviceCount : {}", devices.size());
            for (Device device : devices) {
                try {
                    if (device.getDeviceType() != null && DeviceType.DOOR_LOCK.getValue() == device.getDeviceType()) {
                        continue;
                    }
                    // 发送命令
                    String deviceSn = device.getDeviceSn();
                    TransferMessageInfo<String> transferMessageInfo = new TransferMessageInfo<>();
                    transferMessageInfo
                        .setEventType(TransferEventType.MEMBER_FACE_SYNC.getEventType());
                    transferMessageInfo.setSeverity(3);
                    transferMessageInfo.setSeqId(com.moredian.bee.common.utils.UUID.random19()
                        + TransferEventType.MEMBER_FACE_SYNC.getEventName());
                    transferMessageInfo
                        .setMessage(TransferEventType.MEMBER_FACE_SYNC.getEventName());
                    String jsonStr = JsonUtils.toJson(transferMessageInfo);
                    String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
                    TransferRequest transferRequest = new TransferRequest();
                    transferRequest.setSerialNumber(deviceSn);
                    transferRequest.setBody(base64Message);
                    transferRequest.setCommand(0);

                    ServiceResponse<TransferResponse> response = hubControlServiceV1
                        .transfer(transferRequest);
                    if (response != null && !response.isSuccess()
                        && ControllerErrorCode.DEVICE_OFFLINE.getCode()
                        .equals(response.getErrorContext().getCode())) {
                        logger.info(
                            "Notify device GroupFaceChangedMessage, 设备已离线，本次不发送通知，[org ={} ,deviceSn ={}, deviceId : {} ]",
                            msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                        continue;
                    }

                    TransferResponse result = response.pickDataThrowException();
                    logger.debug(
                        "Notify device GroupFaceChangedMessage, [org ={} ,deviceSn ={}, deviceId : {} ]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                } catch (Exception e) {
                    logger.error(
                        "Notify device GroupFaceChangedMessage failed. org [{}],device [sn={}, id={}]，msg [{}]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId(), e);
                }
            }
        }
    }
}