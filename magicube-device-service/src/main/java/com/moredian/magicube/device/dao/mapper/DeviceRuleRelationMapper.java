package com.moredian.magicube.device.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.moredian.magicube.device.dao.entity.DeviceRuleRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceRuleRelationMapper extends BaseMapper<DeviceRuleRelation> {
    void deleteDeviceRule(@Param("orgId") Long orgId, @Param("ruleId") Long ruleId);

    List<Long> getRuleIdByOrgIdAndDeviceId(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId);

    void batchInsert(@Param("list") List<DeviceRuleRelation> deviceRuleRelationList);

    void deleteByOrgIdAndDeviceId(@Param("orgId") Long orgId, @Param("ruleId") Long ruleId, @Param("deviceIdList") List<Long> deviceIdList);
}