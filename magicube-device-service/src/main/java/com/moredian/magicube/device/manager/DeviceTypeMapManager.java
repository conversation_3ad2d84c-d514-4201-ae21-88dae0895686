package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.DeviceTypeMap;
import com.moredian.magicube.device.dto.type.DeviceCapacityDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-05-18
 */
public interface DeviceTypeMapManager {

    /**
     * 获取map值
     *
     * @param mapName
     * @param mapKey
     * @return
     */
    String getValue(String mapName, String mapKey);

    /**
     * 获取同一map中的多个key对应的值
     *
     * @param mapName
     * @param mapKey
     * @return
     */
    List<DeviceTypeMap> getValues(String mapName, List<String> mapKey);
    /**
     * 获取设备容量
     *
     * @param deviceType 设备类型
     * @return
     */
    Integer getDeviceCapacity(Integer deviceType);

    /**
     * 获取多个设备的容量
     * @param deviceType 设备类型
     * @return
     */
    List<Integer> getDevicesCapacity(List<Integer> deviceType);

    /**
     * 批量获取设备容量
     *
     * @param deviceTypeList
     * @return
     */
    List<DeviceCapacityDTO> getDeviceListCapacity(List<Integer> deviceTypeList);

    /**
     * 新增或修改设备容量
     *
     * @param deviceType 设备类型
     * @param deviceCapacity 设备人脸容量
     * @return
     */
    Boolean saveDeviceCapacity(Integer deviceType, Integer deviceCapacity);

    /**
     * 获取设备容量
     *
     * @param deviceType 设备类型
     * @return
     */
    String getDefaultSpu(Integer deviceType);

    /**
     * 获取设备转换名
     *
     * @param deviceType 设备类型
     * @return
     */
    String getConvertName(Integer deviceType);

    /**
     * 获取设备转换名
     *
     * @param deviceSn 设备SN
     * @return
     */
    Integer getTypeByDeviceSn(String deviceSn);

    /**
     * 批量获取设备转换名
     *
     * @param deviceSnList 设备SN
     * @return
     */
    Map<String/*deviceSn*/, Integer/*deviceType*/> getTypeByDeviceSnList(List<String> deviceSnList);

}
