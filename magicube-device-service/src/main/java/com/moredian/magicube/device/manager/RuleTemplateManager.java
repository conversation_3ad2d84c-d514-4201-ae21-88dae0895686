package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.RuleTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: RuleTemplateManager.java, v 1.0 Exp $
 */
public interface RuleTemplateManager {

    List<RuleTemplate> getDefaultTemplateList();

    RuleTemplate getTemplate(Long templateId);

    List<RuleTemplate> listByModeType(Integer modeType);

}
