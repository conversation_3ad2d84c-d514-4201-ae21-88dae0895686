package com.moredian.magicube.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.monitor.IMonitorService;
import com.moredian.bee.monitor.MonitorData;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.conf.core.client.BeeConfNewClient;
import com.moredian.deliver.api.agent.AgentDeviceRelationService;
import com.moredian.deliver.api.manage.WhiteDeviceService;
import com.moredian.deliver.api.run.AuthorizeDataService;
import com.moredian.deliver.api.run.AuthorizeSpecialAppDataService;
import com.moredian.deliver.dto.AgentDeviceRelationDTO;
import com.moredian.deliver.dto.AppSpecialDTO;
import com.moredian.deliver.dto.AuthorizeOrgDTO;
import com.moredian.deliver.dto.WhiteDeviceDTO;
import com.moredian.deliver.enums.ServerNodeTypeEnum;
import com.moredian.device.pipeline.api.DevicePipelineStateService;
import com.moredian.device.pipeline.dto.request.UpdateDeviceSceneTypeReq;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.TpType;
import com.moredian.magicube.common.model.msg.device.DeviceActiveMsg;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.config.DeviceInitConfigProperties;
import com.moredian.magicube.device.config.OneServiceConfig;
import com.moredian.magicube.device.constant.BeeConfConfigConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.DeviceActiveState;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dao.entity.DeviceVersion;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dao.mapper.DeviceApkVersionMapper;
import com.moredian.magicube.device.dto.activate.*;
import com.moredian.magicube.device.dto.device.*;
import com.moredian.magicube.device.dto.qrcode.DeviceActiveStateDTO;
import com.moredian.magicube.device.dto.upgrade.MergeUpdateAppToLastResponse;
import com.moredian.magicube.device.dto.upgrade.MergeUpgradeDeviceRequest;
import com.moredian.magicube.device.dto.version.ChangeDeviceAppVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceEnforceUpdateDTO;
import com.moredian.magicube.device.enums.DeviceActiveStateEnum;
import com.moredian.magicube.device.enums.DeviceFlagEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.SceneTypeHelper;
import com.moredian.magicube.device.helper.SpaceHelper;
import com.moredian.magicube.device.manager.*;
import com.moredian.magicube.device.model.WhiteDeviceInfo;
import com.moredian.magicube.device.monitor.ActiveUnbindMonitorObj;
import com.moredian.magicube.device.monitor.CommonLogConstants;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.DeviceActivateEngineResolver;
import com.moredian.magicube.device.pipeline.core.engine.EngineExecutor;
import com.moredian.magicube.device.service.ActivityService;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceUpgradeService;
import com.moredian.magicube.device.service.DeviceVersionService;
import com.moredian.magicube.device.service.helper.DdBizManageHelper;
import com.moredian.ota.api.request.apk.OtaApkGetHighApkRequest;
import com.moredian.ota.api.response.apk.OtaApkResponse;
import com.moredian.ota.api.response.apk.OtaApkWithExtraInfoResponse;
import com.moredian.ota.api.service.OtaApkService;
import com.moredian.space.dto.device.ActiveDeviceSceneTreeDTO;
import com.moredian.space.service.SpaceTreeService;
import com.xier.sesame.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.DEVICE_NETWORK_CONNECTED;
import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.DEVICE_WAIT_NETWORK;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class ActivityServiceImpl implements ActivityService {
    @Resource
    private DeviceInitConfigProperties deviceInitConfigProperties;

    @Autowired
    private ActivityManager activityManager;
    @Resource
    private WhiteDeviceManager whiteDeviceManager;

    @Resource
    private DdBizManageHelper ddBizManageHelper;

    @Autowired
    private DeviceActivateEngineResolver deviceActivateEngineResolver;

    @Autowired
    private DeviceVersionManager deviceVersionManager;

    @Autowired
    private DeviceVersionService deviceVersionService;

    @Autowired
    private DeviceUpgradeService deviceUpgradeService;

    @Autowired
    private IMonitorService monitorService;
    @Autowired
    private FunctionQrCodeManager qrCodeManager;
    @Resource
    private OneServiceConfig oneServiceConfig;
    @Resource
    private DeviceApkVersionMapper deviceApkVersionMapper;
    @Resource
    private SpaceHelper spaceHelper;
    @Resource
    private SceneTypeHelper sceneTypeHelper;

    @Resource
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;
    @Resource
    private DeviceManager deviceManager;
    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private AuthorizeDataService authorizeDataService;

    @SI
    private AuthorizeSpecialAppDataService authorizeSpecialAppDataService;

    @SI
    private OtaApkService otaApkService;

    @SI
    private WhiteDeviceService deliverWhiteDeviceService;

    @SI
    private AgentDeviceRelationService agentDeviceRelationService;

    @SI
    private DevicePipelineStateService devicePipelineStateService;

    @SI
    private SpaceTreeService spaceTreeService;

    @Autowired
    private DeviceService deviceService;

    @SI
    private OrgService orgService;

    @Override
    public ServiceResponse<QrCodeDTO> generateQrCode(GenerateQrCodeDTO dto) {
        ServiceResponse<QrCodeDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.generateQrCode(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<QrCodeStatusDTO> getStatusByQrCode(QueryQrCodeStatusDTO dto) {
        ServiceResponse<QrCodeStatusDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.getStatusByQrCode(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<QrCodeStatusDTO> updateQrCodeStatus(UpdateQrCodeStatusDTO dto) {
        ServiceResponse<QrCodeStatusDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.updateQrCodeStatus(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<QrCodeStatusDTO> updateQrCodeStatusBySn(Long orgId, Long accountId,
        String deviceSn) {
        ServiceResponse<QrCodeStatusDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.updateQrCodeStatusBySn(orgId, accountId, deviceSn));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<QueryActivationStatusDTO> getActivityStatusByQrCode(String qrCode) {
        ServiceResponse<QueryActivationStatusDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        serviceResponse.setData(activityManager.getActivityStatusByQrCode(qrCode));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<QueryActivationStatusDTO> getThirdPartyActivityStatusByTpId(
        String thirdPartyDeviceId) {
        ServiceResponse<QueryActivationStatusDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        serviceResponse
            .setData(activityManager.getThirdPartyActivityStatusByTpId(thirdPartyDeviceId));
        return serviceResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<ActivateDeviceResultDTO> activate(ActivateDeviceDTO dto) {
        log.info("activate====> 激活开始 {}",JsonUtils.toJson(dto));
        ServiceResponse<ActivateDeviceResultDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        EngineExecutor engineExecutor = deviceActivateEngineResolver
            .getExecutor(dto.getDeviceActivateType());
        BizAssert.isTrueWithFormat(engineExecutor != null,
            DeviceErrorCode.DEVICE_ACTIVATE_ENGINE_NOT_FOUND,
            DeviceErrorCode.DEVICE_ACTIVATE_ENGINE_NOT_FOUND.getMessage(),
            dto.getDeviceActivateType());
        ActivateDeviceContext context = new ActivateDeviceContext();
        context.setOrgId(dto.getOrgId());
        serviceResponse.setData((ActivateDeviceResultDTO) engineExecutor
            .execute(dto, context, new ActivateDeviceResultDTO()));
        log.info("activate====> 激活成功 {} {}",JsonUtils.toJson(dto),JsonUtils.toJson(serviceResponse));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> updateThirdPartyDeviceInfo(ThirdPartyDeviceInfoDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.updateThirdPartyDeviceInfo(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> unbindDevice(String deviceSn) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.unbindDevice(deviceSn));
        try {
            //构造打印监控日志所需对象
            MonitorData monitorData = new MonitorData();
            monitorData.setTimestamp(System.currentTimeMillis());
            monitorData.setDeviceSn(deviceSn);
            monitorData
                .setData(new ActiveUnbindMonitorObj(CommonLogConstants.MONITOR_BIZ_UNBIND_DEVICE));
            //日志形式埋点
            monitorService.addMonitorData(CommonLogConstants.LOG_SYSTEM,
                CommonLogConstants.ACTIVATE_UNBIND_DEVICE_LOG_STORE, monitorData);
        } catch (Exception e) {
            log.warn("解绑设备调用日志埋点接口失败", e);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<ActivateDeviceResultDTO> getActivateInfo(QueryActivateInfoDTO dto) {
        ServiceResponse<ActivateDeviceResultDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        serviceResponse.setData(activityManager.getActivateInfo(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> enforceUpgradeDevice(List<ActivateUpgradeDTO> requestList) {
        log.info("设备激活升级检测..., {}", JsonUtils.toJson(requestList));

        Boolean enforceUpdate = false;
        MergeUpgradeDeviceRequest mergeUpgradeDeviceRequest = null;

        for (ActivateUpgradeDTO req : requestList) {
            ServiceResponse<DeviceEnforceUpdateDTO> enforceUpdateStatusResponse = null;
            enforceUpdateStatusResponse = deviceVersionService.getDeviceEnforceUpdateStatus(req.getDeviceId());
            if (enforceUpdateStatusResponse != null && enforceUpdateStatusResponse.getData() != null) {
                DeviceEnforceUpdateDTO currentEnforceUpdateStatus = enforceUpdateStatusResponse.getData();
                if (req.getSystemType().equals(currentEnforceUpdateStatus.getSystemType()) &&
                    req.getAppType().equals(currentEnforceUpdateStatus.getAppType()) &&
                    req.getVersionCode().equals(currentEnforceUpdateStatus.getVersionCode())) {
                    int enforceUpgradeTimes = currentEnforceUpdateStatus.getTryTimes();
                    if (enforceUpgradeTimes > 0) {
                        log.warn("中断设备强制升级检测, 曾经尝试强制升级次数 {}, 强制升级状态{}", enforceUpgradeTimes, JsonUtils.toJson(currentEnforceUpdateStatus));
                        return new ServiceResponse<>(true);
                    }
                }
            }
        }

        //获取设备上报的apk类型，确定rom是否存在升级
        Integer apkAppType = null;
        for (ActivateUpgradeDTO req : requestList) {
            if (req != null && req.getIsRom()) {
                continue;
            }
            apkAppType = req.getAppType();
        }

        for (ActivateUpgradeDTO req : requestList) {
            ChangeDeviceAppVersionDTO changeDeviceVersionDto = new ChangeDeviceAppVersionDTO();
            changeDeviceVersionDto.setOrgId(req.getOrgId());
            changeDeviceVersionDto.setAppType(req.getAppType());
            changeDeviceVersionDto.setSn(req.getSerialNumber());
            changeDeviceVersionDto.setDeviceId(req.getDeviceId());
            changeDeviceVersionDto.setVersionCode(req.getVersionCode());

            if (req.getIsRom()) {
                deviceVersionManager.changeDeviceRomVersion(changeDeviceVersionDto);
            } else {
                deviceVersionManager.changeDeviceAppVersion(changeDeviceVersionDto);
            }

            if (!enforceUpdate) {
                OtaApkGetHighApkRequest otaApkGetHighApkRequest = new OtaApkGetHighApkRequest();
                otaApkGetHighApkRequest.setAppType(req.getAppType());
                otaApkGetHighApkRequest.setVersionCode(req.getVersionCode());
                if (req.getIsRom()) {
                    otaApkGetHighApkRequest.setApkAppType(apkAppType);
                }
                List<OtaApkWithExtraInfoResponse> otaApkWithExtraInfos = otaApkService
                    .findHighApkList(otaApkGetHighApkRequest).pickDataThrowException();
                List<OtaApkResponse> otaApkResponses = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(otaApkWithExtraInfos)) {
                    for (OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse : otaApkWithExtraInfos) {
                        OtaApkResponse otaApkResponse = otaApkWithExtraInfoResponse.getOtaApkResponse();
                        if (otaApkResponse != null && otaApkResponse.getInitDeviceUpgrade() != null && otaApkResponse.getInitDeviceUpgrade()) {
                            otaApkResponses.add(otaApkResponse);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(otaApkResponses)) {
                        log.info("设备激活触发强制升级, {}", JsonUtils.toJson(req));
                        enforceUpdate = true;
                        mergeUpgradeDeviceRequest = new MergeUpgradeDeviceRequest();
                        mergeUpgradeDeviceRequest.setSystemType(req.getSystemType());
                        mergeUpgradeDeviceRequest.setAppType(req.getAppType());
                        mergeUpgradeDeviceRequest.setOrgId(req.getOrgId());
                        mergeUpgradeDeviceRequest.setSerialNumber(req.getSerialNumber());
                        ServiceResponse<Boolean> updateResponse = deviceUpgradeService
                            .mergeUpdateAppToLast(mergeUpgradeDeviceRequest);
                        Boolean isUpdate = updateResponse.pickDataThrowException();

                        //实际发送了升级消息，
                        if (isUpdate) {
                            DeviceEnforceUpdateDTO enforceUpdateStatus = new DeviceEnforceUpdateDTO();
                            enforceUpdateStatus.setDeviceId(req.getDeviceId());
                            enforceUpdateStatus.setSystemType(req.getSystemType());
                            enforceUpdateStatus.setAppType(req.getAppType());
                            enforceUpdateStatus.setVersionCode(req.getVersionCode());
                            enforceUpdateStatus.setTryTimes(1);
                            enforceUpdateStatus.setEnforceUpdateTime(new Date());
                            deviceVersionManager
                                .changeDeviceEnforceUpdateStatus(enforceUpdateStatus);
                        }
                    }
                }
            }
        }
        return new ServiceResponse<>(true);
    }

    /**
     * 设备激活后升级——带返回值
     *
     * @param requestList
     * @return
     */
    @Override
    public ServiceResponse<MergeUpdateAppToLastResponse> enforceUpgradeDeviceReturn(List<ActivateUpgradeDTO> requestList) {
        log.info("设备激活升级检测..., {}", JsonUtils.toJson(requestList));

        MergeUpdateAppToLastResponse response = null;
        Boolean enforceUpdate = false;
        MergeUpgradeDeviceRequest mergeUpgradeDeviceRequest = null;

        for (ActivateUpgradeDTO req : requestList) {
            ServiceResponse<DeviceEnforceUpdateDTO> enforceUpdateStatusResponse = null;
            enforceUpdateStatusResponse = deviceVersionService.getDeviceEnforceUpdateStatus(req.getDeviceId());
            if (enforceUpdateStatusResponse != null && enforceUpdateStatusResponse.getData() != null) {
                DeviceEnforceUpdateDTO currentEnforceUpdateStatus = enforceUpdateStatusResponse.getData();
                if (req.getSystemType().equals(currentEnforceUpdateStatus.getSystemType()) &&
                        req.getAppType().equals(currentEnforceUpdateStatus.getAppType()) &&
                        req.getVersionCode().equals(currentEnforceUpdateStatus.getVersionCode())) {
                    int enforceUpgradeTimes = currentEnforceUpdateStatus.getTryTimes();
                    if (enforceUpgradeTimes > 0) {
                        log.warn("中断设备强制升级检测, 曾经尝试强制升级次数 {}, 强制升级状态{}", enforceUpgradeTimes, JsonUtils.toJson(currentEnforceUpdateStatus));
                        return ServiceResponse.createSuccessResponse();
                    }
                }
            }
        }

        //获取设备上报的apk类型，确定rom是否存在升级
        Integer apkAppType = null;
        for (ActivateUpgradeDTO req : requestList) {
            if (req != null && req.getIsRom()) {
                continue;
            }
            apkAppType = req.getAppType();
        }

        for (ActivateUpgradeDTO req : requestList) {
            ChangeDeviceAppVersionDTO changeDeviceVersionDto = new ChangeDeviceAppVersionDTO();
            changeDeviceVersionDto.setOrgId(req.getOrgId());
            changeDeviceVersionDto.setAppType(req.getAppType());
            changeDeviceVersionDto.setSn(req.getSerialNumber());
            changeDeviceVersionDto.setDeviceId(req.getDeviceId());
            changeDeviceVersionDto.setVersionCode(req.getVersionCode());

            if (req.getIsRom()) {
                deviceVersionManager.changeDeviceRomVersion(changeDeviceVersionDto);
            } else {
                deviceVersionManager.changeDeviceAppVersion(changeDeviceVersionDto);
            }

            if (!enforceUpdate) {
                OtaApkGetHighApkRequest otaApkGetHighApkRequest = new OtaApkGetHighApkRequest();
                otaApkGetHighApkRequest.setAppType(req.getAppType());
                otaApkGetHighApkRequest.setVersionCode(req.getVersionCode());
                if (req.getIsRom()) {
                    otaApkGetHighApkRequest.setApkAppType(apkAppType);
                }
                List<OtaApkWithExtraInfoResponse> otaApkWithExtraInfos = otaApkService
                        .findHighApkList(otaApkGetHighApkRequest).pickDataThrowException();
                List<OtaApkResponse> otaApkResponses = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(otaApkWithExtraInfos)) {
                    for (OtaApkWithExtraInfoResponse otaApkWithExtraInfoResponse : otaApkWithExtraInfos) {
                        OtaApkResponse otaApkResponse = otaApkWithExtraInfoResponse.getOtaApkResponse();
                        if (otaApkResponse != null && otaApkResponse.getInitDeviceUpgrade() != null && otaApkResponse.getInitDeviceUpgrade()) {
                            otaApkResponses.add(otaApkResponse);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(otaApkResponses)) {
                        log.info("设备激活触发强制升级, {}", JsonUtils.toJson(req));
                        enforceUpdate = true;
                        mergeUpgradeDeviceRequest = new MergeUpgradeDeviceRequest();
                        mergeUpgradeDeviceRequest.setSystemType(req.getSystemType());
                        mergeUpgradeDeviceRequest.setAppType(req.getAppType());
                        mergeUpgradeDeviceRequest.setOrgId(req.getOrgId());
                        mergeUpgradeDeviceRequest.setSerialNumber(req.getSerialNumber());
                        ServiceResponse<MergeUpdateAppToLastResponse> updateResponse = deviceUpgradeService
                                .mergeUpdateAppToLastReturn(mergeUpgradeDeviceRequest);
                        response = updateResponse.pickDataThrowException();

                        //实际发送了升级消息，
                        if (response != null) {
                            DeviceEnforceUpdateDTO enforceUpdateStatus = new DeviceEnforceUpdateDTO();
                            enforceUpdateStatus.setDeviceId(req.getDeviceId());
                            enforceUpdateStatus.setSystemType(req.getSystemType());
                            enforceUpdateStatus.setAppType(req.getAppType());
                            enforceUpdateStatus.setVersionCode(req.getVersionCode());
                            enforceUpdateStatus.setTryTimes(1);
                            enforceUpdateStatus.setEnforceUpdateTime(new Date());
                            deviceVersionManager
                                    .changeDeviceEnforceUpdateStatus(enforceUpdateStatus);
                        }
                    }
                }
            }
        }
        return new ServiceResponse<>(response);
    }

    @Override
    public ServiceResponse<Boolean> deActivateDevice(String deviceSn) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.deActivateDevice(deviceSn));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceBaseInfoDTO> deviceInitConfig(String deviceSn) {
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(deviceSn);
        BizAssert.notNull(inventoryDevice, "设备白单信息不存在");
        ServiceResponse<DeviceBaseInfoDTO> successResponse = ServiceResponse.createSuccessResponse();
        DeviceBaseInfoDTO infoDTO = DeviceBaseInfoDTO.builder()
            .oaBase(inventoryDevice.getDeviceActivateOaBase())
            .sdkBase(inventoryDevice.getDeviceActivateSdkBase())
            .build();

        buildConfigUrl(infoDTO, inventoryDevice);
        successResponse.setData(infoDTO);
        return successResponse;
    }

    @Override
    public ServiceResponse<DeviceAuthDetailDTO> authDetailBySn(String deviceSn) {
        ServiceResponse<DeviceAuthDetailDTO> successResponse = ServiceResponse.createSuccessResponse();
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(deviceSn);
        BizAssert.notNull(inventoryDevice, "设备白单信息不存在");

        DeviceAuthDetailDTO res = new DeviceAuthDetailDTO();
        res.setDeviceSn(deviceSn);
        res.setOaBase(inventoryDevice.getDeviceActivateOaBase());
        res.setSdkBase(inventoryDevice.getDeviceActivateSdkBase());
        Boolean oneService = oneServiceConfig.getEnableOneService();

        // 如果是私有化环境，默认管控通过
        if (Boolean.TRUE.equals(oneService)) {
            res.setHasDeviceAuthFlag(Boolean.TRUE);
            successResponse.setData(res);
            return successResponse;
        }

        // 判断是否是渠道商设备
        AgentDeviceRelationDTO deviceRelationDTO = agentDeviceRelationService.getByDeviceSn(
            deviceSn).pickDataThrowException();
        res.setIsAgentDevice(deviceRelationDTO != null);
        successResponse.setData(res);
        // 如果不是渠道商设备，就去查授权管控或则脱管白单
        if (deviceRelationDTO == null){
            // 判断是否在托脱管白单中
            List<WhiteDeviceDTO> deliverWhiteList = deliverWhiteDeviceService.listByDeviceSnList(
                Lists.newArrayList(deviceSn)).pickDataThrowException();
            res.setAuthFilter(ObjectUtil.isNotEmpty(deliverWhiteList));

            // 获取设备授权的机构
            AuthorizeOrgDTO authorizeOrgDTO = authorizeDataService.checkDeviceBySn(deviceSn)
                .pickDataThrowException();
            if (authorizeOrgDTO == null){
                res.setHasDeviceAuthFlag(Boolean.FALSE);
                return successResponse;
            }
            res.setHasDeviceAuthFlag(Boolean.TRUE);
            res.setAuthOrg(authorizeOrgDTO.getOrgId());
            res.setBase(authorizeOrgDTO.getBase());
            res.setAuthOrgTpId(authorizeOrgDTO.getTpId());
            if (authorizeOrgDTO.getOrgServerNodeType() != null) {
                res.setOrgPublicOrPrivateFlag(
                    ServerNodeTypeEnum.PRIVATE_NODE.getValue().equals(authorizeOrgDTO.getOrgServerNodeType()));
            }
            // 如果是私有化机构 指定设置空间档案跳转连接
            AppSpecialDTO appSpecialDTO = authorizeSpecialAppDataService
                .getSpecialAppDataByGlobalIdAndAppCode(authorizeOrgDTO.getGlobalOrgConfigId(),
                    "011").pickDataThrowException();
            if (appSpecialDTO != null) {
                res.setAppCode(appSpecialDTO.getAppCode());
                res.setAppRedirectUrl(appSpecialDTO.getAppRedirectUrl() + authorizeOrgDTO.getTpId());
            }
        }
        return successResponse;
    }

    @Override
    public ServiceResponse<DeviceActiveStateDTO> getDeviceActiveState(String deviceSn) {
        ServiceResponse<DeviceActiveStateDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        DeviceActiveState deviceActiveState = activityManager.getDeviceActiveState(deviceSn);
        if (deviceActiveState == null) {
            return serviceResponse;
        }
        DeviceActiveStateDTO activeStateDTO = DeviceActiveStateDTO.builder()
            .id(deviceActiveState.getId())
            .deviceSn(deviceActiveState.getDeviceSn())
            .state(deviceActiveState.getState())
            .bindOrgUrl(deviceActiveState.getBindOrgUrl())
            .bindOrgId(deviceActiveState.getBindOrgId())
            .build();
        serviceResponse.setData(activeStateDTO);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deviceActiveStateChange(String deviceSn, Integer state, String bindOrgUrl) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceActiveState deviceActiveState = activityManager.getDeviceActiveState(deviceSn);
        // 给终端特用，如果终端上报的2，并且当前状态在联网中以及之前，不让修改
        if (DEVICE_WAIT_NETWORK.getCode() == state
                && DEVICE_NETWORK_CONNECTED.getCode() > deviceActiveState.getState()) {
            serviceResponse.setData(Boolean.TRUE);
            return serviceResponse;
        }
        serviceResponse.setData(activityManager.deviceActiveStateChange(deviceSn, state, bindOrgUrl, null));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deviceActiveStateChange(DeviceActiveStateChangeDTO dto) {
        String deviceSn = dto.getDeviceSn();
        Integer state = dto.getState();
        BizAssert.isTrue(state != null, "state must not be null");
        BizAssert.isTrue(!StringUtil.isBlank(deviceSn), "deviceSn must not be null");

        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(activityManager.deviceActiveStateChange(deviceSn, state, dto.getBindOrgUrl(), dto.getBindOrgId()));
        return serviceResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Boolean> updateSceneType(String deviceSn, Integer sceneType,
        Long memberId) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();

        InventoryDevice deviceWhite = whiteDeviceManager.getByDeviceSn(deviceSn);
        Integer type = deviceWhite.getDeviceType();
        // 如果是通通锁，设备还未在数据库中，查不到数据，跳过运行模式的选择
        if (type != null && type == DeviceType.TTLOCK.getValue()) {
            //更新设备激活的状态为激活中
            activityManager.deviceActiveStateChange(deviceSn,
                    DeviceActiveStateEnum.DEVICE_ACTIVATING.getCode(), null, null);
            serviceResponse.setData(Boolean.TRUE);
            return serviceResponse;
        }

        DeviceInfoDTO device = deviceService.getByDeviceSn(deviceSn).pickDataThrowException();
        if (device == null) {
            serviceResponse.setData(Boolean.FALSE);
            return serviceResponse;
        }
        Integer deviceType = device.getDeviceType();
        UpdateDeviceSceneTypeReq req = new UpdateDeviceSceneTypeReq();
        req.setDeviceSn(deviceSn);
        req.setDeviceId(device.getDeviceId());
        req.setOrgId(device.getOrgId());
        req.setSceneType(sceneType);
        boolean result = devicePipelineStateService.updateCloudSceneType(req)
            .pickDataThrowException();
        //如果修改成功 则发送激活成功消息
        if (result) {
            ActiveDeviceSceneTreeDTO activeDeviceSceneTreeDTO = new ActiveDeviceSceneTreeDTO();
            activeDeviceSceneTreeDTO.setOrgId(device.getOrgId());
            activeDeviceSceneTreeDTO.setDeviceId(device.getDeviceId());
            activeDeviceSceneTreeDTO.setDeviceSn(device.getDeviceSn());
            activeDeviceSceneTreeDTO.setSceneType(sceneType);
            activeDeviceSceneTreeDTO.setSendMsgFlag(Boolean.TRUE);

            // 旧版会议室标识
            boolean meetFlag = Boolean.FALSE;

            // 8j10、T10、T21三款老版本水牌激活，需要初始化会议室空间
            // 需要初始化会议室空间的设备类型，加上版本号控制
            if (deviceType != null &&
                    deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.MEETING_DEVICE_INITIAL_MEETING_ROOM, deviceType)) {
                List<DeviceVersion> apkVersions = deviceApkVersionMapper.getBatchDeviceApkVersionByOrgIdAndDeviceIds(device.getOrgId(),
                        Lists.newArrayList(device.getDeviceId()));
                if (CollectionUtils.isNotEmpty(apkVersions)) {
                    DeviceVersion apkVersion = apkVersions.get(0);
                    // 低于 30310020 这个版本需要绑定初始化会议室空间
                    if (apkVersion != null && apkVersion.getVersionCode() != null && apkVersion.getVersionCode() < 30310020) {
                        meetFlag = Boolean.TRUE;
                        spaceTreeService.createTreeBySceneTypeWithoutLevel(activeDeviceSceneTreeDTO)
                                .pickDataThrowException();
                    }
                }
            }

            // 通用的设备，会默认绑一个根空间
            if (!meetFlag && device.getDeviceFlag().equals(DeviceFlagEnum.HY.getValue())) {
                // 行业绑空间
                spaceHelper.bindRootSpace(device.getOrgId(), device.getDeviceId());
            }

            //更新设备激活的状态为激活中
            activityManager.deviceActiveStateChange(deviceSn,
                DeviceActiveStateEnum.DEVICE_ACTIVATING.getCode(), null, null);

            // 根据选择场景更新设备关联的应用
            List<String> appCode = sceneTypeHelper.getAppCodeBySceneType(sceneType);
            UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
            updateDeviceDTO.setDeviceId(device.getDeviceId());
            updateDeviceDTO.setOrgId(device.getOrgId());
            updateDeviceDTO.setAppCode(String.join(",", appCode));
            updateDeviceDTO.setAppCodeList(String.join(",", appCode));
            deviceManager.update(updateDeviceDTO);

            //在事务提交后,在进行消息发送
            TransactionSynchronizationManager
                .registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        // 如果是会议水牌钉钉SDK激活，在设备运行模式选择后才发送设备激活消息(选完运行模式才算整体流程结束)
                        DeviceActiveMsg deviceActiveMsg = new DeviceActiveMsg();
                        deviceActiveMsg.setOrgId(device.getOrgId());
                        deviceActiveMsg.setDeviceId(device.getDeviceId());
                        deviceActiveMsg.setDeviceSn(device.getDeviceSn());
                        deviceActiveMsg.setTpDevId(device.getTpId());
                        deviceActiveMsg.setDeviceType(device.getDeviceType());
                        deviceActiveMsg.setName(device.getDeviceName());
                        deviceActiveMsg.setTimeStamp(System.currentTimeMillis());
                        deviceActiveMsg.setSceneType(sceneType);
                        deviceActiveMsg.setTreeId(device.getTreeId());
                        deviceActiveMsg.setMemberId(memberId);
                        EventBus.publish(deviceActiveMsg);
                        log.info("选择设备运行模式-激活消息发送成功deviceActiveMsg:{}",
                            deviceActiveMsg);
                    }
                });
        }
        serviceResponse.setData(result);
        return serviceResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Boolean> updateSceneTypeBySwitch(String deviceSn, Integer sceneType,
                                                    Long memberId) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();

        DeviceInfoDTO device = deviceService.getByDeviceSn(deviceSn).pickDataThrowException();
        if (device == null) {
            serviceResponse.setData(Boolean.FALSE);
            return serviceResponse;
        }

        InventoryDevice deviceWhite = whiteDeviceManager.getByDeviceSn(deviceSn);
        if (deviceWhite == null && device.getDeviceFlag().equals(DeviceFlagEnum.FX.getValue())){
            List<WhiteDeviceInfo> whiteDeviceList = ddBizManageHelper.getWhiteDeviceList(Collections.singletonList(deviceSn));
            if (CollectionUtils.isNotEmpty(whiteDeviceList)) {
                WhiteDeviceInfo whiteDeviceInfo = whiteDeviceList.get(0);
                whiteDeviceManager.insert(new InventoryDevice()
                        .setSerialNumber(deviceSn)
                        .setMacAddress(whiteDeviceInfo.getMacAddress())
                        .setMacAddress2(whiteDeviceInfo.getMacAddress2())
                        .setPrivateKey(whiteDeviceInfo.getSecretKey())
                        .setBatchFlag(whiteDeviceInfo.getPatchFlag())
                        .setOrgId(device.getOrgId())
                        .setActivityStatus(whiteDeviceInfo.getActivityStatus())
                        .setThirdDeviceId(whiteDeviceInfo.getThirdDeviceId())
                        .setDeviceType(device.getDeviceType())
                        .setDeviceSource(whiteDeviceInfo.getDeviceSource())
                        .setActivationCode(whiteDeviceInfo.getActivationCode())
                        .setIsOpenPlat(whiteDeviceInfo.getIsOpenPlat())
                        .setDeviceActivateOaBase(2)
                        .setDeviceActivateSdkBase(2));
                deviceWhite = whiteDeviceManager.getByDeviceSn(deviceSn);
            }else {
                serviceResponse.setData(Boolean.FALSE);
                return serviceResponse;
            }
        }
        Integer type = deviceWhite.getDeviceType();
        // 如果是通通锁，设备还未在数据库中，查不到数据，跳过运行模式的选择
        if (type != null && type == DeviceType.TTLOCK.getValue()) {
            //更新设备激活的状态为激活中
            activityManager.deviceActiveStateChange(deviceSn,
                    DeviceActiveStateEnum.DEVICE_ACTIVATING.getCode(), null, null);
            serviceResponse.setData(Boolean.TRUE);
            return serviceResponse;
        }

        Integer deviceType = device.getDeviceType();
        UpdateDeviceSceneTypeReq req = new UpdateDeviceSceneTypeReq();
        req.setDeviceSn(deviceSn);
        req.setDeviceId(device.getDeviceId());
        req.setOrgId(device.getOrgId());
        req.setSceneType(sceneType);
        boolean result = devicePipelineStateService.updateCloudSceneType(req)
                .pickDataThrowException();
        //如果修改成功 则发送激活成功消息
        if (result) {
            ActiveDeviceSceneTreeDTO activeDeviceSceneTreeDTO = new ActiveDeviceSceneTreeDTO();
            activeDeviceSceneTreeDTO.setOrgId(device.getOrgId());
            activeDeviceSceneTreeDTO.setDeviceId(device.getDeviceId());
            activeDeviceSceneTreeDTO.setDeviceSn(device.getDeviceSn());
            activeDeviceSceneTreeDTO.setSceneType(sceneType);
            activeDeviceSceneTreeDTO.setSendMsgFlag(Boolean.TRUE);

            // 旧版会议室标识
            boolean meetFlag = Boolean.FALSE;

            // 8j10、T10、T21三款老版本水牌激活，需要初始化会议室空间
            // 需要初始化会议室空间的设备类型，加上版本号控制
            if (deviceType != null &&
                    deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.MEETING_DEVICE_INITIAL_MEETING_ROOM, deviceType)) {
                List<DeviceVersion> apkVersions = deviceApkVersionMapper.getBatchDeviceApkVersionByOrgIdAndDeviceIds(device.getOrgId(),
                        Lists.newArrayList(device.getDeviceId()));
                if (CollectionUtils.isNotEmpty(apkVersions)) {
                    DeviceVersion apkVersion = apkVersions.get(0);
                    // 低于 30310020 这个版本需要绑定初始化会议室空间
                    if (apkVersion != null && apkVersion.getVersionCode() != null && apkVersion.getVersionCode() < 30310020) {
                        meetFlag = Boolean.TRUE;
                        spaceTreeService.createTreeBySceneTypeWithoutLevel(activeDeviceSceneTreeDTO)
                                .pickDataThrowException();
                    }
                }
            }

            // 通用的设备，会默认绑一个根空间
            if (!meetFlag && device.getDeviceFlag().equals(DeviceFlagEnum.HY.getValue())) {
                // 行业绑空间
                spaceHelper.bindRootSpace(device.getOrgId(), device.getDeviceId());
            }

            // 根据选择场景更新设备关联的应用
            List<String> appCode = sceneTypeHelper.getAppCodeBySceneType(sceneType);

            // 获取配置默认的code应用列表关联关系
            List<String> appRelationCodeList = getDeviceAppRelationConfig(null, deviceSn);
            // 判断默认code应用列表是否包含根据场景选择的应用，包含则应用场景关联的应用，不包含则只应用默认code应用列表
            if (CollectionUtils.isNotEmpty(appRelationCodeList) && !CollUtil.containsAll(appRelationCodeList, appCode)) {
                appCode = appRelationCodeList;
            }
            UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
            updateDeviceDTO.setDeviceId(device.getDeviceId());
            updateDeviceDTO.setOrgId(device.getOrgId());
            updateDeviceDTO.setAppCode(String.join(",", appCode));
            updateDeviceDTO.setAppCodeList(String.join(",", appCode));
            deviceManager.update(updateDeviceDTO);
        }
        serviceResponse.setData(result);
        return serviceResponse;
    }

    /**
     * 获取最新的版本配置的appCode
     *
     * @param spaceType   spaceType
     * @param deviceSn     deviceSn
     * @return appCode
     */
    private List<String> getDeviceAppRelationConfig(Integer spaceType, String deviceSn) {
        List<String> appCodeList = new ArrayList<>();
        if (spaceType == null){
            spaceType = -1;
        }
        //查询设备appType和versionCode
        DeviceVersion deviceVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(deviceSn);
        if (deviceVersion == null) {
            log.warn("设备版本信息为空,不做关联应用处理，deviceSn:{},timestamp:{}", deviceSn, System.currentTimeMillis());
            return appCodeList;
        }
        List<DeviceAppRelationConfig> deviceAppRelationConfigs = deviceAppRelationConfigManager
                .selectBySpaceTypeAndAppTypeAndVersionCode(spaceType, deviceVersion.getAppType(), deviceVersion.getVersionCode());
        if (CollectionUtils.isEmpty(deviceAppRelationConfigs)) {
            log.warn("未找到对应用配置信息,spaceType:{},appType:{},versionCode:{}", spaceType, deviceVersion.getAppType(), deviceVersion.getVersionCode());
            return appCodeList;
        }
        return CharSequenceUtil.split(deviceAppRelationConfigs.get(0).getAvailableAppCodeList(), StrPool.COMMA);
    }

    @Override
    public ServiceResponse<Boolean> checkOrgAuthorize(String tpId) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        OrgInfo orgInfo = orgService.getOrgByTp(TpType.DING, tpId).pickDataThrowException();
        if (orgInfo == null) {
            serviceResponse.setData(Boolean.FALSE);
            return serviceResponse;
        }
        String result = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE,
            BeeConfConfigConstants.DEVICE_AUTHORIZE_ORG_WHITE,
            orgInfo.getOrgId().toString());
        if (StringUtils.isNotBlank(result) && result.equals("1")) {
            serviceResponse.setData(Boolean.TRUE);
        } else {
            serviceResponse.setData(Boolean.FALSE);
        }
        return serviceResponse;
    }

    /**
     * 构建设备初始化的url地址
     */
    private void buildConfigUrl(DeviceBaseInfoDTO infoDTO, InventoryDevice inventoryDevice){
        Integer sdkBase = inventoryDevice.getDeviceActivateSdkBase();
        // 如果为空，默认非钉
        if (sdkBase == null){
            sdkBase = 1;
        }

        if (sdkBase == 1) {
            infoDTO.setDeviceBackGroundUrl(deviceInitConfigProperties.getBackUrlWechat());
            infoDTO.setDeviceTempAuthUrl(deviceInitConfigProperties.getTempAuthUrlWechat());
        }else if (sdkBase == 2){
            infoDTO.setDeviceBackGroundUrl(deviceInitConfigProperties.getBackUrlDing());
            infoDTO.setDeviceTempAuthUrl(deviceInitConfigProperties.getTempAuthUrlDing());
        }
    }
}