package com.moredian.magicube.device.manager.impl;

import com.alibaba.fastjson.JSON;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.RefreshDeviceConfigMsg;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceWhiteList;
import com.moredian.magicube.device.dao.mapper.DeviceMapper;
import com.moredian.magicube.device.dao.mapper.DeviceWhiteListMapper;
import com.moredian.magicube.device.dto.device.DeviceWhiteListDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceWhiteListManager;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author:dongchao
 * @Description:
 * @Date: 2020/2/24 22:55
 */
@Service
@Slf4j
public class DeviceWhiteListManagerImpl implements DeviceWhiteListManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private DeviceWhiteListMapper deviceWhiteListMapper;
    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    @Transactional
    public ServiceResponse insert(DeviceWhiteListDTO deviceWhiteListDto) {
        //生成id
        log.info("DeviceWhiteListManagerImpl insert request {}", JSON.toJSONString(deviceWhiteListDto));
        DeviceWhiteList deviceWhiteList = new DeviceWhiteList();
        deviceWhiteList.setDeviceSn(deviceWhiteListDto.getDeviceSn());
        //deviceWhiteList.setOrgId(deviceWhiteListDto.getOrgId());
        //查询设备是否在白名单中
        List<DeviceWhiteList> deviceWhiteLists = deviceWhiteListMapper.getDeviceWhiteList(deviceWhiteList);
        if (CollectionUtils.isEmpty(deviceWhiteLists)) {
            //插入新的
            ServiceResponse<Long> serviceResponse = idgeneratorService.getNextIdByTypeName("com.xier.sesame.attence.DeviceWhiteList");
            if (serviceResponse.isSuccess()) {
                DeviceWhiteList dwl = new DeviceWhiteList();
                BeanUtils.copyProperties(deviceWhiteListDto, dwl);
                dwl.setStatus(1);
                dwl.setWhiteListId(serviceResponse.getData());
                int res = deviceWhiteListMapper.insert(dwl);
                if (res > 0) {
                    return new ServiceResponse<>(true);
                }
            }
        } else {
            DeviceWhiteList dwl = new DeviceWhiteList();
            BeanUtils.copyProperties(deviceWhiteListDto, dwl);
            dwl.setStatus(1);
            int res = deviceWhiteListMapper.update(dwl);
            if (res > 0) {
                return new ServiceResponse<>(true);
            }

        }
        return new ServiceResponse(new ErrorContext(DeviceErrorCode.DEVICE_WHITE_LIST_DATA_ADD_FAIL), null);
    }


    @Override
    @Transactional
    public ServiceResponse insertBatch(List<DeviceWhiteListDTO> whiteListDtos) {
        log.info("DeviceWhiteListManagerImpl insertBatch request {}", JSON.toJSONString(whiteListDtos));
        List<DeviceWhiteList> list = new ArrayList<>();
        List<DeviceWhiteList> bindList = new ArrayList<>();
        //批量插入
        for (DeviceWhiteListDTO dto : whiteListDtos) {
            DeviceWhiteList dwlSearch = new DeviceWhiteList();
            dwlSearch.setDeviceSn(dto.getDeviceSn());
            List<DeviceWhiteList> deviceWhiteList = deviceWhiteListMapper.getDeviceWhiteList(dwlSearch);
            if (CollectionUtils.isNotEmpty(deviceWhiteList)) {
                //已经存在的 进行更新
                DeviceWhiteList dwl = deviceWhiteList.get(0);
                Device device = deviceMapper.getByDeviceSn(dwl.getDeviceSn());
                if(device != null){
                    dwl.setOrgId(device.getOrgId());
                    dwl.setDeviceName(device.getDeviceName());
                    dwl.setDeviceId(device.getDeviceId());
                    int updateRes = deviceWhiteListMapper.update(dwl);
                    if(updateRes > 0){
                        //消息通知
                        sendMessage(dwl.getOrgId(),dwl.getDeviceSn());
                    }
                }
                continue;
            }
            ServiceResponse<Long> serviceResponse = idgeneratorService.getNextIdByTypeName("com.xier.sesame.attence.DeviceWhiteList");
            if (serviceResponse.isSuccess()) {
                DeviceWhiteList dwl = new DeviceWhiteList();
                BeanUtils.copyProperties(dto, dwl);
                dwl.setStatus(1);
                dwl.setWhiteListId(serviceResponse.getData());
                //查询该设备是否已激活，如果激活，就完善插入信息
                Device device = deviceMapper.getByDeviceSn(dwl.getDeviceSn());
                if (device != null) {
                    dwl.setOrgId(device.getOrgId());
                    dwl.setDeviceName(device.getDeviceName());
                    dwl.setDeviceId(device.getDeviceId());
                    bindList.add(dwl);
                }
                list.add(dwl);
            } else {
                //抛出异常
                throw new RuntimeException("id获取失败");
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            int res = deviceWhiteListMapper.insertBatch(list);
            if (res > 0) {
                //对已经绑定orgId的设备进行通知
                for (DeviceWhiteList dwl : bindList) {
                    sendMessage(dwl.getOrgId(),dwl.getDeviceSn());
                }
                return new ServiceResponse<>(true);
            }
        } else {
            return new ServiceResponse<>(true);
        }
        return new ServiceResponse(new ErrorContext(DeviceErrorCode.DEVICE_WHITE_LIST_DATA_ADD_FAIL), null);
    }

    @Override
    public ServiceResponse<List<DeviceWhiteListDTO>> getDeviceWhiteList(DeviceWhiteListDTO deviceWhiteListDto) {
        DeviceWhiteList dwl = new DeviceWhiteList();
        BeanUtils.copyProperties(deviceWhiteListDto, dwl);
        List<DeviceWhiteListDTO> deviceWhiteListDtos = new ArrayList<>();
        List<DeviceWhiteList> deviceWhiteLists = deviceWhiteListMapper.getDeviceWhiteList(dwl);
        if (CollectionUtils.isEmpty(deviceWhiteLists)) {
            return new ServiceResponse<List<DeviceWhiteListDTO>>(deviceWhiteListDtos);
        }
        for (DeviceWhiteList dwlRes : deviceWhiteLists) {
            DeviceWhiteListDTO dto = new DeviceWhiteListDTO();
            BeanUtils.copyProperties(dwlRes, dto);
            deviceWhiteListDtos.add(dto);
        }
        return new ServiceResponse<List<DeviceWhiteListDTO>>(deviceWhiteListDtos);
    }


    @Override
    public ServiceResponse unBind(String deviceSn) {
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        int res = deviceWhiteListMapper.unBind(deviceSn);
        if (res > 0) {
            Device device = deviceMapper.getByDeviceSn(deviceSn);
            if (device != null) {
                //通知设备
                sendMessage(device.getOrgId(),deviceSn);
            }
            return new ServiceResponse<>(true);
        }
        return new ServiceResponse(new ErrorContext(DeviceErrorCode.DEVICE_WHITE_LIST_UN_BIND_FAIL), null);
    }

    @Override
    public ServiceResponse bind(DeviceWhiteListDTO deviceWhiteListDto) {
        DeviceWhiteList dwl = new DeviceWhiteList();
        dwl.setDeviceSn(deviceWhiteListDto.getDeviceSn());
        dwl.setDeviceId(deviceWhiteListDto.getDeviceId());
        dwl.setDeviceName(deviceWhiteListDto.getDeviceName());
        dwl.setOrgId(deviceWhiteListDto.getOrgId());
        int res = deviceWhiteListMapper.update(dwl);
        if (res > 0) {
            //通知设备
            sendMessage(deviceWhiteListDto.getOrgId(),deviceWhiteListDto.getDeviceSn());
            return new ServiceResponse<>(true);
        }
        return new ServiceResponse(new ErrorContext(DeviceErrorCode.DEVICE_WHITE_LIST_IND_SUCCESS), null);
    }

    @Override
    public ServiceResponse<Boolean> hasAvailableDevice(Long orgId) {
        DeviceWhiteList dwl = new DeviceWhiteList();
        dwl.setOrgId(orgId);
        dwl.setStatus(YesNoFlag.YES.getValue());
        List<DeviceWhiteList> deviceWhiteList = deviceWhiteListMapper.getDeviceWhiteList(dwl);
        if (CollectionUtils.isNotEmpty(deviceWhiteList)) {
            return new ServiceResponse<Boolean>(true);
        }
        return new ServiceResponse<Boolean>(false);
    }

    private void sendMessage(Long orgId , String deviceSn){
        RefreshDeviceConfigMsg msg = new RefreshDeviceConfigMsg();
        msg.setOrgId(orgId);
        msg.setDeviceSn(deviceSn);
        EventBus.publish(msg);
    }


}
