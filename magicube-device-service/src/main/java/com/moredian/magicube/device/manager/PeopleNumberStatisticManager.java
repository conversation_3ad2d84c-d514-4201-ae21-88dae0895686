package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.PeopleNumberStatistic;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PeopleNumberStatisticManager {

    /**
     * 新增摄像头人数统计信息
     *
     * @param peopleNumberStatistic 人数信息
     * @return
     */
    Long insert(PeopleNumberStatistic peopleNumberStatistic);

    /**
     * 根据设备sn列表查询摄像头人数统计信息
     *
     * @param orgId     机构Id
     * @param deviceSns 设备sn列表
     * @return
     */
    List<PeopleNumberStatistic> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns);

    /**
     * 根据orgId、设备id查询大华摄像头人数信息
     */
    List<PeopleNumberStatistic> listByOrgIdAndDeviceId(Long orgId, List<Long> deviceIds);
}
