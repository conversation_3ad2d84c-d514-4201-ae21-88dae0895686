package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.device.AppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.PageAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.QueryAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.SaveAppTypeRedirectDTO;
import com.moredian.magicube.device.manager.AppTypeRedirectManager;
import com.moredian.magicube.device.service.AppTypeRedirectService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/18
 */
@SI
public class AppTypeRedirectServiceImpl implements AppTypeRedirectService {

    @Autowired
    private AppTypeRedirectManager appTypeRedirectManager;

    @Override
    public ServiceResponse<Long> save(SaveAppTypeRedirectDTO dto) {
        return new ServiceResponse<>(appTypeRedirectManager.save(dto));
    }

    @Override
    public ServiceResponse<AppTypeRedirectDTO> detail(QueryAppTypeRedirectDTO dto) {
        return new ServiceResponse<>(appTypeRedirectManager.detail(dto));
    }

    @Override
    public ServiceResponse<Boolean> deleteById(Long id) {
        return new ServiceResponse<>(appTypeRedirectManager.deleteById(id));
    }

    @Override
    public ServiceResponse<Pagination<AppTypeRedirectDTO>> page(PageAppTypeRedirectDTO dto) {
        return new ServiceResponse<>(appTypeRedirectManager.page(dto));
    }
}
