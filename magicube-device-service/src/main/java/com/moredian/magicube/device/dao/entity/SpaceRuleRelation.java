package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_space_rule_relation")
public class SpaceRuleRelation extends TimedEntity {
    private Long spaceRuleRelationId;

    private Long orgId;

    private Long ruleId;

    private Long spaceId;

    /**
     * 场景
     */
    private String ruleJson;

    /**
     * 场景Id
     */
    private String sceneId;

    /**
     * 规则编排
     */
    private String ruleArrangeJson;

    /**
     * 规则编排Id
     */
    private String ruleArrangeId;
}