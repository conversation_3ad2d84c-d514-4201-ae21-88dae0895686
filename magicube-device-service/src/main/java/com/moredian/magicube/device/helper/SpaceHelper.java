package com.moredian.magicube.device.helper;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.space.dto.device.BindSpaceTreeDto;
import com.moredian.space.dto.tree.QueryTreeDTO;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 空间服务帮助类
 * @create 2025-03-13 14:53
 */
@Component
public class SpaceHelper {

    @SI
    private SpaceTreeService spaceTreeService;

    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;

    /**
     * 设备绑根空间(如果没根空间，返回 false)
     */
    public Boolean bindRootSpace(Long orgId, Long deviceId) {
        BizAssert.isTrue(orgId != null, "orgId must be not empty");
        BizAssert.isTrue(deviceId != null, "deviceId must be not empty");

        QueryTreeDTO treeDTO = new QueryTreeDTO();
        treeDTO.setParentId(0L);
        treeDTO.setOrgId(orgId);
        List<TreeDTO> treeDTOS = spaceTreeService.listByCondition(treeDTO).pickDataThrowException();
        Long rootTreeId = null;
        if (!ObjectUtils.isEmpty(treeDTOS)){
            TreeDTO item = treeDTOS.get(0);
            rootTreeId = item.getTreeId();
        }

        if (rootTreeId != null){
            // 默认将同步设备绑定到更空间下
            BindSpaceTreeDto dto = new BindSpaceTreeDto();
            dto.setOrgId(orgId);
            dto.setDeviceId(String.valueOf(deviceId));
            dto.setTreeId(rootTreeId);
            dto.setDeviceSource(1);
            dto.setSendMsgFlag(Boolean.FALSE);
            spaceTreeDeviceRelationService.bindSpaceV2(dto).pickDataThrowException();
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
