package com.moredian.magicube.device.jetlinks.rule.arrange;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description：ReactorQL 节点
 * @date ：2024/08/06 11:20
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class ReactorQLNode extends Node {

    public static final String TYPE = "reactor-ql";

    /**
     * 查询的sql语句
     */
    private String sql;

    public ReactorQLNode() {
        super(TYPE);
    }

    /** 定义查询设备的ReactorQL模板*/

    /**
     * 查询某个设备开关状态
     */
    public static final String DEVICE_POWER_STATUS = "SELECT \n" +
            "    id AS deviceId,\n" +
            "    device.property.recent(id,'#{propertyId}') AS #{propertyId},\n" +
            "    device.property.recent(id, 'k1_power') AS k1_power,\n" +
            "    device.property.recent(id, 'k2_power') AS k2_power,\n" +
            "    device.property.recent(id, 'k3_power') AS k3_power\n" +
            "FROM device.selector(device('#{deviceSn}'));";
}
