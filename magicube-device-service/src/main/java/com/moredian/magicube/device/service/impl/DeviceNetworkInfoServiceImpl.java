package com.moredian.magicube.device.service.impl;

import com.alibaba.fastjson.JSON;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.DeviceNetworkInfo;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.lock.NetworkInfoDTO;
import com.moredian.magicube.device.dto.network.DeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.network.InsertDeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import com.moredian.magicube.device.manager.ActivityManager;
import com.moredian.magicube.device.manager.DeviceNetworkInfoManager;
import com.moredian.magicube.device.service.DeviceNetworkInfoService;
import com.moredian.magicube.device.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.DEVICE_BIND_ORG;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class DeviceNetworkInfoServiceImpl implements DeviceNetworkInfoService {

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceNetworkInfoManager deviceNetworkInfoManager;

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    @Resource
    private ActivityManager activityManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResponse<Boolean> upload(InsertDeviceNetworkInfoDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceNetworkInfo networkInfo = new DeviceNetworkInfo();
        BeanUtils.copyProperties(dto, networkInfo);
        networkInfo.setCableStatic(
            dto.getCableStatic() == null ? null : JSON.toJSONString(dto.getCableStatic()));
        networkInfo
            .setWifiInfo(dto.getWifiInfo() == null ? null : JSON.toJSONString(dto.getWifiInfo()));
        DeviceNetworkInfo network = deviceNetworkInfoManager
            .getByOrgIdAndDeviceId(dto.getOrgId(), dto.getDeviceId());
        if (network == null) {
            deviceNetworkInfoManager.insert(networkInfo);
        } else {
            networkInfo.setId(network.getId());
            deviceNetworkInfoManager.update(networkInfo);
        }
        if (dto.getDeviceResetNetworkStatus() != null){
            DeviceInfoDTO deviceInfoDTO = deviceService.getByOrgIdAndId(dto.getOrgId(),dto.getDeviceId()).pickDataThrowException();
            if (deviceInfoDTO != null){
                String key = RedisConstants.getKey(RedisConstants.DEVICE_RESET_NETWORK_STATUS,
                    dto.getOrgId(), deviceInfoDTO.getDeviceSn());
                redissonCacheComponent.setObjectCache(key, dto.getDeviceResetNetworkStatus(),RedisConstants.DEVICE_RESET_NETWORK_TIME_OUT);
            }
        }
        serviceResponse.setData(Boolean.TRUE);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceNetworkInfoDTO> getByOrgIdAndDeviceId(Long orgId, Long deviceId) {
        ServiceResponse<DeviceNetworkInfoDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        DeviceNetworkInfo network = deviceNetworkInfoManager.getByOrgIdAndDeviceId(orgId, deviceId);
        if (network == null) {
            return serviceResponse;
        }
        DeviceNetworkInfoDTO deviceNetworkInfoDTO = new DeviceNetworkInfoDTO();
        BeanUtils.copyProperties(network, deviceNetworkInfoDTO);
        deviceNetworkInfoDTO.setCableStatic(network.getCableStatic() == null ? null
            : JSON.parseObject(network.getCableStatic(), CableStatic.class));
        deviceNetworkInfoDTO.setWifiInfo(network.getWifiInfo() == null ? null
            : JSON.parseObject(network.getWifiInfo(), WifiInfo.class));
        deviceNetworkInfoDTO.setLastTime(network.getGmtModify().getTime());

        //缓存中获取设备重新配网的状态
        DeviceInfoDTO deviceInfoDTO = deviceService.getByOrgIdAndId(orgId,deviceId).pickDataThrowException();
        if (deviceInfoDTO != null){
            String key = RedisConstants.getKey(RedisConstants.DEVICE_RESET_NETWORK_STATUS,
                orgId,deviceInfoDTO.getDeviceSn());
            Integer status = (Integer) redissonCacheComponent.getObjectCache(key);
            deviceNetworkInfoDTO.setDeviceResetNetworkStatus(status == null ? 3 : status);
        }
        serviceResponse.setData(deviceNetworkInfoDTO);
        return serviceResponse;
    }
}
