package com.moredian.magicube.device.manager.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.moredian.bee.common.utils.BeanUtils;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.PeripheralsWhiteList;
import com.moredian.magicube.device.dao.entity.PeripheralsWhiteListNew;
import com.moredian.magicube.device.dao.mapper.PeripheralsWhiteListMapper;
import com.moredian.magicube.device.dao.mapper.PeripheralsWhiteListNewMapper;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddBatchDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewErrorDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewInfoDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewQueryDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewUpdateDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.PeripheralsWhiteListNewManager;
import com.xier.sesame.common.exception.BizAssert;
import com.xier.sesame.common.rpc.dto.PaginationDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Auther: _AF
 * @Date: 2/23/22 17:51
 * @Description:
 */
@Service
public class PeripheralsWhiteListNewManagerImpl implements PeripheralsWhiteListNewManager {

    @Autowired
    private PeripheralsWhiteListNewMapper peripheralsWhiteListNewMapper;

    @Autowired
    private PeripheralsWhiteListMapper peripheralsWhiteListMapper;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private IdgeneratorService idgeneratorService;

    String OBJECT_NAME = "com.moredian.fishnet.device.domain.PeripheralsWhiteListNew";


    @Override
    public PaginationDto<PeripheralsWhiteListNewInfoDTO> pageByCondition(
        PeripheralsWhiteListNewQueryDTO dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        List<PeripheralsWhiteListNew> peripheralsWhiteListNews = peripheralsWhiteListNewMapper.pageByCondition(dto);
        PageInfo pageInfo = new PageInfo(peripheralsWhiteListNews);
        PaginationDto<PeripheralsWhiteListNewInfoDTO> paginationDto = new PaginationDto();
        paginationDto.setPageNo(pageInfo.getPageNum());
        paginationDto.setPageSize(pageInfo.getPageSize());
        paginationDto.setTotalCount((int) pageInfo.getTotal());
        List<PeripheralsWhiteListNewInfoDTO> responseDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(peripheralsWhiteListNews)) {
            responseDataList = BeanUtils.copyListProperties(PeripheralsWhiteListNewInfoDTO.class, peripheralsWhiteListNews);
        }
        paginationDto.setData(responseDataList);
        return paginationDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeByIds(List<Long> ids) {
        BizAssert.notNull(ids);
        peripheralsWhiteListNewMapper.removeByIds(ids);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateById(PeripheralsWhiteListNewUpdateDTO dto) {
        //校验
        checkAddAndParamParam(dto, null);

        PeripheralsWhiteListNew peripheralsWhiteListNew = peripheralsWhiteListNewMapper.getBySnAndType(dto.getPeripheralsSn(), dto.getPeripheralsType());
        //去重
        if (peripheralsWhiteListNew != null && !peripheralsWhiteListNew.getId().equals(dto.getId())) {
            ExceptionUtils.throwException(DeviceErrorCode.PERIPHREALS_SN_EXIST, DeviceErrorCode.PERIPHREALS_SN_EXIST.getMessage());
        }
        //更新
        PeripheralsWhiteListNew peripheralsWhiteListNewUpdate = BeanUtils.copyProperties(PeripheralsWhiteListNew.class, dto);
        peripheralsWhiteListNewMapper.updateById(peripheralsWhiteListNewUpdate);
        return true;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addPeripheralsWhiteListNew(PeripheralsWhiteListNewAddDTO dto) {
        //校验
        checkAddAndParamParam(null, dto);

        PeripheralsWhiteListNew peripheralsWhiteListNew = peripheralsWhiteListNewMapper.getBySnAndType(dto.getPeripheralsSn(), dto.getPeripheralsType());
        //去重
        if (peripheralsWhiteListNew != null) {
            ExceptionUtils.throwException(DeviceErrorCode.PERIPHREALS_SN_EXIST, DeviceErrorCode.PERIPHREALS_SN_EXIST.getMessage());
        }
        PeripheralsWhiteListNew peripheralsWhiteListNewAdd = BeanUtils.copyProperties(PeripheralsWhiteListNew.class, dto);
        peripheralsWhiteListNewAdd.setId(idgeneratorService.getNextIdByTypeName(OBJECT_NAME).getData());
        peripheralsWhiteListNewMapper.addPeripheralsWhiteNewList(peripheralsWhiteListNewAdd);
        return true;


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<PeripheralsWhiteListNewErrorDTO> addByBatch(List<PeripheralsWhiteListNewAddBatchDTO> dtoList) {
        List<PeripheralsWhiteListNewErrorDTO> responseList = new ArrayList<>();
        List<PeripheralsWhiteListNew> addList = new ArrayList<>();

        //peripheralsSn-peripheralsType组成map，用来做去重判断
        Map<String, String> snMap = new HashMap<>();
        for (PeripheralsWhiteListNewAddBatchDTO dto : dtoList) {
            String peripheralsSn = dto.getPeripheralsSn();
            Integer deviceType = dto.getDeviceType();
            Integer appType = dto.getAppType();
            //校验
            //这里判空最好放到锁外面，但是这样要遍历两次，目前只有hbk使用，所以并发可能性很小
            if (StringUtils.isBlank(peripheralsSn)) {
                PeripheralsWhiteListNewErrorDTO responseData = BeanUtils.copyProperties(PeripheralsWhiteListNewErrorDTO.class, dto);
                responseData.setErrorMsg("配件sn未填写");
                responseList.add(responseData);
                continue;
            }
            if (deviceType == null) {
                PeripheralsWhiteListNewErrorDTO responseData = BeanUtils.copyProperties(PeripheralsWhiteListNewErrorDTO.class, dto);
                responseData.setErrorMsg("关联设备类型未填写");
                responseList.add(responseData);
                continue;
            }
            if (dto.getDeviceType() != 0 && !deviceTypePropertyManager.containsDeviceType(
                DeviceTypeConstants.PERIPHERAL_ASSOCIATED_DEVICE_LIST, dto.getDeviceType())) {
                PeripheralsWhiteListNewErrorDTO responseData = BeanUtils.copyProperties(PeripheralsWhiteListNewErrorDTO.class, dto);
                responseData.setErrorMsg("关联设备类型错误");
                responseList.add(responseData);
                continue;
            }
            if (appType == null) {
                PeripheralsWhiteListNewErrorDTO responseData = BeanUtils.copyProperties(PeripheralsWhiteListNewErrorDTO.class, dto);
                responseData.setErrorMsg("关联设备appType未填写");
                responseList.add(responseData);
                continue;
            }
            //判断是否存在
            PeripheralsWhiteListNew peripheralsWhiteListNew = peripheralsWhiteListNewMapper.getBySnAndType(dto.getPeripheralsSn(), dto.getPeripheralsType());
            //去重
            if (peripheralsWhiteListNew != null) {
                PeripheralsWhiteListNewErrorDTO responseData = BeanUtils.copyProperties(PeripheralsWhiteListNewErrorDTO.class, dto);
                responseData.setErrorMsg("设备已存在");
                responseList.add(responseData);
                continue;
            }
            String mapKey = dto.getPeripheralsSn() + "-" + dto.getPeripheralsType();
            if (snMap.containsKey(mapKey)) {
                PeripheralsWhiteListNewErrorDTO responseData = BeanUtils.copyProperties(PeripheralsWhiteListNewErrorDTO.class, dto);
                responseData.setErrorMsg("设备重复");
                responseList.add(responseData);
                continue;
            }
            snMap.put(mapKey, null);
            PeripheralsWhiteListNew peripheralsWhiteListNewAdd = BeanUtils.copyProperties(PeripheralsWhiteListNew.class, dto);
            peripheralsWhiteListNewAdd.setId(idgeneratorService.getNextIdByTypeName(OBJECT_NAME).getData());
            addList.add(peripheralsWhiteListNewAdd);
        }
        if (addList.size() > 0) {
            peripheralsWhiteListNewMapper.addByBatch(addList);
        }
        return responseList;
    }

    @Override
    public PeripheralsWhiteListNewInfoDTO getBySnAndType(String peripheralsSn, Integer peripheralsType) {
        BizAssert.notBlank(peripheralsSn);
        BizAssert.notNull(peripheralsType);
        PeripheralsWhiteListNew peripheralsWhiteListNew = peripheralsWhiteListNewMapper.getBySnAndType(peripheralsSn, peripheralsType);
        if (peripheralsWhiteListNew != null) {
            return BeanUtils.copyProperties(PeripheralsWhiteListNewInfoDTO.class, peripheralsWhiteListNew);
        }
        return null;
    }

    @Override
    public List<PeripheralsWhiteListNewInfoDTO> getBySnList(List<String> peripheralsSnList) {
        return null;
    }

    @Override
    public void syncOldDataToNewData() {
        int peripheralsSnCount = peripheralsWhiteListMapper.countPeripheralsSn();
        if (peripheralsSnCount <= 0) {
            return;
        }
        //分页读取新配置的数据到
        int pageSize = 500;
        int pageNo = peripheralsSnCount % pageSize > 0 ? peripheralsSnCount / pageSize + 1 : peripheralsSnCount / pageSize;
        for (int i = 1; i <= pageNo; i++) {
            int sizeBegin = (i - 1) * pageSize;
            List<PeripheralsWhiteList> peripheralsList = peripheralsWhiteListMapper.pagePeripheralsSn(sizeBegin, pageSize);
            if (CollectionUtils.isNotEmpty(peripheralsList)) {
                List<PeripheralsWhiteListNew> peripheralsWhiteListNewList = new ArrayList<>();
                for (PeripheralsWhiteList peripherals : peripheralsList) {
                    PeripheralsWhiteListNew peripheralsWhiteListNew = new PeripheralsWhiteListNew();
                    peripheralsWhiteListNew.setId(peripherals.getPeripheralsWhiteListId());
                    peripheralsWhiteListNew.setPeripheralsSn(peripherals.getPeripheralsSn());
                    peripheralsWhiteListNew.setDeviceType(YesNoFlag.NO.getValue());
                    peripheralsWhiteListNew.setAppType(YesNoFlag.NO.getValue());
                    peripheralsWhiteListNew.setPeripheralsType(peripherals.getPeripheralsType());
                    peripheralsWhiteListNewList.add(peripheralsWhiteListNew);
                }
                if (peripheralsWhiteListNewList.size() > 0) {
                    peripheralsWhiteListNewMapper.addByBatch(peripheralsWhiteListNewList);
                }
            }
        }
    }

    /**
     * 校验入参
     */
    private void checkAddAndParamParam(PeripheralsWhiteListNewUpdateDTO updateRequest, PeripheralsWhiteListNewAddDTO addRequest) {
        PeripheralsWhiteListNew peripheralsWhiteListNew = new PeripheralsWhiteListNew();
        if (updateRequest != null) {
            BizAssert.notNull(updateRequest.getId());
            BeanUtils.copyProperties(peripheralsWhiteListNew, updateRequest);
        } else {
            BizAssert.notNull(addRequest);
            BeanUtils.copyProperties(peripheralsWhiteListNew, addRequest);
        }
        BizAssert.notBlank(peripheralsWhiteListNew.getPeripheralsSn());
        BizAssert.notNull(peripheralsWhiteListNew.getAppType());
        BizAssert.notNull(peripheralsWhiteListNew.getDeviceType());
        BizAssert.notNull(peripheralsWhiteListNew.getPeripheralsType());
        if (peripheralsWhiteListNew.getPeripheralsSn().length() > 100) {
            ExceptionUtils.throwException(DeviceErrorCode.LENGTH_TOO_LONG, DeviceErrorCode.LENGTH_TOO_LONG.getMessage());
        }
        if (peripheralsWhiteListNew.getDeviceType() != 0 && !deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.PERIPHERAL_ASSOCIATED_DEVICE_LIST, peripheralsWhiteListNew.getDeviceType())) {
            ExceptionUtils.throwException(DeviceErrorCode.DEVICE_TYPE_NOT_SUPPORT, DeviceErrorCode.DEVICE_TYPE_NOT_SUPPORT.getMessage());
        }
    }
}
