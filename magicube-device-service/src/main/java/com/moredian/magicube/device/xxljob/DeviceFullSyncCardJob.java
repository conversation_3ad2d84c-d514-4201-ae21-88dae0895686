package com.moredian.magicube.device.xxljob;

import com.moredian.bee.job.annotation.BeeXxlJob;
import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.rmq.utils.JsonUtils;
import com.moredian.magicube.common.enums.BizSystemType;
import com.moredian.magicube.common.model.msg.CardFullSyncMsg;
import com.moredian.magicube.core.org.request.GetAllOrgIdRequest;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 设备完全同步卡作业
 *
 * <AUTHOR>
 * @date 2024/09/26 10:36
 */
@Component
@Slf4j
public class DeviceFullSyncCardJob {
    @Autowired
    private RedissonLockComponent redissonLockComponent;


    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Resource
    private OrgService orgService;

    /**
     * 设备全量同步卡信息
     *
     * @param params deviceSN1,deviceSN2... 可多个
     * @return {@link ReturnT }<{@link String }>
     */
    @BeeXxlJob(value = "deviceFullSyncCard", name = "设备全量同步卡任务，便于异常情况下数据订正")
    public ReturnT<String> deviceFullSyncCard(String params) {
        ReturnT<String> success = ReturnT.SUCCESS;

        try {
            doCardFullSync(params, success);
        } catch (Exception e) {
            ReturnT<String> fail = ReturnT.FAIL;
            fail.setMsg(e.getMessage());
            return fail;
        }

        return success;
    }

    private void doCardFullSync(String params, ReturnT<String> success) {
        if (!StringUtils.isEmpty(params)) {
            String[] deviceSnList = params.split(",");
            for (String deviceSn : deviceSnList) {
                buildAndSendMqMsg(deviceSn);
            }
            success.setMsg("设备全量同步卡信息发送消息完成");
        } else {
            // 查所有的机构
            GetAllOrgIdRequest request = new GetAllOrgIdRequest();
            request.setBizSystemTypeList(BizSystemType.getAllValue());
            // 所有的实体机构
            List<Long> orgIds = orgService.findAllOrgId(request).pickDataThrowException();
            // 所有的虚拟机构
            // ...

            for (Long orgId : orgIds) {
                // 如果不带参数，就是全量拉取
                List<Device> devices = deviceManager.listByOrgIdAndTypes(orgId,
                    deviceTypePropertyManager.getDeviceTypeByPropertyKey(
                        DeviceTypeConstants.ENABLE_CARD_DEVICE_TYPE_LIST));
                if (CollectionUtils.isNotEmpty(devices)) {
                    List<String> deviceSnList = devices.stream().map(Device::getDeviceSn).distinct()
                        .collect(Collectors.toList());
                    for (String deviceSn : deviceSnList) {
                        try {
                            if (redissonLockComponent.acquire(
                                RedisKeys.getKey(RedisKeys.CARD_FULL_SYNC_DEVICE_LOCK, deviceSn),
                                null, RedisKeys.RELEASE_TIME)) {
                                buildAndSendMqMsg(deviceSn);
                            } else {
                                log.error(
                                    "设备进行卡片全量同步获取分布式锁失败orgId={},deviceSN={}",
                                    orgId, deviceSn);
                            }
                        } catch (Exception e) {
                            log.error(
                                "orgId={},deviceSN={},设备进行卡片全量同步获取分布式锁message:{}",
                                orgId, deviceSn, e.getMessage());
                        } finally {
                            redissonLockComponent.release(
                                RedisKeys.getKey(RedisKeys.CARD_FULL_SYNC_DEVICE_LOCK, deviceSn));
                        }
                    }
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.error("休眠{}ms出现异常,error:{}", 1000, e.getMessage(), e);
                }
            }
        }
    }

    private static void buildAndSendMqMsg(String deviceSn) {
        if (StringUtils.isNotBlank(deviceSn)) {
            CardFullSyncMsg msg = new CardFullSyncMsg();
            msg.setDeviceSN(deviceSn);
            int publish = EventBus.publish(msg);
            log.info("MQ-LOG send CardFullSync msg={},publish-value={}",
                JsonUtils.serialize(msg), publish);
        }
    }
}
