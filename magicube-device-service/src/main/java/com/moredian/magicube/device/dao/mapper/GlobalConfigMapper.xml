<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.GlobalConfigMapper">

    <resultMap id="baseResultMap" type="com.moredian.magicube.device.dao.entity.GlobalConfig">
        <id column="id" property="id"/>
        <result column="config_name" property="configName"/>
        <result column="config_value" property="configValue"/>
        <result column="config_status" property="configStatus"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <select id="getByConfigName" parameterType="string" resultMap="baseResultMap">
        select *
        from
        <include refid="table_name"/>
        where config_name = #{configName} and config_status = 1
    </select>

    <sql id="table_name">
        hive_global_config
    </sql>

</mapper>