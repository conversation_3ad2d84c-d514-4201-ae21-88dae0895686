package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.accessory.AccessoryInfoDTO;
import com.moredian.magicube.device.dto.accessory.AccessoryQueryDTO;
import com.moredian.magicube.device.dto.accessory.BindAccessoryDTO;
import com.moredian.magicube.device.dto.accessory.UnbindAccessoryDTO;
import com.moredian.magicube.device.manager.AccessoryManager;
import com.moredian.magicube.device.service.AccessoryService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2021/12/8
 */
@SI
@Slf4j
public class AccessoryServiceImpl implements AccessoryService {

    @Autowired
    private AccessoryManager accessoryManager;

    @Override
    public ServiceResponse<Boolean> bindAccessory(BindAccessoryDTO dto) {
        return new ServiceResponse<>(accessoryManager.bindAccessory(dto));
    }

    @Override
    public ServiceResponse<Boolean> unbindAccessory(UnbindAccessoryDTO dto) {
        return new ServiceResponse<>(accessoryManager.unbindAccessory(dto));
    }

    @Override
    public ServiceResponse<List<AccessoryInfoDTO>> listAccessoryInfo(AccessoryQueryDTO dto) {
        return new ServiceResponse<>(accessoryManager.listAccessoryInfo(dto));
    }
}
