package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.qrcode.QrCodeActivateRecord;
import com.moredian.magicube.device.dao.mapper.QrCodeActivateRecordMapper;
import com.moredian.magicube.device.dto.qrcode.QrCodeActivateRecordResponse;
import com.moredian.magicube.device.manager.QrCodeActivateRecordManager;
import com.xier.sesame.common.utils.BeanUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Classname： QrCodeActivateRecordManagerImpl
 * @Date: 2023/1/12 11:12 上午
 * @Author: _AF
 * @Description:
 */
@Service
public class QrCodeActivateRecordManagerImpl implements QrCodeActivateRecordManager {

    @Autowired
    private QrCodeActivateRecordMapper qrCodeActivateRecordMapper;
    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Long insert(QrCodeActivateRecord qrCodeActivateRecord) {
        BizAssert.notNull(qrCodeActivateRecord.getQrCodeId());
        BizAssert.notBlank(qrCodeActivateRecord.getDeviceSn());
        BizAssert.notNull(qrCodeActivateRecord.getOrgId());
        BizAssert.notNull(qrCodeActivateRecord.getDeviceId());
        BizAssert.notNull(qrCodeActivateRecord.getDeviceType());
        qrCodeActivateRecord.setId(idgeneratorService.getNextIdByTypeName(QrCodeActivateRecord.class.getName()).pickDataThrowException());
        qrCodeActivateRecordMapper.insert(qrCodeActivateRecord);
        return qrCodeActivateRecord.getId();
    }

    @Override
    public List<QrCodeActivateRecordResponse> listByQrCodeId(Long orgId, Long qrCodeId) {
        BizAssert.notNull(orgId);
        BizAssert.notNull(qrCodeId);
        List<QrCodeActivateRecordResponse> qrCodeActivateRecordResponseList = new ArrayList<>();
        List<QrCodeActivateRecord> qrCodeActivateRecordList = qrCodeActivateRecordMapper.listByQrCodeId(orgId, qrCodeId);
        if (CollectionUtils.isEmpty(qrCodeActivateRecordList)) {
            return qrCodeActivateRecordResponseList;
        }
        for (QrCodeActivateRecord record : qrCodeActivateRecordList) {
            QrCodeActivateRecordResponse response = new QrCodeActivateRecordResponse();
            org.springframework.beans.BeanUtils.copyProperties(record, response);
            qrCodeActivateRecordResponseList.add(response);
        }
        return qrCodeActivateRecordResponseList;
    }

    @Override
    public QrCodeActivateRecordResponse getByDeviceId(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId);
        BizAssert.notNull(deviceId);
        List<QrCodeActivateRecordResponse> qrCodeActivateRecordResponseList = new ArrayList<>();
        QrCodeActivateRecord qrCodeActivateRecord = qrCodeActivateRecordMapper.getByDeviceId(orgId, deviceId);
        if (qrCodeActivateRecord != null) {
           return BeanUtils.copyProperties(QrCodeActivateRecordResponse.class, qrCodeActivateRecord);
        }
        return null;
    }

    @Override
    public QrCodeActivateRecordResponse getNewestRecordByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        QrCodeActivateRecord qrCodeActivateRecord = qrCodeActivateRecordMapper.getNewestRecordByOrgId(orgId);
        if (qrCodeActivateRecord != null) {
            QrCodeActivateRecordResponse response = new QrCodeActivateRecordResponse();
            response.setId(qrCodeActivateRecord.getId());
            response.setOrgId(qrCodeActivateRecord.getOrgId());
            response.setDeviceId(qrCodeActivateRecord.getDeviceId());
            response.setDeviceSn(qrCodeActivateRecord.getDeviceSn());
            response.setQrCodeId(qrCodeActivateRecord.getQrCodeId());
            response.setGmtCreate(qrCodeActivateRecord.getGmtCreate());
            return response;
        }
        return null;
    }
}
