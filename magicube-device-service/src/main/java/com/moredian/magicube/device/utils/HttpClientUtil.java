package com.moredian.magicube.device.utils;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HttpClientUtil {

    private static volatile PoolingHttpClientConnectionManager cm;

    private static void init() {
        if (cm == null) {
            synchronized (HttpClientUtil.class){
                if (cm == null){
                    cm = new PoolingHttpClientConnectionManager();
                    cm.setMaxTotal(50); // 整个连接池最大连接数
                    cm.setDefaultMaxPerRoute(5); // 每路由最大连接数，默认值是2
                }
            }
        }
    }

    /**
     * 通过连接池获取HttpClient
     */
    private static CloseableHttpClient getHttpClient() {
        init();
        return HttpClients.custom().setConnectionManager(cm).build();
    }

    /**
     * 普通 GET 请求
     */
    public static String get(String url) throws IOException {
        HttpGet httpGet = new HttpGet(url);
        return getResult(httpGet);
    }


    /**
     * 带参数 GET 请求
     * @param url、params
     */
    public static String get(String url, Map<String, String > params) throws IOException, URISyntaxException {
        // 格式化参数
        List<NameValuePair> pairs = covertParams2NVPS(params);
        URIBuilder ub = new URIBuilder().setPath(url).setParameters(pairs);
        HttpGet httpGet = new HttpGet(ub.build());
        return getResult(httpGet);
    }


    /**
     * @param url、headers、params
     */
    public static String get(String url, Map<String, Object> headers, Map<String, String> params)
            throws URISyntaxException, IOException {
        URIBuilder ub = new URIBuilder();
        ub.setPath(url);
        if (ObjectUtil.isNotEmpty(params)) {
            List<NameValuePair> pairs = covertParams2NVPS(params);
            ub.setParameters(pairs);
        }

        HttpGet httpGet = new HttpGet(ub.build());
        if (ObjectUtil.isNotEmpty(params)) {
            for (Map.Entry<String, Object> param : headers.entrySet()) {
                httpGet.addHeader(param.getKey(), String.valueOf(param.getValue()));
            }
        }
        return getResult(httpGet);
    }

    /**
     * 普通 POST 请求
     */
    public static String post(String url) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        return getResult(httpPost);
    }


    /**
     * 带参数的 POST 请求
     * 默认请求体是json
     * @param url、params
     */
    public static String post(String url, Object object) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(new StringEntity(JSON.toJSONString(object), ContentType.APPLICATION_JSON));
        return getResult(httpPost);
    }


    /**
     * @param url、headers、params
     */
    public static String post(String url, Map<String, Object> headers, Object object)
            throws IOException {
        HttpPost httpPost = new HttpPost(url);

        if (headers != null) {
            for (Map.Entry<String, Object> param : headers.entrySet()) {
                httpPost.addHeader(param.getKey(), String.valueOf(param.getValue()));
            }
        }
        httpPost.setEntity(new StringEntity(JSON.toJSONString(object), ContentType.APPLICATION_JSON));
        return getResult(httpPost);
    }


    /**
     * 将参数解析成 url 路径参数
     */
    private static List<NameValuePair> covertParams2NVPS(Map<String, String> params) {
        List<NameValuePair> pairs = new ArrayList<>();
        if (params == null) {
            return pairs;
        }
        for (Map.Entry<String, String> param : params.entrySet()) {
            if (param.getValue() != null) {
                pairs.add(new BasicNameValuePair(param.getKey(), param.getValue()));
            }
        }
        return pairs;
    }

    /**
     * 处理 Http 请求
     */
    private static String getResult(HttpRequestBase request) throws IOException {
        CloseableHttpClient httpClient = getHttpClient();
        CloseableHttpResponse response = httpClient.execute(request);
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            String result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            response.close();
            return result;
        }
        return StringUtils.EMPTY_STRING;
    }
}