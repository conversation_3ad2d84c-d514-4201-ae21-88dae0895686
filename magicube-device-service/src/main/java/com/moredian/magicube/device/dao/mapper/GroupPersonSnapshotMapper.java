package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.GroupPersonSnapshot;
import com.moredian.magicube.device.model.PersonIdAndGroupIdSnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 权限组快照表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@Mapper
public interface GroupPersonSnapshotMapper {

    /**
     * 批量插入
     *
     * @param groupPersonSnapshotList
     * @return
     */
    int batchInsert(@Param("groupPersonSnapshotList") List<GroupPersonSnapshot> groupPersonSnapshotList);

    /**
     * 根据snapshotId删除记录
     *
     * @param orgId
     * @param snapshotId
     * @return
     */
    int deleteBySnapshotId(@Param("orgId") Long orgId, @Param("snapshotId") Long snapshotId);

    /**
     * 获取权限组的总人数(去重)
     *
     * @param orgId
     * @param snapshotId
     * @param groupIdList
     * @return
     */
    int getDistinctPersonSize(@Param("orgId") Long orgId, @Param("snapshotId") Long snapshotId, @Param("groupIdList") List<Long> groupIdList);

    /**
     * 分页去重查询组内人员
     *
     * @param orgId
     * @param snapshotId
     * @param groupIds
     * @param offset
     * @param limit
     * @return
     */
    List<GroupPersonSnapshot> pageGroupDistinctPerson(@Param("orgId") Long orgId, @Param("snapshotId") Long snapshotId, @Param("groupIds") List<Long> groupIds, @Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 根据人员查询组
     *
     * @param orgId
     * @param snapshotId
     * @param groupIdList
     * @param personIdList
     * @return
     */
    List<PersonIdAndGroupIdSnapshot> findGroupIdByPersonIds(@Param("orgId") Long orgId, @Param("snapshotId") Long snapshotId, @Param("groupIdList") List<Long> groupIdList, @Param("personIdList") List<Long> personIdList);
}
