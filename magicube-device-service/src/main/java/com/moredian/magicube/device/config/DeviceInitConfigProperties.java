package com.moredian.magicube.device.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description：设备激活初始化配置
 * @date ：2024/09/25 10:53
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "device.activate.init-config")
public class DeviceInitConfigProperties {

    /**
     * 设备后台地址（钉）
     */
    private String backUrlDing;
    /**
     * 设备后台地址（非钉）
     */
    private String backUrlWechat;
    /**
     * 临时权限地址（钉）
     */
    private String tempAuthUrlDing;
    /**
     * 临时权限地址（非钉）
     */
    private String tempAuthUrlWechat;
}
