package com.moredian.magicube.device.jetlinks.rule.arrange;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 规则编排中 HTTP 请求节点
 * @create 2024-12-24 11:47
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class HttpRequestNode extends Node{

    private static final String TYPE = "http request";

    private String authInsertType = "header";

    private String authType;

    private String client_id;

    private String client_secret;

    private Integer connectTimeout = 5000;

    private String grantType = "client_credentials";

    private String method;

    private String password;

    private Boolean paytoqs = false;

    private String txt;

    private String scope;

    private String tls;

    private String tokenUrl;

    private String trustAll = "true";

    private String url;

    private String user;

    private Boolean usetls = false;

    public HttpRequestNode() {
        super(TYPE);
    }
}
