package com.moredian.magicube.device.convertor;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.mybatis.convertor.PaginationConvertor;
import com.moredian.bee.mybatis.domain.PaginationDomain;
import com.moredian.magicube.device.dao.entity.AppVersion;
import com.moredian.magicube.device.dto.version.VersionDTO;
import com.moredian.ota.api.response.apk.OtaApkExtraInfoResponse;
import com.moredian.ota.api.response.apk.OtaApkResponse;
import com.moredian.ota.api.response.apk.OtaApkWithExtraInfoResponse;
import java.util.Arrays;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * app版本信息数据转换器
 *
 * <AUTHOR>
 */

public class AppVersionConvertor {

    public static VersionDTO appVersionToVersionDTO(AppVersion appVersion) {
        if (appVersion == null) {
            return null;
        }
        VersionDTO versionDTO = new VersionDTO();
        BeanUtils.copyProperties(appVersion, versionDTO);
        return versionDTO;
    }

    public static AppVersion versionDTOToAppVersion(VersionDTO versionDTO) {
        if (versionDTO == null) {
            return null;
        }
        AppVersion appVersion = new AppVersion();
        BeanUtils.copyProperties(versionDTO, appVersion);
        return appVersion;
    }

    public static List<VersionDTO> appVersionListToversionDTOList(List<AppVersion> appVersionList) {
        if (appVersionList == null) {
            return null;
        }
        List<VersionDTO> versionDTOList = new ArrayList<VersionDTO>();
        for (AppVersion appVersion : appVersionList) {
            versionDTOList.add(appVersionToVersionDTO(appVersion));
        }
        return versionDTOList;
    }

    public static List<AppVersion> versionDTOListToAppVersionList(List<VersionDTO> versionDTOList) {
        if (versionDTOList == null) {
            return null;
        }
        List<AppVersion> AppVersionList = new ArrayList<>();
        for (VersionDTO versionDTO : versionDTOList) {
            AppVersionList.add(versionDTOToAppVersion(versionDTO));
        }
        return AppVersionList;
    }

    public static Pagination<VersionDTO> paginationAppVersionToPaginationVersionDTO(PaginationDomain<AppVersion> paginationDomain) {
        Pagination<VersionDTO> paginationDto = PaginationConvertor.paginationDomainToPagination(paginationDomain, new Pagination<>());
        if (paginationDto == null) {
            return null;
        }
        paginationDto.setData(appVersionListToversionDTOList(paginationDomain.getData()));
        return paginationDto;
    }

    public static PaginationDomain<AppVersion> paginationVersionDTOToPaginationAppVersion(Pagination<VersionDTO> paginationDto) {
        PaginationDomain<AppVersion> paginationDomain = PaginationConvertor.paginationToPaginationDomain(paginationDto, new PaginationDomain<>());
        if (paginationDomain == null) {
            return null;
        }
        paginationDomain.setData(versionDTOListToAppVersionList(paginationDto.getData()));
        return paginationDomain;
    }

    public static VersionDTO otaApkResponseToAppVersionDto(OtaApkResponse otaApkResponse) {
        if (otaApkResponse == null) {
            return null;
        }
        VersionDTO versionDTO = new VersionDTO();
        versionDTO.setVersionId(otaApkResponse.getId());
        versionDTO.setSystemType(1);
        versionDTO.setAppType(otaApkResponse.getAppType());
        versionDTO.setVersionCode(otaApkResponse.getVersionCode());
        versionDTO.setVersionName(String.valueOf(otaApkResponse.getVersionCode()));
        versionDTO.setVersionDesc(otaApkResponse.getDescription());
        versionDTO.setAppUrl(otaApkResponse.getApkFileUrl());
        versionDTO.setIsActive(otaApkResponse.getOpenForOrgUser());
        versionDTO.setIsEnforceUpdate(otaApkResponse.getInitDeviceUpgrade());
        versionDTO.setExtendInfo(otaApkResponse.getExtendInfo());
        return versionDTO;
    }

    public static VersionDTO otaApkWithExtraInfoToAppVersionDto(
        OtaApkWithExtraInfoResponse response) {
        if (response == null) {
            return null;
        }
        VersionDTO versionDTO = null;
        OtaApkResponse otaApkResponse = response.getOtaApkResponse();
        if (otaApkResponse != null) {
            versionDTO = new VersionDTO();
            versionDTO.setVersionId(otaApkResponse.getId());
            versionDTO.setSystemType(1);
            versionDTO.setAppType(otaApkResponse.getAppType());
            versionDTO.setVersionCode(otaApkResponse.getVersionCode());
            versionDTO.setVersionName(String.valueOf(otaApkResponse.getVersionCode()));
            versionDTO.setVersionDesc(otaApkResponse.getDescription());
            versionDTO.setAppUrl(otaApkResponse.getApkFileUrl());
            versionDTO.setIsActive(otaApkResponse.getOpenForOrgUser());
            versionDTO.setIsEnforceUpdate(otaApkResponse.getInitDeviceUpgrade());
            OtaApkExtraInfoResponse otaApkExtraInfoResponse = response.getOtaApkExtraInfoResponse();
            if (otaApkExtraInfoResponse != null) {
                if (StringUtils.isNotBlank(otaApkExtraInfoResponse.getFaceModels())) {
                    versionDTO.setFaceModels(
                        Arrays.asList(otaApkExtraInfoResponse.getFaceModels().split(",")));
                }
                versionDTO
                    .setEnforcementUpgrade(otaApkExtraInfoResponse.getEnforcementUpgrade());
            }
            if (otaApkResponse.getRomFlag() && !otaApkResponse.getIncrFlag()) {
                if (otaApkResponse.getApkAppType() != null && otaApkResponse.getApkAppType() > 0) {
                    versionDTO.setApkAppType(otaApkResponse.getApkAppType());
                }
                if (otaApkResponse.getApkVersionCode() != null && otaApkResponse.getApkVersionCode() > 0) {
                    versionDTO.setApkVersionCode(otaApkResponse.getApkVersionCode());
                }
            }
            versionDTO.setExtendInfo(otaApkResponse.getExtendInfo());
            versionDTO.setGmtModify(otaApkResponse.getGmtCreate());
        }
        return versionDTO;
    }
}