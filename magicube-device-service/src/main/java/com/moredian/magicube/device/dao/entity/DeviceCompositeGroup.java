package com.moredian.magicube.device.dao.entity;

import com.moredian.magicube.device.enums.DeviceCompositeGroupRangeType;
import java.util.Date;
import lombok.Data;

/**
 * 通行权限组-设备组关系
 * <AUTHOR>
 * @date 2024/11/21 11:49
 */

@Data
public class DeviceCompositeGroup {
    /**
     * ID
     */
    private Long deviceCompositeGroupId;
    /**
     * 机构Id
     */
    private Long orgId;
    /**
     * 通行组ID
     */
    private Long groupId;
    /**
     * 设备组ID
     */
    private Long rangeId;
    /**
     * 范围类型,{@link DeviceCompositeGroupRangeType}
     */
    private Integer rangeType;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModify;
}
