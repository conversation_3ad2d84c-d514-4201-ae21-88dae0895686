package com.moredian.magicube.device.manager;


import com.moredian.magicube.device.dao.entity.PeripheralsWhiteList;

import java.util.List;

/**
 * 外设白名单管理
 *
 * <AUTHOR>
 */

public interface PeripheralsWhiteListManager {

    /**
     * 批量添加
     *
     * @param peripheralsWhiteListList
     * @return
     */
    void batchInsert(List<PeripheralsWhiteList> peripheralsWhiteListList);

    /**
     * 根据sn 和类型查询
     *
     * @param peripheralsSn
     * @param peripheralsType
     * @return
     */
    PeripheralsWhiteList getBySnAndType(String peripheralsSn, Integer peripheralsType);

    /**
     * 根据sn 查询
     *
     * @param peripheralsSnList
     * @return
     */
    List<PeripheralsWhiteList> getBySnList(List<String> peripheralsSnList);

    /**
     * 根据id修改
     *
     * @param peripheralsWhiteListId
     * @param status
     * @return
     */
    int updateStatusById(Long peripheralsWhiteListId, Integer status);

}
