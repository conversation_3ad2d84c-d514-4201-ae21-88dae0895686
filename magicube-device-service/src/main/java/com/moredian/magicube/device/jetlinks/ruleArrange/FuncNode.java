package com.moredian.magicube.device.jetlinks.ruleArrange;

import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
/**
 * <AUTHOR>
 * @version $Id: FunNode.java, v 1.0 Exp $
 */
public class FuncNode extends Node{

    private DeviceMessageSenderNode messageSenderNode;

    public FuncNode(String modelId, DeviceMessageSenderNode messageSenderNode, DeviceTypePropertyManager deviceTypePropertyManager) {
        super(modelId, messageSenderNode.flow, deviceTypePropertyManager);
        this.messageSenderNode = messageSenderNode;
    }

    public String wrapperContent(TreeDeviceRelationDTO treeDeviceRelation, int index) {
        String group = getGroup(treeDeviceRelation);
        String switchPropertyId = getPropertyId(treeDeviceRelation, group);
        String func = getFunc(deviceSn(treeDeviceRelation), switchPropertyId);
        flow.addNode("{\\\"id\\\":\\\""+id+"\\\",\\\"type\\\":\\\"function\\\",\\\"z\\\":\\\""+modelId+"\\\",\\\"name\\\":\\\"\\\",\\\"func\\\":\\\""+func+"\\\",\\\"outputs\\\":1,\\\"noerr\\\":0,\\\"x\\\":430,\\\"y\\\":"+ (2100 + (index * 100))+",\\\"wires\\\":[[\\\""+messageSenderNode.id+"\\\"]]}");
        return id;
    }

    private String getFunc(String deviceSn, String switchPropertyId) {
        return "handler.onMessage(function (msg) {\\\\n\\\\n    //logger.info('handle data :{}',msg.data);\\\\n    return {\\\\n        \\\\\\\"messageType\\\\\\\": \\\\\\\"WRITE_PROPERTY\\\\\\\",\\\\n        \\\\\\\"deviceId\\\\\\\": '"+deviceSn+"',\\\\n        \\\\\\\"properties\\\\\\\": {\\\\n            \\\\\\\""+switchPropertyId+"\\\\\\\": \\\\\\\""+RuleStateEnum.DISABLE.getCode()+"\\\\\\\"\\\\n        }\\\\n    }\\\\n})";
    }
}
