package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.migration.*;
import com.moredian.magicube.device.manager.MigrationDeviceManager;
import com.moredian.magicube.device.service.MigrationDeviceService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @Description: 一键上钉
 * @author: wbf
 * @date: 2024/8/12 下午3:12
 */
@SI
@Slf4j
public class MigrationDeviceServiceImpl implements MigrationDeviceService {

    @Resource
    private MigrationDeviceManager migrationDeviceManager;


    @Override
    public ServiceResponse<MigrationDeviceRes> getList(Long orgId) {
        BizAssert.isTrue(orgId != null, "orgId不能为空");
        ServiceResponse<MigrationDeviceRes> response = ServiceResponse.createSuccessResponse();
        MigrationDeviceRes res = migrationDeviceManager.getList(orgId);
        response.setData(res);
        return response;
    }

    @Override
    public ServiceResponse<Boolean> reqBindOrgUrl(Long orgId, Long deviceId) {
        BizAssert.isTrue(orgId != null, "orgId不能为空");
        BizAssert.isTrue(deviceId != null, "deviceId不能为空");
        ServiceResponse<Boolean> response = ServiceResponse.createSuccessResponse();
        boolean res = migrationDeviceManager.reqBindOrgUrl(orgId, deviceId);
        response.setData(res);
        return response;
    }

    @Override
    public ServiceResponse<MigrationDeviceBindOrgUrlRes> getBindOrgUrl(Long orgId, Long deviceId) {
        BizAssert.isTrue(orgId != null, "orgId不能为空");
        BizAssert.isTrue(deviceId != null, "deviceId不能为空");
        ServiceResponse<MigrationDeviceBindOrgUrlRes> response = ServiceResponse.createSuccessResponse();
        MigrationDeviceBindOrgUrlRes res = migrationDeviceManager.getBindOrgUrl(orgId, deviceId);
        response.setData(res);
        return response;
    }

    @Override
    public ServiceResponse<Boolean> bindOrgUrlSync(String deviceSn, String bindOrgUrl) {
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn不能为空");
        BizAssert.isTrue(StringUtil.isNotBlank(bindOrgUrl), "bindOrgUrl不能为空");
        ServiceResponse<Boolean> response = ServiceResponse.createSuccessResponse();
        migrationDeviceManager.bindOrgUrlSync(deviceSn, bindOrgUrl);
        response.setData(true);
        return response;
    }

    @Override
    public ServiceResponse<MigrationDeviceOrgRes> getOrgIsEqual(String deviceSn, String corpId) {
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn不能为空");
        BizAssert.isTrue(StringUtil.isNotBlank(corpId), "corpId不能为空");
        ServiceResponse<MigrationDeviceOrgRes> response = ServiceResponse.createSuccessResponse();
        MigrationDeviceOrgRes res = migrationDeviceManager.getOrgIsEqual(deviceSn, corpId);
        response.setData(res);
        return response;
    }

    @Override
    public ServiceResponse<MigrationDeviceStatusRes> getMigrationDeviceStatus(Long orgId, Long deviceId) {
        BizAssert.isTrue(orgId != null, "orgId不能为空");
        BizAssert.isTrue(deviceId != null, "deviceId不能为空");
        ServiceResponse<MigrationDeviceStatusRes> response = ServiceResponse.createSuccessResponse();
        MigrationDeviceStatusRes res = migrationDeviceManager.getMigrationDeviceStatus(orgId, deviceId);
        response.setData(res);
        return response;
    }

    @Override
    public ServiceResponse<Boolean> migrationDeviceStatusSync(Long orgId, String deviceSn, Boolean isSuccess) {
        BizAssert.isTrue(orgId != null, "orgId不能为空");
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn不能为空");
        BizAssert.isTrue(isSuccess != null, "isSuccess不能为空");
        ServiceResponse<Boolean> response = ServiceResponse.createSuccessResponse();
        migrationDeviceManager.migrationDeviceStatusSync(orgId, deviceSn, isSuccess);
        response.setData(true);
        return response;
    }

    @Override
    public ServiceResponse<MigrationDeviceOrgCountRes> getMigrationDeviceCount(Long orgId) {
        BizAssert.isTrue(orgId != null, "orgId不能为空");
        ServiceResponse<MigrationDeviceOrgCountRes> response = ServiceResponse.createSuccessResponse();
        MigrationDeviceOrgCountRes res = migrationDeviceManager.getMigrationDeviceCount(orgId);
        response.setData(res);
        return response;
    }
}
