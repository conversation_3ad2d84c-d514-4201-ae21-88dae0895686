package com.moredian.magicube.device.config;

/**
 * <AUTHOR>
 */
public class RedisKeys {

    /**
     * 分布式锁过期时间，单位：秒
     */
    public static final Long RELEASE_TIME = 2 * 60L;

    /**
     * 有关的锁和超时时间
     */
    public static final Long LOCK_TIMEOUT_MSECS = 10 * 60 * 1000L;

    /**
     * 权限组变更消息分布式锁key
     * <li>%s-机构Id</li>
     * <li>%s-组Id</li>
     */
    public static final String GROUP_CHANGE_MSG = "group_change_msg:%s:%s";

    /**
     * 批量信息设备白名单分布式锁key
     * <li>%s-机构Id</li>
     * <li>%s-组Id</li>
     */
    public static final String BATCH_INSERT_WHITE_LIST_DEVICE = "batch_insert_white_list_device";

    public static final String ACTIVATE_QR_CODE_ACCOUNT =  "activate_qr_code_account:%s";

    /**
     * 设备上报APK版本信息
     *<li>%s-设备sn</li>
     */
    public static final String DEVICE_REPORT_APK_VERSION = "magicube_device_report_apk_version_sn_%s";

    /**
     * 设备上报ROM版本信息
     *<li>%s-设备sn</li>
     */
    public static final String DEVICE_REPORT_ROM_VERSION = "magicube_device_report_rom_version_sn_%s";

    /**
     * 设备上报项目信息
     */
    public static final String DEVICE_REPORT_PROJECT_INFO = "magicube_device_report_project_info_sn_%s";

    /**
     * 设备迁移中
     */
    public static final String MIGRATION_DEVICE_RUNNABLE =  "migration_device_runnable:%s";


    /**
     * 设备进行卡片全量同步的分布式锁，锁设备
     */
    public static final String CARD_FULL_SYNC_DEVICE_LOCK= "card_full_sync_device_lock:%s";

    /**
     * 设备xml配置版本号缓存
     */
    public static final String VERSION_KEY_PRE = "DEVICE_CONFIG_VERSION_%s";

    /**
     * 设备组变更，机构锁
     */
    public static final String DEVICE_COMPOSITE_CHANGE_ORG = "device_composite_change_org:%s";

    public static String getKey(String keyFormat, Object... params) {
        return String.format(keyFormat, params);
    }
}
