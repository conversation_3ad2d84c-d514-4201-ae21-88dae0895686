<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceFaceModelMapper" >

	<resultMap id="deviceGroupResultMap" type="com.moredian.magicube.device.dao.entity.DeviceFaceModel">
		<result column="device_face_model_id" property="deviceFaceModelId" />
		<result column="org_id" property="orgId" />
		<result column="device_id" property="deviceId" />
		<result column="face_model" property="faceModel" />
		<result column="model_type" property="modelType" />
		<result column="gmt_create" property="gmtCreate" />
		<result column="gmt_modify" property="gmtModify" />
	</resultMap>

	<sql id="sql_select">
		device_face_model_id,
		org_id,
		device_id,
		face_model,
	  model_type,
		gmt_create,
		gmt_modify
	</sql>

    <insert id="insertOrUpdate">
		insert into hive_device_face_model(
			device_face_model_id,
			org_id,
			device_id,
			face_model,
		  model_type,
			gmt_create,
			gmt_modify
		)
		values (
		 	#{deviceFaceModelId},
			#{orgId},
			#{deviceId},
			#{faceModel},
		  #{modelType},
			now(6),
			now(6)
		) ON DUPLICATE KEY UPDATE gmt_modify = now(3), face_model=#{faceModel}
	</insert>

	<delete id="deleteByDevice">
	    delete from hive_device_face_model where org_id = #{orgId} and device_id = #{deviceId}
	</delete>

    <select id="selectByOrgId" resultType="java.lang.Long">
		select device_id from hive_device_face_model where org_id = #{orgId}
	</select>

</mapper>