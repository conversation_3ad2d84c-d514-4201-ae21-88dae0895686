package com.moredian.magicube.device.subscriber;

import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.device.pipeline.api.DevicePipelineStateService;
import com.moredian.device.pipeline.dto.request.UpdateDeviceSceneTypeReq;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.common.model.msg.device.DeviceTransferSceneTypeTriggerMsg;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dao.mapper.DeviceApkVersionMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.helper.SceneTypeHelper;
import com.moredian.magicube.device.helper.SpaceHelper;
import com.moredian.magicube.device.manager.ActivityManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import com.moredian.magicube.device.model.WhiteDeviceInfo;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.helper.DdBizManageHelper;
import com.moredian.space.service.SpaceTreeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class DeviceTranferSceneMsgSubscriber {

    @Resource
    private DeviceService deviceService;

    @Resource
    private WhiteDeviceManager whiteDeviceManager;

    @Resource
    private DdBizManageHelper ddBizManageHelper;

    @Resource
    private ActivityManager activityManager;

    @SI
    private DevicePipelineStateService devicePipelineStateService;

    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private SpaceTreeService spaceTreeService;

    @Resource
    private SpaceHelper spaceHelper;

    @Resource
    private SceneTypeHelper sceneTypeHelper;

    @Resource
    private DeviceApkVersionMapper deviceApkVersionMapper;

    @Resource
    private DeviceManager deviceManager;

    /**
     * sceneType更新触发消息
     *
     * @param sceneTypeTriggerMsg msg
     */
    @Subscribe
    public void subDeviceActiveMsg(DeviceTransferSceneTypeTriggerMsg sceneTypeTriggerMsg) {
        log.info("分销迁移融元 收到sceneType更新触发消息  msg:{}", sceneTypeTriggerMsg);
        List<String> deviceSnList = sceneTypeTriggerMsg.getDeviceSn();
        Integer sceneType = sceneTypeTriggerMsg.getSceneTypeEnum().getValue();
        Long memberId = sceneTypeTriggerMsg.getMemberId();
        deviceSnList.forEach(deviceSn -> {
            try {
                DeviceInfoDTO device = deviceService.getByDeviceSn(deviceSn).pickDataThrowException();
                InventoryDevice deviceWhite = whiteDeviceManager.getByDeviceSn(deviceSn);
                List<WhiteDeviceInfo> whiteDeviceList = ddBizManageHelper.getWhiteDeviceList(Collections.singletonList(deviceSn));
                if (CollectionUtils.isNotEmpty(whiteDeviceList)) {
                    if (deviceWhite != null){
                        //删除 以塔上为准
                        whiteDeviceManager.delete(deviceSn);
                    }
                    WhiteDeviceInfo whiteDeviceInfo = whiteDeviceList.get(0);
                    whiteDeviceManager.insert(new InventoryDevice()
                            .setSerialNumber(deviceSn)
                            .setMacAddress(whiteDeviceInfo.getMacAddress())
                            .setMacAddress2(whiteDeviceInfo.getMacAddress2())
                            .setPrivateKey(whiteDeviceInfo.getSecretKey())
                            .setBatchFlag(whiteDeviceInfo.getPatchFlag())
                            .setOrgId(device.getOrgId())
                            .setActivityStatus(whiteDeviceInfo.getActivityStatus())
                            .setThirdDeviceId(whiteDeviceInfo.getThirdDeviceId())
                            .setDeviceType(device.getDeviceType())
                            .setDeviceSource(whiteDeviceInfo.getDeviceSource())
                            .setActivationCode(whiteDeviceInfo.getActivationCode())
                            .setIsOpenPlat(whiteDeviceInfo.getIsOpenPlat())
                            .setDeviceActivateOaBase(2)
                            .setDeviceActivateSdkBase(2));
                }
                UpdateDeviceSceneTypeReq req = new UpdateDeviceSceneTypeReq();
                req.setDeviceSn(deviceSn);
                req.setDeviceId(device.getDeviceId());
                req.setOrgId(device.getOrgId());
                req.setSceneType(sceneType);
                devicePipelineStateService.updateCloudSceneType(req)
                        .pickDataThrowException();
                if (SceneTypeEnums.MEETING_ROOM_MODEL.getValue().equals(sceneType)){
                    // 根据选择场景更新设备关联的应用
                    List<String> appCode = sceneTypeHelper.getAppCodeBySceneType(sceneType);
                    UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
                    updateDeviceDTO.setDeviceId(device.getDeviceId());
                    updateDeviceDTO.setOrgId(device.getOrgId());
                    updateDeviceDTO.setAppCode(String.join(",", appCode));
                    updateDeviceDTO.setAppCodeList(String.join(",", appCode));
                    deviceManager.update(updateDeviceDTO);
                }
            } catch (Exception e) {
                log.error("sceneType更新触发异常,deviceSn={},e={}", deviceSn, e.getMessage(), e);
            }
        });
    }
}
