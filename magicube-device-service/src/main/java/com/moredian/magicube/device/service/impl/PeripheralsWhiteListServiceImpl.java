package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.PeripheralsWhiteList;
import com.moredian.magicube.device.dto.peripherals.InsertPeripheralsWhiteListDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListDTO;
import com.moredian.magicube.device.manager.PeripheralsWhiteListManager;
import com.moredian.magicube.device.service.PeripheralsWhiteListService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@SI
public class PeripheralsWhiteListServiceImpl implements PeripheralsWhiteListService {

    @Autowired
    private PeripheralsWhiteListManager peripheralsWhiteListManager;

    @Override
    public ServiceResponse<Boolean> batchInsert(List<InsertPeripheralsWhiteListDTO> list) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(list)) {
            serviceResponse.setData(Boolean.FALSE);
            return serviceResponse;
        }
        List<PeripheralsWhiteList> peripheralsWhiteListList = new ArrayList<>();
        for (InsertPeripheralsWhiteListDTO insertPeripheralsWhiteListDto : list) {
            PeripheralsWhiteList peripheralsWhiteList = new PeripheralsWhiteList();
            BeanUtils.copyProperties(insertPeripheralsWhiteListDto, peripheralsWhiteList);
            peripheralsWhiteListList.add(peripheralsWhiteList);
        }
        peripheralsWhiteListManager.batchInsert(peripheralsWhiteListList);
        serviceResponse.setData(Boolean.TRUE);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<PeripheralsWhiteListDTO> getBySnAndType(String peripheralsSn, Integer peripheralsType) {
        ServiceResponse<PeripheralsWhiteListDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        PeripheralsWhiteList peripheralsWhiteList = peripheralsWhiteListManager.getBySnAndType(peripheralsSn, peripheralsType);
        if (peripheralsWhiteList != null) {
            PeripheralsWhiteListDTO peripheralsWhiteListDto = new PeripheralsWhiteListDTO();
            BeanUtils.copyProperties(peripheralsWhiteList, peripheralsWhiteListDto);
            serviceResponse.setData(peripheralsWhiteListDto);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<PeripheralsWhiteListDTO>> getByPeripheralsSns(List<String> peripheralsSnList) {
        ServiceResponse<List<PeripheralsWhiteListDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<PeripheralsWhiteList> list = peripheralsWhiteListManager.getBySnList(peripheralsSnList);
        if (CollectionUtils.isNotEmpty(list)) {
            List<PeripheralsWhiteListDTO> peripheralsWhiteListDtoList = new ArrayList<>();
            for (PeripheralsWhiteList peripheralsWhiteList : list) {
                PeripheralsWhiteListDTO peripheralsWhiteListDto = new PeripheralsWhiteListDTO();
                BeanUtils.copyProperties(peripheralsWhiteList, peripheralsWhiteListDto);
                peripheralsWhiteListDtoList.add(peripheralsWhiteListDto);
            }
            serviceResponse.setData(peripheralsWhiteListDtoList);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Integer> updateStatusById(Long peripheralsWhiteListId, Integer status) {
        ServiceResponse<Integer> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(peripheralsWhiteListManager.updateStatusById(peripheralsWhiteListId, status));
        return serviceResponse;
    }
}
