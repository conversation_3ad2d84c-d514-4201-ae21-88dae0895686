<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.CameraDeviceMapper">

  <resultMap type="com.moredian.magicube.device.dao.entity.dahua.CameraDeviceInfo" id="baseMap">
    <result property="cameraDeviceId" column="id"/>
    <result property="orgId" column="org_id"/>
    <result property="deviceSn" column="device_sn"/>
    <result property="deviceIp" column="device_ip"/>
    <result property="port" column="port"/>
    <result property="userName" column="user_name"/>
    <result property="password" column="password"/>
    <result property="registerType" column="register_type"/>
    <result property="localDeviceIp" column="local_device_ip"/>
    <result property="localPort" column="local_port"/>
    <result property="gmtCreate" column="gmt_create"/>
    <result property="gmtModify" column="gmt_modify"/>
  </resultMap>

  <sql id="sql_base_column">
        id,
        org_id,
        device_sn,
        device_ip,
        port,
        user_name,
        password,
        register_type,
        local_device_ip,
        local_port,
        gmt_create,
        gmt_modify
    </sql>

  <sql id="sql_table">
        hive_device_camera_info
    </sql>

  <update id="updateByDeviceSn">
    UPDATE
    <include refid="sql_table"/>
    <set>
        <if test="dto.deviceIp != null and dto.deviceIp.length() > 0">
          device_ip = #{dto.deviceIp},
        </if>
        <if test="dto.port != null">
          port = #{dto.port},
        </if>
        gmt_modify = NOW()
    </set>
    WHERE device_sn = #{deviceSn}
  </update>

  <select id="list" resultMap="baseMap">
    select
    <include refid="sql_base_column"/>
    from
    <include refid="sql_table"/>
  </select>

  <select id="getByIpAndPort" resultMap="baseMap">
    select
    <include refid="sql_base_column"/>
    from
    <include refid="sql_table"/>
    where device_ip = #{ip}
    and port = #{port}
  </select>

  <select id="listByCondition" resultMap="baseMap" parameterType="com.moredian.magicube.device.dto.dahua.QueryCameraDTO">
    SELECT
    <include refid="sql_base_column"/>
    FROM
    <include refid="sql_table"/>
    <where>
        <if test="dto.orgId != null">
          AND org_id = #{dto.orgId}
        </if>
        <if test="dto.deviceIds != null and dto.deviceIds.size() > 0">
          AND id in
          <foreach collection="dto.deviceIds" index="index" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
          </foreach>
        </if>
        <if test="dto.deviceSns != null and dto.deviceSns.size() > 0">
          AND device_sn in
          <foreach collection="dto.deviceSns" index="index" item="deviceSn" open="(" separator="," close=")">
            #{deviceSn}
          </foreach>
        </if>
    </where>
    ORDER BY gmt_create DESC
  </select>

</mapper>