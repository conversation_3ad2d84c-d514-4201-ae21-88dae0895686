package com.moredian.magicube.device.xxljob;

import com.moredian.bee.job.annotation.BeeXxlJob;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.mapping.IOrgIotClientMappingService;
import com.moredian.magicube.device.constant.DeviceIotSplitConstants;
import com.moredian.magicube.device.manager.DeviceManager;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * 魔链设备同步魔蓝任务
 */
@Component
@Slf4j
public class DeviceSyncJob {

    @SI
    private IOrgIotClientMappingService orgIotClientMappingService;

    @Autowired
    private DeviceManager deviceManager;


    @BeeXxlJob(value = "deviceSync", name = "魔链设备定时同步魔蓝")
    public ReturnT<String> deviceSync(String param) {
        List<Long> orgIds;
        ReturnT<String> success = ReturnT.SUCCESS;
        if (!StringUtils.isEmpty(param)) {
            // 手动输入orgId
            orgIds = Arrays.stream(param.split(DeviceIotSplitConstants.COMMA)).map(Long::parseLong).collect(
                Collectors.toList());
        } else {
            orgIds = orgIotClientMappingService.getAuthorizedOrgIdList().pickDataThrowException();
        }
        if (ObjectUtils.isEmpty(orgIds)) {
            log.info("已绑定魔链账号的orgId列表为空");
            success.setMsg("已绑定魔链账号的orgId列表为空");
            return success;
        }
        log.info("已绑定魔链账号的orgId列表:{}", StringUtils.join(orgIds, DeviceIotSplitConstants.COMMA));
        try {
            orgIds.forEach(orgId -> deviceManager.syncIotDeviceByOrgId(orgId));
        }catch (Exception e){
            ReturnT<String> fail = ReturnT.FAIL;
            fail.setMsg(e.getMessage());
            return fail;
        }
        return success;
    }
}
