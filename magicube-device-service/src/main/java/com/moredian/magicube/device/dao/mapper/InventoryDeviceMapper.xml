<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.InventoryDeviceMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.InventoryDevice">
    <id column="serial_number" property="serialNumber"/>
    <result column="mac_address" property="macAddress"/>
    <result column="mac_address_2" property="macAddress2"/>
    <result column="device_source" property="deviceSource"/>
    <result column="private_key" property="privateKey"/>
    <result column="batch_flag" property="batchFlag"/>
    <result column="org_id" property="orgId"/>
    <result column="activity_status" property="activityStatus"/>
    <result column="third_device_id" property="thirdDeviceId"/>
    <result column="device_type" property="deviceType"/>
    <result column="activation_code" property="activationCode"/>
    <result column="is_open_plat" property="isOpenPlat"/>
    <result column="is_edu_device" property="isEduDevice"/>
    <result column="device_activate_oa_base" property="deviceActivateOaBase"/>
    <result column="device_activate_sdk_base" property="deviceActivateSdkBase"/>
    <result column="created_at" property="createdAt"/>
    <result column="updated_at" property="updatedAt"/>
  </resultMap>

  <sql id="sql_table">
        da_device_inventory
    </sql>

  <!-- 通用查询结果列 -->
  <sql id="sql_columns">
        serial_number,
        mac_address,
        mac_address_2,
        device_source,
        private_key,
        batch_flag,
        org_id,
        activity_status,
        third_device_id,
        device_type,
        activation_code,
        is_open_plat,
        is_edu_device,
        device_activate_oa_base,
        device_activate_sdk_base,
        created_at,
        updated_at
    </sql>

  <sql id="sql_values">
        #{serialNumber},
        #{macAddress},
        #{macAddress2},
        #{deviceSource},
        #{privateKey},
        #{batchFlag},
        #{orgId},
        #{activityStatus},
        #{thirdDeviceId},
        #{deviceType},
        #{activationCode},
        #{isOpenPlat},
        #{isEduDevice},
        #{deviceActivateOaBase},
        #{deviceActivateSdkBase},
        now(3),
        now(3)
    </sql>

  <sql id="sql_where">
    serial_number is not null
    <if test="serialNumber != null">
      and serial_number = #{serialNumber}
    </if>
    <if test="macAddress != null">
      and mac_address = #{macAddress}
    </if>
    <if test="macAddress2 != null">
      and mac_address_2 = #{macAddress2}
    </if>
    <if test="deviceSource != null">
      and device_source=#{deviceSource}
    </if>
    <if test="deviceType != null">
      and device_type = #{deviceType}
    </if>
    <if test="privateKey != null">
      and private_key = #{privateKey}
    </if>
    <if test="batchFlag != null">
      and batch_flag = #{batchFlag}
    </if>
    <if test="orgId != null">
      and org_id = #{orgId}
    </if>
    <if test="thirdDeviceId != null">
      and third_device_id= #{thirdDeviceId}
    </if>
    <if test="activityStatus != null">
      and activity_status = #{activityStatus}
    </if>
    <if test="activationCode != null">
      and activation_code = #{activationCode}
    </if>
    <if test="isOpenPlat != null">
      and is_open_plat = #{isOpenPlat}
    </if>
    <if test="serialNumbers != null and serialNumbers.size() > 0">
      and serial_number in
      <foreach collection="serialNumbers" index="index" item="deviceSn" open="(" separator="," close=")">
        #{deviceSn}
      </foreach>
    </if>
  </sql>

  <sql id="sql_update">
    <set>
      <if test="serialNumber != null">
        serial_number = #{serialNumber},
      </if>
      <if test="macAddress != null">
        mac_address = #{macAddress},
      </if>
      <if test="macAddress2 != null">
        mac_address_2 = #{macAddress2},
      </if>
      <if test="deviceSource != null">
        device_source =#{deviceSource},
      </if>
      <if test="deviceType != null">
        device_type = #{deviceType},
      </if>
      <if test="privateKey != null">
        private_key = #{privateKey},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="thirdDeviceId != null">
        third_device_id = #{thirdDeviceId},
      </if>
      <if test="activityStatus != null">
        activity_status = #{activityStatus},
      </if>
      <if test="activationCode != null">
        activation_code = #{activationCode},
      </if>
      <if test="isOpenPlat != null">
        is_open_plat = #{isOpenPlat},
      </if>
      <if test="deviceActivateOaBase != null">
        device_activate_oa_base = #{deviceActivateOaBase},
      </if>
      <if test="deviceActivateSdkBase != null">
        device_activate_sdk_base = #{deviceActivateSdkBase},
      </if>
      updated_at = now(3)
    </set>
  </sql>

  <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.InventoryDevice">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>

  <insert id="batchInsert">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    <foreach collection="list" item="item" index="index" open="" separator="," close="">
      (
      #{item.serialNumber},
      #{item.macAddress},
      #{item.macAddress2},
      #{item.deviceSource},
      #{item.privateKey},
      #{item.batchFlag},
      #{item.orgId},
      #{item.activityStatus},
      #{item.thirdDeviceId},
      #{item.deviceType},
      #{item.activationCode},
      #{item.isOpenPlat},
      #{item.isEduDevice},
      #{item.deviceActivateOaBase},
      #{item.deviceActivateSdkBase},
      now(3),
      now(3)
      )
    </foreach>
  </insert>

  <select id="getByDeviceSn" resultMap="BaseResultMap">
    SELECT
    <include refid="sql_columns"/>
    FROM
    <include refid="sql_table"/>
    WHERE serial_number = #{deviceSn}
  </select>

  <select id="getByOrgIdAndThirdDeviceId" parameterType="map" resultMap="BaseResultMap">
    SELECT
    <include refid="sql_columns"/>
    FROM
    <include refid="sql_table"/>
    WHERE org_id = #{orgId}
    AND third_device_id = #{thirdDeviceId}
  </select>

  <select id="getByDeviceSnAndActivationCode" parameterType="string" resultMap="BaseResultMap">
    SELECT
    <include refid="sql_columns"/>
    FROM
    <include refid="sql_table"/>
    WHERE serial_number = #{deviceSn}
    AND activation_code = #{activationCode}
  </select>

  <select id="listByCondition"
    parameterType="com.moredian.magicube.device.condition.white.QueryWhiteDeviceCondition"
    resultMap="BaseResultMap">
    SELECT
    <include refid="sql_columns"/>
    FROM
    <include refid="sql_table"/>
    WHERE
    <include refid="sql_where"/>
  </select>

  <select id="listByDeviceSns" resultMap="BaseResultMap">
    SELECT
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where serial_number in
    <foreach collection="deviceSns" index="index" item="deviceSn" open="(" separator="," close=")">
      #{deviceSn}
    </foreach>
  </select>

  <delete id="delete" parameterType="string">
	    delete from da_device_inventory where serial_number = #{deviceSn}
	</delete>

  <update id="batchUpdate" parameterType="java.util.List">
    update
    <include refid="sql_table"/>
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.orgId != null and item.orgId != ''">
            when serial_number = #{item.serialNumber} then #{item.orgId}
          </if>
        </foreach>
      </trim>
      <trim prefix="activation_code =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.activationCode != null and item.activationCode != ''">
            when serial_number = #{item.serialNumber} then #{item.activationCode}
          </if>
        </foreach>
      </trim>
      <trim prefix="device_activate_oa_base =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.deviceActivateOaBase != null">
            when serial_number = #{item.serialNumber} then #{item.deviceActivateOaBase}
          </if>
        </foreach>
      </trim>
      <trim prefix="serial_number =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.newSerialNumber != null and item.newSerialNumber != ''">
            when serial_number = #{item.serialNumber} then #{item.newSerialNumber}
          </if>
        </foreach>
      </trim>
      <trim prefix="device_activate_sdk_base =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.deviceActivateSdkBase != null">
            when serial_number = #{item.serialNumber} then #{item.deviceActivateSdkBase}
          </if>
        </foreach>
      </trim>
    </trim>
    where
    <foreach collection="list" separator="or" item="item" index="index">
      serial_number=#{item.serialNumber}
    </foreach>
  </update>

  <update id="update" parameterType="com.moredian.magicube.device.dao.entity.InventoryDevice">
    update
    <include refid="sql_table"/>
    <include refid="sql_update"/>
    where serial_number=#{serialNumber}
  </update>

  <update id="unbindDevice" parameterType="com.moredian.magicube.device.dao.entity.InventoryDevice">
    update
    <include refid="sql_table"/>
    set
    <if test="activityStatus != null">
      activity_status = #{activityStatus},
    </if>
    org_id = null
    where serial_number=#{serialNumber}
  </update>
</mapper>
