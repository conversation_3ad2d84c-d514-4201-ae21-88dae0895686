package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */

@Mapper
public interface DeviceConfigMapper {

    /**
     * 新增设备配置
     *
     * @param deviceConfig 设备配置信息
     * @return
     */
    int insertOrUpdate(DeviceConfig deviceConfig);

    /**
     * 根据设备sn获取设备配置信息
     *
     * @param deviceSn 设备Sn
     * @return
     */
    DeviceConfig getByDeviceSn(@Param("deviceSn") String deviceSn);

    /**
     * 根据设备SN查询设备配置签名
     *
     * @param deviceSn
     * @return
     */
    String getConfigSignatureByDeviceSn(@Param("deviceSn") String deviceSn);

    /**
     * 删除设备配置信息
     *
     * @param deviceSn 设备sn
     * @return
     */
    int deleteBySn(@Param("deviceSn") String deviceSn);
}