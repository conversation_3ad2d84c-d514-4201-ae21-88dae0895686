package com.moredian.magicube.device.xxljob;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.job.annotation.BeeXxlJob;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.deliver.api.run.AuthorizeDeviceDataService;
import com.moredian.deliver.dto.AuthorizeDeviceAndOrgInfoDTO;
import com.moredian.deliver.dto.QueryWhiteDeviceDTO;
import com.moredian.deliver.dto.WhiteDeviceDTO;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.white.UpdateWhiteDeviceDTO;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.WhiteDeviceService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @description：脚本获取管控对应的设备
 * @date ：2024/10/23 17:48
 */
@Slf4j
@Component
public class DeviceAuthBaseFreshJob {

    @SI
    private AuthorizeDeviceDataService authorizeDeviceDataService;
    @Resource
    private WhiteDeviceService whiteDeviceService;

    @SI
    private com.moredian.deliver.api.manage.WhiteDeviceService deliverWhiteDeviceService;

    @Resource
    private DeviceService deviceService;

    @SI
    private OrgService orgService;


    @BeeXxlJob(value = "deviceAuthFresh", name = "根据授权机构base刷新设备白单sdkBase")
    public ReturnT<String> deviceAuthFresh(String param){
        log.info("-------根据授权机构base刷新设备白单sdkBase定时任务开始---------");
        List<AuthorizeDeviceAndOrgInfoDTO> authDeviceAndOrgInfo = authorizeDeviceDataService.listAllAuthDevice()
            .pickDataThrowException();
        if (!ObjectUtils.isEmpty(authDeviceAndOrgInfo)) {
            List<String> deviceSns = authDeviceAndOrgInfo.stream()
                .map(AuthorizeDeviceAndOrgInfoDTO::getDeviceSn).distinct().collect(Collectors.toList());
            log.info("同步授权管控设备sn，{}", deviceSns);
            // 行业设备白单数据
            List<com.moredian.magicube.device.dto.white.WhiteDeviceDTO> whiteDeviceDTOS = whiteDeviceService.listByDeviceSns(
                deviceSns).pickDataThrowException();

            Map<String, Integer> whiteMap = Maps.newHashMap();
            if(ObjectUtil.isNotEmpty(whiteDeviceDTOS)){
                whiteMap = whiteDeviceDTOS.stream().filter(e-> ObjectUtil.isNotEmpty(e.getDeviceType())).collect(
                    Collectors.toMap(com.moredian.magicube.device.dto.white.WhiteDeviceDTO::getSerialNumber,
                        com.moredian.magicube.device.dto.white.WhiteDeviceDTO::getDeviceType));
            }

            List<UpdateWhiteDeviceDTO> list = Lists.newArrayList();
            for (AuthorizeDeviceAndOrgInfoDTO item : authDeviceAndOrgInfo) {
                UpdateWhiteDeviceDTO dto = new UpdateWhiteDeviceDTO();
                dto.setSerialNumber(item.getDeviceSn());
                // 判断是否是门锁、访客机
                Integer deviceType = whiteMap.get(item.getDeviceSn());
                if(DeviceType.isNoDingSdkActivate(deviceType)) {
                    dto.setDeviceActivateOaBase(2);
                    dto.setDeviceActivateSdkBase(1);
                }else {
                    Integer oaBase = item.getOaBase() != null && item.getOaBase() == 0 ? 1 : 2;
                    dto.setDeviceActivateOaBase(oaBase);
                    dto.setDeviceActivateSdkBase(oaBase);
                }
                list.add(dto);
            }
            whiteDeviceService.batchUpdate(list);
            log.info("--------根据授权机构base刷新设备白单sdkBase任务结束-----");
        }
        return ReturnT.SUCCESS;
    }



    @BeeXxlJob(value = "deviceAuthWhiteFresh", name = "托管白单刷新设备白单sdkBase")
    public ReturnT<String> deviceAuthWhiteFresh(String param){
        QueryWhiteDeviceDTO dto = new QueryWhiteDeviceDTO();
        dto.setPageNo(1);
        dto.setPageSize(100000000);

        // 查到管控的托管白单数据
        List<WhiteDeviceDTO> deliverWhiteDeviceList = deliverWhiteDeviceService.listPageByCondition(
            dto).pickDataThrowException().getData();


        if(ObjectUtil.isNotEmpty(deliverWhiteDeviceList)) {
            List<String> deviceSns = deliverWhiteDeviceList.stream().map(WhiteDeviceDTO::getDeviceSn)
                .distinct().collect(Collectors.toList());
            List<DeviceInfoDTO> devices = deviceService.listByDeviceSns(deviceSns)
                .pickDataThrowException();
            // 行业设备白单数据
            List<com.moredian.magicube.device.dto.white.WhiteDeviceDTO> whiteDeviceDTOS = whiteDeviceService.listByDeviceSns(
                deviceSns).pickDataThrowException();

            Map<Long, Integer> orgSourceMap = Maps.newHashMap();
            Map<String, Long> deviceOrgMap = Maps.newHashMap();
            Map<String, Integer> whiteMap = Maps.newHashMap();
            if(ObjectUtil.isNotEmpty(whiteDeviceDTOS)){
                whiteMap = whiteDeviceDTOS.stream().filter(e-> ObjectUtil.isNotEmpty(e.getDeviceType())).collect(
                    Collectors.toMap(com.moredian.magicube.device.dto.white.WhiteDeviceDTO::getSerialNumber,
                        com.moredian.magicube.device.dto.white.WhiteDeviceDTO::getDeviceType));
            }

            // 查询设备激活的机构
            if (!ObjectUtil.isEmpty(devices)) {
                deviceOrgMap = devices.stream()
                    .collect(Collectors.toMap(DeviceInfoDTO::getDeviceSn, DeviceInfoDTO::getOrgId));

                List<Long> orgIds = devices.stream().map(DeviceInfoDTO::getOrgId).distinct()
                    .collect(Collectors.toList());
                List<OrgInfo> orgInfos = orgService.findOrgInfoList(orgIds, Lists.newArrayList(1))
                    .pickDataThrowException();
                if (ObjectUtil.isNotEmpty(orgInfos)) {
                    // (0:未知,1:魔蓝,2:钉钉,3:政务钉钉)
                    orgSourceMap = orgInfos.stream()
                        .collect(Collectors.toMap(OrgInfo::getOrgId, OrgInfo::getSource));
                }
            }

            List<UpdateWhiteDeviceDTO> updateDto = new ArrayList<>();
            // 更新白单数据
            for (WhiteDeviceDTO item : deliverWhiteDeviceList) {
                UpdateWhiteDeviceDTO temp = new UpdateWhiteDeviceDTO();
                String deviceSn = item.getDeviceSn();
                temp.setSerialNumber(deviceSn);
                // 先判断是否双屏访客机、门锁，使用钉od，非钉sdk
                Integer deviceType = whiteMap.get(deviceSn);
                if(DeviceType.isNoDingSdkActivate(deviceType)){
                    temp.setDeviceActivateOaBase(2);
                    temp.setDeviceActivateSdkBase(1);
                }else {
                    // 其它设备按照对应激活机构，没有激活的默认是使用非钉
                    if (deviceOrgMap.containsKey(deviceSn)){
                        Integer source = orgSourceMap.get(deviceOrgMap.get(deviceSn));
                        source = Optional.ofNullable(source).orElse(1);
                        if (source == 1 || source == 2){
                            temp.setDeviceActivateOaBase(source);
                            temp.setDeviceActivateSdkBase(source);
                        }else {
                            // 其它机构全部使用非钉
                            temp.setDeviceActivateOaBase(1);
                            temp.setDeviceActivateSdkBase(1);
                        }
                    }else {
                        // 没激活的管控白单设置成非钉sdk
                        temp.setDeviceActivateOaBase(1);
                        temp.setDeviceActivateSdkBase(1);
                    }
                }
                updateDto.add(temp);
            }
            whiteDeviceService.batchUpdate(updateDto);
        }
        return ReturnT.SUCCESS;
    }




}
