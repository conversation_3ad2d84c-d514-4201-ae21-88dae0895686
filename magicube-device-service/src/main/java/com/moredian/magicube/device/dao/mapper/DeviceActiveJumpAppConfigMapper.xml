<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceActiveJumpAppConfigMapper">
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceActiveJumpAppConfig">
        <result column="id" property="id"/>
        <result column="app_code" property="appCode"/>
        <result column="jump_link" property="jumpLink"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">t.id , t.app_code , t.jump_link , t.gmt_create , t.gmt_modify</sql>
    <!-- 查询全部 -->
    <select id="listAll" resultMap="BaseResultMap">SELECT
        <include refid="baseColumns"/> FROM hive_device_active_jump_app_config t
    </select>
    <!-- 根据主键获取单条记录 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="Long">SELECT
        <include refid="baseColumns"/> FROM hive_device_active_jump_app_config t WHERE id = #{id}
    </select>
    <!-- 插入全部字段 -->
    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceActiveJumpAppConfig" keyProperty="id" keyColumn="id" useGeneratedKeys="true">INSERT INTO hive_device_active_jump_app_config
        <trim prefix="(" suffix=")" suffixOverrides=",">id, app_code, jump_link, gmt_create, gmt_modify,</trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">#{id}, #{appCode}, #{jumpLink}, #{gmtCreate}, #{gmtModify},</trim>
    </insert>
    <!-- 更新全部字段 -->
    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceActiveJumpAppConfig">UPDATE hive_device_active_jump_app_config
        <set>app_code=#{appCode}, jump_link=#{jumpLink}, gmt_create=#{gmtCreate}, gmt_modify=#{gmtModify},</set> WHERE id = #{id}
    </update>
    <!-- 更新不为NULL的字段 -->
    <update id="updateSelective" parameterType="com.moredian.magicube.device.dao.entity.DeviceActiveJumpAppConfig">UPDATE hive_device_active_jump_app_config
        <set>
            <if test="appCode != null">app_code=#{appCode},</if>
            <if test="jumpLink != null">jump_link=#{jumpLink},</if>
            <if test="gmtCreate != null">gmt_create=#{gmtCreate},</if>
            <if test="gmtModify != null">gmt_modify=#{gmtModify},</if>
        </set> WHERE id = #{id}
    </update>
    <!-- 根据主键删除记录 -->
    <delete id="delete" parameterType="com.moredian.magicube.device.dao.entity.DeviceActiveJumpAppConfig">delete from hive_device_active_jump_app_config WHERE id = #{id}</delete>

    <select id="getByAppCode" resultMap="BaseResultMap">
        SELECT
        <include refid="baseColumns"/> FROM hive_device_active_jump_app_config t WHERE app_code = #{appCode}
    </select>
</mapper>
