package com.moredian.magicube.device.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 设备激活前上报版本号实体类
 * @Date 2022/11/2
 */
@Getter
@Setter
@ToString
public class BeforeActivationDeviceVersion implements Serializable {

    private static final long serialVersionUID = 6260751902401670795L;

    /**
     * 设备sn(主键)
     */
    private String deviceSn;

    /**
     * apk类型
     */
    private Integer appType;

    /**
     * apk版本号
     */
    private Integer appVersionCode;

    /**
     * rom类型
     */
    private Integer romType;

    /**
     * rom版本号
     */
    private Integer romVersionCode;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
