package com.moredian.magicube.device.jetlinks.rule.arrange;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description：函数节点
 * @date ：2024/08/07 11:16
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class FunctionNode extends Node{

    public static final String TYPE = "function";

    private String func;

    public FunctionNode() {
        super(TYPE);
    }

    /**
     * 无人关闭设备开关节点模板
     */
    public static final String NO_HUMAN_POWER_OFF = "handler.onMessage(function (msg) {if (msg.data.#{propertyId} == '1' || msg.data.k1_power == '1' || msg.data.k2_power == '1' || msg.data.k3_power == '1') {return{ \"messageType\": \"WRITE_PROPERTY\",\"deviceId\": '#{deviceSn}',\"properties\": {\"#{propertyId}\": \"#{propertyValue}\"}}}})";

    /**
     * 有人开启设备模板
     */
    public static final String HUMAN_ON_DEVICE = "handler.onMessage(function (msg) {\n" +
            "    let data = msg.data;\n" +
            "    if (data.#{propertyId} == '0' || data.k1_power == '0' || data.k2_power == '0' || data.k3_power == '0') {\n" +
            "        return {\n" +
            "            \"messageType\": \"WRITE_PROPERTY\", \n" +
            "            \"deviceId\": \"#{deviceSn}\", \n" +
            "            \"properties\": {\n" +
            "                \"#{propertyId}\": \"#{propertyValue}\",\n" +
            "                \"MD_MESSAGE_SOURCE\": \"HUMAN_ON\",\n" +
            "                \"ruleId\": #{ruleArrangeId}\n" +
            "            }\n" +
            "        }\n" +
            "    }\n" +
            "})";

    /**
     * 雷达持续无人时间函数判断
     */
    public static final String NO_HUMAN_TIME = "handler.onMessage(function (msg) {\n"
        + "    let minTime = Number.MAX_VALUE;\n"
        + "    let humFlag = true;\n"
        + "\n"
        + "    for(let i = 0; i<msg.data.devices.length; i++){\n"
        + "        let deviceData = msg.data.devices[i];\n"
        + "        if (deviceData.motionStatus == 1){\n"
        + "            humFlag = false;\n"
        + "            break;\n"
        + "        }\n"
        + "        minTime = Math.min(minTime, deviceData.duration); \n"
        + "    }\n"
        + "\n"
        + "    if(humFlag){\n"
        + "        if (minTime / 1000 > #{time}) { \n"
        + "            logger.info('达到无人关闭条件:{}', msg.data);\n"
        + "             return true;\n"
        + "        }\n"
        + "    } \n"
        + "})";


    public static final String RULE_CONFIG = "handler.onMessage(function(msg){\n" +
            "    logger.info('handle data :{}',msg.data.payload);\n" +
            "    let priority = msg.data.payload.data.humanPriority;\n" +
            "    let lock = msg.data.payload.data.lock;\n" +
            "    // 没有开启规则优先规则\n" +
            "    if (!priority) {\n" +
            "        return true;\n" +
            "    }\n" +
            "    // 是否开启规则优先开关并且规则没有上锁\n" +
            "    if (priority && !lock) {\n" +
            "        return true;\n" +
            "    }\n" +
            "})";

    /**
     * 是否是规则雷达，并推送获取规则配置http接口下游请求数据
     */
    public static final String HUMAN_RADAR_JUDGE = "handler.onMessage(function(msg){\n" +
            "    let deviceIds = [#{radarDeviceIds}];\n" +
            "    logger.info('handle data :{}',msg.data);\n" +
            "    let deviceId = msg.data.payload.deviceId;\n" +
            "    logger.info('handle deviceId :{}', deviceId);\n" +
            "    if(deviceIds.indexOf(deviceId) != -1) {\n" +
            "        // 请求接口看是否规则是否加锁\n" +
            "        return {\n" +
            "            url: \"#{url}\",\n" +
            "            method: \"POST\",\n" +
            "            payload: {\n" +
            "                ruleId: #{ruleArrangeId}\n" +
            "            }\n" +
            "        }\n" +
            "    }\n" +
            "})";

    /**
     * 空间持续无人，释放规则锁
     */
    public static final String RULE_RELEASE_LOCK = "handler.onMessage(function (msg) {\n" +
            "    let minTime = Number.MAX_VALUE;\n" +
            "    let humFlag = true;\n" +
            "    for (let i = 0; i < msg.data.devices.length; i++) {\n" +
            "        let deviceData = msg.data.devices[i];\n" +
            "        if (deviceData.motionStatus == 1) {\n" +
            "            humFlag = false;\n" +
            "            break;\n" +
            "        }\n" +
            "        minTime = Math.min(minTime, deviceData.duration);\n" +
            "    }\n" +
            "    if (humFlag) {\n" +
            "        if (minTime / 1000 / 60 > #{triggerValue}) {\n" +
            "           // 构建发送持续无人多长时间释放锁规则\n" +
            "            return {\n" +
            "                url: \"#{url}\",\n" +
            "                method: \"POST\",\n" +
            "                payload: {\n" +
            "                    ruleId: #{ruleArrangeId},\n" +
            "                    lockType: 0\n" +
            "                }\n" +
            "            }\n" +
            "        }\n" +
            "    }\n" +
            "})";
}
