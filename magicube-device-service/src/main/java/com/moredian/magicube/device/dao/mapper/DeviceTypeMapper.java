package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dto.type.QueryDeviceTypeDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 设备类型表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Mapper
public interface DeviceTypeMapper {

    /**
     * 新增设备类型
     *
     * @param deviceType 设备类型
     * @return
     */
    int insert(DeviceType deviceType);

    /**
     * 根据Id查询设备类型信息
     *
     * @param id 设备类型Id
     * @return
     */
    DeviceType getById(@Param("id") Long id);

    /**
     * 根据Id查询设备类型信息
     *
     * @param deviceType 设备类型
     * @return
     */
    DeviceType getByType(@Param("deviceType") Integer deviceType);

    /**
     * 根据设备类型查询设备类型信息
     *
     * @param deviceType 设备类型
     * @return
     */
    DeviceType getByDeviceType(@Param("deviceType") Integer deviceType);

    /**
     * 编辑设备类型信息
     *
     * @param deviceType 设备类型信息
     * @return
     */
    int update(DeviceType deviceType);

    /**
     * 根据设备类型删除设备类型
     *
     * @param deviceType 设备类型
     * @return
     */
    void deleteByDeviceType(@Param("deviceType") Integer deviceType);

    /**
     * 根据所有设备类型信息
     *
     * @return
     */
    List<DeviceType> list();

    /**
     * 根据设备类型列表查询设备类型列表
     *
     * @param deviceTypes 设备类型列表
     * @return
     */
    List<DeviceType> listByTypes(@Param("deviceTypes") List<Integer> deviceTypes);

    /**
     * 获取设备名称
     * @param deviceType
     * @return
     */
    String getName(@Param("deviceType")Integer deviceType);

    /**
     * 根据条件查询查询设备类型列表
     *
     * @param dto 设备类型
     * @return
     */
    List<DeviceType> listByCondition(QueryDeviceTypeDTO dto);

    /**
     * 根据产品型号查询设备类型信息
     *
     * @param productModelCode 产品型号编码
     * @return
     */
    List<DeviceType> getByProductModelCode(@Param("productModelCode") String productModelCode);

    /**
     * 根据产品型号列表查询设备类型信息
     *
     * @param productModelCodes 产品型号编码列表
     * @return
     */
    List<DeviceType> listByProductModelCodes(@Param("productModelCodes") List<String> productModelCodes);

    /**
     * 根据设备内部名称模糊查询设备类型列表
     *
     * @param keywords 设备内部
     * @return
     */
    List<DeviceType> listDeviceTypeLikeName(@Param("keywords") String keywords);
}
