package com.moredian.magicube.device.third.lock;


import cn.hutool.crypto.digest.MD5;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.moredian.bee.common.exception.*;
import com.moredian.bee.common.utils.HttpInvoker;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.bee.common.utils.HttpInvokerResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.third.lock.config.OpenApiProperties;
import com.moredian.magicube.device.third.lock.config.UriConstants;
import com.moredian.magicube.device.third.lock.req.CommonReq;
import com.moredian.magicube.device.third.lock.req.InitializeLockReq;
import com.moredian.magicube.device.third.lock.req.UploadWifiReq;
import com.moredian.magicube.device.third.lock.res.AccessTokenResp;
import com.moredian.magicube.device.third.lock.res.CommonRes;
import com.moredian.magicube.device.third.lock.res.InitializeLockResp;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Description 通通锁开放平台接口服务
 * @create 2025-02-21 14:32
 */
@Slf4j
@Service
public class TTLockOpenService {

    @Resource
    private RedissonCacheComponent redissonCacheComponent;
    @Resource
    private OpenApiProperties openApiProperties;


    /**
     * 通通锁上报wifi信息，调用开放平台接口
     */
    public Boolean uploadWifiInfo(UploadWifiReq uploadWifiReq) {
        // 开放平台上传网络信息使用异步处理
        CompletableFuture.runAsync(()-> {
            Integer lockId = uploadWifiReq.getLockId();
            BizAssert.isTrue(lockId != null, "lockId must be not empty");
            String token = getToken();
            buildParams(uploadWifiReq);
            uploadWifiReq.setAccessToken(token);
            String params = objConvertUrlParams(uploadWifiReq);

            // 上报锁wifi信息
            String url = openApiProperties.getApiUrl() + UriConstants.UPLOAD_WIFI_INFO + params;
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");
            HttpInvokerResponse response = HttpInvoker.invokerPost(url, headers, null, null);
            boolean success = response.isSuccess();
            if (success) {
                CommonRes commonRes = JsonUtils.fromJson(CommonRes.class, response.getBody());
                if (commonRes != null && commonRes.getErrcode() != null && commonRes.getErrcode() == 0) {
                    log.info("上报锁wifi信息成功，resp=>{}", commonRes);
                }else {
                    log.error("上报锁wifi信息失败，req=>{}, resp=>{}", uploadWifiReq, response.getBody());
                    throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_WIFI_CONFIG_FAILED, DeviceErrorCode.DEVICE_WIFI_CONFIG_FAILED.getMessage()));
                }
            }
        });
        return Boolean.TRUE;
    }



    /**
     * 调用通通锁初始化锁开放接口
     */
    public InitializeLockResp initializeLock(String lockData) {
        BizAssert.isTrue(StringUtil.isNotBlank(lockData), "lockData must be not empty");

        // 请求参数
        String accessToken = getToken();
        InitializeLockReq initializeLockReq = new InitializeLockReq();
        buildParams(initializeLockReq);
        initializeLockReq.setLockData(lockData);
        initializeLockReq.setAccessToken(accessToken);
        String params = objConvertUrlParams(initializeLockReq);

        String url = openApiProperties.getApiUrl() + UriConstants.INITIALIZE_LOCK + params;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        HttpInvokerResponse response = HttpInvoker.invokerPost(url, headers, null, null);
        boolean success = response.isSuccess();
        if (success) {
            InitializeLockResp initializeLockResp = JsonUtils.fromJson(InitializeLockResp.class, response.getBody());
            if (initializeLockResp != null && initializeLockResp.getLockId() != null) {
                log.info("初始化锁成功，resp=>{}", initializeLockResp);
               return initializeLockResp;
            }
        }
        log.error("初始化通通锁失败，req=>{}, resp=>{}", initializeLockReq, response.getBody());
        throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_ACCESS_TOKEN_FAILED, DeviceErrorCode.DEVICE_ACCESS_TOKEN_FAILED.getMessage()));
    }



    /**
     * 获取 TTLock 的 accessToken，先从缓存中取，如果没有再从接口获取
     */
   public String getToken() {
       String tokenKey = RedisConstants.getKey(RedisConstants.TT_LOCK_ACCESS_TOKEN);

       String accessToken = redissonCacheComponent.getStrCache(tokenKey);
       if (StringUtil.isBlank(accessToken)) {
           String url = openApiProperties.getApiUrl() + UriConstants.ACCESS_TOKEN;
           Map<String, String> headers = new HashMap<>();
           headers.put("Content-Type", "application/x-www-form-urlencoded");
           CommonReq commonReq = new CommonReq();
           buildParams(commonReq);
           String params = objConvertUrlParams(commonReq);
           url = url + params;
           HttpInvokerResponse response = HttpInvoker.invokerPost(url, headers, null, null);
           boolean success = response.isSuccess();
           if (success) {
               AccessTokenResp tokenResp = JsonUtils.fromJson(AccessTokenResp.class, response.getBody());
               if (tokenResp != null && StringUtil.isNotBlank(tokenResp.getAccess_token())) {
                   accessToken = tokenResp.getAccess_token();
                   log.info("获取通通锁accessToken，resp=>{}", tokenResp);
                   redissonCacheComponent.setStrCache(tokenKey, accessToken, tokenResp.getExpires_in());
               }else {
                   log.warn("获取通通锁accessToken失败=>{}", response.getBody());
               }
           }else {
               log.error("获取通通锁accessToken失败=>{}", response.getBody());
               throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_ACCESS_TOKEN_FAILED, DeviceErrorCode.DEVICE_ACCESS_TOKEN_FAILED.getMessage()));
           }
       }
       return accessToken;
   }


    private void buildParams(CommonReq commonReq) {
        commonReq.setClientId(openApiProperties.getClientId());
        commonReq.setClientSecret(openApiProperties.getClientSecret());
        commonReq.setUsername(openApiProperties.getUsername());
        commonReq.setPassword(openApiProperties.getPassword());
    }


    private static <T> String objConvertUrlParams(T obj) {
        if (obj == null) {
            return "";
        }
        Map<String, Object> map = objConvertMap(obj);
        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            if (value != null) {
                if (result.length() > 0) {
                    result.append("&");
                }
                try {
                    result.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                            .append("=")
                            .append(URLEncoder.encode(value.toString(), "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        if (result.length() == 0) {
            return "";
        }
        return "?" + result;
    }


    /**
     * 对象转map
     */
    private static <T> Map<String, Object> objConvertMap(T obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map map = objectMapper.convertValue(obj, Map.class);
        Map<String, Object> res = new HashMap<>();
        map.forEach((key, value) -> {
            if (value != null){
                res.put(String.valueOf(key), value);
            }
        });
        return res;
    }
}