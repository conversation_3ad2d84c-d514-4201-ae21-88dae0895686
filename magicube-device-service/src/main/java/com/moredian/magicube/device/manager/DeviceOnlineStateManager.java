package com.moredian.magicube.device.manager;


import com.moredian.magicube.device.dao.entity.DeviceOnlineState;
import com.moredian.ota.api.msg.DeviceOnlineStateMsg;

import java.util.List;

public interface DeviceOnlineStateManager {

    /**
     *
     * @param msg 设备在离线信息
     * @return
     */
    void insert(DeviceOnlineStateMsg msg);

    List<DeviceOnlineState> selectByOrgIdAndDeviceId(Long orgId,List<Long> deviceId);

    List<DeviceOnlineState> selectByDeviceSnList(List<String> deviceSnList);
}
