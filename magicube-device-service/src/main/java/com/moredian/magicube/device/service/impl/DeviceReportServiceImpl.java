package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.device.DeviceReportInfoCallbackMsg;
import com.moredian.magicube.device.dto.device.DeviceReportInfoDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceInfoDTO;
import com.moredian.magicube.device.manager.DeviceReportManager;
import com.moredian.magicube.device.service.DeviceReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/1/24
 */
@SI
@Slf4j
public class DeviceReportServiceImpl implements DeviceReportService {

    @Autowired
    private DeviceReportManager deviceReportManager;

    @Override
    public ServiceResponse<Boolean> notifyDeviceReportInfo(DeviceReportInfoDTO dto) {
        return new ServiceResponse<>(deviceReportManager.notifyDeviceReportInfo(dto));
    }

    @Override
    public ServiceResponse<Boolean> reportDeviceInfo(ReportDeviceInfoDTO dto) {
        Boolean result = deviceReportManager.reportDeviceInfo(dto);
        if (result && dto.getReportType() != null && 1 == dto.getReportType()) {
            DeviceReportInfoCallbackMsg msg = new DeviceReportInfoCallbackMsg();
            msg.setOrgId(dto.getOrgId());
            msg.setDeviceId(dto.getDeviceId());
            msg.setDeviceSn(dto.getDeviceSn());
            msg.setElectricity(dto.getElectric());
            msg.setLocalTime(dto.getLocalTime());
            if (dto.getWifiInfo() != null) {
                msg.setWifiStrength(dto.getWifiInfo().getWifiStrength());
            }
            EventBus.publish(msg);
            log.info("发送设备信号强度和电量消息成功:[{}]", JsonUtils.toJson(msg));
        }
        return new ServiceResponse<>(result);
    }
}
