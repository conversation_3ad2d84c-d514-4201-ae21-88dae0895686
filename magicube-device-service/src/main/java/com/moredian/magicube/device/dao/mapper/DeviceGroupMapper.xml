<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceGroupMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceGroup">
    <result column="device_group_id" property="deviceGroupId"/>
    <result column="org_id" property="orgId"/>
    <result column="device_id" property="deviceId"/>
    <result column="group_id" property="groupId"/>
    <result column="group_code" property="groupCode"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <resultMap id="GroupDeviceSizeDtoMap" type="com.moredian.magicube.device.dto.group.GroupDeviceSizeDTO">
    <result column="group_id" property="groupId" />
    <result column="device_size" property="deviceSize" />
  </resultMap>

  <sql id="sql_table">
        hive_device_group
    </sql>

  <sql id="sql_columns">
        device_group_id,
        org_id,
        device_id,
        group_id,
        group_code,
        gmt_create,
        gmt_modify
    </sql>

  <sql id="sql_values">
        #{deviceGroupId},
        #{orgId},
        #{deviceId},
        #{groupId},
        #{groupCode},
        now(3),
        now(3)
    </sql>

  <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceGroup">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
    ON DUPLICATE KEY UPDATE gmt_modify = now(6)
  </insert>

  <insert id="batchInsert" parameterType="list">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    <foreach collection="deviceGroups" item="item" separator=",">
      (
      #{item.deviceGroupId},
      #{item.orgId},
      #{item.deviceId},
      #{item.groupId},
      #{item.groupCode},
      now(),
      now()
      )
    </foreach>
  </insert>

  <select id="listDeviceIdByOrgIdAndGroupId" parameterType="long" resultType="long">
    select
    DISTINCT(device_id)
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and group_id = #{groupId}
  </select>

  <select id="listDeviceIdByOrgIdAndGroupIds" parameterType="long" resultType="long">
    select
    DISTINCT(device_id)
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="groupIds != null and groupIds.size() > 0">
      and group_id in
      <foreach collection="groupIds" index="index" item="groupId" open="(" separator="," close=")">
        #{groupId}
      </foreach>
    </if>
  </select>

  <select id="listDeviceIdByOrgIdAndGroupIdsAndAppType" parameterType="map" resultType="long">
    select
    DISTINCT(g.device_id)
    from hive_device_group g
    inner join hive_group n
    on g.group_id = n.group_id
    where g.org_id = #{orgId}
    <if test="appType != null">
      and n.app_type = #{appType}
    </if>
    <if test="groupIds != null and groupIds.size() > 0">
      and g.group_id in
      <foreach collection="groupIds" index="index" item="groupId" open="(" separator="," close=")">
        #{groupId}
      </foreach>
    </if>
  </select>

  <select id="listByOrgIdAndGroupIds" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="groupIds != null and groupIds.size() > 0">
      and group_id in
      <foreach collection="groupIds" index="index" item="groupId" open="(" separator="," close=")">
        #{groupId}
      </foreach>
    </if>
  </select>

  <select id="listDeviceIdsByGroupId" resultType="java.lang.Long">
    select device_id
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and group_id = #{groupId}
    <if test="offset != null and limit != null">
      limit #{offset}, #{limit}
    </if>
  </select>

  <select id="listGroupIdByOrgIdAndDeviceIds" parameterType="long" resultType="long">
    select group_id
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceIds != null and deviceIds.size() > 0">
      and device_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
  </select>

  <select id="listGroupIdByOrgIdAndDeviceIdsAndAppType" parameterType="map" resultType="long">
    select g.group_id
    from hive_device_group g
    inner join hive_group n
    on g.group_id = n.group_id
    where g.org_id = #{orgId}
    <if test="appType != null">
      and n.app_type = #{appType}
    </if>
    <if test="deviceIds != null and deviceIds.size() > 0">
      and g.device_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
  </select>

  <select id="listByOrgIdAndDeviceIds" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceIds != null and deviceIds.size() > 0">
      and device_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
  </select>

  <select id="listGroupNameByCondition" parameterType="long" resultType="string">
    select group_code
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and device_id = #{deviceId}
  </select>

  <delete id="deleteByCondition" parameterType="long">
    delete from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="deviceIds != null and deviceIds.size() > 0">
      and device_id in
      <foreach collection="deviceIds" index="index" item="deviceId" open="(" separator=","
        close=")">
        #{deviceId}
      </foreach>
    </if>
    <if test="groupIds != null and groupIds.size() > 0">
      and group_id in
      <foreach collection="groupIds" index="index" item="groupId" open="(" separator="," close=")">
        #{groupId}
      </foreach>
    </if>
  </delete>

  <select id="findGroupIdByDeviceIdAppTypes" parameterType="map" resultType="long">
    select
    g.group_id
    from
    hive_device_group g
    inner join
    hive_group n
    on
    g.group_id = n.group_id
    <if test="appTypes != null and appTypes.size() != 0">
      and n.app_type in
      <foreach collection="appTypes" index="index" item="appType" open="(" separator="," close=")">
        #{appType}
      </foreach>
    </if>
    where
    g.org_id = #{orgId}
    and g.device_id = #{deviceId}
  </select>

  <select id="countDeviceSizeGroupByGroupId" resultMap="GroupDeviceSizeDtoMap">
    SELECT group_id,COUNT(1) AS device_size FROM hive_device_group WHERE org_id=#{orgId} AND group_id IN
    <foreach collection="groupIds" item="groupId" open="(" separator="," close=")" index="index">
      #{groupId}
    </foreach>
    GROUP BY group_id
  </select>

  <delete id="deleteByDeviceGroupIdList" parameterType="map">
    delete from hive_device_group where org_id = #{orgId}
    and device_group_id in
    <foreach collection="deviceGroupIdList" index="index" item="deviceGroupId" open="(" separator="," close=")">#{deviceGroupId}</foreach>
  </delete>

  <select id="findRelationByGroupId"  resultMap="BaseResultMap">
    select <include refid="sql_columns"></include>
    from hive_device_group where org_id = #{orgId} and group_id = #{groupId}
    <if test="deviceIdList != null and deviceIdList.size()>0">
      and device_id in
      <foreach collection="deviceIdList" item="deviceId" open="(" separator="," close=")" index="index">
        #{deviceId}
      </foreach>
    </if>
  </select>
</mapper>
