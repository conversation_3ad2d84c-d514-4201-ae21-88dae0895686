package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddBatchDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewAddDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewErrorDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewInfoDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewQueryDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewUpdateDTO;
import com.moredian.magicube.device.manager.PeripheralsWhiteListNewManager;
import com.moredian.magicube.device.service.PeripheralsWhiteListNewService;
import com.xier.sesame.common.rpc.dto.PaginationDto;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Auther: _AF
 * @Date: 2/23/22 17:33
 * @Description:
 */
@SI
public class PeripheralsWhiteListNewServiceImpl implements PeripheralsWhiteListNewService {

    @Autowired
    private PeripheralsWhiteListNewManager peripheralsWhiteListNewManager;

    @Override
    public ServiceResponse<PaginationDto<PeripheralsWhiteListNewInfoDTO>> pageByCondition(
        PeripheralsWhiteListNewQueryDTO dto) {
        PaginationDto<PeripheralsWhiteListNewInfoDTO> pageDto = peripheralsWhiteListNewManager.pageByCondition(dto);
        return new ServiceResponse<>(pageDto);
    }

    @Override
    public ServiceResponse<Boolean> removeByIds(List<Long> ids) {
        Boolean result = peripheralsWhiteListNewManager.removeByIds(ids);
        return new ServiceResponse<>(result);
    }

    @Override
    public ServiceResponse<Boolean> updateById(PeripheralsWhiteListNewUpdateDTO dto) {
        Boolean result = peripheralsWhiteListNewManager.updateById(dto);
        return new ServiceResponse<>(result);
    }

    @Override
    public ServiceResponse<Boolean> addPeripheralsWhiteListNew(PeripheralsWhiteListNewAddDTO dto) {
        Boolean result = peripheralsWhiteListNewManager.addPeripheralsWhiteListNew(dto);
        return new ServiceResponse<>(result);
    }

    @Override
    public ServiceResponse<List<PeripheralsWhiteListNewErrorDTO>> addByBatch(List<PeripheralsWhiteListNewAddBatchDTO> dto) {
        List<PeripheralsWhiteListNewErrorDTO> response = peripheralsWhiteListNewManager.addByBatch(dto);
        return new ServiceResponse<>(response);
    }

    @Override
    public ServiceResponse<PeripheralsWhiteListNewInfoDTO> getBySnAndType(String peripheralsSn, Integer peripheralsType) {
        PeripheralsWhiteListNewInfoDTO dataResponse = peripheralsWhiteListNewManager.getBySnAndType(peripheralsSn, peripheralsType);
        return new ServiceResponse<>(dataResponse);
    }

    @Override
    public ServiceResponse<List<PeripheralsWhiteListNewInfoDTO>> getBySnList(List<String> peripheralsSnList) {
        List<PeripheralsWhiteListNewInfoDTO> dataResponseList = peripheralsWhiteListNewManager.getBySnList(peripheralsSnList);
        return new ServiceResponse<>(dataResponseList);
    }

    @Override
    public ServiceResponse<Boolean> syncOldDataToNewData() {
        peripheralsWhiteListNewManager.syncOldDataToNewData();
        return new ServiceResponse<>(true);
    }
}
