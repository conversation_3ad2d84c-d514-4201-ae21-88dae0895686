package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceProjectInfo;
import com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DeviceProjectInfoMapper {

    /**
     * 设备上报新增项目信息
     *
     * @param deviceProjectInfo
     * @return
     */
    Long insert(DeviceProjectInfo deviceProjectInfo);


    /**
     * 设备上报更新项目信息
     *
     * @param deviceProjectInfo
     * @return
     */
    Integer update(DeviceProjectInfo deviceProjectInfo);

    /**
     * 根据设备Sn查询项目信息
     *
     * @param deviceSn
     * @return
     */
    DeviceProjectInfo query(@Param("deviceSn") String deviceSn);

    /**
     * 根据条件查询项目信息列表
     *
     * @param request
     * @return
     */
    List<DeviceProjectInfo> findDeviceProjectInfo(QueryDeviceProjectInfoRequest request);
}