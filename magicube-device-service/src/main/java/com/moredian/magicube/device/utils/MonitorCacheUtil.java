package com.moredian.magicube.device.utils;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 机构缓存工具类
 *
 * @description:
 * @author: gongchang
 * @time: 2020/8/24 14:06
 */
public class MonitorCacheUtil {

    /**
     * 节点缓存过期间隔 30分钟
     */
    private static final Long CACHE_EXPIRE_RANGE = 30 * 60 * 1000L;

    /**
     * 全量清理过期节点间隔 5分钟
     */
    private static final Long FULL_CLEAR_EXPIRED_RANGE = 5 * 60 * 1000L;

    /**
     * 机构缓存
     */
    private final static Map<Long, MonitorCache> orgNameMap = Maps.newConcurrentMap();

    /**
     * 初始化下一次清理时间
     */
    private static volatile Long nextFullClearTime = System.currentTimeMillis() + FULL_CLEAR_EXPIRED_RANGE;

    public static MonitorCache get(Long orgId) {
        //全量扫描 清理过期机构
        incrExipreAndfullClearIfNessary(orgId);
        return orgNameMap.get(orgId);
    }

    /**
     * 全量扫描 清理过期机构  对正在获取的机构自动延时
     */
    private static void incrExipreAndfullClearIfNessary(Long orgId) {
        //判断是否需发起过期缓存全量清理
        Date now = new Date();
        //第一次校验
        if (MonitorCacheUtil.nextFullClearTime < now.getTime()) {
            synchronized (orgNameMap) {
                //并发排队 进入后二次校验 若nextFullClearTime已经被更新则跳过此次
                if (MonitorCacheUtil.nextFullClearTime < now.getTime()) {
                    nextFullClearTime = now.getTime() + FULL_CLEAR_EXPIRED_RANGE;
                    for (Map.Entry<Long, MonitorCache> entry : orgNameMap.entrySet()) {
                        //如果是当前需要获取的机构 则自动延时
                        if (entry.getKey().equals(orgId)) {
                            entry.getValue().setExireTime(now.getTime() + CACHE_EXPIRE_RANGE);
                        }
                        //对过期机构进行处理
                        if (entry.getValue().exireTime < now.getTime()) {
                            orgNameMap.remove(entry.getKey());
                        }

                    }
                }
            }
        }
    }

    /**
     * 添加缓存
     * put并发较低 直接对整个map加锁
     *
     * @param orgId
     * @param orgName
     * @return
     */
    public static MonitorCache put(Long orgId, String orgName) {
        MonitorCache monitorCache;
        synchronized (orgNameMap) {
            monitorCache = new MonitorCache(orgId, orgName);
            orgNameMap.put(orgId, monitorCache);
        }
        return monitorCache;
    }

    /**
     * 目前只存放机构名 后期可添加更多
     */
    @Data
    public static class MonitorCache {
        private Long orgId;
        private String orgName;
        private Long exireTime;

        public MonitorCache(Long orgId, String orgName) {
            this.orgId = orgId;
            this.orgName = orgName;
            this.exireTime = System.currentTimeMillis() + CACHE_EXPIRE_RANGE;
        }
    }
}
