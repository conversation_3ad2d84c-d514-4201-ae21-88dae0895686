<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceOnlineStateMapper">
    <resultMap id="DeviceOnlineStateMap" type="com.moredian.magicube.device.dao.entity.DeviceOnlineState">
        <result column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="online_flag" property="onlineFlag"/>
        <result column="msg_timestamp" property="msgTimestamp"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceOnlineState">
        insert into
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
        ON DUPLICATE KEY UPDATE
        online_flag = IF(
        #{onlineFlag} IS NOT NULL AND msg_timestamp &lt;= #{msgTimestamp},
        CASE
        WHEN msg_timestamp = #{msgTimestamp} AND (#{onlineFlag} = 1 OR online_flag = 1)
        THEN 1
        ELSE #{onlineFlag}
        END,
        online_flag
        ),
        msg_timestamp = IF(
        #{msgTimestamp} IS NOT NULL AND msg_timestamp &lt;= #{msgTimestamp},
        #{msgTimestamp},
        msg_timestamp
        ),
        org_id = IF(#{orgId} IS NOT NULL AND msg_timestamp &lt;= #{msgTimestamp},
            #{orgId},
            org_id),
        device_id = IF(#{deviceId} IS NOT NULL AND msg_timestamp &lt;= #{msgTimestamp},
            #{deviceId},
            device_id),
        gmt_modify = NOW(3)
    </insert>
    <update id="updateState" parameterType="com.moredian.magicube.device.dao.entity.DeviceOnlineState">
        update
        <include refid="sql_table"/>
        <set>
            <if test="onlineFlag!=null">
                online_flag = CASE
                WHEN msg_timestamp=#{msgTimestamp} AND (#{onlineFlag} = 1 OR online_flag = 1)
                THEN 1
                ELSE #{onlineFlag}
                END,
            </if>
            <if test="msgTimestamp!=null">
                msg_timestamp=#{msgTimestamp},
            </if>
            <if test="orgId!=null">
                org_id=#{orgId},
            </if>
            <if test="deviceId!=null">
                device_id=#{deviceId},
            </if>
            gmt_modify=now()
        </set>
        where id=#{id}
        and msg_timestamp &lt;= #{msgTimestamp}
    </update>

    <select id="selectByDeviceSn" parameterType="string" resultMap="DeviceOnlineStateMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where
        device_sn = #{deviceSn}
    </select>
    <select id="selectByOrgIdAndDeviceId" resultMap="DeviceOnlineStateMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        <if test="deviceIdList != null and deviceIdList.size() > 0">
        and device_id in
        <foreach collection="deviceIdList" index="index" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        </if>
    </select>

    <sql id="sql_table">
        hive_device_online_state
    </sql>

    <sql id="sql_columns">
        id,
        org_id,
        device_id,
        device_sn,
        online_flag,
        msg_timestamp,
        gmt_create,
        gmt_modify
    </sql>
    <sql id="sql_values">
        #{id},
        #{orgId},
        #{deviceId},
        #{deviceSn},
        #{onlineFlag},
        #{msgTimestamp},
        now(3),
        now(3)
    </sql>

<!--auto generated by MybatisCodeHelper on 2023-12-27-->
    <select id="selectByDeviceSnList" resultMap="DeviceOnlineStateMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where device_sn in
        <foreach item="item" index="index" collection="deviceSnCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>