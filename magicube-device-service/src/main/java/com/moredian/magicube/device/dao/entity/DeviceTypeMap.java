package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_type_map")
public class DeviceTypeMap extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 映射名称（分组）
     */
    private String mapName;

    /**
     * 映射key
     */
    private String mapKey;

    /**
     * 映射value
     */
    private String mapValue;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;


}
