package com.moredian.magicube.device.third.lock.config;

/**
  *@Description      请求 uri 地址
  *<AUTHOR>
  *@create          2025-03-11 15:21
  */
public class UriConstants {

    /**
     * 获取访问令牌
     */
    public static final String ACCESS_TOKEN = "/oauth2/token";

    /**
     * 初始化锁
     */
    public static final String INITIALIZE_LOCK = "/v3/lock/initialize";

    /**
     * 上报wifi信息
     */
    public static final String UPLOAD_WIFI_INFO = "/v3/wifiLock/updateNetwork";
}
