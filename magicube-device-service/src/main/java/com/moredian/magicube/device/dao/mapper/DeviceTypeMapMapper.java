package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceTypeMap;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 设备类型表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Mapper
public interface DeviceTypeMapMapper {

    /**
     * 新增映射
     *
     * @param deviceTypeMap
     * @return
     */
    int insert(DeviceTypeMap deviceTypeMap);

    /**
     * 获取map值
     *
     * @param mapName
     * @param mapKey
     * @return
     */
    String getValue(@Param("mapName") String mapName, @Param("mapKey") String mapKey);

    /**
     * 获取map值
     * @param mapName
     * @param mapKeyList
     * @return
     */
    List<DeviceTypeMap> getValues(@Param("mapName") String mapName, @Param("mapKeyList") List<String> mapKeyList);

    /**
     * 修改设备人脸容量
     *
     * @param deviceType
     * @param deviceCapacity
     * @return
     */
    int updateDeviceCapacity(@Param("deviceType") String deviceType, @Param("deviceCapacity") String deviceCapacity);
}
