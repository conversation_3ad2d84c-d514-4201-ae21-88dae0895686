<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.moredian.magicube.device.dao.mapper.PeripheralsWhiteListNewMapper">

    <resultMap id="baseResultMap" type="com.moredian.magicube.device.dao.entity.PeripheralsWhiteListNew">
        <result column="id" property="id"/>
        <result column="peripherals_sn" property="peripheralsSn"/>
        <result column="device_type" property="deviceType"/>
        <result column="app_type" property="appType"/>
        <result column="peripherals_type" property="peripheralsType"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="base_column_list">
    id,org_id,peripherals_sn,device_type,app_type,peripherals_type,gmt_create,gmt_modify
  </sql>


    <select id="pageByCondition" parameterType="com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListNewQueryDTO"
            resultMap="baseResultMap">
        select * from
        <include refid="table_name"/>
        <where>
            <if test="peripheralsSn!=null">
                and peripherals_sn like concat('%', #{peripheralsSn}, '%')
            </if>
            <if test="deviceType != null">
                and device_type = #{deviceType}
            </if>
            <if test="appType != null">
                and app_type = #{appType}
            </if>
            <if test="peripheralsType != null">
                and peripherals_type = #{peripheralsType}
            </if>
            <if test="beginTime != null and endTime !=null">
                and #{beginTime} &lt; gmt_create
                and gmt_create &lt; #{endTime}
            </if>
        </where>
        order by gmt_create desc
    </select>

    <delete id="removeByIds" parameterType="list">
        delete from
        <include refid="table_name"/>
        where id in
        <foreach item="id" index="index" collection="ids" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <update id="updateById" parameterType="com.moredian.magicube.device.dao.entity.PeripheralsWhiteListNew">
        update
        <include refid="table_name"/>
        <set>
            <if test="peripheralsSn != null">
                peripherals_sn = #{peripheralsSn},
            </if>
            <if test="deviceType != null">
                device_type = #{deviceType},
            </if>
            <if test="appType != null">
                app_type = #{appType},
            </if>
            <if test="peripheralsType != null">
                peripherals_type = #{peripheralsType},
            </if>
            gmt_modify = now(3)
        </set>
        where id = #{id}
    </update>

    <insert id="addPeripheralsWhiteNewList" parameterType="com.moredian.magicube.device.dao.entity.PeripheralsWhiteListNew">
        insert into
        <include refid="table_name"/>
        (id,peripherals_sn,device_type,app_type,peripherals_type,gmt_create,gmt_modify)
        values
        (#{id},#{peripheralsSn},#{deviceType},#{appType},#{peripheralsType},now(3),now(3))
    </insert>

    <insert id="addByBatch" parameterType="java.util.List">
        insert into
        <include refid="table_name"/> (id,peripherals_sn,device_type,app_type,peripherals_type,gmt_create,gmt_modify)
        VALUES
        <foreach collection="requestList" item="item" separator=",">
            (#{item.id}, #{item.peripheralsSn}, #{item.deviceType},#{item.appType},#{item.peripheralsType},now(3),now(3))
        </foreach>
    </insert>

    <select id="getBySnAndType" resultMap="baseResultMap">
        select *
        from
        <include refid="table_name"/>
        where peripherals_sn = #{peripheralsSn} and peripherals_type = #{peripheralsType}
    </select>

    <select id="getBySnList" resultMap="baseResultMap">
        select *
        from
        <include refid="table_name"/>
        where peripherals_sn in
        <foreach collection="peripheralsSnList" item="peripheralsSn" open="(" close=")" separator=",">
            #{peripheralsSn}
        </foreach>

    </select>

    <sql id="table_name">
        hive_peripherals_white_list_new
    </sql>

</mapper>