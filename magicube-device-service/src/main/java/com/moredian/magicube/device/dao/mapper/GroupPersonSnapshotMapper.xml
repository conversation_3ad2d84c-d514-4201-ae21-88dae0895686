<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.GroupPersonSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.GroupPersonSnapshot">
        <id column="id" property="id"/>
        <result column="snapshot_id" property="snapshotId"/>
        <result column="org_id" property="orgId"/>
        <result column="group_id" property="groupId"/>
        <result column="person_type" property="personType"/>
        <result column="person_id" property="personId"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_group_person_snapshot
    </sql>

    <sql id="sql_columns">
        id,
        snapshot_id,
        org_id,
        group_id,
        person_type,
        person_id,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{id},
        #{snapshotId},
        #{orgId},
        #{groupId},
        #{personType},
        #{personId},
        now(3),
        now(3)
    </sql>

    <insert id="batchInsert">
        insert into
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        ) values
        <foreach collection="groupPersonSnapshotList" item="item" separator=",">
            (
            #{item.id},
            #{item.snapshotId},
            #{item.orgId},
            #{item.groupId},
            #{item.personType},
            #{item.personId},
            now(3),
            now(3)
            )
        </foreach>
    </insert>
    <delete id="deleteBySnapshotId">
        delete from
        <include refid="sql_table"/>
        where org_id = #{orgId} and snapshot_id = #{snapshotId}
    </delete>

    <select id="getDistinctPersonSize" resultType="java.lang.Integer">
        select count(DISTINCT person_id) from
        <include refid="sql_table"/>
        where org_id = #{orgId} and snapshot_id = #{snapshotId} and group_id in
        <foreach collection="groupIdList" index="index" item="groupId" open="(" separator="," close=")">#{groupId}
        </foreach>
    </select>

    <select id="pageGroupDistinctPerson" resultMap="BaseResultMap">
        select
        DISTINCT person_id ,person_type
        from
        <include refid="sql_table"/>
        where org_id = #{orgId} and snapshot_id = #{snapshotId}
        <if test="groupIds != null and groupIds.size() > 0">
            and group_id in
            <foreach collection="groupIds" index="index" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        and person_type in (1 ,3)
        order by person_id asc
        limit #{offset},#{limit}
    </select>

    <select id="findGroupIdByPersonIds" resultType="com.moredian.magicube.device.model.PersonIdAndGroupIdSnapshot">
        select
        person_id as personId,
        group_id as groupId
        from
        <include refid="sql_table"/>
        where
        org_id = #{orgId}
        and snapshot_id = #{snapshotId}
        <if test="groupIdList != null and groupIdList.size() > 0">
            and group_id in
            <foreach collection="groupIdList" index="index" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        and person_type in (1 ,3)
        <if test="personIdList != null and personIdList.size() > 0">
            and person_id in
            <foreach collection="personIdList" index="index" item="personId" open="(" separator="," close=")">
                #{personId}
            </foreach>
        </if>
    </select>

</mapper>
