package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.common.enums.PeripheralConnectionStatusEnums;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 外设表
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_peripherals")
public class Peripherals extends TimedEntity {

    private static final long serialVersionUID = 645896068787305053L;

    /**
     * 主键id
     */
    private Long peripheralsId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 外设名称
     */
    private String peripheralsName;

    /**
     * 外设sn
     */
    private String peripheralsSn;

    /**
     * 关联设备sn
     */
    private String deviceSn;

    /**
     * 关联设备名称
     */
    private String deviceName;

    /**
     * 设备连接状态
     *
     * @see PeripheralConnectionStatusEnums
     */
    private Integer status;

    /**
     * 启动状态  0-不启用；1-启用
     */
    private Integer available;

    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 设备型号
     */
    private String modelType;

    /**
     * 绑定时间
     */
    private Date bindTime;

    /**
     * 关联的设备类型
     */
    private Integer deviceType;

    /**
     * 外设的设备类型
     */
    private Integer peripheralsType;

    /**
     * 外设连接设备的唯一标识
     */
    private String peripheralsConnectId;

    /**
     * 系统类型 1-ios ，2-android
     */
    private Integer mobileSysType;

    /**
     * 匹配状态：0-不匹配，1-匹配
     */
    private Integer matchStatus;
}
