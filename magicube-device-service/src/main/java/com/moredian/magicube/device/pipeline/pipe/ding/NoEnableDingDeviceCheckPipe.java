package com.moredian.magicube.device.pipeline.pipe.ding;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.DeviceUnbindMsg;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.constant.StatusCode;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.magicube.device.service.DeviceService;
import com.xier.ding.api.corp.dto.CorpDto;
import com.xier.ding.api.corp.service.CorpHelperService;
import com.xier.ding.api.corp.service.SuiteCorpUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

/**
 * 未开通魔点门禁微应用的钉钉设备二维码激活校验管道
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class NoEnableDingDeviceCheckPipe extends
    AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    @Autowired
    private DeviceService deviceService;

    @SI
    private CorpHelperService corpHelperService;

    @SI
    private SuiteCorpUserService suiteCorpUserService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        //1.判断是否缺少参数
        if (StringUtils.isEmpty(dto.getThirdDeviceId()) || StringUtils.isEmpty(dto.getCorpId())) {
            redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                    + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.THIRD_PARTY_ACTIVE_FAILED,HiveConst.THIRD_PARTY_TIME_OUT);
            ErrorContext errorContext = new ErrorContext("FD0020171202", "未取得第三方确认信息");
            BizAssert.notNull(null, errorContext, errorContext.getMessage());
        }

        //2.没有机构的设备激活校验
        String orgName = "考勤机机构：" + dto.getCorpId();
        Long orgId;
        com.xier.sesame.common.rpc.ServiceResponse<CorpDto> corpDto = corpHelperService
            .getCorpByDingCorpId(dto.getCorpId());
        if (!corpDto.isSuccess() || corpDto.getData() == null) {
            CorpDto cd = new CorpDto();
            cd.setDingCorpId(dto.getCorpId());
            cd.setCorpName(orgName);
            com.xier.sesame.common.rpc.ServiceResponse<Long> orgIdResponse = corpHelperService
                .createCorp(null, cd);
            if (!orgIdResponse.isSuccess() || !orgIdResponse.isExistData()) {
                log.info("设备激活中创建机构失败{}", orgIdResponse);
                ErrorContext errorContext = new ErrorContext("FD0020171203", "机构创建失败!");
                BizAssert.notNull(null, errorContext, errorContext.getMessage());
            }
            orgId = orgIdResponse.getData();
        } else {
            orgId = corpDto.getData().getOrgId();
        }
        context.setOrgId(orgId);
        InventoryDevice inventoryDevice = context.getInventoryDevice();

        //3.校验是否需要重新激活
        if (inventoryDevice.getThirdDeviceId() == null || !dto.getThirdDeviceId()
            .equals(inventoryDevice.getThirdDeviceId())) {
            //如果是已激活设备，先清除设备，再重新激活
            DeviceInfoDTO deviceInfoDTO = deviceService.getByDeviceSn(dto.getDeviceSn())
                .pickDataThrowException();
            if (deviceInfoDTO != null) {
                ServiceResponse<Boolean> serviceResponse = deviceService
                    .deleteById(orgId, deviceInfoDTO.getDeviceId());
                if (serviceResponse == null || !serviceResponse.isSuccess() || !serviceResponse
                    .getData()) {
                    redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                            + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                        StatusCode.THIRD_PARTY_ACTIVE_FAILED,HiveConst.THIRD_PARTY_TIME_OUT);
                    ErrorContext errorContext = new ErrorContext("FD0020171204", "设备已存在");
                    BizAssert.notNull(null, errorContext, errorContext.getMessage());
                }
                //旧设备删除，发送RMq消息
                DeviceUnbindMsg deviceUnbindMsg = new DeviceUnbindMsg();
                BeanUtils.copyProperties(deviceInfoDTO, deviceUnbindMsg);
                deviceUnbindMsg.setName(deviceInfoDTO.getDeviceName());
                deviceUnbindMsg.setTimeStamp(System.currentTimeMillis());
                deviceUnbindMsg.setTpId(inventoryDevice.getThirdDeviceId());
                EventBus.publish(deviceUnbindMsg);
            }
        }
    }
}
