package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.StockDeviceVersion;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StockDeviceVersionMapper {

    /**
     * 批量获取库存设备版本信息
     *
     * @param deviceSnList
     */
    List<StockDeviceVersion> getBatchStockDeviceVersion(@Param("deviceSnList") List<String> deviceSnList);

    /**
     * 批量插入库存设备信息
     *
     * @param stockDeviceVersionList
     * @return
     */
    int batchSaveStockDeviceVersion(@Param("stockDeviceVersionList") List<StockDeviceVersion> stockDeviceVersionList);
}