package com.moredian.magicube.device.xxljob;


import com.moredian.bee.job.annotation.BeeXxlJob;
import com.moredian.magicube.device.dto.snapshot.DeviceSnapshotDTO;
import com.moredian.magicube.device.manager.DeviceSnapshotManager;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 删除设备快照调度
 * @Date 2024/1/4
 */
@Slf4j
@Component
public class DeviceSnapshotJob {

    @Autowired
    private DeviceSnapshotManager deviceSnapshotManager;

    /**
     * 快照保留天数
     */
    @Value("${fishnet.device.snapshot.keepDay:15}")
    private Integer keepDay;

    @BeeXxlJob(value = "deleteDeviceSnapshot", name = "删除设备快照调度")
    public ReturnT<String> deleteDeviceSnapshot(String param) throws Exception {
        Integer day = keepDay;
        if (StringUtils.isNotBlank(param)) {
            day = Integer.valueOf(param);
        }
        List<DeviceSnapshotDTO> deviceSnapshotDTOList = deviceSnapshotManager.queryOvertimeDeviceSnapshot(day);
        if (CollectionUtils.isNotEmpty(deviceSnapshotDTOList)) {
            for (DeviceSnapshotDTO deviceSnapshotDTO : deviceSnapshotDTOList) {
                deviceSnapshotManager.deleteById(deviceSnapshotDTO.getId());
                log.info("删除设备快照调度执行成功:快照id[{}],设备id[{}],设备SN[{}]", deviceSnapshotDTO.getId(),
                        deviceSnapshotDTO.getDeviceId(), deviceSnapshotDTO.getDeviceSn());
            }
        }
        return ReturnT.SUCCESS;
    }
}
