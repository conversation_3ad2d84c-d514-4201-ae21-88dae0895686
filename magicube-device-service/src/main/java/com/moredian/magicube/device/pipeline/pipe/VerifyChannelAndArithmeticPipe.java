package com.moredian.magicube.device.pipeline.pipe;


import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.ActivityRecCoreType;
import com.moredian.magicube.common.enums.VerifyChannel;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理识别通道和算法管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VerifyChannelAndArithmeticPipe extends
    AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private OrgService orgService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        int oldVerifyChannel = orgService.getVerifyChannel(context.getOrgId())
            .pickDataThrowException();
        if (VerifyChannel.NO_VERIFY.getValue() == oldVerifyChannel) {
            Integer verifyChannel = recCoreTypeToVerifyChannel(dto.getRegCoreType());
            orgService.markVerifyChannelAndArithmetic(context.getOrgId(), verifyChannel,
                dto.getRegArithmetic()).pickDataThrowException();
            context.setVerifyChannel(verifyChannel);
        } else {
            context.setVerifyChannel(oldVerifyChannel);
        }
    }

    public Integer recCoreTypeToVerifyChannel(Integer recCoreType) {
        if (ActivityRecCoreType.HUIYAN_THIRDPAD.equals(recCoreType)
            || ActivityRecCoreType.HUIEYE_SELF.equals(recCoreType)) {
            return VerifyChannel.HUIEYE.getValue();
        } else if (ActivityRecCoreType.THIRDPAD_THIRDPAD.equals(recCoreType)) {
            return VerifyChannel.THIRDPART.getValue();
        } else {
            return VerifyChannel.CLOUDEYE.getValue();
        }
    }
}
