package com.moredian.magicube.device.third.lock.res;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description 通通锁token返回参数
 * @create 2025-03-11 16:48
 */
@Data
public class AccessTokenResp {

    /**
     * 访问令牌
     */
    private String access_token;

    /**
     * 用户主键id
     */
    private Long uid;

    /**
     * 刷新令牌
     */
    private String refresh_token;

    /**
     * 访问令牌过期时间，默认90天，单位秒
     */
    private Long expires_in;
}
