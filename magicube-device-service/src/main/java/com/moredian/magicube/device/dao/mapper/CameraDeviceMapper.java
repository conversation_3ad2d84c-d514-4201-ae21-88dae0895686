package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.dahua.CameraDeviceInfo;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.dto.dahua.QueryCameraDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 大华摄像头设备相关接口
 *
 * <AUTHOR>
 */

@Mapper
public interface CameraDeviceMapper {

    /**
     * 查询摄像头列表
     *
     * @return
     */
    List<CameraDeviceInfo> list();

    /**
     * 根据ip和端口号查询摄像头信息
     *
     * @param ip   设备ip
     * @param port 设备端口
     * @return
     */
    CameraDeviceInfo getByIpAndPort(@Param("ip") String ip, @Param("port") int port);

    List<CameraDeviceInfo> listByCondition(@Param("dto") QueryCameraDTO dto);

    Boolean updateByDeviceSn(@Param("deviceSn") String deviceSn, @Param("dto") CameraDeviceInfoDTO dto);
}
