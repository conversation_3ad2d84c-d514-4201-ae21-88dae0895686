package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceOnlineState;
import com.moredian.magicube.device.dto.device.DeviceOnlineStateDTO;
import com.moredian.magicube.device.manager.DeviceOnlineStateManager;
import com.moredian.magicube.device.service.DeviceOnlineService;
import com.moredian.ota.api.msg.DeviceOnlineStateMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SI
@Slf4j
public class DeviceOnlineServiceImpl implements DeviceOnlineService {


    @Autowired
    private DeviceOnlineStateManager deviceOnlineStateManager;
    @Override
    public ServiceResponse<List<DeviceOnlineStateDTO>> selectByOrgId(Long orgId,List<Long> deviceIdList) {
        ServiceResponse<List<DeviceOnlineStateDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceOnlineState> list = deviceOnlineStateManager.selectByOrgIdAndDeviceId(orgId, deviceIdList);
        if (CollectionUtils.isEmpty(list)) {
            return serviceResponse;
        }
        List<DeviceOnlineStateDTO> resList = new ArrayList<>();
        list.forEach(n->{
            DeviceOnlineStateDTO stateDTO = new DeviceOnlineStateDTO();
            BeanUtils.copyProperties(n, stateDTO);
            resList.add(stateDTO);
        });
        serviceResponse.setData(resList);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceOnlineStateDTO>> selectByDeviceSnList(List<String> deviceSnList) {
        ServiceResponse<List<DeviceOnlineStateDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceSnList)) {
            return serviceResponse;
        }
        List<DeviceOnlineState> list = deviceOnlineStateManager.selectByDeviceSnList(deviceSnList);
        if (CollectionUtils.isEmpty(list)) {
            return serviceResponse;
        }
        List<DeviceOnlineStateDTO> resList = new ArrayList<>();
        for (DeviceOnlineState onlineState : list) {
            DeviceOnlineStateDTO stateDTO = new DeviceOnlineStateDTO();
            BeanUtils.copyProperties(onlineState, stateDTO);
            resList.add(stateDTO);
        }
        serviceResponse.setData(resList);
        return serviceResponse;
    }

    /**
     * 插入设备在离线信息<为支持塔上调用，暴露出去>
     *
     * @param msg
     * @return
     */
    @Override
    public ServiceResponse<Boolean> insertDeviceOnlineStateMsg(DeviceOnlineStateMsg msg) {
        deviceOnlineStateManager.insert(msg);
        return new ServiceResponse<>(true);
    }
}
