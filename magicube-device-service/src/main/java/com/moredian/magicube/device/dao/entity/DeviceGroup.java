package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备和组的关系
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_group")
public class DeviceGroup extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 关系Id
     */
    private Long deviceGroupId;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 权限组Id
     */
    private Long groupId;

    /**
     * 权限组码
     */
    private String groupCode;
}
