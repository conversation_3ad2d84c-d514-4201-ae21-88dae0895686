package com.moredian.magicube.device.manager;


import com.moredian.magicube.device.dao.entity.DeviceAccount;

import java.util.List;


/**
 * @Author: limh
 * @Date: 2020/4/10 11:26
 */
public interface DeviceAccountManager {


    DeviceAccount queryById(Long deviceAccountId);

    List<DeviceAccount> queryByCond(DeviceAccount deviceAccount);

    int insert(DeviceAccount deviceAccount);

    int update(DeviceAccount deviceAccount);

    int deleteById(Long deviceAccountId);

    int deleteById(DeviceAccount deviceAccount);

    int insertBatch(Long accountId, List<String> snList);

}
