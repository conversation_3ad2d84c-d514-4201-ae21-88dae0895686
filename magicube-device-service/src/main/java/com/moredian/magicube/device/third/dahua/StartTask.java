package com.moredian.magicube.device.third.dahua;

import java.util.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class StartTask implements CommandLineRunner {

    @Autowired
    private ServiceTask ServiceTask;

    @Override
    public void run(String... args) {
        Timer timer = new Timer();
        //服务启动后间隔1分钟执行一次，以后每分钟会执行一次
//        timer.scheduleAtFixedRate(ServiceTask, 10 * 1000, 60 * 1000);
    }
}
