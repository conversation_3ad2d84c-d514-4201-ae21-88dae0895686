<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceSubjectMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceSubject">
    <id column="id" property="id"/>
    <result column="org_id" property="orgId"/>
    <result column="name" property="name"/>
    <result column="img_urls" property="imgUrls"/>
    <result column="company_name" property="companyName"/>
    <result column="logo_img_url" property="logoImgUrl"/>
    <result column="type" property="type"/>
    <result column="template_type" property="templateType"/>
    <result column="enable" property="enable"/>
    <result column="expend" property="expend"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <sql id="sql_table">
        hive_device_subject
    </sql>

  <sql id="sql_columns">
        id,
        org_id,
        name,
        img_urls,
        company_name,
        logo_img_url,
        type,
        template_type,
        enable,
        expend,
        gmt_create,
        gmt_modify
	</sql>

  <sql id="sql_values">
        #{id},
        #{orgId},
        #{name},
        #{imgUrls},
        #{companyName},
        #{logoImgUrl},
        #{type},
        #{templateType},
        #{enable},
        #{expend},
        now(3),
        now(3)
    </sql>

  <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceSubject">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>

  <insert id="batchInsert" parameterType="list">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    <foreach collection="relations" item="item" separator=",">
      (
      #{item.id},
      #{item.orgId},
      #{item.name},
      #{item.imgUrls},
      #{item.companyName},
      #{item.logoImgUrl},
      #{item.type},
      #{item.templateType},
      #{item.enable},
      #{expend},
      now(3),
      now(3)
      )
    </foreach>
    on duplicate key update id = values(id) , gmt_modify=values(gmt_modify)
  </insert>

  <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceSubject">
    update
    <include refid="sql_table"/>
    <set>
      <if test="name != null">
        name = #{name},
      </if>
      <if test="imgUrls != null">
        img_urls = #{imgUrls},
      </if>
      <if test="companyName != null">
        company_name = #{companyName},
      </if>
      <if test="logoImgUrl != null">
        logo_img_url = #{logoImgUrl},
      </if>
      <if test="type != null">
        type = #{type},
      </if>

      <if test="enable != null">
        enable = #{enable},
      </if>
      <if test="templateType != null">
        template_type = #{templateType},
      </if>
      <if test="expend != null">
        expend = #{expend},
      </if>
      gmt_modify = now(3)
    </set>
    where id = #{id}
    and org_id = #{orgId}
  </update>

  <select id="getById" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and id = #{id}
  </select>

  <select id="getByOrgIdAndNameAndType" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and name = #{name}
    and type = #{type}
  </select>

  <select id="countByOrgIdAndType" parameterType="map" resultType="int">
    select count(*)
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    and type = #{type}
  </select>

  <select id="listByOrgIdAndIds" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="ids != null and ids.size() > 0">
      and id in
      <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="listByCondition" parameterType="com.moredian.magicube.device.dao.entity.DeviceSubject"
    resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="name != null">
      and name = #{name}
    </if>
    <if test="companyName != null">
      and company_name = #{companyName}
    </if>
    <if test="type != null">
      and type = #{type}
    </if>
    order by gmt_create desc
  </select>

  <select id="listByPage"  resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where org_id = #{deviceSubject.orgId}
    <if test="deviceSubject.type != null">
      and type = #{deviceSubject.type}
    </if>
    <if test="deviceSubject.enable != null">
      and enable = #{deviceSubject.enable}
    </if>
    <if test="deviceSubject.type != null">
      and type = #{deviceSubject.type}
    </if>
    <if test="deviceSubject.name != null and deviceSubject.name != ''">
      and name LIKE CONCAT('%', #{deviceSubject.name}, '%')
    </if>
    <if test="subjectIdList != null and subjectIdList.size() > 0">
      and id in
      <foreach collection="subjectIdList" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    order by gmt_create desc
    limit #{pageSize} , #{pageNo}
  </select>


  <select id="countAll"  parameterType="com.moredian.magicube.device.dao.entity.DeviceSubject" resultType="int">
    select
    count(*)
    from
    <include refid="sql_table"/>
    where org_id = #{deviceSubject.orgId}
    <if test="deviceSubject.type != null">
      and type = #{deviceSubject.type}
    </if>
    <if test="deviceSubject.enable != null">
      and enable = #{deviceSubject.enable}
    </if>
    <if test="deviceSubject.type != null">
      and type = #{deviceSubject.type}
    </if>
    <if test="deviceSubject.name != null and deviceSubject.name != ''">
      and name LIKE CONCAT('%', #{deviceSubject.name}, '%')
    </if>
    <if test="subjectIdList != null and subjectIdList.size() > 0">
      and id in
      <foreach collection="subjectIdList" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    order by gmt_create desc
  </select>


  <delete id="deleteByOrgIdAndIds" parameterType="long">
    delete from
    <include refid="sql_table"/>
    where org_id = #{orgId}
    <if test="ids != null and ids.size() > 0">
      and id in
      <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </delete>
</mapper>
