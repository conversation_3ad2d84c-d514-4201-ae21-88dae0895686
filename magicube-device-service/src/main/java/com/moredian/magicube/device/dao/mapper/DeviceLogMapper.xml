<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceLogMapper">
    <resultMap id="deviceLogResultMap" type="com.moredian.magicube.device.dao.entity.DeviceLog">
        <result column="device_log_id" property="deviceLogId"/>
        <result column="org_id" property="orgId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="device_type" property="deviceType"/>
        <result column="event_type" property="envenType"/>
        <result column="event_desc" property="envenDesc"/>
        <result column="oper_time" property="operTime"/>
        <result column="member_id" property="memberId"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_device_log
    </sql>

    <sql id="sql_columns">
        device_log_id,
        org_id,
        device_id,
        device_sn,
        device_type,
        event_type,
        event_desc,
        oper_time,
        member_id,
        gmt_create,
        gmt_modify
	</sql>

    <sql id="sql_values">
        #{deviceLogId},
        #{orgId},
        #{deviceId},
        #{deviceSn},
        #{deviceType},
        #{envenType},
        #{envenDesc},
        #{operTime},
        #{memberId},
        now(3),
        now(3)
    </sql>

    <sql id="condition_sql_where">
        from hive_device_log
        <where>
            <if test="deviceLogId != null">
                and device_log_id = #{deviceLogId}
            </if>
            <if test="orgId != null">
                and org_id = #{orgId}
            </if>
            <if test="deviceSn != null">
                and device_sn like CONCAT('%',#{deviceSn},'%')
            </if>
            <if test="deviceId != null">
                and device_id = #{deviceId}
            </if>
            <if test="deviceType != null">
                and device_type = #{deviceType}
            </if>
            <if test="memberId != null">
                and member_id = #{memberId}
            </if>
        </where>
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceLog">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceLog">
        UPDATE
        <include refid="sql_table"/>
        set member_id = #{memberId},
        gmt_modify = now(3)
        where device_log_id = #{deviceLogId}
    </update>

    <select id="getByOrgIdAndId" resultMap="deviceLogResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_log_id = #{deviceLogId}
    </select>

    <select id="listByOrgIdAndDeviceId" resultMap="deviceLogResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_id = #{deviceId}
    </select>

    <select id="getLatestByOrgIdAndDeviceSnAndType" resultMap="deviceLogResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_sn = #{deviceSn}
        and event_type = #{eventType}
        order by oper_time desc
        limit 1
    </select>

    <select id="listByOrgIdAndDeviceSn" resultMap="deviceLogResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_sn = #{deviceSn}
    </select>

    <select id="listDistinctDeviceTypeByOrgId" resultType="java.lang.Integer">
        select distinct(device_type)
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
    </select>

    <select id="countByOrgId" resultType="java.lang.Integer">
        select count(org_id)
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
    </select>

    <select id="listByDeviceSn" resultMap="deviceLogResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where device_sn = #{deviceSn} order by gmt_create desc
    </select>
</mapper>