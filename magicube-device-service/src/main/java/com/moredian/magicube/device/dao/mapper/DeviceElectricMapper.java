package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceElectric;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备电量信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Mapper
public interface DeviceElectricMapper {

    /**
     * 根据deviceId查询记录
     *
     * @param deviceId
     * @return
     */
    DeviceElectric selectOne(@Param("deviceId") Long deviceId);

    /**
     * 根据deviceSn查询记录
     *
     * @param deviceSn
     * @return
     */
    DeviceElectric selectOneByDeviceSn(@Param("deviceSn") String deviceSn);

    /**
     * 批量查询
     *
     * @param deviceIdList
     * @return
     */
    List<DeviceElectric> selectList(@Param("deviceIdList") List<Long> deviceIdList);

    /**
     * 新增设备电量记录
     *
     * @param deviceElectric
     * @return
     */
    int insert(DeviceElectric deviceElectric);

    /**
     * 修改设备电量
     *
     * @param deviceElectric
     * @return
     */
    int updateById(DeviceElectric deviceElectric);

}
