package com.moredian.magicube.device.jetlinks.ruleArrange;

import lombok.experimental.Accessors;

import lombok.Data;

import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version $Id: Flow.java, v 1.0 Exp $
 */
@Data
@Accessors(chain = true)
public class Flow {

    private StringJoiner chain = new StringJoiner(",");

    public void addNode(String tabNode) {
        chain.add(tabNode);
    }


    @Override
    public String toString(){
        return chain.toString();
    }

}
