package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dto.accessory.AccessoryInfoDTO;
import com.moredian.magicube.device.dto.accessory.AccessoryQueryDTO;
import com.moredian.magicube.device.dto.accessory.BindAccessoryDTO;
import com.moredian.magicube.device.dto.accessory.UnbindAccessoryDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/8
 */
public interface AccessoryManager {

    /**
     * 配件(外设)绑定
     *
     * @param dto
     * @return
     */
    Boolean bindAccessory(BindAccessoryDTO dto);

    /**
     * 配件(外设)解绑
     *
     * @param dto
     * @return
     */
    Boolean unbindAccessory(UnbindAccessoryDTO dto);

    /**
     * 列表 - 配件(外设)条件查询
     *
     * @param dto
     * @return
     */
    List<AccessoryInfoDTO> listAccessoryInfo(AccessoryQueryDTO dto);


    /**
     * 根据设备sn解绑外设信息
     *
     * @param orgId    机构ID
     * @param deviceSn 设备sn
     * @return
     */
    Boolean unbindAccessoryByOrgIdAndDeviceSn(Long orgId, String deviceSn);

}
