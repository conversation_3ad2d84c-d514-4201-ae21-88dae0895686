package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * appType跳转微应用链接
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_app_type_redirect")
public class AppTypeRedirect extends TimedEntity {

    private static final long serialVersionUID = -7047336653292490474L;

    /**
     * 主键
     */
    private Long id;

    /**
     * app类型
     */
    private Integer appType;

    /**
     * 跳转链接
     */
    private String redirectUrl;

    /**
     * 设备来源
     */
    private String env;
}
