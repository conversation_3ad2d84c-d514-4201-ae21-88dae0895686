package com.moredian.magicube.device.third.lock.req;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 通用请求参数
 * @create 2025-03-11 15:25
 */

@Data
public class CommonReq {

    private String clientId;

    private String clientSecret;

    private String accessToken;

    private String username;

    private Integer lockId;

    /**
     * 需要使用MD5，32小写加密
     */
    private String password;

    private Long date = new Date().getTime();
}
