package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 外设白名单表
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_peripherals_white_list")
public class PeripheralsWhiteList extends TimedEntity {

    private static final long serialVersionUID = -3700357347829430245L;

    /**
     * 外设白名单Id
     */
    private Long peripheralsWhiteListId;

    /**
     * 外设sn
     */
    private String peripheralsSn;

    /**
     * 外设的设备类型
     */
    private Integer peripheralsType;

    /**
     * 状态：0-断开 1-连接
     */
    private Integer status;
}
