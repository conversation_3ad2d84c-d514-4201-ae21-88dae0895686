package com.moredian.magicube.device.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.xmlunit.builder.DiffBuilder;
import org.xmlunit.diff.ComparisonResult;
import org.xmlunit.diff.DefaultNodeMatcher;
import org.xmlunit.diff.Diff;
import org.xmlunit.diff.Difference;
import org.xmlunit.diff.ElementSelectors;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Slf4j
public class XMLTools {

    public static boolean similarXml(String xml1, String xml2) {
        if (StringUtils.isBlank(xml1) && StringUtils.isBlank(xml2)) {
            return Boolean.TRUE;
        }
        if (StringUtils.isNotBlank(xml1) && StringUtils.isBlank(xml2)) {
            return Boolean.FALSE;
        }
        if (StringUtils.isBlank(xml1) && StringUtils.isNotBlank(xml2)) {
            return Boolean.FALSE;
        }
        // 创建Diff对象
        Diff diff = DiffBuilder.compare(xml1)
                               .withTest(xml2)
                               .ignoreWhitespace()
                               .ignoreComments()
                               .withNodeMatcher(new DefaultNodeMatcher(ElementSelectors.byNameAndAllAttributes))
                               .build();

        // 检查是否有不同
        boolean similar = Boolean.TRUE;
        if (diff.hasDifferences()) {
            for (Difference difference : diff.getDifferences()) {
                if (ComparisonResult.DIFFERENT.equals(difference.getResult())) {
                    similar = Boolean.FALSE;
                    log.info("XML are first-different: {}", difference);
                    break;
                }
            }
        }
        if (similar) {
            log.info("XML are similar.");
        }
        return similar;
    }

    /*public static void main(String[] args) {
        List<String> lines = FileUtil.readLines("E:\\test-run\\bigXml.txt", CharsetUtil.CHARSET_UTF_8);
        StringBuilder sb = new StringBuilder();
        for (String line : lines) {
            sb.append(line).append("\n");
        }
        String xmlStr1 = sb.toString();

        long begin = System.currentTimeMillis();
        String md5Result = MD5(xmlStr1);
        System.out.println("take:" + (System.currentTimeMillis() - begin) + "ms");
        System.out.println(md5Result);

        String xmlStr2 = sb.toString();
        String md5Result2 = MD5(xmlStr2);
        long begin1 = System.currentTimeMillis();
        boolean equals1 = md5Result.equals(md5Result2);
        log.info("xmlStr1.length={}, xmlStr2.length={}, cost: {}ms, MD-result={}", xmlStr1.length(), xmlStr2.length(), System.currentTimeMillis() - begin1, equals1);

        *//*long start = System.currentTimeMillis();
        boolean similarXml = similarXml(xmlStr1, xmlStr2);
        log.info("xmlStr1.length={}, xmlStr2.length={}, cost: {}ms", xmlStr1.length(), xmlStr2.length(), System.currentTimeMillis() - start);
        System.out.println(similarXml);*//*

        long start1 = System.currentTimeMillis();
        boolean equals = xmlStr1.equals(xmlStr2);
        log.info("xmlStr1.length={}, xmlStr2.length={}, cost: {}ms, result={}", xmlStr1.length(), xmlStr2.length(), System.currentTimeMillis() - start1, equals);
    }*/
}
