package com.moredian.magicube.device.manager;


import com.moredian.magicube.device.dao.entity.NoticeRecord;
import com.moredian.magicube.device.model.StatisticsModel;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
public interface NoticeRecordManager {

    /**
     * 新增工作通知记录
     *
     * @param noticeRecord
     */
    Long addRecord(NoticeRecord noticeRecord);

    /**
     * 统计工作通知记录数
     *
     * @param statisticsModel
     * @return
     */
    int countRecords(StatisticsModel statisticsModel);

    /**
     * 查询最近一次离线时间
     *
     * @param orgId
     * @param deviceSn
     * @param noticeEvent
     * @return
     */
    Long getLastOfflineTime(Long orgId, String deviceSn, Integer noticeEvent);

}
