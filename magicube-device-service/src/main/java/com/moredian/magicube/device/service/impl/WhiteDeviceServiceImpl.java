package com.moredian.magicube.device.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.constant.ImportErrorConstants;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.device.DeviceAuthDetailDTO;
import com.moredian.magicube.device.dto.white.BatchInsertWhiteListDeviceRequest;
import com.moredian.magicube.device.dto.white.GetWhiteDeviceListRequest;
import com.moredian.magicube.device.dto.white.ImportWhiteDeviceErrorDTO;
import com.moredian.magicube.device.dto.white.InsertWhiteDeviceDTO;
import com.moredian.magicube.device.dto.white.QueryWhiteDeviceDTO;
import com.moredian.magicube.device.dto.white.SimpleWhiteDeviceResponse;
import com.moredian.magicube.device.dto.white.UpdateWhiteDeviceDTO;
import com.moredian.magicube.device.dto.white.WhiteDeviceDTO;
import com.moredian.magicube.device.enums.DeviceSourceEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceTypeMapManager;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import com.moredian.magicube.device.service.WhiteDeviceService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class WhiteDeviceServiceImpl implements WhiteDeviceService {

    @Autowired
    private WhiteDeviceManager whiteDeviceManager;

    @Autowired
    private RedissonLockComponent redissonLockComponent;

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Value("${batch.size:100}")
    private Integer BATCH_UPDATE_SIZE;

    private final static Integer IMPORT_WHITE_DEVICE_MAX = 2000;

    private final static String DEVICE_SN_PATTERN = "^[0-9]{12}[A-Z]{2}[0-9]{4}$";

    private final static String MAC_PATTERN = "^[A-Za-z0-9]{12}$";

    private final static String PRIVATE_KEY_PATTERN = "^[A-Za-z0-9]{32}$";

    @Override
    public ServiceResponse<Boolean> insert(InsertWhiteDeviceDTO dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        InventoryDevice inventoryDevice = new InventoryDevice();
        BeanUtils.copyProperties(dto, inventoryDevice);
        Boolean result = whiteDeviceManager.insert(inventoryDevice);
        serviceResponse.setData(result);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<ImportWhiteDeviceErrorDTO>> batchInsert(
        List<InsertWhiteDeviceDTO> list) {
        ServiceResponse<List<ImportWhiteDeviceErrorDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        if (CollectionUtils.isNotEmpty(list) && list.size() > IMPORT_WHITE_DEVICE_MAX) {
            BizAssert.notNull(null, DeviceErrorCode.IMPORT_LIMIT,
                DeviceErrorCode.IMPORT_LIMIT.getMessage());
        }
        List<String> deviceSns = new ArrayList<>();
        for (InsertWhiteDeviceDTO insertWhiteDeviceDTO : list) {
            deviceSns.add(insertWhiteDeviceDTO.getSerialNumber());
        }
        List<InventoryDevice> inventories = whiteDeviceManager.listByDeviceSns(deviceSns);
        List<String> existDeviceSns = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventories)) {
            existDeviceSns = inventories.stream().map(
                com.moredian.magicube.device.dao.entity.InventoryDevice::getSerialNumber)
                .collect(Collectors.toList());
        }
        List<ImportWhiteDeviceErrorDTO> errors = new ArrayList<>();
        serviceResponse.setData(errors);
        //1.校验参数
        validParameter(list, errors, deviceSns, existDeviceSns);
        if (CollectionUtils.isEmpty(errors)) {
            //2.执行导入设备白名单
            doImportWhiteDevice(list);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> batchInsertWithoutCheck(
        BatchInsertWhiteListDeviceRequest request) {
        return new ServiceResponse<>(whiteDeviceManager.batchInsertWithoutCheck(request));
    }

    @Override
    public ServiceResponse<Pagination<WhiteDeviceDTO>> listPage(QueryWhiteDeviceDTO dto) {
        ServiceResponse<Pagination<WhiteDeviceDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        Pagination<InventoryDevice> pa = whiteDeviceManager.listPage(dto);
        Pagination<WhiteDeviceDTO> page = new Pagination<>();
        if (CollectionUtils.isNotEmpty(pa.getData())) {
            List<WhiteDeviceDTO> whiteDeviceDTOs = new ArrayList<>();
            for (InventoryDevice inventoryDevice : pa.getData()) {
                WhiteDeviceDTO whiteDeviceDTO = new WhiteDeviceDTO();
                BeanUtils.copyProperties(inventoryDevice, whiteDeviceDTO);
                whiteDeviceDTO.setGmtCreate(inventoryDevice.getCreatedAt());
                whiteDeviceDTO.setGmtModify(inventoryDevice.getUpdatedAt());
                whiteDeviceDTO.setOaBase(inventoryDevice.getDeviceActivateOaBase());
                whiteDeviceDTO.setSdkBase(inventoryDevice.getDeviceActivateSdkBase());
                whiteDeviceDTOs.add(whiteDeviceDTO);
            }
            page.setTotalCount(pa.getTotalCount());
            page.setData(whiteDeviceDTOs);
        }
        page.setPageNo(pa.getPageNo());
        page.setPageSize(pa.getPageSize());
        serviceResponse.setData(page);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<WhiteDeviceDTO> getByDeviceSn(String deviceSn) {
        ServiceResponse<WhiteDeviceDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(deviceSn);
        if (inventoryDevice != null) {
            WhiteDeviceDTO whiteDeviceDTO = new WhiteDeviceDTO();
            BeanUtils.copyProperties(inventoryDevice, whiteDeviceDTO);
            whiteDeviceDTO.setGmtCreate(inventoryDevice.getCreatedAt());
            whiteDeviceDTO.setGmtModify(inventoryDevice.getUpdatedAt());
            whiteDeviceDTO.setOaBase(inventoryDevice.getDeviceActivateOaBase());
            whiteDeviceDTO.setSdkBase(inventoryDevice.getDeviceActivateSdkBase());
            serviceResponse.setData(whiteDeviceDTO);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<WhiteDeviceDTO>> listByDeviceSns(List<String> deviceSns) {
        List<WhiteDeviceDTO> whiteDeviceDTOs = Lists.newArrayList();
        List<InventoryDevice> inventoryDevices = whiteDeviceManager.listByDeviceSns(deviceSns);
        if (CollectionUtils.isNotEmpty(inventoryDevices)) {
            for (InventoryDevice inventoryDevice : inventoryDevices) {
                WhiteDeviceDTO whiteDeviceDTO = new WhiteDeviceDTO();
                BeanUtils.copyProperties(inventoryDevice, whiteDeviceDTO);
                whiteDeviceDTO.setGmtCreate(inventoryDevice.getCreatedAt());
                whiteDeviceDTO.setGmtModify(inventoryDevice.getUpdatedAt());
                whiteDeviceDTOs.add(whiteDeviceDTO);
            }
        }
        return new ServiceResponse<>(whiteDeviceDTOs);
    }

    @Override
    public ServiceResponse<Boolean> delete(String deviceSn) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        Boolean result = whiteDeviceManager.delete(deviceSn);
        serviceResponse.setData(result);
        return serviceResponse;
    }

    @Transactional
    @Override
    public ServiceResponse<Boolean> batchUpdate(List<UpdateWhiteDeviceDTO> dto) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        // 批量分段更新数据
        List<List<UpdateWhiteDeviceDTO>> partition = ListUtil.partition(dto, BATCH_UPDATE_SIZE);
        for (List<UpdateWhiteDeviceDTO> updateWhiteDeviceDTOS : partition) {
            whiteDeviceManager.batchUpdate(updateWhiteDeviceDTOS);
        }
        serviceResponse.setData(Boolean.TRUE);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<ImportWhiteDeviceErrorDTO>> batchCheckWhiteListDevice(List<InsertWhiteDeviceDTO> list) {
        ServiceResponse<List<ImportWhiteDeviceErrorDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        if (CollectionUtils.isNotEmpty(list) && list.size() > IMPORT_WHITE_DEVICE_MAX) {
            BizAssert.notNull(null, DeviceErrorCode.IMPORT_LIMIT,
                DeviceErrorCode.IMPORT_LIMIT.getMessage());
        }
        List<String> deviceSns = new ArrayList<>();
        for (InsertWhiteDeviceDTO insertWhiteDeviceDTO : list) {
            deviceSns.add(insertWhiteDeviceDTO.getSerialNumber());
        }
        List<InventoryDevice> inventories = whiteDeviceManager.listByDeviceSns(deviceSns);
        List<String> existDeviceSns = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inventories)) {
            existDeviceSns = inventories.stream().map(
                com.moredian.magicube.device.dao.entity.InventoryDevice::getSerialNumber)
                .collect(Collectors.toList());
        }
        List<ImportWhiteDeviceErrorDTO> errors = new ArrayList<>();
        serviceResponse.setData(errors);
        //1.校验参数
        validParameter(list, errors, deviceSns, existDeviceSns);

        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> batchInsertWithoutCheck(List<InsertWhiteDeviceDTO> list) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(list), "批量新增列表不能为空");
        //2.执行导入设备白名单
        try {
            if (redissonLockComponent
                    .acquire(RedisKeys.getKey(RedisKeys.BATCH_INSERT_WHITE_LIST_DEVICE), null,
                            RedisKeys.RELEASE_TIME)) {
                // 获取SN前两位与设备类型映射
                List<String> deviceSnList = list.stream().map(InsertWhiteDeviceDTO::getSerialNumber).distinct().collect(Collectors.toList());
                Map<String/*deviceSn*/, Integer/*deviceType*/> typeByDeviceSnMap = deviceTypeMapManager.getTypeByDeviceSnList(deviceSnList);
                List<InventoryDevice> inventoryDevices = Lists.newArrayList();
                for (InsertWhiteDeviceDTO insertWhiteDeviceDTO : list) {
                    InventoryDevice inventoryDevice = new InventoryDevice();
                    BeanUtils.copyProperties(insertWhiteDeviceDTO, inventoryDevice);
                    if (inventoryDevice.getDeviceType() == null) {
                        //inventoryDevice.setDeviceType(DeviceType.getTypeByDeviceSn(insertWhiteDeviceDTO.getSerialNumber()));
                        inventoryDevice.setDeviceType(typeByDeviceSnMap.get(insertWhiteDeviceDTO.getSerialNumber()));
                    }
                    inventoryDevice.setDeviceSource(DeviceSourceEnum.MD.getCode());
                    inventoryDevices.add(inventoryDevice);
                }
                whiteDeviceManager.batchInsertWithoutCheck(inventoryDevices);
            }
        } catch (Exception e) {
            log.error("执行批量新增设备白名单失败（无校验）:{}", e.getMessage());
        } finally {
            redissonLockComponent
                    .release(RedisKeys.getKey(RedisKeys.BATCH_INSERT_WHITE_LIST_DEVICE));
        }
        return new ServiceResponse<>(Boolean.TRUE);
    }

    @Override
    public ServiceResponse<List<SimpleWhiteDeviceResponse>> getBatchSimpleDeviceInfo(
        GetWhiteDeviceListRequest request) {
        return new ServiceResponse<>(whiteDeviceManager.getBatchSimpleDeviceInfo(request));
    }

    @Override
    public ServiceResponse<DeviceAuthDetailDTO> getSdkBaseBySN(String deviceSn) {
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(deviceSn);
        BizAssert.notNull(inventoryDevice, "设备白单信息不存在");

        ServiceResponse<DeviceAuthDetailDTO> successResponse = ServiceResponse.createSuccessResponse();
        DeviceAuthDetailDTO sdkBaseDTO = DeviceAuthDetailDTO.builder()
            .oaBase(inventoryDevice.getDeviceActivateOaBase())
            .sdkBase(inventoryDevice.getDeviceActivateSdkBase())
            .build();
        successResponse.setData(sdkBaseDTO);
        return successResponse;

    }

    @Override
    public ServiceResponse<Boolean> updateByDeviceSn(UpdateWhiteDeviceDTO dto) {
        String serialNumber = dto.getSerialNumber();
        BizAssert.notBlank(serialNumber, "deviceSn must not be null");
        InventoryDevice inventoryDevice = new InventoryDevice();
        BeanUtils.copyProperties(dto, inventoryDevice);
        whiteDeviceManager.update(inventoryDevice);
        return new ServiceResponse<>(Boolean.TRUE);
    }

    /**
     * 导入设备白名单参数校验
     *
     * @param list           导入参数
     * @param errors         错误信息列表
     * @param deviceSns      所以设备sn列表
     * @param existDeviceSns 系统已存在的设备sn列表
     * @return
     */
    private void validParameter(List<InsertWhiteDeviceDTO> list,
        List<ImportWhiteDeviceErrorDTO> errors, List<String> deviceSns,
        List<String> existDeviceSns) {
        String deviceSn;
        for (int i = 0; i < list.size(); i++) {
            InsertWhiteDeviceDTO insertWhiteDeviceDTO = list.get(i);
            deviceSn = insertWhiteDeviceDTO.getSerialNumber();
            List<String> distinctDeviceSns = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(deviceSns)) {
                distinctDeviceSns.addAll(deviceSns);
            }
            //先判断设备sn是否为空,然后再判断设备sn格式,,最后判断设备白名单是否已经存在
            if (StringUtils.isBlank(deviceSn)) {
                ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                    i + 1, ImportErrorConstants.DEVICE_SN_NOT_EXIST, insertWhiteDeviceDTO);
                errors.add(importWhiteDeviceErrorDTO);
                continue;
            } else {
                distinctDeviceSns.remove(deviceSn);
                if (distinctDeviceSns.contains(deviceSn)) {
                    ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                        i + 1, ImportErrorConstants.DEVICE_SN_REPETITION, insertWhiteDeviceDTO);
                    errors.add(importWhiteDeviceErrorDTO);
                    continue;
                }
                if (!Pattern.matches(DEVICE_SN_PATTERN, deviceSn)) {
                    ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                        i + 1, ImportErrorConstants.DEVICE_SN_FORMAT_ERROR, insertWhiteDeviceDTO);
                    errors.add(importWhiteDeviceErrorDTO);
                    continue;
                }
                if (existDeviceSns.contains(deviceSn)) {
                    ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                        i + 1, ImportErrorConstants.DEVICE_SN_EXIST, insertWhiteDeviceDTO);
                    errors.add(importWhiteDeviceErrorDTO);
                    continue;
                }
            }
            //先判断无线是否存在,再判断无线的格式是否正确
            if (StringUtils.isBlank(insertWhiteDeviceDTO.getMacAddress())) {
                ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                    i + 1, ImportErrorConstants.WIRELESS_MAC_NOT_EXIST, insertWhiteDeviceDTO);
                errors.add(importWhiteDeviceErrorDTO);
                continue;
            } else {
                if (!Pattern.matches(MAC_PATTERN, insertWhiteDeviceDTO.getMacAddress())) {
                    ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                        i + 1, ImportErrorConstants.WIRELESS_MAC_FORMAT_ERROR,
                        insertWhiteDeviceDTO);
                    errors.add(importWhiteDeviceErrorDTO);
                    continue;
                }
            }
            //判断有线的格式是否正确
            if (StringUtils.isNotBlank(insertWhiteDeviceDTO.getMacAddress2())) {
                if (!Pattern.matches(MAC_PATTERN, insertWhiteDeviceDTO.getMacAddress2())) {
                    ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                        i + 1, ImportErrorConstants.WIRED_MAC_FORMAT_ERROR, insertWhiteDeviceDTO);
                    errors.add(importWhiteDeviceErrorDTO);
                    continue;
                }
            }
            //先判断私钥是否存在,再判断私钥格式
            if (StringUtils.isBlank(insertWhiteDeviceDTO.getPrivateKey())) {
                ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                    i + 1, ImportErrorConstants.PRIVATE_KEY_NOT_EXIST, insertWhiteDeviceDTO);
                errors.add(importWhiteDeviceErrorDTO);
            } else {
                if (!Pattern.matches(PRIVATE_KEY_PATTERN, insertWhiteDeviceDTO.getPrivateKey())) {
                    ImportWhiteDeviceErrorDTO importWhiteDeviceErrorDTO = new ImportWhiteDeviceErrorDTO(
                        i + 1, ImportErrorConstants.PRIVATE_KEY_FORMAT_ERROR, insertWhiteDeviceDTO);
                    errors.add(importWhiteDeviceErrorDTO);
                }
            }
        }
    }

    /**
     * 执行导入设备白名单
     *
     * @param list 设备白名单参数
     */
    private void doImportWhiteDevice(List<InsertWhiteDeviceDTO> list) {
        try {
            if (redissonLockComponent
                .acquire(RedisKeys.getKey(RedisKeys.BATCH_INSERT_WHITE_LIST_DEVICE), null,
                    RedisKeys.RELEASE_TIME)) {
                // 获取SN前两位与设备类型映射
                List<String> deviceSnList = list.stream().map(InsertWhiteDeviceDTO::getSerialNumber).distinct().collect(Collectors.toList());
                Map<String/*deviceSn*/, Integer/*deviceType*/> typeByDeviceSnMap = deviceTypeMapManager.getTypeByDeviceSnList(deviceSnList);
                List<InventoryDevice> inventoryDevices = new ArrayList<>();
                for (InsertWhiteDeviceDTO insertWhiteDeviceDTO : list) {
                    InventoryDevice inventoryDevice = new InventoryDevice();
                    BeanUtils.copyProperties(insertWhiteDeviceDTO, inventoryDevice);
                    //inventoryDevice.setDeviceType(DeviceType.getTypeByDeviceSn(insertWhiteDeviceDTO.getSerialNumber()));
                    inventoryDevice.setDeviceType(typeByDeviceSnMap.get(insertWhiteDeviceDTO.getSerialNumber()));
                    inventoryDevices.add(inventoryDevice);
                }
                whiteDeviceManager.batchInsert(inventoryDevices);
            }
        } catch (Exception e) {
            log.error("执行批量新增设备白名单失败:{}", e.getMessage());
        } finally {
            redissonLockComponent
                .release(RedisKeys.getKey(RedisKeys.BATCH_INSERT_WHITE_LIST_DEVICE));
        }
    }
}
