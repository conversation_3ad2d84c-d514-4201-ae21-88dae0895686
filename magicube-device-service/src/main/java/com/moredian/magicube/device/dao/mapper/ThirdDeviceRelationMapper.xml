<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.ThirdDeviceRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.ThirdDeviceRelation">
        <id column="id" property="id" />
    <result column="gmt_create" property="gmtCreate" />
    <result column="gmt_modify" property="gmtModify" />
        <result column="tp_id" property="tpId" />
        <result column="tp_type" property="tpType" />
        <result column="device_sn" property="deviceSn" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tp_id, tp_type, device_sn,
        gmt_create,
        gmt_modify
    </sql>
    <select id="getByTpIdAndTpType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hive_third_device_relation
        where
        tp_id = #{tpId,jdbcType=VARCHAR}
        AND tp_type = #{tpType,jdbcType=NUMERIC}
    </select>

</mapper>
