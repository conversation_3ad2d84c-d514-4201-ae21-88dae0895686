package com.moredian.magicube.device.manager;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.version.ChangeDeviceAppVersionDTO;
import com.moredian.magicube.device.dto.version.ChangeLatchDeviceVersionDto;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceEnforceUpdateDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionInfoDTO;
import com.moredian.magicube.device.dto.version.QueryDeviceVersionDTO;
import com.moredian.magicube.device.dto.version.StockDeviceVersionDTO;
import java.util.List;

/**
 * 设备版本
 *
 * <AUTHOR>
 */
public interface DeviceVersionManager {

    /**
     * 更改设备app版本号
     *
     * @param dto 设备版本信息
     * @return
     */
    void changeDeviceAppVersion(ChangeDeviceAppVersionDTO dto);

    /**
     * 更改设备rom版本号
     *
     * @param dto 设备版本信息
     * @return
     */
    void changeDeviceRomVersion(ChangeDeviceAppVersionDTO dto);

    /**
     * 更新设备强制升级状态
     *
     * @param dto 设备强制升级信息
     * @return
     */
    void changeDeviceEnforceUpdateStatus(DeviceEnforceUpdateDTO dto);

    /**
     * 获取设备强制升级状态
     *
     * @param deviceId 设备Id
     * @return
     */
    DeviceEnforceUpdateDTO getDeviceEnforceUpdateStatus(Long deviceId);

    /**
     * 批量获取设备当前App版本
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<DeviceVersionDTO> listApkByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds);

    /**
     * 批量获取设备当前ROM版本
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<DeviceVersionDTO> listRomByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds);

    /**
     * 批量获取设备当前APP、ROM版本信息及设备最新可用APP、ROM版本信息
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<DeviceCurrVersionDTO> listByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds);

    /**
     * 批量从数据库中获取设备当前ROM版本
     *
     * @param deviceSnList
     * @return
     */
    ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceRomVersionFromDatabase(List<String> deviceSnList);

    /**
     * 批量从数据库中获取设备当前APK版本
     *
     * @param deviceSnList
     * @return
     */
    ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceApkVersionFromDatabase(List<String> deviceSnList);

    /**
     * 设备激活前上报rom版本信息接口
     *
     * @param dto
     * @return
     */
    ServiceResponse<Boolean> reportDeviceRomVersionBeforeActivation(ChangeDeviceAppVersionDTO dto);

    /**
     * 批量从数据库中获取设备当前ROM版本信息(从激活前和激活后中取最新的版本信息)
     *
     * @param deviceSnList
     * @return
     */
    ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceNewestRomVersionFromDatabase(List<String> deviceSnList);

    /**
     * 批量从数据库中获取库存设备版本信息
     *
     * @param deviceSnList
     * @return
     */
    ServiceResponse<List<StockDeviceVersionDTO>> getBatchStockDeviceVersionFromDatabase(List<String> deviceSnList);

    /**
     * 获取机构下比指定版本低的设备
     *
     * @param request
     * @return
     */
    ServiceResponse<List<DeviceVersionInfoDTO>> findDeviceVersionByOrgListAndVersion(
        QueryDeviceVersionDTO request);

    /**
     * 更改门锁设备版本号
     *
     * @param dto
     * @return
     */
    Boolean changeLatchDeviceVersion(ChangeLatchDeviceVersionDto dto);
}