package com.moredian.magicube.device.dao.entity.qrcode;

import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import java.util.Date;
import lombok.Data;

/**
 * @Classname： ActivateQrCode
 * @Date: 2023/1/9 1:48 下午
 * @Author: _AF
 * @Description:
 */
@Data
public class FunctionQrCode {

    private Long id;

    private Long orgId;

    private Long memberId;

    /**
     * 空间id
     */
    private Long treeId;

    /**
     * 空间树名称
     */
    private String treeName;

    /**
     * 空间全路径名称
     */
    private String pathTreeName;

    private String qrCodeName;

    /**
     * 设备地址
     */
    private String deviceAddressName;

    /**
     * 功能类型
     *
     * @see ActivateQrCodeConstant.FunctionTypeEnum
     */
    private Integer functionType;

    /**
     * 内容
     */
    private String content;
    /**
     * 网络连接类型
     *
     * @see ActivateQrCodeConstant.NetworkTypeEnum
     */
    private Integer networkType;
    /**
     * 连接类型
     *
     * @see ActivateQrCodeConstant.ConnectTypeEnum
     */
    private Integer connectType;
    /**
     * 私有云开关
     *
     * @see ActivateQrCodeConstant.PrivateCloudSwitchEnum
     */
    private Integer privateCloudSwitch;
    /**
     * 静态ip配置
     */
    private String cableStatic;
    /**
     * wifi信息
     */
    private String wifiInfo;
    /**
     * 私有云配置
     */
    private String privateCloud;

    /**
     *
     */
    private Integer roleType;

    /**
     * 二维码地址
     *
     * @see ActivateQrCodeConstant.RoleTypeEnum
     */
    private String url;

    private Date gmtCreate;

    private Date gmtModify;
    /**
     * 新激活时间
     */
    private Date newActivateTime;

    /**
     * 来源
     *
     * @see ActivateQrCodeConstant.AppletEnum
     */
    private Integer source;

    /**
     * 机构网络Id
     */
    private Long orgNetworkId;

    /**
     * 出入方向 1入口 2出口
     */
    private Integer direction;
}


