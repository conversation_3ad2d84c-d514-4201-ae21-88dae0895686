package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备网络信息
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_network_info")
public class DeviceNetworkInfo extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 设备网络信息Id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 网络类型 1-有线 2-无线
     */
    private Integer networkType;

    /**
     * 连接类型 1-DHCP 2-静态地址
     */
    private Integer connectType;

    /**
     * 静态ip配置信息
     */
    private String cableStatic;

    /**
     * wifi信息
     */
    private String wifiInfo;

    /**
     * 有线mac地址
     */
    private String wiredMac;

    /**
     * 无线mac地址
     */
    private String wifiMac;
}
