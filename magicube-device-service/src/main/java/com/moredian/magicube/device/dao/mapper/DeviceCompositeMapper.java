package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceComposite;
import com.moredian.magicube.device.dao.entity.DeviceCompositeItem;
import com.moredian.magicube.device.dto.composite.DeviceCompositeDTO;
import com.moredian.magicube.device.dto.composite.QueryDeviceCompositeDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人员可选设备的权限组
 *
 * <AUTHOR>
 */

@Mapper
public interface DeviceCompositeMapper {

    /**
     * 新增设备分组
     *
     * @param deviceComposite 设备分组信息
     */
    void insert(DeviceComposite deviceComposite);

    /**
     * 更新设备分组
     *
     * @param deviceComposite 设备分组信息
     */
    void update(DeviceComposite deviceComposite);

    /**
     * 根据分组Id删除
     *
     * @param orgId             机构号
     * @param deviceCompositeId 分组Id
     */
    void deleteById(@Param("orgId") Long orgId, @Param("deviceCompositeId") Long deviceCompositeId);

    /**
     * 根据Id查询设备分组信息
     *
     * @param orgId             机构号
     * @param deviceCompositeId 分组Id
     */
    DeviceComposite getById(@Param("orgId") Long orgId, @Param("deviceCompositeId") Long deviceCompositeId);

    /**
     * 根据名称查找
     *
     * @param orgId
     * @param bizType
     * @param deviceCompositeName
     * @return
     */
    List<DeviceComposite> getByName(@Param("orgId") Long orgId, @Param("bizType") Integer bizType, @Param("deviceCompositeName") String deviceCompositeName);

    /**
     * 查询机构下的组
     *
     * @param orgId   机构号
     * @param bizType 业务类型
     * @return
     */
    List<DeviceComposite> listByOrgIdAndBizType(@Param("orgId") Long orgId, @Param("bizType") Integer bizType);

    /**
     * 根据deviceCompositeId集合查询
     *
     * @param orgId
     * @param deviceCompositeIdList
     * @param bizType
     * @return
     */
    List<DeviceComposite> listByDeviceCompositeId(@Param("orgId") Long orgId, @Param("deviceCompositeIdList") List<Long> deviceCompositeIdList, @Param("bizType") Integer bizType);

    /**
     * 获取指定业务类型的设备虚拟根组信息
     * 调用该方法后，最好判断根设备组是否存在了，不存在就要新建根设备组
     *
     * 这个方法只能由下面的方法直接调用
     * @see com.moredian.magicube.device.manager.impl.DeviceCompositeManagerImpl#getVirtualRootDeviceComposite(java.lang.Long, java.lang.Integer)
     * @param orgId   组织 ID
     * @param bizType 业务类型
     * @return {@link List }<{@link DeviceComposite }>
     */
    DeviceComposite getVirtualRootDeviceComposite(@Param("orgId") Long orgId, @Param("bizType") Integer bizType);

    /**
     * 查db中的最大code
     *
     * @param orgId 机构ID
     */
    String getMaxDeptCode(@Param("orgId") Long orgId, @Param("bizType") Integer bizType);

    /**
     * 保存
     *
     * @param deviceComposite
     */
    void createComposite(DeviceComposite deviceComposite);

    /**
     * 根据codeList获取设备组
     *
     * @param orgId    组织 ID
     * @param codeList 代码列表
     * @param bizType  业务类型
     * @return {@link List }<{@link DeviceComposite }>
     */
    List<DeviceComposite> getCompositeByCodeList(@Param("orgId") Long orgId,
        @Param("codeList") List<String> codeList, @Param("bizType") Integer bizType);

    /**
     * 查询指定业务设备组下的所有子组
     * @param orgId
     * @param path
     * @param bizType
     * @return
     */
    List<DeviceComposite> getCompositeByPath(@Param("orgId") Long orgId, @Param("path") String path,
        @Param("bizType") Integer bizType);

    /**
     * 根据id删除
     *
     * @param orgId
     * @param deviceCompositeId
     */
    void removeComposite(@Param("orgId") Long orgId, @Param("deviceCompositeId") Long deviceCompositeId,@Param("status") Long status);

    /**
     * 开启管理员，子管理员查询根据自己的设备查询分组信息
     *
     * @param dto 查询条件
     * @return
     */
    List<DeviceComposite> listByCondition(QueryDeviceCompositeDTO dto);

    /**
     * 根据parentId查询设备组Id列表
     *
     * @param orgId    机构Id
     * @param parentId 父Id
     * @return
     */
    List<Long> listCompositeIdsByParentId(@Param("orgId") Long orgId, @Param("parentId") Long parentId);

    /**
     * 根据parentId列表查询设备组信息列表
     *
     * @param orgId    机构Id
     * @param parentIds 父Id列表
     * @return
     */
    List<DeviceComposite> listByParentIds(@Param("orgId") Long orgId, @Param("parentIds") List<Long> parentIds);

    List<DeviceComposite> getCompositeByPathBatch(@Param("orgId") Long orgId, @Param("paths") List<String> paths,
        @Param("bizType") Integer bizType);

    /**
     * 根据设备组id查设备组信息，可查已经删除的
     *
     * @param orgId       组织 ID
     * @param deviceCompositeId 复合 ID
     * @return {@link List }<{@link DeviceCompositeItem }>
     */
    DeviceComposite getCompositeByIdWithDelete(@Param("orgId") Long orgId,
        @Param("deviceCompositeId") Long deviceCompositeId);

    List<DeviceComposite> getCompositeByPathWithDelete(@Param("orgId") Long orgId, @Param("path") String path,
        @Param("bizType") Integer bizType);
}
