package com.moredian.magicube.device.subscriber;


import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import com.moredian.magicube.device.dao.entity.SpaceSubjectReleation;
import com.moredian.magicube.device.dao.mapper.SpaceSubjectRelationMapper;
import com.moredian.magicube.device.enums.DeviceSourceEnum;
import com.moredian.magicube.device.manager.DeviceSubjectRelationManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.message.SaveTreeDeviceRelationMsg;
import com.moredian.space.message.UpdateTreeDeviceRelationMsg;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SpaceTreeMsgSubscribe {

    @Autowired
    private SpaceSubjectRelationMapper spaceSubjectRelationMapper;

    @Autowired
    private DeviceSubjectRelationManager deviceSubjectRelationManager;

    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;

    @SI
    private SpaceTreeService spaceTreeService;

    @Subscribe
    public void subscribeBindMsg(SaveTreeDeviceRelationMsg msg) {
        log.info("监听设备绑定空间消息，msg: {}", msg);
        List<TreeDeviceRelationDTO> treeDeviceRelationDTOS = spaceTreeDeviceRelationService.listByTreeIdsAndSource(msg.getOrgId(), Collections.singletonList(msg.getTreeId()), DeviceSourceEnum.MD.getCode()).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(treeDeviceRelationDTOS)){
            List<Long> deviceIdList = treeDeviceRelationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId).distinct().collect(Collectors.toList());
            TreeDTO treeDTO = spaceTreeService.getById(msg.getTreeId(), msg.getOrgId()).pickDataThrowException();
            String path = treeDTO.getPath();
            if (StringUtils.isBlank(path)){
                return;
            }
            String[] split = path.split("/");
            List<Long> pathIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pathIdList)){
                return;
            }
            List<SpaceSubjectReleation> spaceSubjectReleations = spaceSubjectRelationMapper.listByOrgIdAndSpaceIds(msg.getOrgId(), pathIdList, 1);
            if (CollectionUtils.isNotEmpty(spaceSubjectReleations)){
                List<Long> subjectIdList = spaceSubjectReleations.stream().map(SpaceSubjectReleation::getSubjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(subjectIdList)){
                    for (Long subjectId : subjectIdList) {
                        List<SpaceSubjectReleation> spaceSubjectReleations1 = spaceSubjectRelationMapper.listByOrgIdAndSubjectIds(msg.getOrgId(), Collections.singletonList(subjectId));
                        List<Long> treeIdList = spaceSubjectReleations1.stream().map(SpaceSubjectReleation::getSpaceId).distinct().collect(Collectors.toList());
                        deviceSubjectRelationManager.resetRelationBySubjectId(msg.getOrgId(),subjectId,deviceIdList,treeIdList);
                    }
                }
            }


            List<SpaceSubjectReleation> spaceSubjectReleationsTwo = spaceSubjectRelationMapper.listByOrgIdAndSpaceIds(msg.getOrgId(), Collections.singletonList(msg.getTreeId()), 2);
            if (CollectionUtils.isNotEmpty(spaceSubjectReleationsTwo)){
                List<Long> subjectIdList = spaceSubjectReleationsTwo.stream().map(SpaceSubjectReleation::getSubjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(subjectIdList)){
                    for (Long subjectId : subjectIdList) {
                        List<SpaceSubjectReleation> spaceSubjectReleations2 = spaceSubjectRelationMapper.listByOrgIdAndSubjectIds(msg.getOrgId(), Collections.singletonList(subjectId));
                        List<Long> treeIdList2 = spaceSubjectReleations2.stream().map(SpaceSubjectReleation::getSpaceId).distinct().collect(Collectors.toList());

                        deviceSubjectRelationManager.resetRelationBySubjectId(msg.getOrgId(),subjectId,deviceIdList,treeIdList2);
                    }
                }
            }

        }

    }

    @Subscribe
    public void subscribeuUdateMsg(UpdateTreeDeviceRelationMsg msg) {
        log.info("监听设备换绑空间消息，msg: {}", msg);
        List<TreeDeviceRelationDTO> treeDeviceRelationDTOS = spaceTreeDeviceRelationService.listByTreeIdsAndSource(msg.getOrgId(), Collections.singletonList(msg.getTreeId()), DeviceSourceEnum.MD.getCode()).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(treeDeviceRelationDTOS)){
            List<Long> deviceIdList = treeDeviceRelationDTOS.stream().map(TreeDeviceRelationDTO::getDeviceId).distinct().collect(Collectors.toList());
            TreeDTO treeDTO = spaceTreeService.getById(msg.getTreeId(), msg.getOrgId()).pickDataThrowException();
            String path = treeDTO.getPath();
            if (StringUtils.isBlank(path)){
                return;
            }
            String[] split = path.split("/");
            List<Long> pathIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pathIdList)){
                return;
            }
            List<SpaceSubjectReleation> spaceSubjectReleations = spaceSubjectRelationMapper.listByOrgIdAndSpaceIds(msg.getOrgId(), pathIdList, 1);
            if (CollectionUtils.isNotEmpty(spaceSubjectReleations)){
                List<Long> subjectIdList = spaceSubjectReleations.stream().map(SpaceSubjectReleation::getSubjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(subjectIdList)){
                    for (Long subjectId : subjectIdList) {
                        List<SpaceSubjectReleation> spaceSubjectReleations1 = spaceSubjectRelationMapper.listByOrgIdAndSubjectIds(msg.getOrgId(), Collections.singletonList(subjectId));
                        List<Long> treeIdList = spaceSubjectReleations1.stream().map(SpaceSubjectReleation::getSpaceId).distinct().collect(Collectors.toList());
                        deviceSubjectRelationManager.resetRelationBySubjectId(msg.getOrgId(),subjectId,deviceIdList,treeIdList);
                    }
                }
            }


            List<SpaceSubjectReleation> spaceSubjectReleationsTwo = spaceSubjectRelationMapper.listByOrgIdAndSpaceIds(msg.getOrgId(), Collections.singletonList(msg.getTreeId()), 2);
            if (CollectionUtils.isNotEmpty(spaceSubjectReleationsTwo)){
                List<Long> subjectIdList = spaceSubjectReleationsTwo.stream().map(SpaceSubjectReleation::getSubjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(subjectIdList)){
                    for (Long subjectId : subjectIdList) {
                        List<SpaceSubjectReleation> spaceSubjectReleations2 = spaceSubjectRelationMapper.listByOrgIdAndSubjectIds(msg.getOrgId(), Collections.singletonList(subjectId));
                        List<Long> treeIdList2 = spaceSubjectReleations2.stream().map(SpaceSubjectReleation::getSpaceId).distinct().collect(Collectors.toList());

                        deviceSubjectRelationManager.resetRelationBySubjectId(msg.getOrgId(),subjectId,deviceIdList,treeIdList2);
                    }
                }
            }

        }

    }

}
