package com.moredian.magicube.device.convertor;

import com.moredian.magicube.device.dto.device.DeviceAppRelationConfigDTO;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;

public class DeviceAppRelationConfigConvertor {

    /**
     * Converts a DeviceAppRelationConfigDTO to a DeviceAppRelationConfig.
     *
     * @param dto the DTO to convert
     * @return the converted entity
     */
    public static DeviceAppRelationConfig dtoToEntity(DeviceAppRelationConfigDTO dto) {
        if (dto == null) {
            return null;
        }

        DeviceAppRelationConfig entity = new DeviceAppRelationConfig();

        // Map DTO properties to Entity properties
                entity.setId(dto.getId());
                entity.setSpaceType(dto.getSpaceType());
                entity.setAppType(dto.getAppType());
                entity.setVersionCode(dto.getVersionCode());
                entity.setDefaultAppCode(dto.getDefaultAppCode());
                entity.setAvailableAppCodeList(dto.getAvailableAppCodeList());
                entity.setGmtCreate(dto.getGmtCreate());
                entity.setGmtModify(dto.getGmtModify());
        
        return entity;
    }

    /**
     * Converts a DeviceAppRelationConfig to a DeviceAppRelationConfigDTO.
     *
     * @param entity the entity to convert
     * @return the converted DTO
     */
    public static DeviceAppRelationConfigDTO entityToDto(DeviceAppRelationConfig entity) {
        if (entity == null) {
            return null;
        }

        DeviceAppRelationConfigDTO dto = new DeviceAppRelationConfigDTO();

        // Map Entity properties to DTO properties
                dto.setId(entity.getId());
                dto.setSpaceType(entity.getSpaceType());
                dto.setAppType(entity.getAppType());
                dto.setVersionCode(entity.getVersionCode());
                dto.setDefaultAppCode(entity.getDefaultAppCode());
                dto.setAvailableAppCodeList(entity.getAvailableAppCodeList());
                dto.setGmtCreate(entity.getGmtCreate());
                dto.setGmtModify(entity.getGmtModify());
        
        return dto;
    }
}