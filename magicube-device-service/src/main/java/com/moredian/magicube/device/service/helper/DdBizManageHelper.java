package com.moredian.magicube.device.service.helper;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.web.BaseResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dto.upgrade.GetIncrOrFullNewAppRequest;
import com.moredian.magicube.device.dto.version.AppVersionIncreWithAppVersionInfoDTO;
import com.moredian.magicube.device.dto.version.VersionDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.model.WhiteDeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date : 2024/9/26
 */
@Component
@Slf4j
public class DdBizManageHelper {

    @Value("${dd.address.moredianFishnetWeb:http://************:8049}")
    private String ddFishnetWebUrl;
    @SI
    private OrgService orgService;

    /**
     * 请求超时时间
     */
    public static final int TIME_OUT = 30000;

    /*-------------------------------------------fishnet-web--------------------------------------------*/
    private final static String GET_NEW_APP_VERSION_URL = "/device/version/getNewAppVersion";
    private final static String GET_APP_VERSION_BY_CODE_URL = "/device/version/getNewAppVersionBySCType";
    private final static String GET_CUR_APP_VERSION_BY_CODE_URL = "/device/version/getAppVersionBySCTypeCode";
    private final static String GET_ROM_VERSION_BY_CODE_URL = "/device/version/getRomVersionBySCTypeCode";
    private final static String GET_NEW_APP_VERSION_ACTIVE_URL = "/device/version/getNewAppVersionBySCTypeAndIsActive";
    private final static String GET_NEW_APP_VERSION_INCR_URL = "/device/version/getNewAppVersionIncreByAppTypeAndOldVersion";
    private final static String GET_WHITE_DEVICE_LIST_URL = "/device/whiteDevice/list";

    public VersionDTO getNewAppVersionBySCTypeAndIsActiveAndOrgId(Long orgId, Integer systemType,
                                                                  Integer clientType) {
        OrgInfo orgInfo = orgService.getOrgInfo(orgId, Arrays.asList(OrgStatus.INIT.getValue(), OrgStatus.UNBIND.getValue(), OrgStatus.USABLE.getValue())).pickDataThrowException();
        Map<String, Object> formMap = new HashMap<>(8);
        formMap.put("tpId", orgInfo.getTpId());
        formMap.put("systemType", systemType);
        formMap.put("clientType", clientType);
        String invokeUrl = ddFishnetWebUrl + GET_NEW_APP_VERSION_URL;
        HttpResponse httpResponse;
        try {
            log.info("get请求塔上地址：{} 请求form：{}", invokeUrl,
                    JSONUtil.toJsonStr(formMap));
            httpResponse = HttpUtil.createGet(invokeUrl)
                    .form(formMap)
                    //超时，毫秒
                    .timeout(TIME_OUT)
                    .execute();
        } catch (Exception e) {
            log.error("执行调用" + invokeUrl + "失败:" + e.getMessage(), e);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED, DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED.getMessage()));
        }
        log.info("塔上-getNewAppVersion http result:{}", JSONUtil.toJsonStr(httpResponse));
        if (HttpStatus.HTTP_OK == httpResponse.getStatus() && httpResponse.body() != null) {
            BaseResponse<VersionDTO> baseResponse = JSON.parseObject(httpResponse.body(),
                    new TypeReference<BaseResponse<VersionDTO>>() {
                    });
            if (BaseResponse.SUCCESS_RESULT.equals(baseResponse.getResult())){
                return baseResponse.getData();
            }else {
                log.error("请求调用失败，业务错误：{}", baseResponse.getResult() + ":" + baseResponse.getMessage());
            }
        }else {
            log.error("请求调用失败，状态码异常/响应体为空，返回值：{}", httpResponse);
        }
        return null;
    }

    public VersionDTO getNewAppVersionBySCType(Integer systemType, Integer appType) {
        Map<String, Object> formMap = new HashMap<>(8);
        formMap.put("systemType", systemType);
        formMap.put("appType", appType);
        String invokeUrl = ddFishnetWebUrl + GET_APP_VERSION_BY_CODE_URL;
        HttpResponse httpResponse;
        try {
            log.info("get请求塔上地址：{} 请求form：{}", invokeUrl, JSONUtil.toJsonStr(formMap));
            httpResponse = HttpUtil.createGet(invokeUrl)
                    .form(formMap)
                    .timeout(TIME_OUT)
                    .execute();
        } catch (Exception e) {
            log.error("执行调用" + invokeUrl + "失败:" + e.getMessage(), e);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED, DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED.getMessage()));
        }
        log.info("塔上-getNewAppVersionBySCType http result:{}", JSONUtil.toJsonStr(httpResponse));
        if (HttpStatus.HTTP_OK == httpResponse.getStatus() && httpResponse.body() != null) {
            BaseResponse<VersionDTO> baseResponse = JSON.parseObject(httpResponse.body(),
                    new TypeReference<BaseResponse<VersionDTO>>() {
                    });
            if (BaseResponse.SUCCESS_RESULT.equals(baseResponse.getResult())){
                if (baseResponse.getData() != null && baseResponse.getData().getAppType() != null){
                    return baseResponse.getData();
                }
            } else {
                log.error("请求调用失败，业务错误：{}", baseResponse.getResult() + ":" + baseResponse.getMessage());
            }
        } else {
            log.error("请求调用失败，状态码异常/响应体为空，返回值：{}", httpResponse);
        }
        VersionDTO currAppVersionDto = new VersionDTO();
        currAppVersionDto.setAppType(appType);
        currAppVersionDto.setSystemType(systemType);
        return currAppVersionDto;
    }

    public VersionDTO getRomVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode, Integer apkAppType) {
        Map<String, Object> formMap = new HashMap<>(8);
        formMap.put("systemType", systemType);
        formMap.put("appType", appType);
        formMap.put("versionCode", versionCode);
        formMap.put("apkAppType", apkAppType);
        String invokeUrl = ddFishnetWebUrl + GET_ROM_VERSION_BY_CODE_URL;
        HttpResponse httpResponse;
        try {
            log.info("get请求塔上地址：{} 请求form：{}", invokeUrl, JSONUtil.toJsonStr(formMap));
            httpResponse = HttpUtil.createGet(invokeUrl)
                    .form(formMap)
                    .timeout(TIME_OUT)
                    .execute();
        } catch (Exception e) {
            log.error("执行调用" + invokeUrl + "失败:" + e.getMessage(), e);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED, DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED.getMessage()));
        }
        log.info("塔上-getRomVersionBySCTypeCode http result:{}", JSONUtil.toJsonStr(httpResponse));
        if (HttpStatus.HTTP_OK == httpResponse.getStatus() && httpResponse.body() != null) {
            BaseResponse<VersionDTO> baseResponse = JSON.parseObject(httpResponse.body(),
                    new TypeReference<BaseResponse<VersionDTO>>() {
                    });
            if (BaseResponse.SUCCESS_RESULT.equals(baseResponse.getResult())){
                if (baseResponse.getData() != null && baseResponse.getData().getAppType() != null){
                    return baseResponse.getData();
                }
            } else {
                log.error("请求调用失败，业务错误：{}", baseResponse.getResult() + ":" + baseResponse.getMessage());
            }
        } else {
            log.error("请求调用失败，状态码异常/响应体为空，返回值：{}", httpResponse);
        }
        VersionDTO currRomVersion = new VersionDTO();
        currRomVersion.setAppType(appType);
        currRomVersion.setSystemType(systemType);
        return currRomVersion;
    }

    public VersionDTO getNewRomVersionBySCTypeAndIsActive(Integer systemType, Integer appType, Integer apkAppType) {
        return getNewAppVersionBySCTypeAndIsActive(systemType, appType);
    }

    public VersionDTO getNewAppVersionBySCTypeAndIsActive(Integer systemType, Integer appType) {
        Map<String, Object> formMap = new HashMap<>(8);
        formMap.put("systemType", systemType);
        formMap.put("appType", appType);
        String invokeUrl = ddFishnetWebUrl + GET_NEW_APP_VERSION_ACTIVE_URL;
        HttpResponse httpResponse;
        try {
            log.info("get请求塔上地址：{} 请求form：{}", invokeUrl, JSONUtil.toJsonStr(formMap));
            httpResponse = HttpUtil.createGet(invokeUrl)
                    .form(formMap)
                    .timeout(TIME_OUT)
                    .execute();
        } catch (Exception e) {
            log.error("执行调用" + invokeUrl + "失败:" + e.getMessage(), e);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED, DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED.getMessage()));
        }
        log.info("塔上-getNewAppVersionBySCTypeAndIsActive http result:{}", JSONUtil.toJsonStr(httpResponse));
        if (HttpStatus.HTTP_OK == httpResponse.getStatus() && httpResponse.body() != null) {
            BaseResponse<VersionDTO> baseResponse = JSON.parseObject(httpResponse.body(),
                    new TypeReference<BaseResponse<VersionDTO>>() {
                    });
            if (BaseResponse.SUCCESS_RESULT.equals(baseResponse.getResult())){
                if (baseResponse.getData() != null && baseResponse.getData().getAppType() != null){
                    return baseResponse.getData();
                }
            } else {
                log.error("请求调用失败，业务错误：{}", baseResponse.getResult() + ":" + baseResponse.getMessage());
            }
        } else {
            log.error("请求调用失败，状态码异常/响应体为空，返回值：{}", httpResponse);
        }
        VersionDTO lastAppVersion = new VersionDTO();
        lastAppVersion.setAppType(appType);
        lastAppVersion.setSystemType(systemType);
        return lastAppVersion;
    }

    public AppVersionIncreWithAppVersionInfoDTO getNewAppVersionIncreByAppTypeAndOldVersion(GetIncrOrFullNewAppRequest getIncrOrFullNewAppRequest) {
        OrgInfo orgInfo = orgService.getOrgInfo(getIncrOrFullNewAppRequest.getOrgId(), Arrays.asList(OrgStatus.INIT.getValue(), OrgStatus.UNBIND.getValue(), OrgStatus.USABLE.getValue())).pickDataThrowException();
        Map<String, Object> formMap = new HashMap<>(8);
        formMap.put("tpId", orgInfo.getTpId());
        formMap.put("systemType", 1);
        formMap.put("appType", getIncrOrFullNewAppRequest.getAppType());
        formMap.put("oldVersionCode", getIncrOrFullNewAppRequest.getVersionCode());
        String invokeUrl = ddFishnetWebUrl + GET_NEW_APP_VERSION_INCR_URL;
        HttpResponse httpResponse;
        try {
            log.info("get请求塔上地址：{} 请求form：{}", invokeUrl,
                    JSONUtil.toJsonStr(formMap));
            httpResponse = HttpUtil.createGet(invokeUrl)
                    .form(formMap)
                    //超时，毫秒
                    .timeout(TIME_OUT)
                    .execute();
        } catch (Exception e) {
            log.error("执行调用" + invokeUrl + "失败:" + e.getMessage(), e);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED, DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED.getMessage()));
        }
        log.info("塔上-getNewAppVersion http result:{}", JSONUtil.toJsonStr(httpResponse));
        if (HttpStatus.HTTP_OK == httpResponse.getStatus() && httpResponse.body() != null) {
            BaseResponse<AppVersionIncreWithAppVersionInfoDTO> baseResponse = JSON.parseObject(httpResponse.body(),
                    new TypeReference<BaseResponse<AppVersionIncreWithAppVersionInfoDTO>>() {
                    });
            if (BaseResponse.SUCCESS_RESULT.equals(baseResponse.getResult())){
                return baseResponse.getData();
            }else {
                log.error("请求调用失败，业务错误：{}", baseResponse.getResult() + ":" + baseResponse.getMessage());
            }
        }else {
            log.error("请求调用失败，状态码异常/响应体为空，返回值：{}", httpResponse);
        }
        return null;
    }

    public List<WhiteDeviceInfo> getWhiteDeviceList(List<String> deviceSnList) {
        String invokeUrl = ddFishnetWebUrl + GET_WHITE_DEVICE_LIST_URL;
        HttpResponse httpResponse;
        try {
            log.info("post请求塔上地址：{} 请求body：{}", invokeUrl,
                    JSONUtil.toJsonStr(deviceSnList));
            httpResponse = HttpUtil.createPost(invokeUrl)
                    .body(JSONUtil.toJsonStr(deviceSnList))
                    //超时，毫秒
                    .timeout(TIME_OUT)
                    .execute();
        } catch (Exception e) {
            log.error("执行调用" + invokeUrl + "失败:" + e.getMessage(), e);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED, DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED.getMessage()));
        }
        log.info("塔上-getWhiteDeviceList http result:{}", JSONUtil.toJsonStr(httpResponse));
        if (HttpStatus.HTTP_OK == httpResponse.getStatus() && httpResponse.body() != null) {
            BaseResponse<List<WhiteDeviceInfo>> baseResponse = JSON.parseObject(httpResponse.body(),
                    new TypeReference<BaseResponse<List<WhiteDeviceInfo>>>() {
                    });
            if (BaseResponse.SUCCESS_RESULT.equals(baseResponse.getResult())){
                return baseResponse.getData();
            }else {
                log.error("请求调用失败，业务错误：{}", baseResponse.getResult() + ":" + baseResponse.getMessage());
            }
        }else {
            log.error("请求调用失败，状态码异常/响应体为空，返回值：{}", httpResponse);
        }
        return null;
    }

    public VersionDTO getAppVersionBySCTypeCode(Integer systemType, Integer appType, Integer appVersionCode) {
        Map<String, Object> formMap = new HashMap<>(8);
        formMap.put("systemType", systemType);
        formMap.put("appType", appType);
        formMap.put("appVersionCode", appVersionCode);
        String invokeUrl = ddFishnetWebUrl + GET_CUR_APP_VERSION_BY_CODE_URL;
        HttpResponse httpResponse;
        try {
            log.info("get请求塔上地址：{} 请求form：{}", invokeUrl, JSONUtil.toJsonStr(formMap));
            httpResponse = HttpUtil.createGet(invokeUrl)
                    .form(formMap)
                    .timeout(TIME_OUT)
                    .execute();
        } catch (Exception e) {
            log.error("执行调用" + invokeUrl + "失败:" + e.getMessage(), e);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED, DeviceErrorCode.DEVICE_DD_SERVICE_CALL_FAILED.getMessage()));
        }
        log.info("塔上-getAppVersionBySCTypeCode http result:{}", JSONUtil.toJsonStr(httpResponse));
        if (HttpStatus.HTTP_OK == httpResponse.getStatus() && httpResponse.body() != null) {
            BaseResponse<VersionDTO> baseResponse = JSON.parseObject(httpResponse.body(),
                    new TypeReference<BaseResponse<VersionDTO>>() {
                    });
            if (BaseResponse.SUCCESS_RESULT.equals(baseResponse.getResult())){
                if (baseResponse.getData() != null && baseResponse.getData().getAppType() != null){
                    return baseResponse.getData();
                }
            } else {
                log.error("请求调用失败，业务错误：{}", baseResponse.getResult() + ":" + baseResponse.getMessage());
            }
        } else {
            log.error("请求调用失败，状态码异常/响应体为空，返回值：{}", httpResponse);
        }
        VersionDTO currAppVersionDto = new VersionDTO();
        currAppVersionDto.setAppType(appType);
        currAppVersionDto.setSystemType(systemType);
        return currAppVersionDto;
    }
}
