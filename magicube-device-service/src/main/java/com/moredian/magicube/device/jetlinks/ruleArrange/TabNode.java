package com.moredian.magicube.device.jetlinks.ruleArrange;

import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;

/**
 * <AUTHOR>
 * @version $Id: TabNode.java, v 1.0 Exp $
 */
public class TabNode extends Node{

    public TabNode(String modelId, Flow flow, DeviceTypePropertyManager deviceTypePropertyManager) {
        super(modelId, flow, deviceTypePropertyManager);
    }

    public void wrapperContent(String ruleName, TreeDeviceRelationDTO treeDeviceRelation) {
        flow.addNode("{\\\"id\\\":\\\""+modelId+"\\\",\\\"type\\\":\\\"tab\\\",\\\"label\\\":\\\""+(treeDeviceRelation.getTreeName()+","+ruleName)+"\\\",\\\"disabled\\\":false,\\\"info\\\":\\\"\\\"}");
    }
}
