package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO.DeviceState;
import com.moredian.bridge.api.service.deviceInstance.req.QueryDeviceListRequest;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.DeviceStateRequest;
import com.moredian.iothub.control.api.v1.request.RebootRequest;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.DeviceStateResponse;
import com.moredian.iothub.control.api.v1.response.RebootResponse;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.DeviceIotSplitConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.dto.device.RebootDTO;
import com.moredian.magicube.device.dto.device.TransferDTO;
import com.moredian.magicube.device.enums.IotDeviceStatusEnums;
import com.moredian.magicube.device.manager.DeviceIotManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.helper.IotDeviceManagerHelper;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceTypePropertyService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class DeviceIotManagerImpl implements DeviceIotManager {

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;

    @Resource
    private DeviceManager deviceManager;

    @Resource
    private DeviceTypePropertyService deviceTypePropertyService;
    @Resource
    private IotDeviceManagerHelper iotDeviceManagerHelper;

    @Override
    public DeviceStatusDTO getStatusByDeviceSn(String deviceSn) {
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        DeviceStatusDTO deviceStatusDTO = new DeviceStatusDTO();
        deviceStatusDTO.setSerialNumber(deviceSn);
        DeviceStateRequest deviceStateRequest = new DeviceStateRequest();
        deviceStateRequest.setSerialNumber(deviceSn);
        DeviceStateResponse deviceStateResponse = hubControlServiceV1
            .getDeviceState(deviceStateRequest).pickDataThrowException();
        if (deviceStateResponse != null) {
            deviceStatusDTO.setLastTimeStamp(deviceStateResponse.getLastTimestamp());
            deviceStatusDTO.setOnline(deviceStateResponse.getOnline());
        }
        return deviceStatusDTO;
    }

    @Override
    public List<DeviceStatusDTO> listDeviceStateByDeviceSns(List<String> deviceSns) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceSns), "deviceSns must not be null");
        List<DeviceStatusDTO> deviceStatusDTOs = new ArrayList<>();

        // 根据设备类型区分开智能设备与iot设备
        List<Device> devices = deviceManager.listByDeviceSns(deviceSns);
        if (ObjectUtils.isEmpty(devices)) {
            return new ArrayList<>();
        }
        Long orgId = devices.get(0).getOrgId();

        List<String> iotDeviceSN = new ArrayList<>();
        // 通通锁设备
        List<String> ttLockListSn = new ArrayList<>();
        List<String> smartDeviceSN = new ArrayList<>();
        // 从魔链查询网络状态得设备类型
        List<Integer> onlineFromMoreLinks = deviceTypePropertyService.
            getDeviceTypeByPropertyKey(DeviceTypeConstants.DEVICE_ONLINE_FROM_MORE_LINKS).pickDataThrowException();
        for (Device device : devices) {
            Integer deviceType = device.getDeviceType();
            if (onlineFromMoreLinks.contains(deviceType)) {
                if (DeviceType.TTLOCK.getValue() == deviceType) {
                    ttLockListSn.add(device.getDeviceSn());
                }else {
                    iotDeviceSN.add(device.getDeviceSn());
                }
            }else {
                smartDeviceSN.add(device.getDeviceSn());
            }
        }

        if (!ObjectUtils.isEmpty(smartDeviceSN)){
            // 查询智能设备的在离线
            Map<String, DeviceStateRequest> requestMap = new HashMap<>();
            for (String deviceSn : smartDeviceSN) {
                DeviceStateRequest deviceStateRequest = new DeviceStateRequest();
                requestMap.put(deviceSn, deviceStateRequest);
            }
            Map<String, DeviceStateResponse> map = hubControlServiceV1.getDeviceListState(requestMap)
                .pickDataThrowException();
            if (map != null) {
                for (DeviceStateResponse deviceStateResponse : map.values()) {
                    DeviceStatusDTO deviceStatusDTO = new DeviceStatusDTO();
                    deviceStatusDTO.setLastTimeStamp(deviceStateResponse.getLastTimestamp());
                    deviceStatusDTO.setOnline(deviceStateResponse.getOnline());
                    deviceStatusDTO.setSerialNumber(deviceStateResponse.getSerialNumber());
                    deviceStatusDTOs.add(deviceStatusDTO);
                }
            }
        }

        // iot设备在离线
        if (!ObjectUtils.isEmpty(iotDeviceSN) || !ObjectUtils.isEmpty(ttLockListSn)) {
            // 查询iot设备的在离线
            List<String> newIotDeviceSN = iotDeviceSN.stream().map(this::parseDeviceSn).distinct().collect(Collectors.toList());

            Map<String, Boolean> iotStateMap = Maps.newHashMap();
            // 通通锁
            if (!ObjectUtils.isEmpty(ttLockListSn)) {
                Map<String, Boolean> ttLockStateMap = iotDeviceManagerHelper.getIotDeviceStateMap(-1L, ttLockListSn);
                iotStateMap.putAll(ttLockStateMap);
            }
            // 普通iot设备
            if (!ObjectUtils.isEmpty(iotDeviceSN)) {
                Map<String, Boolean> iotDeviceStateMap = iotDeviceManagerHelper.getIotDeviceStateMap(orgId, newIotDeviceSN);
                iotStateMap.putAll(iotDeviceStateMap);
            }
            List<String> allIotDeviceSn = new ArrayList<>();
            allIotDeviceSn.addAll(iotDeviceSN);
            allIotDeviceSn.addAll(ttLockListSn);
            for (String deviceSn : allIotDeviceSn) {
                Boolean state = iotStateMap.get(parseDeviceSn(deviceSn));
                DeviceStatusDTO deviceStatusDTO = new DeviceStatusDTO();
                deviceStatusDTO.setOnline(state == null ? Boolean.FALSE : state);
                deviceStatusDTO.setSerialNumber(deviceSn);
                deviceStatusDTOs.add(deviceStatusDTO);
            }
        }
        return deviceStatusDTOs;
    }

    private String parseDeviceSn(String deviseSn) {
        return deviseSn.split(DeviceIotSplitConstants.UNDERLINE)[0];
    }

    @Override
    public DeviceStatusDTO reboot(RebootDTO dto) {
        BizAssert.notNull(dto.getSerialNumber(), "serialNumber must not be null");
        RebootRequest rebootRequest = new RebootRequest();
        rebootRequest.setSerialNumber(dto.getSerialNumber());
        RebootResponse rebootResponse = hubControlServiceV1.reboot(rebootRequest)
            .pickDataThrowException();
        DeviceStatusDTO deviceStatusDTO = new DeviceStatusDTO();
        if (rebootResponse != null) {
            BeanUtils.copyProperties(rebootResponse, deviceStatusDTO);
        }
        return deviceStatusDTO;
    }

    @Override
    public DeviceStatusDTO transfer(TransferDTO dto) {
        BizAssert.notNull(dto.getSerialNumber(), "serialNumber must not be null");
        BizAssert.notNull(dto.getBody(), "body must not be null");
        TransferRequest transferRequest = new TransferRequest();
        transferRequest.setBody(dto.getBody());
        transferRequest.setSerialNumber(dto.getSerialNumber());
        transferRequest.setCommand(0);
        TransferResponse transferResponse = hubControlServiceV1.transfer(transferRequest)
            .pickDataThrowException();
        DeviceStatusDTO deviceStatusDTO = new DeviceStatusDTO();
        if (transferResponse != null) {
            BeanUtils.copyProperties(transferResponse, deviceStatusDTO);
        }
        return deviceStatusDTO;
    }
}