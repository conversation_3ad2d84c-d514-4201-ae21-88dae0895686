package com.moredian.magicube.device.jetlinks.rule.arrange;

import com.moredian.magicube.device.jetlinks.constant.JetLinksConstants;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description：设备指令节点
 * @date ：2024/08/06 16:58
 */

@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class DeviceMessageSenderNode extends Node{

    public static final String TYPE = "device-message-sender";

    /**
     * 设备消息来源，fixed、pre-node(默认)
     */
    private String from = JetLinksConstants.ARRANGE_DEVICE_NODE_PRE;

    /**
     * 并行度，默认64
     */
    private Integer concurrency = 64;

    /**
     * 状态过滤，ignoreOffline(默认)、all
     */
    private String stateOperator = JetLinksConstants.ARRANGE_DEVICE_NODE_IGNORE_OFFLINE;

    /**
     * 下发设备消息
     */
    private String message;

    /**
     * 下发指令超时时间
     */
    private String timout = "10s";

    public DeviceMessageSenderNode() {
        super(TYPE);
    }
}
