<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceWhiteListMapper" >

    <resultMap id="deviceWhiteListResultMap" type="com.moredian.magicube.device.dao.entity.DeviceWhiteList">
		<result column="white_list_id" property="whiteListId" />
		<result column="org_id" property="orgId" />
		<result column="device_sn" property="deviceSn" />
		<result column="device_name" property="deviceName" />
		<result column="status" property="status" />
		<result column="device_id" property="deviceId"/>
		<result column="gmt_create" property="gmtCreate" />
		<result column="gmt_modify" property="gmtModify" />
	</resultMap>

	<insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceWhiteList">
		insert into hive_org_device_white_list(
			white_list_id,
			org_id,
			device_sn,
			device_name,
			device_id
			status,
			gmt_create,
			gmt_modify
		) values (
		 	#{whiteListId},
			#{orgId},
			#{deviceSn},
			#{deviceName},
			#{deviceId}
			#{status},
			now(3),
			now(3)
		)
	</insert>

	<insert id="insertBatch" parameterType="com.moredian.magicube.device.dao.entity.DeviceWhiteList">
		INSERT INTO hive_org_device_white_list(white_list_id,org_id,device_sn,device_name,device_id,status,gmt_create,gmt_modify)
		VALUES
		<foreach collection="deviceWhiteList" item="obj" separator=",">
			(
			#{obj.whiteListId},
			#{obj.orgId},
			#{obj.deviceSn},
			#{obj.deviceName},
			#{obj.deviceId},
			#{obj.status},
			now(3),
			now(3)
			)
		</foreach>
	</insert>

	<update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceWhiteList">
		UPDATE hive_org_device_white_list
		<set>
			<if test="status !=null">
				status = #{status},
			</if>
			<if test="orgId !=null">
				org_id = #{orgId},
			</if>
			<if test="deviceName !=null">
				device_name = #{deviceName},
			</if>
			<if test="deviceId !=null">
				device_id = #{deviceId},
			</if>
			gmt_modify = now(3)
		</set>
		where device_sn = #{deviceSn}
	</update>

	<select id="getDeviceWhiteList" parameterType="com.moredian.magicube.device.dao.entity.DeviceWhiteList" resultMap="deviceWhiteListResultMap">
		select
		<include refid="sql_select"/>
		from hive_org_device_white_list
		<where>
			<if test="orgId != null">
				and org_Id = #{orgId}
			</if>
			<if test="deviceId != null">
				and device_id = #{deviceId}
			</if>
			<if test="deviceSn !=null">
				and device_sn = #{deviceSn}
			</if>
			<if test="status !=null">
				and status = #{status}
			</if>

		</where>
	</select>
	<update id="unBind" parameterType="string">
		UPDATE hive_org_device_white_list
		SET org_id = null , device_name = null,device_id = null,gmt_modify = now(3)
		where device_sn = #{deviceSn}
	</update>


	<sql id="sql_select">
			white_list_id,
			org_id,
			device_sn,
			device_name,
			device_id,
			status,
			gmt_create,
			gmt_modify
	</sql>
</mapper>