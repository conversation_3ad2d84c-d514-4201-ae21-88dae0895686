package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.DeviceSubject;
import com.moredian.magicube.device.dto.subject.DeviceSubjectDTO;

import java.util.List;

/**
 * 设备主题服务
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface DeviceSubjectManager {

    /**
     * 新增设备主题
     *
     * @param deviceSubject 设备主题信息
     * @return
     */
    Long insert(DeviceSubject deviceSubject);



    /**
     * 新增设备主题(批量）
     *
     * @param deviceSubjectList 设备主题信息
     * @return
     */
    List<DeviceSubject> batchInsert(List<DeviceSubject> deviceSubjectList);

    /**
     * 根据Id查询设备主题信息
     *
     * @param orgId 机构Id
     * @param id    设备主题Id
     * @return
     */
    DeviceSubject getById(Long orgId, Long id);

    /**
     * 根据机构Id和主题id获取主题信息
     *
     * @param orgId
     * @param id
     * @return
     */
    DeviceSubjectDTO getByOrgIdAndId(Long orgId, Long id);

    /**
     * 编辑设备主题信息
     *
     * @param deviceSubject 设备主题信息
     * @return
     */
    Boolean update(DeviceSubject deviceSubject);

    /**
     * 查询设备主题数量
     *
     * @param orgId 机构Id
     * @param type  主题类型 1-壁纸 2-屏保
     * @return
     */
    Integer countByOrgIdAndType(Long orgId, Integer type);

    /**
     * 根据机构Id和设备主题Id列表查询设备主题信息列表
     *
     * @param orgId 机构Id
     * @param ids   设备主题Id列表
     * @return
     */
    List<DeviceSubject> listByOrgIdAndIds(Long orgId, List<Long> ids);

    /**
     * 根据条件查询设备主题信息列表
     *
     * @param deviceSubject 设备主题信息
     * @return
     */
    List<DeviceSubject> listByCondition(DeviceSubject deviceSubject);


    /**
     * 根据条件分页查询设备主题信息列表
     *
     * @param deviceSubject 设备主题信息
     * @return
     */
    List<DeviceSubject> listByConditionAndPage(DeviceSubject deviceSubject,Integer pageNo,Integer pageSize,List<Long> deviceIdList);

    /**
     * 批量删除设备主题
     *
     * @param orgId      机构Id
     * @param subjectIds 设备主题Id列表
     * @return
     */
    Boolean deleteByOrgIdAndIds(Long orgId, List<Long> subjectIds);

    /**
     * 批量删除设备主题
     *
     * @param deviceSubject 设备主题Id列表
     * @return
     */
    Integer countAll(DeviceSubject deviceSubject,List<Long> subjectIdList);


    /**
     * 更新屏幕老数据
     * @param orgId
     * @return
     */
    boolean updateOldScreenData(Long orgId,Integer type);
}
