package com.moredian.magicube.device.config;

import java.util.Date;
import org.apache.ibatis.reflection.MetaObject;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.moredian.magicube.common.constant.SystemConstant;
import org.springframework.stereotype.Component;

/**
 * 苞米豆对象处理(mybatis-plus版本2.0.9+可以使用)
 * <AUTHOR>
 * @date 2019-10-21 20:17:32
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {


    @Override
    public void insertFill(MetaObject metaObject) {
        //插入填充- 是否有效 创建时间 修改时间  在插入时自动带上
        Object valid = getFieldValByName("valid", metaObject);
        Object gmtCreate = getFieldValByName("gmtCreate", metaObject);
        Object gmtModify = getFieldValByName("gmtModify", metaObject);
        if (valid == null) {
            setFieldValByName("valid", SystemConstant.VALID, metaObject);
        }
        if (gmtCreate == null) {
            setFieldValByName("gmtCreate", new Date(), metaObject);
        }
        if(gmtModify == null){
            setFieldValByName("gmtModify", new Date(), metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        //更新填充
        setFieldValByName("gmtModify", new Date(), metaObject);
    }
}
