package com.moredian.magicube.device.pipeline.executor.strategy;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.executor.AbstractActivateDeviceEngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.device.DeviceNameConvertPipe;
import com.moredian.magicube.device.pipeline.pipe.device.MoDeviceCheckPipe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 魔蓝设备二维码激活引擎执行器
 *
 * <AUTHOR>
 */
@Component
public class MoDeviceActivateEngineExecutor extends AbstractActivateDeviceEngineExecutor {

    @Autowired
    private MoDeviceCheckPipe moDeviceCheckPipe;

    @Autowired
    private DeviceNameConvertPipe deviceNameConvertPipe;

    @Override
    public void afterPropertiesSet() {
        pipeline.addPipe(commonActivateCheckPipe);
        pipeline.addPipe(checkDeviceAuthorizePipe);
        pipeline.addPipe(moDeviceCheckPipe);
        pipeline.addPipe(verifyChannelAndArithmeticPipe);
        pipeline.addPipe(deviceNameConvertPipe);
        pipeline.addPipe(insertDevicePipe);
        pipeline.addPipe(uploadApkVersionPipe);
        pipeline.addPipe(enableBizPipe);
        pipeline.addPipe(insertDeviceLogPipe);
        pipeline.addPipe(updateDeviceWhitePipe);
        pipeline.addPipe(monitorDataPipe);
        pipeline.addPipe(publishMsgPipe);
        pipeline.addPipe(buildResultPipe);
    }

    @Override
    protected void validCustomParameter(ActivateDeviceDTO dto) {
        BizAssert.notNull(dto.getQrCode(), "qrCode must not be null");
    }

    @Override
    protected String setKey(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return "MoDeviceActivate_" + dto.getDeviceType();
    }

    @Override
    public Integer getType() {
        return DeviceActivateEngineEnums.MO_DEVICE_QR_CODE_ACTIVATE.getCode();
    }

}
