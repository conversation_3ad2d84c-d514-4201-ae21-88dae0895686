package com.moredian.magicube.device.manager;

import cn.hutool.core.util.ObjectUtil;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dao.entity.dahua.CameraDeviceInfo;
import com.moredian.magicube.device.dao.mapper.CameraDeviceMapper;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.dto.dahua.QueryCameraDTO;
import com.moredian.magicube.device.manager.dahua.CameraDeviceManager;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
public class CameraDeviceManagerImpl implements CameraDeviceManager {

    @Autowired
    private CameraDeviceMapper cameraDeviceMapper;

    @Override
    public List<CameraDeviceInfo> list() {
        return cameraDeviceMapper.list();
    }

    @Override
    public CameraDeviceInfo getByIpAndPort(String ip, int port) {
        BizAssert.notNull(ip, "deviceIp must not be null");
        BizAssert.notNull(port, "devicePort must not be null");
        return cameraDeviceMapper.getByIpAndPort(ip, port);
    }

    @Override
    public List<CameraDeviceInfo> listByCondition(QueryCameraDTO dto) {
        return cameraDeviceMapper.listByCondition(dto);
    }

    @Override
    public Boolean updateByDeviceSn(String deviceSn, CameraDeviceInfoDTO dto) {
        BizAssert.isTrue(ObjectUtil.isNotEmpty(deviceSn), "deviceSn must not be null");
        return cameraDeviceMapper.updateByDeviceSn(deviceSn, dto);
    }
}
