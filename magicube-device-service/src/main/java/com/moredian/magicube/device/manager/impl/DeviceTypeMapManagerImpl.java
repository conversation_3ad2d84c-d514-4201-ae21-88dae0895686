package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.DeviceTypeMap;
import com.moredian.magicube.device.dao.mapper.DeviceTypeMapMapper;
import com.moredian.magicube.device.dto.type.DeviceCapacityDTO;
import com.moredian.magicube.device.manager.DeviceTypeMapManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-05-18
 */
@Service
public class DeviceTypeMapManagerImpl implements DeviceTypeMapManager {

    @Autowired
    private DeviceTypeMapMapper deviceTypeMapMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public String getValue(String mapName, String mapKey) {
        BizAssert.notBlank(mapName, "mapName must not be null");
        if (mapKey == null) {
            return null;
        }
        return deviceTypeMapMapper.getValue(mapName, mapKey);
    }

    @Override
    public Integer getDeviceCapacity(Integer deviceType) {
        String deviceCapacity = getValue(DeviceTypeConstants.DEVICE_CAPACITY_MAP, String.valueOf(deviceType));
        return StringUtils.isNotBlank(deviceCapacity) && StringUtils.isNumeric(deviceCapacity) ? Integer
            .valueOf(deviceCapacity) : null;
    }

    @Override
    public List<Integer> getDevicesCapacity(List<Integer> deviceType) {
        List<String> deviceTypes = new ArrayList<>();
        deviceType.forEach(n -> deviceTypes.add(String.valueOf(n)));
        List<DeviceTypeMap> deviceTypeMapList = getValues(DeviceTypeConstants.DEVICE_CAPACITY_MAP, deviceTypes);
        List<Integer> deviceCapacity = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceTypeMapList)) {
            List<String> stringList = deviceTypeMapList.stream().map(DeviceTypeMap::getMapValue).collect(Collectors.toList());
            stringList.forEach(n -> deviceCapacity.add(Integer.valueOf(n)));
        }
        return deviceCapacity;
    }

    @Override
    public List<DeviceCapacityDTO> getDeviceListCapacity(List<Integer> deviceTypeList) {
        if(CollectionUtils.isEmpty(deviceTypeList)){
            return Collections.emptyList();
        }
        List<String> deviceTypes = new ArrayList<>();
        deviceTypeList.forEach(n -> deviceTypes.add(String.valueOf(n)));
        List<DeviceTypeMap> deviceTypeMapList = getValues(DeviceTypeConstants.DEVICE_CAPACITY_MAP, deviceTypes);
        if(CollectionUtils.isEmpty(deviceTypeMapList)){
            return Collections.emptyList();
        }
        return deviceTypeMapList.stream().map(deviceTypeMap -> {
            DeviceCapacityDTO dto = new DeviceCapacityDTO();
            dto.setDeviceType(Integer.valueOf(deviceTypeMap.getMapKey()));
            dto.setDeviceCapacity(Integer.valueOf(deviceTypeMap.getMapValue()));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean saveDeviceCapacity(Integer deviceType, Integer deviceCapacity) {
        BizAssert.notNull(deviceType, "deviceType must not be null");
        BizAssert.isTrue(deviceCapacity != null && deviceCapacity > 0, "deviceCapacity must not be null");
        String existDeviceCapacity = getValue(DeviceTypeConstants.DEVICE_CAPACITY_MAP, String.valueOf(deviceType));
        if (StringUtils.isNotBlank(existDeviceCapacity)) {
            //修改
            deviceTypeMapMapper.updateDeviceCapacity(String.valueOf(deviceType), String.valueOf(deviceCapacity));
        } else {
            //新增
            Long id = idgeneratorService.getNextIdByTypeName(DeviceTypeMap.class.getName()).pickDataThrowException();
            DeviceTypeMap deviceTypeMap = new DeviceTypeMap();
            deviceTypeMap.setId(id);
            deviceTypeMap.setMapName(DeviceTypeConstants.DEVICE_CAPACITY_MAP);
            deviceTypeMap.setMapKey(String.valueOf(deviceType));
            deviceTypeMap.setMapValue(String.valueOf(deviceCapacity));
            deviceTypeMapMapper.insert(deviceTypeMap);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<DeviceTypeMap> getValues(String mapName, List<String> deviceTypes) {
        BizAssert.notBlank(mapName, "mapName must not be null");
        if (CollectionUtils.isEmpty(deviceTypes)) {
            return null;
        }
        return deviceTypeMapMapper.getValues(mapName, deviceTypes);
    }

    @Override
    public String getDefaultSpu(Integer deviceType) {
        return getValue(DeviceTypeConstants.DEFAULT_SPU_MAP, String.valueOf(deviceType));
    }

    @Override
    public String getConvertName(Integer deviceType) {
        return getValue(DeviceTypeConstants.CONVERT_NAME_MAP, String.valueOf(deviceType));
    }

    @Override
    public Integer getTypeByDeviceSn(String deviceSn) {
        if (StringUtils.isBlank(deviceSn)) {
            return null;
        }
        String deviceType = getValue(
            DeviceTypeConstants.DEVICE_SN_TO_TYPE_MAP, deviceSn.substring(0, 2));
        return StringUtils.isNotBlank(deviceType) && StringUtils.isNumeric(deviceType) ? Integer.valueOf(deviceType) : null;
    }

    @Override
    public Map<String/*deviceSn*/, Integer/*deviceType*/> getTypeByDeviceSnList(
        List<String> deviceSnList) {
        if (CollectionUtils.isNotEmpty(deviceSnList)) {
            // 解析SN前缀
            Map<String/*deviceSn*/, String/*deviceSnPrefix*/> deviceSnPrefixMap = Maps.newHashMap();
            for (String deviceSn : deviceSnList) {
                if (StringUtils.isNotBlank(deviceSn)) {
                    deviceSnPrefixMap.put(deviceSn, deviceSn.substring(0, 2));
                }
            }

            if (MapUtils.isNotEmpty(deviceSnPrefixMap)) {
                // 查询前缀对应设备类型
                List<String> deviceSnPrefixList = deviceSnPrefixMap.values().stream().distinct().collect(
                    Collectors.toList());
                List<DeviceTypeMap> deviceTypeMapList = deviceTypeMapMapper.getValues(
                    DeviceTypeConstants.DEVICE_SN_TO_TYPE_MAP, deviceSnPrefixList);

                if (CollectionUtils.isNotEmpty(deviceTypeMapList)) {
                    // deviceTypeMapList -> map
                    Map<String/*deviceSnPrefix*/, String/*deviceType*/> deviceSnToTypeMap = deviceTypeMapList.stream().collect(
                        Collectors.toMap(DeviceTypeMap::getMapKey, DeviceTypeMap::getMapValue, (oldValue, newValue) -> newValue));
                    // 映射结果
                    Map<String/*deviceSn*/, Integer/*deviceType*/> result = Maps.newHashMap();
                    // 根据sn前缀匹配设备类型
                    for (String deviceSn : deviceSnPrefixMap.keySet()) {
                        String deviceSnPrefix = deviceSnPrefixMap.get(deviceSn);
                        String deviceType = StringUtils.trim(deviceSnToTypeMap.get(deviceSnPrefix));
                        // 校验deviceType合法性
                        if (StringUtils.isNotBlank(deviceType) && StringUtils.isNumeric(deviceType)) {
                            result.put(deviceSn, Integer.valueOf(deviceType));
                        }
                    }
                    return result;
                }
            }

        }
        return Collections.emptyMap();
    }

}
