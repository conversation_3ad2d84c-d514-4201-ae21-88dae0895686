package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.manager.DeviceConfigManager;
import com.moredian.magicube.device.service.DeviceConfigService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@SI
public class DeviceConfigServiceImpl implements DeviceConfigService {

    @Autowired
    private DeviceConfigManager deviceConfigManager;

    @Override
    public ServiceResponse<Boolean> insertOrUpdate(Long orgId, Long deviceId, String xml) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceConfigManager.insertOrUpdate(orgId, deviceId, xml));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<String> getXmlConfig(String deviceSn) {
        ServiceResponse<String> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceConfigManager.getXmlConfig(deviceSn));
        return serviceResponse;
    }
}
