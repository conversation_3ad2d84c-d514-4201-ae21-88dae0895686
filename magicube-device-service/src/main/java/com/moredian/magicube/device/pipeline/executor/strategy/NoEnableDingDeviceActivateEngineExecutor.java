package com.moredian.magicube.device.pipeline.executor.strategy;


import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.executor.AbstractActivateDeviceEngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.ding.BindingGroupPipe;
import com.moredian.magicube.device.pipeline.pipe.ding.NoEnableDingDeviceCheckPipe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 没开通魔点门禁微应用激活的钉钉设备二维码激活引擎执行器
 *
 * <AUTHOR>
 */
@Component
public class NoEnableDingDeviceActivateEngineExecutor extends AbstractActivateDeviceEngineExecutor {

    @Autowired
    private NoEnableDingDeviceCheckPipe noEnableDingDeviceCheckPipe;

    @Autowired
    protected BindingGroupPipe bindingGroupPipe;

    @Override
    public void afterPropertiesSet() {
        pipeline.addPipe(commonActivateCheckPipe);
        pipeline.addPipe(checkDeviceAuthorizePipe);
        pipeline.addPipe(noEnableDingDeviceCheckPipe);
        pipeline.addPipe(verifyChannelAndArithmeticPipe);
        pipeline.addPipe(insertDevicePipe);
        pipeline.addPipe(uploadApkVersionPipe);
        pipeline.addPipe(insertDeviceLogPipe);
        pipeline.addPipe(updateDeviceWhitePipe);
        pipeline.addPipe(monitorDataPipe);
        pipeline.addPipe(publishMsgPipe);
        pipeline.addPipe(buildResultPipe);
    }

    @Override
    protected void validCustomParameter(ActivateDeviceDTO dto) {

    }

    @Override
    protected String setKey(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return "NoEnableDingDeviceActivate_" + dto.getDeviceSn();
    }

    @Override
    public Integer getType() {
        return DeviceActivateEngineEnums.NO_ENABLE_DING_DEVICE_QR_CODE_ACTIVATE.getCode();
    }

}
