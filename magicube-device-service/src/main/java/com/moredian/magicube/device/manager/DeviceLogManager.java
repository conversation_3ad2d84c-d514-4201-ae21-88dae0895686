package com.moredian.magicube.device.manager;


import com.moredian.magicube.device.dao.entity.DeviceLog;
import java.util.List;

/**
 * 设备日志
 *
 * <AUTHOR>
 */
public interface DeviceLogManager {

    /**
     * 新增设备日志
     *
     * @param deviceLog 设备日志信息
     * @return
     */
    Long insert(DeviceLog deviceLog);

    /**
     * 更新设备激活日志的操作人Id
     *
     * @param deviceLog 设备日志信息
     * @return 设备日志Id
     */
    Boolean updateDeviceLogOperatorId(DeviceLog deviceLog);

    /**
     * 根据设备日志Id获取设备日志信息
     *
     * @param orgId       机构号
     * @param deviceLogId 设备日志Id
     * @return
     */
    DeviceLog getByOrgIdAndId(Long orgId, Long deviceLogId);

    /**
     * 根据设备Id获取设备日志信息列表
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    List<DeviceLog> listByOrgIdAndDeviceId(Long orgId, Long deviceId);

    /**
     * 根据设备sn获取设备日志信息列表
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    List<DeviceLog> listByOrgIdAndDeviceSn(Long orgId, String deviceSn);

    /**
     * 根据机构号获取设备日志信息类型去重列表
     *
     * @param orgId 机构号
     * @return
     */
    List<Integer> listDistinctDeviceTypeByOrgId(Long orgId);

    /**
     * 根据机构号统计设备日志数量
     *
     * @param orgId 机构号
     * @return
     */
    Integer countByOrgId(Long orgId);

    /**
     * 根据设备sn获取设备日志信息列表
     *
     * @param deviceSn 设备sn
     * @return
     */
    List<DeviceLog> listByDeviceSn(String deviceSn);
}
