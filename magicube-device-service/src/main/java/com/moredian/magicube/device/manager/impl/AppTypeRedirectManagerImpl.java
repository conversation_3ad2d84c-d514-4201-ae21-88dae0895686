package com.moredian.magicube.device.manager.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.AppTypeRedirect;
import com.moredian.magicube.device.dao.mapper.AppTypeRedirectMapper;
import com.moredian.magicube.device.dto.device.AppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.PageAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.QueryAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.SaveAppTypeRedirectDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.AppTypeRedirectManager;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/18
 */
@Component
@Slf4j
public class AppTypeRedirectManagerImpl implements AppTypeRedirectManager {

    @Autowired
    private AppTypeRedirectMapper appTypeRedirectMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public Long save(SaveAppTypeRedirectDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getAppType(), "appType must not be null");
        BizAssert.notBlank(dto.getRedirectUrl(), "redirectUrl must not blank");
        Long id;
        if (dto.getId() != null) {
            //查询记录是否存在
            AppTypeRedirect query = new AppTypeRedirect();
            query.setId(dto.getId());
            AppTypeRedirect exist = appTypeRedirectMapper.getOne(query);
            BizAssert.notNull(exist, DeviceErrorCode.APP_TYPE_REDIRECT_NOT_EXIST, DeviceErrorCode.APP_TYPE_REDIRECT_NOT_EXIST.getMessage());
            //校验appType是否重复
            if (!exist.getAppType().equals(dto.getAppType())) {
                AppTypeRedirect queryRepeat = new AppTypeRedirect();
                queryRepeat.setAppType(dto.getAppType());
                AppTypeRedirect appTypeRepeat = appTypeRedirectMapper.getOne(queryRepeat);
                BizAssert.isNull(appTypeRepeat, DeviceErrorCode.APP_TYPE_REDIRECT_EXISTS, DeviceErrorCode.APP_TYPE_REDIRECT_EXISTS.getMessage());
            }
            exist.setAppType(dto.getAppType());
            exist.setRedirectUrl(dto.getRedirectUrl());
            appTypeRedirectMapper.update(exist);
            id = exist.getId();
        } else {
            AppTypeRedirect appTypeRedirect = new AppTypeRedirect();
            appTypeRedirect.setAppType(dto.getAppType());
            AppTypeRedirect exist = appTypeRedirectMapper.getOne(appTypeRedirect);
            BizAssert.isNull(exist, DeviceErrorCode.APP_TYPE_REDIRECT_EXISTS, DeviceErrorCode.APP_TYPE_REDIRECT_EXISTS.getMessage());
            appTypeRedirect.setRedirectUrl(dto.getRedirectUrl());
            id = idgeneratorService.getNextIdByTypeName(AppTypeRedirect.class.getName()).pickDataThrowException();
            appTypeRedirect.setId(id);
            appTypeRedirectMapper.add(appTypeRedirect);
        }
        return id;
    }

    @Override
    public AppTypeRedirectDTO detail(QueryAppTypeRedirectDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        if (dto.getId() == null && dto.getAppType() == null && StringUtils.isBlank(dto.getRedirectUrl())) {
            return null;
        }
        AppTypeRedirect query = new AppTypeRedirect();
        query.setId(dto.getId());
        query.setAppType(dto.getAppType());
        query.setRedirectUrl(dto.getRedirectUrl());
        String env = dto.getEnv();
        if(Objects.nonNull(env)) {
            query.setEnv(env);
        }
        AppTypeRedirect exist = appTypeRedirectMapper.getOne(query);
        AppTypeRedirectDTO appTypeRedirectDTO = null;
        if (exist != null) {
            appTypeRedirectDTO = convertToAppTypeRedirectDTO(exist);
        }
        return appTypeRedirectDTO;
    }

    @Override
    public Boolean deleteById(Long id) {
        BizAssert.notNull(id, "id must not be null");
        appTypeRedirectMapper.deleteById(id);
        return Boolean.TRUE;
    }

    @Override
    public Pagination<AppTypeRedirectDTO> page(PageAppTypeRedirectDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        Page<AppTypeRedirect> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(),
                dto.getPageSize() == null ? 20 : dto.getPageSize())
                .setOrderBy("gmt_create DESC")
                .doSelectPage(() -> appTypeRedirectMapper.page(dto));
        Pagination pagination = new Pagination();
        pagination.setData(Collections.emptyList());
        if (page != null && CollectionUtils.isNotEmpty(page.getResult())) {
            List<AppTypeRedirectDTO> appTypeRedirectDTOList = page.getResult().stream()
                    .map(appTypeRedirect -> convertToAppTypeRedirectDTO(appTypeRedirect)).collect(Collectors.toList());
            pagination.setData(appTypeRedirectDTOList);
        }
        pagination.setTotalCount((int) page.getTotal());
        pagination.setPageNo(page.getPageNum());
        pagination.setPageSize(page.getPageSize());
        return pagination;
    }

    private AppTypeRedirectDTO convertToAppTypeRedirectDTO(AppTypeRedirect appTypeRedirect) {
        if (appTypeRedirect == null) {
            return null;
        }
        AppTypeRedirectDTO appTypeRedirectDTO = new AppTypeRedirectDTO();
        appTypeRedirectDTO.setId(appTypeRedirect.getId());
        appTypeRedirectDTO.setAppType(appTypeRedirect.getAppType());
        appTypeRedirectDTO.setRedirectUrl(appTypeRedirect.getRedirectUrl());
        appTypeRedirectDTO.setGmtCreate(appTypeRedirect.getGmtCreate());
        appTypeRedirectDTO.setGmtModify(appTypeRedirect.getGmtModify());
        return appTypeRedirectDTO;
    }
}
