package com.moredian.magicube.device.subscriber;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.conf.core.client.BeeConfNewClient;
import com.moredian.device.pipeline.api.DevicePipelineStateService;
import com.moredian.device.pipeline.dto.response.DevicePipelineStateListResp;
import com.moredian.device.pipeline.dto.response.DeviceSceneTypeInfoResp;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.common.enums.SpaceTypeEnums;
import com.moredian.magicube.common.model.msg.device.DeviceActiveEvent;
import com.moredian.magicube.common.model.msg.device.DeviceAppVersionReportMsg;
import com.moredian.magicube.device.constant.BeeConfConfigConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import com.moredian.magicube.device.dao.entity.DeviceVersion;
import com.moredian.magicube.device.dao.mapper.DeviceApkVersionMapper;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceFlagEnum;
import com.moredian.magicube.device.enums.DeviceRelateAppFixedBIzTypeEnum;
import com.moredian.magicube.device.enums.DeviceSourceEnum;
import com.moredian.magicube.device.manager.DeviceAppRelationConfigManager;
import com.moredian.magicube.device.manager.DeviceAppRelationFixedConfigManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.MigrationDeviceManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.message.DeviceChangeStateMsg;
import com.moredian.space.message.SaveTreeDeviceRelationMsg;
import com.moredian.space.message.UpdateTreeDeviceRelationMsg;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.SpaceTreeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备关联应用mq监听
 * 设备关联应用整体逻辑：
 * <p>
 * 关联应用分为固定配置和默认配置两张表，固定配置：DeviceAppRelationFixedConfig，默认配置：DeviceAppRelationConfig
 * 设备在进行以下操作时会发出消息触发设备关联应用的操作：
 * 1.激活
 * 2.版本上报
 * 3.空间切换 （先不实现）
 * 关联逻辑：
 * 1.首先根据设备sn去固定配置表里面找对应的记录，
 * 2.如果不存在固定配置，则从默认配置中去寻找 寻找规则: =spaceType，=appType，<=versionCode
 * 找到对应的记录后将、appCode、appCodeList维护进设备表的对应字段即可，结束。
 * </p>
 */
@Slf4j
@Component
public class DeviceAppRelationMsgSubscriber {


    @Resource
    private DeviceApkVersionMapper deviceApkVersionMapper;
    @Resource
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;
    @Resource
    private DeviceManager deviceManager;
    @Resource
    private DeviceAppRelationFixedConfigManager deviceAppRelationFixedConfigManager;
    @Resource
    private MigrationDeviceManager migrationDeviceManager;
    @SI
    private SpaceTreeService spaceTreeService;
    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;
    @SI
    private DevicePipelineStateService devicePipelineStateService;


    /**
     * 设备激活消息
     *
     * @param msg msg
     */
    @Subscribe
    public void subDeviceActiveMsg(DeviceActiveEvent msg) {

        log.info("收到设备激活消息，首次关联应用操作开始: {}", msg.toString());
        // 一键上钉
        migrationDeviceManager.subDeviceActiveMsg(msg.getOrgId(),msg.getDeviceId(),msg.getDeviceSn());

        if (deviceRelateFixedApp(msg.getOrgId(),msg.getDeviceId(),msg.getDeviceSn())){
            return;
        }

        Device device = deviceManager.getByDeviceSn(msg.getDeviceSn());

        if (device.getDeviceFlag() == DeviceFlagEnum.FX.getValue()) {
            log.info("设备不是行业设备 不处理");
            //分销设备不处理
            return;
        }

        boolean sceneTypeToAppCode = isSceneTypeToAppCode(msg.getOrgId(), msg.getDeviceId(), msg.getDeviceSn());
        if (sceneTypeToAppCode) {
            log.warn("收到设备激活消息，对应appType使用场景关联应用。msg=>{}", msg);
            return;
        }

        Integer spaceType = -1;
        //查询设备appType和versionCode
        DeviceVersion deviceVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(msg.getDeviceSn());
        if (deviceVersion == null) {
            log.warn("设备版本信息为空,不做关联应用处理，deviceSn:{},timestamp:{}", msg.getDeviceSn(), System.currentTimeMillis());
            return;
        }
        Integer appType = deviceVersion.getAppType();
        Integer versionCode = deviceVersion.getVersionCode();
        if (msg.getSpaceType() != null){
            spaceType = msg.getSpaceType();
        }else {
            //查询空间类型
            TreeDTO treeDTO = spaceTreeService.getById(msg.getTreeId(), msg.getOrgId()).pickDataThrowException();
            if (treeDTO != null) {
                spaceType = treeDTO.getTags().get(0).getSpaceType();
            }
        }
        DeviceAppRelationConfig deviceAppRelationConfig = getDeviceAppRelationConfig(spaceType, appType, versionCode);
        if (deviceAppRelationConfig == null) {
            log.warn("设备激活更新设备关联应用配置不存在,orgId:{},spaceType:{},appType:{},versionCode:{}", msg.getOrgId(), spaceType, appType, versionCode);
            return;
        }

        //更新设备相关信息
        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
        updateDeviceDTO.setOrgId(msg.getOrgId());
        updateDeviceDTO.setDeviceId(msg.getDeviceId());
        updateDeviceDTO.setAppCode( deviceAppRelationConfig.getDefaultAppCode());
        updateDeviceDTO.setAppCodeList(deviceAppRelationConfig.getAvailableAppCodeList());
        boolean update = deviceManager.update(updateDeviceDTO);
        log.info("设备激活更新关联应用结果:{}", update);

    }

    /**
     * 设备关联固定配置应用
     * @param orgId 机构id
     * @param deviceId 设备id
     * @param deviceSn 设备sn
     * @return 是否存在固定配置
     */
    private boolean deviceRelateFixedApp(Long orgId, Long deviceId,String deviceSn) {
        //先判断固定配置是否存在 如果存在就直接设置固定配置的appCode即可
        DeviceAppRelationFixedConfig fixedConfig = deviceAppRelationFixedConfigManager.getByBizTypeAndId(
                DeviceRelateAppFixedBIzTypeEnum.DEVICE_SN.getCode(), deviceSn);

        if (fixedConfig != null) {
            //更新设备相关信息
            UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
            updateDeviceDTO.setOrgId(orgId);
            updateDeviceDTO.setDeviceId(deviceId);
            updateDeviceDTO.setAppCode(fixedConfig.getDefaultAppCode());
            updateDeviceDTO.setAppCodeList(fixedConfig.getAvailableAppCodeList());
            boolean update = deviceManager.update(updateDeviceDTO);
            log.info("设备更新固定关联应用结果:{}", update);
            return true;
        }
        return false;
    }

    /**
     * 设备切换空间更新关联应用
     *
     * @param msg msg
     */
    @Subscribe
    public void subUpdateTreeDeviceRelationMsgV2(DeviceChangeStateMsg msg) {
        log.info("收到设备切换空间消息，开始更新关联应用: {}", msg.toString());
        Device device = deviceManager.getByOrgIdAndId(msg.getOrgId(), msg.getDeviceId());
        //先判断固定配置是否存在 如果存在就直接设置固定配置的appCode即可
        DeviceAppRelationFixedConfig fixedConfig = deviceAppRelationFixedConfigManager.getByBizTypeAndId(
                DeviceRelateAppFixedBIzTypeEnum.DEVICE_SN.getCode(),device.getDeviceSn());

        if (fixedConfig != null) {
            //更新设备相关信息
            UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
            updateDeviceDTO.setOrgId(msg.getOrgId());
            updateDeviceDTO.setDeviceId(device.getDeviceId());
            updateDeviceDTO.setAppCode(fixedConfig.getDefaultAppCode());
            updateDeviceDTO.setAppCodeList(fixedConfig.getAvailableAppCodeList());
            boolean update = deviceManager.update(updateDeviceDTO);
            log.info("设备切换空间更新固定关联应用结果:{}", update);
            return;
        }

        boolean sceneTypeToAppCode = isSceneTypeToAppCode(device.getOrgId(), device.getDeviceId(), device.getDeviceSn());
        if (sceneTypeToAppCode) {
            log.warn("收到设备切换空间消息，对应appType使用场景关联应用。msg=>{}", msg);
            return;
        }

        Long treeId = msg.getNewSpaceId();
        Long orgId = msg.getOrgId();

        TreeDTO treeDTO = spaceTreeService.getById(treeId, orgId).pickDataThrowException();
        if (treeDTO == null) {
            log.error("【subUpdateTreeDeviceRelationMsgV2】空间树不存在，treeId:{}", treeId);
            return;
        }
        //最新的空间类型
        Integer newSpaceType = treeDTO.getTags().get(0).getSpaceType();

        //查询设备版本
        DeviceVersion deviceVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(device.getDeviceSn());
        if (deviceVersion == null) {
            log.warn("设备版本信息为空,切换空间不做关联应用处理，deviceSn:{},timestamp:{}", device.getDeviceSn(), System.currentTimeMillis());
            return;
        }

        //查询最新的设备可承载应用信息
        DeviceAppRelationConfig deviceAppRelationConfig =
                getDeviceAppRelationConfig(newSpaceType, deviceVersion.getAppType(), deviceVersion.getVersionCode());

        if (deviceAppRelationConfig == null) {
            log.warn("设备切换空间，空间类型appType版本配置不存在,orgId:{},spaceType:{},appType:{},versionCode:{}",
                    msg.getOrgId(),newSpaceType, deviceVersion.getAppType(), deviceVersion.getVersionCode());
            return;
        }
        //更新设备相关信息
        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
        updateDeviceDTO.setOrgId(msg.getOrgId());
        updateDeviceDTO.setDeviceId(msg.getDeviceId());
        updateDeviceDTO.setAppCode(deviceAppRelationConfig.getDefaultAppCode());
        updateDeviceDTO.setAppCodeList(deviceAppRelationConfig.getAvailableAppCodeList());
        //取配置表里最新的versionCode
        boolean update = deviceManager.update(updateDeviceDTO);
        log.info("设备切换空间更新关联应用结果:{}", update);

    }

    /**
     * 设备切换空间更新关联应用
     *
     * @param msg msg
     */
    @Subscribe
    @Deprecated
    public void subUpdateTreeDeviceRelationMsg(UpdateTreeDeviceRelationMsg msg) {
        log.info("收到设备切换空间消息，开始更新关联应用: {}", msg.toString());

    }

    @Subscribe
    public void subSaveTreeDeviceRelationMsg(SaveTreeDeviceRelationMsg msg) {
        log.info("钉base首次绑定空间消息，开始更新关联应用: {}", msg.toString());
        //三方设备直接返回不做处理
        if (Objects.equals(msg.getDeviceSource(), DeviceSourceEnum.THIRD.getCode())) {
            log.warn("三方设备不做处理");
            return;
        }
        Device device = deviceManager.getByOrgIdAndId(msg.getOrgId(), Long.parseLong(msg.getDeviceId()));
        if (deviceRelateFixedApp(msg.getOrgId(),device.getDeviceId(),device.getDeviceSn())){
            return;
        }

        boolean sceneTypeToAppCode = isSceneTypeToAppCode(device.getOrgId(), device.getDeviceId(), device.getDeviceSn());
        if (sceneTypeToAppCode) {
            log.warn("钉base首次绑定空间消息，对应appType使用场景关联应用。msg=>{}", msg);
            return;
        }

        //查询设备版本
        DeviceVersion deviceVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(device.getDeviceSn());
        if (deviceVersion == null) {
            log.warn("设备版本信息为空,钉base首次绑定空间不做关联应用处理，deviceSn:{},timestamp:{}", device.getDeviceSn(), System.currentTimeMillis());
            return;
        }

        Integer spaceType = -1;

        TreeDTO treeDTO = spaceTreeService.getById(msg.getTreeId(), msg.getOrgId()).pickDataThrowException();
        if (treeDTO != null) {
            spaceType = treeDTO.getTags().get(0).getSpaceType();
        }

        //查询最新的设备可承载应用信息
        DeviceAppRelationConfig deviceAppRelationConfig =
                getDeviceAppRelationConfig(spaceType, deviceVersion.getAppType(), deviceVersion.getVersionCode());

        if (deviceAppRelationConfig == null) {
            log.warn("钉base首次绑定空间，空间类型appType版本配置不存在,orgId:{},spaceType:{},appType:{},versionCode:{}",
                    msg.getOrgId(), spaceType, deviceVersion.getAppType(), deviceVersion.getVersionCode());
            return;
        }
        //更新设备相关信息
        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
        updateDeviceDTO.setOrgId(msg.getOrgId());
        updateDeviceDTO.setDeviceId(device.getDeviceId());
        updateDeviceDTO.setAppCode(deviceAppRelationConfig.getDefaultAppCode());
        updateDeviceDTO.setAppCodeList(deviceAppRelationConfig.getAvailableAppCodeList());
        boolean update = deviceManager.update(updateDeviceDTO);
        log.info("钉base首次绑定空间更新关联应用结果:{}", update);

    }


    /**
     * 设备上报版本号时，根据绑定空间关联应用。
     * 教室不支持场景切换，需要特殊处理，如果绑定空间是教室，先查一下设备当前运行模式，如果是教室模式才处理
     */
    @Subscribe
    public void subDeviceAppVersionReportMsg(DeviceAppVersionReportMsg msg) {
        log.info("收到设备版本上报消息，开始更新关联应用: {}", msg.toString());

        //先判断固定配置是否存在 如果存在就直接设置固定配置的appCode即可
        if (deviceRelateFixedApp(msg.getOrgId(),msg.getDeviceId(),msg.getDeviceSn())){
            return;
        }
        //appType和versionCode可能为空 如果为空则不做处理，等待下次上报
        if (msg.getAppType() == null || msg.getVersionCode() == null) {
            log.warn("设备appType或versionCode为空，不做处理。deviceSN:{},appType:{},versionCode:{}",
                    msg.getDeviceSn(), msg.getAppType(), msg.getVersionCode());
            return;
        }
        Device device = deviceManager.getByDeviceSn(msg.getDeviceSn());
        //查询设备版本
        DeviceVersion deviceVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(msg.getDeviceSn());
        //行业设备 查询sceneType——不为空就不更新了
        DevicePipelineStateListResp devicePipelineStateListResp = devicePipelineStateService.getDeviceByDeviceSn(msg.getOrgId(), msg.getDeviceSn()).pickDataThrowException();
        if (device.getDeviceFlag() == DeviceFlagEnum.HY.getValue() && devicePipelineStateListResp != null && devicePipelineStateListResp.getSceneType() != null){
            log.warn("行业设备已有场景类型，不做处理。deviceSN:{},sceneType:{}", msg.getDeviceSn(), devicePipelineStateListResp.getSceneType());
            return;
        }

        Integer spaceType = -1;
        //查询空间类型
        TreeDeviceRelationDTO treeDeviceRelationDTO =
                spaceTreeDeviceRelationService.getByOrgIdAndDeviceId(msg.getOrgId(), msg.getDeviceId().toString()).pickDataThrowException();

        if (treeDeviceRelationDTO != null) {
            TreeDTO treeDTO = spaceTreeService.getById(treeDeviceRelationDTO.getTreeId(), msg.getOrgId()).pickDataThrowException();
            if (treeDTO != null) {
                spaceType = treeDTO.getTags().get(0).getSpaceType();
            }
        }

        // 教室特殊处理，如果绑到教室下，但是不是教室模式，就不处理
        if (Objects.equals(spaceType, SpaceTypeEnums.CLASSROOM_MODEL.getValue())) {
            DeviceSceneTypeInfoResp sceneType = devicePipelineStateService.getSceneTypeInfoByDeviceSn(
                msg.getOrgId(), msg.getDeviceSn()).pickDataThrowException();
            if (!Objects.equals(SceneTypeEnums.CLASSROOM_MODEL.getValue(), sceneType.getSceneType())) {
                log.warn("空间教室不支持场景切换，且绑定空间和运行模式不同，deviceSn=>{},treeId=>{}", msg.getDeviceSn(), msg.getOrgId());
                return;
            }
        }

        DeviceAppRelationConfig deviceAppRelationConfig = getDeviceAppRelationConfig(spaceType, msg.getAppType(), msg.getVersionCode());
        if (deviceAppRelationConfig == null) {
            log.warn("设备版本上报空间，空间类型appType版本配置不存在,orgId:{},spaceType:{},appType:{},versionCode:{}",
                    msg.getOrgId(),spaceType, deviceVersion.getAppType(), deviceVersion.getVersionCode());
            return;
        }
        List<String> appCode = CharSequenceUtil.split(device.getAppCodeList(), StrPool.COMMA);
        List<String> appRelationCodeList = CharSequenceUtil.split(deviceAppRelationConfig.getAvailableAppCodeList(), StrPool.COMMA);
        // 判断默认code应用列表是否包含根据场景选择的应用，包含则应用场景关联的应用，不包含则只应用默认code应用列表
        if (!CollUtil.containsAll(appRelationCodeList, appCode)) {
            appCode = appRelationCodeList;
        }else {
            if (device.getDeviceFlag() == DeviceFlagEnum.FX.getValue() && devicePipelineStateListResp != null && devicePipelineStateListResp.getSceneType() != null){
                log.warn("塔上分销设备已有场景类型且应用场景code合理，不做处理。deviceSN:{},sceneType:{}, appCode:{}, appRelationCodeList:{}",
                        msg.getDeviceSn(), devicePipelineStateListResp.getSceneType(), appCode, appRelationCodeList);
                return;
            }
        }
        //更新设备相关信息
        UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
        updateDeviceDTO.setOrgId(msg.getOrgId());
        updateDeviceDTO.setDeviceId(msg.getDeviceId());
        updateDeviceDTO.setAppCode(deviceAppRelationConfig.getDefaultAppCode());
        updateDeviceDTO.setAppCodeList(String.join(",", appCode));
        boolean update = deviceManager.update(updateDeviceDTO);
        log.info("设备版本上报更新关联应用结果:{}", update);

    }

    /**
     * 校验空间类型是否都相同，如果都相同，则返回true；否则返回false。
     *
     * @param treeDTOS 空间树列表
     * @return true/false
     */
    public static boolean checkConsistentSpaceType(List<TreeDTO> treeDTOS) {
        // 使用Stream API提取每个TreeDTO的tags列表的第一条TagDTO的spaceType
        List<Integer> spaceTypes = treeDTOS.stream().map(treeDTO -> treeDTO.getTags().get(0).getSpaceType()) // 直接获取tags列表的第一条TagDTO的spaceType
                .collect(Collectors.toList()); // 收集所有的spaceType到一个列表中

        // 检查列表中所有的spaceType是否都相同
        Integer firstSpaceType = spaceTypes.get(0); // 获取第一个spaceType作为基准
        return spaceTypes.stream().allMatch(spaceType -> Objects.equals(firstSpaceType, spaceType));
    }


    /**
     * 获取最新的版本配置
     *
     * @param spaceType   spaceType
     * @param appType     appType
     * @param versionCode versionCode
     * @return DeviceAppRelationConfig
     */
    private DeviceAppRelationConfig getDeviceAppRelationConfig(Integer spaceType, Integer appType, Integer versionCode) {
        if (spaceType == null){
            spaceType = -1;
        }
        List<DeviceAppRelationConfig> deviceAppRelationConfigs = deviceAppRelationConfigManager
                .selectBySpaceTypeAndAppTypeAndVersionCode(spaceType, appType, versionCode);
        if (CollectionUtils.isEmpty(deviceAppRelationConfigs)) {
            log.warn("未找到对应用配置信息,spaceType:{},appType:{},versionCode:{}", spaceType, appType, versionCode);
            return null;
        }
        return deviceAppRelationConfigs.get(0);
    }


    /**
     * 通过 appType 去判断是否使用场景去关联应用
     * @param orgId
     * @param deviceId
     * @param deviceSn
     * @return
     */
    private boolean isSceneTypeToAppCode(Long orgId, Long deviceId, String deviceSn) {
        List<DeviceVersion> select = deviceApkVersionMapper.getBatchDeviceApkVersion(Collections.singletonList(deviceSn));
        if (CollectionUtils.isEmpty(select)) {
            return Boolean.FALSE;
        }
        DeviceVersion newVersion = select.get(0);

        // 查询出来的是新版本的versionCode
        String versionCode = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE,
                BeeConfConfigConstants.DEVICE_APPCODE_BY_SCENE_TYPE,
                String.valueOf(newVersion.getAppType()));
        // 没有表示对应的 appType 还是根据空间关联应用
        if (StringUtil.isBlank(versionCode)) {
            return Boolean.FALSE;
        }

        Integer newV = newVersion.getVersionCode();
        Integer confV = Integer.valueOf(versionCode);

        // 当前版本大于等于配置版本，表示使用场景去关联应用
        return newV >= confV;
    }


}
