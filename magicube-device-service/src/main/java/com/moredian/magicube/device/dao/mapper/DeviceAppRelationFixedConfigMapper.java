package com.moredian.magicube.device.dao.mapper;

import java.util.List;

import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;
import org.apache.ibatis.annotations.Mapper;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DeviceAppRelationFixedConfigMapper {


    /**
     *
     * 条件查询列表
     * @param deviceAppRelationConfigDTO dto
     * @return 符合条件记录
     */
    List<DeviceAppRelationFixedConfig> selectByConditions(QueryDeviceAppRelationConfigDTO deviceAppRelationConfigDTO);

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    List<DeviceAppRelationFixedConfig> listAll();

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    DeviceAppRelationFixedConfig getById(Long id);

    /**
     * 根据bizType、bizId查询
     *
     * @param bizType 业务类型
     * @param bizId 业务id
     * @return 返回记录，没有返回null
     */
    DeviceAppRelationFixedConfig getByBizTypeAndId(@Param("bizType") Integer bizType,@Param("bizId") String bizId);

    /**
     * 新增，插入所有字段
     *
     * @param deviceAppRelationFixedConfig 新增的记录
     * @return 返回影响行数
     */
    int insert(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);

    /**
     * 修改所有字段
     *
     * @param deviceAppRelationFixedConfig 修改的记录
     * @return 返回影响行数
     */
    int update(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);

    /**
     * 修改不为null字段
     *
     * @param deviceAppRelationFixedConfig 修改的记录
     * @return 返回影响行数
     */
    int updateSelective(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);

    /**
     * 删除记录
     *
     * @param id 待删除的记录id
     * @return 返回影响行数
     */
    int delete(Long id);

    /**
     * 根据type和id删除
     *
     * @param deviceAppRelationFixedConfig 待删除的记录
     * @return 返回影响行数
     */
    int deleteByBizTypeAndId(DeviceAppRelationFixedConfig deviceAppRelationFixedConfig);


}