<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceAccountMapper">

    <resultMap type="com.moredian.magicube.device.dao.entity.DeviceAccount" id="DeviceAccount">
        <result property="deviceAccountId" column="device_account_id" jdbcType="BIGINT"/>
        <result property="accountId" column="account_id" jdbcType="BIGINT"/>
        <result property="sn" column="sn" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="OTHER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModify" column="gmt_modify" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="sql_table">
        hive_device_account
    </sql>

    <sql id="sql_columns">
        device_account_id,
        account_id,
        sn,
        status,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{deviceAccountId},
        #{accountId},
        #{sn},
        #{status},
        now(3),
        now(3)
    </sql>

    <sql id="sql_where">
        <where>
            <if test="deviceAccountId != null">
                and device_account_id = #{deviceAccountId}
            </if>
            <if test="accountId != null">
                and account_id = #{accountId}
            </if>
            <if test="sn != null and sn != ''">
                and sn = #{sn}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="gmtCreate != null">
                and gmt_create = #{gmtCreate}
            </if>
            <if test="gmtModify != null">
                and gmt_modify = #{gmtModify}
            </if>
        </where>
    </sql>

    <sql id="sql_set">
        <set>
            <if test="accountId != null">
                account_id = #{accountId},
            </if>
            <if test="sn != null and sn != ''">
                sn = #{sn},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate},
            </if>
            <if test="gmtModify != null">
                gmt_modify = now(3),
            </if>
        </set>
    </sql>

    <!--新增所有列-->
    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceAccount">
        insert into
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        ) values (
        <include refid="sql_values"/>
        )
    </insert>

    <insert id="insertBatch">
        insert into
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        ) values
        <foreach collection="list" close="" index="index" item="item" open="" separator=",">
            (
            #{item.deviceAccountId},
            #{item.accountId},
            #{item.sn},
            #{item.status},
            now(3),
            now(3)
            )
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceAccount">
        update
        <include refid="sql_table"/>
        <include refid="sql_set"/>
        <include refid="sql_where"/>
    </update>

    <!--通过主键删除-->
    <delete id="delete" parameterType="com.moredian.magicube.device.dao.entity.DeviceAccount">
        delete from
        <include refid="sql_table"/>
        <include refid="sql_where"/>
    </delete>

    <!--通过实体作为筛选条件查询-->
    <select id="query" resultMap="DeviceAccount" parameterType="com.moredian.magicube.device.dao.entity.DeviceAccount">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        <include refid="sql_where"/>
    </select>

</mapper>