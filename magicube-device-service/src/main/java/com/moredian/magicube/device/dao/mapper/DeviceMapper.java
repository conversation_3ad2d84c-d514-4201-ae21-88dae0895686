package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.PageQueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.QuerySpecificDeviceDTO;
import java.util.List;

import com.moredian.magicube.device.manager.bo.QueryPageDeviceBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备相关
 *
 * <AUTHOR>
 */

@Mapper
public interface DeviceMapper {

    /**
     * 新增设备
     *
     * @param device 信息设备信息
     * @return
     */
    int insert(Device device);

    /**
     * 批量新增设备
     * @param devices
     * @return
     */
    int batchInsert(@Param("devices") List<Device> devices);

    /**
     * 更新设备
     *
     * @param device 更新设备信息
     * @return
     */
    int update(Device device);

    /**
     * 批量更新设备
     *
     * @param devices 更新设备信息列表
     * @return
     */
    int batchUpdate(@Param("devices") List<Device> devices);

    /**
     * 批量更新设备承载应用相关不为空的字段
     *
     * @param devices 更新设备信息列表
     * @return
     */
    int batchUpdateDeviceRelateApp(@Param("devices") List<Device> devices);

    /**
     * 根据设备sn获取设备信息
     *
     * @param deviceSn 设备sn
     * @return
     */
    Device getByDeviceSn(@Param("deviceSn") String deviceSn);

    /**
     * 根据机构号和设备sn获取设备信息
     *
     * @param orgId    机构号
     * @param deviceSn 设备sn
     * @return
     */
    Device getByOrgAndDeviceSn(@Param("orgId") Long orgId, @Param("deviceSn") String deviceSn);

    /**
     * 根据设备sn列表查询设备信息
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    List<Device> listByDeviceSns(@Param("deviceSns") List<String> deviceSns);

    /**
     * 根据机构号和设备sn列表获取设备信息
     *
     * @param orgId     机构号
     * @param deviceSns 设备sn列表
     * @return
     */
    List<Device> listByOrgIdAndDeviceSns(@Param("orgId") Long orgId,
        @Param("deviceSns") List<String> deviceSns);

    /**
     * 根据设备Id获取设备信息
     *
     * @param deviceId 设备Id
     * @return
     */
    Device getById(@Param("deviceId") Long deviceId);

    /**
     * 根据机构号和设备Id获取设备信息
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    Device getByOrgIdAndId(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId);

    /**
     * 根据设备Id列表查询设备信息
     *
     * @param deviceIds 设备Id列表
     * @return
     */
    List<Device> listByIds(@Param("deviceIds") List<Long> deviceIds);

    /**
     * 根据机构号和设备Id列表获取设备信息列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<Device> listByOrgIdAndIds(@Param("orgId") Long orgId,
        @Param("deviceIds") List<Long> deviceIds);

    /**
     * 查询该机构下所有设备信息
     *
     * @param orgId 机构号
     * @return
     */
    List<Device> listByOrgId(Long orgId);

    /**
     * 根据设备Id删除设备信息
     *
     * @param deviceId 设备Id
     * @return
     */
    int deleteById(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId);

    /**
     * 根据条件查询设备信息列表
     *
     * @param queryDeviceDTO 查询条件
     * @return
     */
    List<Device> listByCondition(QueryDeviceDTO queryDeviceDTO);

    /**
     * 根据条件查询设备信息列表
     *
     * @param queryDeviceDTO 查询条件
     * @return
     */
    List<Device> listPageByCondition(QueryDeviceDTO queryDeviceDTO);

    /**
     * 查询根节点iot设备列表
     *
     * @param dto 查询条件
     * @return
     */
    List<Device> listIotDevice(QueryIotDeviceDTO dto);

    /**
     * 根据条件查询Iot设备信息列表
     *
     * @param dto 查询条件
     * @return
     */
    List<Device> listIotByConditionPage(PageQueryIotDeviceDTO dto);

    /**
     * 根据条件查询设备信息列表
     *
     * @param queryDeviceDTO 查询条件
     * @return
     */
    List<Device> listPageByConditionV2(QueryDeviceDTO queryDeviceDTO);

    /**
     * 多租户-根据条件查询设备信息列表
     *
     * @param queryDeviceDTO 查询条件
     * @return
     */
    List<Device> parkPageByCondition(QueryDeviceDTO queryDeviceDTO);

    /**
     * 按设备名称查询设备详情
     *
     * @param orgId      机构号
     * @param deviceName 设备名称
     * @return
     */
    Device getByOrgIdAndDeviceName(@Param("orgId") Long orgId,
        @Param("deviceName") String deviceName);

    /**
     * 按设备名称查询模糊查询设备ID列表
     *
     * @param orgId    机构号
     * @param keywords 设备名称
     * @return
     */
    List<Long> listDeviceIdByLikeName(@Param("orgId") Long orgId,
        @Param("keywords") String keywords, @Param("iotDeviceTypes") List<Integer> iotDeviceTypes);

    /**
     * 根据设备Id列表查询设备名称列表
     *
     * @param deviceIds 设备Id列表
     * @return
     */
    List<String> listDeviceNameByIds(@Param("deviceIds") List<Long> deviceIds);

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param orgId      机构号
     * @param deviceType 设备类型
     * @return
     */
    List<Long> listDeviceIdByOrgIdAndType(@Param("orgId") Long orgId,
        @Param("deviceType") Integer deviceType);

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param deviceType 设备类型
     * @return
     */
    List<Device> listByType(@Param("deviceType") Integer deviceType);

    /**
     * 根据设备类型查询设备信息列表
     *
     * @param orgId      机构号
     * @param deviceType 设备类型
     * @return
     */
    List<Device> listByOrgIdAndType(@Param("orgId") Long orgId,
        @Param("deviceType") Integer deviceType);

    /**
     * 根据设备类型列表查询设备信息列表
     *
     * @param orgId       机构号
     * @param deviceTypes 设备类型列表
     * @return
     */
    List<Device> listByOrgIdAndTypes(@Param("orgId") Long orgId,
        @Param("deviceTypes") List<Integer> deviceTypes);

    /**
     * 根据条件模糊查询设备信息列表
     *
     * @param queryDeviceDTO 查询条件
     * @return
     */
    List<Device> listByLikeCondition(QueryDeviceDTO queryDeviceDTO);

    /**
     * 根据设备Sn、设备名称查询设备列表
     *
     * @param dto
     * @return
     */
    List<Device> listByDeviceSnListAndDeviceName(QuerySpecificDeviceDTO dto);

    List<Device> listAllDevices();

    int countAllDevices();

    List<Device> listDevicesByOrgIds(@Param("orgIds") List<Long> orgIds);

    /**
     * 根据机构id，设备类型列表，设备名称或者sn模糊查询设备列表
     *
     * @param orgId
     * @param deviceTypeList
     * @param keywords
     * @return
     */
    List<Device> findDeviceByOrgIdAndDeviceTypeList(@Param("orgId") Long orgId, @Param("deviceTypeList") List<Integer> deviceTypeList, @Param("keywords") String keywords);

    List<Integer> listAllDeviceTypeInOrg(@Param("orgId") Long orgId);

    /**
     * 修改蓝牙mac
     *
     * @param bluetoothMac
     * @param deviceId
     * @return
     */
    int updateBluetoothMac(@Param("bluetoothMac") String bluetoothMac, @Param("deviceId") Long deviceId);

    List<String> listParentSnByIotParentDeviceSns(@Param("parentDeviceSns") List<String> parentDeviceSns, @Param("deviceTypes") List<Integer> deviceTypes);

    List<Device> listByOrgIdAndParentDeviceSns(@Param("orgId") Long orgId, @Param("parentDeviceSns") List<String> parentDeviceSns, @Param("deviceTypes") List<Integer> deviceTypeByPropertyKey);

    void deleteByOrgIdAndParentSns(@Param("orgId") Long orgId, @Param("parentSns") List<String> parentSns);

    /**
     * 根据条件查询设备列表统计
     */
    List<Device> listDeviceByCondition(QueryIotDeviceDTO dto);

    void batchDeleteByOrgIdAndDeviceId(@Param("orgId") Long orgId, @Param("deviceId") List<Long> deviceId);

    /**
     * 根据条件查询设备信息列表
     */
    List<Device> listDeviceByConditionLike(QueryPageDeviceBO bo);
}