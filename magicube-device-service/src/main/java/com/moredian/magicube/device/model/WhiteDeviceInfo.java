package com.moredian.magicube.device.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class WhiteDeviceInfo implements Serializable {

	private static final long serialVersionUID = -2246865273005796729L;

	//sn号
	private String serialNumber;
	//mac地址
	private String macAddress;
	//mac地址w
	private String macAddress2;
	//私钥
	private String secretKey;

	private Integer patchFlag;

	private Long orgId;

	private Integer activityStatus;

	private String activationCode;

	//设备来源 1魔点设备 2三方设备
	private Integer deviceSource;

	private Boolean isOpenPlat;

	private Date gmtCreate;
	private Date gmtModify;

	private String thirdDeviceId;

	/**
	 * 设备类型
	 */
	private Integer deviceType;

}
