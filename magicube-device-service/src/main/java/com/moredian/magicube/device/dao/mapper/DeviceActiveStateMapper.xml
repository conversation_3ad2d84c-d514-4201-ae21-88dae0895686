<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceActiveStateMapper">

    <resultMap id="DeviceActiveStateResultMap" type="com.moredian.magicube.device.dao.entity.DeviceActiveState">
        <result column="id" property="id"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="state" property="state"/>
        <result column="bind_org_url" property="bindOrgUrl"/>
        <result column="bind_org_id" property="bindOrgId"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_columns">
        id,
        device_sn,
        state,
        bind_org_url,
        gmt_create,
        gmt_modify,
        bind_org_id
    </sql>

    <sql id="sql_table">
        hive_device_active_state
    </sql>

    <select id="getByDeviceSn" parameterType="string" resultMap="DeviceActiveStateResultMap">
        SELECT
        <include refid="sql_columns"/>
        FROM <include refid="sql_table"/>
        WHERE device_sn = #{deviceSn}
    </select>
    
    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceActiveState">
        INSERT INTO
        <include refid="sql_table"/>
        (<include refid="sql_columns"/>)
        VALUES
        (#{id}, #{deviceSn}, #{state}, #{bindOrgUrl}, now(3), now(3), #{bindOrgId})
    </insert>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceActiveState">
        UPDATE
        <include refid="sql_table"/>
        <set>
            <if test="bindOrgUrl != null">
                bind_org_url = #{bindOrgUrl},
            </if>
            <if test="bindOrgId != null">
                bind_org_id = #{bindOrgId},
            </if>
            state = #{state},
            gmt_modify = now(3)
            where device_sn = #{deviceSn}
        </set>
    </update>

    <update id="updateBindOrgUrl" parameterType="com.moredian.magicube.device.dao.entity.DeviceActiveState">
        UPDATE <include refid="sql_table"/> SET state = #{state}, bind_org_url = #{bind_org_url}, gmt_modify = now(3) where device_sn = #{deviceSn}
    </update>

</mapper>