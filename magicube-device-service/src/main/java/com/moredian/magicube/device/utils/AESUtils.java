package com.moredian.magicube.device.utils;


import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title:
 * @Description: AES 对称加密工具类
 * @date
 */
public class AESUtils {

    private static final String KEY_ALGORITHM = "AES/ECB/PKCS5Padding";

    private static SecretKeySpec getSecretKey(final String key) {
        return new SecretKeySpec(key.getBytes(), "AES");
    }

    /**
     * 加密
     *
     * @param content
     * @param key
     * @return
     * @throws Exception
     */
    public static String encrypt(String content, String key) throws Exception {
        try {
            // 1.创建密码器cipher
            Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
            byte[] byteContent = content.getBytes();
            // 2.初始化为加密模式的密码器
            cipher.init(Cipher.ENCRYPT_MODE, getSecret<PERSON>ey(key));
            // 3.加密
            byte[] result = cipher.doFinal(byteContent);

            //4.通过Base64转码返回
            return Base64.encodeBase64String(result);
        } catch (Exception ex) {
            //log.error("加密失败", ex);
            ex.printStackTrace();
        }

        return null;
    }

    /**
     * 解密
     * @param base64Content
     * @param key
     * @return
     * @throws Exception
     */
    public static String decrypt(String base64Content, String key) throws Exception {
        // 1.创建密码器cipher
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        // 2.初始化为加密模式的密码器
        cipher.init(Cipher.DECRYPT_MODE, getSecretKey(key));
        // 3.base64解码
        byte[] content = Base64.decodeBase64(base64Content);
        // 4.解密
        byte[] encrypted = cipher.doFinal(content);
        //返回明文
        return new String(encrypted);

    }



    public static void main(String[] args) throws Exception {

        System.out.println(encrypt("1234567890","d3f6daa173ecb5f0c87cb6f9978e1547"));
        System.out.println(encrypt("1234567890123456","d3f6daa173ecb5f0c87cb6f9978e1547"));
        System.out.println(encrypt("12345678901234567","d3f6daa173ecb5f0c87cb6f9978e1547"));
//JihbMbYLOIfY+DVUnSKuAg==
//A0M5xQO4AlEU5LNxmpz+rrOt9VqN/BLEtELOIfrHz4Q=
//A0M5xQO4AlEU5LNxmpz+rvB53SvVlxgjMdbl3N81w2M=
        System.out.println(decrypt("JihbMbYLOIfY+DVUnSKuAg==","d3f6daa173ecb5f0c87cb6f9978e1547"));
        System.out.println(decrypt("A0M5xQO4AlEU5LNxmpz+rrOt9VqN/BLEtELOIfrHz4Q=","d3f6daa173ecb5f0c87cb6f9978e1547"));
        System.out.println(decrypt("A0M5xQO4AlEU5LNxmpz+rvB53SvVlxgjMdbl3N81w2M=","d3f6daa173ecb5f0c87cb6f9978e1547"));

    }
}

