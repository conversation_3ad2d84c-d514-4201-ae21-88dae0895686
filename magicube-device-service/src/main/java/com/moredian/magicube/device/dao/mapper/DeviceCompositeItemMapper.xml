<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceCompositeItemMapper">

    <resultMap id="resultMap" type="com.moredian.magicube.device.dao.entity.DeviceCompositeItem">
        <id column="device_composite_item_id" property="deviceCompositeItemId"/>
        <result column="org_id" property="orgId"/>
        <result column="device_composite_id" property="deviceCompositeId"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_type" property="deviceType"/>
        <result column="biz_type" property="bizType"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_device_composite_item
    </sql>

    <sql id="sql_columns">
        device_composite_item_id,
		    org_id,
		    device_composite_id,
        device_id,
        device_type,
        biz_type,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{deviceCompositeItemId},
        #{orgId},
        #{deviceCompositeId},
        #{deviceId},
        #{deviceType},
        #{bizType},
        now(3),
        now(3)
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceCompositeItem">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.deviceCompositeItemId},
            #{item.orgId},
            #{item.deviceCompositeId},
            #{item.deviceId},
            #{item.deviceType},
            #{item.bizType},
            now(),
            now()
            )
        </foreach>
    </insert>

    <select id="listByCompositeId" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
        and status = 0
    </select>

    <select id="listByOrgId" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        and status = 0
    </select>

    <delete id="deleteByOrgIdAndDeviceCompositeId" parameterType="long">
        delete from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
    </delete>

    <select id="listByDeviceIds" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_id in
        <foreach collection="deviceIds" item="deviceId" index="index" open="(" close=")" separator=",">
            #{deviceId}
        </foreach>
        and biz_type = #{bizType}
        and status = 0
    </select>

    <select id="listByOrgIdAndDeviceCompositeIds" parameterType="long" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and status = 0
        and device_composite_id in
        <foreach collection="deviceCompositeIds" item="deviceCompositeId" index="index" open="(" close=")"
                 separator=",">
            #{deviceCompositeId}
        </foreach>
    </select>

    <delete id="deleteByDeviceId" parameterType="map">
        delete from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_id = #{deviceId}
        and biz_type = #{bizType}
    </delete>

    <delete id="removeItem">
        delete from <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
        <if test="deviceId != null">
            and device_id = #{deviceId}
        </if>
    </delete>

    <select id="listByCompositeIds" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and status = 0
        and device_composite_id in
        <foreach collection="deviceCompositeIds" item="deviceCompositeId" index="index" open="(" close=")" separator=",">
            #{deviceCompositeId}
        </foreach>
    </select>

    <!-- 只有同时删除组的时候才是软删除 -->
    <update id="removeItemSoft">
        update  <include refid="sql_table"/> set status = #{status}
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
        <if test="deviceId != null">
            and device_id = #{deviceId}
        </if>
        and status = 0
    </update>

    <!-- 可查已经删除的 -->
    <select id="getItemListByCompositeIdWithDeleteBatch" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id in
        <foreach collection="deviceCompositeIds" item="deviceCompositeId" index="index" open="(" close=")" separator=",">
            #{deviceCompositeId}
        </foreach>
    </select>

    <select id="getItemListByCompositeIdListWithDelete" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id in
        <foreach collection="deviceCompositeIds" item="deviceCompositeId" index="index" open="(" close=")" separator=",">
            #{deviceCompositeId}
        </foreach>
    </select>
</mapper>