package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.DeviceOperEventType;
import com.moredian.magicube.common.model.msg.device.DeviceActiveAddMemberMsg;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.DeviceLog;
import com.moredian.magicube.device.dao.mapper.DeviceLogMapper;
import com.moredian.magicube.device.manager.DeviceLogManager;
import com.xier.sesame.common.utils.JsonUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class DeviceLogManagerImpl implements DeviceLogManager {

    @Autowired
    private DeviceLogMapper deviceLogMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public Long insert(DeviceLog deviceLog) {
        BizAssert.notNull(deviceLog.getOrgId(), "orgId must not be null");
        BizAssert.notBlank(deviceLog.getDeviceSn(), "deviceSn must not be null");
        BizAssert.notNull(deviceLog.getEnvenType(), "eventType must not be null");
        BizAssert.notNull(deviceLog.getOperTime(), "operTime must not be null");
        Long deviceLogId = idgeneratorService.getNextIdByTypeName(BeanConstants.DEPLOY)
            .pickDataThrowException();
        deviceLog.setDeviceLogId(deviceLogId);
        deviceLogMapper.insert(deviceLog);
        return deviceLogId;
    }

    @Override
    public Boolean updateDeviceLogOperatorId(DeviceLog deviceLog) {
        BizAssert.notNull(deviceLog.getOrgId(), "orgId must not be null");
        BizAssert.notBlank(deviceLog.getDeviceSn(), "deviceSn must not be null");
        DeviceLog device = deviceLogMapper.getLatestByOrgIdAndDeviceSnAndType(deviceLog.getOrgId(),
            deviceLog.getDeviceSn(), deviceLog.getEnvenType());
        if (device == null) {
            return Boolean.FALSE;
        }
        device.setMemberId(deviceLog.getMemberId());
        deviceLogMapper.update(device);
        //如果是激活后保存操作人，则发送设备激活人员消息
        if (device.getEnvenType().equals(DeviceOperEventType.ACTIVE_DEVICE.getValue())) {
            DeviceActiveAddMemberMsg deviceActiveAddMemberMsg = new DeviceActiveAddMemberMsg();
            deviceActiveAddMemberMsg.setOrgId(device.getOrgId());
            deviceActiveAddMemberMsg.setDeviceId(device.getDeviceId());
            deviceActiveAddMemberMsg.setDeviceSn(device.getDeviceSn());
            deviceActiveAddMemberMsg.setMemberId(device.getMemberId());
            EventBus.publish(deviceActiveAddMemberMsg);
            log.info("设备激活成功-更新激活日志的操作人Id：" + JsonUtils.toJson(deviceActiveAddMemberMsg));
        }
        return Boolean.TRUE;
    }

    @Override
    public DeviceLog getByOrgIdAndId(Long orgId, Long deviceLogId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceLogId, "deviceLogId must not be null");
        return deviceLogMapper.getByOrgIdAndId(orgId, deviceLogId);
    }

    @Override
    public List<DeviceLog> listByOrgIdAndDeviceId(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        return deviceLogMapper.listByOrgIdAndDeviceId(orgId, deviceId);
    }

    @Override
    public List<DeviceLog> listByOrgIdAndDeviceSn(Long orgId, String deviceSn) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        return deviceLogMapper.listByOrgIdAndDeviceSn(orgId, deviceSn);
    }

    @Override
    public List<Integer> listDistinctDeviceTypeByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        return deviceLogMapper.listDistinctDeviceTypeByOrgId(orgId);
    }

    @Override
    public Integer countByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        return deviceLogMapper.countByOrgId(orgId);
    }

    @Override
    public List<DeviceLog> listByDeviceSn(String deviceSn) {
        BizAssert.notNull(deviceSn, "deviceSn must not be null");
        return deviceLogMapper.listByDeviceSn(deviceSn);
    }
}
