package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceGroup;
import com.moredian.magicube.device.dto.group.GroupDeviceSizeDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备组关系
 *
 * <AUTHOR>
 * @since 2021-09-15
 */

@Mapper
public interface DeviceGroupMapper {

    /**
     * 新增设备和组关系
     *
     * @param deviceGroup 设备和组关系信息
     * @return
     */
    void insert(DeviceGroup deviceGroup);

    /**
     * 批量新增设备和组关系
     *
     * @param deviceGroups 设备和组关系信息
     * @return
     */
    void batchInsert(@Param("deviceGroups") List<DeviceGroup> deviceGroups);

    /**
     * 根据机构号和组Id获取设备Id列表
     *
     * @param orgId   机构号
     * @param groupId 组Id
     * @return 设备Id列表
     */
    List<Long> listDeviceIdByOrgIdAndGroupId(@Param("orgId") Long orgId,
        @Param("groupId") Long groupId);

    /**
     * 根据机构号和组Id列表获取设备Id列表
     *
     * @param orgId    机构号
     * @param groupIds 组Id列表
     * @return 设备Id列表
     */
    List<Long> listDeviceIdByOrgIdAndGroupIds(@Param("orgId") Long orgId,
        @Param("groupIds") List<Long> groupIds);

    /**
     * 根据机构号和组Id列表获取设备Id列表
     *
     * @param orgId    机构号
     * @param groupIds 组Id列表
     * @param appType  权限组应用类型
     * @return 设备Id列表
     */
    List<Long> listDeviceIdByOrgIdAndGroupIdsAndAppType(@Param("orgId") Long orgId,
        @Param("groupIds") List<Long> groupIds, @Param("appType") Integer appType);

    /**
     * 根据机构号和组Id列表获取关系列表
     *
     * @param orgId    机构号
     * @param groupIds 组Id列表
     * @return
     */
    List<DeviceGroup> listByOrgIdAndGroupIds(@Param("orgId") Long orgId,
        @Param("groupIds") List<Long> groupIds);

    /**
     * 查询设备Id列表
     *
     * @param orgId   机构号
     * @param groupId 组Id
     * @param offset  开始数
     * @param limit   查询数量
     * @return
     */
    List<Long> listDeviceIdsByGroupId(@Param("orgId") Long orgId,
        @Param("groupId") Long groupId,
        @Param("offset") int offset,
        @Param("limit") int limit);

    /**
     * 根据机构号和设备Id列表查询组Id列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<Long> listGroupIdByOrgIdAndDeviceIds(@Param("orgId") Long orgId,
        @Param("deviceIds") List<Long> deviceIds);

    /**
     * 根据机构号和设备Id列表查询组Id列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @param appType   权限组应用类型
     * @return
     */
    List<Long> listGroupIdByOrgIdAndDeviceIdsAndAppType(@Param("orgId") Long orgId,
        @Param("deviceIds") List<Long> deviceIds, @Param("appType") Integer appType);

    /**
     * 根据机构号和设备Id列表查询列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<DeviceGroup> listByOrgIdAndDeviceIds(@Param("orgId") Long orgId,
        @Param("deviceIds") List<Long> deviceIds);

    /**
     * 根据机构号和设备Id查询组名称列表
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    List<String> listGroupNameByCondition(@Param("orgId") Long orgId,
        @Param("deviceId") Long deviceId);

    /**
     * 根据机构号和设备Id删除关系
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @param groupIds  组Id列表
     * @return
     */
    void deleteByCondition(@Param("orgId") Long orgId, @Param("deviceIds") List<Long> deviceIds,
        @Param("groupIds") List<Long> groupIds);

    List<Long> findGroupIdByDeviceIdAppTypes(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId, @Param("appTypes") List<Integer> appTypes);

    List<GroupDeviceSizeDTO> countDeviceSizeGroupByGroupId(@Param("orgId") Long orgId, @Param("groupIds") List<Long> groupIds);

    int deleteByDeviceGroupIdList(@Param("orgId")Long orgId, @Param("deviceGroupIdList")List<Long> deviceGroupIdList);

    List<DeviceGroup> findRelationByGroupId(@Param("orgId")Long orgId, @Param("groupId")Long groupId, @Param("deviceIdList")List<Long> deviceIdList);
}
