package com.moredian.magicube.device.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.TimeUtil;
import com.moredian.magicube.device.utils.JacksonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "industry")
public class IndustryProtectedConfigs {
    public IndustryProtectedConfig edu;

    /**
     * industry.edu.name=edu
     * industry.edu.enable=true
     * industry.edu.deviceProductTime=2020-08-01
     * #保护的行业列表
     * industry.edu.defaultAppTypes={"09":null,"11":null,"16":null}
     * industry.edu.industries[0].appTypes={"09":70001,"11":null,"16":70003}
     * industry.edu.industries[1].appTypes={"09":70001,"11":null,"16":70003}
     * <p>
     * industry.edu.industries[0].name=学前教育
     * industry.edu.industries[1].name=初中等教育
     * industry.edu.industries[2].name=中等职业教育
     * industry.edu.industries[3].name=高等职业教育
     * industry.edu.industries[4].name=高等教育
     * industry.edu.industries[5].name=教育培训机构
     * industry.edu.industries[6].name=技能培训
     * industry.edu.industries[7].name=在线教育
     */
    @Getter
    @Setter
    public static class IndustryProtectedConfig {
        //是否启用
        public Boolean enable;
        //行业保护名字
        public String name;
        //行业保护启用时间
        public String deviceProductTime;
        @JsonIgnore
        public Date deviceProductDay;
        public String defaultAppTypes;
        @JsonIgnore
        public Map<String, Integer> defaultSnType2AppTypeMap;
        //行业列表
        public IndustryConfig[] industries;

        public void init() {
            this.deviceProductDay = TimeUtil.convertDate(deviceProductTime, TimeUtil.FORMAT_YYYYMMDD);
            this.defaultSnType2AppTypeMap = JacksonUtil.readValue(defaultAppTypes, HashMap.class);
            for (IndustryConfig config : industries) {
                config.init(defaultSnType2AppTypeMap);
            }
        }

        public boolean containSnDeviceType(String industry, String snDeviceType) {
            if (industry == null || snDeviceType == null) {
                return false;
            }
            IndustryConfig industryConfig = getIndustryConfig(industry);
            if (industryConfig == null || industryConfig.appTypes == null) {
                return false;
            }
            return industryConfig.snType2AppTypeMap.containsKey(snDeviceType);
        }

        public Integer getAppType(String industry, String snDeviceType) {
            if (industry == null || snDeviceType == null) {
                return null;
            }

            IndustryConfig industryConfig = getIndustryConfig(industry);
            if (industryConfig == null || industryConfig.appTypes == null) {
                return null;
            }
            return industryConfig.snType2AppTypeMap.get(snDeviceType);
        }

        private IndustryConfig getIndustryConfig(String industry) {
            for (IndustryConfig industryConfig : industries) {
                if (industry.equals(industryConfig.getName())) {
                    if (industryConfig.snType2AppTypeMap == null) {
                        industryConfig.snType2AppTypeMap = defaultSnType2AppTypeMap;
                    }
                    return industryConfig;
                }
            }
            return null;
        }

        public boolean containIndustry(String industry) {
            return getIndustryConfig(industry) != null;
        }
    }

    @Getter
    @Setter
    public static class IndustryConfig {
        private String name;
        private String appTypes;
        @JsonIgnore
        public Map<String, Integer> snType2AppTypeMap;

        public void setAppTypes(String appTypes) {
            this.appTypes = appTypes;
        }

        public void init(Map<String, Integer> defaultAppTypes) {
            if (StringUtils.isNotBlank(appTypes)) {
                this.snType2AppTypeMap = JacksonUtil.readValue(appTypes, HashMap.class);
            } else {
                this.snType2AppTypeMap = defaultAppTypes;
            }
        }
    }

    @PostConstruct
    public void printConfig() {
        edu.init();
        log.info("==============行业保护配置-教育===========================================");
        log.info(JsonUtils.toJson(edu));
        log.info("=========================================================================");
    }

}
