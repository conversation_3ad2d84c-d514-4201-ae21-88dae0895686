package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.core.member.response.GroupPersonResponse;
import com.moredian.magicube.core.member.service.GroupRelationService;
import com.moredian.magicube.core.member.service.MemberService;
import com.moredian.magicube.device.dao.entity.DeviceSnapshot;
import com.moredian.magicube.device.dao.entity.GroupPersonSnapshot;
import com.moredian.magicube.device.dao.mapper.DeviceSnapshotMapper;
import com.moredian.magicube.device.dto.snapshot.DeviceSnapshotDTO;
import com.moredian.magicube.device.dto.snapshot.GroupPersonSnapshotDTO;
import com.moredian.magicube.device.manager.DeviceSnapshotManager;
import com.moredian.magicube.device.manager.GroupPersonSnapshotManager;
import com.moredian.magicube.device.model.GenerateDeviceSnapshotModel;
import com.moredian.magicube.device.model.QueryDeviceSnapshotModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/29
 */
@Component
@Slf4j
public class DeviceSnapshotManagerImpl implements DeviceSnapshotManager {

    @Autowired
    private DeviceSnapshotMapper deviceSnapshotMapper;

    @Autowired
    private GroupPersonSnapshotManager groupPersonSnapshotManager;

    @SI
    private GroupRelationService groupRelationService;

    @SI
    private MemberService memberService;

    @SI
    private IdgeneratorService idgeneratorService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateSnapshot(GenerateDeviceSnapshotModel model) {
        log.info("开始生成设备快照[{}]", JsonUtils.toJson(model));
        //查询设备绑定的组的人员
        List<GroupPersonResponse> groupPersonResponseList = groupRelationService
                .findGroupPerson(model.getOrgId(), model.getBondGroupIdList()).pickDataThrowException();
        //保存快照
        DeviceSnapshot deviceSnapshot = new DeviceSnapshot();
        deviceSnapshot.setDeviceId(model.getDeviceId());
        deviceSnapshot.setDeviceSn(model.getDeviceSn());
        deviceSnapshot.setOrgId(model.getOrgId());
        deviceSnapshot.setTimestamp(model.getTimestamp());
        Long id = idgeneratorService.getNextIdByTypeName(DeviceSnapshot.class.getName()).pickDataThrowException();
        deviceSnapshot.setId(id);
        deviceSnapshotMapper.save(deviceSnapshot);
        if (CollectionUtils.isNotEmpty(groupPersonResponseList)) {
            //获取主键
            BatchIdDto batchIdDto = idgeneratorService.getNextIdBatchBytypeName(GroupPersonSnapshot.class.getName(), groupPersonResponseList.size()).pickDataThrowException();
            //保存组与人员的关系
            List<GroupPersonSnapshotDTO> groupPersonSnapshotDTOList = groupPersonResponseList.stream().map(groupPersonResponse -> {
                GroupPersonSnapshotDTO snapshot = new GroupPersonSnapshotDTO();
                snapshot.setId(batchIdDto.nextId());
                snapshot.setSnapshotId(id);
                snapshot.setOrgId(groupPersonResponse.getOrgId());
                snapshot.setGroupId(groupPersonResponse.getGroupId());
                snapshot.setPersonId(groupPersonResponse.getPersonId());
                snapshot.setPersonType(groupPersonResponse.getPersonType());
                return snapshot;
            }).collect(Collectors.toList());
            groupPersonSnapshotManager.batchInsert(groupPersonSnapshotDTOList);
            log.info("生成设备快照成功入参：[{}]，快照id:[{}]", JsonUtils.toJson(model), id);
            return id;
        } else {
            log.info("未查询到组内存在人员，生成的快照中无人员[{}]", JsonUtils.toJson(model));
        }
        return null;
    }

    @Override
    public DeviceSnapshotDTO getDeviceSnapshot(QueryDeviceSnapshotModel model) {
        BizAssert.notNull(model, "model must not be null");
        BizAssert.notNull(model.getOrgId(), "orgId must not be null");
        BizAssert.notNull(model.getDeviceId(), "deviceId must not be null");
        DeviceSnapshot deviceSnapshot = deviceSnapshotMapper.getByOrgIdAndDeviceId(model);
        if (deviceSnapshot != null) {
            return convertToDeviceSnapshotDTO(deviceSnapshot);
        }
        return null;
    }

    @Override
    public List<DeviceSnapshotDTO> queryOvertimeDeviceSnapshot(Integer keepDay) {
        BizAssert.notNull(keepDay, "keepDay must not be null");
        BizAssert.isTrue(keepDay > 0, "keepDay is illegal");
        Long timestamp = System.currentTimeMillis() - keepDay * 24 * 3600 * 1000L;
        Date limitDate = new Date(timestamp);
        List<DeviceSnapshot> deviceSnapshotList = deviceSnapshotMapper.queryOvertimeDeviceSnapshot(limitDate);
        return convertToDeviceSnapshotDTOList(deviceSnapshotList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long snapshotId) {
        BizAssert.notNull(snapshotId, "snapshotId must not be null");
        DeviceSnapshot exist = deviceSnapshotMapper.getById(snapshotId);
        BizAssert.notNull(exist, "deviceSnapshot not exist");
        groupPersonSnapshotManager.deleteBySnapshotId(exist.getOrgId(), snapshotId);
        deviceSnapshotMapper.deleteById(snapshotId);
        log.info("删除快照成功，快照id:[{}]", snapshotId);
        return Boolean.TRUE;
    }

    private List<DeviceSnapshotDTO> convertToDeviceSnapshotDTOList(List<DeviceSnapshot> deviceSnapshotList) {
        if (CollectionUtils.isEmpty(deviceSnapshotList)) {
            return Collections.emptyList();
        }
        return deviceSnapshotList.stream().map(deviceSnapshot -> {
            DeviceSnapshotDTO deviceSnapshotDTO = convertToDeviceSnapshotDTO(deviceSnapshot);
            return deviceSnapshotDTO;
        }).collect(Collectors.toList());
    }

    private DeviceSnapshotDTO convertToDeviceSnapshotDTO(DeviceSnapshot deviceSnapshot) {
        if (deviceSnapshot == null) {
            return null;
        }
        DeviceSnapshotDTO dto = new DeviceSnapshotDTO();
        dto.setId(deviceSnapshot.getId());
        dto.setOrgId(deviceSnapshot.getOrgId());
        dto.setDeviceId(deviceSnapshot.getDeviceId());
        dto.setDeviceSn(deviceSnapshot.getDeviceSn());
        dto.setGmtCreate(deviceSnapshot.getGmtCreate());
        dto.setGmtModify(deviceSnapshot.getGmtModify());
        dto.setTimestamp(deviceSnapshot.getTimestamp());
        return dto;
    }
}
