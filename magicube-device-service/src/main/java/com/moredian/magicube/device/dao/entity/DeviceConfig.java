package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备配置
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_config")
public class DeviceConfig extends TimedEntity {

    private static final long serialVersionUID = -6140775958096389234L;

    /**
     * 设备配置Id
     */
    private Long deviceConfigId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备配置xml信息
     */
    private String xmlConfig;

    /**
     * 设备配置签名
     */
    private String configSignature;
}