package com.moredian.magicube.device.subscriber;

/**
 * <AUTHOR>
 */

public enum TransferEventType {

    DEVICE_CONFIG("DEVICE_CONFIG", 101, "设备配置信息"),
    LOG_COLLECT("LOG_COLLECT", 106, "手机日志"),
    GROUP_SYNC("GROUP_SYNC", 110, "批量同步群组人员关系"),
    PROPER_VALUES_SYNC("PROPER_VALUES_SYNC", 111, "批量同步人员特征值"),
    SINGLE_VALUE_SYNC("SINGLE_VALUE_SYNC", 112, "同步单个人员特征值"),
    SINGLE_GROUP_SYNC("SINGLE_GROUP_SYNC", 113, "同步单个群组人员关系"),
    SINGLE_GROUP_VALUE_SYNC("SINGLE_GROUP_VALUE_SYNC", 114, "同步单个群组里的人员特征值"),
    DOOR_OVER_TIME("DOOR_OVER_TIME", 131, "更改开门超时时长"),
    FIRE_ALARM_CLEAR("FIRE_ALARM_CLEAR", 132, "清除火警状态"),
    ENABLE_VOICE("ENABLE_VOICE", 133, "启用语音功能"),
    DEVICE_UNBIND("DEVICE_UNBIND", 141, "解绑设备"),
    DEVICE_ATTENCE_SETTING("DEVICE_ATTENCE_SETTING", 134, "打卡设置"),
    DEVICE_OPEN_DOOR("DEVICE_OPEN_DOOR", 142, "开门指令"),

    //机构成员消息
    MEMBER_FACE_SYNC("MEMBER_FACE_SYNC", 206, "通知成员人脸变更,拉取变更日志"),

    PULL_MEMBER_MOBILE_IDCARD("PULL_MEMBER_MOBILE_IDCARD",347,"拉取成员手机号和身份证信息"),

    RESET_NETWORK("RESET_NETWORK",372,"重置网络信息	"),
    DEVICE_SUBJECT_SYNC("DEVICE_SUBJECT_SYNC",373,"设备主题变更"),
    DOOR_LOCK_ALL_SYNC("DOOR_LOCK_ALL_SYNC",381,"门锁全量同步人员特征值、卡片、权限组"),
    DEVICE_BACKGROUND_AUTH_ALL_SYNC("DEVICE_BACKGROUND_AUTH_ALL_SYNC",385,"告知设备来拉取能进入设备后台的人员id"),
    MIGRATION_DEVICE_ORG_URL("MIGRATION_DEVICE_ORG_URL",390,"发起获取组织列表页url请求"),

    ENTERPRISE_ACCOUNT_STATUS_CHANGE("ENTERPRISE_ACCOUNT_STATUS_CHANGE",394,"企业账号状态变更"),
    ;

    private String eventName;
    private int eventType;
    private String description;

    TransferEventType(String eventName, int eventType, String description) {
        this.eventName = eventName;
        this.eventType = eventType;
        this.description = description;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public int getEventType() {
        return eventType;
    }

    public void setEventType(int eventType) {
        this.eventType = eventType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}