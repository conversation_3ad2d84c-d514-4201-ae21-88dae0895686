package com.moredian.magicube.device.utils;

import java.util.regex.Pattern;

public class UUIDValidator {

    private static final Pattern UUID_PATTERN = Pattern.compile(
            "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");

    public static boolean isValidUUID(String uuid) {
        if (uuid == null) {
            return false;
        }
        return UUID_PATTERN.matcher(uuid).matches();
    }

    public static void main(String[] args) {
        String uuid = "85b80759-8276-4d3c-bdf1-570b8c866bba";
        System.out.println("Is the UUID valid? " + isValidUUID(uuid));
    }
}
