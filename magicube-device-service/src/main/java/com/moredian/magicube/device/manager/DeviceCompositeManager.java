package com.moredian.magicube.device.manager;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dao.entity.DeviceComposite;
import com.moredian.magicube.device.dto.composite.*;

import java.util.List;

/**
 * 设备分组
 *
 * <AUTHOR>
 */

public interface DeviceCompositeManager {

    /**
     * 新增或新增设备分组
     *
     * @param dto 设备分组信息
     * @return
     */
    DeviceCompositeDTO insertOrUpdate(InsertDeviceCompositeDTO dto);

    /**
     * 添加设备到设备组，增量添加，如果存在了会报错存在
     *
     * @param dto DTO
     * @return {@link DeviceCompositeDTO }
     */
    Boolean addDeviceInc(DeviceBindCompositeDTO dto);

    /**
     * 新增或更新设备分组 非互斥
     *
     * @param dto 设备分组信息
     * @return
     */
    Long insertOrUpdateNoMutex(InsertDeviceCompositeDTO dto);

    /**
     * 查询机构所有的分组信息，包括组下的设备信息
     *
     * @param orgId   机构号
     * @param bizType 业务类型
     * @return
     */
    List<DeviceCompositeDTO> listByOrgIdAndBizType(Long orgId, Integer bizType);

    /**
     * 删除设备分组
     *
     * @param dto 删除的分组信息
     * @return
     */
    Boolean delete(DeleteDeviceCompositeDTO dto);

    /**
     * 获取已经绑定的设备列表
     * 获取的是这个机构下组下所有的设备
     *
     * @param orgId
     * @param bizType
     * @return
     */
    List<Long> listBindDeviceIdByOrgIdAndBizType(Long orgId, Integer bizType);

    /**
     * 根据设备id删除
     *
     * @param orgId
     * @param deviceId
     * @param bizType
     * @return
     */
    Boolean deleteCompositeItemByDeviceId(Long orgId, Long deviceId, Integer bizType);

    /**
     * 查询设备所属设备组名称
     *
     * @param orgId
     * @param deviceIdList
     * @param bizType
     * @param compositeIds 有权限的组ID列表
     * @return
     */
    List<DeviceCompositeNameListDTO> findDeviceCompositeNameList(Long orgId,
        List<Long> deviceIdList,
        Integer bizType,
        List<Long> compositeIds);

    /**
     * 开启管理员，子管理员查询根据自己的设备查询分组信息
     *
     * @param dto 查询条件
     * @return
     */
    CompositeAndDeviceInfoDTO listByCondition(QueryDeviceCompositeDTO dto);

    /**
     * 获取设备组列表以及下面的设备台数
     *
     * @param orgId   机构号
     * @param bizType 业务类型
     * @return
     */
    List<DeviceCompositeDTO> listDetailByOrgIdAndBizType(Long orgId, Integer bizType);

    /**
     * 获取指定业务的设备组虚拟根
     * ⭐新增接口
     *
     * @param orgId   组织 ID
     * @param bizType 业务类型
     * @return {@link DeviceComposite }
     */
    DeviceComposite getVirtualRootDeviceComposite(Long orgId, Integer bizType);

    /**
     * 获取组下面的设备列表
     *
     * @param orgId             机构号
     * @param deviceCompositeId 设备分组Id
     * @param subFlag  是否包含子组
     * @return
     */
    List<DeviceCompositeItemDTO> listCompositeItemByCompositeId(Long orgId, Long deviceCompositeId,
        Boolean subFlag);

    /**
     * 获取组信息以及下面的设备信息
     *
     * @param orgId             机构号
     * @param deviceCompositeId 设备分组Id
     * @return
     */
    DeviceCompositeDTO getDetailByOrgIdAndId(Long orgId, Long deviceCompositeId);

    /**
     * 获取当前业务下最大的组code
     * ⭐新增接口
     * @param orgId 机构ID
     */
    String getMaxDeptCode(Long orgId, Integer bizType);

    /**
     * 根据设备组ID批量查询设备组实体
     *
     * @param orgId        组织 ID
     * @param compositeIds 复合 ID
     * @param bizType      业务类型
     * @return {@link List }<{@link DeviceCompositeDTO }>
     */
    List<DeviceCompositeDTO> listByDeviceCompositeIds(Long orgId, List<Long> compositeIds,
        Integer bizType);

    /**
     * 根据设备组ID批量查询设备组实体(path)
     *
     * @param orgId        组织 ID
     * @param codeList 复合 ID
     * @param bizType      业务类型
     * @return {@link List }<{@link DeviceCompositeDTO }>
     */
    List<DeviceCompositeDTO> listByDeviceCompositeCodeList(Long orgId, List<String> codeList,
        Integer bizType);

    /**
     * 查出当前设备组的所有子组信息，根据code+path高效查询
     * 根据code+path查出
     * ⭐新增接口
     *
     * @param orgId             组织 ID
     * @param deviceCompositeId 设备复合 ID
     * @return {@link List }<{@link DeviceComposite }>
     */
    List<DeviceComposite> findAllSubDeviceComposite(Long orgId, Long deviceCompositeId);

    /**
     * 获取这批设备的设备组路径列表+设备组详情列表
     *
     * @param request 请求
     * @return {@link DeviceCompositePathInfoDTO }
     */
    DeviceCompositePathInfoDTO getDeviceInCompositePathList(QueryDeviceCompositePathDTO request);

    /**
     * 获取多个设备组的树结构，如果在一棵树上会合并成一颗树
     * ⭐注意,compositeIds就是建树的所有资源，不会自动查子节点
     *
     * @param orgId        组织 ID
     * @param bizType      业务类型
     * @param compositeIds 复合 ID
     * @return {@link List }<{@link DeviceCompositeAndDeviceCountDTO }>
     */
    List<DeviceCompositeAndDeviceCountDTO> getTreeAndDeviceByIds(Long orgId, Integer bizType, List<Long> compositeIds);

    /**
     * 根据设备组ID批量查询组下设备列表
     * @param orgId
     * @param compositeIds
     * @return
     */
    List<Long> listDeviceIdByCompositeIds(Long orgId, List<Long> compositeIds);

    /**
     * 根据设备组ID批量查询组下设备列表
     * @param orgId
     * @param parentId
     * @return
     */
    List<Long> listCompositeIdsByParentId(Long orgId, Long parentId);

    /**
     * 获取设备组以及设备组下面的设备数量
     * ⭐新增接口
     *
     * @param orgId        组织 ID
     * @param compositeIds 复合 ID
     * @param bizType      业务类型
     * @param sceneType     场景类型 @see com.moredian.magicube.common.enums.SceneTypeEnums
     * @return {@link List }<{@link CompositeDeepDeviceSizeDTO }>
     */
    List<CompositeDeepDeviceSizeDTO> getCompositeDeepDeviceSize(Long orgId,
        List<Long> compositeIds, Integer bizType, Integer sceneType);

    /**
     * 根据多个设备组id找出设备组以及子组下面涉及的设备ids
     * ⭐新增接口
     * @param orgId              组织 ID
     * @param deviceCompositeIds 设备复合 ID
     * @param sceneType           场景类型 @see com.moredian.magicube.common.enums.SceneTypeEnums
     * @return {@link List }<{@link Long }>
     */
    List<Long> getDeviceIdsByCompositeDeepBatch(Long orgId, List<Long> deviceCompositeIds, Integer sceneType);

    /**
     * 注意：会存在重复的
     * @param orgId
     * @param deviceCompositeIds
     * @return
     */
    List<DeviceComposite> findAllSubDeviceCompositeBatch(Long orgId,
        List<Long> deviceCompositeIds);

    /**
     * 查出当前设备组的所有子组信息，根据code+path高效查询
     * 根据code+path查出
     * ⭐新增接口
     *
     * @param orgId             组织 ID
     * @param deviceCompositeId 设备复合 ID
     * @return {@link List }<{@link DeviceComposite }>
     */
    List<DeviceComposite> findAllSubDeviceCompositeWithDelete(Long orgId, Long deviceCompositeId);

    /**
     * 根据设备组id查设备组信息，支持查已经删除的
     *
     * @param orgId       组织 ID
     * @param compositeId 复合 ID
     * @return {@link DeviceCompositeDTO }
     */
    DeviceComposite getCompositeByIdWithDelete(Long orgId,Long compositeId);

    /**
     * 获取组信息以及子组的设备信息（无树结构）
     * ⭐完成改造
     *
     * @param orgId
     * @param deviceCompositeId
     * @param hasChildren    是否需要子组中的设备
     * @return
     */
    DeviceCompositeDTO getCompositeInfo(Long orgId, Long deviceCompositeId, Boolean hasChildren);

    /**
     * 批量获取这批组下面的设备情况
     * ⭐完成改造  （适用于查某组以及组下所有子组的所有设备情况）,这里只会查出直接关联的
     *
     * @param orgId              组织 ID
     * @param deviceCompositeIds 设备复合 ID
     * @return {@link List }<{@link DeviceCompositeItemDTO }>
     */
    List<DeviceCompositeItemDTO> getCompositeItemListByCompositeIds(Long orgId,
        List<Long> deviceCompositeIds);
}
