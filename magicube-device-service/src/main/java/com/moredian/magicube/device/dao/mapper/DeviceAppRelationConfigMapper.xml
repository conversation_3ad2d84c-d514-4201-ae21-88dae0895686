<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceAppRelationConfigMapper">


    <resultMap id="configResultMap" type="com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig">
        <id property="id" column="id"/>
        <result property="spaceType" column="space_type"/>
        <result property="appType" column="app_type"/>
        <result property="versionCode" column="version_code"/>
        <result property="defaultAppCode" column="default_app_code"/>
        <result property="availableAppCodeList" column="available_app_code_list"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <select id="listAll" resultMap="configResultMap">SELECT
        <include refid="sql_columns"/> FROM hive_device_app_relation_config
    </select>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig">
        INSERT INTO hive_device_app_relation_config
        (id,space_type, app_type, version_code, default_app_code, available_app_code_list, gmt_create, gmt_modify)
        VALUES
        (#{id},#{spaceType}, #{appType}, #{versionCode}, #{defaultAppCode}, #{availableAppCodeList}, now(3),now(3))
    </insert>

    <delete id="deleteById" parameterType="long">
        DELETE FROM hive_device_app_relation_config WHERE id = #{id}
    </delete>

    <update id="updateById" parameterType="com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig">
        UPDATE hive_device_app_relation_config SET
        space_type = #{spaceType},
        app_type = #{appType},
        version_code = #{versionCode},
        default_app_code = #{defaultAppCode},
        available_app_code_list = #{availableAppCodeList},
        gmt_modify = #{gmtModify}
        WHERE id = #{id}
    </update>

    <select id="selectById" parameterType="long" resultMap="configResultMap">
        SELECT * FROM hive_device_app_relation_config WHERE id = #{id}
    </select>

    <select id="selectBySpaceTypeAndAppTypeAndVersionCode" parameterType="map" resultMap="configResultMap">
        SELECT
        <include refid="sql_columns"/>
        FROM hive_device_app_relation_config
        WHERE space_type = #{spaceType}
        AND app_type = #{appType}
        AND version_code <![CDATA[ <= ]]> #{versionCode}
        order by version_code desc
    </select>

    <update id="updateSelectiveById" parameterType="com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig">
        UPDATE hive_device_app_relation_config
        <set>
            <if test="spaceType != null">space_type = #{spaceType},</if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="versionCode != null">version_code = #{versionCode},</if>
            <if test="defaultAppCode != null">default_app_code = #{defaultAppCode},</if>
            <if test="availableAppCodeList != null">available_app_code_list = #{availableAppCodeList},</if>
            gmt_modify = now(3)
        </set>
        WHERE id = #{id}
    </update>

    <sql id="sql_columns">
        id,
        space_type,
        app_type,
        version_code,
        default_app_code,
        available_app_code_list,
        gmt_create,
        gmt_modify
    </sql>

    <select id="selectByConditions" resultMap="configResultMap" parameterType="com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO">

        SELECT
        <include refid="sql_columns"/>
        FROM hive_device_app_relation_config
        <where>
            <if test="spaceType != null and spaceType != ''">
                AND space_type = #{spaceType}
            </if>
            <if test="appType != null and appType != ''">
                AND app_type = #{appType}
            </if>
            <if test="versionCode != null">
                AND version_code <![CDATA[ <= ]]> #{versionCode}
            </if>
            <if test="availableAppCode != null">
                AND available_app_code_list like concat('%',#{availableAppCode},'%')
            </if>
            <if test="defaultAppCode != null">
                AND default_app_code = #{defaultAppCode}
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2024-08-21-->
    <select id="selectByAppTypeAndVersionCode" resultMap="configResultMap">
        SELECT
        <include refid="sql_columns"/>
        FROM hive_device_app_relation_config
        WHERE app_type = #{appType}
        AND version_code <![CDATA[ <= ]]> #{versionCode}
        order by version_code desc
    </select>
</mapper>