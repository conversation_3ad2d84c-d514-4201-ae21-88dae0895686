/**
 *
 */
package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.CommonErrorCode;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.filemanager.ApkFileManager;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.enums.EnumUpgradeType;
import com.moredian.iothub.control.api.v1.request.*;
import com.moredian.iothub.control.api.v1.request.UpgradeRequest;
import com.moredian.iothub.control.api.v1.request.UpgradeRequest.UpdateEventType;
import com.moredian.iothub.control.api.v1.response.DeviceAppUpdateProgressResponse;
import com.moredian.iothub.control.api.v1.response.MergeUpgradeStatusResponse;
import com.moredian.iothub.control.api.v1.response.UpgradeResponse;
import com.moredian.iothub.control.api.v1.response.UpgradeStatusModel;
import com.moredian.magicube.common.enums.AppSystemType;
import com.moredian.magicube.common.enums.AppType;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.common.model.msg.device.DeviceUpgradeFinishMsg;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.upgrade.*;
import com.moredian.magicube.device.dto.version.AppVersionIncreWithAppVersionInfoDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.dto.version.VersionDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.service.*;
import com.moredian.magicube.device.service.converter.MergeUpdateAppToLastResponseConverter;
import com.moredian.magicube.device.service.helper.DdBizManageHelper;
import com.moredian.ota.api.enums.PlatformType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2018年5月17日
 */
@SI
@Slf4j
public class DeviceUpgradeServiceImpl implements DeviceUpgradeService {

	@Autowired
	private DeviceVersionService deviceVersionService;

	@Autowired
	private AppVersionService appVersionService;

	@Resource
	private DdBizManageHelper ddBizManageHelper;

	@Autowired
	private AppVersionIncrService appVersionIncrService;

	@Autowired
	private DeviceService deviceService;

	@Autowired
	private ApkFileManager apkFileManager;

	@SI
	private HubControlServiceV1 hubControlService;

	@Value("${dd.apkServerRootUrl:http://md-ding-test.oss-cn-hangzhou.aliyuncs.com}")
	private String ddApkServerRootUrl;

	@Value("${molan.apkServerRootUrl:http://md-ali-test.oss-cn-hangzhou.aliyuncs.com}")
	private String moLanApkServerRootUrl;

	/**
	 * (non-Javadoc)
	 *
	 * @see com.moredian.magicube.device.service.DeviceUpgradeService#updateAppToLast(com.moredian.magicube.device.dto.upgrade.UpdateAppToLastRequest)
	 */
	@Override
	public ServiceResponse<Boolean> updateAppToLast(UpdateAppToLastRequest request) {
		log.debug(" --- updateAppToLast --- request: " + (request != null ? JsonUtils.toJson(request) : "NULL")); //for TEST

		if (request == null) { //added by zc
			return (new ServiceResponse<Boolean>(false));
		}

		// 1. 校验入参是否符合要求
		String serialNumber = request.getSerialNumber();
		int systemType = request.getSystemType();
		int appType = request.getAppType();
		BizAssert.notBlank(serialNumber, DeviceErrorCode.PARAMS_WRONG.getError(), "serialNumber");
		BizAssert.greaterZero(systemType, DeviceErrorCode.PARAMS_WRONG.getError(), "systemType");
		BizAssert.greaterZero(appType, DeviceErrorCode.PARAMS_WRONG.getError(), "appType");

		// 2. 获取设备app当前和最新版本号，并判断是否应该升级
		// 获取设备当前版本 和 最新版本
		DeviceInfoDTO deviceInfo = deviceService.getByDeviceSn(serialNumber).pickDataThrowException();
		BizAssert.notNull(deviceInfo, DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
		List<DeviceVersionDTO> deviceVersionDTOS = deviceVersionService.listApkByOrgIdAndDeviceIds(deviceInfo.getOrgId(), Lists.newArrayList(deviceInfo.getDeviceId())).pickDataThrowException();
		DeviceVersionDTO currentVersionDto = deviceVersionDTOS.get(0);
		Integer currentVersionCode = (currentVersionDto == null || currentVersionDto.getVersionCode() == null) ? 0 : currentVersionDto.getVersionCode(); //modified by zc

		// 保证apk发布流程，这里按理不会出现找不到最新版本的情况
		VersionDTO lastVersionDto = appVersionService.getNewAppVersionBySCType(systemType, appType).pickDataThrowException();
		Integer lastVersionCode = lastVersionDto.getVersionCode(); //modified by zc
		if (null != lastVersionCode && currentVersionCode >= lastVersionCode) { //modified by zc
			return ServiceResponse.createSuccessResponse();
		}

		if (currentVersionCode != 0) {
			// 先去掉垮版本升级的限制
			//BizAssert.equals(lastVersionCode, currentVersionCode + 1, DeviceErrorCode.DEVICE_APPUPDATE_CROSSLEVEL.getError(), "");
		} else {
			log.warn("当前设备{} app[{},{}] 本地版本不存在.", serialNumber, systemType, appType);
		}

		// 3. 调用iothub执行升级指令
		UpgradeRequest updateRequest = new UpgradeRequest();
		updateRequest.setDeviceId(String.valueOf(deviceInfo.getDeviceId()));
		String apkUrl;
		//门锁包需要特殊处理，apkUrl信息在extend_info中下发
		if (StringUtils.isNotBlank(lastVersionDto.getAppUrl()) && lastVersionDto.getAppUrl().contains(";")) {
			//由于IOT对url判空，门锁的升级包地址不参与终端业务
			apkUrl = lastVersionDto.getAppUrl();
		} else {
			//获取版本包，如果包在钉钉平台则走原来的逻辑，如果包在魔蓝则需要改变访问路径
			apkUrl = apkFileManager.getApkFileUrl(lastVersionDto.getAppUrl());
		}
		updateRequest.setApkUrl(apkUrl);
		updateRequest.setSerialNumber(serialNumber);
		updateRequest.setAppType(appType);
		updateRequest.setVersionCode(lastVersionDto.getVersionCode());
		if (StringUtils.isNotBlank(lastVersionDto.getExtendInfo())) {
			updateRequest.setExtraInfo(lastVersionDto.getExtendInfo());
		}
		ServiceResponse<UpgradeResponse> upgradeResponse = hubControlService.upgrade(updateRequest);
		upgradeResponse.pickDataThrowException();

		return ServiceResponse.createSuccessResponse();
	}

	/**
	 * (non-Javadoc)
	 *
	 * @see com.moredian.magicube.device.service.DeviceUpgradeService#updateState(com.moredian.magicube.device.dto.upgrade.UpdateStateRequest)
	 */
	@Override
	public ServiceResponse<UpdateStateResponse> updateState(UpdateStateRequest request) {
		log.debug(" --- updateState --- request: " + (request != null ? JsonUtils.toJson(request) : "NULL"));

		if (request == null) {
			return (new ServiceResponse<>(false, null, null));
		}

		DeviceInfoDTO deviceInfo = deviceService.getByDeviceSn(request.getSerialNumber()).pickDataThrowException();
		BizAssert.notNull(deviceInfo, DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());

		UpdateStateResponse response = new UpdateStateResponse();
		DeviceAppUpdateProgressRequest progressRequest = new DeviceAppUpdateProgressRequest();
		progressRequest.setSerialNumber(request.getSerialNumber());
		progressRequest.setSystemType(request.getSystemType());
		progressRequest.setAppType(request.getAppType());
		progressRequest.setDeviceId(String.valueOf(deviceInfo.getDeviceId()));
		DeviceAppUpdateProgressResponse getResult = hubControlService.getAppUpdateProgress(progressRequest).pickDataThrowException();
		int code = 0;
		int details = 0;

		if (getResult != null) {
			response.setVersionCode(getResult.getVersionCode());
			code = getResult.getErrCode();
			details = getResult.getDetails();
		}

		if (code == 1 || code == 2) {
			// 正在升级中
			response.setStatus(1);
		} else if (code == 3) {
			// 升级成功
			response.setStatus(4);
		} else if (code == 4) {
			// 升级失败
			response.setStatus(3);
			// 失败详情
			if (details == 1) {
				// 下载apk失败导致升级失败
			} else if (details == 2) {
				// apk包versionCode和指令下发的目标版本号不一致
			} else if (details == 1000) {
				// 其他未知原因导致升级失败（如果终端能定义出详细的错误原因，请详细列举出来）
			}
		} else {
			response.setStatus(0);
		}

		return new ServiceResponse<>(response);
	}

	@SuppressWarnings("rawtypes")
	@Override
	public ServiceResponse<Boolean> mergeUpdateAppToLast(MergeUpgradeDeviceRequest request) {
		log.debug(" --- mergeUpdateAppToLast --- request: " + (request != null ? JsonUtils.toJson(request) : "NULL"));

		if (request == null) { //added by zc
			return (new ServiceResponse<Boolean>(false));
		}

		String serialNumber = request.getSerialNumber();
		Integer systemType = request.getSystemType();
		//默认安卓
		if (systemType == null) {
			systemType = AppSystemType.ANDROID.getValue();
		}

		DeviceInfoDTO deviceInfo = deviceService.getByDeviceSn(serialNumber).pickDataThrowException();
		BizAssert.notNull(deviceInfo, DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());

		UpgradeRomRequest upgradeRomRequest = null;
		UpgradeRequest upgradeAppRequest = null;

		Long orgId = deviceInfo.getOrgId();
//		if (!orgId.equals(request.getOrgId())) {
//			return new ServiceResponse<Boolean>(false, new ErrorContext(DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage()), null);
//		}

		Long deviceId = deviceInfo.getDeviceId();
		if (deviceId == null) { //added by zc
			log.debug(" --- mergeUpdateAppToLast --- [deviceId == null]"); //for TEST
			return new ServiceResponse<Boolean>(false, new ErrorContext(DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage()), null);
		}

		//rom是否有升级
		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceRomVersion (begin) >>> orgId=" + orgId + ",deviceId=" + deviceId); //for TEST
		ServiceResponse<List<DeviceVersionDTO>> srRomResponse = deviceVersionService.listRomByOrgIdAndDeviceIds(deviceInfo.getOrgId(), Lists.newArrayList(deviceId));
		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceRomVersion (end) >>> romResponse: " + (srRomResponse != null ? JsonUtils.toJson(srRomResponse) : "NULL"));

		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceAppVersion (begin) >>> orgId=" + orgId + ",deviceId=" + deviceId); //for TEST
		ServiceResponse<List<DeviceVersionDTO>> srAppResponse = deviceVersionService.listApkByOrgIdAndDeviceIds(deviceInfo.getOrgId(), Lists.newArrayList(deviceId));
		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceAppVersion (end) >>> appResponse: " + (srAppResponse != null ? JsonUtils.toJson(srAppResponse) : "NULL")); //for TEST

		AppVersionIncreWithAppVersionInfoDTO appVersionIncrWithAppVersionInfoDTO = null;
		if (srRomResponse != null && srRomResponse.isSuccess() && srRomResponse.isExistData()) {
			DeviceVersionDTO currentRomVersionDto = srRomResponse.getData().get(0);
			log.debug(" --- mergeUpdateAppToLast --- mergeUpdateAppToLast.currentRomVersionDto, {}", JsonUtils.toJson(currentRomVersionDto));
			if ((currentRomVersionDto != null) && (currentRomVersionDto.getVersionCode() != null)) {
				Integer appType = currentRomVersionDto.getAppType();
				GetIncrOrFullNewAppRequest getIncrOrFullNewAppRequest = new GetIncrOrFullNewAppRequest();
				getIncrOrFullNewAppRequest.setOrgId(orgId);
				getIncrOrFullNewAppRequest.setAppType(appType);
				getIncrOrFullNewAppRequest.setVersionCode(currentRomVersionDto.getVersionCode());
				if (srAppResponse != null && srAppResponse.isSuccess() && srAppResponse.isExistData()) {
					DeviceVersionDTO currentAppVersionDto = srAppResponse.getData().get(0);
					if (currentAppVersionDto != null) {
						getIncrOrFullNewAppRequest.setApkAppType(currentAppVersionDto.getAppType());
						ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> srRomIncrResponse;
						if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
							srRomIncrResponse = new ServiceResponse<>(ddBizManageHelper.getNewAppVersionIncreByAppTypeAndOldVersion(getIncrOrFullNewAppRequest));
						}else {
							srRomIncrResponse = appVersionIncrService
									.getNewAppVersionIncreByAppTypeAndOldVersion(getIncrOrFullNewAppRequest);
						}
						if (srRomIncrResponse != null && srRomIncrResponse.isSuccess() && srRomIncrResponse.isExistData()) { //modified by zc
							appVersionIncrWithAppVersionInfoDTO = srRomIncrResponse.getData();
							if (appVersionIncrWithAppVersionInfoDTO != null && appVersionIncrWithAppVersionInfoDTO.getIsUpdate()) { //modified by zc
								upgradeRomRequest = new UpgradeRomRequest();
								upgradeRomRequest.setAppType(appType);
								upgradeRomRequest.setDeviceId(deviceId.toString());
								upgradeRomRequest.setIsIncre(appVersionIncrWithAppVersionInfoDTO.getIsIncre());
								upgradeRomRequest.setPreVersionCode(appVersionIncrWithAppVersionInfoDTO.getPreVersionCode());
								upgradeRomRequest.setSerialNumber(serialNumber);
								String romUrl;
								if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
									if (PlatformType.TOWER.getValue().equals(appVersionIncrWithAppVersionInfoDTO.getStorageLocation())) {
										romUrl = ddApkServerRootUrl + appVersionIncrWithAppVersionInfoDTO.getUrl();
									}else {
										romUrl = moLanApkServerRootUrl + appVersionIncrWithAppVersionInfoDTO.getUrl();
									}
								}else {
									romUrl = apkFileManager.getApkFileUrl(appVersionIncrWithAppVersionInfoDTO.getUrl());
								}
								upgradeRomRequest.setUrl(romUrl);
								upgradeRomRequest.setVersionCode(appVersionIncrWithAppVersionInfoDTO.getVersionCode());
							}
						}
					}
				}
			} else {
				//没取到rom版本直接返回失败。避免先升了app，导致严重的问题
				return new ServiceResponse<Boolean>(false, new ErrorContext(DeviceErrorCode.DEVICE_ROM_VERSION_GET_ERROR, DeviceErrorCode.DEVICE_ROM_VERSION_GET_ERROR.getMessage()), false);
			}
		} else {
			//没取到rom版本直接返回失败。避免先升了app，导致严重的问题
			return new ServiceResponse<Boolean>(false, new ErrorContext(DeviceErrorCode.DEVICE_ROM_VERSION_GET_ERROR, DeviceErrorCode.DEVICE_ROM_VERSION_GET_ERROR.getMessage()), false);
		}

		//app是否升级，这段代码先抄上面的
		if (srAppResponse != null && srAppResponse.isSuccess() && srAppResponse.isExistData()) {
			DeviceVersionDTO currentAppVersionDto = srAppResponse.getData().get(0);
			log.debug(" --- mergeUpdateAppToLast --- mergeUpdateAppToLast.currentVersionDto, {}", JsonUtils.toJson(currentAppVersionDto));
			if ((currentAppVersionDto != null) && (currentAppVersionDto.getVersionCode() != null)) {
				Integer currentVersionCode = currentAppVersionDto.getVersionCode();
				Integer appType = currentAppVersionDto.getAppType();
				// 保证apk发布流程，这里按理不会出现找不到最新版本的情况
				log.debug(" --- mergeUpdateAppToLast --- appVersionService.getNewAppVersionBySCTypeAndIsActiveAndOrgId (begin) >>> orgId=" + orgId + ",systemType=" + systemType + ",appType=" + appType); //for TEST
				ServiceResponse<VersionDTO> srLastAppVersionDto;
				//判断是否是分销融元设备：如果是分销转到塔上请求
				if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
					srLastAppVersionDto = new ServiceResponse<>(ddBizManageHelper.getNewAppVersionBySCTypeAndIsActiveAndOrgId(orgId, systemType, appType));
				}else {
					srLastAppVersionDto = appVersionService.getNewAppVersionBySCTypeAndIsActiveAndOrgId(orgId, systemType, appType);
				}
				log.debug(" --- mergeUpdateAppToLast --- appVersionService.getNewAppVersionBySCTypeAndIsActiveAndOrgId (end) >>> srLastAppVersionDto: " + (srLastAppVersionDto != null ? JsonUtils.toJson(srLastAppVersionDto) : "NULL")); //for TEST
				if (srLastAppVersionDto != null && srLastAppVersionDto.isSuccess() && srLastAppVersionDto.isExistData()) { //added by zc
					VersionDTO lastAppVersionDto = srLastAppVersionDto.getData();
					if (lastAppVersionDto != null) { //added by zc
						Integer lastAppVersionCode = lastAppVersionDto.getVersionCode(); //modified by zc
						log.debug(" --- mergeUpdateAppToLast --- lastAppVersionCode=" + lastAppVersionCode + ",currentVersionCode=" + currentVersionCode); //for TEST
						if (null != lastAppVersionCode && null != currentVersionCode && lastAppVersionCode > currentVersionCode) { //modified by zc
							//如果rom里面携带了apk，并且携带的apk的版本比单独升级的apk版本要高，则不单独升级apk。只升级rom即可
							if (appVersionIncrWithAppVersionInfoDTO != null && appVersionIncrWithAppVersionInfoDTO.getApkAppType() != null
									&& appVersionIncrWithAppVersionInfoDTO.getApkVersionCode() != null && appVersionIncrWithAppVersionInfoDTO.getApkVersionCode() >= lastAppVersionCode) {
								//不作处理
							} else {
								upgradeAppRequest = new UpgradeRequest();
								upgradeAppRequest.setDeviceId(String.valueOf(deviceId));
								String apkUrl;
								//门锁包需要特殊处理，apkUrl信息在extend_info中下发
								if (StringUtils.isNotBlank(lastAppVersionDto.getAppUrl()) && lastAppVersionDto.getAppUrl().contains(";")) {
									apkUrl = lastAppVersionDto.getAppUrl();
								} else {
									//获取版本包，如果包在钉钉平台则走原来的逻辑，如果包在魔蓝则需要改变访问路径
									if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
										if (PlatformType.TOWER.getValue().equals(lastAppVersionDto.getStorageLocation())) {
											apkUrl = ddApkServerRootUrl + lastAppVersionDto.getAppUrl();
										}else {
											apkUrl = moLanApkServerRootUrl + lastAppVersionDto.getAppUrl();
										}
									}else {
										apkUrl = apkFileManager.getApkFileUrl(lastAppVersionDto.getAppUrl());
									}
								}
								upgradeAppRequest.setApkUrl(apkUrl);
								upgradeAppRequest.setSerialNumber(serialNumber);
								upgradeAppRequest.setAppType(appType);
								upgradeAppRequest.setVersionCode(lastAppVersionDto.getVersionCode());
								upgradeAppRequest.setUpgradeEventType(this.judgeMergeAppUpgradeTypeEvent(appType, currentVersionCode));
								if (StringUtils.isNotBlank(lastAppVersionDto.getExtendInfo())) {
									upgradeAppRequest.setExtraInfo(lastAppVersionDto.getExtendInfo());
								}
								log.debug(" --- mergeUpdateAppToLast --- upgradeAppRequest: " + JsonUtils.toJson(upgradeAppRequest)); //for TEST
							}
						}
					}
				}
			}
		}

		log.info("mergeUpdateAppToLast, {}, {}", JsonUtils.toJson(upgradeRomRequest), JsonUtils.toJson(upgradeAppRequest));

		if ((upgradeRomRequest == null) && (upgradeAppRequest == null)) {
			//不需要升级,data返回false
			return new ServiceResponse<Boolean>(true, null, false);
		} else {
			MergeUpdateRequest mergeUpdateRequest = new MergeUpdateRequest();
			mergeUpdateRequest.setAppUpdateInfo(upgradeAppRequest);
			mergeUpdateRequest.setDeviceId(deviceId.toString());
			mergeUpdateRequest.setRomUpdateInfo(upgradeRomRequest);
			mergeUpdateRequest.setSerialNumber(serialNumber);
			log.info("mergeUpdateAppToLast.mergeUpdateRequest, {}", JsonUtils.toJson(mergeUpdateRequest));

			ServiceResponse response = hubControlService.mergeUpgrade(mergeUpdateRequest);
			log.debug(" --- mergeUpdateAppToLast --- hubControlService.mergeUpgrade (result) >>> response: " + (response != null ? JsonUtils.toJson(response) : "NULL")); //for TEST
			response.pickDataThrowException();
		}

		DeviceUpgradeFinishMsg deviceUpgradeFinishMsg = new DeviceUpgradeFinishMsg();
		deviceUpgradeFinishMsg.setDeviceId(deviceId);
		deviceUpgradeFinishMsg.setOrgId(deviceInfo.getOrgId());
		deviceUpgradeFinishMsg.setDeviceSn(deviceInfo.getDeviceSn());
		deviceUpgradeFinishMsg.setDeviceType(deviceInfo.getDeviceType());
		deviceUpgradeFinishMsg.setTimestamp(System.currentTimeMillis());
		EventBus.publish(deviceUpgradeFinishMsg);
		log.info("设备升级成功消息:{}",deviceUpgradeFinishMsg);
		return new ServiceResponse<>(true, null, true);
	}

	@Override
	public ServiceResponse<MergeUpdateAppToLastResponse> mergeUpdateAppToLastReturn(MergeUpgradeDeviceRequest request) {
		log.debug(" --- mergeUpdateAppToLast --- request: " + (request != null ? JsonUtils.toJson(request) : "NULL"));

		MergeUpdateRequest mergeUpdateRequest = null;
		if (request == null) { //added by zc
			return new ServiceResponse(MergeUpdateAppToLastResponseConverter.convert(mergeUpdateRequest));
		}

		String serialNumber = request.getSerialNumber();
		Integer systemType = request.getSystemType();
		//默认安卓
		if (systemType == null) {
			systemType = AppSystemType.ANDROID.getValue();
		}

		DeviceInfoDTO deviceInfo = deviceService.getByDeviceSn(serialNumber).pickDataThrowException();
		BizAssert.notNull(deviceInfo, DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());

		UpgradeRomRequest upgradeRomRequest = null;
		UpgradeRequest upgradeAppRequest = null;

		Long orgId = deviceInfo.getOrgId();
//		if (!orgId.equals(request.getOrgId())) {
//			return new ServiceResponse<Boolean>(false, new ErrorContext(DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage()), null);
//		}

		Long deviceId = deviceInfo.getDeviceId();
		if (deviceId == null) { //added by zc
			log.debug(" --- mergeUpdateAppToLast --- [deviceId == null]"); //for TEST
			return new ServiceResponse(MergeUpdateAppToLastResponseConverter.convert(mergeUpdateRequest));
		}

		//rom是否有升级
		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceRomVersion (begin) >>> orgId=" + orgId + ",deviceId=" + deviceId); //for TEST
		ServiceResponse<List<DeviceVersionDTO>> srRomResponse = deviceVersionService.listRomByOrgIdAndDeviceIds(deviceInfo.getOrgId(), Lists.newArrayList(deviceId));
		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceRomVersion (end) >>> romResponse: " + (srRomResponse != null ? JsonUtils.toJson(srRomResponse) : "NULL"));

		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceAppVersion (begin) >>> orgId=" + orgId + ",deviceId=" + deviceId); //for TEST
		ServiceResponse<List<DeviceVersionDTO>> srAppResponse = deviceVersionService.listApkByOrgIdAndDeviceIds(deviceInfo.getOrgId(), Lists.newArrayList(deviceId));
		log.debug(" --- mergeUpdateAppToLast --- deviceVersionClientManager.getBatchDeviceAppVersion (end) >>> appResponse: " + (srAppResponse != null ? JsonUtils.toJson(srAppResponse) : "NULL")); //for TEST

		AppVersionIncreWithAppVersionInfoDTO appVersionIncrWithAppVersionInfoDTO = null;
		if (srRomResponse != null && srRomResponse.isSuccess() && srRomResponse.isExistData()) {
			DeviceVersionDTO currentRomVersionDto = srRomResponse.getData().get(0);
			log.debug(" --- mergeUpdateAppToLast --- mergeUpdateAppToLast.currentRomVersionDto, {}", JsonUtils.toJson(currentRomVersionDto));
			if ((currentRomVersionDto != null) && (currentRomVersionDto.getVersionCode() != null)) {
				Integer appType = currentRomVersionDto.getAppType();
				GetIncrOrFullNewAppRequest getIncrOrFullNewAppRequest = new GetIncrOrFullNewAppRequest();
				getIncrOrFullNewAppRequest.setOrgId(orgId);
				getIncrOrFullNewAppRequest.setAppType(appType);
				getIncrOrFullNewAppRequest.setVersionCode(currentRomVersionDto.getVersionCode());
				if (srAppResponse != null && srAppResponse.isSuccess() && srAppResponse.isExistData()) {
					DeviceVersionDTO currentAppVersionDto = srAppResponse.getData().get(0);
					if (currentAppVersionDto != null) {
						getIncrOrFullNewAppRequest.setApkAppType(currentAppVersionDto.getAppType());
						ServiceResponse<AppVersionIncreWithAppVersionInfoDTO> srRomIncrResponse;
						if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
							srRomIncrResponse = new ServiceResponse<>(ddBizManageHelper.getNewAppVersionIncreByAppTypeAndOldVersion(getIncrOrFullNewAppRequest));
						}else {
							srRomIncrResponse = appVersionIncrService
									.getNewAppVersionIncreByAppTypeAndOldVersion(getIncrOrFullNewAppRequest);
						}
						if (srRomIncrResponse != null && srRomIncrResponse.isSuccess() && srRomIncrResponse.isExistData()) { //modified by zc
							appVersionIncrWithAppVersionInfoDTO = srRomIncrResponse.getData();
							if (appVersionIncrWithAppVersionInfoDTO != null && appVersionIncrWithAppVersionInfoDTO.getIsUpdate()) { //modified by zc
								upgradeRomRequest = new UpgradeRomRequest();
								upgradeRomRequest.setAppType(appType);
								upgradeRomRequest.setDeviceId(deviceId.toString());
								upgradeRomRequest.setIsIncre(appVersionIncrWithAppVersionInfoDTO.getIsIncre());
								upgradeRomRequest.setPreVersionCode(appVersionIncrWithAppVersionInfoDTO.getPreVersionCode());
								upgradeRomRequest.setSerialNumber(serialNumber);
								String romUrl;
								if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
									if (PlatformType.TOWER.getValue().equals(appVersionIncrWithAppVersionInfoDTO.getStorageLocation())) {
										romUrl = ddApkServerRootUrl + appVersionIncrWithAppVersionInfoDTO.getUrl();
									}else {
										romUrl = moLanApkServerRootUrl + appVersionIncrWithAppVersionInfoDTO.getUrl();
									}
								}else {
									romUrl = apkFileManager.getApkFileUrl(appVersionIncrWithAppVersionInfoDTO.getUrl());
								}
								upgradeRomRequest.setUrl(romUrl);
								upgradeRomRequest.setVersionCode(appVersionIncrWithAppVersionInfoDTO.getVersionCode());
							}
						}
					}
				}
			} else {
				//没取到rom版本直接返回失败。避免先升了app，导致严重的问题
				return new ServiceResponse(MergeUpdateAppToLastResponseConverter.convert(mergeUpdateRequest));
			}
		} else {
			//没取到rom版本直接返回失败。避免先升了app，导致严重的问题
			return new ServiceResponse(MergeUpdateAppToLastResponseConverter.convert(mergeUpdateRequest));
		}

		//app是否升级，这段代码先抄上面的
		if (srAppResponse != null && srAppResponse.isSuccess() && srAppResponse.isExistData()) {
			DeviceVersionDTO currentAppVersionDto = srAppResponse.getData().get(0);
			log.debug(" --- mergeUpdateAppToLast --- mergeUpdateAppToLast.currentVersionDto, {}", JsonUtils.toJson(currentAppVersionDto));
			if ((currentAppVersionDto != null) && (currentAppVersionDto.getVersionCode() != null)) {
				Integer currentVersionCode = currentAppVersionDto.getVersionCode();
				Integer appType = currentAppVersionDto.getAppType();
				// 保证apk发布流程，这里按理不会出现找不到最新版本的情况
				log.debug(" --- mergeUpdateAppToLast --- appVersionService.getNewAppVersionBySCTypeAndIsActiveAndOrgId (begin) >>> orgId=" + orgId + ",systemType=" + systemType + ",appType=" + appType); //for TEST
				ServiceResponse<VersionDTO> srLastAppVersionDto;
				//判断是否是分销融元设备：如果是分销转到塔上请求
				if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
					srLastAppVersionDto = new ServiceResponse<>(ddBizManageHelper.getNewAppVersionBySCTypeAndIsActiveAndOrgId(orgId, systemType, appType));
				}else {
					srLastAppVersionDto = appVersionService.getNewAppVersionBySCTypeAndIsActiveAndOrgId(orgId, systemType, appType);
				}
				log.debug(" --- mergeUpdateAppToLast --- appVersionService.getNewAppVersionBySCTypeAndIsActiveAndOrgId (end) >>> srLastAppVersionDto: " + (srLastAppVersionDto != null ? JsonUtils.toJson(srLastAppVersionDto) : "NULL")); //for TEST
				if (srLastAppVersionDto != null && srLastAppVersionDto.isSuccess() && srLastAppVersionDto.isExistData()) { //added by zc
					VersionDTO lastAppVersionDto = srLastAppVersionDto.getData();
					if (lastAppVersionDto != null) { //added by zc
						Integer lastAppVersionCode = lastAppVersionDto.getVersionCode(); //modified by zc
						log.debug(" --- mergeUpdateAppToLast --- lastAppVersionCode=" + lastAppVersionCode + ",currentVersionCode=" + currentVersionCode); //for TEST
						if (null != lastAppVersionCode && null != currentVersionCode && lastAppVersionCode > currentVersionCode) { //modified by zc
							//如果rom里面携带了apk，并且携带的apk的版本比单独升级的apk版本要高，则不单独升级apk。只升级rom即可
							if (appVersionIncrWithAppVersionInfoDTO != null && appVersionIncrWithAppVersionInfoDTO.getApkAppType() != null
									&& appVersionIncrWithAppVersionInfoDTO.getApkVersionCode() != null && appVersionIncrWithAppVersionInfoDTO.getApkVersionCode() >= lastAppVersionCode) {
								//不作处理
							} else {
								upgradeAppRequest = new UpgradeRequest();
								upgradeAppRequest.setDeviceId(String.valueOf(deviceId));
								String apkUrl;
								//门锁包需要特殊处理，apkUrl信息在extend_info中下发
								if (StringUtils.isNotBlank(lastAppVersionDto.getAppUrl()) && lastAppVersionDto.getAppUrl().contains(";")) {
									apkUrl = lastAppVersionDto.getAppUrl();
								} else {
									//获取版本包，如果包在钉钉平台则走原来的逻辑，如果包在魔蓝则需要改变访问路径
									if (deviceInfo.getDeviceFlag().equals(YesNoFlag.YES.getValue())){
										if (PlatformType.TOWER.getValue().equals(lastAppVersionDto.getStorageLocation())) {
											apkUrl = ddApkServerRootUrl + lastAppVersionDto.getAppUrl();
										}else {
											apkUrl = moLanApkServerRootUrl + lastAppVersionDto.getAppUrl();
										}
									}else {
										apkUrl = apkFileManager.getApkFileUrl(lastAppVersionDto.getAppUrl());
									}
								}
								upgradeAppRequest.setApkUrl(apkUrl);
								upgradeAppRequest.setSerialNumber(serialNumber);
								upgradeAppRequest.setAppType(appType);
								upgradeAppRequest.setVersionCode(lastAppVersionDto.getVersionCode());
								upgradeAppRequest.setUpgradeEventType(this.judgeMergeAppUpgradeTypeEvent(appType, currentVersionCode));
								if (StringUtils.isNotBlank(lastAppVersionDto.getExtendInfo())) {
									upgradeAppRequest.setExtraInfo(lastAppVersionDto.getExtendInfo());
								}
								log.debug(" --- mergeUpdateAppToLast --- upgradeAppRequest: " + JsonUtils.toJson(upgradeAppRequest)); //for TEST
							}
						}
					}
				}
			}
		}

		log.info("mergeUpdateAppToLast, {}, {}", JsonUtils.toJson(upgradeRomRequest), JsonUtils.toJson(upgradeAppRequest));

		if ((upgradeRomRequest == null) && (upgradeAppRequest == null)) {
			//不需要升级,data返回false
			return new ServiceResponse(MergeUpdateAppToLastResponseConverter.convert(mergeUpdateRequest));
		} else {
			mergeUpdateRequest = new MergeUpdateRequest();
			mergeUpdateRequest.setAppUpdateInfo(upgradeAppRequest);
			mergeUpdateRequest.setDeviceId(deviceId.toString());
			mergeUpdateRequest.setRomUpdateInfo(upgradeRomRequest);
			mergeUpdateRequest.setSerialNumber(serialNumber);
			log.info("mergeUpdateAppToLast.mergeUpdateRequest, {}", JsonUtils.toJson(mergeUpdateRequest));

			ServiceResponse response = hubControlService.mergeUpgrade(mergeUpdateRequest);
			log.debug(" --- mergeUpdateAppToLast --- hubControlService.mergeUpgrade (result) >>> response: " + (response != null ? JsonUtils.toJson(response) : "NULL")); //for TEST
			response.pickDataThrowException();
		}

		DeviceUpgradeFinishMsg deviceUpgradeFinishMsg = new DeviceUpgradeFinishMsg();
		deviceUpgradeFinishMsg.setDeviceId(deviceId);
		deviceUpgradeFinishMsg.setOrgId(deviceInfo.getOrgId());
		deviceUpgradeFinishMsg.setDeviceSn(deviceInfo.getDeviceSn());
		deviceUpgradeFinishMsg.setDeviceType(deviceInfo.getDeviceType());
		deviceUpgradeFinishMsg.setTimestamp(System.currentTimeMillis());
		EventBus.publish(deviceUpgradeFinishMsg);
		log.info("设备升级成功消息:{}",deviceUpgradeFinishMsg);
		return new ServiceResponse(MergeUpdateAppToLastResponseConverter.convert(mergeUpdateRequest));
	}

	@Override
	public ServiceResponse<MergeUpgradeDeviceStatusResponse> mergeUpdateState(
		MergeUpgradeDeviceStatusRequest request) {
		if (request == null) {
			return (new ServiceResponse<>(false,
				new ErrorContext(CommonErrorCode.ILLEGAL_ARGUMENT_EXCEPTION.getCode(),
					CommonErrorCode.ILLEGAL_ARGUMENT_EXCEPTION.getMessage()), null));
		}

		String serialNumber = request.getSerialNumber();
		DeviceInfoDTO deviceInfo = deviceService.getByDeviceSn(serialNumber).pickDataThrowException();
		BizAssert.notNull(deviceInfo, DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
		log.debug(" --- mergeUpdateState --- deviceInfo (serialNumber=" + serialNumber + ") >>> deviceInfo: " + (deviceInfo != null ? JsonUtils.toJson(deviceInfo) : "NULL")); //for TEST

		Long orgId = deviceInfo.getOrgId();
		if (orgId == null || orgId < 0) {
			return new ServiceResponse<>(false, new ErrorContext(DeviceErrorCode.DEVICE_NOT_EXIST,
				DeviceErrorCode.DEVICE_NOT_EXIST.getMessage()), null);
		}

		Long deviceId = deviceInfo.getDeviceId();
		if (deviceId == null || deviceId < 0) {
			return new ServiceResponse<>(false, new ErrorContext(DeviceErrorCode.DEVICE_NOT_EXIST,
				DeviceErrorCode.DEVICE_NOT_EXIST.getMessage()), null);
		}

		MergeUpgradeStatusRequest hubRequest = new MergeUpgradeStatusRequest();
		hubRequest.setDeviceId(deviceId.toString());
		hubRequest.setSerialNumber(serialNumber);
		ServiceResponse<MergeUpgradeStatusResponse> response = hubControlService.getMergeUpdateStatus(hubRequest);
		MergeUpgradeDeviceStatusResponse mergeUpgradeDeviceStatusResponse = mergeUpgradeStatusResponseToMergeUpgradeStatusResponse(response.pickDataThrowException()); //modified by zc

		return new ServiceResponse<>(true, null, mergeUpgradeDeviceStatusResponse);
	}

	//modified by zc
	private MergeUpgradeDeviceStatusResponse mergeUpgradeStatusResponseToMergeUpgradeStatusResponse(MergeUpgradeStatusResponse source) {
		log.debug(" --- mergeUpgradeStatusResponseToMergeUpgradeStatusResponse --- source: " + (source != null ? JsonUtils.toJson(source) : "NULL")); //for TEST

		if (source == null) {
			return null;
		}

		MergeUpgradeDeviceStatusResponse result = new MergeUpgradeDeviceStatusResponse();
		result.setGlobalStatus(source.getGlobalStatus());

		List<DeviceUpgradeStatusModel> statusList = new ArrayList<>();

		UpgradeStatusModel romStatus = source.getRomStatus();
		DeviceUpgradeStatusModel romStatusModel = upgradeStatusModelToDeviceUpgradeStatusModel(romStatus);
		if (romStatusModel != null) {
			romStatusModel.setUpgradeType(EnumUpgradeType.ROM.getValue());
			statusList.add(romStatusModel);
			log.debug(" --- mergeUpgradeStatusResponseToMergeUpgradeStatusResponse --- romStatusModel: " + JsonUtils.toJson(romStatusModel)); //for TEST
		}

		UpgradeStatusModel appStatus = source.getAppStatus();
		DeviceUpgradeStatusModel appStatusModel = upgradeStatusModelToDeviceUpgradeStatusModel(appStatus);
		if (appStatusModel != null) {
			appStatusModel.setUpgradeType(EnumUpgradeType.APP.getValue());
			statusList.add(appStatusModel);
			log.debug(" --- mergeUpgradeStatusResponseToMergeUpgradeStatusResponse --- appStatusModel: " + JsonUtils.toJson(appStatusModel)); //for TEST
		}

		result.setStatusList(statusList);

		log.debug(" --- mergeUpgradeStatusResponseToMergeUpgradeStatusResponse --- result: " + JsonUtils.toJson(result)); //for TEST
		return result;
	}

	private DeviceUpgradeStatusModel upgradeStatusModelToDeviceUpgradeStatusModel(UpgradeStatusModel source) {
		if (source == null) {
			return null;
		}

		DeviceUpgradeStatusModel deviceUpgradeStatusModel = new DeviceUpgradeStatusModel();
		deviceUpgradeStatusModel.setAppType(source.getAppType());
		deviceUpgradeStatusModel.setErrorNo(source.getErrorNo());
		deviceUpgradeStatusModel.setProgress(source.getProgress());
		deviceUpgradeStatusModel.setUpgradeStatus(source.getUpgradeStatus());
		deviceUpgradeStatusModel.setVersionCode(source.getVersionCode());
		//deviceUpgradeStatusModel.setTaskId(source.getTaskId());

		return deviceUpgradeStatusModel;
	}

	/**
	 * 判断使用的升级推送时间类型，根据不同的appType和versionCode发送不同的事件，因为老包并不支持新的事件，需要兼容。
	 *
	 * @param appType
	 * @param versionCode
	 * @return
	 */
	private Integer judgeMergeAppUpgradeTypeEvent(int appType, int versionCode) {
		//现在只有D2使用老的方式，其他都用新的
		if (AppType.D2_DING.equals(appType)) {
			return UpdateEventType.UPGRADE_DEVICE.getValue();
		} else {
			return UpdateEventType.UPGRADE_APP.getValue();
		}
	}
}
