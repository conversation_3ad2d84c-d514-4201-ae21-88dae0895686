package com.moredian.magicube.device.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
public class JacksonUtil {

    public final static ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        // 这里添加是为了解决web接口long数据精度不足的问题
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        OBJECT_MAPPER.registerModule(simpleModule);

        OBJECT_MAPPER.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        OBJECT_MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        OBJECT_MAPPER.disable(SerializationFeature.WRITE_NULL_MAP_VALUES);
    }

    /**
     * 使用泛型方法，把json字符串转换为相应的JavaBean对象。
     *
     * @param jsonStr   json字符串
     * @param valueType 对象类型
     * @return JavaBean对象
     */
    public static <T> T readValue(String jsonStr, Class<T> valueType) {
        try {
            return OBJECT_MAPPER.readValue(jsonStr, valueType);
        } catch (IOException e) {
            log.error("json字符串转对象出现异常", e);
            throw new RuntimeException("json字符串转对象出现异常");
        }
    }

    public static <T> List<T> readListValue(String jsonStr, Class<T> valueType) {
        try {
            JavaType javaType = getCollectionType(ArrayList.class, valueType);
            return OBJECT_MAPPER.readValue(jsonStr, javaType);
        } catch (IOException e) {
            log.error("json字符串转对象出现异常", e);
            throw new RuntimeException("json字符串转对象出现异常");
        }
    }

    private static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return OBJECT_MAPPER.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }


    /**
     * json字符串转java对象
     *
     * @param jsonStr      json字符串
     * @param valueTypeRef 对象类型
     * @return 对象
     */
    public static <T> T readValue(String jsonStr, TypeReference<T> valueTypeRef) {
        try {
            return OBJECT_MAPPER.readValue(jsonStr, valueTypeRef);
        } catch (IOException e) {
            log.error("json字符串转对象出现异常", e);
            throw new RuntimeException("json字符串转对象出现异常");
        }
    }

    /**
     * 把JavaBean转换为json字符串
     *
     * @param object java 对象
     * @return json字符串
     */
    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转json字符串出现异常", e);
            throw new RuntimeException("对象转json字符串出现异常");
        }
    }
}
