package com.moredian.magicube.device.dao.entity.qrcode;

import java.util.Date;
import lombok.Data;

/**
 * @Classname： QrCodeActivateRecord
 * @Date: 2023/1/12 10:26 上午
 * @Author: _AF
 * @Description: 二维码激活记录
 */
@Data
public class QrCodeActivateRecord {

    private Long id;

    private Long orgId;
    /**
     * 激活码id
     */
    private Long qrCodeId;
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private Integer deviceType;

    private Date gmtCreate;

    private Date gmtModify;
}
