package com.moredian.magicube.device.dao.mapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.moredian.magicube.device.dao.entity.ThirdDeviceRelation;

import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Mapper
public interface ThirdDeviceRelationMapper{

    ThirdDeviceRelation getByTpIdAndTpType(@Param("tpId") String tpId, @Param("tpType") Integer tpType);


}
