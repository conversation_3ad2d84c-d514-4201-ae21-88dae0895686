package com.moredian.magicube.device.manager;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dto.device.AppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.PageAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.QueryAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.SaveAppTypeRedirectDTO;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/18
 */
public interface AppTypeRedirectManager {

    /**
     * 新增或修改appType跳转链接
     *
     * @param dto
     * @return
     */
    Long save(SaveAppTypeRedirectDTO dto);

    /**
     * 获取appType跳转详情
     *
     * @param dto
     * @return
     */
    AppTypeRedirectDTO detail(QueryAppTypeRedirectDTO dto);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean deleteById(Long id);

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    Pagination<AppTypeRedirectDTO> page(PageAppTypeRedirectDTO dto);
}
