package com.moredian.magicube.device.pipeline.core.pipeline;


import com.moredian.magicube.device.pipeline.core.pipeline.rollback.RollBack;
import com.moredian.magicube.device.pipeline.core.pipeline.success.Success;

import java.util.List;

/**
 * 调用链
 *
 * <AUTHOR>
 */
public interface InvocationChain<T, S> {

    /**
     * 执行调用管道
     */
    void invoke();

    /**
     * 执行调用下一个管道
     */
    void invokeNext();

    /**
     * 获取入参
     *
     * @return 入参
     */
    T getParameter();

    /**
     * 获取上下文信息
     *
     * @return 上下文信息
     */
    S getContext();

    /**
     * 这条管道流执行的唯一标识 方便日志查找
     *
     * @return key
     */
    String getKey();

    /**
     * 设置key
     *
     * @param key 唯一标识
     */
    void setKey(String key);

    /**
     * 获取回滚list
     *
     * @return
     */
    List<RollBack<T, S>> getRollBackList();

    /**
     * 获取成功之后执行的list
     *
     * @return
     */
    List<Success<T, S>> getSuccessList();
}
