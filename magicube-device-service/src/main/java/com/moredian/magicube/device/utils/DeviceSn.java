package com.moredian.magicube.device.utils;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class DeviceSn {
    //设备sn中的设备类型
    public static String SN_DEVICE_TYPE_D2 = "09";

    //1,2，设备类型（可字母）
    private String deviceType;
    //3,4，设备子版本（可字母）
    private String subVersion;
    //5,6，组装厂
    private String factory;
    //7,8，生产年，（比如：17）
    private int year;
    //9,10, 生产月（01-12）
    private int month;
    //11,12, 生产日（01-31）
    private int day;
    //13 颜色（白色：W 黑色：K 红色: R 黄色；Y 蓝色；B 绿色：G 粉色：P 青色：C）
    private String color;
    //14 N 新机 R 翻新
    private String reuseType;
    //15,16,17,18 序号
    private String number;

    public DeviceSn(String sn) {
        this.deviceType = sn.substring(0, 2);
        this.subVersion = sn.substring(2, 4);
        this.factory = sn.substring(4, 6);
        this.year = Integer.parseInt(sn.substring(6, 8));
        this.month = Integer.parseInt(sn.substring(8, 10));
        this.day = Integer.parseInt(sn.substring(10, 12));
        this.color = sn.substring(12, 13);
        this.reuseType = sn.substring(13, 14);
        this.number = sn.substring(14, 18);
    }

    public Date getProductDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, Integer.parseInt("20" + year));
        c.set(Calendar.MONTH, month - 1);
        c.set(Calendar.DAY_OF_MONTH, day);
        c.set(Calendar.HOUR, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }
}
