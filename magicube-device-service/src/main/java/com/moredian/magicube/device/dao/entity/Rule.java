package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_rule")
public class Rule extends TimedEntity {

    @TableId
    private Long ruleId;

    private String ruleName;

    private Integer ruleType;

    private Integer modeType;

    private String description;

    private Integer triggerType;

    private String triggerValue;

    private String deviceType;

    private Integer state;

    private Integer delFlag;

    private Long orgId;

    private Long templateId;

    private String ruleJson;


    /**
     * 场景Id
     */
    private String sceneId;

    /**
     * 应用场景id，默认36：会议室，35：数智教室
     */
    private Integer spaceType;

    /**
     * 规则是否人为优先
     */
    private Boolean humanPriority;
}