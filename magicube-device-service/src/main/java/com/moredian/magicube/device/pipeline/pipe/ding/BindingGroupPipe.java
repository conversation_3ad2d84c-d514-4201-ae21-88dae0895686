package com.moredian.magicube.device.pipeline.pipe.ding;

import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.manager.DeviceGroupManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设备绑定权限组管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BindingGroupPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private DeviceGroupManager deviceGroupManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        Device device = context.getDevice();
        // 设备绑定默认群组
        deviceGroupManager.insertDefaultDeviceGroup(device.getOrgId(), device.getDeviceId());
    }

}
