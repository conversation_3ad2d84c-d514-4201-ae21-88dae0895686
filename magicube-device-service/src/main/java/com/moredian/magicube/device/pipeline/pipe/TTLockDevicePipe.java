package com.moredian.magicube.device.pipeline.pipe;

import com.google.common.collect.Lists;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.req.DeviceSaveRequest;
import com.moredian.device.pipeline.api.DevicePipelineStateService;
import com.moredian.device.pipeline.dto.request.UpdateDeviceSceneTypeReq;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.lock.NetworkInfoDTO;
import com.moredian.magicube.device.dto.network.InsertDeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import com.moredian.magicube.device.helper.SpaceHelper;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.magicube.device.pipeline.core.pipeline.InvocationChain;
import com.moredian.magicube.device.pipeline.core.pipeline.rollback.RollBack;
import com.moredian.magicube.device.service.DeviceNetworkInfoService;
import com.moredian.magicube.device.third.lock.TTLockOpenService;
import com.moredian.magicube.device.third.lock.req.UploadWifiReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 魔链设备操作管道
 * @create 2025-03-31 14:21
 */
@Slf4j
@Component
public class TTLockDevicePipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext>
    implements RollBack<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;
    @SI
    private DeviceNetworkInfoService deviceNetworkInfoService;
    @SI
    private DevicePipelineStateService devicePipelineStateService;


    @Resource
    private TTLockOpenService ttLockOpenService;
    @Resource
    private RedissonCacheComponent redissonCacheComponent;
    @Resource
    private SpaceHelper spaceHelper;


    @Override
    protected boolean isFilter(ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext activateDeviceContext) {
        Integer deviceType = activateDeviceDTO.getDeviceType();
        // 不是通通锁设备不进入管道
        return deviceType == null || deviceType != DeviceType.TTLOCK.getValue();
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext activateDeviceContext) {
        Device device = activateDeviceContext.getDevice();

        // 上报网络信息
        uploadWifiInfo(device);

        // 新增魔链设备
        DeviceSaveRequest deviceSaveRequest = new DeviceSaveRequest();
        deviceSaveRequest.setProductId(activateDeviceDTO.getJetLinkProductId());
        deviceSaveRequest.setId(activateDeviceDTO.getDeviceSn());
        deviceSaveRequest.setName(device.getDeviceName());
        List<DeviceSaveRequest> saveList = Lists.newArrayList(deviceSaveRequest);
        iotDeviceInstanceService.batchSaveDeviceAndDeploy(-1L, saveList).pickDataThrowException();
    }


    private void uploadWifiInfo(Device device) {
        // 通通锁设备上报wifi信息，从redis缓存中获取
        String key = RedisConstants.getKey(RedisConstants.TT_LOCK_NETWORK_INFO, device.getDeviceSn());
        NetworkInfoDTO networkInfoDTO = (NetworkInfoDTO) redissonCacheComponent.getObjectCache(key);
        if (networkInfoDTO != null) {
            // 上报通通锁开放平台
            UploadWifiReq req = new UploadWifiReq();
            BeanUtils.copyProperties(networkInfoDTO, req);
            req.setLockId(Integer.valueOf(device.getDeviceSn()));
            ttLockOpenService.uploadWifiInfo(req);

            // 网络信息入库
            InsertDeviceNetworkInfoDTO deviceNetworkInfoDTO = new InsertDeviceNetworkInfoDTO();
            deviceNetworkInfoDTO.setOrgId(device.getOrgId());
            deviceNetworkInfoDTO.setNetworkType(2);
            WifiInfo wifiInfo = new WifiInfo();
            wifiInfo.setIp(networkInfoDTO.getIp());
            wifiInfo.setName(networkInfoDTO.getNetworkName());
            wifiInfo.setPassword(networkInfoDTO.getWifiPassword());
            deviceNetworkInfoDTO.setWifiInfo(wifiInfo);
            deviceNetworkInfoDTO.setWifiMac(networkInfoDTO.getWifiMac());
            deviceNetworkInfoDTO.setDeviceId(device.getDeviceId());
            deviceNetworkInfoDTO.setConnectType(1);
            deviceNetworkInfoService.upload(deviceNetworkInfoDTO).pickDataThrowException();
        }
    }


    @Override
    public void rollBack(InvocationChain<ActivateDeviceDTO, ActivateDeviceContext> invocationChain) {
        ActivateDeviceDTO parameter = invocationChain.getParameter();
        // 删除设备
        iotDeviceInstanceService.batchUnDeployAndDeleteDevice(-1L, Lists.newArrayList(parameter.getDeviceSn()));
    }
}
