package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 主题和设备关联关系表
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_subject_relation")
public class DeviceSubjectRelation extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主题和设备关联id
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备主题id
     */
    private Long subjectId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 主题类型 1-壁纸 2-屏保
     */
    private Integer type;
}
