package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceOperEventType;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.log.DeviceLogDTO;
import com.moredian.magicube.device.helper.DeviceLogHelper;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.magicube.device.service.DeviceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 新增设备日志管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InsertDeviceLogPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private DeviceLogService deviceLogService;

    @Autowired
    private DeviceLogHelper deviceLogHelper;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        DeviceLogDTO deviceLogDTO = new DeviceLogDTO();
        BeanUtils.copyProperties(dto, deviceLogDTO);
        deviceLogDTO.setOrgId(context.getOrgId());
        deviceLogDTO.setDeviceId(context.getDevice().getDeviceId());
        deviceLogDTO.setEnvenType(DeviceOperEventType.ACTIVE_DEVICE.getValue());
        deviceLogDTO.setEnvenDesc(DeviceOperEventType.ACTIVE_DEVICE.getDesc());
        deviceLogDTO.setOperTime(System.currentTimeMillis());
        deviceLogHelper.asyncInsertActiveDeviceLog(deviceLogDTO);
    }
}