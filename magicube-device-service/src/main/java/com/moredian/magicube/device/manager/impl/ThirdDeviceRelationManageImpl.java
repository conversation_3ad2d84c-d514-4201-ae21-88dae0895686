package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dao.entity.ThirdDeviceRelation;
import com.moredian.magicube.device.dao.mapper.ThirdDeviceRelationMapper;
import com.moredian.magicube.device.manager.ThirdDeviceRelationManage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date : 2024/3/14
 */
@Service
@Slf4j
public class ThirdDeviceRelationManageImpl implements ThirdDeviceRelationManage {

    @Autowired
    private ThirdDeviceRelationMapper thirdDeviceRelationMapper;
    @Override
    public ThirdDeviceRelation getByTpId(String tpId, Integer tpType) {
        BizAssert.notNull(tpId, "tpId must not be null");
        BizAssert.notNull(tpType, "tpId must not be null");
        return thirdDeviceRelationMapper.getByTpIdAndTpType(tpId, tpType);
    }
}
