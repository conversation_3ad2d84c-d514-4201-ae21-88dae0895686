package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.DeviceNetworkInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备网络信息Mapper
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
public interface DeviceNetworkInfoManager {

    /**
     * 新增设备网络信息
     *
     * @param networkInfo 设备网络信息
     * @return
     */
    Long insert(DeviceNetworkInfo networkInfo);

    /**
     * 更新设备网络信息
     *
     * @param networkInfo 更新设备信息
     * @return
     */
    Boolean update(DeviceNetworkInfo networkInfo);

    /**
     * 根据机构号和设备Id获取网络信息
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    DeviceNetworkInfo getByOrgIdAndDeviceId(Long orgId, Long deviceId);
}
