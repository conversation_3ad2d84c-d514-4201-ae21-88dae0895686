package com.moredian.magicube.device.subscriber;

import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.GroupSyncMsg;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.service.DeviceGroupRelationService;
import com.moredian.magicube.device.service.DeviceService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 权限组消息订阅
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class GroupMsgSubscriber {

    @SI
    private DeviceGroupRelationService deviceGroupRelationService;

    @SI
    private DeviceService deviceService;

    @Autowired
    private RedissonLockComponent redissonLockComponent;

    /**
     * 处理设备是否超限逻辑
     *
     * @param msg 权限组变更消息
     */
    @Subscribe
    public void groupSyncMsg(GroupSyncMsg msg) {
        log.info("groupSyncMsg设备服务收到权限组变更消息GroupSyncMsg{}:", msg);
        try {
            if (redissonLockComponent.acquire(RedisKeys.getKey(RedisKeys.GROUP_CHANGE_MSG,msg.getOrgId(),msg.getGroupId()),
                null, RedisKeys.RELEASE_TIME)) {
                doDeviceCapacityExceeded(msg);
            } else {
                log.error("groupSyncMsg获取权限组变更消息的分布式锁失败GroupSyncMsg:{}", msg);
            }
        } catch (Exception e) {
            log.error("groupSyncMsg处理设备是否超限失败message:{}", e.getMessage());
        } finally {
            redissonLockComponent.release(RedisKeys.getKey(RedisKeys.GROUP_CHANGE_MSG,msg.getOrgId(),msg.getGroupId()));
        }
    }

    /**
     * 处理设备容量超限
     *
     * @param msg 权限组信息
     */
    private void doDeviceCapacityExceeded(GroupSyncMsg msg) {
        List<Long> deviceIds = deviceGroupRelationService
            .listDeviceIdByOrgIdAndGroupId(msg.getOrgId(), msg.getGroupId())
            .pickDataThrowException();
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            deviceService.doDeviceCapacityExceeded(msg.getOrgId(), deviceIds)
                .pickDataThrowException();
            log.info("groupSyncMsg处理设备容量超限完成deviceIds:{}", deviceIds);
        }
    }
}