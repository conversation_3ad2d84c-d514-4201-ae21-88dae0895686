package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.OrgDeviceMigration;
import com.moredian.magicube.device.enums.MigrationStatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 一键上钉
 * @author: wbf
 * @date: 2024/8/12 下午3:34
 */
@Mapper
public interface OrgDeviceMigrationMapper {
    /**
     * 插入
     *
     * @param orgDeviceMigration
     * @return
     */
    int insert(OrgDeviceMigration orgDeviceMigration);

    /**
     * 更新
     *
     * @param orgDeviceMigration
     * @return
     */
    int updateByPrimaryKey(OrgDeviceMigration orgDeviceMigration);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 查询待迁移设备列表
     *
     * @param orgId
     * @return
     */
    List<OrgDeviceMigration> selectMigrationListByOrgId(Long orgId);

    /**
     * 查询组织列表页
     *
     * @param orgId
     * @param deviceId
     * @return
     */
    OrgDeviceMigration selectMigrationDeviceInfo(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId);

    /**
     * 绑定组织页url
     *
     * @param deviceId
     * @param bindOrgUrl
     */
    void updateOrgUrlByDeviceId(@Param("deviceId") Long deviceId, @Param("bindOrgUrl") String bindOrgUrl);

    /**
     * 根据设备id查询迁移设备信息
     *
     * @param deviceId
     * @return
     */
    OrgDeviceMigration selectMigrationDeviceInfoByDeviceId(Long deviceId);

    /**
     * 修改设备迁移状态
     *
     * @param deviceId
     * @param migrationStatus
     */
    void updateMigrationStatus(@Param("deviceId") Long deviceId, @Param("migrationStatus") Integer migrationStatus);

    /**
     * 根据组织id和设备id删除迁移设备信息
     *
     * @param orgId
     * @param deviceId
     */
    void deleteByOrgIdAndDeviceId(@Param("orgId") long orgId, @Param("deviceId") long deviceId);

    /**
     * 批量插入
     * @param orgDeviceMigrationList
     */
    void batchInsert(List<OrgDeviceMigration> orgDeviceMigrationList);
}
