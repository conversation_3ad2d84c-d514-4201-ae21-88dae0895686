package com.moredian.magicube.device.manager;

import java.util.List;

/**
 * 设备组/通行组/设备管理组件
 *
 * <AUTHOR>
 * @date 2024/12/5 12:36
 */
public interface DeviceGroupCompositeComponent {

    /**
     * 设备组添加
     *
     * @param orgId        机构Id
     * @param compositeId  设备组
     * @param deviceIdList 设备ID列表
     * @return true
     */
    boolean deviceCompositeAdd(Long orgId, Long compositeId, List<Long> deviceIdList);

    /**
     * 设备组删除
     *
     * <pre>
     *      1. 删除通信组和设备组的关系
     *      2. 删除通信组和设备的关系
     * </pre>
     *
     * @param orgId              机构Id
     * @param compositeId        设备组当前设备
     * @param subCompositeIdList 子是设备组
     * @return true
     */
    boolean deleteGroupDeviceCompositeRelationByCompositeId(Long orgId, Long compositeId);

    /**
     * 设备组删除
     *
     * <pre>
     *      1. 删除通信组和设备组的关系
     *      2. 删除通信组和设备的关系
     * </pre>
     *
     * @param orgId              机构Id
     * @param compositeId        设备组当前设备
     * @param deleteDeviceIdList 删除设备列表
     * @param addDeviceIdList    添加设备
     * @return 需要发消息设备
     */
    List<Long> updateComposite(Long orgId, Long compositeId, List<Long> deleteDeviceIdList, List<Long> addDeviceIdList);
}
