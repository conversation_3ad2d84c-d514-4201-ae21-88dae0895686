package com.moredian.magicube.device.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.DeviceStateRequest;
import com.moredian.iothub.control.api.v1.response.DeviceStateResponse;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.common.enums.TpType;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgConfigService;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.OrgDeviceMigration;
import com.moredian.magicube.device.dao.mapper.OrgDeviceMigrationMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.migration.*;
import com.moredian.magicube.device.enums.MigrationStatusEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.MqttMessageComponent;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.MigrationDeviceManager;
import com.moredian.magicube.device.service.DeviceOnlineService;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.subscriber.TransferEventType;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 一键上钉
 * @author: wbf
 * @date: 2024/8/12 下午3:32
 */
@Service
@Slf4j
public class MigrationDeviceManagerImpl implements MigrationDeviceManager {
    @SI
    private OrgService orgService;
    @SI
    private DeviceService deviceService;
    @SI
    private DeviceOnlineService deviceOnlineService;
    @SI
    private HubControlServiceV1 hubControlServiceV1;
    @SI
    private OrgConfigService orgConfigService;
    @Resource
    private OrgDeviceMigrationMapper orgDeviceMigrationMapper;
    @Resource
    private MqttMessageComponent mqttMessageComponent;
    @Resource
    private DeviceManager deviceManager;
    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public MigrationDeviceRes getList(Long orgId) {
        MigrationDeviceRes res = new MigrationDeviceRes();
        // 查询组织信息
        OrgInfo orgInfo = orgService.getOrgInfo(orgId, Collections.singletonList(OrgStatus.USABLE.getValue())).pickDataThrowException();
        BizAssert.isTrue(orgInfo != null, "组织不存在");
        res.setOrgId(orgId);
        res.setOrgName(orgInfo.getOrgName());
        // 查询待迁移设备列表
        List<OrgDeviceMigration> migrationList = orgDeviceMigrationMapper.selectMigrationListByOrgId(orgId);
        if (migrationList.size() == 0) {
            res.setDeviceCount(0);
            res.setDeviceList(new ArrayList<>());
            return res;
        }
        // 查询设备信息组装返回结果
        List<MigrationDeviceList> deviceList = new ArrayList<>();
        // 设备信息详情
        List<Long> deviceIdList = migrationList.stream().map(OrgDeviceMigration::getDeviceId).collect(Collectors.toList());
        List<DeviceInfoDTO> deviceInfoDTOList = deviceService.listByOrgIdAndIds(orgId, deviceIdList).pickDataThrowException();

        // 查询智能设备的在离线
        Map<String, DeviceStateRequest> requestMap = new HashMap<>();
        for (DeviceInfoDTO deviceInfoDTO : deviceInfoDTOList) {
            DeviceStateRequest deviceStateRequest = new DeviceStateRequest();
            requestMap.put(deviceInfoDTO.getDeviceSn(), deviceStateRequest);
        }
        Map<String, DeviceStateResponse> map = hubControlServiceV1.getDeviceListState(requestMap).pickDataThrowException();

        deviceInfoDTOList.forEach(deviceInfo -> {
            MigrationDeviceList device = new MigrationDeviceList();
            device.setDeviceId(deviceInfo.getDeviceId());
            device.setDeviceSn(deviceInfo.getDeviceSn());
            device.setDeviceName(deviceInfo.getDeviceName());
            device.setDeviceType(deviceInfo.getDeviceType());
            device.setPosition(removeLeadingSlash(deviceInfo.showName()));
            DeviceStateResponse deviceStateResponse = map.get(deviceInfo.getDeviceSn());
            if (deviceStateResponse != null) {
                device.setOnline(deviceStateResponse.getOnline());
            } else {
                device.setOnline(false);
            }
            deviceList.add(device);
        });
        res.setDeviceList(deviceList);
        res.setDeviceCount(deviceList.size());
        return res;
    }

    @Override
    public MigrationDeviceBindOrgUrlRes getBindOrgUrl(Long orgId, Long deviceId) {
        MigrationDeviceBindOrgUrlRes res = new MigrationDeviceBindOrgUrlRes();
        res.setDeviceId(deviceId);
        OrgDeviceMigration orgDeviceMigration = orgDeviceMigrationMapper.selectMigrationDeviceInfo(orgId, deviceId);
        if (orgDeviceMigration != null && StringUtil.isNotBlank(orgDeviceMigration.getDingTalkUrl())) {
            res.setBindOrgUrl(orgDeviceMigration.getDingTalkUrl());
        }
        // 清空已获取完成的设备id
        orgDeviceMigrationMapper.updateOrgUrlByDeviceId(deviceId, null);
        return res;
    }

    @Override
    public void bindOrgUrlSync(String deviceSn, String bindOrgUrl) {
        Device device = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.isTrue(device != null && device.getDeviceId() != null, "设备不存在");
        orgDeviceMigrationMapper.updateOrgUrlByDeviceId(device.getDeviceId(), bindOrgUrl);
        String redisKey = RedisKeys.getKey(RedisKeys.MIGRATION_DEVICE_RUNNABLE, device.getDeviceId());
        stringRedisTemplate.delete(redisKey);
    }

    @Override
    public MigrationDeviceOrgRes getOrgIsEqual(String deviceSn, String corpId) {
        log.info("manager----getOrgIsEqual----deviceSn[{}],corpId[{}]", deviceSn, corpId);
        OrgInfo orgInfo = orgService.getOrgByTp(TpType.DING, corpId).pickDataThrowException();
        log.info("manager----getOrgIsEqual----orgInfo[{}]", orgInfo);
        BizAssert.isTrue(orgInfo != null, "组织不存在");
        Device device = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.isTrue(device != null && device.getDeviceId() != null, "设备不存在");
        OrgDeviceMigration orgDeviceMigration = orgDeviceMigrationMapper.selectMigrationDeviceInfoByDeviceId(device.getDeviceId());
        log.info("manager----getOrgIsEqual----orgDeviceMigration[{}]", orgDeviceMigration);
        BizAssert.isTrue(orgDeviceMigration != null, "待迁移设备未找到");
        boolean isEqual = orgDeviceMigration.getOrgId().equals(orgInfo.getOrgId());
        if (!isEqual) {
            orgDeviceMigrationMapper.updateMigrationStatus(device.getDeviceId(), MigrationStatusEnum.MIGRATION_FAIL_ORG_NOT_EQUAL.getCode());
        }
        MigrationDeviceOrgRes res = new MigrationDeviceOrgRes();
        res.setIsEqual(isEqual);
        res.setCorpId(corpId);
        res.setOrgId(orgDeviceMigration.getOrgId());
        res.setCorpOrgId(orgInfo.getOrgId());
        return res;
    }

    @Override
    public OrgDeviceMigration getMigrationDeviceInfo(Long deviceId) {
        return orgDeviceMigrationMapper.selectMigrationDeviceInfoByDeviceId(deviceId);
    }

    @Override
    public boolean reqBindOrgUrl(Long orgId, Long deviceId) {
        // 获取待迁移设备
        OrgDeviceMigration orgDeviceMigration = orgDeviceMigrationMapper.selectMigrationDeviceInfo(orgId, deviceId);
        BizAssert.isTrue(orgDeviceMigration != null, "待迁移设备不存在");
        // 获取设备信息
        Device device = deviceManager.getByOrgIdAndId(orgId, deviceId);
        BizAssert.isTrue(device != null, "设备不存在");
        //判断设备是否在线
        DeviceStateRequest deviceStateRequest = new DeviceStateRequest();
        deviceStateRequest.setSerialNumber(device.getDeviceSn());
        DeviceStateResponse deviceStateResponse = hubControlServiceV1.getDeviceState(deviceStateRequest).pickDataThrowException();
        if (deviceStateResponse == null || deviceStateResponse.getOnline() == false) {
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_ONT_ONLINE, DeviceErrorCode.DEVICE_ONT_ONLINE.getMessage()));
        }
        String redisKey = RedisKeys.getKey(RedisKeys.MIGRATION_DEVICE_RUNNABLE, deviceId);
        if(!stringRedisTemplate.opsForValue().setIfAbsent(redisKey,redisKey,RedisKeys.RELEASE_TIME, TimeUnit.SECONDS)){
            throw new BizException(new ErrorContext(DeviceErrorCode.MIGRATION_DEVICE_RUNNABLE,DeviceErrorCode.MIGRATION_DEVICE_RUNNABLE.getMessage()));
        }
        if(MigrationStatusEnum.MIGRATION_FAIL_DEVICE_ERROR.getCode().equals(orgDeviceMigration.getMigrationStatus())
                ||MigrationStatusEnum.MIGRATION_FAIL_ORG_NOT_EQUAL.getCode().equals(orgDeviceMigration.getMigrationStatus())){
            orgDeviceMigrationMapper.updateMigrationStatus(deviceId,MigrationStatusEnum.MIGRATION_WAITING.getCode());
        }
        JSONObject json = new JSONObject();
        json.put("orgId", orgId);
        json.put("deviceId", deviceId);
        // 发送mqtt
        log.info("manager----reqBindOrgUrl----sendMQTTMsg----deviceId[{}],deviceSn[{}]", deviceId, device.getDeviceSn());
        boolean res = mqttMessageComponent.sendMqttMessage(device.getDeviceSn(), TransferEventType.MIGRATION_DEVICE_ORG_URL.getEventType(), json.toJSONString(), "MIGRATION_DEVICE_ORG_URL");
        log.info("manager----reqBindOrgUrl----sendMQTTMsg----result-----deviceId[{}],deviceSn[{}],res[{}]", deviceId, device.getDeviceSn(), res);
        return res;
    }

    @Override
    public MigrationDeviceStatusRes getMigrationDeviceStatus(Long orgId, Long deviceId) {
        OrgDeviceMigration orgDeviceMigration = orgDeviceMigrationMapper.selectMigrationDeviceInfo(orgId, deviceId);
        BizAssert.isTrue(orgDeviceMigration != null, "待迁移设备不存在");
        MigrationDeviceStatusRes res = new MigrationDeviceStatusRes();
        res.setMigrationStatus(orgDeviceMigration.getMigrationStatus());
        res.setDeviceId(orgDeviceMigration.getDeviceId());
        if (MigrationStatusEnum.MIGRATION_SUCCESS.getCode().equals(orgDeviceMigration.getMigrationStatus())) {
            List<OrgDeviceMigration> migrationList = orgDeviceMigrationMapper.selectMigrationListByOrgId(orgId);
            if (migrationList.size() == 0) {
                res.setIsAllEnd(true);
            } else {
                res.setIsAllEnd(false);
            }
        } else {
            res.setIsAllEnd(false);
        }
        return res;
    }

    @Override
    public MigrationDeviceOrgCountRes getMigrationDeviceCount(Long orgId) {
        MigrationDeviceOrgCountRes res = new MigrationDeviceOrgCountRes();
        List<OrgDeviceMigration> migrationList = orgDeviceMigrationMapper.selectMigrationListByOrgId(orgId);
        res.setDeviceCount(migrationList.size());
        res.setOrgId(orgId);
        return res;
    }

    @Override
    public void migrationDeviceStatusSync(Long orgId, String deviceSn, Boolean isSuccess) {
        int migrationStatus = isSuccess ? MigrationStatusEnum.MIGRATION_SUCCESS.getCode() : MigrationStatusEnum.MIGRATION_FAIL_DEVICE_ERROR.getCode();
        Device device = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.isTrue(device != null && device.getDeviceId() != null, "设备不存在");
        orgDeviceMigrationMapper.updateMigrationStatus(device.getDeviceId(), migrationStatus);
    }

    @Override
    public void subDeviceActiveMsg(long orgId, long deviceId, String deviceSn) {
        boolean checkOrg = orgConfigService.checkOrgHasMdTransferDingFlag(orgId).pickDataThrowException();
        log.info("manager----设备激活----orgId[{}],deviceId[{}],deviceSn[{}],checkOrg[{}]", orgId, deviceId, deviceSn, checkOrg);
        if (!checkOrg) {
            log.info("manager----设备激活-----组织未开启迁移----orgId[{}],deviceId[{}],deviceSn[{}]", orgId, deviceId, deviceSn);
            return;
        }
        Device device = deviceManager.getByOrgIdAndId(orgId, deviceId);
        if(device == null){
            log.info("manager----设备激活-----设备不存在----orgId[{}],deviceId[{}],deviceSn[{}]", orgId, deviceId, deviceSn);
            return;
        }
        // 判断设备类型是否在集合中
        List<Integer> deviceTypeList = deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.MIGRATION_DEVICE_TYPE);
        if (!deviceTypeList.contains(device.getDeviceType())) {
            log.info("manager----设备激活----设备类型错误，orgId[{}],deviceId[{}],deviceSn[{}],deviceType[{}]", orgId, deviceId, deviceSn, device.getDeviceType());
            return;
        }
        // 根据ThirdDeviceId 判断是否是微信绑定设备
        if (StringUtil.isBlank(device.getThirdDeviceId())) {
            OrgDeviceMigration orgDeviceMigration = new OrgDeviceMigration();
            orgDeviceMigration.setOrgId(orgId);
            orgDeviceMigration.setDeviceId(deviceId);
            orgDeviceMigration.setMigrationStatus(MigrationStatusEnum.MIGRATION_WAITING.getCode());
            orgDeviceMigrationMapper.insert(orgDeviceMigration);
        }
    }

    @Override
    public void subDeviceUnbindMsg(long orgId, long deviceId) {
        log.info("manager----设备解绑----orgId[{}],deviceId[{}]", orgId, deviceId);
        orgDeviceMigrationMapper.deleteByOrgIdAndDeviceId(orgId, deviceId);
    }

    @Override
    public void mdOrgTransferDingOrgMsg(Long orgId) {
        log.info("manager----组织开通迁移----orgId[{}]", orgId);
        List<Device> deviceList = deviceManager.listByOrgId(orgId);
        log.info("manager----组织开通迁移----orgId[{}]，deviceSize[{}]", orgId, deviceList.size());
        if (deviceList.size() == 0) {
            return;
        }
        List<Integer> deviceTypeList = deviceTypePropertyManager.getDeviceTypeByPropertyKey(DeviceTypeConstants.MIGRATION_DEVICE_TYPE);
        log.info("manager----组织开通迁移----deviceTypeList[{}]", JSONObject.toJSONString(deviceTypeList));
        List<OrgDeviceMigration> orgDeviceMigrationList = new ArrayList<>();
        deviceList.forEach(device -> {
            if(StringUtil.isNotBlank(device.getThirdDeviceId())){
                log.info("manager----组织开通迁移----设备第三方id不是null，orgId[{}],deviceId[{}],deviceSn[{}],deviceType[{}]", orgId, device.getDeviceId(), device.getDeviceSn(), device.getDeviceType());
                return;
            }
            // 判断设备类型是否在集合中
            if (!deviceTypeList.contains(device.getDeviceType())) {
                log.info("manager----组织开通迁移----设备类型错误，orgId[{}],deviceId[{}],deviceSn[{}],deviceType[{}]", orgId, device.getDeviceId(), device.getDeviceSn(), device.getDeviceType());
                return;
            }
            OrgDeviceMigration orgDeviceMigration = new OrgDeviceMigration();
            orgDeviceMigration.setOrgId(orgId);
            orgDeviceMigration.setDeviceId(device.getDeviceId());
            orgDeviceMigration.setMigrationStatus(MigrationStatusEnum.MIGRATION_WAITING.getCode());
            orgDeviceMigrationList.add(orgDeviceMigration);
        });
        log.info("manager----组织开通迁移----orgId[{}]，orgDeviceMigrationList[{}]", orgId, orgDeviceMigrationList.size());
        orgDeviceMigrationMapper.batchInsert(orgDeviceMigrationList);
    }

    /**
     * 去掉首字符"/"
     * @param str
     * @return
     */
    public static String removeLeadingSlash(String str) {
        if (str != null && str.length() > 0 && str.startsWith("/")) {
            return str.substring(1);
        }
        return str;
    }

}
