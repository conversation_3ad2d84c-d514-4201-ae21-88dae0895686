package com.moredian.magicube.device.manager.impl;

import cn.hutool.core.util.ObjectUtil;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.config.HuieyeApiSignatureUtil;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceFaceModel;
import com.moredian.magicube.device.dao.mapper.DeviceFaceModelMapper;
import com.moredian.magicube.device.dao.mapper.DeviceMapper;
import com.moredian.magicube.device.dto.register.SaveDeviceFaceModelRequest;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceFaceModelManager;
import com.moredian.unified.recognition.api.constants.FaceModel;
import com.moredian.unified.recognition.api.constants.FeatureType;
import com.moredian.unified.recognition.api.request.BindFaceRepositoryFaceModelRelationRequest;
import com.moredian.unified.recognition.api.service.UnifiedRecognitionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-27
 */
@Service
public class DeviceFaceModelManagerImpl implements DeviceFaceModelManager {

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private DeviceFaceModelMapper deviceFaceModelMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @SI
    private UnifiedRecognitionService unifiedRecognitionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDeviceFaceModel(SaveDeviceFaceModelRequest request) {
        BizAssert.notNull(request.getOrgId(), "orgId must not be null");
        BizAssert.notNull(request.getDeviceId(), "deviceId must not be null");
        BizAssert.notNull(request.getFaceModel(), "faceModel must not be null");

        // 校验设备是否存在
        Device device = deviceMapper.getByOrgIdAndId(request.getOrgId(), request.getDeviceId());
        if (device == null) {
            ExceptionUtils.throwException(DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
        }

        Integer modelType = request.getModelType();
        FeatureType type = ObjectUtil.isEmpty(modelType) ? FeatureType.MOBILE : FeatureType.fromCode(modelType);

        // 新增（orgId-deviceId -> 已存在绑定，则会更新模型为最新）
        DeviceFaceModel deviceFaceModel = new DeviceFaceModel();
        Long deviceFaceModelId = idgeneratorService.getNextIdByTypeName(DeviceFaceModel.class.getName()).pickDataThrowException();
        deviceFaceModel.setDeviceFaceModelId(deviceFaceModelId);
        deviceFaceModel.setOrgId(request.getOrgId());
        deviceFaceModel.setDeviceId(request.getDeviceId());
        deviceFaceModel.setFaceModel(request.getFaceModel());
        deviceFaceModel.setModelType(type.getCode());
        deviceFaceModelMapper.insertOrUpdate(deviceFaceModel);

        // 同步至慧眼
        BindFaceRepositoryFaceModelRelationRequest bindRelationRequest = new BindFaceRepositoryFaceModelRelationRequest(HuieyeApiSignatureUtil.getSignature());
        bindRelationRequest.setFaceRepositoryId(String.valueOf(request.getOrgId()));
        bindRelationRequest.setFaceModel(FaceModel.valueOf(request.getFaceModel()));
        bindRelationRequest.setFeatureType(type);
        unifiedRecognitionService.bindFaceRepositoryFaceModelRelation(bindRelationRequest).pickDataThrowException();

        return Boolean.TRUE;
    }

    @Override
    public List<Device> findDeviceByOrg(Long orgId) {
        List<Long> deviceIdList = deviceFaceModelMapper.selectByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(deviceIdList)) {
            return deviceMapper.listByOrgIdAndIds(orgId, deviceIdList);
        }
        return Collections.emptyList();
    }

    @Override
    public Boolean deleteByDevice(Long orgId, Long deviceId) {
        deviceFaceModelMapper.deleteByDevice(orgId, deviceId);
        return Boolean.TRUE;
    }

}
