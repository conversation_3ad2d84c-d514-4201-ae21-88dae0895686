package com.moredian.magicube.device.dao.mapper;


import com.moredian.magicube.device.dao.entity.NoticeRecord;
import java.util.Date;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@Mapper
public interface NoticeRecordDao {

    int insertNoticeRecord(NoticeRecord noticeRecord);

    int countRecords(
        @Param("orgId") Long orgId, @Param("deviceSn") String deviceSn,
        @Param("noticeEvent") Integer noticeEvent, @Param("noticeResult") Integer noticeResult,
        @Param("startDay") Date startDay, @Param("endDay") Date endDay);

    Date getLatestRecordTimeByDeviceSn(@Param("orgId") Long orgId,
        @Param("deviceSn") String deviceSn, @Param("noticeEvent") Integer noticeEvent);

    Long getLastOfflineTime(@Param("orgId") Long orgId,
        @Param("deviceSn") String deviceSn, @Param("noticeEvent") Integer noticeEvent);
}
