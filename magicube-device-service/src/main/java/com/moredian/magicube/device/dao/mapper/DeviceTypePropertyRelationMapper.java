package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceTypePropertyRelation;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 设备类型和属性关系
 *
 * <AUTHOR>
 */

@Mapper
public interface DeviceTypePropertyRelationMapper {

    /**
     * 新增设备类型和属性关系
     *
     * @param deviceTypePropertyRelation 关系信息
     * @return
     */
    int insert(DeviceTypePropertyRelation deviceTypePropertyRelation);

    /**
     * 批量新增设备类型和属性关系
     *
     * @param relations 关系信息
     * @return
     */
    void batchInsert(@Param("relations") List<DeviceTypePropertyRelation> relations);

    /**
     * 根据设备类型和属性Id查询关系
     *
     * @param propertyId 属性Id
     * @param deviceType 设备类型
     * @return
     */
    DeviceTypePropertyRelation getByPropertyIdAndDeviceType(@Param("propertyId") Long propertyId,
        @Param("deviceType") Integer deviceType);

    /**
     * 根据设备类型查询属性Id列表
     *
     * @param deviceType 设备类型
     * @return
     */
    List<Long> listPropertyIdByDeviceType(@Param("deviceType") Integer deviceType);

    /**
     * 根据设备类型属性Id查询设备类型列表
     *
     * @param propertyId 设备类型属性Id
     * @return
     */
    List<Integer> listDeviceTypeByPropertyId(@Param("propertyId") Long propertyId);

    /**
     * 根据设备类型和属性Id删除关系
     *
     * @param deviceTypes 设备类型列表
     * @param propertyIds 属性Id列表
     * @return
     */
    void deleteByDeviceTypeAndPropertyId(@Param("deviceTypes") List<Integer> deviceTypes,
        @Param("propertyIds") List<Long> propertyIds);
}
