package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.register.SaveDeviceFaceModelRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-27
 */
public interface DeviceFaceModelManager {

    /**
     * 保存设备人脸模型版本
     *
     * @param request
     * @return
     */
    Boolean saveDeviceFaceModel(SaveDeviceFaceModelRequest request);

    /**
     * 查询注册过的设备信息
     *
     * @param orgId
     * @return
     */
    List<Device> findDeviceByOrg(Long orgId);

    /**
     * 删除
     *
     * @param orgId 机构ID
     * @param deviceId 设备ID
     * @return
     */
    Boolean deleteByDevice(Long orgId, Long deviceId);

}
