<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceAppRelationFixedConfigMapper">
    <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig">
        <result column="id" property="id"/>
        <result column="biz_type" property="bizType"/>
        <result column="biz_id" property="bizId"/>
        <result column="default_app_code" property="defaultAppCode"/>
        <result column="available_app_code_list" property="availableAppCodeList"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>
    <!-- 表字段 -->
    <sql id="baseColumns">t.id, t.biz_type, biz_id, t.default_app_code, t.available_app_code_list , t.gmt_create , t.gmt_modify</sql>
    <!-- 查询全部 -->
    <select id="listAll" resultMap="BaseResultMap">SELECT
        <include refid="baseColumns"/> FROM hive_device_app_relation_fixed_config t
    </select>
    <!-- 根据主键获取单条记录 -->
    <select id="getById" resultMap="BaseResultMap" parameterType="Long">SELECT
        <include refid="baseColumns"/> FROM hive_device_app_relation_fixed_config t WHERE id = #{id}
    </select>
    <!-- 插入全部字段 -->
    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig" keyProperty="id" keyColumn="id" useGeneratedKeys="true">INSERT INTO hive_device_app_relation_fixed_config
        <trim prefix="(" suffix=")" suffixOverrides=",">id, biz_type, biz_id, default_app_code, available_app_code_list, gmt_create, gmt_modify,</trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">#{id}, #{bizType},#{bizId}, #{defaultAppCode}, #{availableAppCodeList},now(3), now(3),</trim>
    </insert>
    <!-- 更新全部字段 -->
    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig">UPDATE hive_device_app_relation_fixed_config
        <set>biz_type=#{bizType}, biz_id=#{bizId}, default_app_code=#{defaultAppCode}, available_app_code_list=#{availableAppCodeList}, gmt_modify=now(3)</set>  WHERE id = #{id}
    </update>
    <!-- 更新不为NULL的字段 -->
    <update id="updateSelective" parameterType="com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig">UPDATE hive_device_app_relation_fixed_config
        <set>
            <if test="bizType != null">biz_type=#{bizType},</if>
            <if test="bizId != null">biz_id=#{bizId},</if>
            <if test="defaultAppCode != null">default_app_code=#{defaultAppCode},</if>
            <if test="availableAppCodeList != null">available_app_code_list=#{availableAppCodeList},</if>
            gmt_modify = now(3)
        </set> WHERE id = #{id}
    </update>
    <!-- 根据主键删除记录 -->
    <delete id="delete" parameterType="Long">delete from hive_device_app_relation_fixed_config WHERE id = #{id}</delete>

    <select id="getByBizTypeAndId" resultMap="BaseResultMap">SELECT
        <include refid="baseColumns"/> FROM hive_device_app_relation_fixed_config t WHERE biz_type = #{bizType} and biz_id = #{bizId}
    </select>

    <select id="selectByConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="baseColumns"/>
        FROM hive_device_app_relation_fixed_config t
        <where>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="bizId != null and bizId != ''">
                AND biz_id = #{bizId}
            </if>
        </where>
    </select>

    <delete id="deleteByBizTypeAndId" parameterType="com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig">
        delete from
        hive_device_app_relation_fixed_config
        WHERE biz_type = #{bizType} and biz_id = #{bizId}
    </delete>
</mapper>
