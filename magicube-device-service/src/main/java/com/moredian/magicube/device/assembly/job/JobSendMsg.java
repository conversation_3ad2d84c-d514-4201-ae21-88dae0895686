package com.moredian.magicube.device.assembly.job;


import com.google.common.collect.Lists;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.rmq.utils.JsonUtils;
import com.moredian.magicube.device.assembly.request.ObjectAndClassBeanDto;
import com.moredian.magicube.device.assembly.service.impl.AssemblyServiceImpl;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/2 17:35
 */
@Slf4j
@Component
public class JobSendMsg {

    /**
     * 线程池定时任务的时间
     */
    public final static Long SEND_MSG_GAP_SECOND = 60L;

    /**
     * 降噪消息
     */
    public final static String NOISE_REDUCTION_MESSAGE = "NOISE_REDUCTION_MESSAGE";

    @Resource
    private RedisTemplate redisTemplate;


    public static String getZsetKey(String redisKey) {
        return redisKey + ":zset";
    }


    public static String getStrKey(String redisKey) {
        return redisKey + ":str";
    }


    public static Integer publish(Object msg) {
        int publish = EventBus.publish(msg);
        log.info("MQ-LOG:publish msg class={},content = {},publish-value = {}", msg.getClass(),
            com.moredian.bee.common.utils.JsonUtils.toJson(msg),
            publish);
        return publish;
    }


    public JobSendMsg() {
        // 启动一个定时任务来处理队列中的消息
//        Executors.newSingleThreadScheduledExecutor()
//            .scheduleAtFixedRate(this::processMessageQueue, 0, SEND_MSG_GAP_SECOND,
//                TimeUnit.SECONDS);
    }


    private void processMessageQueue() {
        try {
            if (redisTemplate == null) {
                return;
            }

            // 获取当前时间时间戳
            double minScore = 0;
            double maxScore = (double) Instant.now().getEpochSecond();

            // 从 Redis 的 ZSet 中获取时间戳范围内的消息ID
            Set<String> redisKeySet = (Set<String>) redisTemplate.opsForZSet()
                .rangeByScore(NOISE_REDUCTION_MESSAGE, minScore, maxScore);
            log.info(" -- 降噪消息-读出 minScore：{},maxScore：{}, redisKeySet:{}", minScore, maxScore, redisKeySet);
            if (CollectionUtils.isEmpty(redisKeySet)) {
                return;
            }

            for (String redisKey : redisKeySet) {
                if (StringUtil.isBlank(redisKey)) {
                    continue;
                }
                String zsetKey = getZsetKey(redisKey);
                // 降噪key
                Set<String> redisKeySetInner = (Set<String>) redisTemplate.opsForZSet()
                    .rangeByScore(zsetKey, minScore, maxScore);
                if (CollectionUtils.isEmpty(redisKeySetInner)) {
                    continue;
                }
                // 降噪key
                Map<Class<?>, List<Object>> needMergeMsgMap = new HashMap<>();
                Map<Class<?>, String/*待合并字段*/> needMergeFiledMap = new HashMap<>();
                for (String key : redisKeySetInner) {
                    String strJson = (String) redisTemplate.opsForValue()
                        .get(JobSendMsg.getStrKey(redisKey) + ":" + key);
                    if (StringUtil.isBlank(strJson)) {
                        continue;
                    }
                    ObjectAndClassBeanDto dtoMsg = JsonUtils.deSerialize(strJson,
                        ObjectAndClassBeanDto.class);

                    String msg = dtoMsg.getMsgStr();
                    Class<?> aClass = AssemblyServiceImpl.classMaps.get(dtoMsg.getClazzName());
                    if (aClass == null) {
                        // 消息类型丢失，等待下次发消息即可
                        log.error("消息类型丢失，等待下次发消息即可,type={}", dtoMsg.getClazzName());
                        continue;
                    }
                    Class<?> clazz = aClass;
                    Object o = JsonUtils.deSerialize(msg, clazz);
                    if (BooleanUtils.isTrue(dtoMsg.getMerge())) {
                        // 需要合并
                        if (needMergeMsgMap.containsKey(clazz)) {
                            needMergeMsgMap.get(clazz).add(o);
                        } else {
                            needMergeMsgMap.put(clazz, Lists.newArrayList(o));
                        }
                        needMergeFiledMap.put(clazz, dtoMsg.getResetField());
                    } else {
                        JobSendMsg.publish(o);
                    }
                }
                if (MapUtils.isNotEmpty(needMergeMsgMap)) {
                    for (Entry<Class<?>, List<Object>> entry : needMergeMsgMap.entrySet()) {
                        Class<?> key = entry.getKey();
                        List<Object> value = entry.getValue();
                        if (CollectionUtils.isEmpty(value)) {
                            continue;
                        }
                        // 根据机构id分组
                        Map<Long, List<Object>> groupedByOrgIdMap = value.stream()
                            .collect(Collectors.groupingBy(obj -> {
                                try {
                                    Field field = obj.getClass().getDeclaredField("orgId");
                                    // Make the private field accessible
                                    field.setAccessible(true);
                                    return (Long) field.get(obj);
                                } catch (NoSuchFieldException | IllegalAccessException e) {
                                    throw new RuntimeException(e);
                                }
                            }));
                        for (Entry<Long, List<Object>> innerEntry : groupedByOrgIdMap.entrySet()) {
                            Long entryKey = innerEntry.getKey();
                            List<Object> entryValue = innerEntry.getValue();
                            if (CollectionUtils.isNotEmpty(entryValue)) {
                                Object o = entryValue.get(0);
                                List<?> collect = entryValue.stream().map(obj -> {
                                    try {
                                        Field field = obj.getClass()
                                            .getDeclaredField(needMergeFiledMap.get(key));
                                        // Make the private field accessible
                                        field.setAccessible(true);
                                        return (List<?>) field.get(obj);
                                    } catch (NoSuchFieldException | IllegalAccessException e) {
                                        throw new RuntimeException(e);
                                    }
                                }).flatMap(Collection::stream).collect(Collectors.toList());
                                try {
                                    Field field = o.getClass()
                                        .getDeclaredField(needMergeFiledMap.get(key));
                                    // Make the private field accessible
                                    field.setAccessible(true);
                                    field.set(o, collect);
                                } catch (NoSuchFieldException | IllegalAccessException e) {
                                    throw new RuntimeException(e);
                                }
                                log.info("类型[{}],本批消息发送 orgId={},发送内容={}",
                                    key.getCanonicalName(), entryKey, JsonUtils.serialize(o));
                                JobSendMsg.publish(o);
                            }
                        }
                    }
                }

                // 处理完一种清除这种的key
                redisTemplate.opsForZSet().removeRangeByScore(zsetKey, minScore, maxScore);
            }
            // 处理完一种清除这种的key
            redisTemplate.opsForZSet()
                .removeRangeByScore(NOISE_REDUCTION_MESSAGE, minScore, maxScore);
            log.info(" -- 降噪消息-移除已过期的key minScore：{}, maxScore：{}", minScore, maxScore);
        } catch (Exception e) {
            log.error(" -- 降噪消息-处理异常 {}", e.getMessage(), e);
        }
    }
}
