package com.moredian.magicube.device.manager;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import java.util.List;
import java.util.Map;

/**
 * 主题和设备关联关系
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface DeviceSubjectRelationManager {

    /**
     * 重置一个主题和多台设备的关系
     *
     * @param orgId     机构号
     * @param subjectId 设备主题Id
     * @param deviceIds 设备Id列表
     * @return
     */
    Boolean resetRelationBySubjectId(Long orgId, Long subjectId, List<Long> deviceIds,List<Long> spaceIds);


    /**
     * 重置一个设备和多个壁纸或者屏保的关系
     *
     * @param orgId     机构号
     * @param deviceId 设备Id
     * @param subjectIds 设备主题Id列表
     * @param type 1 壁纸，2屏保
     * @return
     */
    Boolean resetRelationByDeviceId(Long orgId, Long deviceId, List<Long> subjectIds,Integer type);


    /**
     * 重置多个主题和多台设备的关系
     *
     * @param orgId     机构号
     * @param subjectIdDeviceIdList 主题ID、设备ID映射
     * @return
     */
    Boolean resetRelationBySubjectIdList(Long orgId, Map<Long,Long> subjectIdDeviceIdList,List<Long> deviceIds,Integer type);

    /**
     * 根据机构Id和设备主题Id列表查询关系信息
     *
     * @param orgId      机构Id
     * @param subjectIds 设备主题Id列表
     * @return
     */
    List<DeviceSubjectRelation> listByOrgIdAndSubjectIds(Long orgId, List<Long> subjectIds);

    /**
     * 根据机构Id和设备Id列表查询关系信息
     *
     * @param orgId     机构Id
     * @param deviceIds 设备Id列表
     * @param type      主题类型 1-壁纸 2-屏保
     * @return
     */
    List<DeviceSubjectRelation> listByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds,
        Integer type);

    /**
     * 删除关系
     *
     * @param orgId      机构Id
     * @param subjectIds 设备主题Id列表
     * @return
     */
    Boolean deleteByOrgIdAndSubjectIds(Long orgId, List<Long> subjectIds);
}
