package com.moredian.magicube.device.pipeline.bo;

import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.activate.ActivateDeviceResultDTO;
import com.xier.guard.accessKey.dto.AccessKeyDto;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备激活信息上下文
 *
 * <AUTHOR>
 */

@Data
public class ActivateDeviceContext implements Serializable {

    private static final long serialVersionUID = -1067086086477044887L;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 空间所在机构Id（多租户情况下会产生，设备是自有的，空间是园区的）
     */
    private Long spaceOrgId;

    /**
     * 空间树Id
     */
    private Long treeId;

    /**
     * 识别通道
     *
     * @see com.moredian.magicube.common.enums.VerifyChannel
     */
    private Integer verifyChannel;

    /**
     * 设备信息
     */
    private Device device;

    /**
     * 设备激活返回信息
     */
    private ActivateDeviceResultDTO resultDTO;

    /**
     * 设备签名秘钥信息
     */
    private AccessKeyDto accessKeyDto;

    /**
     * 设备白名单信息
     */
    private InventoryDevice inventoryDevice;

    /**
     * 空间与设备关系Id
     */
    private Long treeDeviceRelationId;

    /**
     * 发消息时需要带着新创建的消息空间树Id
     */
    private Long newTreeId;

    /**
     * 设备环境 1元石分销 2元石行业
     */
    private Integer environment;

    /**
     * 出入方向 1入口 2出口
     */
    private Integer direction;
}
