package com.moredian.magicube.device.manager.impl;


import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.GroupAppType;
import com.moredian.magicube.common.enums.PersonType;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.common.enums.VerifyChannel;
import com.moredian.magicube.core.member.service.GroupRelationService;
import com.moredian.magicube.core.org.model.GroupInfo;
import com.moredian.magicube.core.org.service.GroupService;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceCompositeGroup;
import com.moredian.magicube.device.dao.entity.DeviceGroup;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeGroupMapper;
import com.moredian.magicube.device.dao.mapper.DeviceGroupMapper;
import com.moredian.magicube.device.dao.mapper.DeviceMapper;
import com.moredian.magicube.device.dto.composite.DeviceCompositeGroupRelationAddReq;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceDTO;
import com.moredian.magicube.device.dto.group.DeviceGroupPersonCountDTO;
import com.moredian.magicube.device.dto.group.ResetGroupRelationDTO;
import com.moredian.magicube.device.dto.group.SimpleDeviceGroupDTO;
import com.moredian.magicube.device.enums.DeviceCompositeGroupRangeType;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceCompositeManager;
import com.moredian.magicube.device.manager.DeviceGroupManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypeMapManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.service.DeviceService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceGroupManagerImpl implements DeviceGroupManager {

    @Autowired
    private DeviceGroupMapper deviceGroupMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Autowired
    private GroupService groupService;

    @Autowired
    private GroupRelationService groupRelationService;

    @SI
    private OrgService orgService;

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private DeviceGroupManager deviceGroupManager;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceCompositeGroupMapper deviceCompositeGroupMapper;

    @Autowired
    private DeviceCompositeManager deviceCompositeManager;

    @SI
    private DeviceService deviceService;

    @Override
    public Boolean insertDefaultDeviceGroup(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        Device device = deviceMapper.getById(deviceId);
        if (!deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_GROUP_LIST, device.getDeviceType())) {
            return true;
        }
        if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_GROUP_LIST, device.getDeviceType())) {
            this.bindDefaultGroup(orgId, deviceId);
        } else if(deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.SINGLE_VISITOR_DEVICE_TYPE_LIST, device.getDeviceType())) {
            this.bindVisitorGroup(orgId, deviceId);
            //}else if(DeviceType.BOARD_X2.getValue() == device.getDeviceType()){
        } else if(deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NOT_BIND_DEFAULT_GROUP_DEVICE_LIST, device.getDeviceType())) {
            //X2不绑定组，由前端激活过程中触发绑定默认组
            return true;
        }
        return Boolean.TRUE;
    }

    @Override
    public List<Long> listDeviceIdByOrgIdAndGroupId(Long orgId, Long groupId) {
        BizAssert.notNull(orgId, "orgId is must not be null");
        BizAssert.notNull(groupId, "groupId is must not be null");
        return deviceGroupMapper.listDeviceIdByOrgIdAndGroupId(orgId, groupId);
    }

    @Override
    public List<Long> listDeviceIdByOrgIdAndGroupIds(Long orgId, List<Long> groupIds) {
        BizAssert.notNull(orgId, "orgId is must not be null");
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        return deviceGroupMapper.listDeviceIdByOrgIdAndGroupIds(orgId, groupIds);
    }

    @Override
    public List<DeviceGroup> listByOrgIdAndGroupIds(Long orgId, List<Long> groupIds) {
        BizAssert.notNull(orgId, "orgId is must not be null");
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        return deviceGroupMapper.listByOrgIdAndGroupIds(orgId, groupIds);
    }

    @Override
    public List<Long> listDeviceIdByCondition(Long orgId, Long groupId, int offset,
        int limit) {
        BizAssert.notNull(orgId, "orgId is must not be null");
        BizAssert.notNull(groupId, "groupId is must not be null");
        return deviceGroupMapper.listDeviceIdsByGroupId(orgId, groupId, offset, limit);
    }

    @Override
    public List<Long> listGroupIdByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        BizAssert.notNull(orgId, "orgId is must not be null");
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        return deviceGroupMapper.listGroupIdByOrgIdAndDeviceIds(orgId, deviceIds);
    }

    @Override
    public List<String> listGroupNameByCondition(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId is must not be null");
        BizAssert.notNull(deviceId, "deviceId is must not be null");
        return deviceGroupMapper.listGroupNameByCondition(orgId, deviceId);
    }

    @Override
    public List<DeviceGroup> listByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        BizAssert.notNull(orgId, "orgId is must not be null");
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        return deviceGroupMapper.listByOrgIdAndDeviceIds(orgId, deviceIds);
    }

    @Override
    public boolean deleteByCondition(Long orgId, List<Long> deviceIds, List<Long> groupIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        deviceGroupMapper.deleteByCondition(orgId, deviceIds, groupIds);
        // 有设备关系的通信组(删除)
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            for (Long deviceId : deviceIds) {
                resetDeviceGroupRelationDeleteDeviceGroupIdRelation(orgId, deviceId, groupIds);
            }
        }
        //处理超限逻辑
        deviceManager.doDeviceCapacityExceeded(orgId, deviceIds);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetRelationByDeviceId(Long orgId, Long deviceId, List<Long> finalGroupIds,
        Integer appType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");

        List<Long> incGroupIds = new ArrayList<>(finalGroupIds);
        //1.校验设备绑定的数量是否超限
        int channel = orgService.getVerifyChannel(orgId).pickDataThrowException();
        if (VerifyChannel.THIRDPART.getValue() == channel) {
            checkDeviceMemberLimit(orgId, deviceId, incGroupIds);
        }

        //2.查询设备已绑定组Id列表
        List<Long> existGroupIds = deviceGroupMapper
            .listGroupIdByOrgIdAndDeviceIdsAndAppType(orgId, Collections.singletonList(deviceId),
                appType);

        //3.删除多余的组Id列表
        List<Long> decGroupIds = new ArrayList<>(existGroupIds);
        decGroupIds.removeAll(finalGroupIds);
        if (CollectionUtils.isNotEmpty(decGroupIds)) {
            deviceGroupMapper
                .deleteByCondition(orgId, Collections.singletonList(deviceId), decGroupIds);
        }

        //4.构建需要添加的组Id列表
        incGroupIds.removeAll(existGroupIds);
        if (CollectionUtils.isNotEmpty(incGroupIds)) {
            List<GroupInfo> groupInfos = groupService.findGroupByIds(orgId, incGroupIds)
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(groupInfos)) {
                List<DeviceGroup> deviceGroups = new ArrayList<>(groupInfos.size());
                groupInfos.forEach(groupInfo -> {
                    if (!Objects.equals(appType, groupInfo.getAppType())) {
                        return;
                    }
                    DeviceGroup deviceGroup = new DeviceGroup();
                    deviceGroup.setDeviceGroupId(genDeviceGroupId());
                    deviceGroup.setDeviceId(deviceId);
                    BeanUtils.copyProperties(groupInfo, deviceGroup);
                    deviceGroups.add(deviceGroup);
                });
                deviceGroupMapper.batchInsert(deviceGroups);
            }
        }

        // -----只处理有设备组或设备与通行组关系的数据------
        // 1. 有设备关系的通信组(删除)
        resetDeviceGroupRelationDeleteDeviceGroupIdRelation(orgId, deviceId, finalGroupIds);
        // 2. 有设备组关系的通信组(新增)
        resetDeviceGroupRelationAddDeviceGroupIdRelation(orgId, deviceId, finalGroupIds);

        //5.处理超限逻辑
        if (CollectionUtils.isNotEmpty(decGroupIds) || CollectionUtils.isNotEmpty(incGroupIds)) {
            deviceManager.doDeviceCapacityExceeded(orgId, Collections.singletonList(deviceId));
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertRelationByDeviceIds(Long orgId, List<Long> deviceIds, List<Long> groupIds, Integer appType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(groupIds), "groupIds must not be null");

        List<Long> existGroupIds = deviceGroupMapper
                .listGroupIdByOrgIdAndDeviceIdsAndAppType(orgId, deviceIds, appType);
        for (Long groupId : existGroupIds) {
            groupIds.remove(groupId);
            if (CollectionUtils.isEmpty(groupIds)) {
                return Boolean.TRUE;
            }
        }
        List<Long> allGroupIds = new ArrayList<>(existGroupIds);
        allGroupIds.addAll(groupIds);
        //校验设备绑定的数量是否超限
        int channel = orgService.getVerifyChannel(orgId).pickDataThrowException();
        if (VerifyChannel.THIRDPART.getValue() == channel) {
            checkDeviceListMemberLimit(orgId, deviceIds, allGroupIds);
        }
        Set<Long> groupIdList = new HashSet<>(groupIds);
        //需要添加的组Id列表
        List<GroupInfo> groupInfos = groupService.findGroupByIds(orgId, new ArrayList<>(groupIdList))
                .pickDataThrowException();
        if (CollectionUtils.isNotEmpty(groupInfos)) {
            List<DeviceGroup> deviceGroups = new ArrayList<>(groupInfos.size());
            for (Long deviceId : deviceIds) {
                for (GroupInfo groupInfo : groupInfos) {
                    if (!Objects.equals(appType, groupInfo.getAppType())) {
                        continue;
                    }
                    DeviceGroup deviceGroup = new DeviceGroup();
                    deviceGroup.setDeviceGroupId(genDeviceGroupId());
                    deviceGroup.setDeviceId(deviceId);
                    BeanUtils.copyProperties(groupInfo, deviceGroup);
                    deviceGroups.add(deviceGroup);
                }
                // 有设备组关系的通信组(新增)
                resetDeviceGroupRelationAddDeviceGroupIdRelation(orgId, deviceId, groupIds);
            }
            deviceGroupMapper.batchInsert(deviceGroups);
        }
        //处理超限逻辑
        deviceManager.doDeviceCapacityExceeded(orgId, deviceIds);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetRelationByGroupId(Long orgId, Long groupId, List<Long> finalDeviceIds,
        Integer appType) {
        ResetGroupRelationDTO resetGroupRelationDTO = resetRelationByGroupIdAndDeviceIdList(orgId, groupId, finalDeviceIds, appType);
        return resetGroupRelationDTO.getResult();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResetGroupRelationDTO resetRelationByGroupIdAndDeviceIdList(Long orgId, Long groupId, List<Long> finalDeviceIds, Integer appType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(groupId, "groupId must not be null");
        List<Long> existDeviceIds = deviceGroupMapper
                .listDeviceIdByOrgIdAndGroupIdsAndAppType(orgId, Collections.singletonList(groupId),
                        appType);
        ResetGroupRelationDTO resetGroupRelationDTO = new ResetGroupRelationDTO();
        // 批量新增增加的关系(上面重置接口有人员数量限制,这里不需要限制?)
        List<Long> incDeviceIds = new ArrayList<>(finalDeviceIds);
        incDeviceIds.removeAll(existDeviceIds);
        GroupInfo group = groupService.getGroupInfo(orgId, groupId).pickDataThrowException();
        BizAssert.notNull(group, DeviceErrorCode.DEVICE_BIND_GROUP_NOT_EXIST,
                DeviceErrorCode.DEVICE_BIND_GROUP_NOT_EXIST.getMessage());
        if (CollectionUtils.isNotEmpty(incDeviceIds)) {
            List<DeviceGroup> deviceGroups = new ArrayList<>(incDeviceIds.size());
            for (Long deviceId : incDeviceIds) {
                if (!Objects.equals(appType, group.getAppType())) {
                    continue;
                }
                DeviceGroup deviceGroup = new DeviceGroup();
                deviceGroup.setDeviceGroupId(genDeviceGroupId());
                deviceGroup.setDeviceId(deviceId);
                BeanUtils.copyProperties(group, deviceGroup);
                deviceGroups.add(deviceGroup);
            }
            if (CollectionUtils.isNotEmpty(deviceGroups)) {
                deviceGroupMapper.batchInsert(deviceGroups);
            }
            resetGroupRelationDTO.setAddedDeviceIdList(incDeviceIds);
        }

        // 删除减少关系
        List<Long> decDeviceIds = new ArrayList<>(existDeviceIds);
        decDeviceIds.removeAll(finalDeviceIds);
        if (CollectionUtils.isNotEmpty(decDeviceIds)) {
            deviceGroupMapper
                    .deleteByCondition(orgId, decDeviceIds, Collections.singletonList(groupId));
            resetGroupRelationDTO.setRemovedDeviceIdList(decDeviceIds);
        }
        //处理超限 此处多考虑一种情况:设备关联权限组没变,权限组关联人员和部门改变,此时也需要改变数据库超限状态
        deviceManager.doDeviceCapacityExceeded(orgId, finalDeviceIds);
        resetGroupRelationDTO.setResult(Boolean.TRUE);
        return resetGroupRelationDTO;
    }

    @Override
    public Integer distinctCountDeviceGroupPerson(DeviceGroupPersonCountDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId is required");
        BizAssert.notNull(dto.getDeviceId(), "deviceId is required");

        // 查询设备绑定的权限组
        List<Long> groupIdList = deviceGroupMapper.findGroupIdByDeviceIdAppTypes(dto.getOrgId(), dto.getDeviceId(), dto.getAppTypeList());

        if (CollectionUtils.isEmpty(groupIdList)) {
            return 0;
        }
        return groupRelationService.getDistinctPersonSize(dto.getOrgId(), groupIdList).pickDataThrowException();
    }

    @Override
    public List<DeviceGroup> findRelationByGroupId(Long orgId,Long groupId) {
        Assert.notNull(orgId, "orgId is required");
        Assert.notNull(groupId, "groupId is required");
        return deviceGroupMapper.listByOrgIdAndGroupIds(orgId, Lists.newArrayList(groupId));
    }

    @Override
    public List<SimpleDeviceGroupDTO> findDeviceGroupByGroupIds(Long orgId, List<Long> groupIds) {
        List<DeviceGroup> deviceGroupList = deviceGroupMapper.listByOrgIdAndGroupIds(orgId, groupIds);
        if (CollectionUtils.isEmpty(deviceGroupList)) {
            return Collections.emptyList();
        }
        List<Long> deviceIdList = deviceGroupList.stream().map(DeviceGroup::getDeviceId).distinct().collect(
            Collectors.toList());
        List<Device> deviceList = deviceMapper.listByIds(deviceIdList);
        if (CollectionUtils.isEmpty(deviceList)) {
            return Collections.emptyList();
        }
        Map<Long, List<DeviceGroup>> deviceGroupMap = deviceGroupList.stream().collect(Collectors.groupingBy(DeviceGroup::getGroupId));
        Map<Long/*deviceId*/, String/*deviceSn*/> deviceIdToDeviceSnMap = deviceList.stream().collect(Collectors.toMap(Device::getDeviceId, Device::getDeviceSn));
        List<SimpleDeviceGroupDTO> responseList = new ArrayList<>(deviceGroupList.size());
        for (Map.Entry<Long, List<DeviceGroup>> entry : deviceGroupMap.entrySet()) {
            Long groupId = entry.getKey();
            List<DeviceGroup> deviceGroups = entry.getValue();
            SimpleDeviceGroupDTO response = new SimpleDeviceGroupDTO();
            response.setGroupId(groupId);
            List<SimpleDeviceGroupDTO.SimpleDevice> simpleDeviceList = new ArrayList<>(deviceGroups.size());
            for (DeviceGroup deviceGroup : deviceGroups) {
                SimpleDeviceGroupDTO.SimpleDevice simpleDevice = new SimpleDeviceGroupDTO.SimpleDevice();
                simpleDevice.setDeviceId(deviceGroup.getDeviceId());
                simpleDevice.setDeviceSn(deviceIdToDeviceSnMap.get(deviceGroup.getDeviceId()));
                simpleDeviceList.add(simpleDevice);
            }
            response.setDeviceList(simpleDeviceList);
            responseList.add(response);
        }
        return responseList;
    }

    @Override
    public Map<Long, List<Long>> getDeviceIdToGroupIdsByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds, Integer appType) {
        Map<Long, List<Long>> deviceIdToGroupIdsMap = new HashMap<>();
        List<DeviceGroup> list = deviceGroupManager.listByOrgIdAndDeviceIds(orgId, deviceIds);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> groupIds = list.stream().map(DeviceGroup::getGroupId)
                .collect(Collectors.toList());
            if (appType != null) {
                //过滤对应设备类型
                groupIds = filterGroupAppType(orgId, appType, groupIds);
            }

            //组装map
            List<DeviceGroup> dgs = deviceGroupManager.listByOrgIdAndGroupIds(orgId, groupIds);
            if (CollectionUtils.isNotEmpty(dgs)) {
                dgs.forEach(x -> {
                    if (deviceIdToGroupIdsMap.containsKey(x.getDeviceId())) {
                        deviceIdToGroupIdsMap.get(x.getDeviceId()).add(x.getGroupId());
                    } else {
                        deviceIdToGroupIdsMap
                            .put(x.getDeviceId(), com.google.common.collect.Lists.newArrayList(x.getGroupId()));
                    }
                });
            }
        }
        return deviceIdToGroupIdsMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetRelationOfDeviceGroup(DeviceCompositeGroupRelationAddReq req) {
        BizAssert.notNull(req);
        BizAssert.notNull(req.getOrgId());
        BizAssert.notNull(req.getGroupId());

        Long orgId = req.getOrgId();
        Long groupId = req.getGroupId();

        List<Long> allNewDeviceIdList;
        List<DeviceGroup> existDeviceGroupList = deviceGroupMapper.listByOrgIdAndGroupIds(orgId, Lists.newArrayList(groupId));
        List<DeviceCompositeGroup> existDeviceCompositeGroupList = deviceCompositeGroupMapper.selectByGroupId(orgId, groupId, null);

        //1. 参数无设备和设备组
        if (CollectionUtils.isEmpty(req.getDeviceIdList())
            && CollectionUtils.isEmpty(req.getDeviceCompositeIdList())) {
            deleteDeviceCompositeGroupRelation(orgId, existDeviceGroupList, existDeviceCompositeGroupList);
            return true;
        }

        GroupInfo groupInfo = groupService.getGroupInfo(orgId, groupId).pickDataThrowException();
        if (groupInfo != null && groupInfo.getAppType() != null) {
            if (groupInfo.getAppType() == GroupAppType.GATE.getValue()
                || groupInfo.getAppType() == GroupAppType.ATTENDANCE.getValue()
                || groupInfo.getAppType() == GroupAppType.BLACK_LIST.getValue()) {
                req.setSceneType(SceneTypeEnums.STANDARD_MODEL.getValue());
            } else if (groupInfo.getAppType() == GroupAppType.ELEVATOR.getValue()) {
                req.setSceneType(SceneTypeEnums.ELEVATOR_MODEL.getValue());
            } else if (groupInfo.getAppType() == GroupAppType.MEETING.getValue()) {
                req.setSceneType(SceneTypeEnums.MEETING_ROOM_MODEL.getValue());
            } else if (groupInfo.getAppType() == GroupAppType.CLASS_ROM.getValue()) {
                req.setSceneType(SceneTypeEnums.CLASSROOM_MODEL.getValue());
            }
        }
        allNewDeviceIdList = getAllNewDeviceIdList(req);
        // 2.行权限组和设备关系处理
        if (CollectionUtils.isNotEmpty(existDeviceGroupList)) {
            List<Long> existDeviceIdList = existDeviceGroupList.stream()
                .map(DeviceGroup::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
            List<Long> incDeviceIdList = CollUtil.subtractToList(allNewDeviceIdList, existDeviceIdList);
            List<Long> decDeviceIdList = CollUtil.subtractToList(existDeviceIdList, allNewDeviceIdList);
            // 创建通行权限组和设备关系
            batchCreateDeviceGroupRelation(orgId, groupId, incDeviceIdList);
            // 删除通行权限组和设备关系
            batchDeleteDeviceGroupRelation(orgId, groupId, decDeviceIdList);
        } else {
            // 创建通行权限组和设备关系
            batchCreateDeviceGroupRelation(orgId, groupId, allNewDeviceIdList);
        }
        // 3. 通行权限组和设备/设备组关系处理
        if (CollectionUtils.isNotEmpty(existDeviceCompositeGroupList)) {
            // 通行权限组和设备组关系
            handleDeviceCompositeGroup(req, existDeviceCompositeGroupList);
            // 通行权限组和设备关系
            handleDeviceCompositeGroupForDevice(req, existDeviceCompositeGroupList);
        } else {
            // 创建通行权限组和设备组关系
            if (CollectionUtils.isNotEmpty(req.getDeviceCompositeIdList())) {
                batchCreateDeviceCompositeGroupRelation(req.getDeviceCompositeIdList(), DeviceCompositeGroupRangeType.GROUP, req);
            }
            // 创建通行权限组和设备关系
            if (CollectionUtils.isNotEmpty(req.getDeviceIdList())) {
                batchCreateDeviceCompositeGroupRelation(req.getDeviceIdList(), DeviceCompositeGroupRangeType.DEVICE, req);
            }
        }
        return true;
    }

    private List<Long> filterGroupAppType(Long orgId, Integer appType, List<Long> allGroupIds) {
        List<Long> groupIds = new ArrayList<>(allGroupIds.size());
        List<GroupInfo> groupInfos = groupService.findGroupByIds(orgId, allGroupIds)
            .pickDataThrowException();
        if (CollectionUtils.isNotEmpty(groupInfos)) {
            groupIds = groupInfos.stream()
                .filter(groupInfo -> appType.equals(groupInfo.getAppType()))
                .map(GroupInfo::getGroupId)
                .collect(Collectors.toList());
        }
        return groupIds;
    }

    /**
     * 设备绑定人员数量限制校验
     *
     * @param orgId       机构号
     * @param deviceId    设备Id
     * @param incGroupIds 增量组Id列表
     */
    private void checkDeviceMemberLimit(Long orgId, Long deviceId, List<Long> incGroupIds) {
        Device device = deviceMapper.getById(deviceId);
        if (device != null) {
            Integer personSize = groupRelationService
                .getDistinctPersonSize(orgId, incGroupIds).pickDataThrowException();
            Integer deviceCapacity = deviceTypeMapManager.getDeviceCapacity(device.getDeviceType());
            if (deviceCapacity != null) {
                BizAssert.isTrue(personSize <= deviceCapacity,
                    DeviceErrorCode.OUT_OF_RANGE_DEVICE_GROUP_MEMBERSIZE,
                    DeviceErrorCode.OUT_OF_RANGE_DEVICE_GROUP_MEMBERSIZE.getMessage());
            }
        }
    }

    private void checkDeviceListMemberLimit(Long orgId, List<Long> deviceIds, List<Long> groupIds) {
        List<Device> devices = deviceMapper.listByIds(deviceIds);
        if (CollectionUtils.isNotEmpty(devices)) {
            List<Integer> deviceTypeList = devices.stream().map(Device::getDeviceType).collect(Collectors.toList());
            Integer personSize = groupRelationService
                    .getDistinctPersonSize(orgId, groupIds).pickDataThrowException();
            List<Integer> devicesCapacity = deviceTypeMapManager.getDevicesCapacity(deviceTypeList);
            if (CollectionUtils.isNotEmpty(devicesCapacity)) {
                for (Integer capacity : devicesCapacity) {
                    BizAssert.isTrue(personSize <= capacity,
                            DeviceErrorCode.OUT_OF_RANGE_DEVICE_GROUP_MEMBERSIZE,
                            DeviceErrorCode.OUT_OF_RANGE_DEVICE_GROUP_MEMBERSIZE.getMessage());
                }
            }
        }
    }

    /**
     * 设备绑定门禁默认组
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     */
    private void bindDefaultGroup(Long orgId, Long deviceId) {
        Device device = deviceMapper.getById(deviceId);
        if (device != null) {
            List<GroupInfo> groupInfos = groupService
                .getDefaultGroup(orgId, GroupAppType.GATE.getValue()).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(groupInfos)) {
                boolean needBindGroup = true;
                //检查组内人员是否超限，超过设备就不绑定组了
                for (GroupInfo groupInfo : groupInfos) {
                    List<Long> personIds = groupRelationService
                        .findPersonIdByGroupId(orgId, groupInfo.getGroupId(),
                            PersonType.MEMBER.getValue()).pickDataThrowException();
                    int size = 0;
                    if (CollectionUtils.isNotEmpty(personIds)) {
                        size = personIds.size();
                    }
                    if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NOT_BIND_DEFAULT_GROUP_DEVICE_LIST, device.getDeviceType())) {
                        needBindGroup = false;
                    } else {
                        //判断是否超限
                        Integer deviceCapacity = deviceTypeMapManager
                            .getDeviceCapacity(device.getDeviceType());
                        if (deviceCapacity != null) {
                            if (deviceCapacity < size) {
                                needBindGroup = false;
                            }
                        }
                    }

                    if (needBindGroup) {
                        DeviceGroup deviceGroup = new DeviceGroup();
                        deviceGroup.setDeviceGroupId(genDeviceGroupId());
                        deviceGroup.setDeviceId(deviceId);
                        BeanUtils.copyProperties(groupInfo, deviceGroup);
                        deviceGroupMapper.insert(deviceGroup);

                        buildGroupRange(orgId,deviceId,groupInfo);
                    }
                }
            }
        }
    }

    private void bindVisitorGroup(Long orgId, Long deviceId) {
        GroupInfo groupInfo = groupService.getVisitorGroup(orgId).pickDataThrowException();
        DeviceGroup deviceGroup = new DeviceGroup();
        deviceGroup.setDeviceGroupId(this.genDeviceGroupId());
        deviceGroup.setOrgId(orgId);
        deviceGroup.setDeviceId(deviceId);
        deviceGroup.setGroupId(groupInfo.getGroupId());
        deviceGroup.setGroupCode(groupInfo.getGroupCode());
        deviceGroupMapper.insert(deviceGroup);
    }

    private void buildGroupRange(Long orgId, Long deviceId, GroupInfo groupInfo) {
        List<DeviceCompositeGroup> deviceCompositeGroupList= deviceCompositeGroupMapper.selectByGroupId(orgId,groupInfo.getGroupId(),null);
        if(CollectionUtils.isNotEmpty(deviceCompositeGroupList)){
            boolean flag = deviceCompositeGroupList.stream()
                .filter(e->DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                .anyMatch(e->e.getRangeId().equals(deviceId));
            if(!flag){
                DeviceCompositeGroup deviceCompositeGroup = new DeviceCompositeGroup();
                deviceCompositeGroup.setDeviceCompositeGroupId(this.genDeviceCompositeGroupId());
                deviceCompositeGroup.setRangeId(deviceId);
                deviceCompositeGroup.setOrgId(orgId);
                deviceCompositeGroup.setRangeType(DeviceCompositeGroupRangeType.DEVICE.getCode());
                deviceCompositeGroup.setGroupId(groupInfo.getGroupId());
                deviceCompositeGroupMapper.insert(deviceCompositeGroup);
            }
        }
    }

    private Long genDeviceGroupId() {
        return idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_GROUP)
            .pickDataThrowException();
    }

    private Long genDeviceCompositeGroupId() {
        return idgeneratorService.getNextIdByTypeName(DeviceCompositeGroup.class.getName()).getData();
    }

    private void batchCreateDeviceCompositeGroupRelation(List<Long> addDeviceIdList,
        DeviceCompositeGroupRangeType deviceCompositeGroupRangeType,
        DeviceCompositeGroupRelationAddReq req) {
        BatchIdDto batchIdDto = idgeneratorService.getNextIdBatchBytypeName(DeviceCompositeGroup.class.getName(), addDeviceIdList.size()).pickDataThrowException();
        batchCreateDeviceCompositeGroupRelation(req, deviceCompositeGroupRangeType, addDeviceIdList, batchIdDto);
    }

    private void batchCreateDeviceCompositeGroupRelation(DeviceCompositeGroupRelationAddReq req,
        DeviceCompositeGroupRangeType deviceCompositeGroupRangeType,
        List<Long> deviceIdList,
        BatchIdDto batchIdDto) {
        for (Long deviceId : deviceIdList) {
            DeviceCompositeGroup deviceCompositeGroup = new DeviceCompositeGroup();
            deviceCompositeGroup.setDeviceCompositeGroupId(batchIdDto.nextId());
            deviceCompositeGroup.setRangeId(deviceId);
            deviceCompositeGroup.setOrgId(req.getOrgId());
            deviceCompositeGroup.setRangeType(deviceCompositeGroupRangeType.getCode());
            deviceCompositeGroup.setGroupId(req.getGroupId());
            deviceCompositeGroupMapper.insert(deviceCompositeGroup);
        }
    }


    protected void handleDeviceCompositeGroup(DeviceCompositeGroupRelationAddReq req,
        List<DeviceCompositeGroup> existDeviceCompositeGroupList) {
        List<Long> existDeviceCompositeIdList = existDeviceCompositeGroupList.stream()
            .filter(deviceCompositeGroup -> DeviceCompositeGroupRangeType.GROUP.getCode().equals(deviceCompositeGroup.getRangeType()))
            .map(DeviceCompositeGroup::getRangeId)
            .distinct()
            .collect(Collectors.toList());
        List<Long> addDeviceCompositeGroupIdList = Lists.newArrayList();
        List<Long> delDeviceCompositeGroupIdList = Lists.newArrayList();
        // 通行权限组和设备组关系
        if (CollectionUtils.isNotEmpty(existDeviceCompositeIdList)) {
            addDeviceCompositeGroupIdList = CollUtil.subtractToList(req.getDeviceCompositeIdList(), existDeviceCompositeIdList);
            delDeviceCompositeGroupIdList = CollUtil.subtractToList(existDeviceCompositeIdList, req.getDeviceCompositeIdList());
        } else {
            addDeviceCompositeGroupIdList = req.getDeviceCompositeIdList();
        }
        if (CollectionUtils.isNotEmpty(delDeviceCompositeGroupIdList)) {
            final List<Long> finalDelDeviceCompositeGroupIdList = delDeviceCompositeGroupIdList;
            List<Long> existDeviceCompositeGroupIdList = existDeviceCompositeGroupList.stream()
                .filter(e -> finalDelDeviceCompositeGroupIdList.contains(e.getRangeId()))
                .filter(e -> DeviceCompositeGroupRangeType.GROUP.getCode().equals(e.getRangeType()))
                .map(DeviceCompositeGroup::getDeviceCompositeGroupId)
                .distinct()
                .collect(Collectors.toList());
            deviceCompositeGroupMapper.deleteByIds(req.getOrgId(), existDeviceCompositeGroupIdList);
        }
        if (CollectionUtils.isNotEmpty(addDeviceCompositeGroupIdList)) {
            batchCreateDeviceCompositeGroupRelation(addDeviceCompositeGroupIdList, DeviceCompositeGroupRangeType.GROUP, req);
        }
        log.info("handleDeviceCompositeGroup addDeviceCompositeGroupIdList={},delDeviceCompositeGroupIdList={}",
            addDeviceCompositeGroupIdList,delDeviceCompositeGroupIdList);
    }

    private void handleDeviceCompositeGroupForDevice(DeviceCompositeGroupRelationAddReq req, List<DeviceCompositeGroup> existDeviceCompositeGroupList) {
        List<Long> addDeviceIdList;
        List<Long> delDeviceIdList = Lists.newArrayList();
        List<Long> existDeviceCompositeDeviceIdList = existDeviceCompositeGroupList.stream()
            .filter(deviceCompositeGroup -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(deviceCompositeGroup.getRangeType()))
            .map(DeviceCompositeGroup::getRangeId)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existDeviceCompositeDeviceIdList)) {
            addDeviceIdList = CollUtil.subtractToList(req.getDeviceIdList(), existDeviceCompositeDeviceIdList);
            delDeviceIdList = CollUtil.subtractToList(existDeviceCompositeDeviceIdList, req.getDeviceIdList());
        } else {
            addDeviceIdList = req.getDeviceIdList();
        }
        if (CollectionUtils.isNotEmpty(delDeviceIdList)) {
            final List<Long> finalDelDeviceIdList = delDeviceIdList;
            List<Long> existDeviceCompositeGroupIdList = existDeviceCompositeGroupList.stream()
                .filter(e -> finalDelDeviceIdList.contains(e.getRangeId()))
                .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                .map(DeviceCompositeGroup::getDeviceCompositeGroupId)
                .distinct()
                .collect(Collectors.toList());
            deviceCompositeGroupMapper.deleteByIds(req.getOrgId(), existDeviceCompositeGroupIdList);
        }
        if (CollectionUtils.isNotEmpty(addDeviceIdList)) {
            //去除其他模式的设备
            QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
            queryDeviceDTO.setOrgId(req.getOrgId());
            queryDeviceDTO.setDeviceIds(addDeviceIdList);
            queryDeviceDTO.setSceneType(req.getSceneType());
            List<DeviceInfoDTO> deviceInfos = deviceService.listByLikeCondition(queryDeviceDTO)
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                List<Long> deviceIds =  deviceInfos.stream().map(DeviceInfoDTO::getDeviceId).
                    distinct().collect(Collectors.toList());
                batchCreateDeviceCompositeGroupRelation(deviceIds, DeviceCompositeGroupRangeType.DEVICE, req);
            }
        }
        log.info("handleDeviceCompositeGroupForDevice addDeviceIdList={},delDeviceIdList={}", addDeviceIdList, delDeviceIdList);
    }

    private void resetDeviceGroupRelationAddDeviceGroupIdRelation(Long orgId, Long deviceId, List<Long> finalGroupIdList) {
        if (CollectionUtils.isEmpty(finalGroupIdList)) {
            return;
        }
        List<DeviceCompositeGroup> deviceCompositeGroupList = deviceCompositeGroupMapper.selectByGroupIdList(orgId, finalGroupIdList, null);
        if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
            List<Long> addGroupIdList = Lists.newArrayList();
            Map<Long, List<DeviceCompositeGroup>> groupDeviceSizeDtoMap = getGroupIdDeviceCompositeGroupListMap(deviceCompositeGroupList);
            for (Map.Entry<Long, List<DeviceCompositeGroup>> entry : groupDeviceSizeDtoMap.entrySet()) {
                //没有设备关系的通信组
                boolean hasNoDevice = entry.getValue().stream()
                    .noneMatch(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()));
                //有设备关系且没有当前设备关系的通行组
                boolean noCurDevice = entry.getValue().stream()
                    .filter(e -> DeviceCompositeGroupRangeType.DEVICE.getCode().equals(e.getRangeType()))
                    .noneMatch(e -> e.getRangeId().equals(deviceId));
                if (hasNoDevice) {
                    addGroupIdList.add(entry.getKey());
                }
                if (noCurDevice) {
                    addGroupIdList.add(entry.getKey());
                }
            }

            List<Long> newAddGroupIdList = addGroupIdList.stream().distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(newAddGroupIdList)) {
                return;
            }
            BatchIdDto batchIdDto = idgeneratorService.getNextIdBatchBytypeName(DeviceCompositeGroup.class.getName(), newAddGroupIdList.size()).pickDataThrowException();
            for (Long groupId : newAddGroupIdList) {
                DeviceCompositeGroup deviceCompositeGroup = new DeviceCompositeGroup();
                deviceCompositeGroup.setDeviceCompositeGroupId(batchIdDto.nextId());
                deviceCompositeGroup.setRangeId(deviceId);
                deviceCompositeGroup.setOrgId(orgId);
                deviceCompositeGroup.setRangeType(DeviceCompositeGroupRangeType.DEVICE.getCode());
                deviceCompositeGroup.setGroupId(groupId);
                deviceCompositeGroupMapper.insert(deviceCompositeGroup);
            }
        }
    }

    private void resetDeviceGroupRelationDeleteDeviceGroupIdRelation(Long orgId, Long deviceId, List<Long> finalGroupIdList) {
        List<Long> existDeviceGroupIdList = Lists.newArrayList();
        List<DeviceCompositeGroup> existDeviceGroupList = deviceCompositeGroupMapper.selectByDeviceIdList(orgId, Lists.newArrayList(deviceId));
        if (CollectionUtils.isNotEmpty(existDeviceGroupList)) {
            existDeviceGroupIdList.addAll(existDeviceGroupList.stream().map(DeviceCompositeGroup::getGroupId).distinct().collect(Collectors.toList()));
        }
        List<Long> ids;
        if (CollectionUtils.isEmpty(finalGroupIdList)) {
            ids = existDeviceGroupList.stream().map(DeviceCompositeGroup::getDeviceCompositeGroupId).distinct().collect(Collectors.toList());
        } else {
            existDeviceGroupIdList.removeAll(finalGroupIdList);
            ids = existDeviceGroupList.stream()
                .filter(e->existDeviceGroupIdList.contains(e.getGroupId()))
                .map(DeviceCompositeGroup::getDeviceCompositeGroupId)
                .distinct()
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            deviceCompositeGroupMapper.deleteByIds(orgId, ids);
        }
    }

    private void deleteDeviceCompositeGroupRelation(Long orgId,
        List<DeviceGroup> existDeviceGroupList,
        List<DeviceCompositeGroup> existDeviceCompositeGroupList) {
        // 删除通行组和设备的关系
        if (CollectionUtils.isNotEmpty(existDeviceGroupList)) {
            List<Long> deviceGroupIdList = existDeviceGroupList.stream()
                .map(DeviceGroup::getDeviceGroupId)
                .distinct()
                .collect(Collectors.toList());
            deviceGroupMapper.deleteByDeviceGroupIdList(orgId, deviceGroupIdList);
        }
        // 删除通行组和设备组及设备的关系
        if (CollectionUtils.isNotEmpty(existDeviceCompositeGroupList)) {
            List<Long> deviceCompositeGroupIdList = existDeviceCompositeGroupList.stream()
                .map(DeviceCompositeGroup::getDeviceCompositeGroupId)
                .distinct()
                .collect(Collectors.toList());
            deviceCompositeGroupMapper.deleteByIds(orgId, deviceCompositeGroupIdList);
        }
    }

    private void batchCreateDeviceGroupRelation(Long orgId, Long groupId, List<Long> allNewDeviceIdList) {
        for (Long deviceId : allNewDeviceIdList) {
            DeviceGroup deviceGroup = new DeviceGroup();
            deviceGroup.setDeviceGroupId(this.genDeviceGroupId());
            deviceGroup.setOrgId(orgId);
            deviceGroup.setDeviceId(deviceId);
            deviceGroup.setGroupId(groupId);
            GroupInfo group = groupService.getGroupInfo(orgId, groupId).pickDataThrowException();
            if (group != null) {
                deviceGroup.setGroupCode(group.getGroupCode());
            }
            deviceGroupMapper.insert(deviceGroup);
        }
    }

    private void batchDeleteDeviceGroupRelation(Long orgId, Long groupId, List<Long> decDeviceIdList) {
        if (CollectionUtils.isNotEmpty(decDeviceIdList)) {
            List<DeviceGroup> deviceGroupList = deviceGroupMapper.findRelationByGroupId(orgId, groupId, decDeviceIdList);
            if (CollectionUtils.isNotEmpty(deviceGroupList)) {
                List<Long> deviceGroupIdList = deviceGroupList.stream()
                    .map(DeviceGroup::getDeviceGroupId)
                    .distinct()
                    .collect(Collectors.toList());
                deviceGroupMapper.deleteByDeviceGroupIdList(orgId, deviceGroupIdList);
            }
        }
    }

    private List<Long> getAllNewDeviceIdList(DeviceCompositeGroupRelationAddReq req) {
        List<Long> allNewDeviceIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(req.getDeviceIdList())) {
            allNewDeviceIdList.addAll(req.getDeviceIdList());
        }
        List<Long> deviceCompositeIdList = getAllNewCompositeIdList(req);
        if (CollectionUtils.isNotEmpty(deviceCompositeIdList)) {
            List<Long> compositeGroupDeviceIdList = deviceCompositeManager.
                getDeviceIdsByCompositeDeepBatch(req.getOrgId(), deviceCompositeIdList, req.getSceneType());
            if (CollectionUtils.isNotEmpty(compositeGroupDeviceIdList)) {
                allNewDeviceIdList.addAll(compositeGroupDeviceIdList);
            }
        }
        return allNewDeviceIdList.stream().distinct().collect(Collectors.toList());
    }

    private List<Long> getAllNewCompositeIdList(DeviceCompositeGroupRelationAddReq req) {
        List<Long>  deviceCompositeIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(req.getDeviceCompositeIdList())) {
            deviceCompositeIdList.addAll(req.getDeviceCompositeIdList());
        }
        return deviceCompositeIdList.stream().distinct().collect(Collectors.toList());
    }

    private Map<Long, List<DeviceCompositeGroup>> getGroupIdDeviceCompositeGroupListMap(List<DeviceCompositeGroup> deviceCompositeGroupList) {
        Map<Long, List<DeviceCompositeGroup>> groupIdDeviceCompositeGroupListMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(deviceCompositeGroupList)) {
            groupIdDeviceCompositeGroupListMap = deviceCompositeGroupList.stream()
                .collect(Collectors.groupingBy(DeviceCompositeGroup::getGroupId));
        }
        return groupIdDeviceCompositeGroupListMap;
    }
}