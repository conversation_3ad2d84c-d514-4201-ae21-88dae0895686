package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeSimpleDto;
import com.moredian.magicube.device.dto.qrcode.AleadyActivateOrgDto;
import com.moredian.magicube.device.dto.qrcode.BindPersonnelRelationDto;
import com.moredian.magicube.device.dto.qrcode.InsertNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.SimpleActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.UpdateNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.UploadNetworkCodeStatusDTO;

import java.util.Date;
import java.util.List;

/**
 * @Classname： QrCodeManager
 * @Date: 2023/1/4 1:53 下午
 * @Author: _AF
 * @Description:
 */
public interface FunctionQrCodeManager {

    /**
     * 创建二维码
     *
     * @param
     * @return 二维码路径
     */
    Long createActivateQrCode(ActivateQrCodeCreateDto dto);

    /**
     * 获取二维码
     *
     * @param memberId
     * @param id
     * @return
     */
    ActivateQrCodeDto getActivateQrCodeById(Long memberId, Long id);

    /**
     *  通过ID获取激活二维码
     *
     * @param id    编号
     * @param orgId 组织 ID
     * @return {@link ActivateQrCodeDto}
     */
    ActivateQrCodeDto getActivateQrCodeByOrgIdAndId(Long orgId, Long id);

    /**
     * 根据人员Id获取对应的激活二维码
     *
     * @param memberId
     * @return
     */
    List<ActivateQrCodeSimpleDto> listByMemberId(Long memberId, Integer roleType, Integer source);

    /**
     * 根据人员Id获取历史机构
     *
     * @param memberId
     * @param roleType
     * @param source
     * @return
     */
    List<ActivateQrCodeSimpleDto> listHistoryOrgByMemberId(Long memberId, Integer roleType, Integer source);


    /**
     * 查看管理员的激活记录
     *
     * @param id
     * @param roleType
     * @return
     */
    ActivateQrCodeSimpleDto getByIdAndRoleType(Long id, Integer roleType);

    /**
     * 更新最新的激活时间
     *
     * @param newActivateTime
     */
    void updateNewActivateTime(Long id, Date newActivateTime);


    /**
     * 查看安装师傅已经创建激活码的机构
     *
     * @param accountId
     * @return
     */
    List<AleadyActivateOrgDto> listByAleadyActivateOrg(Long accountId);


    /**
     * 获取身份绑定关系
     * @return
     */
    Integer getPersonnelRelation(Long accountId);

    /**
     * 绑定关系
     * @param dto
     * @return
     */
    Boolean bindPersonnelRelation(BindPersonnelRelationDto dto);


    /**
     * 创建配网码
     *
     * @param
     * @return 二维码路径
     */
    Long createNetworkQrCode(NetworkQrCodeCreateDto dto);

    /**
     * 新增机构网络信息
     *
     * @param   dto 网络信息
     * @return 二维码路径
     */
    Long insertNetworkInfo(InsertNetworkInfoDTO dto);

    /**
     * 编辑机构配网信息
     *
     * @param   dto 网络信息
     * @return 二维码路径
     */
    Boolean updateNetworkInfo(UpdateNetworkInfoDTO dto);

    /**
     * 设备配网码状态信息上报
     *
     * @param   dto 配网状态信息
     * @return 是否成功
     */
    Boolean uploadNetworkCodeStatus(UploadNetworkCodeStatusDTO dto);

    /**
     * 根据网络Id删除机构配网信息
     *
     * @param orgId 机构Id
     * @param id    网络Id
     * @return 二维码路径
     */
    Boolean deleteByNetworkId(Long orgId, Long id);

    /**
     * 查询配网码id
     *
     * @param orgId
     * @param id
     * @return
     */
    NetworkQrCodeDto getNetworkQrCodeById(Long orgId, Long id);

    /**
     * 获取配网码信息列表
     *
     * @param orgId
     * @return
     */
    List<OrgNetwork> listNetworkInfoByOrgId(Long orgId);

    /**
     * 根据激活码Id获取网络信息
     *
     * @param qrCodeId
     * @return
     */
    NetworkQrCodeDto getNetWorkInfoByQrCodeId(Long qrCodeId);

    /**
     * 获取激活码简单信息
     *
     * @param qrCodeId
     * @return
     */
    SimpleActivateQrCodeDto getSimpleQrCodeByQrCodeId(Long qrCodeId);
}
