<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.AppTypeRedirectMapper">
    <resultMap id="appTypeRedirectResultMap" type="com.moredian.magicube.device.dao.entity.AppTypeRedirect">
        <result column="id" property="id"/>
        <result column="app_type" property="appType"/>
        <result column="redirect_url" property="redirectUrl"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_app_type_redirect_url
    </sql>

    <sql id="sql_columns">
        id,
        app_type,
        redirect_url,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{id},
        #{appType},
        #{redirectUrl},
        now(3),
        now(3)
    </sql>

    <insert id="add" parameterType="com.moredian.magicube.device.dao.entity.AppTypeRedirect">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <select id="getOne" resultType="com.moredian.magicube.device.dao.entity.AppTypeRedirect"
            resultMap="appTypeRedirectResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="appType != null">
                and app_type = #{appType}
            </if>
            <if test="redirectUrl != null and redirectUrl != ''">
                and redirect_url = #{redirectUrl}
            </if>
        </where>
    </select>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.AppTypeRedirect">
        update
        <include refid="sql_table"/>
        <set>
            <if test="appType != null">
                app_type = #{appType},
            </if>
            <if test="redirectUrl != null and redirectUrl != ''">
                redirect_url = #{redirectUrl},
            </if>
            gmt_modify = now(3)
        </set>
        where
        id = #{id}
    </update>

    <delete id="deleteById">
        delete from
        <include refid="sql_table"/>
        where id = #{id}
    </delete>

    <select id="page" resultMap="appTypeRedirectResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="appType != null">
                and app_type = #{appType}
            </if>
            <if test="redirectUrl != null and redirectUrl != ''">
                and redirect_url like  concat('%',#{redirectUrl},'%')
            </if>
        </where>
    </select>
</mapper>