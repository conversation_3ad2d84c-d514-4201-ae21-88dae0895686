package com.moredian.magicube.device.subscriber;

import com.google.common.collect.Lists;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.model.msg.DeleteGroupRelationDataMsg;
import com.moredian.magicube.device.dao.mapper.DeviceGroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date : 2024/1/19
 */
@Component
@Slf4j
public class GroupDeleteMsgSubscriber {

    @Autowired
    private DeviceGroupMapper deviceGroupMapper;

    @Subscribe
    public void receiveGroupDeleteMsg(DeleteGroupRelationDataMsg msg) {
        log.info("收到删除组的消息: " + (msg != null ? JsonUtils.toJson(msg) : "NULL"));
        deviceGroupMapper.deleteByCondition(msg.getOrgId(), null, Lists.newArrayList(msg.getGroupId()));
    }
}
