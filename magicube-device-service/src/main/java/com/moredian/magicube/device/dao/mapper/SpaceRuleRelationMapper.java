package com.moredian.magicube.device.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.moredian.magicube.device.dao.entity.SpaceRuleRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SpaceRuleRelationMapper extends BaseMapper<SpaceRuleRelation> {
    List<SpaceRuleRelation> getRelationByOrgIdAndRuleId(@Param("orgId") Long orgId, @Param("ruleId") Long ruleId);

    List<Long> getRuleIdByOrgIdAndSpaceId(@Param("orgId") Long orgId, @Param("spaceId") Long spaceId);

    int batchInsert(@Param("list") List<SpaceRuleRelation> list);

    void deleteSpaceRule(@Param("orgId") Long orgId, @Param("ruleId") Long ruleId);

    void update(SpaceRuleRelation spaceRuleRelation);

    void delete(@Param("orgId") Long orgId, @Param("list") List<Long> spaceRuleRelationIdList);

    List<SpaceRuleRelation> getByCondition(@Param("orgId") Long orgId, @Param("ruleId") Long ruleId, @Param("spaceId") Long spaceId);

    List<Long> getSpaceIdByOrgIdAndRuleId(@Param("orgId") Long orgId, @Param("ruleId") Long ruleId);

    List<SpaceRuleRelation> getRelationByOrgId(@Param("orgId") Long orgId);
}