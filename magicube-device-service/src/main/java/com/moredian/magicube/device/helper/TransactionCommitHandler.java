package com.moredian.magicube.device.helper;

import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 *  事务提交后的处理器，action执行严格依赖调用方的事务提交
 *  如果调用方没有事务，不执行；
 *  如果调用方事务回滚，不执行；
 *  action异常不影响调用方事务提交；
 */
@Component
public class TransactionCommitHandler {
    public void handle(Runnable action){
        if (TransactionSynchronizationManager.isActualTransactionActive()){
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                  //具体的异步操作
                  action.run();
                }
            });
        }
    }
}
