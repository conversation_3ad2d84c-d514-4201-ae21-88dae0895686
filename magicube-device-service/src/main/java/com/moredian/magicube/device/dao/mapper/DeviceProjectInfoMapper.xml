<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceProjectInfoMapper">

    <resultMap id="baseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceProjectInfo">
        <result column="id" property="id"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="project_id" property="projectId"/>
        <result column="version" property="version"/>
        <result column="scene_type" property="sceneType"/>
        <result column="project_info" property="projectInfo"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_columns">
        id,
        device_sn,
        project_id,
        version,
        scene_type,
        project_info,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_table">
        hive_device_project_info
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceProjectInfo">
        insert into
        <include refid="sql_table"/>
        (<include refid="sql_columns"/>)
        VALUES
        (#{id}, #{deviceSn}, #{projectId}, #{version}, #{sceneType}, #{projectInfo}, now(3), now(3))
    </insert>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceProjectInfo">
        UPDATE
        <include refid="sql_table"/>
        <set>
            <if test="projectId != null and projectId != ''">
                project_id = #{projectId},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            <if test="sceneType != null">
                scene_type = #{sceneType},
            </if>
            <if test="projectInfo != null and projectInfo != ''">
                project_info = #{projectInfo},
            </if>
            gmt_modify = now(3)
            where device_sn = #{deviceSn}
        </set>
    </update>

    <select id="query" resultMap="baseResultMap">
        select <include refid="sql_columns"/>
        from <include refid="sql_table"/>
        where device_sn = #{deviceSn}
    </select>

    <select id="findDeviceProjectInfo" parameterType="com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest" resultMap="baseResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        <where>
            <if test="deviceSnList != null and deviceSnList.size() > 0">
                device_sn in
                <foreach collection="deviceSnList" index="index" item="deviceSn" open="(" separator="," close=")">
                    #{deviceSn}
                </foreach>
            </if>
            <if test="projectId != null and projectId!=''">
                and project_id = #{projectId}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="sceneType != null">
                and scene_type = #{sceneType}
            </if>
        </where>
        order by gmt_create desc
    </select>
</mapper>