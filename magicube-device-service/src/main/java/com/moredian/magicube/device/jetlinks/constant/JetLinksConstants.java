package com.moredian.magicube.device.jetlinks.constant;

/**
 * <AUTHOR>
 * @description：jetlinks 平台相关常量
 * @date ：2024/08/06 17:54
 */
public class JetLinksConstants {

    /**
     * 设备节点固定消息类型
     */
    public static final String ARRANGE_DEVICE_NODE_FIXED = "fixed";

    /**
     * 设备节点上一节点类型
     */
    public static final String ARRANGE_DEVICE_NODE_PRE = "pre-node";

    /**
     * 设备状态过滤。过滤掉离线的设备
     */
    public static final String ARRANGE_DEVICE_NODE_IGNORE_OFFLINE = "ignoreOffline";

    /**
     * 设备状态过滤。直接发送
     */
    public static final String ARRANGE_DEVICE_NODE_ALL = "all";
}
