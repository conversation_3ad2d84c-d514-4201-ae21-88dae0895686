package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.DeviceStateRequest;
import com.moredian.iothub.control.api.v1.response.DeviceStateResponse;
import com.moredian.magicube.common.enums.TransferEventType;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.DeviceReportInfoDTO;
import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceElectricDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceInfoDTO;
import com.moredian.magicube.device.dto.device.TransferDTO;
import com.moredian.magicube.device.dto.device.TransferMessageDTO;
import com.moredian.magicube.device.dto.device.WifiInfoDTO;
import com.moredian.magicube.device.dto.network.InsertDeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import com.moredian.magicube.device.dto.version.ChangeLatchDeviceVersionDto;
import com.moredian.magicube.device.manager.DeviceIotManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceReportManager;
import com.moredian.magicube.device.service.DeviceNetworkInfoService;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceVersionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/1/24
 */
@Component
@Slf4j
public class DeviceReportManagerImpl implements DeviceReportManager {

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @SI
    private DeviceNetworkInfoService deviceNetworkInfoService;

    @SI
    private DeviceVersionService deviceVersionService;

    @SI
    private DeviceService deviceService;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceIotManager deviceIotManager;

    @Override
    public Boolean notifyDeviceReportInfo(DeviceReportInfoDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getDeviceId(), "deviceId must not be null");
        Device device = deviceManager.getByOrgIdAndId(dto.getOrgId(), dto.getDeviceId());
        BizAssert.notNull(device, "device not exist");
        DeviceStateRequest deviceStateRequest = new DeviceStateRequest();
        deviceStateRequest.setSerialNumber(device.getDeviceSn());
        DeviceStateResponse deviceStateResponse = hubControlServiceV1.getDeviceState(deviceStateRequest).pickDataThrowException();
        BizAssert.isTrue(deviceStateResponse != null && deviceStateResponse.getOnline(), "device is offline");
        TransferMessageDTO<String> transferMessageDTO = new TransferMessageDTO<>();
        transferMessageDTO.setEventType(TransferEventType.REPORT_LATCH_DEVICE_INFO.getValue());
        transferMessageDTO.setSeverity(5);
        transferMessageDTO.setSeqId(UUID.randomUUID().toString());
        transferMessageDTO.setMessage("report info");
        String jsonStr = JsonUtils.toJson(transferMessageDTO);
        String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
        TransferDTO transferDTO = new TransferDTO();
        transferDTO.setBody(base64Message);
        transferDTO.setSerialNumber(device.getDeviceSn());
        DeviceStatusDTO result = deviceIotManager.transfer(transferDTO);
        if (result != null && StringUtils.isNotBlank(result.getSerialNumber())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean reportDeviceInfo(ReportDeviceInfoDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getDeviceId(), "deviceId must not be null");
        BizAssert.notBlank(dto.getDeviceSn(), "deviceSn must not be null");
        BizAssert.notNull(dto.getLocalTime(), "localTime must not be null");

        Device device = deviceManager.getByOrgIdAndId(dto.getOrgId(), dto.getDeviceId());
        BizAssert.notNull(device, "device not exist");

        if (dto.getElectric() != null) {
            ReportDeviceElectricDTO reportDeviceElectricDTO = buildDeviceElectricDTO(dto);
            deviceService.reportDeviceElectric(reportDeviceElectricDTO).pickDataThrowException();
        }
        InsertDeviceNetworkInfoDTO insertDeviceNetworkInfoDTO = buildDeviceNetworkInfoDTO(dto);
        deviceNetworkInfoService.upload(insertDeviceNetworkInfoDTO).pickDataThrowException();

        ChangeLatchDeviceVersionDto changeLatchDeviceVersionDto = buildLatchDeviceVersionDto(dto);
        deviceVersionService.changeLatchDeviceVersion(changeLatchDeviceVersionDto).pickDataThrowException();

        deviceManager.updateBluetoothMac(dto.getBluetoothMac(), device.getDeviceId());
        return Boolean.TRUE;
    }

    private ChangeLatchDeviceVersionDto buildLatchDeviceVersionDto(ReportDeviceInfoDTO dto) {
        ChangeLatchDeviceVersionDto changeLatchDeviceVersionDto = new ChangeLatchDeviceVersionDto();
        changeLatchDeviceVersionDto.setLockVersion(dto.getLockVersion());
        changeLatchDeviceVersionDto.setModelVersion(dto.getModelVersion());
        changeLatchDeviceVersionDto.setLockBoard805Version(dto.getLockBoard805Version());
        changeLatchDeviceVersionDto.setLockBoard804Version(dto.getLockBoard804Version());
        changeLatchDeviceVersionDto.setLockBoard3861LVersion(dto.getLockBoard3861LVersion());
        changeLatchDeviceVersionDto.setAppType(dto.getAppType());
        changeLatchDeviceVersionDto.setDeviceSn(dto.getDeviceSn());
        changeLatchDeviceVersionDto.setDeviceId(dto.getDeviceId());
        changeLatchDeviceVersionDto.setOrgId(dto.getOrgId());
        changeLatchDeviceVersionDto.setAppVersion(dto.getAppVersion());
        changeLatchDeviceVersionDto.setRomType(dto.getRomType());
        changeLatchDeviceVersionDto.setRomVersion(dto.getRomVersion());
        return changeLatchDeviceVersionDto;
    }

    private InsertDeviceNetworkInfoDTO buildDeviceNetworkInfoDTO(ReportDeviceInfoDTO dto) {
        InsertDeviceNetworkInfoDTO insertDeviceNetworkInfoDTO = new InsertDeviceNetworkInfoDTO();
        insertDeviceNetworkInfoDTO.setCableStatic(dto.getCableStatic());
        insertDeviceNetworkInfoDTO.setConnectType(dto.getConnectType());
        insertDeviceNetworkInfoDTO.setDeviceId(dto.getDeviceId());
        insertDeviceNetworkInfoDTO.setNetworkType(dto.getNetworkType());
        insertDeviceNetworkInfoDTO.setOrgId(dto.getOrgId());
        WifiInfoDTO wifiInfoDTO = dto.getWifiInfo();
        if (wifiInfoDTO != null) {
            WifiInfo wifiInfo = new WifiInfo();
            wifiInfo.setIp(wifiInfoDTO.getIp());
            wifiInfo.setName(wifiInfoDTO.getName());
            wifiInfo.setPassword(wifiInfoDTO.getPassword());
            insertDeviceNetworkInfoDTO.setWifiInfo(wifiInfo);
        }
        insertDeviceNetworkInfoDTO.setDeviceResetNetworkStatus(dto.getDeviceResetNetworkStatus());
        insertDeviceNetworkInfoDTO.setWifiMac(dto.getWifiMac());
        insertDeviceNetworkInfoDTO.setWiredMac(dto.getWiredMac());
        return insertDeviceNetworkInfoDTO;
    }

    private ReportDeviceElectricDTO buildDeviceElectricDTO(ReportDeviceInfoDTO dto) {
        ReportDeviceElectricDTO deviceElectricDTO = new ReportDeviceElectricDTO();
        deviceElectricDTO.setDeviceId(dto.getDeviceId());
        deviceElectricDTO.setElectric(dto.getElectric());
        deviceElectricDTO.setDeviceSn(dto.getDeviceSn());
        return deviceElectricDTO;
    }
}
