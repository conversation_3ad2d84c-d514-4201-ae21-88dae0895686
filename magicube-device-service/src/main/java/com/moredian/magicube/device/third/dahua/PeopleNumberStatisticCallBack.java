package com.moredian.magicube.device.third.dahua;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.carme.InsertPeopleNumStatisticDTO;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.service.PeopleNumberStatisticService;
import com.moredian.magicube.device.service.dahua.CameraDeviceService;
import com.netsdk.lib.NetSDKLib;
import com.sun.jna.Pointer;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 区域内人数统计回调
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PeopleNumberStatisticCallBack implements NetSDKLib.fVideoStatSumCallBack {

    private static PeopleNumberStatisticService peopleNumberStatisticService;

    private static CameraDeviceService cameraDeviceService;

    @Autowired
    public void setPeopleNumberStatisticService(PeopleNumberStatisticService peopleNumberStatisticService) {
        PeopleNumberStatisticCallBack.peopleNumberStatisticService = peopleNumberStatisticService;
    }

    @Autowired
    public void setCameraDeviceService(CameraDeviceService cameraDeviceService) {
        PeopleNumberStatisticCallBack.cameraDeviceService = cameraDeviceService;
    }

    public Map<Integer, String> deviceMap = new HashMap<>();

    private static PeopleNumberStatisticCallBack instance = new PeopleNumberStatisticCallBack();

    public static PeopleNumberStatisticCallBack getInstance() {
        return instance;
    }

    @Override
    public void invoke(NetSDKLib.LLong lAttachHandle, NetSDKLib.NET_VIDEOSTAT_SUMMARY stVideoState,
        int dwBufLen, Pointer dwUser) {
        SummaryInfo summaryInfo = new SummaryInfo(
            stVideoState.nChannelID, stVideoState.stuTime.toStringTime(),
            stVideoState.stuEnteredSubtotal.nToday,
            stVideoState.stuEnteredSubtotal.nHour,
            stVideoState.stuEnteredSubtotal.nTotal,
            stVideoState.stuExitedSubtotal.nToday,
            stVideoState.stuExitedSubtotal.nHour,
            stVideoState.stuExitedSubtotal.nTotal,
            stVideoState.nInsidePeopleNum,
            stVideoState.emRuleType);
        log.info("收到人员统计回调SummaryInfo:{}", summaryInfo);
        String deviceInfo = deviceMap.get(lAttachHandle.intValue());
        if (StringUtils.isBlank(deviceInfo)) {
            log.info("未匹配到设备summaryInfo:{}", summaryInfo);
            return;
        }
        String[] arr = deviceInfo.split(":");
        String deviceIp = arr[0];
        int devicePort = Integer.parseInt(arr[1]);
        CameraDeviceInfoDTO cameraDeviceInfoDTO = cameraDeviceService
            .getByIpAndPort(deviceIp, devicePort).pickDataThrowException();
        if (cameraDeviceInfoDTO == null) {
            log.info("大华摄像头信息不存在deviceInfo:{}", deviceInfo);
            return;
        }

        //保存推送数据
        InsertPeopleNumStatisticDTO peopleNumberStatistic = new InsertPeopleNumStatisticDTO();
        peopleNumberStatistic.setOrgId(cameraDeviceInfoDTO.getOrgId());
        peopleNumberStatistic.setDeviceSn(cameraDeviceInfoDTO.getDeviceSn());
        peopleNumberStatistic.setInsidePeopleNum(summaryInfo.getNInsidePeopleNum());
        peopleNumberStatistic.setDeviceId(cameraDeviceInfoDTO.getCameraDeviceId());
        peopleNumberStatisticService.insert(peopleNumberStatistic);

    }

    public Map<Integer, String> getDeviceMap() {
        return deviceMap;
    }
}

