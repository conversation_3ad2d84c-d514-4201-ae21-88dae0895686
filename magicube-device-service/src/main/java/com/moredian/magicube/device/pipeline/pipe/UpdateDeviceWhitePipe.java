package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 更新设备白名单表状态管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UpdateDeviceWhitePipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private WhiteDeviceManager whiteDeviceManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        InventoryDevice inventoryDevice = context.getInventoryDevice();
        inventoryDevice.setOrgId(context.getOrgId());
        inventoryDevice.setSerialNumber(context.getDevice().getDeviceSn());
        inventoryDevice.setThirdDeviceId(dto.getThirdDeviceId());
        inventoryDevice.setActivityStatus(YesNoFlag.YES.getValue());
        whiteDeviceManager.update(inventoryDevice);
    }
}