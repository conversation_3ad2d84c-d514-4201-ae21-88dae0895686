package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceNetworkInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备网络信息Mapper
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Mapper
public interface DeviceNetworkInfoMapper {

    /**
     * 新增设备网络信息
     *
     * @param networkInfo 设备网络信息
     * @return
     */
    int insert(DeviceNetworkInfo networkInfo);

    /**
     * 更新设备网络信息
     *
     * @param networkInfo 更新设备信息
     * @return
     */
    int update(DeviceNetworkInfo networkInfo);

    /**
     * 根据机构号和设备Id获取网络信息
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    DeviceNetworkInfo getByOrgIdAndDeviceId(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId);
}
