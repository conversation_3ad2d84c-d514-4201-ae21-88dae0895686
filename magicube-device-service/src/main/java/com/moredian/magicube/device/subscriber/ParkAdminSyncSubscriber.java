package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.model.msg.CardFullSyncMsg;
import com.moredian.magicube.common.model.msg.CardWaitDeleteBySnMsg;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.park.message.DeviceAdminSyncParkMsg;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 卡同步适用于设备的主管理员同步
 *
 * <AUTHOR>
 * @date 2024/10/28 23:27
 */
@Slf4j
@Component
public class ParkAdminSyncSubscriber {

    @Resource
    private DeviceManager deviceManager;

    @Resource
    private SendMQTTBase sendMQTTBase;


    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;


    /**
     * 借用卡片的全量同步消息，目前仅园区会发
     *
     * @param msg 消息
     */
    @Subscribe
    public void subDeviceAdminSync(CardFullSyncMsg msg) {
        log.info("同步园区进入设备管理员-借用-收到指定设备全量同步卡消息: {}",
            JsonUtils.toJson(msg));
        if (msg == null || StringUtil.isBlank(msg.getDeviceSN())) {
            return;
        }
        parkDeviceAdminSync(msg.getDeviceSN());
    }


    /**
     * 借用园区中租户不租了，删除设备中的卡片的消息，仅园区会发
     *
     * @param msg 消息
     */
    @Subscribe
    public void subDeviceAdminSync(CardWaitDeleteBySnMsg msg) {
        log.info("同步园区进入设备管理员-借用-收到待删除的卡片消息: {}",
            JsonUtils.toJson(msg));
        if (msg == null || StringUtil.isBlank(msg.getDeviceSn())) {
            return;
        }
        parkDeviceAdminSync(msg.getDeviceSn());
    }


    /**
     * 当机构中无卡的时候会发送下面的消息，适配园区场景
     *
     * @param msg 消息
     */
    @Subscribe
    public void subDeviceAdminSync(DeviceAdminSyncParkMsg msg) {
        log.info("同步园区进入设备管理员-收到园区版设备管理员需要变更消息: {}",
            JsonUtils.toJson(msg));
        if (msg == null || CollectionUtils.isEmpty(msg.getDeviceSn())) {
            return;
        }
        for (String deviceSn : msg.getDeviceSn()) {
            parkDeviceAdminSync(deviceSn);
        }
    }

    private void parkDeviceAdminSync(String deviceSn) {
        Device device = deviceManager.getByDeviceSn(deviceSn);
        if (device != null) {
            try {
                // 1、业务
                String appointMethod = "";
                List<Integer> enableType = deviceTypePropertyManager.getDeviceTypeByPropertyKey(
                    DeviceTypeConstants.SUPPORT_AUTH_DEVICE_MANAGEMENT_LIST);
                if (!enableType.contains(device.getDeviceType())) {
                    log.info("不支持菜单配置进入设备后台，deviceSN={}",
                        device.getDeviceSn());
                    // 如果不支持新的同步进入设备后台，就直接老方式
                    appointMethod = "old";
                }

                // 2、执行
                sendMQTTBase.doSwitchAdminSyncSendMQTT(device, "ParkAdminSyncSubscriber",
                    com.moredian.magicube.device.subscriber.TransferEventType.DEVICE_BACKGROUND_AUTH_ALL_SYNC,
                    appointMethod);
            } catch (Exception e) {
                log.error(
                    "Notify device park device admin sync failed. [deviceSn={},deviceId={}]，errorMsg={}",
                    device.getDeviceSn(), device.getDeviceId(), e.getMessage());
            }
        }
    }
}
