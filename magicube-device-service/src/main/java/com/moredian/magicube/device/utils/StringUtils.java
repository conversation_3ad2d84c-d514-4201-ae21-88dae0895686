package com.moredian.magicube.device.utils;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.moredian.bee.common.utils.StringUtil;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description：公共字符串工具类
 * @date ：2024/07/20 14:02
 */
@Slf4j
public class StringUtils extends StringUtil {

    public static final String COMMA = ",";

    public static final String CROSS_LINE = "-";


    /**
     * 生成uuid，去掉短横线
     */
    public static String uuidStr(){
        return UUID.fastUUID().toString().replace(CROSS_LINE, EMPTY_STRING);
    }


    /**
     * 从字符串中，通过正则串解析出特殊变量
     */
    public static Map<String, String> getVariableOfRegex(String str, String regx){
        Map<String, String> result = Maps.newHashMap();
        if(ObjectUtil.isEmpty(str)) {
            return result;
        }
        Pattern pattern = Pattern.compile(regx);
        Matcher matcher = pattern.matcher(str);

        while (matcher.find()) {
            result.put(matcher.group(0), matcher.group(1));
        }
        return result;
    }


    /**
     * 将一个json字符串转换成一个带有转义符号的json字符串
     */
    public static String getSpecialJsonStr(Object object) {
        if (ObjectUtil.isEmpty(object)) {
            return EMPTY_STRING;
        }

        ObjectMapper mapper = new ObjectMapper();
        try {
            String s = mapper.writeValueAsString(mapper.writeValueAsString(object));
            // 去掉首位的引号
            s = s.substring(1, s.length() - 1);
            return s;
        }catch (JsonProcessingException e) {
            log.error("StringUtil#getSpecialJsonStr，msg=>{}", e.getMessage());
            throw new RuntimeException("parse json error");
        }
    }
}
