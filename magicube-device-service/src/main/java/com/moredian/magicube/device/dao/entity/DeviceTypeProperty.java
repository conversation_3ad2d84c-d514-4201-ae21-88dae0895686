package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备类型属性表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_type_property")
public class DeviceTypeProperty extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 设备类型属性Id
     */
    private Long id;

    /**
     * 属性key
     */
    private String propertyKey;

    /**
     * 属性值
     */
    private String propertyValue;

    /**
     * 属性描述
     */
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
}
