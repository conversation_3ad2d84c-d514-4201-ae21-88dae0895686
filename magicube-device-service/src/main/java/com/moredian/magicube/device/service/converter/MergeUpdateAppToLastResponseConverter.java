package com.moredian.magicube.device.service.converter;

import com.moredian.iothub.control.api.v1.request.MergeUpdateRequest;
import com.moredian.iothub.control.api.v1.request.UpgradeRequest;
import com.moredian.iothub.control.api.v1.request.UpgradeRomRequest;
import com.moredian.magicube.device.dto.upgrade.MergeUpdateAppToLastResponse;

public class MergeUpdateAppToLastResponseConverter {
    public static MergeUpdateAppToLastResponse convert(MergeUpdateRequest source) {
        if (source == null) {
            return null;
        }
        MergeUpdateAppToLastResponse target = new MergeUpdateAppToLastResponse();
        target.setDeviceSn(source.getSerialNumber());
        target.setDeviceId(source.getDeviceId());
        target.setRomUpdateInfo(convert(source.getRomUpdateInfo()));
        target.setAppUpdateInfo(convert(source.getAppUpdateInfo()));
        return target;
    }

    private static MergeUpdateAppToLastResponse.RomUpdateInfo convert(UpgradeRomRequest source) {
        if (source == null) {
            return null;
        }

        MergeUpdateAppToLastResponse.RomUpdateInfo target = new MergeUpdateAppToLastResponse.RomUpdateInfo();
        target.setSerialNumber(source.getSerialNumber());
        target.setDeviceId(source.getDeviceId());
        target.setAppType(source.getAppType());
        target.setPreVersionCode(source.getPreVersionCode());
        target.setVersionCode(source.getVersionCode());
        target.setIsIncre(source.getIsIncre());
        target.setUrl(source.getUrl());
        return target;
    }

    private static MergeUpdateAppToLastResponse.AppUpdateInfo convert(UpgradeRequest source) {
        if (source == null) {
            return null;
        }

        MergeUpdateAppToLastResponse.AppUpdateInfo target = new MergeUpdateAppToLastResponse.AppUpdateInfo();
        target.setSerialNumber(source.getSerialNumber());
        target.setDeviceId(source.getDeviceId());
        target.setForce(source.getForce());
        target.setAppType(source.getAppType());
        target.setVersionCode(source.getVersionCode());
        target.setApkUrl(source.getApkUrl());
        target.setUpgradeEventType(source.getUpgradeEventType());
        return target;
    }
}
