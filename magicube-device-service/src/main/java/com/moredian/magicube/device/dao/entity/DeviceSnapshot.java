package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备快照表表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_snapshot")
public class DeviceSnapshot extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 同步时间戳
     */
    private Long timestamp;
}
