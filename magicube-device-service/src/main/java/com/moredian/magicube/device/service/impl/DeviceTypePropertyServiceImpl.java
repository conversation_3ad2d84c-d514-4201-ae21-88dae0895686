package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.DeviceTypeProperty;
import com.moredian.magicube.device.dto.type.DeviceTypeDTO;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDetailDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.UpdateDeviceTypePropertyDTO;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.service.DeviceTypePropertyRelationService;
import com.moredian.magicube.device.service.DeviceTypePropertyService;
import com.moredian.magicube.device.service.DeviceTypeService;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class DeviceTypePropertyServiceImpl implements DeviceTypePropertyService {

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private DeviceTypePropertyRelationService deviceTypePropertyRelationService;

    @SI
    private DeviceTypeService deviceTypeService;

    @Override
    public ServiceResponse<Long> insert(InsertDeviceTypePropertyDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceTypeProperty deviceTypeProperty = new DeviceTypeProperty();
        BeanUtils.copyProperties(dto, deviceTypeProperty);
        serviceResponse.setData(deviceTypePropertyManager.insert(deviceTypeProperty));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Long> update(UpdateDeviceTypePropertyDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        DeviceTypeProperty deviceTypeProperty = new DeviceTypeProperty();
        BeanUtils.copyProperties(dto, deviceTypeProperty);
        serviceResponse.setData(deviceTypePropertyManager.update(deviceTypeProperty));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceTypePropertyDetailDTO> getById(Long id) {
        ServiceResponse<DeviceTypePropertyDetailDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        DeviceTypeProperty deviceTypeProperty = deviceTypePropertyManager.getById(id);
        DeviceTypePropertyDetailDTO propertyDetailDTO = getDeviceTypes(deviceTypeProperty);
        serviceResponse.setData(propertyDetailDTO);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceTypePropertyDetailDTO> getByPropertyKey(String propertyKey) {
        ServiceResponse<DeviceTypePropertyDetailDTO> serviceResponse = ServiceResponse
            .createSuccessResponse();
        DeviceTypeProperty deviceTypeProperty = deviceTypePropertyManager
            .getByPropertyKey(propertyKey);
        DeviceTypePropertyDetailDTO propertyDetailDTO = getDeviceTypes(deviceTypeProperty);
        serviceResponse.setData(propertyDetailDTO);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceTypePropertyDTO>> list() {
        ServiceResponse<List<DeviceTypePropertyDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceTypeProperty> deviceTypeProperties = deviceTypePropertyManager.list();
        List<DeviceTypePropertyDTO> propertyDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceTypeProperties)) {
            for (DeviceTypeProperty deviceTypeProperty : deviceTypeProperties) {
                DeviceTypePropertyDTO propertyDTO = new DeviceTypePropertyDTO();
                BeanUtils.copyProperties(deviceTypeProperty, propertyDTO);
                propertyDTOS.add(propertyDTO);
            }
        }
        serviceResponse.setData(propertyDTOS);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceTypePropertyDTO>> listByIds(List<Long> ids) {
        ServiceResponse<List<DeviceTypePropertyDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<DeviceTypeProperty> deviceTypeProperties = deviceTypePropertyManager.listByIds(ids);
        List<DeviceTypePropertyDTO> propertyDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceTypeProperties)) {
            for (DeviceTypeProperty deviceTypeProperty : deviceTypeProperties) {
                DeviceTypePropertyDTO propertyDTO = new DeviceTypePropertyDTO();
                BeanUtils.copyProperties(deviceTypeProperty, propertyDTO);
                propertyDTOS.add(propertyDTO);
            }
        }
        serviceResponse.setData(propertyDTOS);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Integer>> getDeviceTypeByPropertyKey(String propertyKey) {
        return new ServiceResponse<>(deviceTypePropertyManager.getDeviceTypeByPropertyKey(propertyKey));
    }

    @Override
    public ServiceResponse<Boolean> containsDeviceType(String propertyKey, Integer deviceType) {
        return new ServiceResponse<>(deviceTypePropertyManager.containsDeviceType(propertyKey, deviceType));
    }

    /**
     * 获取设备类型列表
     *
     * @param deviceTypeProperty
     * @return
     */
    private DeviceTypePropertyDetailDTO getDeviceTypes(DeviceTypeProperty deviceTypeProperty) {
        DeviceTypePropertyDetailDTO propertyDetailDTO = new DeviceTypePropertyDetailDTO();
        if (deviceTypeProperty != null) {
            BeanUtils.copyProperties(deviceTypeProperty, propertyDetailDTO);
            List<Integer> deviceTypes = deviceTypePropertyRelationService
                .listDeviceTypeByPropertyId(deviceTypeProperty.getId()).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceTypes)) {
                List<DeviceTypeDTO> deviceTypeDTOS = deviceTypeService.listByTypes(deviceTypes)
                    .pickDataThrowException();
                propertyDetailDTO.setDeviceTypeDTOS(deviceTypeDTOS);
            }
        }
        return propertyDetailDTO;
    }
}
