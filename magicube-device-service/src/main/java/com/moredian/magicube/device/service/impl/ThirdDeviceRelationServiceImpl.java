package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.ThirdDeviceRelation;
import com.moredian.magicube.device.dto.device.ThirdDeviceRelationDTO;
import com.moredian.magicube.device.manager.ThirdDeviceRelationManage;
import com.moredian.magicube.device.service.ThirdDeviceRelationService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@SI
public class ThirdDeviceRelationServiceImpl implements ThirdDeviceRelationService {

    @Autowired
    private ThirdDeviceRelationManage thirdDeviceRelationManage;

    @Override
    public ServiceResponse<ThirdDeviceRelationDTO> getByTpId(String tpId, Integer tpType) {
        ServiceResponse<ThirdDeviceRelationDTO> serviceResponse = ServiceResponse.createSuccessResponse();

        ThirdDeviceRelation thirdDevice = thirdDeviceRelationManage.getByTpId(tpId, tpType);
        if (thirdDevice == null) {
            return serviceResponse;
        }
        ThirdDeviceRelationDTO dto = new ThirdDeviceRelationDTO();
        BeanUtils.copyProperties(thirdDevice, dto);
        serviceResponse.setData(dto);

        return serviceResponse;
    }
}
