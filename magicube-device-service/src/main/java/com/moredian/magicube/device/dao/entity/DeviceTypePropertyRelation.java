package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备类型和属性关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_type_property_relation")
public class DeviceTypePropertyRelation extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 设备类型和属性关系Id
     */
    private Long id;

    /**
     * 设备类型属性Id
     */
    private Long deviceTypePropertyId;

    /**
     * 设备类型
     */
    private Integer deviceType;


}
