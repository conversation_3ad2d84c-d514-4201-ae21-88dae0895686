<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.RuleTemplateMapper">

    <resultMap id="baseMap" type="com.moredian.magicube.device.dao.entity.RuleTemplate">
        <result property="ruleTemplateId" column="rule_template_id"/>
        <result property="templateName" column="template_name"/>
        <result property="modeType" column="mode_type"/>
        <result property="description" column="description"/>
        <result property="triggerType" column="trigger_type"/>
        <result property="triggerValue" column="trigger_value"/>
        <result property="triggerDeviceTypeProperty" column="trigger_device_type_property"/>
        <result property="implementDeviceTypeProperty" column="implement_device_type_property"/>
        <result property="ruleJson" column="rule_json"/>
        <result property="actionJson" column="action_json"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
    </resultMap>

    <sql id="sql_table">
        hive_rule_template
    </sql>

    <sql id="sql_columns">
        rule_template_id,
        template_name,
        mode_type,
        description,
        trigger_type,
        trigger_value,
        trigger_device_type_property,
        implement_device_type_property,
        gmt_create,
        gmt_modify
    </sql>

    <select id="getDefaultTemplateList" resultMap="baseMap">
        select  <include refid="sql_columns"/>
        from <include refid="sql_table"/>
    </select>

    <select id="listByModeType" resultMap="baseMap">
        select  <include refid="sql_columns"/>
        from <include refid="sql_table"/>
        where mode_type = ${modeType}
    </select>

    <select id="getTemplate" resultMap="baseMap">
        select rule_template_id,
        template_name,
        mode_type,
        description,
        trigger_type,
        trigger_value,
        trigger_device_type_property,
        implement_device_type_property,
        rule_json,
        action_json
        from <include refid="sql_table"/>
        where rule_template_id = #{templateId}
    </select>
</mapper>