package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_third_device_relation")
public class ThirdDeviceRelation extends TimedEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 三方设备id
     */
    private String tpId;

    /**
     * 三方设备类型
     */
    private Integer tpType;

    /**
     * 设备SN
     */
    private String deviceSn;


}
