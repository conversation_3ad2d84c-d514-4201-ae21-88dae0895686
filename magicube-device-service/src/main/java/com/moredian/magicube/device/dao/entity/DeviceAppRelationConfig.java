package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_app_relation_config")
public class DeviceAppRelationConfig extends TimedEntity {

    private static final long serialVersionUID = -5552570931481596943L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 空间类型
     */
    private Integer spaceType;

    /**
     * 类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 默认关联应用
     */
    private String defaultAppCode;

    /**
     * 可承载应用列表
     */
    private String availableAppCodeList;

}
