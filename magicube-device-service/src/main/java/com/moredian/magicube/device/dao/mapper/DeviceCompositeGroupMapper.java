package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceCompositeGroup;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 通行设备组mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceCompositeGroupMapper {

    /**
     * 新增
     *
     * @param deviceCompositeGroup 实体
     * @return 数量
     */
    int insert(DeviceCompositeGroup deviceCompositeGroup);

    /**
     * 批量插入
     *
     * @param itemList 实体
     * @return 数量
     */
    void batchInsert(@Param("itemList") List<DeviceCompositeGroup> itemList);

    /**
     * 批量删除
     *
     * @param orgId                      机构Id
     * @param deviceCompositeGroupIdList 关系ID列表
     * @return 数量
     */
    int deleteByIds(@Param("orgId") Long orgId, @Param("deviceCompositeGroupIdList") List<Long> deviceCompositeGroupIdList);

    /**
     * 查询通信组下设备组列表
     *
     * @param orgId   机构Id
     * @param groupId 通行权限组
     * @param rangeType 范围类型
     * @return 数量
     */
    List<DeviceCompositeGroup> selectByGroupId(@Param("orgId") Long orgId, @Param("groupId") Long groupId, @Param("rangeType") Integer rangeType);

    /**
     * 批量查询通信组下设备组列表
     *
     * @param orgId       机构Id
     * @param groupIdList 通行权限组
     * @param rangeType   范围类型
     * @return 数量
     */
    List<DeviceCompositeGroup> selectByGroupIdList(@Param("orgId") Long orgId, @Param("groupIdList") List<Long> groupIdList, @Param("rangeType") Integer rangeType);

    /**
     * 批量查询通信组下设备组列表
     *
     * @param orgId       机构Id
     * @param deviceIdList 设备ID
     * @return 数量
     */
    List<DeviceCompositeGroup> selectByDeviceIdList(@Param("orgId") Long orgId, @Param("deviceIdList") List<Long> deviceIdList);

    /**
     * 根据设备组批量查询通信
     *
     * @param orgId       机构Id
     * @param deviceCompositeIdList  设备组ID
     * @return 数量
     */
    List<DeviceCompositeGroup> selectByDeviceCompositeIdList(@Param("orgId") Long orgId, @Param("deviceCompositeIdList") List<Long> deviceCompositeIdList);

    /**
     * 查询通信组下设备组数量
     *
     * @param orgId    机构Id
     * @param groupIds 通行权限组
     * @return 数量
     */
    int countDeviceCompositeGroupSizeByGroupId(@Param("orgId") Long orgId, @Param("groupIds") List<Long> groupIds);
}
