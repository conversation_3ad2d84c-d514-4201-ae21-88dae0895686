package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备激活跳转应用配置表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_active_jump_app_config")
public class DeviceActiveJumpAppConfig extends TimedEntity {

    private static final long serialVersionUID = -0L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 设备跳转应用code
     */
    private String appCode;
    /**
     * 跳转链接
     */
    private String jumpLink;

}