package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceTypeProperty;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备类型属性表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-30
 */

@Mapper
public interface DeviceTypePropertyMapper {

    /**
     * 新增设备类型属性
     *
     * @param deviceTypeProperty 设备类型
     * @return
     */
    int insert(DeviceTypeProperty deviceTypeProperty);

    /**
     * 编辑设备类型属性
     *
     * @param deviceTypeProperty 设备类型信息
     * @return
     */
    int update(DeviceTypeProperty deviceTypeProperty);

    /**
     * 根据Id查询设备类型属性
     *
     * @param id 设备类型Id
     * @return
     */
    DeviceTypeProperty getById(@Param("id") Long id);

    /**
     * 根据Id查询设备类型信息
     *
     * @param propertyKey 属性key
     * @return
     */
    DeviceTypeProperty getByKey(@Param("propertyKey") String propertyKey);

    /**
     * 查询所有设备类型属性列表
     *
     * @return
     */
    List<DeviceTypeProperty> list();

    /**
     * 根据主键Id查询设备类型属性列表
     *
     * @param ids 设备类型属性Id列表
     * @return
     */
    List<DeviceTypeProperty> listByIds(@Param("ids") List<Long> ids);
}
