package com.moredian.magicube.device.manager.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.DeviceStateRequest;
import com.moredian.iothub.control.api.v1.response.DeviceStateResponse;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.mapper.DeviceMapper;
import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.manager.DeviceIotManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.google.common.collect.Sets.newHashSet;
import static org.apache.commons.collections.CollectionUtils.intersection;

/**
 * <AUTHOR>
 * @Description
 * @create 2024-12-28 18:03
 */
@Component
public class DeviceManagerHelper {

    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private DeviceIotManager deviceIotManager;

    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;


    /**
     * 查询对应空间下的设备id
     * @param treeIds 空间Id，不能为空
     * @param subTreeFlag 是否查询子空间下的设备
     */
    public List<Long> listDeviceIdsByTreeIds(Long orgId, List<Long> treeIds, Boolean subTreeFlag) {
        BizAssert.isTrue(CollectionUtil.isNotEmpty(treeIds), "treeIds must be not empty");
        BizAssert.notNull(subTreeFlag, "subTreeFlag must be not null");
        BizAssert.notNull(orgId, "orgId must be not null");

        List<TreeDeviceRelationDTO> relationDTOS;
        if (subTreeFlag) {
            relationDTOS = spaceTreeDeviceRelationService.listAllByTreeIdsAndSource(orgId, treeIds, 1)
                    .pickDataThrowException();
        } else {
            relationDTOS = spaceTreeDeviceRelationService.listByTreeIdsAndSource(orgId, treeIds, 1)
                    .pickDataThrowException();
        }
        // 没有关联设备直接返回
        if (CollectionUtils.isEmpty(relationDTOS)) {
            return Collections.emptyList();
        }
        return relationDTOS.stream()
                .map(TreeDeviceRelationDTO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 查询机构下对应状态的的设备id
     * @param orgId   机构号，不能为空
     * @param status 设备在线状态
     */
    public List<Long> listDeviceIdByOrgIdsAndStatus(Long orgId, Boolean status, List<Integer> deviceType) {
        BizAssert.notNull(status, "status must be not null");

        List<Long> deviceIds = new ArrayList<>();
        List<Device> devices = deviceMapper.listByOrgIdAndTypes(orgId, deviceType);
        if (CollectionUtil.isNotEmpty(devices)) {
            List<String> deviceSns = devices.stream().map(Device::getDeviceSn).distinct().collect(Collectors.toList());
            // 查询设备状态
            List<DeviceStatusDTO> deviceStatus = deviceIotManager.listDeviceStateByDeviceSns(deviceSns);
            if (CollectionUtil.isNotEmpty(deviceStatus)) {
                // 满足条件的设备sn
                List<String> conditionDeviceSns = deviceStatus.stream().filter(e -> status.equals(e.getOnline()))
                        .map(DeviceStatusDTO::getSerialNumber).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(conditionDeviceSns)) {
                    List<Device> deviceList = deviceMapper.listByDeviceSns(conditionDeviceSns);
                    if (CollectionUtil.isNotEmpty(deviceList)) {
                        deviceIds = deviceList.stream().map(Device::getDeviceId)
                                .collect(Collectors.toList());
                    }
                }
            }
        }
        return deviceIds;
    }
}
