package com.moredian.magicube.device.manager;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.white.BatchInsertWhiteListDeviceRequest;
import com.moredian.magicube.device.dto.white.GetWhiteDeviceListRequest;
import com.moredian.magicube.device.dto.white.QueryWhiteDeviceDTO;
import com.moredian.magicube.device.dto.white.SimpleWhiteDeviceResponse;
import com.moredian.magicube.device.dto.white.UpdateWhiteDeviceDTO;

import java.util.List;

/**
 * 设备白名单
 *
 * <AUTHOR>
 */
public interface WhiteDeviceManager {

    /**
     * 新增白名单
     *
     * @param inventoryDevice 设备白名单信息
     * @return
     */
    Boolean insert(InventoryDevice inventoryDevice);

    /**
     * 批量新增设备白名单
     *
     * @param inventoryDevices 设备白名单信息列表
     * @return
     */
    Boolean batchInsert(List<InventoryDevice> inventoryDevices);

    /**
     * 根据第三方Id查询白名单设备信息
     *
     * @param orgId         机构号
     * @param thirdDeviceId 第三方Id
     * @return
     */
    InventoryDevice getByOrgIdAndThirdDeviceId(Long orgId, String thirdDeviceId);

    /**
     * 根据第设备sn和激活码查询白名单设备信息
     *
     * @param deviceSn       设备sn
     * @param activationCode 激活码
     * @return
     */
    InventoryDevice getByDeviceSnAndActivationCode(String deviceSn, String activationCode);

    /**
     * 分页查询设备白名单信息
     *
     * @param dto 分页条件
     * @return
     */
    Pagination<InventoryDevice> listPage(QueryWhiteDeviceDTO dto);

    /**
     * 根据设备Sn查询白名单设备信息
     *
     * @param deviceSn 设备Sn
     * @return
     */
    InventoryDevice getByDeviceSn(String deviceSn);

    /**
     * 根据设备Sn列表查询白名单设备信息列表
     *
     * @param deviceSns 设备Sn列表
     * @return
     */
    List<InventoryDevice> listByDeviceSns(List<String> deviceSns);

    /**
     * 删除白名单
     *
     * @param deviceSn 设备sn
     * @return
     */
    Boolean delete(String deviceSn);

    /**
     * 批量更新白名单设备信息
     *
     * @param dto 更新白名单信息列表
     * @return
     */
    Boolean batchUpdate(List<UpdateWhiteDeviceDTO> dto);

    /**
     * 批量新增白单 （无检验）
     *
     * @param request
     * @return
     */
    Boolean batchInsertWithoutCheck(BatchInsertWhiteListDeviceRequest request);

    /**
     * 更新白名单设备信息
     *
     * @param inventoryDevice 更新白名单信息列表
     * @return
     */
    Boolean update(InventoryDevice inventoryDevice);

    /**
     * 无校验批量插入 - 仅用于设备sn创建流程的白单导入
     *
     * @param inventoryDevices
     * @return
     */
    Boolean batchInsertWithoutCheck(List<InventoryDevice> inventoryDevices);

    /**
     * 批量获取设备白单，版本号，激活状态
     *
     * @param request
     * @return
     */
    List<SimpleWhiteDeviceResponse> getBatchSimpleDeviceInfo(GetWhiteDeviceListRequest request);

    /**
     * 解绑设备接口
     *
     * @param inventoryDevice
     * @return
     */
    Boolean unbindDevice(InventoryDevice inventoryDevice);
}