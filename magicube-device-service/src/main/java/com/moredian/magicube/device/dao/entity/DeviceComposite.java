package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备分组
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_composite")
public class DeviceComposite extends TimedEntity {

    private static final long serialVersionUID = 25732731051597207L;

    /**
     * 设备分组Id
     */
    private Long deviceCompositeId;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 类型，使用，0-通用
     */
    private Integer bizType;

    /**
     * 组名
     */
    private String deviceCompositeName;

    /**
     * 父组 ID
     */
    private Long parentId;

    /**
     * 组 code
     */
    private String code;

    /**
     * 组code路径 包括自身code
     * /00000000/xx
     * 从虚拟根开始
     */
    private String path;

    /**
     * 0正常
     * 其他值删除
     */
    private Long status;
}
