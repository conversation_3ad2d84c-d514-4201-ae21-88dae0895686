package com.moredian.magicube.device.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.moredian.magicube.device.dao.entity.RuleTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RuleTemplateMapper extends BaseMapper<RuleTemplate> {
    List<RuleTemplate> getDefaultTemplateList();

    RuleTemplate getTemplate(Long templateId);

    List<RuleTemplate> listByModeType(@Param("modeType") Integer modeType);
}