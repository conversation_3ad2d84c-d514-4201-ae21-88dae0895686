package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.dao.entity.DeviceComposite;
import com.moredian.magicube.device.dto.composite.CompositeAndDeviceInfoDTO;
import com.moredian.magicube.device.dto.composite.CompositeDeepDeviceSizeDTO;
import com.moredian.magicube.device.dto.composite.DeleteDeviceCompositeDTO;
import com.moredian.magicube.device.dto.composite.DeviceBindCompositeDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositeAndDeviceCountDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositeDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositeItemDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositeNameListDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositePathInfoDTO;
import com.moredian.magicube.device.dto.composite.InsertDeviceCompositeDTO;
import com.moredian.magicube.device.dto.composite.QueryDeviceCompositeDTO;
import com.moredian.magicube.device.dto.composite.QueryDeviceCompositePathDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceCompositeManager;
import com.moredian.magicube.device.service.DeviceCompositeService;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class DeviceCompositeServiceImpl implements DeviceCompositeService {

    @Autowired
    private DeviceCompositeManager deviceCompositeManager;

    @Autowired
    private RedissonLockComponent redissonLockComponent;

    @Override
    public ServiceResponse<DeviceCompositeDTO> insertOrUpdate(InsertDeviceCompositeDTO dto) {
        return new ServiceResponse<>(deviceCompositeManager.insertOrUpdate(dto));
    }

    @Override
    public ServiceResponse<Boolean> addDeviceInc(DeviceBindCompositeDTO dto) {
        return new ServiceResponse<>((Boolean) doJobWithOrgLock(dto.getOrgId(),
            () -> deviceCompositeManager.addDeviceInc(dto)));
    }

    @Override
    public ServiceResponse<Long> insertOrUpdateNoMutex(InsertDeviceCompositeDTO dto) {
        return new ServiceResponse<>((Long) doJobWithOrgLock(dto.getOrgId(),
            () -> deviceCompositeManager.insertOrUpdateNoMutex(dto)));
    }

    @Override
    public ServiceResponse<List<DeviceCompositeDTO>> listByOrgIdAndBizType(Long orgId, Integer bizType) {
        return new ServiceResponse<>(deviceCompositeManager.listByOrgIdAndBizType(orgId, bizType));
    }

    @Override
    public ServiceResponse<Boolean> delete(DeleteDeviceCompositeDTO dto) {
        return new ServiceResponse<>(deviceCompositeManager.delete(dto));
    }

    @Override
    public ServiceResponse<List<Long>> listBindDeviceIdByOrgIdAndBizType(Long orgId, Integer bizType) {
        return new ServiceResponse<>(deviceCompositeManager.listBindDeviceIdByOrgIdAndBizType(orgId, bizType));
    }

    @Override
    public ServiceResponse<Boolean> deleteCompositeItemByDeviceId(Long orgId, Long deviceId, Integer bizType) {
        return new ServiceResponse<>(deviceCompositeManager.deleteCompositeItemByDeviceId(orgId, deviceId, bizType));
    }

    @Override
    public ServiceResponse<List<DeviceCompositeNameListDTO>> findDeviceCompositeNameList(Long orgId, List<Long> deviceIdList, Integer bizType, List<Long> compositeIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIdList), "deviceIdList must not be null");
        BizAssert.notNull(bizType, "bizType must not be null");
        return new ServiceResponse<>(deviceCompositeManager.findDeviceCompositeNameList(orgId, deviceIdList, bizType, compositeIds));
    }

    @Override
    public ServiceResponse<CompositeAndDeviceInfoDTO> listByCondition(QueryDeviceCompositeDTO dto) {
        return new ServiceResponse<>(deviceCompositeManager.listByCondition(dto));
    }

    @Override
    public ServiceResponse<List<DeviceCompositeDTO>> listDetailByOrgIdAndBizType(Long orgId, Integer bizType) {
        return new ServiceResponse<>(deviceCompositeManager.listByOrgIdAndBizType(orgId, bizType));
    }

    @Override
    public ServiceResponse<List<DeviceCompositeItemDTO>> listCompositeItemByCompositeId(Long orgId, Long deviceCompositeId) {
        return new ServiceResponse<>(deviceCompositeManager.listCompositeItemByCompositeId(orgId, deviceCompositeId, Boolean.FALSE));
    }

    @Override
    public ServiceResponse<DeviceCompositeDTO> getDetailByOrgIdAndId(Long orgId, Long deviceCompositeId) {
        return new ServiceResponse<>(deviceCompositeManager.getDetailByOrgIdAndId(orgId, deviceCompositeId));
    }

    @Override
    public ServiceResponse<List<DeviceCompositeDTO>> listByDeviceCompositeIds(Long orgId,
        List<Long> compositeIds, Integer bizType) {
        return new ServiceResponse<>(
            deviceCompositeManager.listByDeviceCompositeIds(orgId, compositeIds, bizType));
    }

    @Override
    public ServiceResponse<List<DeviceCompositeDTO>> getCompositeNameByCode(Long orgId,
        List<String> codeList, Integer bizType) {
        if (CollectionUtils.isEmpty(codeList)){
            return new ServiceResponse<>(Collections.emptyList());
        }
        List<DeviceCompositeDTO> compositeDtoList = deviceCompositeManager.listByDeviceCompositeCodeList(
            orgId, codeList, bizType);
        if (CollectionUtils.isEmpty(compositeDtoList)) {
            return new ServiceResponse<>(Collections.emptyList());
        }
        return new ServiceResponse<>(compositeDtoList);
    }

    @Override
    public ServiceResponse<DeviceCompositePathInfoDTO> getDeviceInCompositePathList(
        QueryDeviceCompositePathDTO dto) {
        return new ServiceResponse<>(deviceCompositeManager.getDeviceInCompositePathList(dto));
    }

    @Override
    public ServiceResponse<List<DeviceCompositeAndDeviceCountDTO>> getTreeAndDeviceByIds(Long orgId,
        Integer bizType, List<Long> compositeIds) {
        BizAssert.notNull(orgId,"orgId cannot be null");
        BizAssert.notNull(bizType,"bizType cannot be null");
        if (CollectionUtils.isEmpty(compositeIds)){
            return new ServiceResponse<>(Collections.emptyList());
        }
        return new ServiceResponse<>(
            deviceCompositeManager.getTreeAndDeviceByIds(orgId, bizType, compositeIds));
    }

    @Override
    public ServiceResponse<List<Long>> listDeviceIdByCompositeIds(Long orgId,
        List<Long> compositeIds) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceCompositeManager.listDeviceIdByCompositeIds(orgId, compositeIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Long>> listCompositeIdsByParentId(Long orgId, Long parentId) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceCompositeManager.listCompositeIdsByParentId(orgId, parentId));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Long>> findAllSubDeviceComposite(Long orgId, Long deviceCompositeId) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<DeviceComposite> deviceComposites = deviceCompositeManager.findAllSubDeviceComposite(orgId, deviceCompositeId);
        if (CollectionUtils.isNotEmpty(deviceComposites)){
            serviceResponse.setData(deviceComposites.stream()
                .map(DeviceComposite::getDeviceCompositeId).collect(Collectors.toList()));
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<CompositeDeepDeviceSizeDTO>> getCompositeDeepDeviceSize(Long orgId,
        Integer bizType, List<Long> compositeIds) {
        return new ServiceResponse<>(deviceCompositeManager.getCompositeDeepDeviceSize(orgId, compositeIds, bizType, null));
    }

    @Override
    public ServiceResponse<List<Long>> getDeviceIdsByCompositeDeepBatch(Long orgId,
        List<Long> deviceCompositeIds, Integer sceneType) {
        return new ServiceResponse<>(deviceCompositeManager.getDeviceIdsByCompositeDeepBatch(orgId, deviceCompositeIds, sceneType));
    }

    @Override
    public ServiceResponse<List<Long>> findAllSubDeviceCompositeIdBatch(Long orgId, List<Long> deviceCompositeIds) {
        ServiceResponse<List<Long>> serviceResponse  = ServiceResponse.createSuccessResponse();
        List<DeviceComposite> deviceComposites = deviceCompositeManager.
            findAllSubDeviceCompositeBatch(orgId, deviceCompositeIds);
        if (CollectionUtils.isNotEmpty(deviceComposites)) {
            List<Long> compositeIds = deviceComposites.stream().map(DeviceComposite::
                getDeviceCompositeId).distinct().collect(Collectors.toList());
            serviceResponse.setData(compositeIds);
        }
        return serviceResponse;
    }

    /**
     * 设备组变更机构锁
     *
     * @param orgId 组织 ID
     * @param sup   支持
     * @return {@link Object }
     */
    public Object doJobWithOrgLock(Long orgId, Supplier<?> sup) {
        Object result = null;
        String lockKey = null;
        try {
            lockKey = RedisKeys.getKey(RedisKeys.DEVICE_COMPOSITE_CHANGE_ORG, orgId);
            // 等待2s，任务最长时间执行60s
            boolean getSuccessLock = redissonLockComponent.acquire(lockKey, 2L, 60L);
            if (getSuccessLock) {
                result = sup.get();
            } else {
                log.info("设备组机构锁-分布式锁获取失败:orgId={}",
                    JsonUtils.toJson(orgId));
                ExceptionUtils.throwException(DeviceErrorCode.DEVICE_COMPOSITE_CHANGE_ORG,
                    DeviceErrorCode.DEVICE_COMPOSITE_CHANGE_ORG.getMessage());
            }
        } finally {
            if (StringUtils.isNotEmpty(lockKey)) {
                redissonLockComponent.release(lockKey);
            }
        }
        return result;
    }
}
