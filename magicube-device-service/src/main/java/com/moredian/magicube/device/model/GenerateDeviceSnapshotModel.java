package com.moredian.magicube.device.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/29
 */
@Getter
@Setter
@ToString
public class GenerateDeviceSnapshotModel implements Serializable {

    private static final long serialVersionUID = -9074562005233666438L;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 设备绑定的组
     */
    private List<Long> bondGroupIdList;

    /**
     * 时间戳
     */
    private Long timestamp;
}
