package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.CommonErrorCode;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;

import com.moredian.magicube.common.enums.AppSystemType;
import com.moredian.magicube.common.enums.errorcode.DeviceVersionErrorCode;
import com.moredian.magicube.device.dao.entity.DeviceVersion;
import com.moredian.magicube.device.dao.mapper.DeviceApkVersionMapper;
import com.moredian.magicube.device.dao.mapper.DeviceRomVersionMapper;
import com.moredian.magicube.device.dto.version.AppVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import com.moredian.magicube.device.dto.version.VersionDTO;
import com.moredian.magicube.device.manager.DeviceVersionPersistenceClientManager;
import com.moredian.magicube.device.service.AppVersionService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 *@description:
 *@author: gongchang
 *@time: 2023-04-06 18:14
 *
 */
@Slf4j
@Service
public class DeviceVersionPersistenceClientManagerImpl implements
    DeviceVersionPersistenceClientManager {

    private int DEFAULT_MAX_COUNT = 2000;

    private int DEFAULT_APPROM_MAX_COUNT = 100;

    @Autowired
    private DeviceRomVersionMapper deviceRomVersionMapper;

    @Autowired
    private DeviceApkVersionMapper deviceApkVersionMapper;

    @Autowired
    private AppVersionService appVersionService;


    @Override
    public ServiceResponse<List<DeviceVersionDTO>> getBatchDeviceAppVersion(Long orgId, Long[] deviceIds) {
        log.debug(" ---> getBatchDeviceAppVersion --- orgId=" + (orgId != null ? orgId : "NULL") + ",deviceIds.size=" + (deviceIds != null ? deviceIds.length : 0)); //for TEST

        if (orgId == null || orgId < 0) {
            return (new ServiceResponse<>(false,
                new ErrorContext(CommonErrorCode.ILLEGAL_ARGUMENT_EXCEPTION, "机构ID不能为空"), null));
        }
        if (deviceIds == null || ArrayUtils.isEmpty(deviceIds)) {
            return (new ServiceResponse<>(false,
                new ErrorContext(DeviceVersionErrorCode.DEVICEIDS_NOT_EXIST,
                    DeviceVersionErrorCode.DEVICEIDS_NOT_EXIST.getMessage()), null));
        }
        if (deviceIds.length > DEFAULT_MAX_COUNT) {
            return (new ServiceResponse<>(false,
                new ErrorContext(DeviceVersionErrorCode.TOO_MANY_DATA_REQUEST,
                    DeviceVersionErrorCode.TOO_MANY_DATA_REQUEST.getMessage()), null));
        }

        //从数据库中批量查询
        List<DeviceVersion> apkDeviceVersionList = deviceApkVersionMapper.getBatchDeviceApkVersionByOrgIdAndDeviceIds(orgId, Lists.newArrayList(deviceIds));
        //构造集合
        Map<Long/*deviceId*/, DeviceVersionDTO> apkDeviceVersionMap = buildDeviceVersionDtoMap(apkDeviceVersionList);
        //设置结果值
        List<DeviceVersionDTO> resultList          = Lists.newArrayList();
        for (Long deviceId : deviceIds){
            DeviceVersionDTO deviceVersionDto = apkDeviceVersionMap.get(deviceId);
            //如果数据库中无数据则拼装一个
            if (deviceVersionDto == null){
                deviceVersionDto = new DeviceVersionDTO();
                deviceVersionDto.setDeviceId(deviceId);
            }
            resultList.add(deviceVersionDto);
        }

        return new ServiceResponse<>(resultList);
    }

    /**
     * 构造从数据库查询出的版本map
     * @param deviceVersionList
     * @return
     */
    private Map<Long/*deviceId*/, DeviceVersionDTO> buildDeviceVersionDtoMap(List<DeviceVersion> deviceVersionList){
        Map<Long/*deviceId*/, DeviceVersionDTO>     deviceVersionMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(deviceVersionList)){
            return  deviceVersionMap;
        }
        for (DeviceVersion deviceVersion : deviceVersionList){
            DeviceVersionDTO deviceVersionDto = new DeviceVersionDTO();
            deviceVersionDto.setDeviceId(deviceVersion.getDeviceId());
            deviceVersionDto.setVersionCode(deviceVersion.getVersionCode());
            deviceVersionDto.setAppType(deviceVersion.getAppType());
            deviceVersionMap.put(deviceVersion.getDeviceId(), deviceVersionDto);
        }
        return  deviceVersionMap;
    }

    @Override
    public ServiceResponse<List<DeviceVersionDTO>> getBatchDeviceRomVersion(Long orgId, Long[] deviceIds) {
        log.debug(" ---> getBatchDeviceRomVersion --- orgId=" + (orgId != null ? orgId : "NULL") + ",deviceIds.size=" + (deviceIds != null ? deviceIds.length : 0)); //for TEST

        if (orgId == null || orgId < 0) {
            return (new ServiceResponse<List<DeviceVersionDTO>>(false, new ErrorContext(CommonErrorCode.ILLEGAL_ARGUMENT_EXCEPTION, "机构ID不能为空"), null));
        }
        if (deviceIds == null || ArrayUtils.isEmpty(deviceIds)) {
            return (new ServiceResponse<List<DeviceVersionDTO>>(false, new ErrorContext(DeviceVersionErrorCode.DEVICEIDS_NOT_EXIST, DeviceVersionErrorCode.DEVICEIDS_NOT_EXIST.getMessage()), null));
        }
        if (deviceIds.length > DEFAULT_MAX_COUNT) {
            return (new ServiceResponse<List<DeviceVersionDTO>>(false, new ErrorContext(DeviceVersionErrorCode.TOO_MANY_DATA_REQUEST, DeviceVersionErrorCode.TOO_MANY_DATA_REQUEST.getMessage()), null));
        }
        //从数据库中批量查询
        List<DeviceVersion> romDeviceVersionList = deviceRomVersionMapper.getBatchDeviceRomVersionByOrgIdAndDeviceIds(orgId, Lists.newArrayList(deviceIds));
        //构造map
        Map<Long/*deviceId*/, DeviceVersionDTO>     romDeviceVersionMap  = buildDeviceVersionDtoMap(romDeviceVersionList);

        //设置结果值
        List<DeviceVersionDTO> resultList          = Lists.newArrayList();
        for (Long deviceId : deviceIds){
            DeviceVersionDTO deviceVersionDto = romDeviceVersionMap.get(deviceId);
            //如果数据库中无数据则拼装一个
            if (deviceVersionDto == null){
                deviceVersionDto = new DeviceVersionDTO();
                deviceVersionDto.setDeviceId(deviceId);
            }
            resultList.add(deviceVersionDto);
        }
        return new ServiceResponse<List<DeviceVersionDTO>>(resultList);
    }


    @Override
    public ServiceResponse<List<DeviceCurrVersionDTO>> getBatchDeviceVersion(Long orgId, Long[] deviceIds) {
        log.debug(" ---> getBatchDeviceVersion --- orgId=" + (orgId != null ? orgId : "NULL") + ",deviceIds.size=" + (deviceIds != null ? deviceIds.length : 0)); //for TEST

        if (orgId == null || orgId < 0) {
            return (new ServiceResponse<List<DeviceCurrVersionDTO>>(false, new ErrorContext(CommonErrorCode.ILLEGAL_ARGUMENT_EXCEPTION, "机构ID不能为空"), null));
        }
        if (deviceIds == null || ArrayUtils.isEmpty(deviceIds)) {
            return (new ServiceResponse<List<DeviceCurrVersionDTO>>(false, new ErrorContext(DeviceVersionErrorCode.DEVICEIDS_NOT_EXIST, DeviceVersionErrorCode.DEVICEIDS_NOT_EXIST.getMessage()), null));
        }
        if (deviceIds.length > DEFAULT_APPROM_MAX_COUNT) {
            return (new ServiceResponse<List<DeviceCurrVersionDTO>>(false, new ErrorContext(DeviceVersionErrorCode.TOO_MANY_DATA_REQUEST, DeviceVersionErrorCode.TOO_MANY_DATA_REQUEST.getMessage()), null));
        }

        int systemType = AppSystemType.ANDROID.getValue();


        //从数据库中批量查询APK数据
        List<DeviceVersion> apkDeviceVersionList = deviceApkVersionMapper.getBatchDeviceApkVersionByOrgIdAndDeviceIds(orgId, Lists.newArrayList(deviceIds));
        Map<Long/*deviceId*/, DeviceVersion>     apkDeviceVersionMap = Maps.newHashMap();
        for (DeviceVersion deviceVersion : apkDeviceVersionList){
            apkDeviceVersionMap.put(deviceVersion.getDeviceId(), deviceVersion);
        }

        //从数据库中批量查询ROM数据
        List<DeviceVersion> romDeviceVersionList = deviceRomVersionMapper.getBatchDeviceRomVersionByOrgIdAndDeviceIds(orgId, Lists.newArrayList(deviceIds));
        Map<Long/*deviceId*/, DeviceVersion>     romDeviceVersionMap = Maps.newHashMap();
        for (DeviceVersion deviceVersion : romDeviceVersionList){
            romDeviceVersionMap.put(deviceVersion.getDeviceId(), deviceVersion);
        }

        List<DeviceCurrVersionDTO> resultData = new ArrayList<>();
        for (Long currentDeviceId : deviceIds) {
            DeviceCurrVersionDTO deviceCurrVersionDto = new DeviceCurrVersionDTO();
            deviceCurrVersionDto.setDeviceId(currentDeviceId);
            log.debug(" ---> getBatchDeviceVersion --- CurrAppVersion -- orgId=" + orgId + ",currentDeviceId=" + (currentDeviceId != null ? currentDeviceId : "NULL")); //for TEST
            //查询APK信息
            DeviceVersion currentApkDeviceVersion = apkDeviceVersionMap.get(currentDeviceId);
            if (currentApkDeviceVersion != null){
                //设置当前版本信息
                AppVersionDTO currentAppVersion = getAppVersionBySCTypeCode(systemType, currentApkDeviceVersion.getAppType(), currentApkDeviceVersion.getVersionCode());
                if (currentAppVersion != null){
                    deviceCurrVersionDto.setCurrAppVersion(currentAppVersion);
                }
                //设置最新版本信息
                AppVersionDTO latestApkVersion = getNewAppVersionBySCTypeAndIsActive(systemType, currentApkDeviceVersion.getAppType());
                if (latestApkVersion != null){
                    deviceCurrVersionDto.setLastAppVersion(latestApkVersion);
                }
            }

            //查询ROM信息
            DeviceVersion currentRomDeviceVersion = romDeviceVersionMap.get(currentDeviceId);
            if (currentRomDeviceVersion != null){
                //设置当前版本信息
                AppVersionDTO currentRomVersion = getAppVersionBySCTypeCode(systemType, currentRomDeviceVersion.getAppType(), currentRomDeviceVersion.getVersionCode());
                if (currentRomVersion != null){
                    deviceCurrVersionDto.setCurrRomVersion(currentRomVersion);
                }
                //设置最新版本信息
                AppVersionDTO latestRomVersion = getNewAppVersionBySCTypeAndIsActive(systemType, currentRomDeviceVersion.getAppType());
                if (latestRomVersion != null){
                    deviceCurrVersionDto.setLastRomVersion(latestRomVersion);
                }
            }
            resultData.add(deviceCurrVersionDto);
        }
        return (new ServiceResponse<>(resultData));
    }


    /**
     * 获取指定type下最新可用版本
     * @param systemType
     * @param appType
     * @return
     */
    private AppVersionDTO getNewAppVersionBySCTypeAndIsActive(Integer systemType, Integer appType){
        AppVersionDTO appRomVersion = null;
        try {
            log.debug(" ---> getBatchDeviceVersion >>> LastAppVersion -- (begin)"); //for TEST
            ServiceResponse<VersionDTO> srLastAppVersion = appVersionService.getNewAppVersionBySCTypeAndIsActive(systemType, appType);
            if (srLastAppVersion != null && srLastAppVersion.isSuccess() && srLastAppVersion.isExistData()) {
                VersionDTO versionDto = srLastAppVersion.getData();
                if (versionDto != null) {
                    appRomVersion =  new AppVersionDTO();
                    appRomVersion.setAppType(appType);
                    appRomVersion.setVersionCode(versionDto.getVersionCode());
                    appRomVersion.setVersionName(versionDto.getVersionName());
                    appRomVersion.setVersionDesc(versionDto.getVersionDesc());
                }
                log.debug(" ---> getBatchDeviceVersion >>> LastAppVersion  -- (result) lastAppVersionDto: " + (versionDto != null ? JsonUtils.toJson(versionDto) : "NULL")); //for TEST
            } else {
                log.debug(" ---> getBatchDeviceVersion >>> LastAppVersion -- (appVersionService.getNewAppVersionBySCTypeAndIsActive) -- the result is empty. (systemType=" + systemType + ",currAppType=" + appType + ")"); //for TEST
            }
            log.debug(" ---> getBatchDeviceVersion >>> LastAppVersion -- (end)"); //for TEST
        } catch (Exception e) {
            e.printStackTrace();
        }
        return appRomVersion;
    }


    /**
     * 根据type和code获取版本详情
     * @param systemType
     * @param appType
     * @param versionCode
     * @return
     */
    private AppVersionDTO getAppVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode){
        log.debug(" ---> getBatchDeviceVersion >>> CurrAppVersion -- deviceAppVersionCache.get -- (begin)"); //for TEST
        AppVersionDTO appVersionDTO = null;
        try {
            log.debug(" ---> getBatchDeviceVersion >>> CurrAppVersion  -- (begin) appVersionService.getAppVersionBySCTypeCode"); //for TEST
            ServiceResponse<VersionDTO> srLastAppVersion = appVersionService.getAppVersionBySCTypeCode(systemType, appType, versionCode);
            log.debug(" ---> getBatchDeviceVersion >>> CurrAppVersion  -- (end) appVersionService.getAppVersionBySCTypeCode >>> srLastAppVersion: " + (srLastAppVersion != null ? JsonUtils.toJson(srLastAppVersion) : "NULL")); //for TEST
            if (srLastAppVersion != null && srLastAppVersion.isSuccess() && srLastAppVersion.isExistData()) { //modified by zc
                VersionDTO versionDto = srLastAppVersion.getData();
                if (versionDto != null) {
                    appVersionDTO =  new AppVersionDTO();
                    //注：此处不使用versionDto参数赋值,直接使用appType versionCode 是因为ota中不存在此版本信息，
                    appVersionDTO.setAppType(appType);
                    appVersionDTO.setVersionCode(versionCode);
                    appVersionDTO.setVersionName(versionDto.getVersionName());
                    appVersionDTO.setVersionDesc(versionDto.getVersionDesc());
                }
                log.debug(" ---> getBatchDeviceVersion >>> CurrAppVersion  -- (result) currAppVersionDto: " + (versionDto != null ? JsonUtils.toJson(versionDto) : "NULL")); //for TEST
            } else {
                log.debug(" ---> getBatchDeviceVersion >>> CurrAppVersion -- (appVersionService.getAppVersionBySCTypeCode) -- the result is empty. (systemType=" + systemType + ",currAppType=" + appType + ",currAppVersionCode=" + versionCode + ")"); //for TEST
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return appVersionDTO;
    }
}
