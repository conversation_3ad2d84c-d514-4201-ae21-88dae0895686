package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.SpaceRuleRelation;
import com.moredian.magicube.device.dao.mapper.SpaceRuleRelationMapper;
import com.moredian.magicube.device.manager.SpaceRuleRelationManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.moredian.magicube.device.constant.BeanConstants.SPACE_RULE_RELATION;

/**
 * <AUTHOR>
 * @version $Id: SpaceRuleRelationManagerImpl.java, v 1.0 Exp $
 */
@Component
@Slf4j
public class SpaceRuleRelationManagerImpl implements SpaceRuleRelationManager {

    @Resource
    private SpaceRuleRelationMapper spaceRuleRelationMapper;
    @SI
    private IdgeneratorService idGeneratorService;

    @Override
    public List<SpaceRuleRelation> getRelationByOrgIdAndRuleId(Long orgId, Long ruleId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(ruleId, "ruleId must not be null");

        List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationMapper.getRelationByOrgIdAndRuleId(orgId, ruleId);
        spaceRuleRelationList = Optional.ofNullable(spaceRuleRelationList).orElse(Collections.EMPTY_LIST);
        return spaceRuleRelationList;
    }

    @Override
    public List<Long> getSpaceIdByOrgIdAndRuleId(Long orgId, Long ruleId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(ruleId, "ruleId must not be null");
        return spaceRuleRelationMapper.getSpaceIdByOrgIdAndRuleId(orgId, ruleId);
    }

    @Override
    public List<SpaceRuleRelation> getRelationByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        return spaceRuleRelationMapper.getRelationByOrgId(orgId);
    }

    @Override
    public List<Long> getRuleIdByOrgIdAndSpaceId(Long orgId, Long spaceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(spaceId, "spaceId must not be null");

        return spaceRuleRelationMapper.getRuleIdByOrgIdAndSpaceId(orgId, spaceId);
    }

    @Override
    public List<SpaceRuleRelation> getRelationByOrgIdAndSpaceId(Long orgId, Long ruleId, Long spaceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(orgId, "ruleId must not be null");
        BizAssert.notNull(spaceId, "spaceId must not be null");
        return spaceRuleRelationMapper.getByCondition(orgId, ruleId, spaceId);
    }

    @Override
    public void ruleRelateSpace(Long orgId, Long ruleId, List<Long> spaceIdList) {

        spaceRuleRelationMapper.deleteSpaceRule(orgId, ruleId);

        if (CollectionUtils.isEmpty(spaceIdList)) {
            return;
        }
        ServiceResponse<BatchIdDto> nextIdBatch = idGeneratorService.getNextIdBatchBytypeName(SPACE_RULE_RELATION, spaceIdList.size());
        BatchIdDto batchId = nextIdBatch.getData();

        List<SpaceRuleRelation> spaceRuleRelationList = spaceIdList.stream()
                .map(spaceId -> new SpaceRuleRelation().setSpaceRuleRelationId(batchId.nextId()).setOrgId(orgId).setSpaceId(spaceId).setRuleId(ruleId))
                .collect(Collectors.toList());
        spaceRuleRelationMapper.batchInsert(spaceRuleRelationList);
    }

    @Override
    public void addOrUpdate(List<SpaceRuleRelation> spaceRuleRelationList) {
        if (CollectionUtils.isEmpty(spaceRuleRelationList)) {
            return;
        }
        ServiceResponse<BatchIdDto> nextBatchResp = idGeneratorService.getNextIdBatchBytypeName(SPACE_RULE_RELATION, spaceRuleRelationList.size());
        BatchIdDto batchId = nextBatchResp.getData();

        List<SpaceRuleRelation> newList = Lists.newArrayList(), updateList = Lists.newArrayList();
        for (SpaceRuleRelation spaceRuleRelation : spaceRuleRelationList) {
            if (Objects.isNull(spaceRuleRelation.getSpaceRuleRelationId())) {
                spaceRuleRelation.setSpaceRuleRelationId(batchId.nextId());
                newList.add(spaceRuleRelation);
            } else {
                updateList.add(spaceRuleRelation);
            }
        }

        if (!CollectionUtils.isEmpty(newList)) {
            spaceRuleRelationMapper.batchInsert(newList);
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            for (SpaceRuleRelation spaceRuleRelation : updateList) {
                spaceRuleRelationMapper.update(spaceRuleRelation);
            }
        }
    }

    @Override
    public void delete(Long orgId, List<Long> spaceRuleRelationIdList) {
        if (CollectionUtils.isEmpty(spaceRuleRelationIdList)) {
            return;
        }
        spaceRuleRelationMapper.delete(orgId, spaceRuleRelationIdList);
    }
}
