package com.moredian.magicube.device.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/12/20
 */
//@Configuration
@Getter
@Setter
public class MoLanApkAccessConfig {

    @Value("${molan.apk.apkServerRootUrl:}")
    private String apkServerRootUrl;
}
