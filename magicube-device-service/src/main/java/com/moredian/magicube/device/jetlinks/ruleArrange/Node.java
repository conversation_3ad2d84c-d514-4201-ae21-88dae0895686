package com.moredian.magicube.device.jetlinks.ruleArrange;

import com.moredian.magicube.device.constant.DeviceIotSplitConstants;
import com.moredian.magicube.device.enums.DeviceTypeSwitchCodeEnum;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;

import java.util.UUID;

import static com.moredian.magicube.device.constant.DeviceTypeConstants.IOT_VIRTUAL_DEVICE_TYPE_LIST;

/**
 * <AUTHOR>
 * @version $Id: Node.java, v 1.0 Exp $
 */
public class Node {

    protected String modelId;

    protected String id;

    protected Flow flow;

    private DeviceTypePropertyManager deviceTypePropertyManager;

    public Node(String modelId, Flow flow, DeviceTypePropertyManager deviceTypePropertyManager) {
        this.modelId = modelId;
        this.flow = flow;
        this.id = uuid();
        this.deviceTypePropertyManager = deviceTypePropertyManager;
    }

   public String getId() {
        return id;
   }

    private String uuid() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    protected String deviceSn(TreeDeviceRelationDTO treeDeviceRelation) {
        return deviceTypePropertyManager.containsDeviceType(IOT_VIRTUAL_DEVICE_TYPE_LIST, treeDeviceRelation.getDeviceType()) ?
                treeDeviceRelation.getParentDeviceSn() : treeDeviceRelation.getDeviceSn();
    }

    protected String getGroup(TreeDeviceRelationDTO treeDeviceRelation) {
        boolean needSplitIotDevice = deviceTypePropertyManager.containsDeviceType(IOT_VIRTUAL_DEVICE_TYPE_LIST, treeDeviceRelation.getDeviceType());
        String deviceSn = treeDeviceRelation.getDeviceSn();
        return needSplitIotDevice ? deviceSn.substring(deviceSn.lastIndexOf(DeviceIotSplitConstants.UNDERLINE) + 1) : null;
    }

    protected String getPropertyId(TreeDeviceRelationDTO treeDeviceRelation, String group) {
        return DeviceTypeSwitchCodeEnum.getSwitchPropertyId(treeDeviceRelation.getDeviceType(), group);
    }
}

