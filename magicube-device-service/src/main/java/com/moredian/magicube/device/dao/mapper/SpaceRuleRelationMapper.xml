<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.SpaceRuleRelationMapper">

    <resultMap id="baseMap" type="com.moredian.magicube.device.dao.entity.SpaceRuleRelation">
        <result property="spaceRuleRelationId" column="space_rule_relation_id"/>
        <result property="orgId" column="org_id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="spaceId" column="space_id"/>
        <result property="sceneId" column="scene_id"/>
        <result property="ruleArrangeJson" column="rule_arrange_json"/>
        <result property="ruleArrangeId" column="rule_arrange_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModify" column="gmt_modify"/>
    </resultMap>

    <sql id="sql_table">
        hive_space_rule_relation
    </sql>

    <sql id="sql_columns">
        space_rule_relation_id,
        org_id,
        rule_id,
        space_id,
        scene_id,
        rule_arrange_id,
        gmt_create,
        gmt_modify
    </sql>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.SpaceRuleRelation">
        update <include refid="sql_table"/>
        <set>
            <if test="sceneId != null and sceneId != ''">
                scene_id = #{sceneId},
            </if>
            <if test="ruleJson != null and ruleJson != ''">
                rule_json = #{ruleJson},
            </if>
            <if test="ruleArrangeId != null and ruleArrangeId != ''">
                rule_arrange_id = #{ruleArrangeId},
            </if>
            <if test="ruleArrangeJson != null and ruleArrangeJson != ''">
                rule_arrange_json = #{ruleArrangeJson},
            </if>
            gmt_modify = now(3)
        </set>
        where org_id = #{orgId} and space_rule_relation_id = #{spaceRuleRelationId}
    </update>

    <delete id="deleteSpaceRule">
        delete from   <include refid="sql_table"/>
        <where>
            org_id = #{orgId} and rule_id = #{ruleId}
        </where>
    </delete>
    <delete id="delete">
        delete from <include refid="sql_table"/>
        where org_id = #{orgId}
        and space_rule_relation_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>


    <select id="getRelationByOrgIdAndRuleId" resultMap="baseMap">
        select  <include refid="sql_columns"/> from <include refid="sql_table"/>
        where org_id = #{orgId} and rule_id = #{ruleId}
    </select>
    <select id="getRuleIdByOrgIdAndSpaceId"
            resultType="java.lang.Long">
        select  rule_id from  <include refid="sql_table"/>
        where org_id = #{orgId} and space_id = #{spaceId}
    </select>
    <select id="getByCondition" resultMap="baseMap">
        select space_rule_relation_id,
        org_id,
        rule_id,
        space_id,
        scene_id
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and rule_id = #{ruleId}
        and space_id = #{spaceId}
    </select>
    <select id="getSpaceIdByOrgIdAndRuleId" resultType="java.lang.Long">
        select  space_id from  <include refid="sql_table"/>
        where org_id = #{orgId} and rule_id = #{ruleId}
    </select>
    <select id="getRelationByOrgId" resultMap="baseMap">
        select
        space_rule_relation_id,
        org_id,
        rule_id,
        space_id,
        scene_id,
        rule_arrange_id
        from  <include refid="sql_table"/>
        where org_id = #{orgId}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into
        <include refid="sql_table"/>
        (
        space_rule_relation_id,
        org_id,
        rule_id,
        space_id,
        rule_json,
        scene_id,
        rule_arrange_json,
        rule_arrange_id,
        gmt_create,
        gmt_modify
        ) values
        <foreach collection="list" item="item" separator=",">
            (#{item.spaceRuleRelationId},
            #{item.orgId},
            #{item.ruleId},
            #{item.spaceId},
            #{item.ruleJson},
            #{item.sceneId},
            #{item.ruleArrangeJson},
            #{item.ruleArrangeId},
            now(3),
            now(3))
        </foreach>
    </insert>

</mapper>