package com.moredian.magicube.device.subscriber;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.moredian.bee.common.utils.HttpInvoker;
import com.moredian.bee.common.utils.HttpInvokerResponse;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.common.model.msg.device.DeviceActiveMsg;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.enums.DeviceFlagEnum;
import com.moredian.magicube.device.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2025/6/10
 */
@Slf4j
@Component
public class MirgationUnbindTowerDevice {

    @SI
    private OrgService orgService;
    @SI
    private DeviceService deviceService;

    @Value("${dd.address.moredianFishnetWeb:http://************:8049}")
    private String moredianFishnetWeb;

    private final String UNBIND_FUSION_DEVICE = "/device/unbindFusionDevice";

    /**
     * 监听设备激活消息，解绑塔上设备
     *
     * @param msg msg
     */
    @Subscribe
    public void unbindTowerDevice(DeviceActiveMsg msg) {
        log.info("收到设备激活消息  msg:{}", msg);
        DeviceInfoDTO deviceInfoDTO = deviceService.getByDeviceSn(msg.getDeviceSn()).pickDataThrowException();
        if (!Objects.equals(DeviceFlagEnum.FX.getValue(), deviceInfoDTO.getDeviceFlag())) {
            OrgInfo orgInfo = orgService.getOrgInfo(msg.getOrgId(), Lists.newArrayList(OrgStatus.USABLE.getValue())).pickDataThrowException();
            String url = moredianFishnetWeb + UNBIND_FUSION_DEVICE + "?orgId=" + orgInfo.getFusionOrgId() + "&deviceSn=" + msg.getDeviceSn();
            log.info("unbind tower device url={}", url);
            HttpInvokerResponse response = HttpInvoker.invokerDelete(url, null, null);
            log.info("unbind tower device response={}", JSON.toJSONString(response));
        }
    }
}
