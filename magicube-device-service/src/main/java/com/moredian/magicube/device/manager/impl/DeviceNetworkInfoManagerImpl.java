package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.DeviceNetworkInfo;
import com.moredian.magicube.device.dao.mapper.DeviceNetworkInfoMapper;
import com.moredian.magicube.device.manager.DeviceNetworkInfoManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */

@Component
public class DeviceNetworkInfoManagerImpl implements DeviceNetworkInfoManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private DeviceNetworkInfoMapper deviceNetworkInfoMapper;

    @Override
    public Long insert(DeviceNetworkInfo networkInfo) {
        BizAssert.notNull(networkInfo.getOrgId(), "orgId must not be null");
        BizAssert.notNull(networkInfo.getDeviceId(), "deviceId must not be null");
        BizAssert.notNull(networkInfo.getConnectType(), "connectType must not be null");
        BizAssert.notNull(networkInfo.getNetworkType(), "networkType must not be null");
        DeviceNetworkInfo deviceNetworkInfo = deviceNetworkInfoMapper
            .getByOrgIdAndDeviceId(networkInfo.getOrgId(), networkInfo.getDeviceId());
        if (deviceNetworkInfo == null) {
            networkInfo.setId(
                idgeneratorService.getNextIdByTypeName(DeviceNetworkInfo.class.getName())
                    .pickDataThrowException());
            deviceNetworkInfoMapper.insert(networkInfo);
            return networkInfo.getId();
        }
        return deviceNetworkInfo.getId();
    }

    @Override
    public Boolean update(DeviceNetworkInfo networkInfo) {
        BizAssert.notNull(networkInfo.getId(), "id must not be null");
        BizAssert.notNull(networkInfo.getOrgId(), "orgId must not be null");
        BizAssert.notNull(networkInfo.getDeviceId(), "deviceId must not be null");
        deviceNetworkInfoMapper.update(networkInfo);
        return Boolean.TRUE;
    }

    @Override
    public DeviceNetworkInfo getByOrgIdAndDeviceId(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        return deviceNetworkInfoMapper.getByOrgIdAndDeviceId(orgId, deviceId);
    }
}
