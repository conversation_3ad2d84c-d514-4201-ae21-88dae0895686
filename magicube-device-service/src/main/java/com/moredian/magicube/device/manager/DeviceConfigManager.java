package com.moredian.magicube.device.manager;


/**
 * 设备配置
 *
 * <AUTHOR>
 */
public interface DeviceConfigManager {

    /**
     * 新增或更新设备配置
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @param xml      配置信息
     * @return
     */
    boolean insertOrUpdate(Long orgId, Long deviceId, String xml);

    /**
     * 查询设备配置
     *
     * @param deviceSn 机构号
     * @return
     */
    String getXmlConfig(String deviceSn);
}
