package com.moredian.magicube.device.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.dao.entity.RuleTemplate;
import com.moredian.magicube.device.enums.ModeTriggerEnum;
import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.magicube.device.jetlinks.rule.arrange.ArrangeMetaData;
import com.moredian.magicube.device.jetlinks.rule.arrange.DeviceMessageSenderNode;
import com.moredian.magicube.device.jetlinks.rule.arrange.FunctionNode;
import com.moredian.magicube.device.jetlinks.rule.arrange.Node;
import com.moredian.magicube.device.jetlinks.rule.arrange.ReactorQLNode;
import com.moredian.magicube.device.jetlinks.rule.arrange.TabNode;
import com.moredian.magicube.device.jetlinks.rule.arrange.TimerNode;
import com.moredian.magicube.device.jetlinks.ruleArrange.RuleInstance;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.RuleManager;
import com.moredian.magicube.device.service.strategy.ModeTriggerTemplateParseStrategy;
import com.moredian.magicube.device.utils.StringUtils;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.StringJoiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.ObjectUtils;
import static com.moredian.magicube.device.constant.DeviceIotPropertyConstants.*;
import static com.moredian.magicube.device.constant.DeviceTypeConstants.*;

/**
 * <AUTHOR>
 * @version $Id: SaveEnergyDeviceTemplateParseStrategy.java, v 1.0 Exp $
 */
@Slf4j
@Service(value="saveEnergyDeviceTriggerTemplateParseStrategy")
public class SaveEnergyDeviceTriggerTemplateParseStrategy implements ModeTriggerTemplateParseStrategy {

    @Resource
    private RuleManager ruleManager;
    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private OrgService orgService;

    ModeTriggerEnum templateEnum = ModeTriggerEnum.SAVE_ENERGY_DEVICE_TRIGGER;

    public static final String MODEL_TYPE = "node-red";

    public static final String TIMER_CORN = "0/30 * * * * ?";

    @Override
    public Map<Long, RuleInstance> getSceneRule(Rule rule, List<TreeDeviceRelationDTO> deviceRelationList,
        Map<String, IotDeviceInfoDTO> iotDeviceMap, Map<String, BatchIotDevicePropertyInfoDTO> iotDevicePropertyMap,
        List<Integer> selectDeviceTypes) {

        RuleTemplate ruleTemplate = ruleManager.getRuleTemplate(rule.getOrgId(), rule.getRuleId());
        if (ruleTemplate == null) {
            log.info("模版为空：orgId: {}, ruleId: {}", rule.getOrgId(), rule.getRuleId());
            return Collections.emptyMap();
        }

        //按照空间划分设备
        Map<Long, List<TreeDeviceRelationDTO>> treeDeviceMap =  deviceRelationList.stream()
            .collect(Collectors.groupingBy(TreeDeviceRelationDTO::getTreeId));

        // 查询机构信息，用于构建规则名称
        OrgInfo orgInfo = orgService.getOrgInfo(rule.getOrgId(), Lists.newArrayList(OrgStatus.USABLE.getValue()))
            .pickDataThrowException();
        String orgName = ObjectUtils.isEmpty(orgInfo) ? "" : orgInfo.getOrgName();

        Map<Long, RuleInstance> spaceRuleMetaDataMap = Maps.newHashMap();
        for (Map.Entry<Long, List<TreeDeviceRelationDTO>> treeDeviceEntry :  treeDeviceMap.entrySet()) {
            // 需要控制的设备集合
            List<TreeDeviceRelationDTO> controlDeviceList = Lists.newArrayList();
            // 雷达设备集合
            List<TreeDeviceRelationDTO> triggerDeviceRelationList = Lists.newArrayList();
            // 当前空间下的所有设备
            List<TreeDeviceRelationDTO> treeDeviceRelationList = treeDeviceEntry.getValue();

            for (TreeDeviceRelationDTO treeDeviceRelation : treeDeviceRelationList) {
                Integer deviceType = treeDeviceRelation.getDeviceType();
                if (selectDeviceTypes.contains(deviceType)) {
                    controlDeviceList.add(treeDeviceRelation);
                } else if (Boolean.TRUE.equals(deviceTypePropertyManager
                    .containsDeviceType(IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST, deviceType))) {
                    triggerDeviceRelationList.add(treeDeviceRelation);
                }
            }

            if (CollectionUtils.isEmpty(controlDeviceList) || CollectionUtils.isEmpty(triggerDeviceRelationList)) {
                log.error("iot触发设备或节能设备不存在，orgId: {} ,treeId: {}", rule.getOrgId(), treeDeviceEntry.getKey());
                continue;
            }

            // 设备触发方式全部改成规则编排触发
            RuleInstance ruleInstance = getRuleArrangeInstance(triggerDeviceRelationList,
                controlDeviceList, rule.getRuleName(),
                orgName, rule.getTriggerValue());
            spaceRuleMetaDataMap.put(treeDeviceEntry.getKey(), ruleInstance);
        }
        return spaceRuleMetaDataMap;
    }

    /**
     * 获取一个空间下对应设备的规则编排实例
     */
    private RuleInstance getRuleArrangeInstance(List<TreeDeviceRelationDTO> triggerDeviceRelationList,
        List<TreeDeviceRelationDTO> controlDeviceList, String ruleName, String orgName, String triggerValue) {
        // 规则名称：房间名称+规则名称
        ruleName = controlDeviceList.get(0).getTreeName() + StringUtils.CROSS_LINE + ruleName;

        StringJoiner controlDeviceName = new StringJoiner(",", "(", ")");
        controlDeviceList.forEach(e-> controlDeviceName.add(e.getDeviceName()));

        RuleInstance ruleInstance = new RuleInstance();
        ruleInstance.setModelId(StringUtils.uuidStr());
        ruleInstance.setName(ruleName);
        ruleInstance.setDescription(orgName + controlDeviceName);
        ruleInstance.setModelType(MODEL_TYPE);
        String metadata = getMetadata(triggerDeviceRelationList, controlDeviceList, ruleName, triggerValue);
        ruleInstance.setModelMeta(metadata);
        ruleInstance.setRuleType(JET_LINK_RULE_TYPE_RULE_ARRANGE);
        return ruleInstance;
    }

    private String getMetadata(List<TreeDeviceRelationDTO> triggerDeviceRelationList, List<TreeDeviceRelationDTO> controlDeviceList,
        String ruleName, String triggerValue) {
        List<Node> flows = new ArrayList<>();

        // 主节点
        TabNode tabNode = new TabNode();
        tabNode.setLabel(ruleName);
        tabNode.setDisabled(Boolean.TRUE);
        flows.add(tabNode);

        // 设备指令节点
        DeviceMessageSenderNode deviceMessageSenderNode = new DeviceMessageSenderNode();
        List<List<String>> wires1 = new ArrayList<>();
        deviceMessageSenderNode.setWires(wires1);
        deviceMessageSenderNode.setZ(tabNode.getId());
        flows.add(deviceMessageSenderNode);

        // 判断雷达最小的无人时间
        FunctionNode humanTime = new FunctionNode();
        String timeFunc = FunctionNode.NO_HUMAN_TIME.replaceAll("#\\{time}",
            String.valueOf(Integer.parseInt(triggerValue) * 60));
        humanTime.setFunc(timeFunc);
        humanTime.setZ(tabNode.getId());
        flows.add(humanTime);

        // 需要控制的设备判断开关状态
        for (TreeDeviceRelationDTO deviceRelation : controlDeviceList) {
            String group = getGroup(deviceRelation);
            String powerPropertyId = getPropertyId(deviceRelation, group);
            String deviceSn = parseDeviceSn(deviceRelation.getDeviceSn());

            // 开关判断函数节点
            FunctionNode functionNode = new FunctionNode();
            String powerFunc = FunctionNode.NO_HUMAN_POWER_OFF.replaceAll("#\\{propertyId}", powerPropertyId)
                .replaceAll("#\\{deviceSn}", deviceSn)
                .replaceAll("#\\{propertyValue}", String.valueOf(RuleStateEnum.DISABLE.getCode()));
            functionNode.setFunc(powerFunc);
            functionNode.nodeJoin(deviceMessageSenderNode);
            functionNode.setZ(tabNode.getId());
            flows.add(functionNode);

            // 查询开关状态ReactorQL节点
            ReactorQLNode powerQLNode = new ReactorQLNode();
            String powerQL = ReactorQLNode.DEVICE_POWER_STATUS.replaceAll("#\\{propertyId}",
                    powerPropertyId).replaceAll("#\\{deviceSn}", deviceSn);
            powerQLNode.setSql(powerQL);
            powerQLNode.nodeJoin(functionNode);
            powerQLNode.setZ(tabNode.getId());
            flows.add(powerQLNode);

            humanTime.nodeJoin(powerQLNode);
        }

        // 获取雷达状态ReactorQL节点
        ReactorQLNode humanBodyStatusQL = buildHumanBodyStatusQL(triggerDeviceRelationList);
        humanBodyStatusQL.setZ(tabNode.getId());
        humanBodyStatusQL.nodeJoin(humanTime);
        flows.add(humanBodyStatusQL);

        // 构建定时任务节点
        TimerNode timerNode = new TimerNode();
        timerNode.setCron(TIMER_CORN);
        timerNode.nodeJoin(humanBodyStatusQL);
        timerNode.setZ(tabNode.getId());
        flows.add(timerNode);

        ArrangeMetaData metaData = new ArrangeMetaData();
        metaData.setFlows(flows);
        return StringUtils.getSpecialJsonStr(metaData);
    }

    /**
     * 构建查询雷达状态节点
     */
    private ReactorQLNode buildHumanBodyStatusQL(List<TreeDeviceRelationDTO> triggerDeviceRelationList) {
        StringJoiner reactorSql = new StringJoiner("");
        StringJoiner select = new StringJoiner("", 
            "select collect_list(deviceId, motionStatus, duration) AS devices from( ",
            " ) t");

        StringJoiner humanDeviceSelect = getHumSelectSql(triggerDeviceRelationList);
        select.add(humanDeviceSelect.toString());
        reactorSql.add(select.toString());

        ReactorQLNode reactorQLNode = new ReactorQLNode();
        reactorQLNode.setSql(reactorSql.toString());
        return reactorQLNode;
    }

    private StringJoiner getHumSelectSql(List<TreeDeviceRelationDTO> triggerDeviceRelationList) {
        StringJoiner humanDeviceSelect = new StringJoiner(" union all ");
        TreeDeviceRelationDTO triggerDeviceRelation;
        for (TreeDeviceRelationDTO treeDeviceRelationDTO : triggerDeviceRelationList) {
            triggerDeviceRelation = treeDeviceRelationDTO;
            String selectSql =
                "select id AS deviceId,device.property.recent(id,'motionStatus') AS motionStatus,\n"
                + "device.config(id, 'state') AS state,\n"
                + "now() - device.property_time.recent(id, 'motionStatus') AS duration\n"
                + " from device.selector(device('" + parseDeviceSn(triggerDeviceRelation.getDeviceSn())
                + "'))";
            humanDeviceSelect.add(selectSql);
        }
        return humanDeviceSelect;
    }

    @Override
    public Boolean virtualIotDevice(Integer deviceType) {
        return deviceTypePropertyManager.containsDeviceType(IOT_VIRTUAL_DEVICE_TYPE_LIST, deviceType);
    }

    @Override
    public ModeTriggerEnum getTemplate() {
        return templateEnum;
    }
}
