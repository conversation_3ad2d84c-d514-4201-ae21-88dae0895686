package com.moredian.magicube.device.jetlinks.rule.arrange;

import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.utils.StringUtils;
import java.util.ArrayList;
import java.util.List;
import com.moredian.magicube.device.utils.StringUtils;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @description：抽象出规则编排的每一个节点
 * @date ：2024/08/06 14:18
 */
@Data
public abstract class Node {

    private String id;

    private String type;

    private String name;

    private Long x;

    private Long y;

    /**
     * tab的id
     */
    private String z;

    /**
     * 下一个节点的id
     */
    private List<List<String>> wires;

    protected Node(){}

    protected Node(String type){
        this.id = StringUtils.uuidStr();
        this.type = type;
    }

    /**
     * 连接两个节点
     */
    public void nodeJoin(Node next){
        if (next == null){
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_RULE_NODE_CANNOT_BE_NULL, DeviceErrorCode.DEVICE_RULE_NODE_CANNOT_BE_NULL.getMessage()));
        }
        List<List<String>> tempWires = this.getWires();
        if (tempWires == null){
            tempWires = new ArrayList<>();
            // 需要初始化一个空节点
            tempWires.add(new ArrayList<>());
            this.setWires(tempWires);
        }
        // 只使用到第一个元素
        tempWires.get(0).add(next.getId());
    }
}
