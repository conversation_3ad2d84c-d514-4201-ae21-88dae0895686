<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceTypePropertyRelationMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap"
    type="com.moredian.magicube.device.dao.entity.DeviceTypePropertyRelation">
    <id column="id" property="id"/>
    <result column="device_type_property_id" property="deviceTypePropertyId"/>
    <result column="device_type" property="deviceType"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <sql id="sql_table">
        hive_device_type_property_relation
    </sql>

  <sql id="sql_columns">
        id,
        device_type_property_id,
        device_type,
        gmt_create,
        gmt_modify
	</sql>

  <sql id="sql_values">
        #{id},
        #{deviceTypePropertyId},
        #{deviceType},
        now(3),
        now(3)
    </sql>

  <insert id="insert"
    parameterType="com.moredian.magicube.device.dao.entity.DeviceTypePropertyRelation">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>

  <insert id="batchInsert" parameterType="list">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    <foreach collection="relations" item="item" separator=",">
      (
      #{item.id},
      #{item.deviceTypePropertyId},
      #{item.deviceType},
      now(),
      now()
      )
    </foreach>
  </insert>

  <select id="getByPropertyIdAndDeviceType" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where device_type_property_id = #{propertyId}
    and device_type = #{deviceType}
  </select>

  <select id="listPropertyIdByDeviceType" parameterType="integer" resultType="long">
    select device_type_property_id
    from
    <include refid="sql_table"/>
    where device_type = #{deviceType}
  </select>

  <select id="listDeviceTypeByPropertyId" parameterType="long" resultType="integer">
    select device_type
    from
    <include refid="sql_table"/>
    where device_type_property_id = #{propertyId}
  </select>

  <delete id="deleteByDeviceTypeAndPropertyId" parameterType="map">
    delete from
    <include refid="sql_table"/>
    where 1=1
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      and device_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
    <if test="propertyIds != null and propertyIds.size() > 0">
      and device_type_property_id in
      <foreach collection="propertyIds" index="index" item="propertyId" open="(" separator="," close=")">
        #{propertyId}
      </foreach>
    </if>
  </delete>
</mapper>
