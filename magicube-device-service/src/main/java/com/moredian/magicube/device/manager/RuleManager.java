package com.moredian.magicube.device.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.dao.entity.RuleTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleManager.java, v 1.0 Exp $
 */
public interface RuleManager extends IService<Rule> {

    List<Rule> getRuleByCondition(Long orgId, Integer spaceType, Long deviceId,
        Integer modeType, Long ruleId);

    List<Long> getTemplateIdByOrgId(Long orgId);

    Rule getByRuleId(Long ruleId);

    /**
     * 批量插入规则
     */
    void batchInsert(Long orgId, List<RuleTemplate> templateList);
    /**
     * 插入规则
     */
    Long insert(Long orgId, RuleTemplate template, List<Integer> deviceTypes,
        String ruleName, Integer spaceType, Boolean humanPriority);

    void updateRuleAndDevice(Long orgId, Rule rule, List<Long> spaceIdList, List<Long> deviceIdList);

    void updateRule(Long orgId, Rule rule);

    void updateRuleAndDevice(Long orgId, Rule rule, List<Long> deviceIdList);

    Rule getRule(Long orgId, Long ruleId);

    RuleTemplate getRuleTemplate(Long orgId, Long ruleId);
}

