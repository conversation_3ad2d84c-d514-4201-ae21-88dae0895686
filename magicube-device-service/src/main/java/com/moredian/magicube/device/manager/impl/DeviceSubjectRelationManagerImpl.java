package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import com.moredian.magicube.device.dao.entity.SpaceSubjectReleation;
import com.moredian.magicube.device.dao.mapper.DeviceSubjectRelationMapper;
import com.moredian.magicube.device.dao.mapper.SpaceSubjectRelationMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.SimpleDeviceInfoDTO;
import com.moredian.magicube.device.dto.subject.DeviceSubjectDTO;
import com.moredian.magicube.device.enums.DeviceSourceEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.MqttMessageComponent;
import com.moredian.magicube.device.manager.DeviceSubjectManager;
import com.moredian.magicube.device.manager.DeviceSubjectRelationManager;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceSubjectService;
import com.moredian.magicube.device.subscriber.TransferEventType;
import com.moredian.magicube.device.utils.StringUtils;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-28
 */

@Component
@Slf4j
public class DeviceSubjectRelationManagerImpl implements DeviceSubjectRelationManager {

    @SI
    private DeviceSubjectService deviceSubjectService;

    @SI
    private IdgeneratorService idgeneratorService;

    @SI
    private DeviceService deviceService;

    @Autowired
    private MqttMessageComponent mqttMessageComponent;

    @Autowired
    private DeviceSubjectRelationMapper deviceSubjectRelationMapper;

    @Autowired
    private DeviceSubjectManager deviceSubjectManager;

    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;

    @Autowired
    private SpaceSubjectRelationMapper spaceSubjectRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetRelationBySubjectId(Long orgId, Long subjectId, List<Long> deviceIds,List<Long> spaceIds) {
        if (CollectionUtils.isEmpty(spaceIds)){
            spaceIds = new ArrayList<>();
        }
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(subjectId, "subjectId must not be null");
        DeviceSubjectDTO deviceSubjectDTO = deviceSubjectManager.getByOrgIdAndId(orgId,subjectId);
        // 空间处理

        List<Long> deviceIdListBySpaceId = new ArrayList<>();
        List<TreeDeviceRelationDTO> treeDeviceRelationDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spaceIds)){
            treeDeviceRelationDTOS = spaceTreeDeviceRelationService.listAllByTreeIdsAndSource(orgId, spaceIds, DeviceSourceEnum.MD.getCode()).pickDataThrowException();
        }

        if (CollectionUtils.isNotEmpty(treeDeviceRelationDTOS)){
            deviceIdListBySpaceId = treeDeviceRelationDTOS.stream().distinct().map(TreeDeviceRelationDTO::getDeviceId).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(deviceIdListBySpaceId)){
            List<DeviceInfoDTO> deviceInfoDTOS = deviceService.listByOrgIdAndIds(orgId, deviceIdListBySpaceId).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceInfoDTOS)){
                List<Long> collect = deviceInfoDTOS.stream().filter(item -> Objects.equals(SceneTypeEnums.MEETING_ROOM_MODEL.getValue(), item.getSceneType())).map(DeviceInfoDTO::getDeviceId).distinct().collect(Collectors.toList());
                deviceIds.addAll(collect);
            }
        }

        List<Long> existSpaceIds = spaceSubjectRelationMapper.listSpaceIdByOrgIdAndSubjectId(orgId, subjectId);
        List<Long> decSpaceIds = new ArrayList<>(existSpaceIds);
        decSpaceIds.removeAll(spaceIds);
        if (CollectionUtils.isNotEmpty(decSpaceIds)) {
            spaceSubjectRelationMapper.deleteByCondition(orgId,
                    decSpaceIds, Collections.singletonList(subjectId));
        }

        List<Long> incSpaceIds = new ArrayList<>(spaceIds);
        incSpaceIds.removeAll(existSpaceIds);
        if (CollectionUtils.isNotEmpty(incSpaceIds)) {
            BatchIdDto batchIdDto = idgeneratorService
                    .getNextIdBatchBytypeName(DeviceSubjectRelation.class.getName(),
                            incSpaceIds.size()).pickDataThrowException();
            List<SpaceSubjectReleation> spaceSubjectReleations = new ArrayList<>(incSpaceIds.size());
            for (Long spaceId : incSpaceIds) {
                SpaceSubjectReleation relation = new SpaceSubjectReleation();
                relation.setId(batchIdDto.nextId());
                relation.setOrgId(orgId);
                relation.setSubjectId(subjectId);
                relation.setSpaceId(spaceId);
                relation.setType(deviceSubjectDTO.getType());
                spaceSubjectReleations.add(relation);
            }
            spaceSubjectRelationMapper.batchInsert(spaceSubjectReleations);
        }

        BizAssert.notNull(deviceSubjectDTO, DeviceErrorCode.DEVICE_SUBJECT_NOT_EXIST,
            DeviceErrorCode.DEVICE_SUBJECT_NOT_EXIST.getMessage());

        List<Long> existDeviceIds = deviceSubjectRelationMapper
            .listDeviceIdByOrgIdAndSubjectId(orgId, subjectId);
        // 批量新增增加的关系(上面重置接口有人员数量限制,这里不需要限制?)
        List<Long> incDeviceIds = new ArrayList<>(deviceIds);
        incDeviceIds.removeAll(existDeviceIds);
        if (CollectionUtils.isNotEmpty(incDeviceIds)) {
            BatchIdDto batchIdDto = idgeneratorService
                .getNextIdBatchBytypeName(DeviceSubjectRelation.class.getName(),
                    incDeviceIds.size()).pickDataThrowException();
            List<DeviceSubjectRelation> relations = new ArrayList<>(incDeviceIds.size());
            for (Long deviceId : incDeviceIds) {
                DeviceSubjectRelation relation = new DeviceSubjectRelation();
                relation.setId(batchIdDto.nextId());
                relation.setOrgId(orgId);
                relation.setSubjectId(subjectId);
                relation.setDeviceId(deviceId);
                relation.setType(deviceSubjectDTO.getType());
                relations.add(relation);
            }
            if (CollectionUtils.isNotEmpty(relations)) {
                deviceSubjectRelationMapper.batchInsert(relations);
            }
        }

        // 删除减少关系
        List<Long> decDeviceIds = new ArrayList<>(existDeviceIds);
        decDeviceIds.removeAll(deviceIds);
        if (CollectionUtils.isNotEmpty(decDeviceIds)) {
            deviceSubjectRelationMapper.deleteByCondition(orgId,
                decDeviceIds, Collections.singletonList(subjectId));
        }

        //获取需要重新下发主题的设备Id
        List<Long> changeDeviceIds = getChangeDeviceIds(deviceIds, incDeviceIds, decDeviceIds);
        if (CollectionUtils.isNotEmpty(changeDeviceIds)) {
            List<SimpleDeviceInfoDTO> simpleDeviceInfoDTOS = deviceService
                    .listBaseByIds(changeDeviceIds).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(simpleDeviceInfoDTOS)){
                for (SimpleDeviceInfoDTO simpleDeviceInfoDTO : simpleDeviceInfoDTOS) {
                    if (simpleDeviceInfoDTO!=null && StringUtils.isNotBlank(simpleDeviceInfoDTO.getDeviceSn())){
                        log.info("发送设备主题变更消息，,devicesn:{}",simpleDeviceInfoDTO.getDeviceSn());
                        mqttMessageComponent.sendMqttMessage(simpleDeviceInfoDTO.getDeviceSn(),
                                TransferEventType.DEVICE_SUBJECT_SYNC.getEventType(), null,
                                "DEVICE_SUBJECT_SYNC");
                    }
                }
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 重置一个主题和多台设备的关系
     *
     * @param orgId      机构号
     * @param deviceId   设备Id
     * @param subjectIds 设备主题Id列表
     * @param type 1 壁纸，2屏保
     * @return
     */
    @Transactional
    @Override
    public Boolean resetRelationByDeviceId(Long orgId, Long deviceId, List<Long> subjectIds,Integer type) {
        List<DeviceSubjectRelation> deviceSubjectRelations = listByOrgIdAndDeviceIds(orgId, Collections.singletonList(deviceId), type);
        List<Long> existSubjectIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceSubjectRelations)){
            existSubjectIdList = deviceSubjectRelations.stream().map(DeviceSubjectRelation::getSubjectId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        }
        Collection<Long> addSubjectList = CollectionUtils.subtract(subjectIds, existSubjectIdList);
        Collection<Long> deleteSubjectList = CollectionUtils.subtract(existSubjectIdList,subjectIds);
        if (CollectionUtils.isNotEmpty(deleteSubjectList)) {
            deviceSubjectRelationMapper.deleteByCondition(orgId, Collections.singletonList(deviceId), new ArrayList<>(deleteSubjectList));
        }
        if (CollectionUtils.isNotEmpty(addSubjectList)) {
            deviceSubjectRelationMapper.batchInsert(getDeviceSubjectRelationList(orgId, deviceId, new HashSet<>(addSubjectList), type));
        }
        DeviceInfoDTO deviceInfoDTO = deviceService.getById(deviceId).pickDataThrowException();
        if (deviceInfoDTO!=null){
            log.info("发送设备主题变更消息，deviceid:{},devicesn:{}",deviceInfoDTO.getDeviceId(),deviceInfoDTO.getDeviceSn());
            mqttMessageComponent.sendMqttMessage(deviceInfoDTO.getDeviceSn(),
                    TransferEventType.DEVICE_SUBJECT_SYNC.getEventType(), null,
                    "DEVICE_SUBJECT_SYNC");
        }else{
            log.info("设备已被解绑,deviceId:{}",deviceId);
        }
        return Boolean.TRUE;
    }

    @Transactional
    @Override
    public Boolean resetRelationBySubjectIdList(Long orgId, Map<Long, Long> subjectIdDeviceIdList,List<Long> deviceIds,Integer type) {
        List<DeviceSubjectRelation> deviceSubjectRelations = deviceSubjectRelationMapper.listByOrgIdAndDeviceIds(orgId, deviceIds, type);
        Map<Long, Long> existDeviceSubjectRelationMap = deviceSubjectRelations.stream().collect(Collectors.toMap(DeviceSubjectRelation::getDeviceId, DeviceSubjectRelation::getSubjectId));
        Map<Long, Long> newDeviceSubjectRelationMap = new HashMap<>();
        Map<Long, Long> updateDeviceSubjectRelationMap = new HashMap<>();
        List<Long> removeDeviceSubjectRelationList = new ArrayList<>();
        for (Map.Entry<Long, Long> entry : subjectIdDeviceIdList.entrySet()) {
            Long deviceId = entry.getKey();
            Long subjectId = entry.getValue();
            Long subjectIdExist = existDeviceSubjectRelationMap.get(deviceId);
            if (subjectIdExist==null){
                // 不变的删除
                newDeviceSubjectRelationMap.put(deviceId,subjectId);
            }

            if (subjectIdExist!=null && !subjectIdExist.equals(subjectId)){
                removeDeviceSubjectRelationList.add(subjectIdExist);
                // 不变的删除
                updateDeviceSubjectRelationMap.put(deviceId,subjectId);
            }
        }
        if (!newDeviceSubjectRelationMap.isEmpty()){
            List<DeviceSubjectRelation> deviceSubjectRelationList = getDeviceSubjectRelationList(orgId, newDeviceSubjectRelationMap, type);
            if (CollectionUtils.isNotEmpty(deviceSubjectRelationList)){
                deviceSubjectRelationMapper.batchInsert(deviceSubjectRelationList);
            }
        }

        if (!updateDeviceSubjectRelationMap.isEmpty()){
            Set<Long> deviceIdList = updateDeviceSubjectRelationMap.keySet();
            if (CollectionUtils.isNotEmpty(removeDeviceSubjectRelationList)){
                deviceSubjectRelationMapper.deleteByCondition(orgId,new ArrayList<>(deviceIdList),removeDeviceSubjectRelationList);
            }
            List<DeviceSubjectRelation> deviceSubjectRelationList = getDeviceSubjectRelationList(orgId, updateDeviceSubjectRelationMap, type);
            if (CollectionUtils.isNotEmpty(deviceSubjectRelationList)){
                deviceSubjectRelationMapper.batchInsert(deviceSubjectRelationList);
            }
        }
        Set<Long> all = new HashSet<>();
        Set<Long> newDeviceIdList = newDeviceSubjectRelationMap.keySet();
        Set<Long> updateDeviceIdList = updateDeviceSubjectRelationMap.keySet();
        if (CollectionUtils.isNotEmpty(newDeviceIdList)){
            all.addAll(newDeviceIdList);
        }
        if (CollectionUtils.isNotEmpty(updateDeviceIdList)){
            all.addAll(updateDeviceIdList);
        }

        //获取需要重新下发主题的设备Id
        if (CollectionUtils.isNotEmpty(all)) {
            List<DeviceInfoDTO> deviceInfoList = deviceService
                    .listByIds( new ArrayList<>(all)).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceInfoList)){
                for (DeviceInfoDTO deviceInfo : deviceInfoList) {
                    if (deviceInfo!=null){
                        log.info("发送设备主题变更消息，deviceid:{},devicesn:{}",deviceInfo.getDeviceId(),deviceInfo.getDeviceSn());
                        mqttMessageComponent.sendMqttMessage(deviceInfo.getDeviceSn(),
                                TransferEventType.DEVICE_SUBJECT_SYNC.getEventType(), null,
                                "DEVICE_SUBJECT_SYNC");
                    }
                }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public List<DeviceSubjectRelation> listByOrgIdAndSubjectIds(Long orgId, List<Long> subjectIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(subjectIds), "subjectIds must not be null");
        return deviceSubjectRelationMapper.listByOrgIdAndSubjectIds(orgId, subjectIds);
    }

    @Override
    public List<DeviceSubjectRelation> listByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds,
        Integer type) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        return deviceSubjectRelationMapper.listByOrgIdAndDeviceIds(orgId, deviceIds, type);
    }

    @Override
    public Boolean deleteByOrgIdAndSubjectIds(Long orgId, List<Long> subjectIds) {
        List<DeviceSubjectRelation> subjectRelations = deviceSubjectRelationMapper.listByOrgIdAndSubjectIds(orgId, subjectIds);

        if (CollectionUtils.isNotEmpty(subjectRelations)) {
            List<Long> deviceIdList = subjectRelations.stream()
                    .map(DeviceSubjectRelation::getDeviceId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deviceIdList)) {
                List<DeviceInfoDTO> deviceInfoList = deviceService.listByIds( deviceIdList).pickDataThrowException();
                if (CollectionUtils.isNotEmpty(deviceInfoList)){
                    for (DeviceInfoDTO dto : deviceInfoList) {
                        if (dto!=null){
                            log.info("发送设备主题变更消息，deviceid:{},devicesn:{}",dto.getDeviceId(),dto.getDeviceSn());
                            mqttMessageComponent.sendMqttMessage(dto.getDeviceSn(),
                                    TransferEventType.DEVICE_SUBJECT_SYNC.getEventType(), null,
                                    "DEVICE_SUBJECT_SYNC");
                        }
                    }
                }
            }
        }
        deviceSubjectRelationMapper.deleteByCondition(orgId, new ArrayList<>(), subjectIds);
        return Boolean.TRUE;
    }

    private List<Long> getChangeDeviceIds(List<Long> deviceIds, List<Long> incDeviceIds,
        List<Long> decDeviceIds) {
        Set<Long> changeDeviceIds = new HashSet<>();
        changeDeviceIds.addAll(decDeviceIds);
        changeDeviceIds.addAll(incDeviceIds);
        if (CollectionUtils.isEmpty(changeDeviceIds) && CollectionUtils
            .isEmpty(deviceIds)) {
            changeDeviceIds = new HashSet<>();
        } else {
            changeDeviceIds.addAll(deviceIds);
        }
        return new ArrayList<>(changeDeviceIds);
    }


    public List<DeviceSubjectRelation> getDeviceSubjectRelationList(Long orgId,Map<Long,Long> deviceIdSubjectIdMap,Integer type){
        List<DeviceSubjectRelation> deviceSubjectRelations = new ArrayList<>();
        BatchIdDto batchIdDto = idgeneratorService.getNextIdBatchBytypeName(DeviceAccountManagerImpl.class.getName(), deviceIdSubjectIdMap.size()).pickDataThrowException();
        for (Map.Entry<Long, Long> entry : deviceIdSubjectIdMap.entrySet()) {
            Long deviceId = entry.getKey();
            Long subjectId = entry.getValue();
            DeviceSubjectRelation deviceSubjectRelation = new DeviceSubjectRelation();
            deviceSubjectRelation.setOrgId(orgId);
            deviceSubjectRelation.setId(batchIdDto.nextId());
            deviceSubjectRelation.setDeviceId(deviceId);
            deviceSubjectRelation.setSubjectId(subjectId);
            deviceSubjectRelation.setType(type);
            deviceSubjectRelations.add(deviceSubjectRelation);
        }
        return deviceSubjectRelations;
    }

    public List<DeviceSubjectRelation> getDeviceSubjectRelationList(Long orgId,Long deviceId,Set<Long> subjectIdList,Integer type){
        if (CollectionUtils.isEmpty(subjectIdList)){
            return Collections.emptyList();
        }
        List<DeviceSubjectRelation> deviceSubjectRelations = new ArrayList<>();
        for (Long subjectId : subjectIdList) {
            DeviceSubjectRelation deviceSubjectRelation = new DeviceSubjectRelation();
            deviceSubjectRelation.setOrgId(orgId);
            deviceSubjectRelation.setSubjectId(subjectId);
            deviceSubjectRelation.setDeviceId(deviceId);
            deviceSubjectRelation.setType(type);
            deviceSubjectRelations.add(deviceSubjectRelation);
        }
        return deviceSubjectRelations;
    }


}
