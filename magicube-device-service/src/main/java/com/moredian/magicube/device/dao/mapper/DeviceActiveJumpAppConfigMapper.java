package com.moredian.magicube.device.dao.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.moredian.magicube.device.dao.entity.DeviceActiveJumpAppConfig;

@Mapper
public interface DeviceActiveJumpAppConfigMapper {

	/**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
	List<DeviceActiveJumpAppConfig> listAll();

	/**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
	DeviceActiveJumpAppConfig getById(Long id);

	/**
	 * 根据Appcode查询
	 *
	 * @param appCode 应用id
	 * @return 返回记录，没有返回null
	 */
	DeviceActiveJumpAppConfig getByAppCode(String appCode);

	/**
     * 新增，插入所有字段
     *
     * @param deviceActiveJumpAppConfig 新增的记录
     * @return 返回影响行数
     */
	int insert(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig);
	
	/**
     * 修改所有字段
     *
     * @param deviceActiveJumpAppConfig 修改的记录
     * @return 返回影响行数
     */
	int update(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig);
	
	/**
     * 修改不为null字段
     *
     * @param deviceActiveJumpAppConfig 修改的记录
     * @return 返回影响行数
     */
	int updateSelective(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig);
	
	/**
     * 删除记录
     *
     * @param deviceActiveJumpAppConfig 待删除的记录
     * @return 返回影响行数
     */
	int delete(DeviceActiveJumpAppConfig deviceActiveJumpAppConfig);
	
}