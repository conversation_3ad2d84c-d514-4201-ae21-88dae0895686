package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceWhiteList;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author:dongchao
 * @Description: 设备开启测温白名单mapper
 * @Date: 2020/2/24 21:53
 */
@Mapper
public interface DeviceWhiteListMapper {

    /**
     * 插入
     *
     * @param deviceWhiteList
     * @return
     */
    int insert(DeviceWhiteList deviceWhiteList);

    /**
     * 批量插入
     *
     * @param deviceWhiteList
     * @return
     */
    int insertBatch(@Param("deviceWhiteList") List<DeviceWhiteList> deviceWhiteList);

    /**
     * 修改
     *
     * @param deviceWhiteList
     * @return
     */
    int update(DeviceWhiteList deviceWhiteList);

    /**
     * 根据条件查询
     *
     * @param deviceWhiteList
     * @return
     */
    List<DeviceWhiteList> getDeviceWhiteList(DeviceWhiteList deviceWhiteList);


    /**
     * 设备解绑
     *
     * @return
     */
    int unBind(@Param("deviceSn") String deviceSn);
}
