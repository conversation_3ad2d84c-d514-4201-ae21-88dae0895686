<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceCompositeMapper">
    <resultMap id="resultMap" type="com.moredian.magicube.device.dao.entity.DeviceComposite">
        <id column="device_composite_id" property="deviceCompositeId"/>
        <result column="org_id" property="orgId"/>
        <result column="biz_type" property="bizType"/>
        <result column="device_composite_name" property="deviceCompositeName"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="path" property="path"/>
        <result column="status" property="status"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_device_composite
    </sql>

    <sql id="sql_columns">
        device_composite_id,
        org_id,
        biz_type,
        device_composite_name,
        parent_id,
        code,
        path,
        status,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{deviceCompositeId},
        #{orgId},
        #{bizType},
        #{deviceCompositeName},
        #{parentId},
        #{code},
        #{path},
        #{status},
        now(3),
        now(3)
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceComposite">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceComposite">
        update
        <include refid="sql_table"/>
        set
        <if test="deviceCompositeName !=  null">
            device_composite_name = #{deviceCompositeName},
        </if>
        gmt_modify = now()
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
    </update>

    <delete id="deleteById" parameterType="long">
        delete from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
    </delete>

    <select id="getById" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
        and status = 0
    </select>

    <select id="getByName" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        and device_composite_name = #{deviceCompositeName}
        and status = 0
    </select>

    <select id="listByOrgIdAndBizType" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        and status = 0
    </select>

    <select id="listByDeviceCompositeId" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id in
        <foreach collection="deviceCompositeIdList" index="index" item="deviceCompositeId" open="(" separator=","
                 close=")">
            #{deviceCompositeId}
        </foreach>
        and biz_type = #{bizType}
        and status = 0
    </select>

    <select id="getVirtualRootDeviceComposite" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from <include refid="sql_table"/>
        where org_id = #{orgId}
        and parent_id = 0 and biz_type = #{bizType} and status = 0
        limit 1;
    </select>

    <select id="getMaxDeptCode" resultType="string">
        select max(code)
        FROM <include refid="sql_table"/>
        WHERE org_id = #{orgId}
        and biz_type = #{bizType} and status = 0
    </select>

    <insert id="createComposite" parameterType="com.moredian.magicube.device.dao.entity.DeviceComposite">
        insert into <include refid="sql_table"/>
        (device_composite_id,org_id,biz_type,device_composite_name,gmt_create,gmt_modify,parent_id,`code`,`path`)
        values
        (#{deviceCompositeId},#{orgId},#{bizType},#{deviceCompositeName},now(),now(),#{parentId},#{code},#{path})
    </insert>

    <select id="getCompositeByCodeList" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        and `code` in
        <foreach collection="codeList" index="index" item="code" open="(" separator="," close=")">#{code}</foreach>
        and status = 0
    </select>

    <select id="getCompositeByPath" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        and `path` like concat(#{path},'%') and status = 0
    </select>

    <update id="removeComposite">
        update  <include refid="sql_table"/> set status = #{status}
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId} and status = 0
    </update>

    <select id="listByCondition" resultMap="resultMap" parameterType="com.moredian.magicube.device.dto.composite.QueryDeviceCompositeDTO">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        <if test="parentIds != null and parentIds.size() > 0">
            and `parent_id` in
            <foreach collection="parentIds" index="index" item="parentId" open="("
              separator="," close=")">#{parentId}
            </foreach>
        </if>
        <if test="keyWords != null and keyWords != ''">
            and device_composite_name like concat('%',#{keyWords},'%')
        </if>
        <if test="compositeIds != null and compositeIds.size() > 0">
            and `device_composite_id` in
            <foreach collection="compositeIds" index="index" item="compositeId" open="("
              separator="," close=")">#{compositeId}
            </foreach>
        </if>
        and status = 0
        and code != '00000000'
    </select>

    <select id="listCompositeIdsByParentId" parameterType="long" resultType="long">
        select device_composite_id
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and parent_id = #{parentId}
        and status = 0
    </select>

    <select id="listByParentIds" parameterType="long" resultMap="resultMap">
        select *
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and status = 0
        <if test="parentIds != null and parentIds.size() > 0">
            and `parent_id` in
            <foreach collection="parentIds" index="index" item="parentId" open="("
              separator="," close=")">#{parentId}
            </foreach>
        </if>
    </select>

    <select id="getCompositeByPathBatch" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        <if test="paths != null and paths.size() > 0">
            and (
            <foreach collection="paths" item="path" separator=" OR ">
                `path` like concat(#{path},'%')
            </foreach>
            )
        </if>
        and status = 0
    </select>

    <!-- 可查已经删除的设备组 -->
    <select id="getCompositeByIdWithDelete" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from <include refid="sql_table"/>
        where org_id = #{orgId}
        and device_composite_id = #{deviceCompositeId}
    </select>

    <select id="getCompositeByPathWithDelete" resultMap="resultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_id = #{orgId}
        and biz_type = #{bizType}
        and `path` like concat(#{path},'%')
    </select>
</mapper>