package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_app_version")
public class AppVersion extends TimedEntity {

    private static final long serialVersionUID = -5087845692081429705L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 系统类型 1安卓 2苹果
     */
    private Integer systemType;

    /**
     * 设备app类型
     */
    private Integer appType;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 版本名
     */
    private String versionName;

    /**
     * 版本描述
     */
    private String versionDesc;

    /**
     * 安装文件url
     */
    private String appUrl;

    /**
     * 是否立即升级
     */
    private Boolean isActive;

    /**
     * 是否需要强制更新
     */
    private Boolean isEnforceUpdate;

    /**
     * 删除标识
     */
    private Boolean isDelFlag;

    /**
     * 创建人ID
     */
    private Long userCreate;

    /**
     * 上电强制升级：默认0-否，1-是
     */
    private Boolean enforcementUpgrade;
}