package com.moredian.magicube.device.dao.mapper;
import java.util.Collection;

import com.moredian.magicube.device.dao.entity.DeviceOnlineState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceOnlineStateMapper {

    void insert(DeviceOnlineState deviceOnlineState);

    DeviceOnlineState selectByDeviceSn(@Param("deviceSn") String deviceSn);

    List<DeviceOnlineState> selectByDeviceSnList(@Param("deviceSnCollection")Collection<String> deviceSnCollection);



    List<DeviceOnlineState> selectByOrgIdAndDeviceId(@Param("orgId") Long orgId, @Param("deviceIdList") List<Long> deviceIdList);

    void updateState(DeviceOnlineState deviceOnlineState);
}
