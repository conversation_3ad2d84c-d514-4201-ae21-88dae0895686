package com.moredian.magicube.device.manager;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.IotDeviceDTO;
import com.moredian.magicube.device.dto.device.PageQueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.iot.IotDeviceListDTO;
import com.moredian.magicube.device.dto.device.iot.PageQueryIotDeviceListDTO;

import java.util.List;

/**
 * Iot设备模块
 *
 * <AUTHOR>
 */
public interface IotDeviceManager {

    /**
     * 查询iot设备列表
     *
     * @param dto 查询Iot设备条件
     */
    List<Device> listIotDevice(QueryIotDeviceDTO dto);

    /**
     * 条件统计设备列表
     */
    List<Device> listDeviceByCondition(QueryIotDeviceDTO dto);

    /**
     * 根据条件分页查询Iot设备信息列表
     *
     * @param dto 查询条件
     * @return
     */
    Pagination<Device> listByConditionPage(PageQueryIotDeviceDTO dto);

    /**
     * 查询Iot设备详情
     *
     * @param orgId    机构Id
     * @param deviceId 设备Id
     * @return
     */
    Device getById(Long orgId, Long deviceId);

    /**
     * 条件查询iot设备
     */
    Pagination<IotDeviceListDTO> listByConditionPage(PageQueryIotDeviceListDTO dto);
}