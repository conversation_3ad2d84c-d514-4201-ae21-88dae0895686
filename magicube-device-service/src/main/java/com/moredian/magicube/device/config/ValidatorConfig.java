package com.moredian.magicube.device.config;

import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validation;
import javax.validation.Validator;

import lombok.extern.slf4j.Slf4j;

import org.aopalliance.aop.Advice;
import org.aopalliance.intercept.MethodInvocation;
import org.hibernate.validator.HibernateValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.validation.beanvalidation.MethodValidationInterceptor;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.BizException;
import com.moredian.magicube.common.util.ExceptionPrintMessageUtil;
import com.moredian.magicube.common.valid.BeeValid;

/**
 * 方法校验器
 * <AUTHOR>
 * @time 2019-11-8 14:04:24
 */
@Configuration
@Slf4j
public class ValidatorConfig {

    @Bean
    @Primary
    public BeeMethodValidationPostProcessor methodValidationPostProcessor() {
        BeeMethodValidationPostProcessor postProcessor = new BeeMethodValidationPostProcessor();
        postProcessor.setValidatedAnnotationType(BeeValid.class);
        postProcessor.setBeforeExistingAdvisors(true);
        postProcessor.setProxyTargetClass(true);
        postProcessor.setValidator(getValidator());
        return postProcessor;
    }


    /**
     * 实例化一个校验器
     * 只要实现 Validator接口的校验器都可以使用 当前项目使用Hibernate  ValidatorImpl
     * @return
     */
    public Validator getValidator() {
        return Validation.byProvider(HibernateValidator.class)
                .configure().failFast(false)         //立即返回
                .buildValidatorFactory().getValidator();
    }


    /**
     * @description: 处理器
     * @author: gongchang
     * @time: 2019/11/8 11:46
     */
    public static class BeeMethodValidationPostProcessor extends MethodValidationPostProcessor {
        @Override
        protected Advice createMethodValidationAdvice(Validator validator) {
            return (validator != null ?
                    new BeeMethodValidationInterceptor(validator) : new BeeMethodValidationInterceptor());
        }
    }

    /**
     * 校验拦截器
     */
    public static class BeeMethodValidationInterceptor extends MethodValidationInterceptor {
        public BeeMethodValidationInterceptor(){}
        public BeeMethodValidationInterceptor(Validator validator) {
            super(validator);
        }

        @Override
        public Object invoke(MethodInvocation invocation) throws Throwable {
            Object object = null;
            try {
                object = super.invoke(invocation);
            }catch (ConstraintViolationException ce){
                //支持返回多个错误合并 当前项目为立即返回 只有一个错误
                StringBuilder msg = new StringBuilder();
                Set<ConstraintViolation<?>> constraintViolations =  ce.getConstraintViolations();
                for (ConstraintViolation violation : constraintViolations){
                    msg.append(violation.getMessage());
                }
                BizAssert.isTrue(false, msg.toString());
            }catch (BizException be){
                throw be;
            }catch (Exception e){
                log.error("exception toString and track space : {}", "\r\n" + e);
                log.error(ExceptionPrintMessageUtil.errorTrackSpace(e));
                BizAssert.isTrue(false, e.getMessage() == null ? "unknown error" : e.getMessage());
            }
            return object;
        }
    }
}

