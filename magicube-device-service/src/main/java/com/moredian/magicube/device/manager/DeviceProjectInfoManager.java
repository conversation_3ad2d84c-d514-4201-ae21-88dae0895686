package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.DeviceProjectCustomParam;
import com.moredian.magicube.device.dto.project.DeviceProjectInfoResponse;
import com.moredian.magicube.device.dto.project.PullDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.ReportDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateProjectInfoResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/10
 */
public interface DeviceProjectInfoManager {

    /**
     * 设备上报项目信息
     *
     * @param request
     * @return
     */
    Long reportProjectInfo(ReportDeviceProjectInfoRequest request);

    /**
     * 设备拉取项目信息
     *
     * @param request
     * @return
     */
    DeviceProjectInfoResponse pullProjectInfo(PullDeviceProjectInfoRequest request);

    /**
     * 根据条件查询设备项目信息
     *
     * @param request
     * @return
     */
    List<DeviceProjectInfoResponse> findDeviceProjectInfo(QueryDeviceProjectInfoRequest request);

    /**
     * 修改设备项目信息并下发mqtt消息
     *
     * @param request
     * @return
     */
    UpdateProjectInfoResponse updateProjectInfo(UpdateDeviceProjectInfoRequest request);

    /**
     * 查询设备的项目信息定制化参数
     *
     * @param deviceSn
     * @return
     */
    DeviceProjectCustomParam queryDeviceProjectCustomParam(String deviceSn);
}
