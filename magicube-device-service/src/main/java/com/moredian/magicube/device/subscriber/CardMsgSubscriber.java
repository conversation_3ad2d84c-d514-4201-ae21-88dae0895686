package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.UUID;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.ControllerErrorCode;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.common.model.msg.CardStatusChangeMsg;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/12/7
 */
@Slf4j
@Component
public class CardMsgSubscriber {

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;


    @Subscribe
    public void subPersonCardChange(CardStatusChangeMsg msg) {
        log.info("收到人员卡信息变更消息syncPersonCardInfo: {}",JsonUtils.toJson(msg));
        // 获取需要通知的设备
        List<Device> devices = deviceManager.listByOrgIdAndTypes(msg.getOrgId(),
                                                                 deviceTypePropertyManager.getDeviceTypeByPropertyKey(
                                                                     DeviceTypeConstants.ENABLE_CARD_DEVICE_TYPE_LIST));
        if (CollectionUtils.isNotEmpty(devices)) {
            for (Device device : devices) {
                try {
                    // 发送命令
                    String deviceSn = device.getDeviceSn();
                    TransferMessageInfo<String> transferMessageInfo = new TransferMessageInfo<>();
                    transferMessageInfo.setEventType(com.moredian.magicube.common.enums.TransferEventType.MEMBERCARD_SYNC.getValue());
                    transferMessageInfo.setSeverity(3);
                    transferMessageInfo.setSeqId(UUID.random19() + com.moredian.magicube.common.enums.TransferEventType.MEMBERCARD_SYNC.getDesc());
                    transferMessageInfo.setMessage(com.moredian.magicube.common.enums.TransferEventType.MEMBERCARD_SYNC.getDesc());
                    String jsonStr = JsonUtils.toJson(transferMessageInfo);
                    String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
                    TransferRequest transferRequest = new TransferRequest();
                    transferRequest.setSerialNumber(deviceSn);
                    transferRequest.setBody(base64Message);
                    transferRequest.setCommand(0);
                    ServiceResponse<TransferResponse> response = hubControlServiceV1.transfer(transferRequest);
                    if (!response.isSuccess() && ControllerErrorCode.DEVICE_OFFLINE.getCode().equals(response.getErrorContext().getCode())) {
                        log.info("Notify device syncPersonCardInfo，设备已离线，本次不发送通知，[org={},deviceSn={},deviceId={}]", msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                        continue;
                    }
                    TransferResponse result = response.pickDataThrowException();
                    log.info("Notify device syncPersonCardInfo，[org={},deviceSn={},deviceId={}]", msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                } catch (Exception e) {
                    log.error("Notify device syncPersonCardInfo failed. [org={},deviceSn={},deviceId={}]，errorMsg={}", msg.getOrgId(), device.getDeviceSn(), device.getDeviceId(), e.getMessage());
                }
            }
        }
    }
}
