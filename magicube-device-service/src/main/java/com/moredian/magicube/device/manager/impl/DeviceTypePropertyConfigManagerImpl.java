package com.moredian.magicube.device.manager.impl;

import static com.moredian.magicube.device.constant.RedisConstants.DEVICE_TYPE_PROPERTY_CONFIG;
import cn.hutool.core.util.ObjectUtil;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.DeviceTypePropertyConfig;
import com.moredian.magicube.device.dao.mapper.DeviceTypePropertyConfigMapper;
import com.moredian.magicube.device.manager.DeviceTypePropertyConfigManager;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description：设备属性前端展示配置
 * @date ：2024/07/25 16:05
 */
@Service
@RequiredArgsConstructor
public class DeviceTypePropertyConfigManagerImpl implements DeviceTypePropertyConfigManager {

    private final RedissonCacheComponent redissonCacheComponent;
    private final DeviceTypePropertyConfigMapper deviceTypePropertyConfigMapper;

    @PostConstruct
    public void setCache(){
        refreshCache(null);
    }

    @Override
    public void refreshCache(Integer deviceType) {
        DeviceTypePropertyConfig entity = DeviceTypePropertyConfig.builder()
            .deviceType(deviceType).build();

        List<DeviceTypePropertyConfig> configList = deviceTypePropertyConfigMapper.listByEntity(entity);
        if (ObjectUtil.isNotEmpty(configList)) {
            Map<Integer, List<DeviceTypePropertyConfig>> configMap = configList.stream()
                .collect(Collectors.groupingBy(DeviceTypePropertyConfig::getDeviceType));
            for (Entry<Integer, List<DeviceTypePropertyConfig>> config : configMap.entrySet()) {
                String key = RedisConstants.getKey(DEVICE_TYPE_PROPERTY_CONFIG, config.getKey());
                redissonCacheComponent.setObjectCache(key, config.getValue());
            }
        }
    }
}
