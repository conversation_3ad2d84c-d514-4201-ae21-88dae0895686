package com.moredian.magicube.device.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.DeviceCompositeChangeEnums;
import com.moredian.magicube.common.model.msg.DeviceCompositeChangeMsg;
import com.moredian.magicube.core.org.service.OrgConfigService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.constant.DeviceCompositeBizType;
import com.moredian.magicube.device.constant.DeviceCompositeChangeMsgSate;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceComposite;
import com.moredian.magicube.device.dao.entity.DeviceCompositeItem;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeItemMapper;
import com.moredian.magicube.device.dao.mapper.DeviceCompositeMapper;
import com.moredian.magicube.device.dto.composite.*;
import com.moredian.magicube.device.dto.device.DeviceDTO;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceDTO;
import com.moredian.magicube.device.enums.DeleteStatus;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.CompositeCodeGenHelper;
import com.moredian.magicube.device.manager.DeviceCompositeManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.bo.TreeNode;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceTypePropertyService;
import com.moredian.magicube.device.utils.BizUtil;
import com.moredian.magicube.device.utils.DeviceCompositeBizUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * @Auther: _AF
 * @Date: 2020/11/26 16:49
 * @Description: 设备组合
 */
@Service
@Slf4j
public class DeviceCompositeManagerImpl implements DeviceCompositeManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private DeviceCompositeMapper deviceCompositeMapper;

    @Autowired
    private DeviceCompositeItemMapper deviceCompositeItemMapper;

    @Autowired
    private DeviceManager deviceManager;

    @SI
    private DeviceService deviceService;

    @Resource
    private CompositeCodeGenHelper compositeCodeGenHelper;

    @SI
    private OrgConfigService orgConfigService;

    @SI
    private DeviceTypePropertyService deviceTypePropertyService;

    /**
     * 设备组分组层级限制
     */
    @Value("${device.composite.level.limit:5}")
    private Integer compositeLevelLimit;

    private final static String ORG_CONFIG_KEY_FOR_COMPOSITE_LEVEL_LIMIT = "compositeLevelLimit";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceCompositeDTO insertOrUpdate(InsertDeviceCompositeDTO dto) {
        log.info("DeviceCompositeManagerImpl.insertOrUpdate param {}", JSON.toJSONString(dto));
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getBizType(), "bizType must not be null");
        Long deviceCompositeId;
        //是否修改
        boolean updateFlag = false;
        if (dto.getDeviceCompositeId() == null) {
            BizAssert
                .notBlank(dto.getDeviceCompositeName(), "deviceCompositeName must not be null");
            deviceCompositeId = insertDeviceComposite(dto.getOrgId(), dto.getBizType(),
                dto.getDeviceCompositeName());
        } else {
            deviceCompositeId = dto.getDeviceCompositeId();
            DeviceComposite deviceComposite = deviceCompositeMapper.getById(dto.getOrgId(),
                deviceCompositeId);
            BizAssert.notNull(deviceComposite, DeviceErrorCode.DEVICE_COMPOSITE_NOT_EXIST,
                DeviceErrorCode.DEVICE_COMPOSITE_NOT_EXIST.getMessage());
            //改名
            if (StringUtils.isNotBlank(dto.getDeviceCompositeName())) {
                //修改组名
                //修改组信息-组名
                List<DeviceComposite> deviceCompositeByNames = deviceCompositeMapper
                    .getByName(dto.getOrgId(), dto.getBizType(), dto.getDeviceCompositeName());
                // 如果不是本组且和本组是同一个父组，那么就要报错，设备组名称不能重复
                for (DeviceComposite deviceCompositeByName : deviceCompositeByNames) {
                    if (deviceCompositeByName != null && !deviceCompositeByName
                        .getDeviceCompositeId().equals(deviceCompositeId) && Objects.equals(
                        deviceCompositeByName.getParentId(), deviceComposite.getParentId())) {
                        ExceptionUtils.throwException(DeviceErrorCode.DEVICE_COMPOSITE_NAME_EXIST,
                            DeviceErrorCode.DEVICE_COMPOSITE_NAME_EXIST.getMessage());
                    }
                }
                updateFlag = true;
            }
        }

        DeviceComposite deviceComposite = getDeviceCompositeById(dto.getOrgId(), deviceCompositeId);
        BizAssert.notNull(deviceComposite, DeviceErrorCode.DEVICE_COMPOSITE_NOT_EXIST,
            DeviceErrorCode.DEVICE_COMPOSITE_NOT_EXIST.getMessage());

        if (updateFlag) {
            //修改组信息-组名
            updateDeviceComposite(dto);
        }

        //插入设备组，先删除之前的设备组
        deviceCompositeItemMapper.deleteByOrgIdAndDeviceCompositeId(dto.getOrgId(),
            deviceCompositeId);
        //插入设备组
        addDevice(dto.getOrgId(), deviceCompositeId, dto.getDeviceIdList(), dto.getBizType(), true);
        DeviceCompositeDTO deviceCompositeDTO = new DeviceCompositeDTO();
        BeanUtils.copyProperties(deviceComposite, deviceCompositeDTO);
        if (CollectionUtils.isNotEmpty(dto.getDeviceIdList())) {
            List<DeviceCompositeItemDTO> itemList = getItemListByCompositeId(dto.getOrgId(),
                dto.getDeviceCompositeId(), Boolean.FALSE);
            deviceCompositeDTO.setItems(itemList);
        }
        //创建/更新组发送消息
        sendDeviceCompositeChangeMsg(dto.getOrgId(), deviceCompositeId, dto.getBizType(),
            dto.getDeviceCompositeId() == null ? DeviceCompositeChangeMsgSate.COMPOSITE_ADD
                : DeviceCompositeChangeMsgSate.COMPOSITE_UPDATE);
        //返回信息
        return deviceCompositeDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addDeviceInc(DeviceBindCompositeDTO dto) {
        log.info("DeviceCompositeManagerImpl.addDeviceInc param {}", JSON.toJSONString(dto));
        BizAssert.notNull(dto.getOrgId(), DeviceErrorCode.DEVICE_ORG_ID_REQUIRED, DeviceErrorCode.DEVICE_ORG_ID_REQUIRED.getMessage());
        BizAssert.isTrue(CollectionUtils.isNotEmpty(dto.getRelationships()), DeviceErrorCode.DEVICE_RELATIONSHIPS_REQUIRED, DeviceErrorCode.DEVICE_RELATIONSHIPS_REQUIRED.getMessage());

        List<DeviceBindCompositeRelationDTO> relationships = dto.getRelationships();

        Map<Long/*compositeId*/, List<Long>/*deviceIds*/> listMap = relationships.stream().collect(
            Collectors.groupingBy(DeviceBindCompositeRelationDTO::getCompositeId,
                Collectors.mapping(DeviceBindCompositeRelationDTO::getDeviceId, Collectors.toList())));

        Set<Long> compositeIds = listMap.keySet();
        List<DeviceCompositeItem> compositeItems = deviceCompositeItemMapper.listByCompositeIds(
            dto.getOrgId(), new ArrayList<>(compositeIds));

        List<DeviceComposite> deviceComposites = deviceCompositeMapper.listByDeviceCompositeId(
            dto.getOrgId(), new ArrayList<>(compositeIds), DeviceCompositeBizType.COMMON);
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceComposites),"要绑定的设备组已经不存在了");
        Map<Long, DeviceComposite> deviceCompositeMap = deviceComposites.stream()
            .collect(Collectors.toMap(DeviceComposite::getDeviceCompositeId, v -> v));

        if (CollectionUtils.isNotEmpty(compositeItems)){
            // 如果这些设备组已经绑定了设备，就要检验设备组中是否已经绑定了这个设备
            Map<Long, List<Long>> listMapHas = compositeItems.stream()
                .collect(Collectors.groupingBy(DeviceCompositeItem::getDeviceCompositeId,
                    Collectors.mapping(DeviceCompositeItem::getDeviceId, Collectors.toList())));

            listMap.forEach((k,v)->{
                // 设备组中已经存在的设备
                List<Long> deviceIdsHas = listMapHas.get(k);
                // 如果要添加的设备id已经存在了，就全部失败
                boolean result = deviceIdsHas.stream().anyMatch(v::contains);
                BizAssert.isTrue(!result,"设备已经存在于要绑定的设备组中");
            });
        }

        listMap.forEach((k,v)->{
            DeviceComposite composite = deviceCompositeMap.get(k);
            //插入设备组
            addDevice(dto.getOrgId(), k,v, composite.getBizType(), false);
        });
        return Boolean.TRUE;
    }

    @Override
    public Long insertOrUpdateNoMutex(InsertDeviceCompositeDTO dto) {
        log.info("DeviceCompositeManagerImpl.addDevice param {}", JSON.toJSONString(dto));
        BizAssert.notNull(dto.getOrgId(), "机构id不能为空");
        BizAssert.notNull(dto.getBizType(), "业务类型不能为空");
        Long deviceCompositeId = null;
        //是否修改
        //boolean updateFlag = false;
        if (dto.getDeviceCompositeId() == null) {
            BizAssert.notBlank(dto.getDeviceCompositeName(), "设备组名不能为空");
            // 添加设备到组的时候，如果组不存在，就默认创建一个组
            deviceCompositeId = createDeviceComposite(dto.getOrgId(), dto.getBizType(),
                dto.getDeviceCompositeName(), dto.getParentId());
        } else {
            // 组存在了就去取出设备组的id就好
            deviceCompositeId = dto.getDeviceCompositeId();
        }
        // 获取设备组的实体信息
        DeviceComposite deviceComposite = getDeviceCompositeById(dto.getOrgId(), deviceCompositeId);
        if (deviceComposite == null) {
            ExceptionUtils.throwException(DeviceErrorCode.DEVICE_COMPOSITE_NOT_EXIST,
                DeviceErrorCode.DEVICE_COMPOSITE_NOT_EXIST.getMessage());
        }
        if (StringUtils.isNotBlank(dto.getDeviceCompositeName())
            && dto.getDeviceCompositeId() != null) {
            //修改组名
            //修改组信息-组名
            List<DeviceComposite> deviceCompositeByNames = deviceCompositeMapper.getByName(dto.getOrgId(),
                dto.getBizType(), dto.getDeviceCompositeName());
            // 如果不是本组且和本组是同一个父组，那么就要报错，设备组名称不能重复
            for (DeviceComposite deviceCompositeByName : deviceCompositeByNames) {
                if (deviceCompositeByName != null && !deviceCompositeByName.getDeviceCompositeId()
                    .equals(deviceCompositeId) && Objects.equals(
                    deviceCompositeByName.getParentId(), deviceComposite.getParentId())) {
                    ExceptionUtils.throwException(DeviceErrorCode.DEVICE_COMPOSITE_NAME_EXIST,
                        DeviceErrorCode.DEVICE_COMPOSITE_NAME_EXIST.getMessage());
                }
            }
            updateDeviceComposite(dto);
        }

        //插入设备组，先删除之前的设备组
        deviceCompositeItemMapper.removeItem(dto.getOrgId(), deviceCompositeId, null,
            DeleteStatus.DELETED.getCode());
        //插入设备组
        addDevice(dto.getOrgId(), deviceCompositeId, dto.getDeviceIdList(), dto.getBizType(),
            false);
        //创建/更新组发送消息
        sendDeviceCompositeChangeMsg(dto.getOrgId(), deviceCompositeId, dto.getBizType(),
            dto.getDeviceCompositeId() == null ? DeviceCompositeChangeMsgSate.COMPOSITE_ADD
                : DeviceCompositeChangeMsgSate.COMPOSITE_UPDATE);
        //返回信息
        return deviceCompositeId;
    }

    @Override
    public List<DeviceCompositeDTO> listByOrgIdAndBizType(Long orgId, Integer bizType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(bizType, "bizType must not be null");
        List<DeviceCompositeDTO> deviceCompositeDtoList = new ArrayList<>();
        List<DeviceComposite> deviceCompositeList = deviceCompositeMapper
            .listByOrgIdAndBizType(orgId, bizType);
        if (CollectionUtils.isNotEmpty(deviceCompositeList)) {
            List<Long> deviceCompositeIds = new ArrayList<>();
            Map<Long, DeviceCompositeDTO> deviceCompositeDTOMap = new HashMap<>();
            for (DeviceComposite deviceComposite : deviceCompositeList) {
                //id集合
                deviceCompositeIds.add(deviceComposite.getDeviceCompositeId());
                //dto集合
                DeviceCompositeDTO deviceCompositeDto = new DeviceCompositeDTO();
                BeanUtils.copyProperties(deviceComposite, deviceCompositeDto);
                //map
                deviceCompositeDTOMap
                    .put(deviceCompositeDto.getDeviceCompositeId(), deviceCompositeDto);
            }
            //查询条目.对条目进行分类
            List<DeviceCompositeItem> itemList = deviceCompositeItemMapper
                .listByOrgIdAndDeviceCompositeIds(orgId, deviceCompositeIds);
            if (CollectionUtils.isNotEmpty(itemList)) {
                //查询所有的设备信息
                List<Long> deviceIdList = new ArrayList<>();
                for (DeviceCompositeItem item : itemList) {
                    deviceIdList.add(item.getDeviceId());
                }
                List<Device> deviceList = deviceManager.listByIds(deviceIdList);
                Map<Long, Device> deviceMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(deviceList)) {
                    for (Device device : deviceList) {
                        deviceMap.put(device.getDeviceId(), device);
                    }
                }

                for (DeviceCompositeItem item : itemList) {
                    Long deviceCompositeId = item.getDeviceCompositeId();
                    Long deviceId = item.getDeviceId();
                    Device device = deviceMap.get(deviceId);
                    if (device != null) {
                        //设备存在。
                        DeviceCompositeItemDTO deviceCompositeItemDto = new DeviceCompositeItemDTO();
                        deviceCompositeItemDto.setDeviceId(deviceId);
                        deviceCompositeItemDto.setDeviceName(device.getDeviceName());
                        deviceCompositeItemDto.setDeviceType(device.getDeviceType());
                        DeviceCompositeDTO deviceCompositeDto = deviceCompositeDTOMap
                            .get(deviceCompositeId);
                        if (deviceCompositeDto != null) {
                            List<DeviceCompositeItemDTO> deviceCompositeItemDtoList = deviceCompositeDto
                                .getItems();
                            if (deviceCompositeItemDtoList == null) {
                                deviceCompositeItemDtoList = new ArrayList<>();
                                deviceCompositeDto.setItems(deviceCompositeItemDtoList);
                            }
                            deviceCompositeItemDtoList.add(deviceCompositeItemDto);
                            deviceCompositeDTOMap.put(deviceCompositeId, deviceCompositeDto);
                        }
                    }
                }
            }
            for (Map.Entry<Long, DeviceCompositeDTO> entry : deviceCompositeDTOMap.entrySet()) {
                deviceCompositeDtoList.add(entry.getValue());
            }
        }
        return deviceCompositeDtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(DeleteDeviceCompositeDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getDeviceCompositeId(), "deviceCompositeId must not be null");
        DeviceComposite deviceComposite = deviceCompositeMapper.getById(dto.getOrgId(),
            dto.getDeviceCompositeId());
        if (deviceComposite != null) {
            // 1、找出当前设备组的所有子组
            List<DeviceComposite> composite = Optional.ofNullable(this.findAllSubDeviceComposite(
                dto.getOrgId(), deviceComposite.getDeviceCompositeId())).orElse(new ArrayList<>());
            composite.add(deviceComposite);

            // 循环删除设备组以及子组信息
            for (DeviceComposite deviceCompositeTemp : composite) {
                deleteDeviceCompositeAndSendMsg(deviceCompositeTemp.getOrgId(),
                    deviceCompositeTemp.getDeviceCompositeId());
            }
            //发送消息,只发最上节点的消息
            sendDeviceCompositeChangeMsg(deviceComposite.getOrgId(), deviceComposite.getDeviceCompositeId(), deviceComposite.getBizType(),
                DeviceCompositeChangeMsgSate.COMPOSITE_DELETE);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<Long> listBindDeviceIdByOrgIdAndBizType(Long orgId, Integer bizType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(bizType, "bizType must not be null");
        List<Long> deviceIdList = new ArrayList<>();
        //查询出所有分组
        List<DeviceComposite> deviceCompositeList = deviceCompositeMapper
            .listByOrgIdAndBizType(orgId, bizType);
        if (CollectionUtils.isNotEmpty(deviceCompositeList)) {
            List<Long> deviceCompositeIds = new ArrayList<>();
            for (DeviceComposite deviceComposite : deviceCompositeList) {
                deviceCompositeIds.add(deviceComposite.getDeviceCompositeId());
            }
            List<DeviceCompositeItem> items = deviceCompositeItemMapper
                .listByOrgIdAndDeviceCompositeIds(orgId, deviceCompositeIds);
            if (CollectionUtils.isNotEmpty(items)) {
                Set<Long> deviceIdSet = new HashSet<>();
                for (DeviceCompositeItem deviceCompositeItem : items) {
                    deviceIdSet.add(deviceCompositeItem.getDeviceId());
                }
                deviceIdList = new ArrayList<>(deviceIdSet);
            }
        }
        return deviceIdList;
    }

    @Override
    public Boolean deleteCompositeItemByDeviceId(Long orgId, Long deviceId, Integer bizType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(deviceId, "deviceId must not be null");
        BizAssert.notNull(bizType, "bizType must not be null");
        //先查询在删除
        List<Long> deviceIdList = new ArrayList<>();
        deviceIdList.add(deviceId);
        List<DeviceCompositeItem> deviceCompositeItems = deviceCompositeItemMapper
            .listByDeviceIds(orgId, deviceIdList, bizType);
        if (CollectionUtils.isNotEmpty(deviceCompositeItems)) {
            Set<Long> deviceCompositeIdSet = new HashSet<>();
            for (DeviceCompositeItem deviceCompositeItem : deviceCompositeItems) {
                deviceCompositeIdSet.add(deviceCompositeItem.getDeviceCompositeId());
            }
            deviceCompositeItemMapper.deleteByDeviceId(orgId, deviceId, bizType);
            //发送消息
            for (Long deviceCompositeId : deviceCompositeIdSet) {
                sendDeviceCompositeChangeMsg(orgId, deviceCompositeId, bizType,
                    DeviceCompositeChangeEnums.COMPOSITE_UPDATE.getValue());
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public List<DeviceCompositeNameListDTO> findDeviceCompositeNameList(Long orgId,
        List<Long> deviceIdList, Integer bizType, List<Long> compositeIds) {
        List<DeviceCompositeItem> itemList = deviceCompositeItemMapper.listByDeviceIds(orgId, deviceIdList, bizType);
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        List<Long> existCompositeIds = itemList.stream().map(DeviceCompositeItem::getDeviceCompositeId).distinct().collect(
            Collectors.toList());
        List<Long> deviceCompositeIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(compositeIds)) {
            //查询compositeIds所有的子节点和自己本身
           List<DeviceComposite> allComposites = findAllSubDeviceCompositeBatch(orgId, compositeIds);
           if (CollectionUtils.isNotEmpty(allComposites)) {
               List<Long> allCompositeIds = allComposites.stream().map
                   (DeviceComposite::getDeviceCompositeId).distinct().collect(Collectors.toList());
               if (CollectionUtils.isNotEmpty(allCompositeIds)) {
                   allCompositeIds.addAll(compositeIds);
               } else {
                   allCompositeIds = compositeIds;
               }
               //剔除没有权限的设备组id
               for (Long existCompositeId : existCompositeIds) {
                   if (allCompositeIds.contains(existCompositeId)) {
                       deviceCompositeIds.add(existCompositeId);
                   }
               }
           }
        } else {
            deviceCompositeIds = existCompositeIds;
        }
        if (CollectionUtils.isEmpty(deviceCompositeIds)) {
            return  Collections.emptyList();
        }
        List<DeviceComposite> deviceComposites = deviceCompositeMapper.listByDeviceCompositeId(orgId, deviceCompositeIds, bizType);
        Map<Long, DeviceComposite> deviceCompositeMap = deviceComposites.stream().collect(Collectors.toMap(DeviceComposite::getDeviceCompositeId, deviceComposite -> deviceComposite));
        Map<Long, List<DeviceCompositeItem>> collectMap = itemList.stream().collect(Collectors.groupingBy(DeviceCompositeItem::getDeviceId));
        List<DeviceCompositeNameListDTO> list = Lists.newArrayList();
        for (Entry<Long, List<DeviceCompositeItem>> entry : collectMap.entrySet()) {
            DeviceCompositeNameListDTO deviceCompositeNameListDto = new DeviceCompositeNameListDTO();
            deviceCompositeNameListDto.setOrgId(orgId);
            deviceCompositeNameListDto.setDeviceId(entry.getKey());
            List<DeviceCompositeItem> compositeItems = entry.getValue();
            List<Long> collect = compositeItems.stream().map(DeviceCompositeItem::getDeviceCompositeId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                List<DeviceCompositeDTO> dcs = new ArrayList<>();
                for (Long compositeId : collect) {
                    DeviceComposite deviceComposite = deviceCompositeMap.get(compositeId);
                    if (deviceComposite != null) {
                        DeviceCompositeDTO deviceCompositeDTO = new DeviceCompositeDTO();
                        BeanUtils.copyProperties(deviceComposite, deviceCompositeDTO);
                        dcs.add(deviceCompositeDTO);
                    }
                }
                if (CollectionUtils.isNotEmpty(dcs)) {
                    deviceCompositeNameListDto.setCompositeNameList(dcs);
                    list.add(deviceCompositeNameListDto);
                }
            }
        }
        return list;
    }

    @Override
    public CompositeAndDeviceInfoDTO listByCondition(QueryDeviceCompositeDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        CompositeAndDeviceInfoDTO compositeAndDeviceInfoDTO = new CompositeAndDeviceInfoDTO();
        compositeAndDeviceInfoDTO.setOrgId(dto.getOrgId());
        //如果模糊查询的话，不需要根据父组id查询
        if (StringUtils.isNotBlank(dto.getKeyWords())) {
            dto.setParentId(null);
            List<DeviceComposite> deviceComposites = findAllSubDeviceCompositeBatch(
                dto.getOrgId(), dto.getParentIds());
            if (CollectionUtils.isNotEmpty(deviceComposites)) {
                List<Long> parentCompositeIds = deviceComposites.stream().map(DeviceComposite::
                    getDeviceCompositeId).distinct().collect(Collectors.toList());
                dto.getParentIds().addAll(parentCompositeIds);
            }
        }
        List<DeviceComposite> finalDeviceComposites = new ArrayList<>();
        if (dto.getCompositeFlag() == null || dto.getCompositeFlag()) {
            //查询compositeIds下所有的子设备组id列表
            if (CollectionUtils.isNotEmpty(dto.getCompositeIds())) {
                List<DeviceComposite> deviceComposites = findAllSubDeviceCompositeBatch(
                    dto.getOrgId(), dto.getCompositeIds());
                if (CollectionUtils.isNotEmpty(deviceComposites)) {
                    List<Long> compositeIds = deviceComposites.stream().map(DeviceComposite::
                        getDeviceCompositeId).distinct().collect(Collectors.toList());
                    dto.getCompositeIds().addAll(compositeIds);
                }
            }
            finalDeviceComposites = deviceCompositeMapper.listByCondition(dto);
        }
        if (CollectionUtils.isNotEmpty(finalDeviceComposites)) {
            //查询是否存在子组
            List<Long> compositeIds = finalDeviceComposites.stream()
                .map(DeviceComposite::getDeviceCompositeId).collect(Collectors.toList());
            List<DeviceComposite> deviceComposites = deviceCompositeMapper
                .listByParentIds(dto.getOrgId(), compositeIds);
            Map<Long, List<DeviceComposite>> deviceCompositeMap = new HashMap<>(deviceComposites.size());
            if (CollectionUtils.isNotEmpty(deviceComposites)) {
                deviceCompositeMap = deviceComposites.stream().collect(Collectors
                    .groupingBy(DeviceComposite::getParentId));
            }
            //模糊匹配不需要合并展示
            if (StringUtils.isBlank(dto.getKeyWords())) {
                //子节点合并展示最上层节点
                finalDeviceComposites = filterTopLevelDeviceComposites(finalDeviceComposites);
            }
            //获取每个组对应设备数量
            Map<Long, Integer> map = getDeviceSizeMap(dto, finalDeviceComposites);
            List<DeviceCompositeDTO> compositeSearchList = new ArrayList<>();
            // 构建去除自己的 codeNameMap
            List<String> parentDeviceCompositeCodeList = finalDeviceComposites.stream()
                .filter(Objects::nonNull)
                .flatMap(e -> Arrays.stream(StringUtils.split(e.getPath(), "/")))
                .distinct()
                .collect(Collectors.toList());
            List<DeviceCompositeDTO> nameList = listByDeviceCompositeCodeList(dto.getOrgId(), parentDeviceCompositeCodeList, DeviceCompositeBizType.COMMON);
            Map<String, String> idNameMap = nameList.stream()
                .filter(e -> !Objects.equals(e.getCode(), "00000000"))
                .collect(Collectors.toMap(DeviceCompositeDTO::getCode,
                    DeviceCompositeDTO::getDeviceCompositeName, (k1, k2) -> k1));
            // 组装返回列表
            for (DeviceComposite deviceComposite : finalDeviceComposites) {
                DeviceCompositeDTO deviceCompositeDTO = new DeviceCompositeDTO();
                BeanUtils.copyProperties(deviceComposite, deviceCompositeDTO);
                List<DeviceComposite> dc = deviceCompositeMap.get(deviceComposite
                    .getDeviceCompositeId());
                deviceCompositeDTO.setHasChildren(CollectionUtils.isNotEmpty(dc));
                deviceCompositeDTO.setDeviceSize(map.get(deviceComposite.getDeviceCompositeId()));
                String pathName = DeviceCompositeBizUtil.codePathToNamePath(
                    deviceCompositeDTO.getDeviceCompositeName(), deviceCompositeDTO.getPath(), idNameMap);
                deviceCompositeDTO.setPathName(pathName);
                compositeSearchList.add(deviceCompositeDTO);
            }
            compositeAndDeviceInfoDTO.setCompositeSearchList(compositeSearchList);
        }

        //查询设备信息
        if (StringUtils.isNotBlank(dto.getKeyWords())) {
            QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
            queryDeviceDTO.setOrgId(dto.getOrgId());
            if (dto.getSceneType() != null) {
                queryDeviceDTO.setSceneTypes(Lists.newArrayList(dto.getSceneType()));
            }
            queryDeviceDTO.setKeywords(dto.getKeyWords());
            queryDeviceDTO.setDeviceTypes(dto.getDeviceTypes());
            if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                queryDeviceDTO.setDeviceIds(dto.getDeviceIds());
            }
            List<DeviceInfoDTO> devices = deviceService.listByLikeCondition(queryDeviceDTO)
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(devices)) {
                List<DeviceDTO> deviceDTOList = new ArrayList<>();
                for (DeviceInfoDTO device : devices) {
                    DeviceDTO deviceDTO = new DeviceDTO();
                    BeanUtils.copyProperties(device, deviceDTO);
                    deviceDTOList.add(deviceDTO);
                }
                compositeAndDeviceInfoDTO.setDeviceSearchList(deviceDTOList);
            }
            //如果不是模糊查询的话,需要查询设备组下面的设备列表信息
        } else {
            List<DeviceDTO> deviceSearchList = new ArrayList<>();
            List<DeviceInfoDTO> finalDevices = new ArrayList<>();
            List<DeviceInfoDTO> totalDevices;
            QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
            queryDeviceDTO.setOrgId(dto.getOrgId());
            queryDeviceDTO.setDeviceTypes(dto.getDeviceTypes());
            if (dto.getSceneType() != null) {
                queryDeviceDTO.setSceneTypes(Lists.newArrayList(dto.getSceneType()));
            }
            boolean flag = true;
            if (!(dto.getAllDeviceFlag() != null && dto.getAllDeviceFlag())) {
                if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                    queryDeviceDTO.setDeviceIds(dto.getDeviceIds());
                } else {
                    flag = false;
                }
            }
            if (flag) {
                totalDevices = deviceService.listByLikeCondition(queryDeviceDTO)
                    .pickDataThrowException();
            } else {
                totalDevices = new ArrayList<>();
            }
            List<Long> hasPermissionDeviceIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(totalDevices)) {
                hasPermissionDeviceIds = totalDevices.stream().map(DeviceInfoDTO::getDeviceId)
                    .distinct().collect(Collectors.toList());
            }
            DeviceComposite deviceComposite = deviceCompositeMapper.getById(dto.getOrgId(),
                dto.getParentId());
            if (deviceComposite != null) {
                //1-第一层级-deviceSearchList放未分组设备 2-子设备组层级-deviceSearchList放该设备组下面的设备信息
                List<Long> deviceIds = new ArrayList<>();
                if (deviceComposite.getParentId() == 0L) {
                    //如果设备组信息为null，需要查询机构下所有的设备信息放入未分组设备列表中
                    if (compositeAndDeviceInfoDTO.getCompositeSearchList() == null) {
                        finalDevices = totalDevices;
                    } else {
                        List<Long> deviceCompositeIds = compositeAndDeviceInfoDTO
                            .getCompositeSearchList().stream().map(DeviceCompositeDTO::
                                getDeviceCompositeId).distinct().collect(Collectors.toList());
                        List<Long> existDeviceIds = getDeviceIdsByCompositeDeepBatch(dto.getOrgId()
                            ,deviceCompositeIds, dto.getSceneType());
                        if (CollectionUtils.isNotEmpty(existDeviceIds)) {
                            //需要去除没有权限的设备Id
                            for (Long existDeviceId : existDeviceIds) {
                                if (hasPermissionDeviceIds.contains(existDeviceId)) {
                                    deviceIds.add(existDeviceId);
                                }
                            }
                            //判断这些设备的运行模式是否匹配(所有设备组下的设备都为0)
                            queryDeviceDTO.setDeviceIds(deviceIds);
                            List<DeviceInfoDTO> devices = deviceService
                                .listByLikeCondition(queryDeviceDTO).pickDataThrowException();
                            if (CollectionUtils.isEmpty(devices)) {
                                finalDevices = totalDevices;
                            } else {
                                if (CollectionUtils.isNotEmpty(totalDevices)) {
                                    deviceIds = devices.stream().map(DeviceInfoDTO::getDeviceId)
                                        .distinct().collect(Collectors.toList());
                                    for (DeviceInfoDTO device : totalDevices) {
                                        if (!deviceIds.contains(device.getDeviceId())) {
                                            finalDevices.add(device);
                                        }
                                    }
                                }
                            }
                        } else {
                            finalDevices = totalDevices;
                        }
                    }
                } else {
                    deviceIds = deviceCompositeItemMapper.listByCompositeId(dto.getOrgId(),
                            dto.getParentId()).stream().map(DeviceCompositeItem::getDeviceId)
                        .distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(deviceIds)) {
                        List<Long> deviceIdList = new ArrayList<>();
                        //取有权限的设备信息
                        for (Long deviceId : deviceIds) {
                            if (hasPermissionDeviceIds.contains(deviceId)) {
                                deviceIdList.add(deviceId);
                            }
                        }
                        finalDevices = deviceService.listByOrgIdAndIds(dto.getOrgId(),
                            deviceIdList).pickDataThrowException();
                    }
                }
            } else {
                finalDevices = totalDevices;
            }
            if (CollectionUtils.isNotEmpty(finalDevices)) {
                List<Integer> deviceTypes = deviceTypePropertyService.
                    getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_LIST)
                    .pickDataThrowException();
                for (DeviceInfoDTO device : finalDevices) {
                    //判断是否iot设备类型，如果是的话不统计
                    if (deviceTypes.contains(device.getDeviceType())) {
                        continue;
                    }
                    DeviceDTO deviceDTO = new DeviceDTO();
                    BeanUtils.copyProperties(device, deviceDTO);
                    deviceDTO.setDirectionText(device.getDirection());
                    deviceSearchList.add(deviceDTO);
                }
            }
            compositeAndDeviceInfoDTO.setDeviceSearchList(deviceSearchList);
        }
        return compositeAndDeviceInfoDTO;
    }

    /**
     * 合并设备组信息
     *
     * @param finalDeviceComposites 设备组信息
     * @return {@link List<DeviceComposite>}
     */
    public List<DeviceComposite> filterTopLevelDeviceComposites(List<DeviceComposite> finalDeviceComposites) {
        List<String> allPaths = finalDeviceComposites.stream()
            .map(DeviceComposite::getPath)
            .collect(Collectors.toList());

        List<DeviceComposite> result = new ArrayList<>();
        for (DeviceComposite dc : finalDeviceComposites) {
            String path = dc.getPath();
            boolean hasAncestor = false;
            for (String otherPath : allPaths) {
                if (!path.equals(otherPath) && path.startsWith(otherPath + "/")) {
                    hasAncestor = true;
                    break;
                }
            }
            if (!hasAncestor) {
                result.add(dc);
            }
        }
        return result;
    }

    /**
     * 获取当前机构下的虚拟根设备组 只能用这个方法，不能用mapper的
     *
     * @param orgId   组织 ID
     * @param bizType 业务类型
     * @return {@link DeviceComposite }
     */
    @Override
    public DeviceComposite getVirtualRootDeviceComposite(Long orgId, Integer bizType) {
        DeviceComposite virtualRoot = deviceCompositeMapper.getVirtualRootDeviceComposite(
            orgId, bizType);
        if (virtualRoot == null) {
            // 不存在设备组虚拟根，那么就需要新建一个
            createVirtualRootDeviceComposite(orgId, bizType);
        }
        DeviceComposite rootDeviceComposite = deviceCompositeMapper.getVirtualRootDeviceComposite(
            orgId, bizType);
        BizAssert.isTrue(rootDeviceComposite != null, "虚拟根设备组创建失败");
        return rootDeviceComposite;
    }

    @Override
    public List<DeviceCompositeDTO> listDetailByOrgIdAndBizType(Long orgId, Integer bizType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(bizType, "bizType must not be null");
        List<DeviceCompositeDTO> DeviceCompositeDTOList = new ArrayList<>();
        List<DeviceComposite> deviceCompositeList = deviceCompositeMapper
            .listByOrgIdAndBizType(orgId, bizType);
        if (CollectionUtils.isNotEmpty(deviceCompositeList)) {
            List<Long> deviceCompositeIds = new ArrayList<>();
            Map<Long, DeviceCompositeDTO> DeviceCompositeDTOMap = new HashMap<>();
            for (DeviceComposite deviceComposite : deviceCompositeList) {
                //id集合
                deviceCompositeIds.add(deviceComposite.getDeviceCompositeId());
                //dto集合
                DeviceCompositeDTO DeviceCompositeDTO = new DeviceCompositeDTO();
                BeanUtils.copyProperties(deviceComposite, DeviceCompositeDTO);
                //map
                DeviceCompositeDTOMap
                    .put(DeviceCompositeDTO.getDeviceCompositeId(), DeviceCompositeDTO);
            }
            //查询条目.对条目进行分类
            List<DeviceCompositeItem> itemList = deviceCompositeItemMapper
                .listByOrgIdAndDeviceCompositeIds(orgId, deviceCompositeIds);
            if (CollectionUtils.isNotEmpty(itemList)) {
                List<Long> deviceIdList = new ArrayList<>();
                for (DeviceCompositeItem deviceCompositeItem : itemList) {
                    deviceIdList.add(deviceCompositeItem.getDeviceId());
                }
                //查询设备名
                List<Device> deviceList = deviceManager.listByIds(deviceIdList);
                //设备名-map
                Map<Long, Device> deviceMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(deviceList)) {
                    for (Device device : deviceList) {
                        deviceMap.put(device.getDeviceId(), device);
                    }
                }
                for (DeviceCompositeItem deviceCompositeItem : itemList) {
                    if (deviceMap.get(deviceCompositeItem.getDeviceId()) == null) {
                        continue;
                    }
                    DeviceCompositeDTO DeviceCompositeDTO = DeviceCompositeDTOMap
                        .get(deviceCompositeItem.getDeviceCompositeId());
                    List<DeviceCompositeItemDTO> deviceCompositeItemDTOList = DeviceCompositeDTO
                        .getItems();
                    if (deviceCompositeItemDTOList == null) {
                        deviceCompositeItemDTOList = new ArrayList<>();
                        DeviceCompositeDTO.setItems(deviceCompositeItemDTOList);
                    }
                    DeviceCompositeItemDTO deviceCompositeItemDTO = com.moredian.bee.common.utils.BeanUtils
                        .copyProperties(DeviceCompositeItemDTO.class, deviceCompositeItem);
                    deviceCompositeItemDTO.setDeviceName(
                        deviceMap.get(deviceCompositeItem.getDeviceId()).getDeviceName());
                    deviceCompositeItemDTO.setDeviceSn(
                        deviceMap.get(deviceCompositeItem.getDeviceId()).getDeviceSn());
                    deviceCompositeItemDTO.setDeviceType(
                        deviceMap.get(deviceCompositeItem.getDeviceId()).getDeviceType());
                    deviceCompositeItemDTOList.add(deviceCompositeItemDTO);
                    List<Integer> deviceTypeList = DeviceCompositeDTO.getDeviceTypeList();
                    if (deviceTypeList == null) {
                        deviceTypeList = new ArrayList<>();
                        DeviceCompositeDTO.setDeviceTypeList(deviceTypeList);
                    }
                    deviceTypeList.add(deviceCompositeItem.getDeviceType());
                }
            }
            //完善数据
            for (Long deviceCompositeId : DeviceCompositeDTOMap.keySet()) {
                DeviceCompositeDTO DeviceCompositeDTO = DeviceCompositeDTOMap
                    .get(deviceCompositeId);
                if (CollectionUtils.isNotEmpty(DeviceCompositeDTO.getItems())) {
                    DeviceCompositeDTO.setDeviceSize(DeviceCompositeDTO.getItems().size());
                }
                DeviceCompositeDTOList.add(DeviceCompositeDTO);
            }
        }
        return DeviceCompositeDTOList;
    }

    @Override
    public List<DeviceCompositeItemDTO> listCompositeItemByCompositeId(Long orgId,
        Long deviceCompositeId, Boolean subFlag) {
        return getItemListByCompositeId(orgId, deviceCompositeId, subFlag);
    }

    @Override
    public DeviceCompositeDTO getDetailByOrgIdAndId(Long orgId, Long deviceCompositeId) {
        //先查询组
        DeviceComposite deviceComposite = deviceCompositeMapper.getById(orgId, deviceCompositeId);
        if (deviceComposite == null) {
            return null;
        }
        DeviceCompositeDTO deviceCompositeDTO = com.moredian.bee.common.utils.BeanUtils
            .copyProperties(DeviceCompositeDTO.class, deviceComposite);
        deviceCompositeDTO.setItems(getItemListByCompositeId(orgId, deviceCompositeId, Boolean.FALSE));
        return deviceCompositeDTO;
    }

    @Override
    public String getMaxDeptCode(Long orgId, Integer bizType) {
        BizAssert.notNull(orgId);
        return deviceCompositeMapper.getMaxDeptCode(orgId, bizType);
    }

    @Override
    public List<DeviceCompositeDTO> listByDeviceCompositeIds(Long orgId, List<Long> compositeIds,
        Integer bizType) {
        List<DeviceComposite> deviceComposites = deviceCompositeMapper.listByDeviceCompositeId(
            orgId, compositeIds, bizType);
        return BeanUtil.copyToList(deviceComposites, DeviceCompositeDTO.class);
    }

    @Override
    public List<DeviceCompositeDTO> listByDeviceCompositeCodeList(Long orgId, List<String> codeList, Integer bizType) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Collections.emptyList();
        }
        List<DeviceComposite> deviceComposites = deviceCompositeMapper.getCompositeByCodeList(
            orgId, codeList, bizType);
        return BeanUtil.copyToList(deviceComposites, DeviceCompositeDTO.class);
    }

    @Override
    public List<DeviceComposite> findAllSubDeviceComposite(Long orgId, Long deviceCompositeId) {
        List<DeviceComposite> deviceComposites = new ArrayList<>();
        DeviceComposite deviceComposite = deviceCompositeMapper.getById(orgId, deviceCompositeId);
        BizAssert.notNull(deviceComposite, "当前设备组不存在");
        deviceComposites.add(deviceComposite);

        String path = deviceComposite.getPath() + "/";
        List<DeviceComposite> dc = deviceCompositeMapper.getCompositeByPath(orgId,
            path, deviceComposite.getBizType());
        if (CollectionUtils.isNotEmpty(dc)){
            deviceComposites.addAll(dc);
        }
        return Optional.of(deviceComposites).orElse(Collections.emptyList());
    }

    @Override
    public DeviceCompositePathInfoDTO getDeviceInCompositePathList(
        QueryDeviceCompositePathDTO request) {
        BizAssert.notNull(request, "request is null");
        BizAssert.notNull(request.getOrgId(), "orgId is null");

        List<Long> deviceIds = request.getDeviceIds();
        List<Long> compositeIds = request.getCompositeIds();
        DeviceCompositePathInfoDTO resp = new DeviceCompositePathInfoDTO();
        resp.setOrgId(resp.getOrgId());

        // 1、查设备的设备组路径情况
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            Map<Long/*deviceId*/, List<Long>/*compositeIds*/> listMap = new HashMap<>();
            Map<Long/*deviceId*/, List<String>/*pathList*/> devicePathMap = new HashMap<>();
            Map<Long/*deviceId*/, Device/*device*/> deviceIdMap = new HashMap<>();
            for (Long deviceId : deviceIds) {
                devicePathMap.put(deviceId, new ArrayList<>());
            }

            List<Device> deviceList = deviceManager.listByOrgIdAndIds(request.getOrgId(), deviceIds);
            if (CollectionUtils.isNotEmpty(deviceList)){
                deviceIdMap = deviceList.stream().collect(Collectors.toMap(Device::getDeviceId,v->v));
            }

            List<DeviceCompositeItem> compositeItems = deviceCompositeItemMapper.listByDeviceIds(
                request.getOrgId(), request.getDeviceIds(), DeviceCompositeBizType.COMMON);
            if (CollectionUtils.isNotEmpty(compositeItems)) {
                listMap = Optional.of(compositeItems.stream().collect(
                    Collectors.groupingBy(DeviceCompositeItem::getDeviceId,
                        Collectors.mapping(DeviceCompositeItem::getDeviceCompositeId,
                            Collectors.toList())))).orElse(new HashMap<>());
            }
            if (MapUtils.isNotEmpty(listMap)) {
                List<Long> compositeIdsInner = listMap.values().stream().flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
                List<DeviceComposite> deviceComposites = deviceCompositeMapper.listByDeviceCompositeId(
                    request.getOrgId(), compositeIdsInner, DeviceCompositeBizType.COMMON);
                if (CollectionUtils.isNotEmpty(deviceComposites)) {
                    Map<Long/*compositeId*/, String/*path*/> compositePathMap = deviceComposites.stream()
                        .collect(Collectors.toMap(
                            DeviceComposite::getDeviceCompositeId, DeviceComposite::getPath));
                    for (Entry<Long, List<Long>> entry : listMap.entrySet()) {
                        Long deviceIdTemp = entry.getKey();
                        List<Long> compositeIdsTemp = entry.getValue();
                        for (Long compositeId : compositeIdsTemp) {
                            String path = compositePathMap.get(compositeId);
                            devicePathMap.get(deviceIdTemp).add(path);
                        }
                    }
                }
            }
            // 组装返回数据
            List<DeviceInCompositePathDTO> devicePathInfoList = new ArrayList<>();
            for (Entry<Long, List<String>> entry : devicePathMap.entrySet()) {
                Long deviceId = entry.getKey();
                List<String> pathList = entry.getValue();
                Device device = deviceIdMap.get(deviceId);
                DeviceInCompositePathDTO dto = new DeviceInCompositePathDTO();
                dto.setDeviceId(deviceId);
                dto.setDeviceName(device.getDeviceName());
                dto.setDeviceSn(device.getDeviceSn());
                dto.setDeviceType(device.getDeviceType());
                dto.setPathList(pathList);
                devicePathInfoList.add(dto);
            }
            resp.setDevicePathInfoList(devicePathInfoList);
        }

        // 2、查设备组情况
        if (CollectionUtils.isNotEmpty(compositeIds)) {
            List<DeviceCompositeDTO> compositeDtoList = new ArrayList<>();
            List<DeviceComposite> deviceComposites = deviceCompositeMapper.listByDeviceCompositeId(
                request.getOrgId(), compositeIds, DeviceCompositeBizType.COMMON);
            // 组装返回数据
            for (DeviceComposite deviceComposite : deviceComposites) {
                DeviceCompositeDTO compositeDto = BeanUtil.copyProperties(deviceComposite,
                    DeviceCompositeDTO.class);
                compositeDtoList.add(compositeDto);
            }
            resp.setCompositeDtoList(compositeDtoList);
        }
        return resp;
    }

    @Override
    public List<DeviceCompositeAndDeviceCountDTO> getTreeAndDeviceByIds(Long orgId, Integer bizType,
        List<Long> compositeIds) {
        BizAssert.notNull(orgId, "orgId cannot be null");
        BizAssert.notNull(bizType, "bizType cannot be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(compositeIds), "compositeIds cannot be empty");
        log.info(
            "DeviceCompositeManagerImpl.getTreeAndDeviceByIds orgId {}, bizType {},compositeIds {}",
            orgId, bizType, compositeIds);


        DeviceComposite composite = deviceCompositeMapper.getVirtualRootDeviceComposite(
            orgId, DeviceCompositeBizType.COMMON);
        // 根据parentId找出机构下所有的子组
        List<DeviceComposite> compositeListAll = Optional.ofNullable(
            this.findAllSubDeviceComposite(orgId, composite.getDeviceCompositeId())).orElse(new ArrayList<>());

        // 根据id找出实体
        List<DeviceComposite> deviceComposites = compositeListAll.stream()
            .filter(f -> compositeIds.contains(f.getDeviceCompositeId()))
            .filter(f -> Objects.equals(bizType, f.getBizType()))
            .collect(Collectors.toList());


        Map<Long, DeviceComposite> deviceCompositeMap = deviceComposites.stream()
            .collect(Collectors.toMap(DeviceComposite::getDeviceCompositeId, v -> v));

        // 根据实体来组装树结构(这里只是需要分出多棵树就好)
        List<DeviceCompositeTreeDTO> compositeTreeDtoList = BizUtil.compositeBuildTree(
            deviceComposites);
        if (CollectionUtils.isEmpty(compositeTreeDtoList)) {
            return Collections.emptyList();
        }
        // 最终的节点
        List<Long> compositeIdList = compositeTreeDtoList.stream()
            .map(DeviceCompositeTreeDTO::getDeviceCompositeId).collect(Collectors.toList());

        List<DeviceCompositeAndDeviceCountDTO> result = new ArrayList<>();


        // 具体dto数据，还没树结构（把树中的每个节点都找出详情【每个节点的设备+设备类型】）
        Map<Long, DeviceCompositeAndDeviceCountDTO> beanMap = getCompositeDtoMap(
            orgId, compositeListAll);

        // 遍历多颗数据的根节点
        for (Long compositeId : compositeIdList) {
            List<DeviceComposite> compositeList = new ArrayList<>();
            // 找出每棵树的根组的信息
            DeviceComposite currDeviceComposite = deviceCompositeMap.get(compositeId);
            BizAssert.isTrue(currDeviceComposite != null, "设备组不存在");

            List<DeviceComposite> composites = compositeListAll.stream()
                .filter(f -> f.getPath().startsWith(currDeviceComposite.getPath() + '/')).collect(
                    Collectors.toList());
            if (CollectionUtils.isNotEmpty(composites)){
                compositeList.addAll(composites);
            }

            // 把父也加入
            compositeList.add(currDeviceComposite);
            // 组装设备组树
            TreeNode treeNode = BizUtil.buildTree(compositeList,
                currDeviceComposite.getDeviceCompositeId());

            Map<Long, DeviceCompositeAndDeviceCountDTO> beanMapTemp = compositeList.stream()
                .map(f -> beanMap.get(f.getDeviceCompositeId()))
                .collect(Collectors.toMap(DeviceCompositeAndDeviceCountDTO::getDeviceCompositeId, v -> v));

            // 构建树
            List<DeviceCompositeAndDeviceCountDTO> dtoList = Optional.ofNullable(
                BizUtil.buildDeviceCompositeDtoTree(
                    Lists.newArrayList(treeNode), beanMapTemp, null)).orElse(Collections.emptyList());
            if (CollectionUtils.isNotEmpty(dtoList)) {
                result.add(dtoList.get(0));
            }
        }
        return result;
    }

    @Override
    public List<Long> listDeviceIdByCompositeIds(Long orgId, List<Long> compositeIds) {
        BizAssert.notNull(orgId, "orgId cannot be null");
        List<Long> deviceIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(compositeIds)) {
            return deviceIdList;
        }
        List<DeviceCompositeItem> deviceCompositeItems = deviceCompositeItemMapper
            .listByOrgIdAndDeviceCompositeIds(orgId, compositeIds);
        if (CollectionUtils.isNotEmpty(deviceCompositeItems)) {
            return deviceCompositeItems.stream().map(DeviceCompositeItem::getDeviceId).distinct()
                .collect(Collectors.toList());
        }
        return deviceIdList;
    }

    @Override
    public List<Long> listCompositeIdsByParentId(Long orgId, Long parentId) {
        BizAssert.notNull(orgId, "orgId cannot be null");
        BizAssert.notNull(parentId, "parentId cannot be null");
        return deviceCompositeMapper.listCompositeIdsByParentId(orgId, parentId);
    }

    @Override
    public List<CompositeDeepDeviceSizeDTO> getCompositeDeepDeviceSize(Long orgId,
        List<Long> compositeIds, Integer bizType, Integer sceneType) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(compositeIds), "compositeIds cannot be empty");
        // 获取设备组实体
        List<DeviceComposite> deviceComposites = deviceCompositeMapper.listByDeviceCompositeId(
            orgId, compositeIds, bizType);
        // 组装组下面的设备数量
        return this.getCompositeDeepDeviceSize(orgId, deviceComposites, sceneType);
    }

    @Override
    public List<Long> getDeviceIdsByCompositeDeepBatch(Long orgId, List<Long> deviceCompositeIds,
        Integer sceneType) {
        if (CollectionUtils.isEmpty(deviceCompositeIds)){
            return Collections.emptyList();
        }
        List<Long> compositeIdList=Lists.newArrayList();
        compositeIdList.addAll(deviceCompositeIds);
        // 根据parentId找出所有的子组
        List<DeviceComposite> compositeList = Optional.ofNullable(
                this.findAllSubDeviceCompositeBatch(orgId, deviceCompositeIds))
            .orElse(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(compositeList)) {
            List<Long> compositeIds = compositeList.stream().
                map(DeviceComposite::getDeviceCompositeId).distinct().collect(Collectors.toList());
            compositeIdList.addAll(compositeIds);
        }

        // 查询设备
        List<DeviceCompositeItem> itemList = deviceCompositeItemMapper.listByCompositeIds(orgId,
            compositeIdList);
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<Long> deviceIds = itemList.stream().filter(f -> f.getDeviceId() != null).distinct().map(
                DeviceCompositeItem::getDeviceId).collect(Collectors.toList());
            QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
            queryDeviceDTO.setOrgId(orgId);
            queryDeviceDTO.setDeviceIds(deviceIds);
            queryDeviceDTO.setSceneType(sceneType);
            List<DeviceInfoDTO> deviceInfos = deviceService.listByLikeCondition(queryDeviceDTO)
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                return deviceInfos.stream().map(DeviceInfoDTO::getDeviceId).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<DeviceComposite> findAllSubDeviceCompositeBatch(Long orgId,
        List<Long> deviceCompositeIds) {
        if (CollectionUtils.isEmpty(deviceCompositeIds)){
            return Collections.emptyList();
        }
        List<DeviceComposite> compositeList = deviceCompositeMapper.listByDeviceCompositeId(
            orgId, deviceCompositeIds, DeviceCompositeBizType.COMMON);
        List<String> paths = new ArrayList<>();
        if (CollectionUtils.isEmpty(compositeList)) {
            return Collections.emptyList();
        }
        for (DeviceComposite composite : compositeList) {
            String path = composite.getPath() + "/";
            paths.add(path);
        }
        List<DeviceComposite> deviceComposites = deviceCompositeMapper.getCompositeByPathBatch(
            orgId, paths, DeviceCompositeBizType.COMMON);
        return Optional.ofNullable(deviceComposites).orElse(Collections.emptyList());
    }

    @Override
    public List<DeviceComposite> findAllSubDeviceCompositeWithDelete(Long orgId, Long deviceCompositeId) {
        DeviceComposite deviceComposite = deviceCompositeMapper.getCompositeByIdWithDelete(orgId, deviceCompositeId);
        BizAssert.notNull(deviceComposite, "当前设备组不存在");

        String path = deviceComposite.getPath() + "/";
        List<DeviceComposite> deviceComposites = deviceCompositeMapper.getCompositeByPathWithDelete(orgId,
            path, deviceComposite.getBizType());
        return Optional.ofNullable(deviceComposites).orElse(Collections.emptyList());
    }

    @Override
    public DeviceComposite getCompositeByIdWithDelete(Long orgId,
        Long compositeId) {
        return deviceCompositeMapper.getCompositeByIdWithDelete(orgId,compositeId);
    }

    @Override
    public DeviceCompositeDTO getCompositeInfo(Long orgId, Long deviceCompositeId,
        Boolean hasChildren) {
        // 先查询组
        DeviceComposite deviceComposite = deviceCompositeMapper.getById(orgId, deviceCompositeId);
        if (deviceComposite == null) {
            return null;
        }
        DeviceCompositeDTO deviceCompositeDto = com.moredian.bee.common.utils.BeanUtils.copyProperties(
            DeviceCompositeDTO.class, deviceComposite);
        if (!hasChildren) {
            // 老逻辑，不需要查出子组下设备的信息
            // 查出这个组下面有哪些设备
            deviceCompositeDto.setItems(getItemListByCompositeId(orgId, deviceCompositeId,false));
            return deviceCompositeDto;
        }
        // 新逻辑还需要子组的信息
        List<DeviceComposite> compositeList = Optional.ofNullable(
            this.findAllSubDeviceComposite(orgId, deviceCompositeId)).orElse(new ArrayList<>());
        // 把本组也加入到列表中
        compositeList.add(deviceComposite);
        // 查出这个组下面有哪些设备,包括子组下面的设备
        deviceCompositeDto.setItems(
            getItemListByCompositeIdList(orgId, compositeList.stream().map(
                DeviceComposite::getDeviceCompositeId).distinct().collect(
                Collectors.toList())));
        return deviceCompositeDto;
    }

    @Override
    public List<DeviceCompositeItemDTO> getCompositeItemListByCompositeIds(Long orgId,
        List<Long> deviceCompositeIds) {
        return getItemListByCompositeIdList(orgId, deviceCompositeIds);
    }

    /**
     * 查询
     *
     * @param orgId
     * @param deviceCompositeId
     * @return
     */
    private DeviceComposite getDeviceCompositeById(Long orgId, Long deviceCompositeId) {
        return deviceCompositeMapper.getById(orgId, deviceCompositeId);
    }

    /**
     * 创建分组
     */
    private Long insertDeviceComposite(Long orgId, Integer bizType, String deviceCompositeName) {
        //查询是否已经存在同样组名
        List<DeviceComposite> deviceComposites = deviceCompositeMapper
            .getByName(orgId, bizType, deviceCompositeName);
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceComposites), DeviceErrorCode.
            DEVICE_COMPOSITE_NAME_EXIST, DeviceErrorCode.DEVICE_COMPOSITE_NAME_EXIST.getMessage());
        //插入
        DeviceComposite deviceComposite = new DeviceComposite();
        deviceComposite.setDeviceCompositeId(
            idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_COMPOSITE_ITEM).getData());
        deviceComposite.setOrgId(orgId);
        deviceComposite.setBizType(bizType);
        deviceComposite.setDeviceCompositeName(deviceCompositeName);
        deviceCompositeMapper.insert(deviceComposite);
        return deviceComposite.getDeviceCompositeId();
    }

    /**
     * 修改组名
     *
     * @param dto
     */
    private void updateDeviceComposite(InsertDeviceCompositeDTO dto) {
        DeviceComposite dc = new DeviceComposite();
        dc.setOrgId(dto.getOrgId());
        dc.setDeviceCompositeId(dto.getDeviceCompositeId());
        dc.setDeviceCompositeName(dto.getDeviceCompositeName());
        deviceCompositeMapper.update(dc);
    }

    /**
     * 增加设备 //isMutex，是否互斥，true，是-访客，false-不互斥，门禁，
     */
    private void addDevice(Long orgId, Long deviceCompositeId, List<Long> deviceIdList,
        Integer bizType, Boolean isMutex) {
        if (CollectionUtils.isEmpty(deviceIdList)) {
            return;
        }
        //查询是否已经有在组里面的设备
        if (isMutex) {
            List<DeviceCompositeItem> deviceCompositeItems = deviceCompositeItemMapper
                .listByDeviceIds(orgId, deviceIdList, bizType);
            if (CollectionUtils.isNotEmpty(deviceCompositeItems)) {
                BizAssert.notNull(null, DeviceErrorCode.DEVICE_BIND_COMPOSITE_ALREADY,
                    DeviceErrorCode.DEVICE_BIND_COMPOSITE_ALREADY.getMessage());
            }
        }
        //查询设备
        List<Device> deviceList = deviceManager.listByIds(deviceIdList);
        if (CollectionUtils.isEmpty(deviceList)) {
            BizAssert.notNull(null, DeviceErrorCode.BIND_DEVICE_NOT_EXIST,
                DeviceErrorCode.BIND_DEVICE_NOT_EXIST.getMessage());
        }
        List<DeviceCompositeItem> itemList = new ArrayList<>();
        for (Device device : deviceList) {
            DeviceCompositeItem deviceCompositeItem = new DeviceCompositeItem();
            deviceCompositeItem.setDeviceCompositeItemId(idgeneratorService
                .getNextIdByTypeName(BeanConstants.DEVICE_COMPOSITE_ITEM).pickDataThrowException());
            deviceCompositeItem.setOrgId(orgId);
            deviceCompositeItem.setDeviceCompositeId(deviceCompositeId);
            deviceCompositeItem.setDeviceId(device.getDeviceId());
            deviceCompositeItem.setDeviceType(device.getDeviceType());
            deviceCompositeItem.setBizType(bizType);
            itemList.add(deviceCompositeItem);
        }
        //批量插入
        deviceCompositeItemMapper.batchInsert(itemList);
    }

    /**
     * 查询该分组的内容
     *
     * @param orgId             机构号
     * @param deviceCompositeId 设备分组Id
     * @return
     */
    private List<DeviceCompositeItemDTO> getItemListByCompositeId(Long orgId,
        Long deviceCompositeId, Boolean subFlag) {
        //查询所有的进行返回
        List<DeviceCompositeItemDTO> itemDtoList = new ArrayList<>();
        List<Long> deviceCompositeIds = new ArrayList<>();
        if (subFlag != null && subFlag) {
            List<DeviceComposite> composites = findAllSubDeviceComposite(orgId, deviceCompositeId);
            if (CollectionUtils.isEmpty(composites)) {
                return itemDtoList;
            }
            deviceCompositeIds = composites.stream().map(DeviceComposite::getDeviceCompositeId)
                .collect(Collectors.toList());
        } else {
            deviceCompositeIds.add(deviceCompositeId);
        }
        List<DeviceCompositeItem> itemList = deviceCompositeItemMapper
            .listByCompositeIds(orgId, deviceCompositeIds);
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<Long> itemDeviceIdList = new ArrayList<>();
            for (DeviceCompositeItem item : itemList) {
                itemDeviceIdList.add(item.getDeviceId());
            }
            List<Device> devices = deviceManager.listByIds(itemDeviceIdList);
            Map<Long, String> deviceMap = new HashMap<>(devices.size());
            for (Device device : devices) {
                deviceMap.put(device.getDeviceId(), device.getDeviceName());
            }
            Map<Long, String> deviceSnMap = devices.stream().collect(
                Collectors.toMap(Device::getDeviceId, Device::getDeviceSn, (oldV, newV) -> newV));
            for (DeviceCompositeItem item : itemList) {
                DeviceCompositeItemDTO deviceCompositeItemDTO = new DeviceCompositeItemDTO();
                deviceCompositeItemDTO.setDeviceId(item.getDeviceId());
                deviceCompositeItemDTO.setDeviceType(item.getDeviceType());
                deviceCompositeItemDTO.setDeviceName(deviceMap.get(item.getDeviceId()));
                deviceCompositeItemDTO.setDeviceSn(Optional.ofNullable(deviceSnMap.get(item.getDeviceId())).orElse(StringUtils.EMPTY));
                itemDtoList.add(deviceCompositeItemDTO);
            }
        }
        return itemDtoList;
    }

    /**
     * @param orgId
     * @param deviceCompositeId
     * @param bizType
     * @param changeState
     */
    private void sendDeviceCompositeChangeMsg(Long orgId, Long deviceCompositeId, Integer bizType,
        Integer changeState) {
        DeviceCompositeChangeMsg compositeChangeMsg = new DeviceCompositeChangeMsg();
        compositeChangeMsg.setOrgId(orgId);
        compositeChangeMsg.setBizType(bizType);
        compositeChangeMsg.setDeviceCompositeId(deviceCompositeId);
        compositeChangeMsg.setChangeState(changeState);
        int publish = EventBus.publish(compositeChangeMsg);
        log.info("MQ-LOG send DeviceCompositeChangeMsg,content={},publish-value={}",
            JsonUtils.toJson(compositeChangeMsg), publish);
    }

    /**
     * 删除设备组并且发送删除消息
     *
     * @param orgId             组织 ID
     * @param deviceCompositeId 设备复合 ID
     */
    private void deleteDeviceCompositeAndSendMsg(Long orgId, Long deviceCompositeId) {
        // 删组和删设备时同一个删除状态
        Long deleteTime = DeleteStatus.DELETED.getCode();
        // 组也删除了就用软删除
        deviceCompositeItemMapper.removeItemSoft(orgId, deviceCompositeId, null,
            deleteTime);
        deviceCompositeMapper.removeComposite(orgId, deviceCompositeId,
            deleteTime);
    }

    /**
     * 创建分组，支持指定改在某个组下面
     */
    private Long createDeviceComposite(Long orgId, Integer bizType, String deviceCompositeName,
        Long parentId) {
        if (deviceCompositeName.length() > 20){
            ExceptionUtils.throwException(DeviceErrorCode.DEVICE_COMPOSITE_NAME_LIMIT,
                DeviceErrorCode.DEVICE_COMPOSITE_NAME_LIMIT.getMessage());
        }
        if (parentId == null || parentId == 0) {
            DeviceComposite virtualRoot = this.getVirtualRootDeviceComposite(
                orgId, bizType);
            parentId = virtualRoot.getDeviceCompositeId();
        }
        //查询是否已经存在同样组名
        List<DeviceComposite> deviceCompositeByNames = deviceCompositeMapper.getByName(orgId,
            bizType, deviceCompositeName);
        // 如果不是本组且和本组是同一个父组，那么就要报错，设备组名称不能重复
        for (DeviceComposite deviceCompositeByName : deviceCompositeByNames) {
            if (deviceCompositeByName != null && Objects.equals(
                deviceCompositeByName.getParentId(), parentId)) {
                ExceptionUtils.throwException(DeviceErrorCode.DEVICE_COMPOSITE_NAME_EXIST,
                    DeviceErrorCode.DEVICE_COMPOSITE_NAME_EXIST.getMessage());
            }
        }

        // 父组信息
        DeviceComposite parentInfo = deviceCompositeMapper.getById(orgId, parentId);
        if(parentInfo == null){
            ExceptionUtils.throwException(DeviceErrorCode.DEVICE_COMPOSITE_PARENT_EXIST,
                DeviceErrorCode.DEVICE_COMPOSITE_PARENT_EXIST.getMessage());
        }

        // 检验层级
        checkCompositeLevel(orgId, parentInfo);

        //插入
        DeviceComposite deviceComposite = new DeviceComposite();
        deviceComposite.setDeviceCompositeId(idgeneratorService.getNextIdByTypeName(
            "com.moredian.magicube.device.dao.entity.DeviceComposite").getData());
        deviceComposite.setOrgId(orgId);
        deviceComposite.setBizType(bizType);
        deviceComposite.setDeviceCompositeName(deviceCompositeName);
        deviceComposite.setParentId(parentId);
        String code = compositeCodeGenHelper.genCodes(orgId, 1, bizType).next();
        deviceComposite.setCode(code);
        deviceComposite.setPath(parentInfo.getPath() + "/" + code);
        deviceCompositeMapper.createComposite(deviceComposite);
        return deviceComposite.getDeviceCompositeId();
    }

    /**
     * 创建指定业务类型的设备虚拟根组
     *
     * @param orgId   组织 ID
     * @param bizType 业务类型
     */
    private void createVirtualRootDeviceComposite(Long orgId, Integer bizType) {
        DeviceComposite deviceComposite;
        deviceComposite = new DeviceComposite();
        deviceComposite.setDeviceCompositeId(idgeneratorService.getNextIdByTypeName(
            "com.moredian.magicube.device.dao.entity.DeviceComposite").getData());
        deviceComposite.setOrgId(orgId);
        deviceComposite.setBizType(bizType);
        deviceComposite.setDeviceCompositeName("设备组虚拟根");
        deviceComposite.setCode(compositeCodeGenHelper.getVirtualRootCompositeCode());
        deviceComposite.setPath(compositeCodeGenHelper.getVirtualRootCompositeCode());
        deviceComposite.setParentId(0L);
        deviceCompositeMapper.createComposite(deviceComposite);
    }

    /**
     * 检查设备组层级
     *
     * @param orgId      组织 ID
     * @param parentInfo 家长信息
     */
    private void checkCompositeLevel(Long orgId, DeviceComposite parentInfo) {
        Integer levelLimit = null;
        // 1、先取出hive_org_config中的值
        String value = orgConfigService.getOrgConfigValue(orgId,
            ORG_CONFIG_KEY_FOR_COMPOSITE_LEVEL_LIMIT).pickDataThrowException();
        if (StringUtils.isNotBlank(value)) {
            levelLimit = Integer.parseInt(value);
        } else {
            // 2、上一步没值就读取系统配置compositeLevelLimit
            levelLimit = compositeLevelLimit;
        }
        String parentPath = parentInfo.getPath();
        String[] split = parentPath.split("/");
        if (split.length > levelLimit) {
            // 父组就已经是第五层了
            BizAssert.isTrueWithFormat(false,
                    DeviceErrorCode.DEVICE_COMPOSITE_BEYOND_LEVEL,
                    DeviceErrorCode.DEVICE_COMPOSITE_BEYOND_LEVEL.getMessage(),
                    levelLimit);
        }
    }

    private Map<Long/*compositeId*/, DeviceCompositeAndDeviceCountDTO> getCompositeDtoMap(
        Long orgId, List<DeviceComposite> compositeList) {
        // bean类型转化
        Map<Long, DeviceCompositeAndDeviceCountDTO> compositeAndDeviceCountDtoMap = compositeList.stream()
            .collect(Collectors.toMap(DeviceComposite::getDeviceCompositeId, v -> {
                DeviceCompositeAndDeviceCountDTO dto = new DeviceCompositeAndDeviceCountDTO();
                BeanUtils.copyProperties(v, dto);
                return dto;
            }));
        List<Long> deviceCompositeIdList = new ArrayList<>(compositeAndDeviceCountDtoMap.keySet());

        // 查树上每个设备组节点下，存在的设备情况
        List<DeviceCompositeItem> itemList = deviceCompositeItemMapper
            .listByOrgIdAndDeviceCompositeIds(orgId, deviceCompositeIdList);
        if (CollectionUtils.isNotEmpty(itemList)) {
            // 所有的设备id
            List<Long> deviceIdList = new ArrayList<>();
            for (DeviceCompositeItem deviceCompositeItem : itemList) {
                deviceIdList.add(deviceCompositeItem.getDeviceId());
            }
            //查询设备名
            List<Device> deviceList = deviceManager.listByOrgIdAndIds(orgId, deviceIdList);
            //设备名-map
            Map<Long, Device> deviceMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(deviceList)) {
                for (Device device : deviceList) {
                    deviceMap.put(device.getDeviceId(), device);
                }
            }
            // 塞入设备和设备类型到设备组下
            for (DeviceCompositeItem deviceCompositeItem : itemList) {
                if (deviceMap.get(deviceCompositeItem.getDeviceId()) == null) {
                    continue;
                }
                // 拿到当前设备绑定的设备组节点（一个设备可能绑定多个设备组）
                DeviceCompositeAndDeviceCountDTO deviceCompositeAndDeviceCountDto = compositeAndDeviceCountDtoMap.get(
                    deviceCompositeItem.getDeviceCompositeId());
                List<DeviceCompositeItemDTO> deviceCompositeItemDtoList = deviceCompositeAndDeviceCountDto.getDeviceList();
                if (deviceCompositeItemDtoList == null) {
                    deviceCompositeItemDtoList = new ArrayList<>();
                    deviceCompositeAndDeviceCountDto.setDeviceList(deviceCompositeItemDtoList);
                }
                DeviceCompositeItemDTO deviceCompositeItemDto = com.moredian.bee.common.utils.BeanUtils.copyProperties(
                    DeviceCompositeItemDTO.class, deviceCompositeItem);
                deviceCompositeItemDto.setDeviceName(
                    deviceMap.get(deviceCompositeItem.getDeviceId()).getDeviceName());
                deviceCompositeItemDto.setDeviceSn(
                    deviceMap.get(deviceCompositeItem.getDeviceId()).getDeviceSn());
                deviceCompositeItemDto.setDeviceType(
                    deviceMap.get(deviceCompositeItem.getDeviceId()).getDeviceType());
                deviceCompositeItemDtoList.add(deviceCompositeItemDto);
                List<Integer> deviceTypeList = deviceCompositeAndDeviceCountDto.getDeviceTypeList();
                if (deviceTypeList == null) {
                    deviceTypeList = new ArrayList<>();
                    deviceCompositeAndDeviceCountDto.setDeviceTypeList(deviceTypeList);
                }
                deviceTypeList.add(deviceCompositeItem.getDeviceType());
            }
        }
        //完善数据 (统计下每个设备组下面的设备数量，直系)
        for (Long deviceCompositeId : compositeAndDeviceCountDtoMap.keySet()) {
            DeviceCompositeAndDeviceCountDTO dto = compositeAndDeviceCountDtoMap.get(
                deviceCompositeId);
            if (CollectionUtils.isNotEmpty(dto.getDeviceList())) {
                dto.setDeviceCompositeItemCount(dto.getDeviceList().size());
            }
        }
        return compositeAndDeviceCountDtoMap;
    }

    /**
     * 统计设备组下的设备数量
     *
     * @param dto 组织id
     * @param finalDeviceComposites 设备组id
     * @return 设备组下的设备数量
     */
    private Map<Long, Integer> getDeviceSizeMap(QueryDeviceCompositeDTO dto,
        List<DeviceComposite> finalDeviceComposites) {
        Map<Long, Integer> map = new HashMap<>();
        if (finalDeviceComposites == null) {
            return map;
        }
        //查询每个设备组下的设备数量
        List<Long> compositeIds = finalDeviceComposites.stream()
            .map(DeviceComposite::getDeviceCompositeId).distinct().collect(Collectors.toList());
        Map<Long, List<DeviceComposite>> compositeIdToSubMap = new HashMap<>();
        List<Long> allComposites = new ArrayList<>();
        for (Long compositeId : compositeIds) {
            //查询所有子设备组
            List<DeviceComposite> allDeviceComposites = findAllSubDeviceComposite(dto.getOrgId(),
                compositeId);
            if (CollectionUtils.isNotEmpty(allDeviceComposites)) {
                //维护compositeId下所有DeviceComposite信息map
                compositeIdToSubMap.put(compositeId, allDeviceComposites);
                allComposites.addAll(allDeviceComposites.stream()
                    .map(DeviceComposite::getDeviceCompositeId).collect(Collectors.toList()));
            }
        }
        List<DeviceCompositeItem> compositeItems = deviceCompositeItemMapper.
            listByOrgIdAndDeviceCompositeIds(dto.getOrgId(), allComposites);
        if (CollectionUtils.isNotEmpty(compositeItems)) {
                //设备Id列表
            Set<Long> deviceIdSet = compositeItems.stream()
                .map(DeviceCompositeItem::getDeviceId).collect(Collectors.toSet());
            //和传入的有权限设备Id列表做交集
            if (dto.getAllDeviceFlag() == null || !dto.getAllDeviceFlag()) {
                if (CollectionUtils.isNotEmpty(dto.getDeviceIds())) {
                    deviceIdSet.retainAll(dto.getDeviceIds());
                } else {
                    //说明这些设备都没有权限
                    deviceIdSet.clear();
                }
            }
            List<Device> devices = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(deviceIdSet)){
                QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
                queryDeviceDTO.setDeviceIds(new ArrayList<>(deviceIdSet));
                queryDeviceDTO.setOrgId(dto.getOrgId());
                queryDeviceDTO.setSceneType(dto.getSceneType());
                queryDeviceDTO.setDeviceTypes(dto.getDeviceTypes());
                devices = deviceManager.listByLikeCondition(queryDeviceDTO);
            }
            if (CollectionUtils.isNotEmpty(devices)) {
                Map<Long, Device> deviceMap = devices.stream().collect(Collectors
                    .toMap(Device::getDeviceId, device -> device));
                for (DeviceComposite deviceComposite : finalDeviceComposites) {
                    List<DeviceComposite> subDeviceComposites = compositeIdToSubMap
                        .get(deviceComposite.getDeviceCompositeId());
                    List<Long> subDeviceCompositeIds = subDeviceComposites.stream()
                        .map(DeviceComposite::getDeviceCompositeId).collect(Collectors.toList());
                    int deviceSize = 0;
                    //已计算过的设备Id列表
                    Set<Long> deviceIdSetTemp = new HashSet<>();
                    for (DeviceCompositeItem compositeItem : compositeItems) {
                        if (subDeviceCompositeIds.contains(compositeItem.getDeviceCompositeId())) {
                            if (!deviceIdSetTemp.contains(compositeItem.getDeviceId()) &&
                                deviceMap.get(compositeItem.getDeviceId()) != null) {
                                deviceSize++;
                                deviceIdSetTemp.add(compositeItem.getDeviceId());
                            }
                        }
                    }
                    map.put(deviceComposite.getDeviceCompositeId(), deviceSize);
                }
            } else {
                for (DeviceComposite deviceComposite : finalDeviceComposites) {
                    map.put(deviceComposite.getDeviceCompositeId(), 0);
                }
            }
        }
        return map;
    }

    /**
     * 获取这批设备组以及子组下设备数量情况
     *
     * @param orgId            组织 ID
     * @param deviceComposites 设备组实体list
     * @return {@link List }<{@link CompositeDeepDeviceSizeDTO }>
     */
    public List<CompositeDeepDeviceSizeDTO> getCompositeDeepDeviceSize(Long orgId,
        List<DeviceComposite> deviceComposites, Integer sceneType) {
        if (CollectionUtils.isEmpty(deviceComposites)) {
            return Collections.emptyList();
        }
        List<CompositeDeepDeviceSizeDTO> result = new ArrayList<>();

        // 加上"/"这样就只会找到子
        List<String> paths = deviceComposites.stream().map(f -> f.getPath() + "/")
            .collect(Collectors.toList());

        Map<String/*path*/, Long/*id*/> pathIdMap = deviceComposites.stream()
            .collect(Collectors.toMap(DeviceComposite::getPath,
                DeviceComposite::getDeviceCompositeId));

        // 分别获取他们的组和子组信息
        List<DeviceComposite> compositeByPathBatch = deviceCompositeMapper.getCompositeByPathBatch(
            orgId, paths, DeviceCompositeBizType.COMMON);

        Map<Long/*id*/, List<Long>/*所有的子包括子子等*/> idChildrenMap = new HashMap<>();

        for (Entry<String, Long> entry : pathIdMap.entrySet()) {
            String path = entry.getKey() + "/";
            Long deviceCompositeId = entry.getValue();

            // 所有匹配到的子，可能也是本身
            for (DeviceComposite composite : compositeByPathBatch) {
                if (composite.getPath().startsWith(path)) {
                    List<Long> ids = idChildrenMap.get(deviceCompositeId);
                    if (CollectionUtils.isNotEmpty(ids)) {
                        idChildrenMap.get(deviceCompositeId).add(composite.getDeviceCompositeId());
                    } else {
                        idChildrenMap.put(deviceCompositeId,
                            Lists.newArrayList(composite.getDeviceCompositeId()));
                    }
                }
            }
            // 匹配不到子,就是空子
            idChildrenMap.putIfAbsent(deviceCompositeId, Collections.emptyList());
        }
        // 获取这批设备组下面设备的sn情况

        Set<Long> compositeIds = CollectionUtils.isNotEmpty(idChildrenMap.keySet()) ? new HashSet<>(idChildrenMap.keySet()) : new HashSet<>();
        Set<Long> collect = idChildrenMap.values().stream().flatMap(Collection::stream)
            .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(collect)) {
            compositeIds.addAll(collect);
        }

        Map<Long/*设备组id*/, List<Long>/*设备id列表*/> compositeIdDeviceSnMap = new HashMap<>();
        List<DeviceCompositeItem> existCompositeItems = new ArrayList<>();
        List<DeviceCompositeItem> compositeItems = deviceCompositeItemMapper.listByCompositeIds(
            orgId, new ArrayList<>(compositeIds));
        if (CollectionUtils.isNotEmpty(compositeItems)) {
            List<Long> deviceIds = compositeItems.stream().map(DeviceCompositeItem::getDeviceId)
                .distinct().collect(Collectors.toList());
            QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
            queryDeviceDTO.setOrgId(orgId);
            queryDeviceDTO.setDeviceIds(deviceIds);
            queryDeviceDTO.setSceneType(sceneType);
            List<DeviceInfoDTO> deviceInfos = deviceService.listByLikeCondition(queryDeviceDTO)
                .pickDataThrowException();
            Map<Long, DeviceInfoDTO> deviceMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                deviceMap = deviceInfos.stream().collect(Collectors.
                    toMap(DeviceInfoDTO::getDeviceId, x -> x));
            }
            for (DeviceCompositeItem compositeItem :compositeItems) {
                DeviceInfoDTO deviceInfoDTO = deviceMap.get(compositeItem.getDeviceId());
                if (deviceInfoDTO != null) {
                    existCompositeItems.add(compositeItem);
                }
            }
            compositeIdDeviceSnMap = existCompositeItems.stream().collect(Collectors.groupingBy(
                DeviceCompositeItem::getDeviceCompositeId, Collectors.mapping(
                    DeviceCompositeItem::getDeviceId, Collectors.toList())));
        } else {
            // 如果都没有设备，就是空的设备数
            for (Long compositeId : compositeIds) {
                compositeIdDeviceSnMap.put(compositeId, Collections.emptyList());
            }
        }

        // 设备组以及子组下面的设备数量统计去重
        for (Entry<Long, List<Long>> entry : idChildrenMap.entrySet()) {
            Set<Long> deviceIds = new HashSet<>();
            Long compositeId = entry.getKey();
            List<Long> compositeIdChildren = entry.getValue();
            // 计算本组下面的设备数
            List<Long> deviceIdsTemp = compositeIdDeviceSnMap.get(compositeId);
            if (CollectionUtils.isNotEmpty(deviceIdsTemp)) {
                deviceIds.addAll(deviceIdsTemp);
            }
            // 遍历每个子组
            for (Long childCompositeId : compositeIdChildren) {
                if (CollectionUtils.isNotEmpty(compositeIdDeviceSnMap.get(childCompositeId))) {
                    for (Long deviceId : compositeIdDeviceSnMap.get(childCompositeId)) {
                        deviceIds.add(deviceId);
                    }
                }
            }
            CompositeDeepDeviceSizeDTO dto = new CompositeDeepDeviceSizeDTO();
            dto.setDeviceCompositeId(compositeId);
            dto.setDeviceSize(deviceIds.size());
            dto.setDeviceIds(new ArrayList<>(deviceIds));
            result.add(dto);
        }
        return result;
    }

    /**
     * 查询这批设备组下的设备情况
     *
     * @param orgId
     * @param deviceCompositeIds
     * @return
     */
    private List<DeviceCompositeItemDTO> getItemListByCompositeIdList(Long orgId,
        List<Long> deviceCompositeIds) {
        //查询所有的进行返回
        List<DeviceCompositeItemDTO> itemDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deviceCompositeIds)){
            return Collections.emptyList();
        }
        List<DeviceCompositeItem> itemList = deviceCompositeItemMapper.listByCompositeIds(orgId,
            deviceCompositeIds);
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<Long> itemDeviceIdList = new ArrayList<>();
            for (DeviceCompositeItem item : itemList) {
                itemDeviceIdList.add(item.getDeviceId());
            }
            List<Device> deviceByIdList = deviceManager.listByOrgIdAndIds(orgId, itemDeviceIdList);
            Map<Long, String> deviceMap = new HashMap<>();
            for (Device device : deviceByIdList) {
                deviceMap.put(device.getDeviceId(), device.getDeviceName());
            }
            Map<Long, String> deviceSnMap = deviceByIdList.stream().collect(
                Collectors.toMap(Device::getDeviceId, Device::getDeviceSn, (oldV, newV) -> newV));
            for (DeviceCompositeItem item : itemList) {
                if (deviceMap.get(item.getDeviceId()) == null){
                    // 设备不存在了
                    continue;
                }
                DeviceCompositeItemDTO deviceCompositeItemDto = new DeviceCompositeItemDTO();
                deviceCompositeItemDto.setOrgId(item.getOrgId());
                deviceCompositeItemDto.setDeviceCompositeId(item.getDeviceCompositeId());
                deviceCompositeItemDto.setDeviceId(item.getDeviceId());
                deviceCompositeItemDto.setDeviceType(item.getDeviceType());
                deviceCompositeItemDto.setDeviceName(deviceMap.get(item.getDeviceId()));
                deviceCompositeItemDto.setDeviceSn(
                    Optional.ofNullable(deviceSnMap.get(item.getDeviceId()))
                        .orElse(StringUtils.EMPTY));
                itemDtoList.add(deviceCompositeItemDto);
            }
        }
        return itemDtoList;
    }
}
