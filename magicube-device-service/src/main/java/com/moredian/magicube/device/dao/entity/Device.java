package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_orgi_equipment")
public class Device extends TimedEntity {

    private static final long serialVersionUID = 3353451031375958380L;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 三方设备id
     */
    private String thirdDeviceId;

    /**
     * 位置Id
     */
    private Long positionId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * 设备Sn
     */
    private String deviceSn;

    /**
     * 激活码
     */
    private String activeCode;

    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 设备MAC地址
     */
    private String macAddress;

    /**
     * 位置
     */
    private String position;

    /**
     * 设备扩展信息
     */
    private String extendsInfo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 设备容量超限：默认0-没有超限，1-超限
     */
    private Integer capacityExceeded;

    /**
     * 蓝牙mac地址
     */
    private String bluetoothMac;

    /**
     * 父设备sn
     */
    private String parentDeviceSn;

    /**
     * 是否为虚拟设备
     * 0-非虚拟设备，1-虚拟设备
     */
    private Integer virtualFlag;

    /**
     * 默认跳转应用
     */
    private String appCode;

    /**
     * 设备可关联应用
     */
    private String appCodeList;

    /**
     * 0行业，1分销
     */
    private Integer deviceFlag;

    /**
     * 设备时区
     */
    private Integer deviceTimeZone;

    /**
     * 设备语言
     */
    private Integer deviceLanguage;

}