<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DistributorMapper">
    <resultMap id="resultMap" type="com.moredian.magicube.device.dao.entity.Distributor">
        <result column="distributor_id" property="distributorId"/>
        <result column="device_sn_list" property="deviceSnList"/>
        <result column="org_name_list" property="orgNameList"/>
        <result column="distributor_name" property="distributorName"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_distributor
    </sql>

    <sql id="sql_columns">
        distributor_id,
        device_sn_list,
        org_name_list,
        distributor_name,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{distributorId},
        #{deviceSnList},
        #{orgNameList},
        #{distributorName},
        now(3),
        now(3)
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.Distributor">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <select id="listByOrgName" parameterType="string" resultType="com.moredian.magicube.device.dao.entity.Distributor">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where org_name_list like CONCAT('%',#{orgName},'%')
    </select>

    <select id="getByDeviceSn" parameterType="string" resultType="com.moredian.magicube.device.dao.entity.Distributor">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where device_sn_list like CONCAT('%',#{deviceSn},'%')
    </select>
</mapper>