package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.hephaestus.request.relation.GetRelationBySerialNumberRequest;
import com.moredian.hephaestus.response.relation.SimpleSpuInventoryRelationResponse;
import com.moredian.hephaestus.service.SpuInventoryRelationService;
import com.moredian.magicube.common.enums.DeviceStatus;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.core.org.response.PositionInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.core.org.service.PositionService;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceFlagEnum;
import com.moredian.magicube.device.enums.EnvConfigEnum;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypeManager;
import com.moredian.magicube.device.manager.DeviceTypeMapManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 新增设备管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class InsertDevicePipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    private static final Integer DEVICE_SN_MIN_LENGTH = 6;

    private static final String SEPARATOR = "-";

    private static final String REGEX_CHINESE = "[\u4e00-\u9fa5]";

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceTypeManager deviceTypeManager;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Autowired
    private DeviceTypeMapManager deviceTypeMapManager;

    @Resource
    private RedissonCacheComponent redissonCacheComponent;

    @SI
    private PositionService positionService;

    @SI
    private SpuInventoryRelationService spuInventoryRelationService;
    @SI
    private OrgService orgService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        Device device = (context.getDevice() == null ? new Device() : context.getDevice());
        BeanUtils.copyProperties(dto, device);
        device.setOrgId(context.getOrgId());
        PositionInfo positionInfo = positionService.getRootPosition(context.getOrgId())
            .pickDataThrowException();
        if (positionInfo != null) {
            device.setPositionId(positionInfo.getPositionId());
        }
        if (StringUtils.isBlank(dto.getDeviceName())) {
            if (dto.getDeviceSn().length() >= DEVICE_SN_MIN_LENGTH) {
                String sn = dto.getDeviceSn();
                String suffixName = sn.substring(sn.length() - DEVICE_SN_MIN_LENGTH);
                String deviceNamePrefix = "";
                GetRelationBySerialNumberRequest getRelationBySerialNumberRequest = new GetRelationBySerialNumberRequest();
                getRelationBySerialNumberRequest.setSerialNumberList(Arrays.asList(sn));
                ServiceResponse<List<SimpleSpuInventoryRelationResponse>> spuRelationServiceResp = spuInventoryRelationService.findSpuInventoryRelationBySerialNumberList(getRelationBySerialNumberRequest);
                if (spuRelationServiceResp.isSuccess() && spuRelationServiceResp.isExistData() && CollectionUtils.isNotEmpty(spuRelationServiceResp.getData())) {
                    SimpleSpuInventoryRelationResponse spuRelation = spuRelationServiceResp.getData().get(0);
                    if (spuRelation != null) {
                        deviceNamePrefix = spuRelation.getSpuDisplayName();
                    }
                }
                if (StringUtils.isBlank(deviceNamePrefix)) {
                    String deviceEnumName = deviceTypeManager.getName(dto.getDeviceType());
                    //将转换后的名字去除中文
                    deviceNamePrefix = deviceEnumName.replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                }
                if (StringUtils.isBlank(deviceNamePrefix) && deviceTypePropertyManager.containsDeviceType(
                    DeviceTypeConstants.NEED_DEFAULT_SPU_LIST, dto.getDeviceType())) {
                    deviceNamePrefix = deviceTypeMapManager.getDefaultSpu(dto.getDeviceType());
                }
                device.setDeviceName(deviceNamePrefix + SEPARATOR + suffixName);
            } else {
                String deviceEnumName = deviceTypeManager.getName(dto.getDeviceType());
                //将转换后的名字去除中文
                String deviceName = deviceEnumName.replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                if (StringUtils.isBlank(deviceName) && deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_SPU_LIST, dto.getDeviceType())) {
                    deviceName = deviceTypeMapManager.getDefaultSpu(dto.getDeviceType());
                }
                device.setDeviceName(deviceName);
            }
        } else {
            device.setDeviceName(dto.getDeviceName());
        }
        device.setActiveTime(new Date());
        device.setStatus(DeviceStatus.USABLE.getValue());
        //元石分销设备 如果position为空,则默认为设备名称
        if (EnvConfigEnum.FX.getCode().equals(context.getEnvironment()) && StringUtils.isBlank(dto.getPosition())) {
            device.setPosition(device.getDeviceName());
        } else {
            device.setPosition(dto.getPosition());
        }
        if (StringUtils.isNotBlank(dto.getThirdDeviceId())) {
            device.setThirdDeviceId(dto.getThirdDeviceId());
        }
        if (device.getDeviceFlag() == null){
            device.setDeviceFlag(DeviceFlagEnum.HY.getValue());
        }else {
            device.setDeviceFlag(dto.getDeviceFlag());
        }

        dto.setDeviceFlag(device.getDeviceFlag());

        // 如果是通通锁需要查询redis中的lockData并存入
        Integer deviceType = dto.getDeviceType();
        if (deviceType != null && deviceType == DeviceType.TTLOCK.getValue()) {
            String key = RedisConstants.getKey(RedisConstants.TT_LOCK_DATA, device.getDeviceSn());
            String lockData = (String) redissonCacheComponent.getObjectCache(key);
            device.setExtendsInfo(lockData);
        }
        deviceManager.insert(device);
        context.setDevice(device);
    }
}
