package com.moredian.magicube.device.enums;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2024/9/27 11:01
 */
public enum DeviceAdminSyncMethod {

    DEVICE_ADMIN_OLD_SYNC("old","进入设备主管理员老同步"),
    DEVICE_ADMIN_NEW_SYNC("new","进入设备主管理员新同步"),
    /**
     * 考虑到有的设备没接入新同步
     */
    DEVICE_ADMIN_ALL_SYNC("all","进入设备主管理员老同步+新同步一起发");


    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    DeviceAdminSyncMethod(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DeviceAdminSyncMethod getEnumByCode(String code) {
        BizAssert.isTrue(StringUtil.isNotBlank(code), "code cannot be empty");

        DeviceAdminSyncMethod[] values = DeviceAdminSyncMethod.values();
        for (DeviceAdminSyncMethod value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
