package com.moredian.magicube.device.generate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.FileOutConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.TemplateConfig;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

/**
 * <p>
 * 代码生成器演示
 * </p>
 */
public class ShardingMpGenerator {

    /**
     * <p>
     * MySQL 生成演示
     * </p>
     */
    public static void main(String[] args) throws Exception {
        generateCode();
    }

    public static final void generateCode() throws Exception {
        generateByTables(
        "hive_device_group"
        );
    }

    private static final void generateByTables(String... tableNames) throws Exception {
        if (tableNames.length<1){
            throw new Exception("至少填一个表");
        }

        AutoGenerator mpg = new AutoGenerator();
        GlobalConfig config = new GlobalConfig();
        mpg.setGlobalConfig(config);
        String dbUrl = "**************************************************************************************************************";
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setDbType(DbType.MYSQL)
                .setUrl(dbUrl)
                .setUsername("moredian")
                .setPassword("moredian@1")
                .setDriverName("com.mysql.jdbc.Driver");
        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig
                .setCapitalMode(true)
                .setEntityLombokModel(true)
                .setNaming(NamingStrategy.underline_to_camel)
                .setSuperEntityClass("com.moredian.magicube.device.dao.base.sharding.ShardingTimeEntity")
                .setSuperServiceClass("com.moredian.magicube.device.dao.base.sharding.IBaseShardingDao")
                .setSuperServiceImplClass("com.moredian.magicube.device.dao.base.sharding.IBaseShardingDaoImpl")
                .setTablePrefix("hive")
                .setSuperEntityColumns(new String[]{"org_id", "valid", "gmt_create", "gmt_modify"})
                .setInclude(tableNames);//修改替换成你需要的表名，多个表名传数组
        config.setActiveRecord(false)
                .setAuthor("Generator")
                .setOutputDir("./magicube-device-service/src/main/java")
                .setServiceName("%sDao")
                .setServiceImplName("%sDaoImpl")
                .setFileOverride(false)
                .setEnableCache(false)
                .setBaseResultMap(true)
                .setBaseColumnList(true);





        // 注入自定义配置，可以在 VM 中使用 cfg.abc 【可无】
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                setMap(new HashMap<String, Object>());
            }
        };

        List<FileOutConfig> focList = new ArrayList<>();

        focList.add(new FileOutConfig("/templates/entity.java.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return "./magicube-device-service/src/main/java/com/moredian/magicube/device/dao/entity/" + tableInfo.getEntityName() + ".java";
            }
        });

        focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return "./magicube-device-service/src/main/java/com/moredian/magicube/device/dao/mapper/" + tableInfo.getEntityName() + "Mapper.xml";
            }
        });

        focList.add(new FileOutConfig("/templates/mapper.java.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return "./magicube-device-service/src/main/java/com/moredian/magicube/device/dao/mapper/" + tableInfo.getEntityName() + "Mapper.java";
            }
        });

        focList.add(new FileOutConfig("/templates/service.java.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return "./magicube-device-service/src/main/java/com/moredian/magicube/device/dao/enhance/" + tableInfo.getEntityName() + "Dao.java";
            }
        });
        focList.add(new FileOutConfig("/templates/serviceImpl.java.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return "./magicube-device-service/src/main/java/com/moredian/magicube/device/dao/enhance/impl/" + tableInfo.getEntityName() + "DaoImpl.java";
            }
        });
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);
        TemplateConfig tc = new TemplateConfig();
        tc.setXml(null);
        tc.setController(null);
        tc.setService(null);
        tc.setServiceImpl(null);
        mpg.setTemplate(tc);
        mpg.setGlobalConfig(config)
                .setDataSource(dataSourceConfig)
                .setStrategy(strategyConfig)
                .setPackageInfo(
                        new PackageConfig()
                                .setParent("com.moredian.magicube.device")
                                .setMapper("dao.mapper")
                                .setEntity("dao.entity")
                                .setService("dao.enhance")
                                .setServiceImpl("dao.enhance.impl")
                )
                .execute();
    }


}