package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.space.dto.device.BindSpaceTreeDto;
import com.moredian.space.dto.tree.QueryTreeDTO;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.service.SpaceTreeService;
import com.moredian.space.service.v2.SpaceTreeDeviceRelationServiceV2;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 设备绑定空间管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BindSpaceTreePipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private SpaceTreeDeviceRelationServiceV2 treeDeviceRelationServiceV2;

    @SI
    private SpaceTreeService treeService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        Long orgId = context.getSpaceOrgId() != null ? context.getSpaceOrgId() : context.getOrgId();
        //如果没有空间，默认使用根目录(没有空间分为2种情况，第一种激活码里面没有空间信息，第二种没传激活码)
        if (context.getTreeId() == null) {
            QueryTreeDTO queryTreeDTO = new QueryTreeDTO();
            queryTreeDTO.setOrgId(orgId);
            queryTreeDTO.setParentId(0L);
            List<TreeDTO> trees = treeService.listByCondition(queryTreeDTO).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(trees)) {
                context.setTreeId(trees.get(0).getTreeId());
            }
        }

        Device device = (context.getDevice() == null ? new Device() : context.getDevice());
        BindSpaceTreeDto bindSpaceTreeDto = new BindSpaceTreeDto();
        bindSpaceTreeDto.setOrgId(context.getOrgId());
        bindSpaceTreeDto.setDeviceId(device.getDeviceId().toString());
        bindSpaceTreeDto.setTreeId(context.getTreeId());
        bindSpaceTreeDto.setDeviceSource(1);
        if (context.getDirection() != null) {
            bindSpaceTreeDto.setDirection(context.getDirection());
        }
        //由于事务原因，激活绑空间时，空间服务不发送绑定消息，上移到设备服务中发送。
        bindSpaceTreeDto.setSendMsgFlag(Boolean.FALSE);
        Long treeDeviceRelationId = treeDeviceRelationServiceV2.bindSpace(bindSpaceTreeDto).pickDataThrowException();
        context.setTreeDeviceRelationId(treeDeviceRelationId);
    }
}
