package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.device.DeviceWhiteListDTO;
import com.moredian.magicube.device.manager.DeviceWhiteListManager;
import com.moredian.magicube.device.service.DeviceWhiteListService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author:dongchao
 * @Description:
 * @Date: 2020/2/25 1:33
 */
@SI
public class DeviceWhiteListServiceImpl implements DeviceWhiteListService {

    @Autowired
    private DeviceWhiteListManager deviceWhiteListManager;

    @Override
    public ServiceResponse insert(DeviceWhiteListDTO deviceWhiteListDto) {
        return deviceWhiteListManager.insert(deviceWhiteListDto);
    }

    @Override
    public ServiceResponse insertBatch(List<DeviceWhiteListDTO> whiteListDtos) {
        return deviceWhiteListManager.insertBatch(whiteListDtos);
    }

    @Override
    public ServiceResponse<List<DeviceWhiteListDTO>> getDeviceWhiteList(DeviceWhiteListDTO deviceWhiteListDto) {
        return deviceWhiteListManager.getDeviceWhiteList(deviceWhiteListDto);
    }

    @Override
    public ServiceResponse unBind(String deviceSn) {
        return deviceWhiteListManager.unBind(deviceSn);
    }

    @Override
    public ServiceResponse bind(DeviceWhiteListDTO deviceWhiteListDto) {
        return deviceWhiteListManager.bind(deviceWhiteListDto);
    }

    @Override
    public ServiceResponse<Boolean> hasAvailableDevice(Long orgId) {
        return deviceWhiteListManager.hasAvailableDevice(orgId);
    }
}
