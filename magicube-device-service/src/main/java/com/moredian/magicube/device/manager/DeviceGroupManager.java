package com.moredian.magicube.device.manager;

import com.moredian.magicube.common.enums.GroupAppType;
import com.moredian.magicube.device.dao.entity.DeviceGroup;
import com.moredian.magicube.device.dto.composite.DeviceCompositeGroupRelationAddReq;
import com.moredian.magicube.device.dto.group.DeviceGroupPersonCountDTO;
import com.moredian.magicube.device.dto.group.ResetGroupRelationDTO;
import com.moredian.magicube.device.dto.group.SimpleDeviceGroupDTO;
import java.util.List;
import java.util.Map;

/**
 * 设备组关系
 *
 * <AUTHOR>
 */
public interface DeviceGroupManager {

    /**
     * 新增默认关系
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    Boolean insertDefaultDeviceGroup(Long orgId, Long deviceId);

    /**
     * 根据机构号和组Id获取设备Id列表
     *
     * @param orgId   机构号
     * @param groupId 组Id
     * @return 设备Id列表
     */
    List<Long> listDeviceIdByOrgIdAndGroupId(Long orgId, Long groupId);

    /**
     * 根据机构号和组Id列表获取设备Id列表
     *
     * @param orgId    机构号
     * @param groupIds 组Id列表
     * @return 设备Id列表
     */
    List<Long> listDeviceIdByOrgIdAndGroupIds(Long orgId, List<Long> groupIds);

    /**
     * 根据机构号和组Id列表获取关系列表
     *
     * @param orgId    机构号
     * @param groupIds 组Id列表
     * @return
     */
    List<DeviceGroup> listByOrgIdAndGroupIds(Long orgId, List<Long> groupIds);

    /**
     * 查询设备Id列表
     *
     * @param orgId   机构号
     * @param groupId 组Id
     * @param offset  开始数
     * @param limit   查询数量
     * @return
     */
    List<Long> listDeviceIdByCondition(Long orgId, Long groupId, int offset,
        int limit);

    /**
     * 根据机构号和设备Id列表查询组Id列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<Long> listGroupIdByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds);

    /**
     * 根据机构号和设备Id查询组名称列表
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @return
     */
    List<String> listGroupNameByCondition(Long orgId, Long deviceId);

    /**
     * 根据机构号和设备Id列表查询列表
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @return
     */
    List<DeviceGroup> listByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds);

    /**
     * 根据机构号和设备Id删除关系
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @param groupIds  组Id列表
     * @return
     */
    boolean deleteByCondition(Long orgId, List<Long> deviceIds, List<Long> groupIds);

    /**
     * 重置设备和多个组的关系
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @param groupIds 组Id列表
     * @param appType  权限组应用类型
     * @return
     * @see GroupAppType
     */
    Boolean resetRelationByDeviceId(Long orgId, Long deviceId, List<Long> groupIds,
        Integer appType);

    /**
     *  增量添加多个设备和多个组的关系
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @param groupIds  组Id列表
     * @param appType   权限组应用类型
     * @return
     */
    Boolean insertRelationByDeviceIds(Long orgId, List<Long> deviceIds, List<Long> groupIds, Integer appType);

    /**
     * 重置一个组和多台设备的关系
     *
     * @param orgId     机构号
     * @param groupId   组Id
     * @param deviceIds 设备Id列表
     * @param appType   权限组应用类型
     * @return
     * @see GroupAppType
     */
    Boolean resetRelationByGroupId(Long orgId, Long groupId, List<Long> deviceIds, Integer appType);


    /**
     * 重置一个组和多台设备的关系 替换resetRelationOfDeviceGroup
     * 由于增量事件中心需要过滤新增绑定与删除绑定的设备，所以返回参数中需要更改，故拷出一个接口来修改
     *
     * @param orgId     机构号
     * @param groupId   组Id
     * @param deviceIds 设备Id列表
     * @param appType   权限组应用类型
     * @return
     * @see GroupAppType
     */
    ResetGroupRelationDTO resetRelationByGroupIdAndDeviceIdList(Long orgId, Long groupId, List<Long> deviceIds,
                                                                                 Integer appType);

    /**
     * 查询指定设备绑定的去重成员总数
     * 说明：可以指定群组类型，也可以不指定群组类型，当不指定时就默认查询全部群组人员去重总数
     *
     * @param dto
     * @return
     */
    Integer distinctCountDeviceGroupPerson(DeviceGroupPersonCountDTO dto);

    List<DeviceGroup> findRelationByGroupId(Long orgId, Long groupId);

    List<SimpleDeviceGroupDTO> findDeviceGroupByGroupIds(Long orgId, List<Long> groupIds);

    Map<Long, List<Long>> getDeviceIdToGroupIdsByOrgIdAndDeviceIds(Long orgId,
        List<Long> deviceIds, Integer appType);

    /**
     * 重置新设备组关系
     * <p>
     *     	1. 先根据新设备删除所有非关联设备(即不在)
     *     	2.
     *  </p>
     * @param req 新设备组关系 {最新设备,最新设备组}
     * @return 是否成功
     */
    boolean resetRelationOfDeviceGroup(DeviceCompositeGroupRelationAddReq req);
}