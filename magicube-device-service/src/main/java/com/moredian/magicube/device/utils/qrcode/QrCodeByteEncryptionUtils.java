package com.moredian.magicube.device.utils.qrcode;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * @Classname： QrCodeByteEncryptionUtils
 * @Date: 2023/2/8 5:30 下午
 * @Author: _AF
 * @Description: 激活二维码加密
 */
public class QrCodeByteEncryptionUtils {


    public static byte[] encryptzeropadding(byte[] dataBytes, byte[] key, byte[] iv) {


        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            int blockSize = cipher.getBlockSize();
            int length = dataBytes.length;
            // 计算需填充长度
            if (length % blockSize != 0) {
                length = length + (blockSize - (length % blockSize));
            }
            byte[] plaintext = new byte[length];
            // 填充
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            // 设置偏移量参数
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encryped = cipher.doFinal(plaintext);

            return encryped;

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return null;
        }
    }

    public static byte[] desEncryptzeropadding(byte[] data, byte[] key, byte[] iv) {


        try {
            //byte[] encryp = parseHexStr2Byte(data);
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] original = cipher.doFinal(data);
            return original;
        } catch (Exception e) {
            // TODO: handle exception
        }
        return null;
    }
}
