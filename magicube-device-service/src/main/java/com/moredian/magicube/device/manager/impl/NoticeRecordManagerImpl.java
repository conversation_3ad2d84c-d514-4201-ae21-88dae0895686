package com.moredian.magicube.device.manager.impl;


import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.NoticeRecord;
import com.moredian.magicube.device.dao.mapper.NoticeRecordDao;
import com.moredian.magicube.device.manager.NoticeRecordManager;
import com.moredian.magicube.device.model.StatisticsModel;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@Service
public class NoticeRecordManagerImpl implements NoticeRecordManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private NoticeRecordDao noticeRecordDao;

    @Override
    public Long addRecord(NoticeRecord noticeRecord) {
        if (Optional.ofNullable(noticeRecord.getNoticeRecordId()).isPresent()) {
            noticeRecordDao.insertNoticeRecord(noticeRecord);
            return noticeRecord.getNoticeRecordId();
        }
        Long id = idgeneratorService.getNextIdByTypeName(NoticeRecord.class.getName())
            .pickDataThrowException();
        noticeRecord.setNoticeRecordId(id);
        noticeRecordDao.insertNoticeRecord(noticeRecord);
        return id;
    }

    @Override
    public int countRecords(StatisticsModel statisticsModel) {
        return noticeRecordDao.countRecords(statisticsModel.getOrgId(),
            statisticsModel.getDeviceSn(), statisticsModel.getNoticeEvent(),
            statisticsModel.getNoticeResult(), statisticsModel.getStartDay(),
            statisticsModel.getEndDay());
    }

    @Override
    public Long getLastOfflineTime(Long orgId, String deviceSn, Integer noticeEvent) {
        return noticeRecordDao.getLastOfflineTime(orgId, deviceSn, noticeEvent);
    }
}
