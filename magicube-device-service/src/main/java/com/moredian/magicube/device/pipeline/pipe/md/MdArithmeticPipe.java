package com.moredian.magicube.device.pipeline.pipe.md;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.ActivityRecCoreType;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.VerifyChannel;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处理魔点识别通道和算法管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MdArithmeticPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private OrgService orgService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        boolean isHuiEyeSelf = (null != dto.getRegCoreType()) && dto.getRegCoreType()
            .equals(ActivityRecCoreType.HUIEYE_SELF.getValue());
        if (isHuiEyeSelf) {
            Integer verifyChannel = activityRecCoreTypeToVerifyChannel(dto.getRegCoreType());
            /*orgService.markVerifyChannelAndArithmetic(dto.getOrgId(), verifyChannel,
                dto.getRegArithmetic()).pickDataThrowException();*/
        } else {
            // 为了慧眼支持访客，访客机先不设置识别通道
            if (DeviceType.BOARD_VISITOR.getValue() != dto.getDeviceType()) {
                ServiceResponse<Boolean> regResponse = orgService
                    .markVerifyChannel(context.getOrgId(), VerifyChannel.CLOUDEYE.getValue());
                if (regResponse != null && !regResponse.isSuccess()) {
                    regResponse.pickDataThrowException();
                }
            }
        }
    }

    private Integer activityRecCoreTypeToVerifyChannel(Integer activityRecCoreType) {
        log.debug(" --- activityRecCoreTypeToVerifyChannel --- activityRecCoreType=" + (
            activityRecCoreType != null ? activityRecCoreType : "NULL"));

        if (activityRecCoreType == null) {
            return null;
        }

        if (ActivityRecCoreType.HUIYAN_THIRDPAD.equals(activityRecCoreType)
            || ActivityRecCoreType.HUIEYE_SELF.equals(activityRecCoreType)) {
            return VerifyChannel.HUIEYE.getValue();
        } else if (ActivityRecCoreType.THIRDPAD_THIRDPAD.equals(activityRecCoreType)) {
            return VerifyChannel.THIRDPART.getValue();
        } else {
            return VerifyChannel.CLOUDEYE.getValue();
        }
    }
}
