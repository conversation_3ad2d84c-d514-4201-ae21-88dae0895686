package com.moredian.magicube.device.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 标准写入日志格式
 *
 * @description:
 * @author: gongchang
 * @time: 2020/6/23 17:27
 */

@Setter
@Getter
@ToString
public class MonitorLogModel implements Serializable {

    private static final long serialVersionUID = 2302983022636123312L;

    private String eventCode;
    private Long eventTime;
    private String eventData;
    private String indicatorSn;
    private BigDecimal indicatorNum;
    private Long orgId;
    private String deviceSn;
    private String personId;
    private String files;

    //version2 add properties
    private String orgName;
    private Integer deviceType;
    private String deviceName;

    //version3 add properties
    private String appVersion;

}
