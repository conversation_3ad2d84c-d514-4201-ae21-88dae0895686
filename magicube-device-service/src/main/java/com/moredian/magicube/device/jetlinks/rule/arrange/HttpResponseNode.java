package com.moredian.magicube.device.jetlinks.rule.arrange;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description Http 相应节点
 * @create 2024-12-24 18:14
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class HttpResponseNode extends Node{

    private static final String TYPE = "http response";

    private Map<String, String> headers = new HashMap<>();

    private String statusCode;

    public HttpResponseNode() {
        super(TYPE);
    }
}
