package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.condition.white.QueryWhiteDeviceCondition;
import com.moredian.magicube.device.dto.white.UpdateWhiteDeviceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-09-22
 */

@Mapper
public interface InventoryDeviceMapper {

    /**
     * 新增白名单
     *
     * @param inventoryDevice 白名单信息
     * @return
     */
    int insert(InventoryDevice inventoryDevice);

    /**
     * 批量新增设备白名单
     *
     * @param list 设备白名单信息列表
     * @return
     */
    int batchInsert(@Param("list") List<InventoryDevice> list);

    /**
     * 根据设备sn查询设备白名单信息
     *
     * @param deviceSn 设备sn
     * @return
     */
    InventoryDevice getByDeviceSn(@Param("deviceSn") String deviceSn);

    /**
     * 根据第三方Id查询白名单设备信息
     *
     * @param orgId         机构号
     * @param thirdDeviceId 第三方Id
     * @return
     */
    InventoryDevice getByOrgIdAndThirdDeviceId(@Param("orgId") Long orgId, @Param("thirdDeviceId") String thirdDeviceId);

    /**
     * 根据第设备sn和激活码查询白名单设备信息
     *
     * @param deviceSn       设备sn
     * @param activationCode 激活码
     * @return
     */
    InventoryDevice getByDeviceSnAndActivationCode(@Param("deviceSn") String deviceSn, @Param("activationCode") String activationCode);

    /**
     * 根据条件查询设备白名单列表
     *
     * @param condition 查询条件
     * @return
     */
    List<InventoryDevice> listByCondition(QueryWhiteDeviceCondition condition);

    /**
     * 根据设备sn列表查询设备白名单信息列表
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    List<InventoryDevice> listByDeviceSns(@Param("deviceSns") List<String> deviceSns);

    /**
     * 根据设备sn删除白名单信息
     *
     * @param deviceSn 设备sn列表
     * @return
     */
    int delete(@Param("deviceSn") String deviceSn);

    /**
     * 批量更新设备激活信息
     *
     * @param list 设备白名单信息列表
     * @return
     */
    void batchUpdate(@Param("list") List<UpdateWhiteDeviceDTO> list);

    /**
     * 更新白名单设备信息
     *
     * @param inventoryDevice 更新白名单信息列表
     * @return
     */
    void update(InventoryDevice inventoryDevice);

    /**
     * 解绑设备接口
     *
     * @param inventoryDevice
     * @return
     */
    void unbindDevice(InventoryDevice inventoryDevice);
}
