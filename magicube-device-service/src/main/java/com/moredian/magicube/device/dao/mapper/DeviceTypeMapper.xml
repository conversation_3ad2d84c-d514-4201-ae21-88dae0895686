<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceTypeMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceType">
    <id column="id" property="id"/>
    <result column="device_type" property="deviceType"/>
    <result column="device_type_name" property="deviceTypeName"/>
    <result column="description" property="description"/>
    <result column="status" property="status"/>
    <result column="device_type_pic" property="deviceTypePic"/>
    <result column="product_model_code" property="productModelCode"/>
    <result column="device_type_inner_name" property="deviceTypeInnerName"/>
    <result column="device_type_name_hy" property="deviceTypeNameHy"/>
    <result column="device_type_name_ding" property="deviceTypeNameDing"/>
    <result column="device_type_name_ml" property="deviceTypeNameMl"/>
    <result column="device_type_display_name_hy" property="deviceTypeDisplayNameHy"/>
    <result column="device_type_display_name_ding" property="deviceTypeDisplayNameDing"/>
    <result column="device_type_display_name_ml" property="deviceTypeDisplayNameMl"/>
    <result column="config" property="config"/>
    <result column="operator_name" property="operatorName"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <sql id="sql_table">
    hive_device_type
  </sql>

  <sql id="sql_columns">
    id,
    device_type,
    device_type_name,
    description,
    status,
    device_type_pic,
    product_model_code,
    device_type_inner_name,
    device_type_name_hy,
    device_type_name_ding,
    device_type_name_ml,
    device_type_display_name_hy,
    device_type_display_name_ding,
    device_type_display_name_ml,
    config,
    operator_name,
    gmt_create,
    gmt_modify
  </sql>

  <sql id="sql_values">
    #{id},
    #{deviceType},
    #{deviceTypeName},
    #{description},
    #{status},
    #{deviceTypePic},
    #{productModelCode},
    #{deviceTypeInnerName},
    #{deviceTypeNameHy},
    #{deviceTypeNameDing},
    #{deviceTypeNameMl},
    #{deviceTypeDisplayNameHy},
    #{deviceTypeDisplayNameDing},
    #{deviceTypeDisplayNameMl},
    #{config},
    #{operatorName},
    now(3),
    now(3)
  </sql>

  <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceType">
    INSERT INTO
    <include refid="sql_table"/>
    (
    <include refid="sql_columns"/>
    )
    VALUES
    (
    <include refid="sql_values"/>
    )
  </insert>

  <update id="update" parameterType="com.moredian.magicube.device.dao.entity.DeviceType">
    update
    <include refid="sql_table"/>
    <set>
      <if test="deviceType != null">
        device_type = #{deviceType},
      </if>
      <if test="deviceTypeName != null and deviceTypeName != ''">
        device_type_name = #{deviceTypeName},
      </if>
      <if test="deviceTypePic != null and deviceTypePic != ''">
        device_type_pic=#{deviceTypePic,jdbcType=VARCHAR},
      </if>
      <if test="productModelCode != null and productModelCode != ''">
        product_model_code=#{productModelCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeInnerName != null and deviceTypeInnerName != ''">
        device_type_inner_name=#{deviceTypeInnerName,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeNameHy != null and deviceTypeNameHy != ''">
        device_type_name_hy=#{deviceTypeNameHy,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeNameDing != null and deviceTypeNameDing != ''">
        device_type_name_ding=#{deviceTypeNameDing,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeNameMl != null and deviceTypeNameMl != ''">
        device_type_name_ml=#{deviceTypeNameMl,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeDisplayNameHy != null and deviceTypeDisplayNameHy != ''">
        device_type_display_name_hy=#{deviceTypeDisplayNameHy,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeDisplayNameDing != null and deviceTypeDisplayNameDing != ''">
        device_type_display_name_ding=#{deviceTypeDisplayNameDing,jdbcType=VARCHAR},
      </if>
      <if test="deviceTypeDisplayNameMl != null and deviceTypeDisplayNameMl != ''">
        device_type_display_name_ml=#{deviceTypeDisplayNameMl,jdbcType=VARCHAR},
      </if>
      <if test="config != null and config != ''">
        config=#{config,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        description = #{description},
      </if>
      <if test="operatorName != null and operatorName != ''">
        operator_name = #{operatorName},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      gmt_modify = now(3)
    </set>
    where id = #{id}
  </update>

  <delete id="deleteByDeviceType" parameterType="integer">
    delete from
    <include refid="sql_table"/>
    where device_type = #{deviceType}
  </delete>

  <select id="getById" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where id = #{id}
  </select>

  <select id="getByType" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where device_type = #{deviceType}
    and status = 1
  </select>

  <select id="getByDeviceType" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where device_type = #{deviceType}
  </select>

  <select id="list" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
  </select>

  <select id="listByTypes" parameterType="integer" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where
    <if test="deviceTypes != null and deviceTypes.size() > 0">
      device_type in
      <foreach collection="deviceTypes" index="index" item="deviceType" open="(" separator=","
        close=")">
        #{deviceType}
      </foreach>
    </if>
  </select>

  <select id="getName" resultType="java.lang.String">
    select device_type_name from
    <include refid="sql_table"/>
    where device_type = #{deviceType}
  </select>

  <select id="listByCondition"
    parameterType="com.moredian.magicube.device.dto.type.QueryDeviceTypeDTO"
    resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where 1 = 1
    <if test="deviceType != null">
      and device_type = #{deviceType}
    </if>
    <if test="productModelCode != null and productModelCode != ''">
      and product_model_code = #{productModelCode}
    </if>
    <if test="keywords != null and keywords != ''">
      and (device_type_name = #{keywords} or device_type_inner_name = #{keywords})
    </if>
  </select>

  <select id="getByProductModelCode" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where product_model_code = #{productModelCode}
  </select>

  <select id="listByProductModelCodes" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    <if test="productModelCodes != null and productModelCodes.size() > 0">
      where product_model_code in
      <foreach collection="productModelCodes" index="index" item="productModelCode" open="(" separator=","
        close=")">
        #{productModelCode}
      </foreach>
    </if>
  </select>

  <select id="listDeviceTypeLikeName" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="sql_columns"/>
    from
    <include refid="sql_table"/>
    where 1 = 1
    <if test="keywords != null and keywords != ''">
      and (device_type_name = #{keywords} or device_type_inner_name = #{keywords})
    </if>
  </select>
</mapper>
