package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.qrcode.OrgNetwork;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Classname： EndAuditMapper
 * @Date: 2022/4/14 5:27 下午
 * @Author: _AF
 * @Description: 手动结束审批信息
 */
@Mapper
public interface OrgNetworkMapper {


    /**
     * 插入
     *
     * @param orgNetwork
     * @return
     */
    int insert(OrgNetwork orgNetwork);

    /**
     * 编辑机构配网信息
     *
     * @param orgNetwork 网络信息
     * @return 二维码路径
     */
    Boolean update(OrgNetwork orgNetwork);

    /**
     * 获取
     *
     * @param orgId
     * @param id
     * @return
     */
    OrgNetwork getById(@Param("orgId") Long orgId, @Param("id") Long id);

    /**
     * 获取配网码信息列表
     *
     * @param orgId
     * @return
     */
    List<OrgNetwork> listNetworkInfoByOrgId(@Param("orgId") Long orgId);

    /**
     * 根据网络Id删除机构配网信息
     *
     * @param orgId 机构Id
     * @param id    网络Id
     * @return 二维码路径
     */
    void deleteByNetworkId(@Param("orgId") Long orgId, @Param("id") Long id);

    /**
     * 根据条件查询
     *
     * @param orgNetwork
     * @return
     */
    List<OrgNetwork> listByCondition(OrgNetwork orgNetwork);
}
