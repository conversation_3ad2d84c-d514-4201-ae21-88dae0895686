package com.moredian.magicube.device.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.moredian.magicube.device.dao.entity.DeviceComposite;
import com.moredian.magicube.device.dto.composite.DeviceCompositeAndDeviceCountDTO;
import com.moredian.magicube.device.dto.composite.DeviceCompositeTreeDTO;
import com.moredian.magicube.device.manager.bo.TreeNode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2024/11/14 17:06
 */
public class BizUtil {

    /**
     * 来构建设备组树的数据一定是在一个业务类型下的
     * 组装子组
     *
     * @param deviceComposites 器件复合材料
     * @param parentId         父 ID
     * @return {@link List }<{@link TreeNode }>
     */
    public static List<TreeNode> buildTreeChildren(List<DeviceComposite> deviceComposites, Long parentId) {
        Map<Long, TreeNode> nodeMap = new HashMap<>();
        List<TreeNode> result = new ArrayList<>();

        // 转化节点，收集根下一级节点
        for (DeviceComposite composite : deviceComposites) {
            TreeNode node = new TreeNode(composite.getDeviceCompositeId());
            nodeMap.put(composite.getDeviceCompositeId(), node);
            if (Objects.equals(composite.getParentId(), parentId)) {
                // 如果父组是传入的父组，就直接加入到返回结果中
                result.add(node);
            }
        }

        // 构建父子关系
        for (DeviceComposite composite : deviceComposites) {
            // 父组不是根，就需要更新。如果父组是根就算一级组，一级组直接平铺展示，不需要加入到上级节点
            if (!Objects.equals(composite.getParentId(), parentId)) {
                // 当前节点的信息
                TreeNode currNode = nodeMap.get(composite.getDeviceCompositeId());
                // 当前节点的父节点信息，有可能是一级节点
                TreeNode parentNode = nodeMap.get(composite.getParentId());
                if (parentNode != null) {
                    parentNode.getChildren().add(currNode);
                }
            }
        }

        return result;
    }


    /**
     * 构建树
     * 包括父节点
     *
     * @param deviceComposites 器件复合材料
     * @param parentId         父 ID
     * @return {@link TreeNode }
     */
    public static TreeNode buildTree(List<DeviceComposite> deviceComposites, Long parentId) {
        Map<Long, TreeNode> nodeMap = new HashMap<>();
        TreeNode treeNode = null;

        // 转化节点，收集根下一级节点
        for (DeviceComposite composite : deviceComposites) {
            TreeNode node = new TreeNode(composite.getDeviceCompositeId());
            nodeMap.put(composite.getDeviceCompositeId(), node);
            if (Objects.equals(composite.getDeviceCompositeId(), parentId)) {
                // 如果父组是传入的父组，就直接加入到返回结果中
                treeNode = node;
            }
        }

        // 构建父子关系
        for (DeviceComposite composite : deviceComposites) {
            // 如果不是根组就可以来更新上级
            if (!Objects.equals(composite.getDeviceCompositeId(), parentId)) {
                // 当前节点的信息
                TreeNode currNode = nodeMap.get(composite.getDeviceCompositeId());
                // 当前节点的父节点信息，有可能是一级节点
                TreeNode parentNode = nodeMap.get(composite.getParentId());
                if (parentNode != null) {
                    parentNode.getChildren().add(currNode);
                }
            }
        }

        return nodeMap.get(treeNode.getId());
    }



    /**
     * 单树转设备组，带树结构，并限制层级
     *
     * @param treeNode          tree 节点
     * @param deviceCountDtoMap 设备计数 DTO 映射
     * @param currentLevel      当前层级
     * @param maxLevel          最大允许层级
     * @return {@link DeviceCompositeAndDeviceCountDTO }
     */
    public static DeviceCompositeAndDeviceCountDTO convertToDto(TreeNode treeNode,
        Map<Long, DeviceCompositeAndDeviceCountDTO> deviceCountDtoMap, int currentLevel, Integer maxLevel) {
        if (maxLevel == null){
            maxLevel = Integer.MAX_VALUE;
        }
        if (currentLevel > maxLevel) {
            return null; // 如果当前层级超过最大层级，返回null
        }

        DeviceCompositeAndDeviceCountDTO deviceCountDto = deviceCountDtoMap.get(treeNode.getId());

        if (deviceCountDto == null) {
            return null; //找不到实体
        }

        if (CollUtil.isNotEmpty(treeNode.getChildren())){
            // 当前有子设备组
            deviceCountDto.setHasChildren(Boolean.TRUE);
        }else {
            return deviceCountDto;
        }
        // 具化子设备组
        for (TreeNode childNode : treeNode.getChildren()) {
            DeviceCompositeAndDeviceCountDTO childDto = convertToDto(childNode, deviceCountDtoMap, currentLevel + 1, maxLevel);
            if (childDto != null) {
                if (CollUtil.isNotEmpty( deviceCountDto.getChildren())){
                    deviceCountDto.getChildren().add(childDto);
                }else {
                    List<DeviceCompositeAndDeviceCountDTO> list = new ArrayList<>();
                    list.add(childDto);
                    deviceCountDto.setChildren(list);
                }
            }
        }

        return deviceCountDto;
    }

    /**
     * 多树转设备组，带树结构，并限制层级
     *
     * @param treeNodes         树节点
     * @param deviceCountDtoMap 设备计数 DTO 映射
     * @param maxLevel          最大允许层级
     * @return {@link List }<{@link DeviceCompositeAndDeviceCountDTO }>
     */
    public static List<DeviceCompositeAndDeviceCountDTO> buildDeviceCompositeDtoTree(
        List<TreeNode> treeNodes, Map<Long, DeviceCompositeAndDeviceCountDTO> deviceCountDtoMap, Integer maxLevel) {
        List<DeviceCompositeAndDeviceCountDTO> result = new ArrayList<>();
        for (TreeNode treeNode : treeNodes) {
            DeviceCompositeAndDeviceCountDTO dto = convertToDto(treeNode, deviceCountDtoMap, 1, maxLevel);
            if (dto != null) {
                result.add(dto);
            }
        }

        return result;
    }


    public static Map<Long, DeviceCompositeAndDeviceCountDTO> buildDeviceCompositeAndCountMap(
        List<DeviceComposite> compositeList) {
        return compositeList.stream()
            .collect(Collectors.toMap(DeviceComposite::getDeviceCompositeId, v -> {
                DeviceCompositeAndDeviceCountDTO dto = new DeviceCompositeAndDeviceCountDTO();
                BeanUtils.copyProperties(v, dto);
                return dto;
            }));
    }

    /**
     * 构建树
     *
     * @param deviceList 设备列表
     * @return {@link List }<{@link DeviceCompositeTreeDTO }>
     */// 构建树形结构
    public static List<DeviceCompositeTreeDTO> compositeBuildTree(List<DeviceComposite> deviceList) {
        List<DeviceCompositeTreeDTO> compositeTreeDtoList = BeanUtil.copyToList(deviceList,
            DeviceCompositeTreeDTO.class);

        // 按 path 长度排序，确保父节点先处理
        compositeTreeDtoList.sort(Comparator.comparingInt(d -> d.getPath().length()));

        // 返回的根节点列表
        List<DeviceCompositeTreeDTO> roots = new ArrayList<>();
        for (DeviceCompositeTreeDTO compositeTreeDto : compositeTreeDtoList) {
            boolean hasParent = false;
            for (DeviceCompositeTreeDTO potentialParent : compositeTreeDtoList) {
                if (compositeTreeDto.getParentId().equals(potentialParent.getDeviceCompositeId())){
                    // 子找父 -> 找到父节点
                    potentialParent.addChild(compositeTreeDto);
                    hasParent = true;
                    break;
                }
            }
            if (!hasParent) {
                // 没有父节点的视为根节点
                roots.add(compositeTreeDto);
            }
        }
        return roots;
    }

}
