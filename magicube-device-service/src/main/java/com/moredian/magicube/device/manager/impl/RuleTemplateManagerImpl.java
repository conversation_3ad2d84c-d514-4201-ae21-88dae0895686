package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dao.entity.RuleTemplate;
import com.moredian.magicube.device.dao.mapper.RuleTemplateMapper;
import com.moredian.magicube.device.manager.RuleTemplateManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: RuleTemplateManaget.java, v 1.0 Exp $
 */
@Component
@Slf4j
public class RuleTemplateManagerImpl implements RuleTemplateManager {

    @Resource
    private RuleTemplateMapper ruleTemplateMapper;

    @Override
    public List<RuleTemplate> getDefaultTemplateList() {
        return ruleTemplateMapper.getDefaultTemplateList();
    }

    @Override
    public RuleTemplate getTemplate(Long templateId) {
        return ruleTemplateMapper.getTemplate(templateId);
    }

    @Override
    public List<RuleTemplate> listByModeType(Integer modeType) {
        BizAssert.notNull(modeType, "modeType must not be null");
        return ruleTemplateMapper.listByModeType(modeType);
    }
}
