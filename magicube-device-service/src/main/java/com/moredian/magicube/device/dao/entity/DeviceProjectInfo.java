package com.moredian.magicube.device.dao.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 设备上报项目信息实体类
 * @Date 2023/5/10
 */
@Getter
@Setter
@ToString
public class DeviceProjectInfo implements Serializable {

    private static final long serialVersionUID = 5350643307309894046L;

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 项目Id
     */
    private String projectId;

    /**
     * 版本
     */
    private String version;

    /**
     * 场景
     */
    private Integer sceneType;

    /**
     * 项目信息原始json
     */
    private String projectInfo;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;
}
