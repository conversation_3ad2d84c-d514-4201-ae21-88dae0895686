package com.moredian.magicube.device.third.lock.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-12 11:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InitializeLockReq extends CommonReq {

    /**
     * lockData 初始化数据，由前端小程序调用通通锁SDK初始化获取
     */
    private String lockData;

    private String lockAlias;

    private Integer groupId;
}
