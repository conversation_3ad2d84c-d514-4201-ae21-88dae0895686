package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import com.moredian.magicube.device.enums.DeviceRelateAppFixedBIzTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_app_relation_fixed_config")
public class DeviceAppRelationFixedConfig extends TimedEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 关联的业务类型
     * @see DeviceRelateAppFixedBIzTypeEnum
     */
    private Integer bizType;

    /**
     * 关联的业务id
     */
    private String bizId;

    /**
     * 默认跳转应用
     */
    private String defaultAppCode;

    /**
     * 可承载应用列表
     */
    private String availableAppCodeList;

}
