package com.moredian.magicube.device.pipeline.executor.strategy;


import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.executor.AbstractActivateDeviceEngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.ding.DingDeviceCheckPipe;
import com.moredian.magicube.device.pipeline.pipe.reverse.BindDefaultRoomSpacePipe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 钉钉反扫去空间二维码激活引擎执行器
 *
 * <AUTHOR>
 */
@Component
public class DingReverseRemoveSpaceActivateEngineExecutor extends AbstractActivateDeviceEngineExecutor {

    @Autowired
    private DingDeviceCheckPipe dingDeviceCheckPipe;

    @Autowired
    private BindDefaultRoomSpacePipe bindDefaultRoomSpacePipe;

    @Override
    public void afterPropertiesSet() {
        pipeline.addPipe(commonActivateCheckPipe);
        pipeline.addPipe(dingDeviceCheckPipe);
        pipeline.addPipe(checkDeviceAuthorizePipe);
        pipeline.addPipe(verifyChannelAndArithmeticPipe);
        pipeline.addPipe(insertDevicePipe);
        pipeline.addPipe(uploadApkVersionPipe);
        pipeline.addPipe(enableBizPipe);
        pipeline.addPipe(insertDeviceLogPipe);
        pipeline.addPipe(updateDeviceWhitePipe);
        pipeline.addPipe(monitorDataPipe);
//        pipeline.addPipe(bindDefaultRoomSpacePipe);
        pipeline.addPipe(publishMsgPipe);
        pipeline.addPipe(buildResultPipe);
    }

    @Override
    protected void validCustomParameter(ActivateDeviceDTO dto) {

    }

    @Override
    protected String setKey(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return "DingReverseRemoveSpaceActivate_" + dto.getDeviceSn();
    }

    @Override
    public Integer getType() {
        return DeviceActivateEngineEnums.DING_REVERSE_REMOVE_SPACE_ACTIVATE.getCode();
    }

}
