package com.moredian.magicube.device.manager.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dao.mapper.DeviceAppRelationConfigMapper;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceDTO;
import com.moredian.magicube.device.manager.DeviceAppRelationConfigManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Component
public class DeviceAppRelationConfigManagerImpl implements DeviceAppRelationConfigManager {

    @SI
    private IdgeneratorService idgeneratorService;
    @Resource
    private DeviceAppRelationConfigMapper deviceAppRelationConfigMapper;

    @Override
    public Pagination<DeviceAppRelationConfig> listPage(QueryDeviceAppRelationConfigDTO dto) {

        Pagination<DeviceAppRelationConfig> pa = new Pagination<>();
        pa.setPageNo(dto.getPageNo());
        pa.setPageSize(dto.getPageSize());

        Page<DeviceAppRelationConfig> page = PageHelper.startPage(dto.getPageNo() == null ? 1 : dto.getPageNo(),
                        dto.getPageSize() == null ? 20 : dto.getPageSize())
                .setOrderBy("gmt_create DESC")
                .doSelectPage(() -> deviceAppRelationConfigMapper.selectByConditions(dto));
        pa.setTotalCount((int) page.getTotal());
        pa.setData(page.getResult());
        return pa;
    }

    @Override
    public List<DeviceAppRelationConfig> listAll() {
        return deviceAppRelationConfigMapper.listAll();
    }

    @Override
    public Long insert(DeviceAppRelationConfig config) {

        Long id = idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_APP_RELATION_CONFIG)
                .pickDataThrowException();
        config.setId(id);
        deviceAppRelationConfigMapper.insert(config);
        return id;
    }

    @Override
    public int deleteById(Long id) {
        return deviceAppRelationConfigMapper.deleteById(id);
    }

    @Override
    public int updateById(DeviceAppRelationConfig config) {
        return deviceAppRelationConfigMapper.updateById(config);
    }

    @Override
    public int updateSelectiveById(DeviceAppRelationConfig config) {
        return deviceAppRelationConfigMapper.updateSelectiveById(config);
    }

    @Override
    public DeviceAppRelationConfig selectById(Long id) {
        return deviceAppRelationConfigMapper.selectById(id);
    }

    @Override
    public List<DeviceAppRelationConfig> selectBySpaceTypeAndAppTypeAndVersionCode(Integer spaceType, Integer appType, Integer versionCode) {
        if (spaceType == null ) {
            spaceType = -1;
        }
        return deviceAppRelationConfigMapper.selectBySpaceTypeAndAppTypeAndVersionCode(spaceType, appType, versionCode);
    }

    @Override
    public List<DeviceAppRelationConfig> selectAllByAppTypeAndVersionCode(Integer appType, Integer versionCode) {
        return deviceAppRelationConfigMapper.selectByAppTypeAndVersionCode(appType, versionCode);
    }

    @Override
    public void addOrUpdate(List<DeviceAppRelationConfig> list) {
        if (ObjectUtil.isEmpty(list)){
            return;
        }
        list.forEach(item -> {
            Long id = item.getId();
            if (ObjectUtil.isEmpty(id)){
                deviceAppRelationConfigMapper.insert(item);
            }else {
                deviceAppRelationConfigMapper.updateSelectiveById(item);
            }
        });
    }
}
