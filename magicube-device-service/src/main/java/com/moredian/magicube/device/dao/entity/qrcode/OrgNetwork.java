package com.moredian.magicube.device.dao.entity.qrcode;

import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import java.util.Date;
import lombok.Data;

/**
 * @Date: 2023/1/9 1:48 下午
 * @Author: _AF
 * @Description:
 */
@Data
public class OrgNetwork {

    private Long id;

    private Long orgId;

    /**
     * 网络连接类型
     *
     * @see ActivateQrCodeConstant.NetworkTypeEnum
     */
    private Integer networkType;

    /**
     * 连接类型
     *
     * @see ActivateQrCodeConstant.ConnectTypeEnum
     */
    private Integer connectType;

    /**
     * 静态ip配置
     */
    private String cableStatic;

    /**
     * wifi信息
     */
    private String wifiInfo;

    /**
     * 二维码地址
     */
    private String url;

    /**
     * 设备配网码状态 0-待处理 1-处理中 2-处理完成
     */
    private Integer status;

    /**
     * 绑定机构页面url（仅用于钉钉SDK激活流程）
     */
    private String bindOrgUrl;

    private Date gmtCreate;

    private Date gmtModify;

    /**
     * 服务器地址 目前仅用于保存私有化服务器服务器地址
     */
    private String serverAddress;
}


