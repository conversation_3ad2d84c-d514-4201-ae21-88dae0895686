package com.moredian.magicube.device.pipeline.pipe;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.conf.core.client.BeeConfNewClient;
import com.moredian.magicube.common.enums.AuthorizeAppEnum;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.common.model.msg.CreateDeviceExtraInfoMsg;
import com.moredian.magicube.common.model.msg.device.DeviceActiveEvent;
import com.moredian.magicube.common.model.msg.device.DeviceActiveMsg;
import com.moredian.magicube.device.constant.BeeConfConfigConstants;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceFlagEnum;
import com.moredian.magicube.device.manager.DeviceAppRelationConfigManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.magicube.device.utils.StringUtils;
import com.moredian.magicube.ocean.service.SpaceSenceTypeRelationService;
import com.moredian.space.dto.tree.TreeDTO;
import com.moredian.space.message.SaveTreeDeviceRelationMsg;
import com.moredian.space.service.SpaceTreeService;
import com.xier.sesame.common.utils.JsonUtils;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 发送MQ消息管道
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class PublishMsgPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext>{

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @SI
    private SpaceTreeService treeService;

    @SI
    private SpaceSenceTypeRelationService spaceSenceTypeRelationService;
    @Autowired
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        //在事务提交后,在进行消息发送
        TransactionSynchronizationManager
            .registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    publishMsg(dto, context);
                }
            });
    }

    private void publishMsg(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        Device device = context.getDevice();
        if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.EXTRA_INFO_NEED_LIST, device.getDeviceType())) {
            CreateDeviceExtraInfoMsg msg = new CreateDeviceExtraInfoMsg();
            msg.setOrgId(device.getOrgId());
            msg.setDeviceId(device.getDeviceId());
            msg.setDeviceSn(device.getDeviceSn());
            EventBus.publish(msg);
            log.info("发出消息CreateDeviceExtraInfoMsg:" + com.moredian.bee.common.utils.JsonUtils
                .toJson(msg));
        }

        DeviceActiveEvent deviceActiveEvent = new DeviceActiveEvent();
        BeanUtils.copyProperties(device, deviceActiveEvent);
        deviceActiveEvent.setName(device.getDeviceName());
        deviceActiveEvent.setTpDevId(dto.getThirdDeviceId());
        deviceActiveEvent.setTimeStamp(System.currentTimeMillis());

        //新增新的设备激活消息，由于钉钉SDK激活绑定空间是激活接口成功后才绑定的，所以不能提前发送激活成功消息，会导致业务逻辑错误。因此新增激活消息，新业务用此消息处理。
        // 如果是钉钉SDK激活，在选择空间完成后才发送设备激活消息
        DeviceActiveMsg deviceActiveMsg = new DeviceActiveMsg();
        BeanUtils.copyProperties(device, deviceActiveMsg);
        deviceActiveMsg.setTpDevId(dto.getThirdDeviceId());
        deviceActiveMsg.setTimeStamp(System.currentTimeMillis());
        if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_NOTICE_ACTIVATE_USER_DEVICE_LIST, dto.getDeviceType())) {
            deviceActiveEvent.setUserId(dto.getUserId());
            deviceActiveMsg.setUserId(dto.getUserId());
        }
        //钉钉sdk激活接口无法绑定空间，激活成功后再绑定
        if (context.getTreeId() != null) {
            TreeDTO treeDTO = treeService.getByIdSupportCollaboration(context.getTreeId(),context.getOrgId()).pickDataThrowException();
            if (treeDTO != null) {
                Integer sceneType = treeService.getSceneTypeByOrgIdAndTreeIdSupportCollaboration(treeDTO.getOrgId(), treeDTO.getTreeId()).pickDataThrowException();
                deviceActiveEvent.setSceneType(sceneType == null ? 0 : sceneType);
                deviceActiveMsg.setSceneType(sceneType == null ? 0 : sceneType);
            }
            handlerActivate(dto, treeDTO, deviceActiveEvent, deviceActiveMsg);
            // 添加空间树id到消息中
            deviceActiveEvent.setTreeId(context.getTreeId());
            deviceActiveMsg.setTreeId(context.getTreeId());
        }
        EventBus.publish(deviceActiveEvent);
        log.info("激活消息发送成功：" + JsonUtils.toJson(deviceActiveEvent));
        //未绑定空间说明真正激活流程没有结束，不会发送新激活消息
        if (context.getTreeId() != null || Objects.equals(DeviceFlagEnum.FX.getValue(), dto.getDeviceFlag())) {
            EventBus.publish(deviceActiveMsg);
            log.info("新激活消息发送成功：" + JsonUtils.toJson(deviceActiveMsg));
        }

        if (context.getTreeDeviceRelationId() != null) {
            SaveTreeDeviceRelationMsg saveTreeDeviceRelationMsg = new SaveTreeDeviceRelationMsg();
            saveTreeDeviceRelationMsg.setOrgId(context.getOrgId());
            saveTreeDeviceRelationMsg.setDeviceId(device.getDeviceId().toString());
            saveTreeDeviceRelationMsg.setTreeId(context.getTreeId() == null ? context.getNewTreeId() : context.getTreeId());
            saveTreeDeviceRelationMsg.setTreeDeviceRelationId(context.getTreeDeviceRelationId());
            saveTreeDeviceRelationMsg.setDeviceSource(1);
            EventBus.publish(saveTreeDeviceRelationMsg);
            log.info("设备激活绑定空间消息发送成功：" + JsonUtils.toJson(saveTreeDeviceRelationMsg));
        }
    }

    private void handlerActivate(ActivateDeviceDTO dto, TreeDTO treeDTO,
            DeviceActiveEvent deviceActiveEvent, DeviceActiveMsg deviceActiveMsg) {
        if (treeDTO == null) {
            return;
        }
        String sysSwitch = BeeConfNewClient.get(BeeConfConfigConstants.DOMAIN_CODE,
                BeeConfConfigConstants.GROUP_DEVICE_APP,
                BeeConfConfigConstants.SYS_ACTIVATE_SCENE_SWITH_CONFIG_KEY);
        if (!Objects.equals("1", sysSwitch)) {
            return;
        }
        //无法确保数据库数据的准确性，暂时先不启用该逻辑
        String availableAppCodeList = "";
        //根据设备激活时的appType和Version+ 设备绑定的空间spaceType 查询在该空间下的场景
        if (dto.getAppType() != null && dto.getVersion() != null) {
            List<DeviceAppRelationConfig> configs = deviceAppRelationConfigManager
                    .selectBySpaceTypeAndAppTypeAndVersionCode(
                            treeDTO.getTags().get(0).getSpaceType(), dto.getAppType(),
                            dto.getVersion());
            DeviceAppRelationConfig deviceAppRelationConfig =
                    CollUtil.isNotEmpty(configs) ? configs.get(0) : null;
            availableAppCodeList = deviceAppRelationConfig == null ? ""
                    : deviceAppRelationConfig.getAvailableAppCodeList();
            log.info("device activate appType:{},version:{},availableAppCodeList:{}",
                    dto.getAppType(), dto.getVersion(),
                    JSON.toJSONString(availableAppCodeList));
        }
        //设备在空间下支持的应用。如果是门禁，设备消息为门禁模式 否则拿空间对应的sceneType
        if (StringUtils.isNotBlank(availableAppCodeList) && availableAppCodeList.contains(
                AuthorizeAppEnum.FACEDOOR.getCode())) {
            deviceActiveEvent.setSceneType(SceneTypeEnums.STANDARD_MODEL.getValue());
            deviceActiveMsg.setSceneType(SceneTypeEnums.STANDARD_MODEL.getValue());
        }
    }
}
