package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bridge.api.message.OrgAuthorizedMsg;
import com.moredian.magicube.device.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrgAuthorizedMsgSubscriber {

    @Autowired
    private DeviceManager deviceManager;

    @Subscribe
    public void orgAuthorizedMsg(OrgAuthorizedMsg msg) {
        log.info("收到机构绑定魔链事件消息: " + JsonUtils.toJson(msg));
        if (msg != null){
            deviceManager.syncIotDeviceByOrgId(msg.getOrgId());
        }
    }
}
