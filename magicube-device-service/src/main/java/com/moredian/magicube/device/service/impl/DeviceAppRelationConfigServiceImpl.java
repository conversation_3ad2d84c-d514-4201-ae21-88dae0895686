package com.moredian.magicube.device.service.impl;

import com.google.common.collect.Maps;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.AuthorizeAppEnum;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.device.convertor.DeviceAppRelationConfigConvertor;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.dto.device.DeviceAppRelationConfigDTO;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.device.DeviceRelateAppDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import com.moredian.magicube.device.helper.DeviceAppRelationHandler;
import com.moredian.magicube.device.manager.DeviceAppRelationConfigManager;
import com.moredian.magicube.device.service.DeviceAppRelationConfigService;
import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.service.DeviceVersionService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;


@SI
public class DeviceAppRelationConfigServiceImpl implements DeviceAppRelationConfigService {

    @Resource
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;

    @Autowired
    private DeviceAppRelationHandler deviceAppRelationHandler;

    @SI
    private DeviceVersionService deviceVersionService;

    @SI
    private DeviceService deviceService;

    @Override
    public ServiceResponse<Pagination<DeviceAppRelationConfigDTO>> listPage(QueryDeviceAppRelationConfigDTO dto) {
        Pagination<DeviceAppRelationConfig> pagination = deviceAppRelationConfigManager.listPage(dto);
        Pagination<DeviceAppRelationConfigDTO> result = new Pagination<>();
        result.setTotalCount(pagination.getTotalCount());
        result.setPageNo(pagination.getPageNo());
        result.setPageSize(pagination.getPageSize());
        if (CollectionUtils.isNotEmpty(pagination.getData())){
            List<DeviceAppRelationConfigDTO> collect = pagination.getData()
                    .stream()
                    .map(DeviceAppRelationConfigConvertor::entityToDto)
                    .collect(Collectors.toList());
            result.setData(collect);
        }
        return new ServiceResponse<>(result);
    }

    /**
     * 查询所有记录
     *
     * @return 返回集合，没有返回空List
     */
    @Override
    public ServiceResponse<List<DeviceAppRelationConfigDTO>> listAll() {
    
    	List<DeviceAppRelationConfig> deviceAppRelationConfigList = deviceAppRelationConfigManager.listAll();
        //转换成DTO
        List<DeviceAppRelationConfigDTO> collect = deviceAppRelationConfigList.stream().map(DeviceAppRelationConfigConvertor::entityToDto).collect(Collectors.toList());
    	return new ServiceResponse<>(collect);
    }


    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 返回记录，没有返回null
     */
    @Override
    public ServiceResponse<DeviceAppRelationConfigDTO> getById(Long id) {
        BizAssert.notNull(id, "id must not null");
        DeviceAppRelationConfig deviceAppRelationConfig = deviceAppRelationConfigManager.selectById(id);
    	DeviceAppRelationConfigDTO deviceAppRelationConfigDTO = DeviceAppRelationConfigConvertor.entityToDto(deviceAppRelationConfig);
        return new ServiceResponse<>(deviceAppRelationConfigDTO);
    }
	
    /**
     * 新增
     *
     * @param deviceAppRelationConfigDTO 新增的记录
     * @return 主键id
     */
    @Override
    public ServiceResponse<Long> insert(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO) {
    	DeviceAppRelationConfig deviceAppRelationConfig = DeviceAppRelationConfigConvertor.dtoToEntity(deviceAppRelationConfigDTO);
    	return new ServiceResponse<>(deviceAppRelationConfigManager.insert(deviceAppRelationConfig));
    }
	
    /**
     * 修改所有字段
     *
     * @param deviceAppRelationConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    @Override
    public ServiceResponse<Boolean> update(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO) {
    	DeviceAppRelationConfig deviceAppRelationConfig = DeviceAppRelationConfigConvertor.dtoToEntity(deviceAppRelationConfigDTO);
    	int flag = deviceAppRelationConfigManager.updateById(deviceAppRelationConfig);
    	return new ServiceResponse<>(flag == 1);
    }
	
    /**
     * 修改不为null字段
     *
     * @param deviceAppRelationConfigDTO 修改的记录
     * @return 是否成功true/false
     */
    @Override
    public ServiceResponse<Boolean> updateSelective(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO) {
    	DeviceAppRelationConfig deviceAppRelationConfig = DeviceAppRelationConfigConvertor.dtoToEntity(deviceAppRelationConfigDTO);
    	int flag = deviceAppRelationConfigManager.updateSelectiveById(deviceAppRelationConfig);
    	return new ServiceResponse<>(flag == 1);
    }
	
    /**
     * 删除
     *
     * @param deviceAppRelationConfigDTO 待删除的记录
     * @return 是否成功true/false
     */
    @Override
    public ServiceResponse<Boolean> delete(DeviceAppRelationConfigDTO deviceAppRelationConfigDTO) {
    	int flag = deviceAppRelationConfigManager.deleteById(deviceAppRelationConfigDTO.getId());
    	return new ServiceResponse<>(flag == 1);
    }

    @Override
    public ServiceResponse<DeviceAppRelationConfigDTO> getByOrgIdAndDeviceId(Long orgId, Long deviceId, Integer spaceType) {
        BizAssert.notNull(orgId, "orgId must be not null");
        BizAssert.notNull(deviceId, "deviceId must be not null");

        //如果固定配置表为空，从默认模版表里面去找
        //查询设备版本信息
        List<DeviceCurrVersionDTO> deviceCurrVersionDTOS = deviceVersionService.listByOrgIdAndDeviceIds(orgId,
                Collections.singletonList(deviceId)).pickDataThrowException();

        DeviceCurrVersionDTO deviceCurrVersionDTO = deviceCurrVersionDTOS.get(0);

        //如果spaceType 、appType versionCode都不为空
        if (spaceType != null && deviceCurrVersionDTO.getCurrAppVersion().getAppType() != null
                && deviceCurrVersionDTO.getCurrAppVersion().getVersionCode() != null) {
            DeviceAppRelationConfigDTO appRelationConfigDTO = new DeviceAppRelationConfigDTO();
            List<DeviceAppRelationConfig> deviceAppRelationConfigs = deviceAppRelationConfigManager.selectBySpaceTypeAndAppTypeAndVersionCode(
                    spaceType, deviceCurrVersionDTO.getCurrAppVersion().getAppType(),
                    deviceCurrVersionDTO.getCurrAppVersion().getVersionCode());
            if (CollectionUtils.isNotEmpty(deviceAppRelationConfigs)) {
                //取第一条
                DeviceAppRelationConfig deviceAppRelationConfig = deviceAppRelationConfigs.get(0);
                appRelationConfigDTO.setDefaultAppCode(deviceAppRelationConfig.getDefaultAppCode());
                appRelationConfigDTO.setAvailableAppCodeList(deviceAppRelationConfig.getAvailableAppCodeList());
            }
            return new ServiceResponse<>(appRelationConfigDTO);
        }
        return new ServiceResponse<>(null);
    }

    @Override
    public ServiceResponse<List<DeviceAppRelationConfigDTO>> getAllAppByOrgIdAndDeviceId(Long orgId, Long deviceId) {
        BizAssert.notNull(orgId, "orgId must be not null");
        BizAssert.notNull(deviceId, "deviceId must be not null");
        //查询设备版本信息
        List<DeviceCurrVersionDTO> deviceCurrVersionDTOS = deviceVersionService.listByOrgIdAndDeviceIds(orgId,
                Collections.singletonList(deviceId)).pickDataThrowException();

        DeviceCurrVersionDTO deviceCurrVersionDTO = deviceCurrVersionDTOS.get(0);

        if (deviceCurrVersionDTO.getCurrAppVersion().getAppType() == null || deviceCurrVersionDTO.getCurrAppVersion().getVersionCode() == null) {
            return new ServiceResponse<>(Collections.emptyList());
        }
        //如果appType versionCode都不为空
        List<DeviceAppRelationConfig> deviceAppRelationConfigs = deviceAppRelationConfigManager.selectAllByAppTypeAndVersionCode(
                deviceCurrVersionDTO.getCurrAppVersion().getAppType(),
                deviceCurrVersionDTO.getCurrAppVersion().getVersionCode());
        List<DeviceAppRelationConfigDTO> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deviceAppRelationConfigs)) {
            for (DeviceAppRelationConfig appRelationConfig : deviceAppRelationConfigs) {
                DeviceAppRelationConfigDTO appRelationConfigDTO = new DeviceAppRelationConfigDTO();
                appRelationConfigDTO.setSpaceType(appRelationConfig.getSpaceType());
                appRelationConfigDTO.setAppType(appRelationConfig.getAppType());
                appRelationConfigDTO.setVersionCode(appRelationConfig.getVersionCode());
                appRelationConfigDTO.setDefaultAppCode(appRelationConfig.getDefaultAppCode());
                appRelationConfigDTO.setAvailableAppCodeList(appRelationConfig.getAvailableAppCodeList());
                list.add(appRelationConfigDTO);
            }
        }
        return new ServiceResponse<>(list);
    }

    @Override
    public ServiceResponse<List<Integer>> listSceneTypeByDeviceSn(Long orgId, String deviceSn) {
        ServiceResponse<List<Integer>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<Integer> sceneTypes = new ArrayList<>();
        serviceResponse.setData(sceneTypes);
        DeviceInfoDTO deviceInfoDTO = deviceService.getByOrgAndDeviceSn(orgId,
            deviceSn).pickDataThrowException();
        if (deviceInfoDTO == null) {
            return serviceResponse;
        }

        DeviceAppRelationConfig deviceAppRelationConfig = deviceAppRelationHandler
            .getDeviceAppCode(orgId, deviceSn, null);
        if (deviceAppRelationConfig == null) {
            return serviceResponse;
        }
        List<String> availableAppCodeList = Arrays.asList(
            deviceAppRelationConfig.getAvailableAppCodeList().split(","));
        for (String appCode : availableAppCodeList) {
            //考勤和访客对应是门禁模式
            if (AuthorizeAppEnum.ATTENDANCE.getCode().equals(appCode)
                || AuthorizeAppEnum.VISITOR.getCode().equals(appCode)
                || AuthorizeAppEnum.FACEDOOR.getCode().equals(appCode)) {
                sceneTypes.add(SceneTypeEnums.STANDARD_MODEL.getValue());
            } else if (AuthorizeAppEnum.MEETING.getCode().equals(appCode)) {
                sceneTypes.add(SceneTypeEnums.MEETING_ROOM_MODEL.getValue());
            } else if (AuthorizeAppEnum.CLASSCARD.getCode().equals(appCode)) {
                sceneTypes.add(SceneTypeEnums.CLASSROOM_MODEL.getValue());
            } else if (AuthorizeAppEnum.CABINET.getCode().equals(appCode)) {
                sceneTypes.add(SceneTypeEnums.CLASSROOM_MODEL.getValue());
            } else if (AuthorizeAppEnum.LADDER.getCode().equals(appCode)) {
                sceneTypes.add(SceneTypeEnums.ELEVATOR_MODEL.getValue());
            }
        }
        sceneTypes.stream().distinct();
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Map<Long, List<DeviceAppRelationConfigDTO>>> getByOrgIdAndDeviceIdList(Long orgId,
        List<Long> deviceIds, Integer spaceType) {

        BizAssert.notNull(orgId, "orgId must be not null");
        BizAssert.notNull(deviceIds, "deviceIds must be not null");

        //查询设备版本信息
        List<DeviceCurrVersionDTO> deviceCurrVersionDTOS = deviceVersionService.listByOrgIdAndDeviceIds(orgId,
            deviceIds).pickDataThrowException();

        Map<Long, List<DeviceCurrVersionDTO>> deviceVersionMap = deviceCurrVersionDTOS.stream()
            .collect(Collectors.groupingBy(DeviceCurrVersionDTO::getDeviceId));

        Map<Long, List<DeviceAppRelationConfigDTO>> result = Maps.newHashMap();
        for (Entry<Long, List<DeviceCurrVersionDTO>> entry : deviceVersionMap.entrySet()) {
            DeviceCurrVersionDTO versionDTO = entry.getValue().get(0);
            Integer appType = versionDTO.getCurrAppVersion().getAppType();
            Integer versionCode = versionDTO.getCurrAppVersion().getVersionCode();

            if(appType!= null && versionCode!= null){
                List<DeviceAppRelationConfig> relation ;
                if (spaceType != null) {
                    relation = deviceAppRelationConfigManager.selectBySpaceTypeAndAppTypeAndVersionCode(spaceType, appType, versionCode);
                } else {
                    relation = deviceAppRelationConfigManager.selectAllByAppTypeAndVersionCode(appType, versionCode);
                }
                List<DeviceAppRelationConfigDTO> temp = relation.stream().map(e -> {
                    DeviceAppRelationConfigDTO relationConfigDTO = new DeviceAppRelationConfigDTO();
                    BeanUtils.copyProperties(e, relationConfigDTO);
                    return relationConfigDTO;
                }).collect(Collectors.toList());
                result.put(entry.getKey(), temp);
            }
        }
        return new ServiceResponse<>(result);
    }


}