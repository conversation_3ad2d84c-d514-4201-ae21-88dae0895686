package com.moredian.magicube.device.manager.bo;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 分页查询设备列表参数
 * @create 2024-12-28 17:31
 */
@Data
public class QueryPageDeviceBO implements Serializable {

    private static final long serialVersionUID = -8550155295706081722L;

    /**
     * 机构id，不能为空
     */
    private Long orgId;

    /**
     * 设备名
     */
    private String deviceName;

    /**
     * 设备SN
     */
    private List<String> deviceSn;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 设备类型列表
     */
    private List<Integer> deviceTypes;
    /**
     * 排除的设备类型列表
     */
    private List<Integer> filterDeviceTypes;
    /**
     * 设备Id列表
     */
    private List<Long> deviceIds;
    /**
     * 在线状态
     */
    private Boolean online;
    /**
     * 空间树Id列表
     */
    private List<Long> treeIds;

    /**
     * 是否查询子空间下的设备
     */
    private Boolean subTreeFlag;

    /**
     * 每页显示数目
     */
    private Integer pageSize;

    /**
     * 当前页数
     */
    private Integer pageNo;

    /**
     * 空间名称模糊匹配到的设备id
     */
    private List<Long> likeTreeNameDeviceId;
}
