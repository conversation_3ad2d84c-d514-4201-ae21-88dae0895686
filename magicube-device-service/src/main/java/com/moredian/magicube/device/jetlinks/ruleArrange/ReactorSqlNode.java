package com.moredian.magicube.device.jetlinks.ruleArrange;

import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.magicube.device.enums.MotionStatusEnum;
import com.moredian.magicube.device.enums.RuleDeviceTypeEnum;
import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;

import java.util.List;
import java.util.StringJoiner;

import static com.moredian.magicube.device.constant.DeviceIotPropertyConstants.MOTION_STATUS;

/**
 * <AUTHOR>
 * @version $Id: ReactorSqlNode.java, v 1.0 Exp $
 */
public class ReactorSqlNode extends Node{

    public ReactorSqlNode(String modelId, Flow flow, DeviceTypePropertyManager deviceTypePropertyManager) {
        super(modelId, flow, deviceTypePropertyManager);
    }

    public void wrapperContent(TreeDeviceRelationDTO oneTreeDeviceRelation, List<TreeDeviceRelationDTO> deviceRelations, List<TreeDeviceRelationDTO> triggerDeviceRelationList, String funNodeIdJoiner, IotDeviceInfoDTO iotTriggerDevice) {
        StringJoiner reactorSql = getReactorSql(oneTreeDeviceRelation, deviceRelations, triggerDeviceRelationList, iotTriggerDevice);
        flow.addNode("{\\\"id\\\":\\\""+id+"\\\",\\\"type\\\":\\\"reactor-ql\\\",\\\"z\\\":\\\""+modelId+"\\\",\\\"name\\\":\\\"\\\",\\\"sql\\\":\\\""+reactorSql+"\\\",\\\"x\\\":170,\\\"y\\\":2200,\\\"wires\\\":[["+funNodeIdJoiner+"]]}");;
    }

    private StringJoiner getReactorSql(TreeDeviceRelationDTO oneTreeDeviceRelation, List<TreeDeviceRelationDTO> deviceRelations, List<TreeDeviceRelationDTO> triggerDeviceRelationList, IotDeviceInfoDTO iotTriggerDevice) {
        StringJoiner reactorSql = new StringJoiner("");

        StringJoiner select = new StringJoiner("", "select * from (\\\\n    select\\\\n",
                "from \\\\\\\"/device/"+iotTriggerDevice.getProductId()+"/*/message/property/report\\\\\\\"\\\\n) t");

        StringJoiner selectValue = new StringJoiner("");
        StringJoiner whereAndSql = new StringJoiner(" and ", " where ", "");
        StringJoiner whereOrSql = new StringJoiner(" or ", " and (", ")");

        TreeDeviceRelationDTO triggerDeviceRelation;
        for (int i = 0, size = triggerDeviceRelationList.size(); i < size; i++) {

            triggerDeviceRelation = triggerDeviceRelationList.get(i);

            selectValue.add("  device.property.recent('"+deviceSn(triggerDeviceRelation)+"','"+ MOTION_STATUS +"') "+(MOTION_STATUS +i)+",\\\\n");

            whereAndSql.add(" t."+(MOTION_STATUS +i) +" = '"+ MotionStatusEnum.NO_PERSON.getCode()+"' \\\\n ");
        }

//        for (int i = 0; i < deviceRelations.size(); i++) {
//            TreeDeviceRelationDTO deviceRelation = deviceRelations.get(i);
//            String group = getGroup(deviceRelation);
//            String switchPropertyId = getPropertyId(deviceRelation, group);
//            if (RuleDeviceTypeEnum.MEASUREMENT_SOCKET.getDeviceTypes().contains(deviceRelation.getDeviceType())){
//                //插座类型
//                String str = RuleDeviceTypeEnum.MEASUREMENT_SOCKET.name().toLowerCase() + i;
//                selectValue.add("  device.property.recent('"+deviceSn(deviceRelation)+"','"+ switchPropertyId+"') "+(str)+"\\\\n");
//                whereOrSql.add(" t."+(str) +" = '"+ RuleStateEnum.ACTIVE.getCode() +"' \\\\n ");
//            } else if (RuleDeviceTypeEnum.AIR_CONDITIONER.getDeviceTypes().contains(deviceRelation.getDeviceType())){
//                //空调
//                String str = RuleDeviceTypeEnum.AIR_CONDITIONER.name().toLowerCase() + i;
//                selectValue.add("  device.property.recent('"+deviceSn(deviceRelation)+"','"+ switchPropertyId+"') "+(str)+"\\\\n");
//                whereOrSql.add(" t."+(str) +" = '"+ RuleStateEnum.ACTIVE.getCode() +"' \\\\n ");
//            } else {
//                //开关
//                selectValue.add("  device.property.recent('"+deviceSn(deviceRelation)+"','"+ switchPropertyId+"') "+(switchPropertyId+ i)+"\\\\n");
//                whereOrSql.add(" t."+(switchPropertyId+ i) +" = '"+ RuleStateEnum.ACTIVE.getCode() +"' \\\\n ");
//            }
//        }

        TreeDeviceRelationDTO deviceRelation = oneTreeDeviceRelation;
        String group = getGroup(deviceRelation);
        String switchPropertyId = getPropertyId(deviceRelation, group);
        selectValue.add("  device.property.recent('"+deviceSn(deviceRelation)+"','"+ switchPropertyId+"') "+(switchPropertyId)+"\\\\n");
        whereAndSql.add(" t."+(switchPropertyId) +" = '"+ RuleStateEnum.ACTIVE.getCode() +"' \\\\n ");

        select.add(selectValue.toString());

        reactorSql.add(select.toString());
        reactorSql.add(whereAndSql.toString());
//        reactorSql.add(whereOrSql.toString());
        return reactorSql;
    }

//    public static void main(String[] args) {
//        StringJoiner select = new StringJoiner("", "select * from (\\\\n    select\\\\n",
////                "from \\\\\\\"/device/"+iotTriggerDevice.getProductId()+"/*/message/property/report\\\\\\\"\\\\n) t");
//            "from \\\\\\\"/device/"+"*/*/message/property/report\\\\\\\"\\\\n) t");
//        StringJoiner reactorSql = new StringJoiner("");
//
//        StringJoiner selectValue = new StringJoiner(",");
//        StringJoiner whereAndSql = new StringJoiner(" and ", " where ", "");
//        StringJoiner whereOrSql = new StringJoiner(" or ", " and (", ")");
//
//        selectValue.add("  device.property.recent('"+"1"+"','"+ MOTION_STATUS +"') "+(MOTION_STATUS +1)+"\\\\n");
//        selectValue.add("  device.property.recent('"+"2"+"','"+ MOTION_STATUS +"') "+(MOTION_STATUS +0)+"\\\\n");
//        whereAndSql.add(" t."+(MOTION_STATUS +1) +" = '"+ MotionStatusEnum.NO_PERSON.getCode()+"' \\\\n ");
//        whereAndSql.add(" t."+(MOTION_STATUS +0) +" = '"+ MotionStatusEnum.NO_PERSON.getCode()+"' \\\\n ");
//        whereOrSql.add(" t."+("PowerSwitch") +" = '"+ RuleStateEnum.ACTIVE.getCode() +"' \\\\n ");
//        whereOrSql.add(" t."+("power") +" = '"+ RuleStateEnum.ACTIVE.getCode() +"' \\\\n ");
//
//        select.add(selectValue.toString());
//        reactorSql.add(select.toString());
//        reactorSql.add(whereAndSql.toString());
//        reactorSql.add(whereOrSql.toString());
//        System.out.println(
//            reactorSql
//        );
//    }
}
