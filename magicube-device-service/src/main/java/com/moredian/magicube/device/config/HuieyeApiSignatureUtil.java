package com.moredian.magicube.device.config;

import com.moredian.bee.common.exception.CommonErrorCode;
import com.moredian.bee.common.utils.ExceptionUtils;
import com.moredian.unified.recognition.api.request.ApiSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @since 2023-06-27
 */
@Component
public class HuieyeApiSignatureUtil {

    private static ApiSignature apiSignature;
    @Autowired
    private CloudeyeProperties cloudeyeProperties;

    private static ApiSignature buildApiSignature(CloudeyeProperties cloudeyeProperties) {
        ApiSignature apiSignature = new ApiSignature();
        apiSignature.setVer(cloudeyeProperties.getVer());
        apiSignature.setTimestamp(System.currentTimeMillis());
        apiSignature.setAppkey(cloudeyeProperties.getAppKey());
        apiSignature.setSign(cloudeyeProperties.getSign());
        return apiSignature;
    }

    public static ApiSignature getSignature() {
        if (apiSignature == null) {
            ExceptionUtils.throwException(CommonErrorCode.SERVICE_BUSY, "System is starting, service has not been initialized");
        }
        return apiSignature;
    }

    @PostConstruct
    private void init() {
        apiSignature = buildApiSignature(cloudeyeProperties);
    }

}
