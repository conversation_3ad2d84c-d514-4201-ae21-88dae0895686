package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceAccount;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 设备-账户表 设备与使用设备的账户关联(HiveDeviceAccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-10 11:14:08
 */
@Mapper
public interface DeviceAccountMapper {

    /**
     * 通用查询，如果分页，请自已实现
     *
     * @param deviceAccount 实例对象
     * @return 实例对象列表
     */
    List<DeviceAccount> query(DeviceAccount deviceAccount);

    /**
     * 新增数据
     *
     * @param deviceAccount 实例对象
     * @return 影响行数
     */
    int insert(DeviceAccount deviceAccount);

    /**
     * 修改数据
     *
     * @param deviceAccount 实例对象
     * @return 影响行数
     */
    int update(DeviceAccount deviceAccount);

    /**
     * 删除数据
     *
     * @param deviceAccount 删除条件
     * @return 影响行数
     */
    int delete(DeviceAccount deviceAccount);

    /**
     * @param list
     * @return
     */
    int insertBatch(List<DeviceAccount> list);

}