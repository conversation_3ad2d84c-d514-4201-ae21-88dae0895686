package com.moredian.magicube.device.pipeline.pipe;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.DirectionEnum;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.constant.StatusCode;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeDto;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.FunctionQrCodeManager;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import com.moredian.magicube.device.pipeline.core.pipeline.InvocationChain;
import com.moredian.magicube.device.pipeline.core.pipeline.rollback.RollBack;
import com.xier.guard.accessKey.dto.AccessKeyDto;
import com.xier.guard.accessKey.service.UserAccessKeyService;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 设备激活通用校验管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommonActivateCheckPipe extends
    AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> implements
    RollBack<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private WhiteDeviceManager whiteDeviceManager;

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;
    @Autowired
    private FunctionQrCodeManager qrCodeManager;
    @SI
    protected UserAccessKeyService userAccessKeyService;

    @SI
    protected OrgService orgService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        //1.判断激活码是否有效
        com.xier.sesame.common.rpc.ServiceResponse<AccessKeyDto> sr = userAccessKeyService
                .issuDeviceAccessKeyAutoRevoke(dto.getDeviceSn());
        BizAssert.isTrue((sr != null && sr.isSuccess() && sr.isExistData()),
                DeviceErrorCode.ISSU_DEVICE_ACCESS_KEY_FAIL,
                DeviceErrorCode.ISSU_DEVICE_ACCESS_KEY_FAIL.getMessage());
        context.setAccessKeyDto(sr.getData());

        //2.判断是否合法设备
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSn(dto.getDeviceSn());
        if (inventoryDevice == null) {
            log.error("设备激活中设备对应的白名单不存在{}", dto);
            ErrorContext errorContext = new ErrorContext("FD0020171201", "设备未经过认证:设备白名单为空！");
            BizAssert.notNull(null, errorContext, errorContext.getMessage());
        } else {
            //fixbug: 偶现 上下文中机构id已经存在,设备白单中绑定的机构id可能是脏数据,不需要放到上下文中
            if (context.getOrgId() == null && inventoryDevice.getOrgId() != null) {
                context.setOrgId(inventoryDevice.getOrgId());
            }
            context.setInventoryDevice(inventoryDevice);
        }
        //3.判断机构是否存在
        if (dto.getOrgId() != null && dto.getOrgId() != 0L) {
            OrgInfo orgInfo = orgService.getOrgInfo(dto.getOrgId(),
                Collections.singletonList(OrgStatus.USABLE.getValue())).pickDataThrowException();
            BizAssert.notNull(orgInfo, DeviceErrorCode.ORG_ID_VALIDATE_GET_ERROR, DeviceErrorCode.ORG_ID_VALIDATE_GET_ERROR.getMessage());
            context.setOrgId(dto.getOrgId());
        }
        //4.判断激活码中是否有选择方向
        if (dto.getQrCodeId() != null && context.getDirection() == null) {
            NetworkQrCodeDto qrCodeDto = qrCodeManager.getNetWorkInfoByQrCodeId(dto.getQrCodeId());
            if (qrCodeDto != null && qrCodeDto.getDirection() != null) {
                log.info("设备{}激活选择方向 direction:{}", dto.getDeviceSn(),
                        DirectionEnum.getDirectionName(qrCodeDto.getDirection()));
                context.setDirection(qrCodeDto.getDirection());
            }
        }
    }

    @Override
    public void rollBack(
        InvocationChain<ActivateDeviceDTO, ActivateDeviceContext> invocationChain) {
        ActivateDeviceDTO dto = invocationChain.getParameter();
        if (StringUtils.isNotBlank(dto.getThirdDeviceId())) {
            Integer status = (Integer) redissonCacheComponent.getObjectCache(dto
                .getThirdDeviceId() + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY);
            if (status == null) {
                redissonCacheComponent.setObjectCache(dto.getThirdDeviceId()
                        + HiveConst.MEMCACHED_THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY,
                    StatusCode.THIRD_PARTY_ACTIVE_FAILED, HiveConst.THIRD_PARTY_TIME_OUT);
            }
        } else if (StringUtils.isNotBlank(dto.getQrCode())) {
            redissonCacheComponent.setObjectCache(dto.getQrCode() +
                    HiveConst.MEMCACHED_EQUIPMENT_ACTIVE_STATUS_KEY,
                StatusCode.ACTIVE_FAIL, HiveConst.ACTIVE_DEVICE_TIME_OUT);
        }
    }
}
