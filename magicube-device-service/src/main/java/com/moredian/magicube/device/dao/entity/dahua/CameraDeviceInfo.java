package com.moredian.magicube.device.dao.entity.dahua;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import java.io.Serializable;
import javax.ws.rs.GET;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 大华摄像头设备信息
 *
 * <AUTHOR>
 */

@Setter
@Getter
@ToString
@Accessors(chain = true)
@TableName("hive_device_camera_info")
public class CameraDeviceInfo extends TimedEntity {

    private static final long serialVersionUID = -7967834674046201307L;

    /**
     * 摄像头设备Id
     */
    private Long cameraDeviceId;

    /**
     * 机构Id
     */
    private Long orgId;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备ip
     */
    private String deviceIp;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 设备用户名
     */
    private String userName;

    /**
     * 设备密码
     */
    private String password;

    /**
     * 注册类型 1-平台注册 2-设备注册
     */
    private Integer registerType;

    /**
     * 本地设备ip
     */
    private String localDeviceIp;

    /**
     * 本地端口号
     */
    private Integer localPort;
}