package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.Distributor;
import com.moredian.magicube.device.dao.mapper.DistributorMapper;
import com.moredian.magicube.device.manager.DistributorManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */

@Service
public class DistributorManagerImpl implements DistributorManager {

    @Autowired
    private DistributorMapper distributorMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(Distributor distributor) {
        //以前用的就是这个对象生成的id, 不敢改动
        Long id = idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_WHITE_LIST)
            .pickDataThrowException();
        distributor.setDistributorId(id);
        distributorMapper.insert(distributor);
        return id;
    }

    @Override
    public List<Distributor> listByOrgName(String orgName) {
        return distributorMapper.listByOrgName(orgName);
    }

    @Override
    public Distributor getByDeviceSn(String deviceSn) {
        return distributorMapper.getByDeviceSn(deviceSn);
    }
}
