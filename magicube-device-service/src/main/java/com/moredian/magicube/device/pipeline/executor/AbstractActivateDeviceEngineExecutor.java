package com.moredian.magicube.device.pipeline.executor;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.activate.ActivateDeviceResultDTO;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.engine.AbstractEngineExecutor;
import com.moredian.magicube.device.pipeline.core.engine.EngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.*;
import com.moredian.magicube.device.pipeline.pipe.ding.BindingGroupPipe;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 抽象激活设备引擎
 * 目前主要有4种类型的引擎:
 *
 * 1.魔点设备扫码激活流程 checkDeviceAuthorizePipe->commonActivateCheckPipe->moDeviceCheckPipe->verifyChannelAndArithmeticPipe
 * ->deviceNameConvertPipe->insertDevicePipe->enableBizPipe->insertDeviceLogPipe
 * ->updateDeviceWhitePipe->publishMsgPipe->buildResultPipe
 *
 * 2.激活码激活流程 checkDeviceAuthorizePipe->commonActivateCheckPipe->codeCheckPipe->verifyChannelAndArithmeticPipe
 * ->deviceNameConvertPipe->insertDevicePipe->enableBizPipe->insertDeviceLogPipe
 * ->updateDeviceWhitePipe->publishMsgPipe->buildResultPipe
 *
 * 3.钉钉设备扫码激活流程 checkDeviceAuthorizePipe->commonActivateCheckPipe->dingDeviceCheckPipe->verifyChannelAndArithmeticPipe
 * ->insertDevicePipe->bindingGroupPipe->enableBizPipe->insertDeviceLogPipe->updateDeviceWhitePipe
 * ->publishMsgPipe->buildResultPipe
 *
 * 4.没开通魔点门禁微应用激活流程 checkDeviceAuthorizePipe->commonActivateCheckPipe->noEnableDingDeviceCheckPipe
 * ->verifyChannelAndArithmeticPipe->insertDevicePipe->insertDeviceLogPipe->updateDeviceWhitePipe
 * ->publishMsgPipe->buildResultPipe
 *
 * <AUTHOR>
 */
public abstract class AbstractActivateDeviceEngineExecutor
    extends
    AbstractEngineExecutor<ActivateDeviceDTO, ActivateDeviceContext, ActivateDeviceResultDTO>
    implements EngineExecutor<ActivateDeviceDTO, ActivateDeviceContext, ActivateDeviceResultDTO> {

    /**
     * 公共管道
     */
    @Autowired
    protected InsertDevicePipe insertDevicePipe;

    @Autowired
    protected BindingGroupPipe bindingGroupPipe;

    @Autowired
    protected CommonActivateCheckPipe commonActivateCheckPipe;

    @Autowired
    protected CheckDeviceAuthorizePipe checkDeviceAuthorizePipe;

    @Autowired
    protected UploadApkVersionPipe uploadApkVersionPipe;

    @Autowired
    protected EnableBizPipe enableBizPipe;

    @Autowired
    protected InsertDeviceLogPipe insertDeviceLogPipe;

    @Autowired
    protected PublishMsgPipe publishMsgPipe;

    @Autowired
    protected UpdateDeviceWhitePipe updateDeviceWhitePipe;

    @Autowired
    protected VerifyChannelAndArithmeticPipe verifyChannelAndArithmeticPipe;

    @Autowired
    protected BuildResultPipe buildResultPipe;

    @Autowired
    protected MonitorDataPipe monitorDataPipe;

    @Override
    public void validCommonParameter(ActivateDeviceDTO dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getDeviceSn(), "deviceSn must not be null");
        BizAssert.notNull(dto.getDeviceType(), "deviceType must not be null");
    }

    @Override
    public ActivateDeviceResultDTO execute(ActivateDeviceDTO dto, ActivateDeviceContext context,
        ActivateDeviceResultDTO resultDTO) {

        doPipeline(dto, context);

        return context.getResultDTO();
    }
}
