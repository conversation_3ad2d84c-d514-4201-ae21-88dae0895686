package com.moredian.magicube.device.subscriber;

import cn.hutool.core.thread.ThreadUtil;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.core.msg.EnterpriseAccountStatusChangeMsg;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.helper.MqttMessageComponent;
import com.moredian.magicube.device.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14 15:48
 */
@Slf4j
@Component
public class EnterpriseAccountStatusChangeSubscriber {
    @SI
    private DeviceService deviceService;

    @Autowired
    private MqttMessageComponent mqttMessageComponent;

    @Subscribe
    public void receiveEnterpriseAccountStatusChangeMsg(EnterpriseAccountStatusChangeMsg enterpriseAccountStatusChangeMsg) {
        log.info("EnterpriseAccountStatusChangeSubscriber#receiveEnterpriseAccountStatusChangeMsg===,params=({})", JsonUtils.toJson(enterpriseAccountStatusChangeMsg));
        List<DeviceInfoDTO> allDevice = deviceService.listByOrgId(enterpriseAccountStatusChangeMsg.getOrgId()).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(allDevice)) {
            ThreadUtil.execute(() -> {
                for (DeviceInfoDTO deviceInfo : allDevice) {
                    try {
                        mqttMessageComponent.sendMqttMessage(deviceInfo.getDeviceSn(),
                                TransferEventType.ENTERPRISE_ACCOUNT_STATUS_CHANGE.getEventType(), null, TransferEventType.ENTERPRISE_ACCOUNT_STATUS_CHANGE.getEventName());
                    } catch (Exception e) {
                        log.error("sendMqttMessage error,sn={}",deviceInfo.getDeviceSn(), e);
                    }
                }
            });
        }
    }
}
