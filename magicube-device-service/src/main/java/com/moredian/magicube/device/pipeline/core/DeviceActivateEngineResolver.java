package com.moredian.magicube.device.pipeline.core;

import com.moredian.magicube.device.pipeline.core.engine.EngineExecutor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备激活引擎解析器
 * <p>通过spring的特性策略模式初始化EngineExecutorMap</p>
 *
 * <AUTHOR>
 */
@Component
public class DeviceActivateEngineResolver implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private final Map<Integer, EngineExecutor> engineExecutorMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        Map<String, EngineExecutor> beanMap = applicationContext.getBeansOfType(EngineExecutor.class);
        for (EngineExecutor executor : beanMap.values()) {
            this.engineExecutorMap.put(executor.getType(), executor);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public EngineExecutor getExecutor(Integer type) {
        return engineExecutorMap.get(type);
    }
}
