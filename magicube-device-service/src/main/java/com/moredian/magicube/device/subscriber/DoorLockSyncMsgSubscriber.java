package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.UUID;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.ControllerErrorCode;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.core.msg.DoorLockSyncMsg;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DoorLockSyncMsgSubscriber {

    @Autowired
    private DeviceManager deviceManager;

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @Subscribe
    public void subscribeDoorLockSyncMsg(DoorLockSyncMsg msg) {
        log.info("subscribeDoorLockSyncMsg receive DoorLockSyncMsg:{}", msg);
        // 获取需要通知的设备
        List<Device> devices = deviceManager.listByOrgIdAndTypes(msg.getOrgId(),Collections.singletonList(
                DeviceType.DOOR_LOCK.getValue()) );
        if (CollectionUtils.isEmpty(devices)) {
            log.warn("Notify device DoorLockSyncMsg,no device is found");
        } else {
            log.info("Notify device DoorLockSyncMsg,deviceCount:{}", devices.size());
            for (Device device : devices) {
                try {
                    // 发送命令
                    String deviceSn = device.getDeviceSn();
                    TransferMessageInfo<String> transferMessageInfo = new TransferMessageInfo<>();
                    transferMessageInfo
                        .setEventType(TransferEventType.DOOR_LOCK_ALL_SYNC.getEventType());
                    transferMessageInfo.setSeverity(3);
                    transferMessageInfo.setSeqId(UUID.random19()
                        + TransferEventType.DOOR_LOCK_ALL_SYNC.getEventName());
                    transferMessageInfo
                        .setMessage(TransferEventType.DOOR_LOCK_ALL_SYNC.getEventName());
                    String jsonStr = JsonUtils.toJson(transferMessageInfo);
                    String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
                    TransferRequest transferRequest = new TransferRequest();
                    transferRequest.setSerialNumber(deviceSn);
                    transferRequest.setBody(base64Message);
                    transferRequest.setCommand(0);
                    ServiceResponse<TransferResponse> response = hubControlServiceV1.transfer(
                        transferRequest);
                    if (response != null && !response.isSuccess()
                        && ControllerErrorCode.DEVICE_OFFLINE.getCode()
                        .equals(response.getErrorContext().getCode())) {
                        log.info(
                            "Notify device DoorLockSyncMsg, 设备已离线，本次不发送通知，[org={},deviceSn={},deviceId={}]",
                            msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                        continue;
                    }

                    TransferResponse result = response.pickDataThrowException();
                    log.info("Notify device DoorLockSyncMsg, [org={},deviceSn={},deviceId={}]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId());
                } catch (Exception e) {
                    log.error(
                        "Notify device DoorLockSyncMsg failed. [org={},device [sn={},id={}]]",
                        msg.getOrgId(), device.getDeviceSn(), device.getDeviceId(), e);
                }
            }
        }
    }
}
