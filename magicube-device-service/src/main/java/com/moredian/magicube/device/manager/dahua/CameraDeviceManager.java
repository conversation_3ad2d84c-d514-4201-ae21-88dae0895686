package com.moredian.magicube.device.manager.dahua;

import com.moredian.magicube.device.dao.entity.dahua.CameraDeviceInfo;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.dto.dahua.QueryCameraDTO;
import java.util.List;

/**
 * 大华摄像头设备相关接口
 *
 * <AUTHOR>
 */
public interface CameraDeviceManager {

    /**
     * 查询摄像头列表
     *
     * @return
     */
    List<CameraDeviceInfo> list();

    /**
     * 根据ip和端口号查询摄像头信息
     *
     * @param ip 设备ip
     * @param port 设备端口
     * @return
     */
    CameraDeviceInfo getByIpAndPort(String ip, int port);

    /**
     * 根据条件查询大华摄像头，条件同时满足，没条件查询所有设备
     */
    List<CameraDeviceInfo> listByCondition(QueryCameraDTO dto);

    /**
     * 根据设备sn修改
     */
    Boolean updateByDeviceSn(String deviceSn, CameraDeviceInfoDTO dto);
}
