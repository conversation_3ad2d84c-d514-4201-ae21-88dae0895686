package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.BeanUtils;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.GlobalConfig;
import com.moredian.magicube.device.dto.global.GlobalConfigDTO;
import com.moredian.magicube.device.manager.GlobalConfigManager;
import com.moredian.magicube.device.service.GlobalConfigService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Auther: _AF
 * @Date: 12/28/21 15:14
 * @Description:
 */
@SI
public class GlobalConfigServiceImpl implements GlobalConfigService {

    @Autowired
    private GlobalConfigManager globalConfigManager;

    @Override
    public ServiceResponse<GlobalConfigDTO> getByConfigName(String configName) {
        GlobalConfig globalConfig = globalConfigManager.getByConfigName(configName);
        if (globalConfig == null) {
            return new ServiceResponse(null);
        }
        GlobalConfigDTO globalConfigDTO = BeanUtils.copyProperties(GlobalConfigDTO.class, globalConfig);
        return new ServiceResponse<>(globalConfigDTO);
    }
}
