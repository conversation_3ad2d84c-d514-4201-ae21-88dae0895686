package com.moredian.magicube.device.dao.entity;

import java.util.Date;
import lombok.Data;

/**
 * @Auther: _AF
 * @Date: 2/23/22 16:58
 * @Description:新的测温外设白名单
 */
@Data
public class PeripheralsWhiteListNew {

    private Long id;

    /**
     * 外设sn
     */
    private String peripheralsSn;
    /**
     * 关联设备类型，全部类型为0
     */
    private Integer deviceType;
    /**
     * 关联appType类型，全部为0
     */
    private Integer appType;
    /**
     * 外设类型
     *
     * @see com.moredian.fishnet.device.enums.PeripheralsType
     */
    private Integer peripheralsType;

    private Date gmtCreate;

    private Date gmtModify;
}
