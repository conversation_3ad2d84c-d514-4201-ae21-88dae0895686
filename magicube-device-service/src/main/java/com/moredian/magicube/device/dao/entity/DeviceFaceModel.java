package com.moredian.magicube.device.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-27
 */
@Data
public class DeviceFaceModel implements Serializable {

    private static final long serialVersionUID = -6487943882660873398L;

    /**
     * 主键ID
     */
    private Long deviceFaceModelId;

    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 人脸模型
     */
    private String faceModel;

    /**
     * 1服务识别用的端特征值，2Android终端识别用的特征值
     */
    private Integer modelType;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModify;

}
