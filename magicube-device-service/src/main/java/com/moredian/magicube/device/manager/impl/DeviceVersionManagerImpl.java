package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.AppSystemType;
import com.moredian.magicube.common.model.msg.device.DeviceAppVersionReportMsg;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.constant.CacheNameConstants;
import com.moredian.magicube.device.dao.entity.BeforeActivationDeviceVersion;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceVersion;
import com.moredian.magicube.device.dao.entity.StockDeviceVersion;
import com.moredian.magicube.device.dao.mapper.*;
import com.moredian.magicube.device.dto.version.*;
import com.moredian.magicube.device.enums.DeviceFlagEnum;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.helper.TransactionCommitHandler;
import com.moredian.magicube.device.manager.DeviceVersionManager;
import com.moredian.magicube.device.service.AppVersionService;
import com.moredian.magicube.device.service.helper.DdBizManageHelper;
import com.moredian.ota.api.enums.OtaAppType;
import com.moredian.ota.api.request.task.ReportDeviceVersionInfoRequest;
import com.moredian.ota.api.service.OtaTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceVersionManagerImpl implements DeviceVersionManager {

    @SI
    private AppVersionService appVersionService;

    @SI
    private OtaTaskService otaTaskService;

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private DeviceApkVersionMapper deviceApkVersionMapper;

    @Autowired
    private DeviceRomVersionMapper deviceRomVersionMapper;

    @Autowired
    private BeforeActivationDeviceVersionMapper beforeActivationDeviceVersionMapper;

    @Autowired
    private StockDeviceVersionMapper stockDeviceVersionMapper;

    @Autowired
    private Executor deviceReportVersionExecutor;

    @Autowired
    private RedissonLockComponent redissonLockComponent;

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    @Autowired
    private DeviceMapper deviceMapper;
    
    @Autowired
    private DdBizManageHelper ddBizManageHelper;
    
    @Resource
    private TransactionCommitHandler transactionCommitHandler;

    /**
     * 默认版本缓存时间
     */
    private final long DEFAULT_DEVICE_APP_VERSION_CACHE_TIME = 3600 * 24 * 25;

    /**
     * 默认版本缓存时间
     */
    private final long DEFAULT_DEVICE_ROM_VERSION_CACHE_TIME = 3600 * 24 * 25;

    /**
     * 默认强制升级状态缓存时间
     */
    private final long DEFAULT_DEVICE_ENFORCE_UPDATE_STATUS_CACHE_TIME = 3600 * 24 * 25;

    private int DEFAULT_MAX_COUNT = 100;

    //ID右移得到时间戳 用于24小时的均匀分布
    private final Integer ID_MOVE_FOR_GET_TIME= 33;

    @Override
    public void changeDeviceAppVersion(ChangeDeviceAppVersionDTO dto) {
        BizAssert.notNull(dto.getDeviceId(), "deviceId must not be null");
        try {
            DeviceVersionCacheData deviceVersionCacheData = new DeviceVersionCacheData();
            deviceVersionCacheData.setAppType(dto.getAppType());
            deviceVersionCacheData.setVersionCode(dto.getVersionCode());
            deviceVersionCacheData.setOrgId(dto.getOrgId());
            redissonCacheComponent.setObjectCache(dto.getDeviceId() +
                    CacheNameConstants.DEVIE_APP_VERSION_NEW,
                deviceVersionCacheData, DEFAULT_DEVICE_APP_VERSION_CACHE_TIME);

            DeviceVersionCacheData deviceAppVersionCacheData = new DeviceVersionCacheData();
            deviceAppVersionCacheData.setAppType(dto.getAppType());
            deviceAppVersionCacheData.setVersionCode(dto.getVersionCode());
            deviceAppVersionCacheData.setOrgId(dto.getOrgId());
            redissonCacheComponent.setObjectCache(dto.getDeviceId() +
                    CacheNameConstants.DEVIE_APP_VERSION,
                deviceAppVersionCacheData, DEFAULT_DEVICE_APP_VERSION_CACHE_TIME);

            //将数据持久化到数据库
            String key = RedisKeys.getKey(RedisKeys.DEVICE_REPORT_APK_VERSION, dto.getSn());
            try {
                if (StringUtils.isNotBlank(dto.getSn())) {
                    if (redissonLockComponent.acquire(key)) {
                        DeviceVersion deviceVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(dto.getSn());
                        if (deviceVersion == null) {
                            DeviceVersion newDeviceVersion = new DeviceVersion();
                            Long id = idgeneratorService.getNextIdByTypeName(DeviceVersion.class.getName()).pickDataThrowException();
                            newDeviceVersion.setId(id);
                            buildDeviceVersion(newDeviceVersion, dto);
                            deviceApkVersionMapper.insert(newDeviceVersion);
                        } else {
                            updDeviceApkVersionIfNeed(dto, deviceVersion);
                        }
                        ReportDeviceVersionInfoRequest request = new ReportDeviceVersionInfoRequest();
                        request.setDeviceSn(dto.getSn());
                        request.setVersionCode(dto.getVersionCode());
                        request.setAppType(dto.getAppType());
                        request.setPackageType(OtaAppType.APP.getValue());
                        deviceReportVersionExecutor.execute(() ->{
                            otaTaskService.updateTaskState(request);
                        });
                        //新增：发送设备激活后上报版本消息
//                        transactionCommitHandler.handle(() -> {
                            //发送消息
                            DeviceAppVersionReportMsg deviceAppVersionReportMsg = new DeviceAppVersionReportMsg();
                            deviceAppVersionReportMsg.setOrgId(dto.getOrgId());
                            deviceAppVersionReportMsg.setAppType(dto.getAppType());
                            deviceAppVersionReportMsg.setVersionCode(dto.getVersionCode());
                            deviceAppVersionReportMsg.setDeviceId(dto.getDeviceId());
                            deviceAppVersionReportMsg.setDeviceSn(dto.getSn());
                            EventBus.publish(deviceAppVersionReportMsg);
//                        });
                    } else {
                        log.info("设备上报激活后apk版本信息分布式锁获取失败:{}", JsonUtils.toJson(dto));
                    }
                }
            } catch (Exception e) {
                log.error("激活后apk版本信息持久化失败:{},{}", JsonUtils.toJson(dto), e);
            } finally {
                redissonLockComponent.release(key);
            }

        } catch (Exception e) {
            log.warn("保存设备app版本号异常", e);
        }
    }

    @SuppressWarnings("rawtypes")
    @Override
    public void changeDeviceRomVersion(ChangeDeviceAppVersionDTO dto) {
        BizAssert.notNull(dto.getDeviceId(), "deviceId must not be null");
        try {
            DeviceVersionCacheData deviceVersionCacheData = new DeviceVersionCacheData();
            deviceVersionCacheData.setAppType(dto.getAppType());
            deviceVersionCacheData.setVersionCode(dto.getVersionCode());
            deviceVersionCacheData.setOrgId(dto.getOrgId());
            redissonCacheComponent.setObjectCache(dto.getDeviceId() +
                    CacheNameConstants.DEVIE_ROM_VERSION_NEW,
                deviceVersionCacheData, DEFAULT_DEVICE_ROM_VERSION_CACHE_TIME);

            DeviceVersionCacheData deviceAppVersionCacheData = new DeviceVersionCacheData();
            deviceAppVersionCacheData.setAppType(dto.getAppType());
            deviceAppVersionCacheData.setVersionCode(dto.getVersionCode());
            deviceAppVersionCacheData.setOrgId(dto.getOrgId());
            redissonCacheComponent.setObjectCache(dto.getDeviceId() +
                    CacheNameConstants.DEVIE_ROM_VERSION,
                deviceAppVersionCacheData, DEFAULT_DEVICE_ROM_VERSION_CACHE_TIME);

            //将数据持久化到数据库
            String key = RedisKeys.getKey(RedisKeys.DEVICE_REPORT_ROM_VERSION, dto.getSn());
            try {
                if (StringUtils.isNotBlank(dto.getSn())) {
                    if (redissonLockComponent.acquire(key)) {
                        DeviceVersion deviceVersion = deviceRomVersionMapper.getDeviceRomVersionBySn(dto.getSn());
                        if (deviceVersion == null) {
                            DeviceVersion newDeviceVersion = new DeviceVersion();
                            Long id = idgeneratorService.getNextIdByTypeName(DeviceVersion.class.getName()).pickDataThrowException();
                            newDeviceVersion.setId(id);
                            buildDeviceVersion(newDeviceVersion, dto);
                            deviceRomVersionMapper.insert(newDeviceVersion);
                        } else {
                            updDeviceRomVersionIfNeed(dto, deviceVersion);
                        }
                        ReportDeviceVersionInfoRequest request = new ReportDeviceVersionInfoRequest();
                        request.setDeviceSn(dto.getSn());
                        request.setVersionCode(dto.getVersionCode());
                        request.setAppType(dto.getAppType());
                        request.setPackageType(OtaAppType.ROM.getValue());
                        deviceReportVersionExecutor.execute(() ->{
                            otaTaskService.updateTaskState(request);
                        });
                    } else {
                        log.info("设备上报激活后rom版本信息分布式锁获取失败:{}", JsonUtils.toJson(dto));
                    }
                }
            } catch (Exception e) {
                log.error("激活后rom版本信息持久化失败:{},{}", JsonUtils.toJson(dto), e);
            } finally {
                redissonLockComponent.release(key);
            }

        } catch (Exception e) {
            log.error("保存设备app版本号异常", e);
        }
    }

    @SuppressWarnings("rawtypes")
    @Override
    public void changeDeviceEnforceUpdateStatus(DeviceEnforceUpdateDTO dto) {
        BizAssert.notNull(dto.getDeviceId(), "deviceId must not be null");
        try {
            redissonCacheComponent.setObjectCache(dto.getDeviceId() +
                    CacheNameConstants.DEVICE_ENFORCE_UPDATE_STATUS,
                dto, DEFAULT_DEVICE_ENFORCE_UPDATE_STATUS_CACHE_TIME);
        } catch (Exception e) {
            log.error("更新设备强制升级状态异常", e);
        }
    }

    @Override
    public DeviceEnforceUpdateDTO getDeviceEnforceUpdateStatus(Long deviceId) {
        BizAssert.notNull(deviceId, "deviceId must not be null");
        DeviceEnforceUpdateDTO deviceEnforceUpdateDTO = null;
        try {
            deviceEnforceUpdateDTO = (DeviceEnforceUpdateDTO) redissonCacheComponent.
                getObjectCache(deviceId + CacheNameConstants.DEVICE_ENFORCE_UPDATE_STATUS);
        } catch (Exception e) {
            log.error("获取设备强制升级状态异常", e);
        }
        return deviceEnforceUpdateDTO;
    }

    @Override
    public List<DeviceVersionDTO> listApkByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        if (deviceIds.size() > DEFAULT_MAX_COUNT) {
            BizAssert.notNull(orgId, DeviceErrorCode.TOO_MANY_DATA_REQUEST, DeviceErrorCode.TOO_MANY_DATA_REQUEST.getMessage());
        }
        List<DeviceVersionDTO> resultData = new ArrayList<>();
        for (Long currentDeviceId : deviceIds) {
            DeviceVersionDTO deviceVersionDTO = new DeviceVersionDTO();
            deviceVersionDTO.setDeviceId(currentDeviceId);
            resultData.add(deviceVersionDTO);
            try {
                DeviceVersionCacheData deviceVersionCacheData = (DeviceVersionCacheData) redissonCacheComponent.getObjectCache(currentDeviceId + CacheNameConstants.DEVIE_APP_VERSION_NEW);
                if (deviceVersionCacheData != null) {
                    Long curOrgId = deviceVersionCacheData.getOrgId();
                    if (curOrgId != null && curOrgId.equals(orgId)) {
                        deviceVersionDTO.setAppType(deviceVersionCacheData.getAppType());
                        deviceVersionDTO.setVersionCode(deviceVersionCacheData.getVersionCode());
                    }
                }
            } catch (Exception e) {
                log.error("获取设备apk版本信息错误", e);
                e.printStackTrace();
            }
        }
        return resultData;
    }

    @Override
    public List<DeviceVersionDTO> listRomByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        if (deviceIds.size() > DEFAULT_MAX_COUNT) {
            BizAssert.notNull(orgId, DeviceErrorCode.TOO_MANY_DATA_REQUEST, DeviceErrorCode.TOO_MANY_DATA_REQUEST.getMessage());
        }
        List<DeviceVersionDTO> resultData = new ArrayList<>();
        for (Long currentDeviceId : deviceIds) {
            DeviceVersionDTO deviceVersionDTO = new DeviceVersionDTO();
            deviceVersionDTO.setDeviceId(currentDeviceId);
            resultData.add(deviceVersionDTO);
            try {
                DeviceVersionCacheData deviceVersionCacheData = (DeviceVersionCacheData) redissonCacheComponent.getObjectCache(currentDeviceId + CacheNameConstants.DEVIE_ROM_VERSION_NEW);
                if (deviceVersionCacheData != null) {
                    Long curOrgId = deviceVersionCacheData.getOrgId();
                    if (curOrgId != null && curOrgId.equals(orgId)) {
                        deviceVersionDTO.setAppType(deviceVersionCacheData.getAppType());
                        deviceVersionDTO.setVersionCode(deviceVersionCacheData.getVersionCode());
                    }
                }
            } catch (Exception e) {
                log.error("获取设备rom版本信息错误", e);
                e.printStackTrace();
            }
        }
        return resultData;
    }

    @Override
    public List<DeviceCurrVersionDTO> listByOrgIdAndDeviceIds(Long orgId, List<Long> deviceIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be null");
        if (deviceIds.size() > DEFAULT_MAX_COUNT) {
            BizAssert.notNull(orgId, DeviceErrorCode.TOO_MANY_DATA_REQUEST, DeviceErrorCode.TOO_MANY_DATA_REQUEST.getMessage());
        }
        int systemType = AppSystemType.ANDROID.getValue();
        List<DeviceCurrVersionDTO> resultData = new ArrayList<>();
        
        // 获取设备列表的信息，用于判断设备类型
        List<Device> deviceList = deviceMapper.listByIds(deviceIds);
        Map<Long, Device> deviceMap = deviceList.stream()
            .collect(Collectors.toMap(Device::getDeviceId, Function.identity(), (o, n) -> n));
            
        for (Long currentDeviceId : deviceIds) {
            DeviceCurrVersionDTO deviceCurrVersionDTO = new DeviceCurrVersionDTO();
            deviceCurrVersionDTO.setDeviceId(currentDeviceId);
            try {
                // 获取设备信息，判断是否是分销融元设备
                Device device = deviceMap.get(currentDeviceId);
                boolean isFxDevice = device != null && 
                    device.getDeviceFlag() != null && 
                    device.getDeviceFlag().equals(DeviceFlagEnum.FX.getValue());
                
                //1.查询当前设备app版本信息
                Integer currAppType = 0;
                DeviceVersionCacheData deviceAppVersionCacheData = (DeviceVersionCacheData) redissonCacheComponent.getObjectCache(currentDeviceId + CacheNameConstants.DEVIE_APP_VERSION_NEW);
                if (deviceAppVersionCacheData != null) {
                    Long curOrgId = deviceAppVersionCacheData.getOrgId();
                    if (curOrgId != null && curOrgId.equals(orgId)) {
                        currAppType = deviceAppVersionCacheData.getAppType();
                        Integer currAppVersionCode = deviceAppVersionCacheData.getVersionCode();
                        VersionDTO currAppVersionDto;
                        if (isFxDevice) {
                            // 分销融元设备调用塔上请求
                            currAppVersionDto = ddBizManageHelper.getAppVersionBySCTypeCode(systemType, currAppType, currAppVersionCode);
                        } else {
                            // 非分销设备走原来逻辑
                            currAppVersionDto = appVersionService.getAppVersionBySCTypeCode(systemType, currAppType, currAppVersionCode).pickDataThrowException();
                        }
                        if (currAppVersionDto != null) {
                            deviceCurrVersionDTO.setCurrAppVersion(currAppType, currAppVersionCode, currAppVersionDto.getVersionName(), currAppVersionDto.getVersionDesc());
                        }
                    }
                } else if (isFxDevice) {
                    DeviceVersion deviceApkVersion = deviceApkVersionMapper.getDeviceApkVersionBySn(device.getDeviceSn());
                    if (deviceApkVersion != null){
                        currAppType = deviceApkVersion.getAppType();
                        Integer currAppVersionCode = deviceApkVersion.getVersionCode();
                        VersionDTO currAppVersionDto = ddBizManageHelper.getAppVersionBySCTypeCode(systemType, currAppType, currAppVersionCode);
                        if (currAppVersionDto != null) {
                            deviceCurrVersionDTO.setCurrAppVersion(currAppType, currAppVersionCode, currAppVersionDto.getVersionName(), currAppVersionDto.getVersionDesc());
                        }
                    }
                }

                //2.查询当前设备rom版本信息
                Integer currRomType = 0;
                DeviceVersionCacheData deviceRomVersionCacheData = (DeviceVersionCacheData) redissonCacheComponent.getObjectCache(currentDeviceId + CacheNameConstants.DEVIE_ROM_VERSION_NEW);
                if (deviceRomVersionCacheData != null) {
                    Long curOrgId = deviceRomVersionCacheData.getOrgId();
                    if (curOrgId != null && curOrgId.equals(orgId)) {
                        currRomType = deviceRomVersionCacheData.getAppType();
                        Integer currRomVersionCode = deviceRomVersionCacheData.getVersionCode();
                        VersionDTO currRomVersion;
                        if (isFxDevice) {
                            // 分销融元设备调用塔上请求
                            currRomVersion = ddBizManageHelper.getRomVersionBySCTypeCode(systemType, currRomType, currRomVersionCode, currAppType);
                        } else {
                            // 非分销设备走原来逻辑
                            currRomVersion = appVersionService.getRomVersionBySCTypeCode(systemType, currRomType, currRomVersionCode, currAppType).pickDataThrowException();
                        }
                        if (currRomVersion != null) {
                            deviceCurrVersionDTO.setCurrRomVersion(currRomType, currRomVersionCode, currRomVersion.getVersionName(), currRomVersion.getVersionDesc());
                        }
                    }
                }else if (isFxDevice) {
                    DeviceVersion deviceRomVersion = deviceRomVersionMapper.getDeviceRomVersionBySn(device.getDeviceSn());
                    if (deviceRomVersion != null){
                        currRomType = deviceRomVersion.getAppType();
                        Integer currRomVersionCode = deviceRomVersion.getVersionCode();
                        VersionDTO currRomVersion = ddBizManageHelper.getRomVersionBySCTypeCode(systemType, currRomType, currRomVersionCode, currAppType);
                        if (currRomVersion != null) {
                            deviceCurrVersionDTO.setCurrRomVersion(currRomType, currRomVersionCode, currRomVersion.getVersionName(), currRomVersion.getVersionDesc());
                        }
                    }
                }

                //3.查询可更新设备rom版本信息
                VersionDTO lastRomVersion = null;
                if (currRomType > 0) {
                    if (isFxDevice) {
                        // 分销融元设备调用塔上请求
                        lastRomVersion = ddBizManageHelper.getNewRomVersionBySCTypeAndIsActive(systemType, currRomType, currAppType);
                    } else {
                        // 非分销设备走原来逻辑
                        lastRomVersion = appVersionService.getNewRomVersionBySCTypeAndIsActive(systemType, currRomType, currAppType).pickDataThrowException();
                    }
                    if (lastRomVersion != null) {
                        deviceCurrVersionDTO.setLastRomVersion(currRomType, lastRomVersion.getVersionCode(), lastRomVersion.getVersionName(), lastRomVersion.getVersionDesc());
                    }
                }

                //4.查询可更新设备apk版本信息
                if (currAppType > 0) {
                    VersionDTO lastAppVersion;
                    if (isFxDevice) {
                        // 分销融元设备调用塔上请求
                        lastAppVersion = ddBizManageHelper.getNewAppVersionBySCTypeAndIsActive(systemType, currAppType);
                    } else {
                        // 非分销设备走原来逻辑
                        lastAppVersion = appVersionService.getNewAppVersionBySCTypeAndIsActive(systemType, currAppType).pickDataThrowException();
                    }
                    if (lastAppVersion != null) {
                        if (lastRomVersion != null && lastRomVersion.getApkAppType() != null && lastRomVersion.getApkVersionCode() != null
                                && lastAppVersion.getVersionCode() != null && lastRomVersion.getApkVersionCode() >= lastAppVersion.getVersionCode()) {
                            //如果rom里面携带了apk，并且携带的apk的版本比单独升级的apk版本要高，则不单独升级apk。只升级rom即可
                        } else {
                            deviceCurrVersionDTO.setLastAppVersion(currAppType, lastAppVersion.getVersionCode(), lastAppVersion.getVersionName(), lastAppVersion.getVersionDesc());
                        }
                    }
                }

            } catch (Exception e) {
                log.error("获取设备app和rom版本信息错误", e);
                e.printStackTrace();
            }
            resultData.add(deviceCurrVersionDTO);
        }
        return resultData;
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceRomVersionFromDatabase(List<String> deviceSnList) {
        ServiceResponse<List<DeviceVersionInfoDTO>> response = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceSnList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        List<DeviceVersion> deviceRomVersionList = deviceRomVersionMapper.getBatchDeviceRomVersion(deviceSnList);
        if (CollectionUtils.isEmpty(deviceRomVersionList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        List<DeviceVersionInfoDTO> deviceVersionResponseList = deviceRomVersionList.stream().map(deviceVersion -> {
            DeviceVersionInfoDTO deviceVersionResponse = new DeviceVersionInfoDTO();
            BeanUtils.copyProperties(deviceVersion, deviceVersionResponse);
            return deviceVersionResponse;
        }).collect(Collectors.toList());
        response.setData(deviceVersionResponseList);
        return response;
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceApkVersionFromDatabase(List<String> deviceSnList) {
        ServiceResponse<List<DeviceVersionInfoDTO>> response = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceSnList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        List<DeviceVersion> deviceApkVersionList = deviceApkVersionMapper.getBatchDeviceApkVersion(deviceSnList);
        if (CollectionUtils.isEmpty(deviceApkVersionList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        List<DeviceVersionInfoDTO> deviceVersionResponseList = deviceApkVersionList.stream().map(deviceVersion -> {
            DeviceVersionInfoDTO deviceVersionResponse = new DeviceVersionInfoDTO();
            BeanUtils.copyProperties(deviceVersion, deviceVersionResponse);
            return deviceVersionResponse;
        }).collect(Collectors.toList());
        response.setData(deviceVersionResponseList);
        return response;
    }

    @Override
    public ServiceResponse<Boolean> reportDeviceRomVersionBeforeActivation(ChangeDeviceAppVersionDTO changeDeviceVersionDto) {
        ServiceResponse<Boolean> response = ServiceResponse.createSuccessResponse();
        try {
            if (StringUtils.isBlank(changeDeviceVersionDto.getSn()) || changeDeviceVersionDto.getAppType() == null || changeDeviceVersionDto.getVersionCode() == null) {
                log.info("激活前上报版本信息失败:[{}]", JsonUtils.toJson(changeDeviceVersionDto));
                response.setData(Boolean.FALSE);
                return response;
            }
            //更改ota设备任务状态
            ReportDeviceVersionInfoRequest request = new ReportDeviceVersionInfoRequest();
            request.setDeviceSn(changeDeviceVersionDto.getSn());
            request.setVersionCode(changeDeviceVersionDto.getVersionCode());
            request.setAppType(changeDeviceVersionDto.getAppType());
            request.setPackageType(OtaAppType.ROM.getValue());
            deviceReportVersionExecutor.execute(() ->{
                otaTaskService.updateTaskState(request);
            });

            BeforeActivationDeviceVersion deviceVersion = new BeforeActivationDeviceVersion();
            deviceVersion.setDeviceSn(changeDeviceVersionDto.getSn());
            deviceVersion.setRomType(changeDeviceVersionDto.getAppType());
            deviceVersion.setRomVersionCode(changeDeviceVersionDto.getVersionCode());
            int result = beforeActivationDeviceVersionMapper.insertOrUpdateDeviceRomVersion(deviceVersion);
            if (result > 0) {
                response.setData(Boolean.TRUE);
                return response;
            }
        } catch (Exception e) {
            log.error("激活前上报版本信息报错:[{}]", JsonUtils.toJson(changeDeviceVersionDto), e);
        }
        response.setData(Boolean.FALSE);
        return response;
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> getBatchDeviceNewestRomVersionFromDatabase(List<String> deviceSnList) {
        ServiceResponse<List<DeviceVersionInfoDTO>> response = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceSnList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        List<DeviceVersionInfoDTO> list = new ArrayList<>();
        //分别查询激活前和激活后的设备rom版本信息,如果存在交集，则以修改时间最新的为准
        List<DeviceVersion> afterActivationRomVersionList = deviceRomVersionMapper.getBatchDeviceRomVersion(deviceSnList);
        List<BeforeActivationDeviceVersion> beforeActivationRomVersionList = beforeActivationDeviceVersionMapper.getBatchBeforeActivationDeviceRomVersion(deviceSnList);
        if (CollectionUtils.isEmpty(afterActivationRomVersionList) && CollectionUtils.isEmpty(beforeActivationRomVersionList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        if (CollectionUtils.isEmpty(beforeActivationRomVersionList)) {
            List<DeviceVersionInfoDTO> afterList = afterActivationRomVersionList.stream().map(deviceVersion -> {
                DeviceVersionInfoDTO deviceVersionResponse = new DeviceVersionInfoDTO();
                BeanUtils.copyProperties(deviceVersion, deviceVersionResponse);
                return deviceVersionResponse;
            }).collect(Collectors.toList());
            response.setData(afterList);
            return response;
        }
        if (CollectionUtils.isEmpty(afterActivationRomVersionList)) {
            List<DeviceVersionInfoDTO> beforeList = beforeActivationRomVersionList.stream().map(beforeActivationDeviceVersion -> {
                DeviceVersionInfoDTO deviceVersionResponse = new DeviceVersionInfoDTO();
                BeanUtils.copyProperties(beforeActivationDeviceVersion, deviceVersionResponse);
                deviceVersionResponse.setAppType(beforeActivationDeviceVersion.getRomType());
                deviceVersionResponse.setVersionCode(beforeActivationDeviceVersion.getRomVersionCode());
                return deviceVersionResponse;
            }).collect(Collectors.toList());
            response.setData(beforeList);
            return response;
        }
        List<String> afterActivationDeviceSnList = afterActivationRomVersionList.stream().map(DeviceVersion::getDeviceSn).collect(Collectors.toList());
        List<String> beforeActivationDeviceSnList = beforeActivationRomVersionList.stream().map(BeforeActivationDeviceVersion::getDeviceSn).collect(Collectors.toList());
        List<String> repeatDeviceSnList = beforeActivationDeviceSnList.stream().filter(deviceSn -> afterActivationDeviceSnList.contains(deviceSn)).collect(Collectors.toList());
        Map<String, DeviceVersion> afterActivationDeviceSnMap = afterActivationRomVersionList.stream().collect(Collectors.toMap(DeviceVersion::getDeviceSn, Function
            .identity()));
        Map<String, BeforeActivationDeviceVersion> beforeActivationDeviceSnMap = beforeActivationRomVersionList.stream().collect(Collectors.toMap(BeforeActivationDeviceVersion::getDeviceSn, Function.identity()));
        if (CollectionUtils.isNotEmpty(repeatDeviceSnList)) {
            //将三部分的数据合起来
            //afterActivationDeviceSnList过滤repeatDeviceSn
            List<String> afterActivationDeviceSnReduceList = afterActivationDeviceSnList.stream().filter(deviceSn -> !repeatDeviceSnList.contains(deviceSn)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(afterActivationDeviceSnReduceList)) {
                afterActivationDeviceSnReduceList.stream().forEach(deviceSn -> {
                    DeviceVersion deviceVersion = afterActivationDeviceSnMap.get(deviceSn);
                    if (deviceVersion != null) {
                        DeviceVersionInfoDTO afterDeviceVersionResponse = new DeviceVersionInfoDTO();
                        BeanUtils.copyProperties(deviceVersion, afterDeviceVersionResponse);
                        list.add(afterDeviceVersionResponse);
                    }
                });
            }
            //beforeActivationDeviceSnList过滤repeatDeviceSn
            List<String> beforeActivationDeviceSnReduceList = beforeActivationDeviceSnList.stream().filter(deviceSn -> !repeatDeviceSnList.contains(deviceSn)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(beforeActivationDeviceSnReduceList)) {
                beforeActivationDeviceSnReduceList.stream().forEach(deviceSn -> {
                    BeforeActivationDeviceVersion beforeActivationDeviceVersion = beforeActivationDeviceSnMap.get(deviceSn);
                    if (beforeActivationDeviceVersion != null) {
                        DeviceVersionInfoDTO beforeDeviceVersionResponse = new DeviceVersionInfoDTO();
                        BeanUtils.copyProperties(beforeActivationDeviceVersion, beforeDeviceVersionResponse);
                        beforeDeviceVersionResponse.setAppType(beforeActivationDeviceVersion.getRomType());
                        beforeDeviceVersionResponse.setVersionCode(beforeActivationDeviceVersion.getRomVersionCode());
                        list.add(beforeDeviceVersionResponse);
                    }
                });
            }
            repeatDeviceSnList.stream().forEach(deviceSn -> {
                DeviceVersion deviceVersion = afterActivationDeviceSnMap.get(deviceSn);
                BeforeActivationDeviceVersion beforeActivationDeviceVersion = beforeActivationDeviceSnMap.get(deviceSn);
                if (deviceVersion != null && beforeActivationDeviceVersion != null) {
                    DeviceVersionInfoDTO repeatDeviceVersionResponse = new DeviceVersionInfoDTO();
                    if (deviceVersion.getGmtModify().getTime() > beforeActivationDeviceVersion.getGmtModify().getTime()) {
                        BeanUtils.copyProperties(deviceVersion, repeatDeviceVersionResponse);
                    } else {
                        BeanUtils.copyProperties(beforeActivationDeviceVersion, repeatDeviceVersionResponse);
                        repeatDeviceVersionResponse.setAppType(beforeActivationDeviceVersion.getRomType());
                        repeatDeviceVersionResponse.setVersionCode(beforeActivationDeviceVersion.getRomVersionCode());
                    }
                    list.add(repeatDeviceVersionResponse);
                }
            });
        } else {
            afterActivationDeviceSnList.stream().forEach(deviceSn -> {
                DeviceVersion deviceVersion = afterActivationDeviceSnMap.get(deviceSn);
                if (deviceVersion != null) {
                    DeviceVersionInfoDTO afterDeviceVersionResponse = new DeviceVersionInfoDTO();
                    BeanUtils.copyProperties(deviceVersion, afterDeviceVersionResponse);
                    list.add(afterDeviceVersionResponse);
                }
            });
            beforeActivationDeviceSnList.stream().forEach(deviceSn -> {
                BeforeActivationDeviceVersion beforeActivationDeviceVersion = beforeActivationDeviceSnMap.get(deviceSn);
                if (beforeActivationDeviceVersion != null) {
                    DeviceVersionInfoDTO beforeDeviceVersionResponse = new DeviceVersionInfoDTO();
                    BeanUtils.copyProperties(beforeActivationDeviceVersion, beforeDeviceVersionResponse);
                    beforeDeviceVersionResponse.setAppType(beforeActivationDeviceVersion.getRomType());
                    beforeDeviceVersionResponse.setVersionCode(beforeActivationDeviceVersion.getRomVersionCode());
                    list.add(beforeDeviceVersionResponse);
                }
            });
        }
        response.setData(list);
        return response;
    }

    @Override
    public ServiceResponse<List<StockDeviceVersionDTO>> getBatchStockDeviceVersionFromDatabase(List<String> deviceSnList) {
        ServiceResponse<List<StockDeviceVersionDTO>> response = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(deviceSnList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        List<StockDeviceVersion> stockDeviceVersionList = stockDeviceVersionMapper.getBatchStockDeviceVersion(deviceSnList);
        if (CollectionUtils.isEmpty(stockDeviceVersionList)) {
            response.setData(Collections.emptyList());
            return response;
        }
        List<StockDeviceVersionDTO> stockDeviceVersionResponseList = stockDeviceVersionList.stream().map(stockDeviceVersion -> {
            StockDeviceVersionDTO stockDeviceVersionResponse = new StockDeviceVersionDTO();
            BeanUtils.copyProperties(stockDeviceVersion, stockDeviceVersionResponse);
            return stockDeviceVersionResponse;
        }).collect(Collectors.toList());
        response.setData(stockDeviceVersionResponseList);
        return response;
    }

    @Override
    public ServiceResponse<List<DeviceVersionInfoDTO>> findDeviceVersionByOrgListAndVersion(QueryDeviceVersionDTO request) {
        BizAssert.notNull(request, "request must not be null");
        BizAssert.notNull(request.getAppType(), "appType must not be null");
        BizAssert.notNull(request.getVersionCode(), "versionCode must not be null");
        BizAssert.notNull(request.getRomFlag(), "romFlag must not be null");
        ServiceResponse<List<DeviceVersionInfoDTO>> response = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(request.getOrgIdList())) {
            response.setData(Collections.EMPTY_LIST);
            return response;
        }
        List<DeviceVersion> deviceVersionList = new ArrayList<>();
        if (request.getRomFlag()) {
            if (request.getOsFlag() == null || request.getOsFlag()) {
                deviceVersionList = deviceRomVersionMapper.findDeviceOsVersionByOrgIdListAndVersion(request.getOrgIdList(), request.getAppType(), request.getVersionCode());
            } else {
                deviceVersionList = deviceRomVersionMapper.findDeviceRomVersionByOrgIdListAndVersion(request.getOrgIdList(), request.getAppType(), request.getVersionCode(), request.getApkAppType());
            }
        } else {
            deviceVersionList = deviceApkVersionMapper.findDeviceApkVersionByOrgIdListAndVersion(request.getOrgIdList(), request.getAppType(), request.getVersionCode());
        }
        if (CollectionUtils.isEmpty(deviceVersionList)) {
            response.setData(Collections.EMPTY_LIST);
            return response;
        }
        //可能设备已解绑或激活到别的机构，所以需要过滤
        List<String> deviceSnList = deviceVersionList.stream().map(DeviceVersion::getDeviceSn).collect(Collectors.toList());
        List<Device> deviceList = deviceMapper.listByDeviceSns(deviceSnList);
        Set<String> correctDeviceSnList = deviceList.stream().filter(device -> request.getOrgIdList().contains(device.getOrgId())).map(Device::getDeviceSn).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(correctDeviceSnList)) {
            response.setData(Collections.EMPTY_LIST);
            return response;
        } else {
            List<DeviceVersionInfoDTO> deviceVersionResponseList = deviceVersionList.stream()
                    .filter(deviceVersion -> correctDeviceSnList.contains(deviceVersion.getDeviceSn())).map(deviceVersion -> {
                        DeviceVersionInfoDTO deviceVersionResponse = new DeviceVersionInfoDTO();
                        BeanUtils.copyProperties(deviceVersion, deviceVersionResponse);
                        return deviceVersionResponse;
                    }).collect(Collectors.toList());
            response.setData(deviceVersionResponseList);
        }
        return response;
    }

    @Override
    public Boolean changeLatchDeviceVersion(ChangeLatchDeviceVersionDto dto) {
        BizAssert.notNull(dto, "dto must not be null");
        BizAssert.notNull(dto.getOrgId(), "orgId must not be null");
        BizAssert.notNull(dto.getDeviceId(), "deviceId must not be null");
        BizAssert.notBlank(dto.getDeviceSn(), "deviceSn must not be blank");
        BizAssert.notNull(dto.getAppType(), "appType must not be null");
        BizAssert.notNull(dto.getAppVersion(), "appVersion must not be null");
        BizAssert.notNull(dto.getRomType(), "romType must not be null");
        BizAssert.notNull(dto.getRomVersion(), "romVersion must not be null");
        BizAssert.notNull(dto.getLockBoard3861LVersion(), "3861LVersion must not be null");
        BizAssert.notNull(dto.getLockBoard804Version(), "804Version must not be null");
        BizAssert.notNull(dto.getLockBoard805Version(), "805Version must not be null");
        BizAssert.notNull(dto.getModelVersion(), "modelVersion must not be null");
        BizAssert.notNull(dto.getLockVersion(), "lockVersion must not be null");

        ChangeDeviceAppVersionDTO changeDeviceRomVersionDTO = new ChangeDeviceAppVersionDTO();
        changeDeviceRomVersionDTO.setOrgId(dto.getOrgId());
        changeDeviceRomVersionDTO.setDeviceId(dto.getDeviceId());
        changeDeviceRomVersionDTO.setSn(dto.getDeviceSn());
        changeDeviceRomVersionDTO.setAppType(dto.getRomType());
        changeDeviceRomVersionDTO.setVersionCode(dto.getRomVersion());
        changeDeviceRomVersion(changeDeviceRomVersionDTO);

        ChangeDeviceAppVersionDTO changeDeviceAppVersionDTO = new ChangeDeviceAppVersionDTO();
        changeDeviceAppVersionDTO.setOrgId(dto.getOrgId());
        changeDeviceAppVersionDTO.setDeviceId(dto.getDeviceId());
        changeDeviceAppVersionDTO.setSn(dto.getDeviceSn());
        changeDeviceAppVersionDTO.setAppType(dto.getAppType());
        changeDeviceAppVersionDTO.setVersionCode(dto.getAppVersion());
        changeDeviceAppVersion(changeDeviceAppVersionDTO);
        return Boolean.TRUE;
    }

    private DeviceVersion buildDeviceVersion(DeviceVersion deviceVersion, ChangeDeviceAppVersionDTO changeDeviceVersionDto) {
        deviceVersion.setOrgId(changeDeviceVersionDto.getOrgId());
        deviceVersion.setDeviceId(changeDeviceVersionDto.getDeviceId());
        deviceVersion.setDeviceSn(changeDeviceVersionDto.getSn());
        deviceVersion.setAppType(changeDeviceVersionDto.getAppType());
        deviceVersion.setVersionCode(changeDeviceVersionDto.getVersionCode());
        return deviceVersion;
    }

    /**
     * 更新版本信息
     * 规则如下：
     * 若发生机构ID 设备ID apptype appverion任何一项发送变更则进行修改
     * 若上报信息未发生变更：
     *  判断当日是否有上报
     *      若有则不更新
     *      若当日未上报 则继续判断当前时段是否为当前设备ID所对应时间段，是则更新，不是则打印日志不做任何处理
     *
     *
     * @param changeDeviceVersionDto
     * @param deviceVersion
     */
    private void updDeviceApkVersionIfNeed(ChangeDeviceAppVersionDTO changeDeviceVersionDto, DeviceVersion deviceVersion){
        //机构ID 设备ID apptype appverion任何一项发送变更则进行修改
        if (!changeDeviceVersionDto.getOrgId().equals(deviceVersion.getOrgId())
            || !changeDeviceVersionDto.getDeviceId().equals(deviceVersion.getDeviceId())
            || !changeDeviceVersionDto.getAppType().equals(deviceVersion.getAppType())
            || !changeDeviceVersionDto.getVersionCode().equals(deviceVersion.getVersionCode())){
            //构造并修改
            deviceVersion = buildDeviceVersion(deviceVersion, changeDeviceVersionDto);
            deviceApkVersionMapper.updateDeviceApkVersionByDeviceSn(deviceVersion);
        }else {//若未发生变更则判断是否需要更新修改时间
            Date today = new Date();
            //判断当日是否更新过上报时间 若更新过则不再更新
            SimpleDateFormat ddSf = new SimpleDateFormat("dd");
            Integer todayDD = Integer.parseInt(ddSf.format(today));
            Integer versionModifyDD = Integer.parseInt(ddSf.format(deviceVersion.getGmtModify()));
            //当日已更新 不需更新
            if (todayDD.equals(versionModifyDD)){
                log.info("机构ID:{},设备SN:{},今日已更新上报时间，无需更新",deviceVersion.getOrgId(), deviceVersion.getDeviceSn());
            }else {//若当日未更新 则判断当前是否在更新时段
                SimpleDateFormat hourSf = new SimpleDateFormat("HH");
                Integer todayHour = Integer.parseInt(hourSf.format(today));
                //如果当前小时数匹配上设备ID计算后取模数 则继续往下操作
                if (todayHour.equals(calculateMod(changeDeviceVersionDto.getDeviceId()))){
                    deviceVersion.setGmtCreate(today);
                    deviceApkVersionMapper.updateDeviceApkVersionByDeviceSn(deviceVersion);
                }
            }
        }

    }


    /**
     * 更新版本信息
     * 规则如下：
     * 若发生机构ID 设备ID apptype appverion任何一项发送变更则进行修改
     * 若上报信息未发生变更：
     *  判断当日是否有上报
     *      若有则不更新
     *      若当日未上报 则继续判断当前时段是否为当前设备ID所对应时间段，是则更新，不是则打印日志不做任何处理
     *
     *
     * @param changeDeviceVersionDto
     * @param deviceVersion
     */
    private void updDeviceRomVersionIfNeed(ChangeDeviceAppVersionDTO changeDeviceVersionDto, DeviceVersion deviceVersion){
        //机构ID 设备ID apptype appverion任何一项发送变更则进行修改
        if (!changeDeviceVersionDto.getOrgId().equals(deviceVersion.getOrgId())
            || !changeDeviceVersionDto.getDeviceId().equals(deviceVersion.getDeviceId())
            || !changeDeviceVersionDto.getAppType().equals(deviceVersion.getAppType())
            || !changeDeviceVersionDto.getVersionCode().equals(deviceVersion.getVersionCode())){
            //构造并修改
            deviceVersion = buildDeviceVersion(deviceVersion, changeDeviceVersionDto);
            deviceRomVersionMapper.updateDeviceRomVersionByDeviceSn(deviceVersion);
        }else {//若未发生变更则判断是否需要更新修改时间
            Date today = new Date();
            //判断当日是否更新过上报时间 若更新过则不再更新
            SimpleDateFormat ddSf = new SimpleDateFormat("dd");
            Integer todayDD = Integer.parseInt(ddSf.format(today));
            Integer versionModifyDD = Integer.parseInt(ddSf.format(deviceVersion.getGmtModify()));
            //当日已更新 不需更新
            if (todayDD.equals(versionModifyDD)){
                log.info("机构ID:{},设备SN:{},今日已更新上报时间，无需更新",deviceVersion.getOrgId(), deviceVersion.getDeviceSn());
            }else {//若当日未更新 则判断当前是否在更新时段
                SimpleDateFormat hourSf = new SimpleDateFormat("HH");
                Integer todayHour = Integer.parseInt(hourSf.format(today));
                //如果当前小时数匹配上设备ID计算后取模数 则继续往下操作
                if (todayHour.equals(calculateMod(changeDeviceVersionDto.getDeviceId()))){
                    deviceVersion.setGmtCreate(today);
                    deviceRomVersionMapper.updateDeviceRomVersionByDeviceSn(deviceVersion);
                }
            }
        }

    }

    /**
     * 计算ID的模值
     * 设备ID向右移33位后得到时间戳部分数据 对24取模
     *
     * @param deviceId
     * @return
     */
    private Integer calculateMod(Long deviceId){
        Long rValue = deviceId >> ID_MOVE_FOR_GET_TIME;
        Long resultLValue = rValue%24;
        return Integer.parseInt(String.valueOf(resultLValue));
    }
}