package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.PeopleNumberStatistic;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 大华摄像头设备人数统计
 *
 * <AUTHOR>
 */

@Mapper
public interface PeopleNumberStatisticMapper {

    /**
     * 根据设备Sn查询设备人员数量
     *
     * @param orgId    机构Id
     * @param deviceSn 设备sn
     * @return
     */
    PeopleNumberStatistic getByOrgIdAndDeviceSn(@Param("orgId") Long orgId,
        @Param("deviceSn") String deviceSn);

    /**
     * 新增设备人数统计信息
     *
     * @param peopleNumberStatistic
     * @return
     */
    int insert(PeopleNumberStatistic peopleNumberStatistic);

    /**
     * 更新设备人数统计信息
     *
     * @param peopleNumberStatistic
     * @return
     */
    int update(PeopleNumberStatistic peopleNumberStatistic);

    /**
     * 根据设备sn列表查询摄像头人数统计信息
     *
     * @param orgId     机构Id
     * @param deviceSns 设备sn列表
     * @return
     */
    List<PeopleNumberStatistic> listByOrgIdAndDeviceSns(@Param("orgId") Long orgId,
        @Param("deviceSns") List<String> deviceSns);

    List<PeopleNumberStatistic> listByOrgIdAndDeviceIds(@Param("orgId") Long orgId,
        @Param("deviceIds") List<Long> deviceIds);
}
