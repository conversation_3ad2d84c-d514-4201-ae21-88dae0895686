package com.moredian.magicube.device.manager;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.device.DeviceWhiteListDTO;
import java.util.List;

/**
 * @Author:dongchao
 * @Description: 测温设备白名单
 * @Date: 2020/2/24 22:53
 */

public interface DeviceWhiteListManager {

    /**
     * 插入
     *
     * @param dto
     * @return
     */
    ServiceResponse insert(DeviceWhiteListDTO dto);

    /**
     * 批量插入
     *
     * @param dto
     * @return
     */
    ServiceResponse insertBatch(List<DeviceWhiteListDTO> dto);

    /**
     * 根据条件查询
     *
     * @param dto
     * @return
     */
    ServiceResponse<List<DeviceWhiteListDTO>> getDeviceWhiteList(DeviceWhiteListDTO dto);

    /**
     * @param deviceSn
     * @return
     */
    ServiceResponse unBind(String deviceSn);


    /**
     * 绑定
     *
     * @param dto
     * @return
     */
    ServiceResponse bind(DeviceWhiteListDTO dto);

    /**
     * 判断该机构下面是否有白名单设备
     *
     * @param orgId
     * @return
     */
    ServiceResponse<Boolean> hasAvailableDevice(Long orgId);
}
