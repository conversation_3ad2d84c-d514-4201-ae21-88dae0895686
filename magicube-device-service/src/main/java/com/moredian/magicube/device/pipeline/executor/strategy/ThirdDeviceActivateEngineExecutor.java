package com.moredian.magicube.device.pipeline.executor.strategy;

import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.executor.AbstractActivateDeviceEngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.TTLockDevicePipe;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 三方设备的激活流程
 *      1.通通锁激活
 * @create 2025-03-13 11:34
 */
@Component
public class ThirdDeviceActivateEngineExecutor extends AbstractActivateDeviceEngineExecutor {

    @Resource
    private TTLockDevicePipe ttLockDevicePipe;

    @Override
    public void afterPropertiesSet() throws Exception {
//        pipeline.addPipe(commonActivateCheckPipe);
//        pipeline.addPipe(checkDeviceAuthorizePipe);
        pipeline.addPipe(insertDevicePipe);
        pipeline.addPipe(insertDeviceLogPipe);
//        pipeline.addPipe(updateDeviceWhitePipe);
        pipeline.addPipe(ttLockDevicePipe);
        pipeline.addPipe(monitorDataPipe);
        pipeline.addPipe(publishMsgPipe);
        pipeline.addPipe(buildResultPipe);
    }

    @Override
    protected void validCustomParameter(ActivateDeviceDTO activateDeviceDTO) {

    }

    @Override
    protected String setKey(ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext activateDeviceContext) {
        return "ThirdDeviceActivate_" + activateDeviceDTO.getDeviceSn();
    }

    @Override
    public Integer getType() {
        return DeviceActivateEngineEnums.THIRD_DEVICE_ACTIVATE.getCode();
    }
}
