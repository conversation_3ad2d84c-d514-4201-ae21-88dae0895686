package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.dto.device.RebootDTO;
import com.moredian.magicube.device.dto.device.TransferDTO;
import com.moredian.magicube.device.manager.DeviceIotManager;
import com.moredian.magicube.device.service.DeviceIotService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class DeviceIotServiceImpl implements DeviceIotService {

    @Autowired
    private DeviceIotManager deviceIotManager;

    @Override
    public ServiceResponse<DeviceStatusDTO> getStatusByDeviceSn(String deviceSn) {
        ServiceResponse<DeviceStatusDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceIotManager.getStatusByDeviceSn(deviceSn));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DeviceStatusDTO>> listDeviceStateByDeviceSns(
        List<String> deviceSns) {
        ServiceResponse<List<DeviceStatusDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        serviceResponse.setData(deviceIotManager.listDeviceStateByDeviceSns(deviceSns));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceStatusDTO> reboot(RebootDTO dto) {
        ServiceResponse<DeviceStatusDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceIotManager.reboot(dto));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DeviceStatusDTO> transfer(TransferDTO dto) {
        ServiceResponse<DeviceStatusDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceIotManager.transfer(dto));
        return serviceResponse;
    }
}