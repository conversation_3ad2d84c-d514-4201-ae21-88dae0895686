package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceCompositeItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备组条目
 *
 * <AUTHOR>
 */

@Mapper
public interface DeviceCompositeItemMapper {

    /**
     * 创建设备分组条目信息
     *
     * @param deviceCompositeItem 设备分组条目信息
     */
    void insert(DeviceCompositeItem deviceCompositeItem);

    /**
     * 批量创建设备分组条目信息
     *
     * @param list 设备分组条目信息列表
     */
    void batchInsert(@Param("list") List<DeviceCompositeItem> list);

    /**
     * 查询设备分组类目
     *
     * @param orgId             机构号
     * @param deviceCompositeId 设备分组Id
     * @return
     */
    List<DeviceCompositeItem> listByCompositeId(@Param("orgId") Long orgId, @Param("deviceCompositeId") Long deviceCompositeId);

    /**
     * 根据机构Id和业务类型查询设备分组类目
     *
     * @param orgId   机构号
     * @param bizType 业务类型
     * @return
     */
    List<DeviceCompositeItem> listByOrgId(@Param("orgId") Long orgId, @Param("bizType") Integer bizType);

    /**
     * 根据机构号和设备分组Id删除设备分组类目
     *
     * @param orgId             机构号
     * @param deviceCompositeId 设备分组Id
     */
    void deleteByOrgIdAndDeviceCompositeId(@Param("orgId") Long orgId, @Param("deviceCompositeId") Long deviceCompositeId);

    /**
     * 根据deviceIds查询设备分组类目
     *
     * @param orgId     机构号
     * @param deviceIds 设备Id列表
     * @param bizType   业务类型
     * @return
     */
    List<DeviceCompositeItem> listByDeviceIds(@Param("orgId") Long orgId, @Param("deviceIds") List<Long> deviceIds, @Param("bizType") Integer bizType);

    /**
     * 根据设备分组Id列表查询设备分组条目信息列表
     *
     * @param orgId              机构号
     * @param deviceCompositeIds 设备分组Id列表
     * @return
     */
    List<DeviceCompositeItem> listByOrgIdAndDeviceCompositeIds(@Param("orgId") Long orgId, @Param("deviceCompositeIds") List<Long> deviceCompositeIds);

    /**
     * 根据机构和设备id删除设备分组类目
     *
     * @param orgId    机构号
     * @param deviceId 设备Id
     * @param bizType  业务类型
     */
    void deleteByDeviceId(@Param("orgId") Long orgId, @Param("deviceId") Long deviceId, @Param("bizType") Integer bizType);

    /**
     * 删除。deviceId可以为null，当为null的时候是整个组删除
     * （物理删除，用户全量更新删除，或者只是删除某条关系）
     *
     * @param orgId
     * @param deviceCompositeId
     * @param deviceId
     */
    void removeItem(@Param("orgId") Long orgId, @Param("deviceCompositeId") Long deviceCompositeId, @Param("deviceId") Long deviceId,@Param("status") Long status);

    /**
     * 批量查询组下面的设备情况
     *
     * @param orgId              组织 ID
     * @param deviceCompositeIds 设备复合 ID
     * @return {@link List }<{@link DeviceCompositeItem }>
     */
    List<DeviceCompositeItem> listByCompositeIds(@Param("orgId") Long orgId, @Param("deviceCompositeIds") List<Long> deviceCompositeIds);

    /**
     * 删除(用于删除组的时候，被动删除设备)
     *
     * @param orgId             组织 ID
     * @param deviceCompositeId 设备复合 ID
     * @param deviceId          设备 ID
     * @param status            地位
     */
    void removeItemSoft(@Param("orgId") Long orgId, @Param("deviceCompositeId") Long deviceCompositeId, @Param("deviceId") Long deviceId,@Param("status") Long status);

    List<DeviceCompositeItem> getItemListByCompositeIdWithDeleteBatch(@Param("orgId") Long orgId,
        @Param("deviceCompositeIds") List<Long> deviceCompositeIds);

    /**
     * 根据设备组查询设备元素，包括删除的
     *
     * @param orgId       组织 ID
     * @param deviceCompositeIds 复合 ID
     * @return {@link List }<{@link DeviceCompositeItem }>
     */
    List<DeviceCompositeItem> getItemListByCompositeIdListWithDelete(@Param("orgId") Long orgId,
        @Param("deviceCompositeIds") List<Long> deviceCompositeIds);
}
