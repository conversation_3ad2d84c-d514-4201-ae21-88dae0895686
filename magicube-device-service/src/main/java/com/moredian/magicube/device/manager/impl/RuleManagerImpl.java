package com.moredian.magicube.device.manager.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.dao.entity.RuleTemplate;
import com.moredian.magicube.device.dao.mapper.RuleMapper;
import com.moredian.magicube.device.enums.RuleDeviceTypeEnum;
import com.moredian.magicube.device.enums.RuleTypeEnum;
import com.moredian.magicube.device.manager.DeviceRuleRelationManager;
import com.moredian.magicube.device.manager.RuleManager;
import com.moredian.magicube.device.manager.RuleTemplateManager;
import com.moredian.magicube.device.manager.SpaceRuleRelationManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleManagerImpl.java, v 1.0 Exp $
 */
@Component
@Slf4j
public class RuleManagerImpl extends ServiceImpl<RuleMapper, Rule> implements RuleManager {

    @Resource
    private RuleMapper ruleMapper;
    @Resource
    private SpaceRuleRelationManager spaceRuleRelationManager;
    @Resource
    private DeviceRuleRelationManager deviceRuleRelationManager;
    @Resource
    private RuleTemplateManager ruleTemplateManager;
    @SI
    private IdgeneratorService idgeneratorService;

    //展示空间绑定的规则
    private static final Integer SHOW_SPACE_RULE = 1;

    @Override
    public List<Rule> getRuleByCondition(Long orgId, Integer spaceType, Long deviceId,
        Integer modeType, Long ruleId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        return ruleMapper.getRuleList(orgId, modeType, spaceType,
            Objects.nonNull(ruleId) ? Lists.newArrayList(ruleId) : new ArrayList<>());
    }

    @Override
    public List<Long> getTemplateIdByOrgId(Long orgId) {
        BizAssert.notNull(orgId, "orgId must not be null");

        return ruleMapper.getRuleTemplateIdByOrgId(orgId);
    }

    @Override
    public Rule getByRuleId(Long ruleId) {
        BizAssert.notNull(ruleId, "ruleId must not be null");
        return ruleMapper.getByRuleId(ruleId);
    }

    @Override
    public void batchInsert(Long orgId, List<RuleTemplate> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return;
        }
        BatchIdDto batchIdDto = idgeneratorService.getNextIdBatchBytypeName(
                BeanConstants.RULE, templateList.size()).pickDataThrowException();

        //3. 初始化机构未关联的规则模版
        List<Rule> ruleList = templateList.stream().map(
                defaultTemplate -> new Rule().setOrgId(orgId).setRuleId(batchIdDto.nextId()).setRuleName(defaultTemplate.getTemplateName())
                        .setRuleType(RuleTypeEnum.DEFAULT.getCode()).setModeType(defaultTemplate.getModeType())
                        .setDescription(defaultTemplate.getDescription())
                        .setTriggerType(defaultTemplate.getTriggerType()).setTriggerValue(defaultTemplate.getTriggerValue())
                        .setTemplateId(defaultTemplate.getRuleTemplateId())
                        .setDelFlag(YesNoFlag.NO.getValue())
                        .setDeviceType(JSONUtil.toJsonStr(RuleDeviceTypeEnum.SUPPORT_ALL_DEVICE_TYPES))
        ).collect(Collectors.toList());
        ruleMapper.batchInsert(ruleList);
    }

    @Override
    public Long insert(Long orgId, RuleTemplate defaultTemplate,
        List<Integer> deviceTypes, String ruleName, Integer spaceType, Boolean humanPriority) {
        if (defaultTemplate == null) {
            return null;
        }
        Long id = idgeneratorService.getNextIdByTypeName(
            "com.moredian.magicube.device.dao.entity.Rule").getData();
        Rule rule = new Rule().setOrgId(orgId).setRuleId(id)
            .setRuleName(ruleName)
            .setRuleType(RuleTypeEnum.DEFAULT.getCode()).setModeType(defaultTemplate.getModeType())
            .setDescription(defaultTemplate.getDescription())
            .setTriggerType(defaultTemplate.getTriggerType())
            .setSpaceType(spaceType)
            .setTriggerValue(defaultTemplate.getTriggerValue())
            .setTemplateId(defaultTemplate.getRuleTemplateId())
            .setDelFlag(YesNoFlag.NO.getValue())
            .setHumanPriority(humanPriority)
            .setDeviceType(CollectionUtils.isNotEmpty(deviceTypes) ? JSONUtil.toJsonStr(deviceTypes)
                : JSONUtil.toJsonStr(RuleDeviceTypeEnum.SUPPORT_ALL_DEVICE_TYPES))
            ;
        ruleMapper.batchInsert(Lists.newArrayList(rule));
        return id;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRuleAndDevice(Long orgId, Rule rule, List<Long> spaceIdList, List<Long> deviceIdList) {
        if (CollectionUtils.isEmpty(spaceIdList)) {
            rule.setSceneId("");
            rule.setRuleJson("");
        }
        ruleMapper.updateRule(orgId, rule);
        spaceRuleRelationManager.ruleRelateSpace(orgId, rule.getRuleId(), spaceIdList);
        deviceRuleRelationManager.ruleRelateDevice(orgId, rule.getRuleId(), deviceIdList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRule(Long orgId, Rule rule) {
        ruleMapper.updateRule(orgId, rule);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRuleAndDevice(Long orgId, Rule rule, List<Long> deviceIdList) {
        ruleMapper.updateRule(orgId, rule);
        deviceRuleRelationManager.ruleRelateDevice(orgId, rule.getRuleId(), deviceIdList);
    }

    @Override
    public Rule getRule(Long orgId, Long ruleId) {
        return ruleMapper.getRule(orgId, ruleId);
    }

    @Override
    public RuleTemplate getRuleTemplate(Long orgId, Long ruleId) {
        Rule rule = getRule(orgId, ruleId);
        return rule == null ? null : ruleTemplateManager.getTemplate(rule.getTemplateId());
    }
}
