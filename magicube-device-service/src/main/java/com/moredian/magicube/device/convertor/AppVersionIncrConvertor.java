package com.moredian.magicube.device.convertor;

import com.moredian.magicube.device.dto.version.AppVersionIncrDTO;
import com.moredian.ota.api.response.apk.OtaApkResponse;

/**
 * 差量升级包数据转换器
 *
 * <AUTHOR>
 */
public class AppVersionIncrConvertor {


    public static AppVersionIncrDTO otaApkResponseToAppVersionIncrDto(
        OtaApkResponse otaApkResponse) {
        if (otaApkResponse == null) {
            return null;
        }
        AppVersionIncrDTO appVersionIncrDTO = new AppVersionIncrDTO();
        appVersionIncrDTO.setAppVersionIncreId(otaApkResponse.getId());
        appVersionIncrDTO.setPreVersionCode(otaApkResponse.getIncrPreVersionCode());
        appVersionIncrDTO.setPackageUrl(otaApkResponse.getApkFileUrl());
        return appVersionIncrDTO;
    }
}