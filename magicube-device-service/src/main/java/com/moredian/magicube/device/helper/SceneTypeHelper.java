package com.moredian.magicube.device.helper;

import com.moredian.magicube.common.enums.AuthorizeAppEnum;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 场景工具类
 * @create 2025-03-18 17:28
 */
@Component
public class SceneTypeHelper {

    /**
     * 根据场景获取到对应关联的应用
     * @see AuthorizeAppEnum 执行的应用
     * @see SceneTypeEnums 场景类型
     * @return 根据场景转换的appCode，001,003,005......
     */
    public List<String> getAppCodeBySceneType(Integer sceneType) {
        List<String> appcode = new ArrayList<>();
        if (sceneType == null) {
            return appcode;
        }

        List<Integer> sceneTypeList = Arrays.stream(SceneTypeEnums.values())
                .map(SceneTypeEnums::getValue).collect(Collectors.toList());

        if (!sceneTypeList.contains(sceneType)) {
            return appcode;
        }

        if (sceneType.equals(SceneTypeEnums.STANDARD_MODEL.getValue())) {
            appcode.add(AuthorizeAppEnum.ATTENDANCE.getCode());
            appcode.add(AuthorizeAppEnum.VISITOR.getCode());
            appcode.add(AuthorizeAppEnum.FACEDOOR.getCode());
        }else if (sceneType.equals(SceneTypeEnums.MEETING_ROOM_MODEL.getValue())) {
            appcode.add(AuthorizeAppEnum.MEETING.getCode());
        }else if (sceneType.equals(SceneTypeEnums.CLASSROOM_MODEL.getValue())) {
            appcode.add(AuthorizeAppEnum.CLASSCARD.getCode());
        }else if (sceneType.equals(SceneTypeEnums.ELEVATOR_MODEL.getValue())) {
            appcode.add(AuthorizeAppEnum.LADDER.getCode());
        }else if (sceneType.equals(SceneTypeEnums.LOCKER_MODEL.getValue())) {
            appcode.add(AuthorizeAppEnum.CABINET.getCode());
        }
        return appcode;
    }


}
