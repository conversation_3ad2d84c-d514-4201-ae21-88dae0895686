package com.moredian.magicube.device.manager;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import java.util.List;

/**
 * 设备版本查询(从持久化数据中查)
 *@description:
 *@author: gongchang
 *@time: 2023-04-06 18:09
 *
 */
public interface DeviceVersionPersistenceClientManager {

    /**
     * 批量查询设备APK版本号(单批次查询最大2000)
     * @param orgId
     * @param deviceIds
     * @return
     */
    ServiceResponse<List<DeviceVersionDTO>> getBatchDeviceAppVersion(Long orgId, Long[] deviceIds);

    /**
     * 批量查询设备ROM版本号(单批次查询最大2000)
     * @param orgId
     * @param deviceIds
     * @return
     */
    ServiceResponse<List<DeviceVersionDTO>> getBatchDeviceRomVersion(Long orgId, Long[] deviceIds);

    /**
     * 批量获取设备当前APP、ROM版本信息及设备最新可用APP、ROM版本信息(单批次查询最大100)
     * @param orgId
     * @param deviceIds
     * @return
     */
    ServiceResponse<List<DeviceCurrVersionDTO>> getBatchDeviceVersion(Long orgId, Long[] deviceIds);

}
