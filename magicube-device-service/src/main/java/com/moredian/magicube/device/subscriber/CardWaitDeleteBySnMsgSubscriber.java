package com.moredian.magicube.device.subscriber;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.constant.RedisKeysConstant;
import com.moredian.magicube.common.enums.TransferEventType;
import com.moredian.magicube.common.model.msg.CardWaitDeleteBySnMsg;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

/**
 * 卡片待删除消息监听
 *
 * <AUTHOR>
 * @date 2024/9/30 10:00
 */
@Slf4j
@Component
public class CardWaitDeleteBySnMsgSubscriber {

    @Resource
    private RedissonLockComponent redissonLockComponent;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private SendMQTTBase sendMQTTBase;

    @Resource
    private DeviceManager deviceManager;

    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Subscribe
    public void subCardWaitDeleteMsg(CardWaitDeleteBySnMsg msg) {
        log.info("收到待删除的卡片消息: {}", JsonUtils.toJson(msg));
        // 设置key的序列化方式为StringRedisSerializer
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        // 设置value的序列化方式为StringRedisSerializer
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        // 存入redis中
        // 获取分布式锁，同一时间设备只能要么拉取，要么塞入待删除的卡号数据
        // 判断设备是否支持刷卡，不支持刷卡要过滤
        // 可刷卡的设备类型列表
        List<Integer> enableType = deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.ENABLE_CARD_DEVICE_TYPE_LIST);
        Device device = deviceManager.getByDeviceSn(msg.getDeviceSn());
        if (device != null) {
            if (!enableType.contains(device.getDeviceType())) {
                log.info("不支持刷卡的设备,无需把卡片从设备中移除，deviceSN={}",
                    device.getDeviceSn());
                return;
            }
            try {
                if (redissonLockComponent.acquire(
                    RedisKeysConstant.getKey(RedisKeysConstant.DEVICE_DELETE_CARD_NO_LOCK,
                        msg.getDeviceSn()),
                    3L, 30L)) {
                    // 2、获取key
                    String key = RedisKeysConstant
                        .getKey(RedisKeysConstant.DEVICE_DELETE_CARD_NO_LIST,
                            msg.getDeviceSn());
                    // 3、把待删除的卡号塞入缓存中
                    for (String cardNo : msg.getCardNos()) {
                        redisTemplate.opsForZSet().add(key, cardNo, System.currentTimeMillis());
                    }
                    // 4、设置30天的过期时间
                    redisTemplate.expire(key, 30, TimeUnit.DAYS);
                    sendMQTTBase.doSwitchCardSyncSendMQTT(device, "CardWaitDeleteBySnMsg",
                        TransferEventType.DEVICE_CARD_NO_DELETE);
                } else {
                    log.error(
                        "待删除的卡号列表存入redis，获取分布式锁失败deviceSN={}", msg.getDeviceSn());
                }
            } catch (Exception e) {
                log.error(
                    "deviceSN={},待删除的卡号列表存入redis，获取分布式锁失败message:{}",
                    msg.getDeviceSn(), e.getMessage());
            } finally {
                redissonLockComponent.release(
                    RedisKeysConstant.getKey(RedisKeysConstant.DEVICE_DELETE_CARD_NO_LOCK,
                        msg.getDeviceSn()));
            }
        }
    }

}
