package com.moredian.magicube.device.manager.impl;

import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.filemanager.FileManager;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.dto.BatchIdDto;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.DeviceSubject;
import com.moredian.magicube.device.dao.entity.DeviceSubjectRelation;
import com.moredian.magicube.device.dao.entity.SpaceSubjectReleation;
import com.moredian.magicube.device.dao.mapper.DeviceSubjectMapper;
import com.moredian.magicube.device.dao.mapper.SpaceSubjectRelationMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.subject.DeviceSubjectDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceSubjectManager;
import com.moredian.magicube.device.manager.DeviceSubjectRelationManager;

import java.util.*;

import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.moredian.magicube.device.service.DeviceService;
import com.moredian.magicube.device.utils.UUIDValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 设备主题服务
 *
 * <AUTHOR>
 * @since 2023-08-28
 */

@Component
public class DeviceSubjectManagerImpl implements DeviceSubjectManager {

    @Autowired
    private DeviceSubjectMapper deviceSubjectMapper;

    @Autowired
    private DeviceSubjectRelationManager deviceSubjectRelationManager;

    @Autowired
    private SpaceSubjectRelationMapper spaceSubjectRelationMapper;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private FileManager fileManager;

    @SI
    private IdgeneratorService idgeneratorService;

    @SI
    private DeviceService deviceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(DeviceSubject deviceSubject) {
        BizAssert.notNull(deviceSubject.getOrgId(), "orgId must not be null");
        BizAssert.notNull(deviceSubject.getName(), "name must not be null");
        BizAssert.notNull(deviceSubject.getImgUrls(), "imgUrls must not be null");
        BizAssert.notNull(deviceSubject.getType(), "type must not be null");
        deviceSubject.setId(idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_TYPE)
            .pickDataThrowException());
        if (deviceSubject.getTemplateType() == null){
            deviceSubject.setTemplateType(1);
        }
        deviceSubjectMapper.insert(deviceSubject);
        return deviceSubject.getId();
    }

    @Override
    public List<DeviceSubject> batchInsert(List<DeviceSubject> deviceSubjectList) {
        if (CollectionUtils.isEmpty(deviceSubjectList)){
            return Collections.emptyList();
        }
        BatchIdDto batchIdDto = idgeneratorService.getNextIdBatchBytypeName(DeviceSubjectManagerImpl.class.getName(), deviceSubjectList.size()).pickDataThrowException();
        for (DeviceSubject deviceSubject : deviceSubjectList) {
            deviceSubject.setId(batchIdDto.nextId());
            if (deviceSubject.getTemplateType() == null){
                deviceSubject.setTemplateType(1);
            }
        }
        deviceSubjectMapper.batchInsert(deviceSubjectList);
        return deviceSubjectList;
    }

    @Override
    public DeviceSubject getById(Long orgId, Long id) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(id, "id must not be null");
        return deviceSubjectMapper.getById(orgId, id);
    }

    @Override
    public DeviceSubjectDTO getByOrgIdAndId(Long orgId, Long subjectId) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(subjectId, "id must not be null");
        DeviceSubject deviceSubject = this.getById(orgId, subjectId);
        if (deviceSubject == null) {
            return null;
        }
        DeviceSubjectDTO deviceSubjectDTO = new DeviceSubjectDTO();
        BeanUtils.copyProperties(deviceSubject, deviceSubjectDTO);

        List<Long> spaceIds = new ArrayList<>();
        List<SpaceSubjectReleation> spaceSubjectReleations = spaceSubjectRelationMapper.listByOrgIdAndSubjectIds(orgId, Collections.singletonList(subjectId));
        if (CollectionUtils.isNotEmpty(spaceSubjectReleations)){
            spaceIds = spaceSubjectReleations.stream().map(SpaceSubjectReleation::getSpaceId).distinct().collect(Collectors.toList());
            deviceSubjectDTO.setSpaceIds(spaceIds);
        }
        List<DeviceSubjectRelation> relations = deviceSubjectRelationManager
                .listByOrgIdAndSubjectIds(orgId, Lists.newArrayList(deviceSubject.getId()));
        Map<Long, DeviceInfoDTO> deviceIdToDeviceInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relations)) {
            List<Long> deviceIds = relations.stream().map(DeviceSubjectRelation::getDeviceId)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                List<DeviceInfoDTO> deviceInfoDTOS = deviceService
                        .listByIds( deviceIds).pickDataThrowException();
                List<DeviceInfoDTO> deviceInfoDTOSResult = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(deviceInfoDTOS)) {
                    for (DeviceInfoDTO deviceInfoDTO : deviceInfoDTOS) {
                        String path = deviceInfoDTO.getPath();
                        if (StringUtils.isNotBlank(path)) {
                            String[] split = path.split("/");
                            Set<Long> longSet = Arrays.stream(split)
                                    .map(Long::parseLong)
                                    .collect(Collectors.toSet());
                            ArrayList<Long> longs = new ArrayList<>(longSet);
                            longs.retainAll(spaceIds);
                            if (CollectionUtils.isEmpty(longs)) {
                                deviceInfoDTOSResult.add(deviceInfoDTO);
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(deviceInfoDTOSResult)) {
                    deviceIdToDeviceInfoMap = deviceInfoDTOSResult.stream()
                            .collect(Collectors.toMap(DeviceInfoDTO::getDeviceId, x -> x));
                }
            }
            List<DeviceInfoDTO> devices = new ArrayList<>();
            for (DeviceSubjectRelation relation : relations) {
                if (deviceIdToDeviceInfoMap.get(relation.getDeviceId()) != null) {
                    devices.add(deviceIdToDeviceInfoMap.get(relation.getDeviceId()));
                }
            }
            deviceSubjectDTO.setDevices(devices);
        }
        getPathUrl(deviceSubject, deviceSubjectDTO);
        return deviceSubjectDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(DeviceSubject deviceSubject) {
        BizAssert.notNull(deviceSubject.getOrgId(), "orgId must not be null");
        BizAssert.notNull(deviceSubject.getId(), "id must not be null");
        DeviceSubject ds = deviceSubjectMapper
            .getById(deviceSubject.getOrgId(), deviceSubject.getId());
        BizAssert.notNull(ds, DeviceErrorCode.DEVICE_SUBJECT_NOT_EXIST,
            DeviceErrorCode.DEVICE_SUBJECT_NOT_EXIST.getMessage());
        deviceSubjectMapper.update(deviceSubject);
        return true;
    }

    @Override
    public Integer countByOrgIdAndType(Long orgId, Integer type) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.notNull(type, "type must not be null");
        return deviceSubjectMapper.countByOrgIdAndType(orgId, type);
    }

    @Override
    public List<DeviceSubject> listByOrgIdAndIds(Long orgId, List<Long> ids) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(ids), "ids must not be null");
        return deviceSubjectMapper.listByOrgIdAndIds(orgId, ids);
    }

    @Override
    public List<DeviceSubject> listByCondition(DeviceSubject deviceSubject) {
        BizAssert.notNull(deviceSubject.getOrgId(), "orgId must not be null");
        return deviceSubjectMapper.listByCondition(deviceSubject);
    }

    /**
     * 根据条件分页查询设备主题信息列表
     *
     * @param deviceSubject 设备主题信息
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public List<DeviceSubject> listByConditionAndPage(DeviceSubject deviceSubject, Integer pageNo, Integer pageSize,List<Long> subjectIdList) {
        BizAssert.notNull(deviceSubject.getOrgId(), "orgId must not be null");
        BizAssert.notNull(pageNo, "pageNo must not be null");
        BizAssert.notNull(pageSize, "pageSize must not be null");
        pageNo = pageSize * (pageNo-1);
        return deviceSubjectMapper.listByPage(deviceSubject,pageNo,pageSize,subjectIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByOrgIdAndIds(Long orgId, List<Long> subjectIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(subjectIds), "subjectIds must not be null");
        deviceSubjectMapper.deleteByOrgIdAndIds(orgId, subjectIds);
        spaceSubjectRelationMapper.deleteByCondition(orgId,new ArrayList<>(), subjectIds);
        deviceSubjectRelationManager.deleteByOrgIdAndSubjectIds(orgId, subjectIds);
        return Boolean.TRUE;
    }

    /**
     * 批量删除设备主题
     *
     * @param deviceSubject 设备主题Id列表
     * @return
     */
    @Override
    public Integer countAll(DeviceSubject deviceSubject,List<Long> subjectIdList) {
        BizAssert.notNull(deviceSubject.getOrgId(), "orgId must not be null");
        Integer total = deviceSubjectMapper.countAll(deviceSubject,subjectIdList);
        return total;
    }

    /**
     * 更新屏幕老数据
     *
     * @param orgId
     * @return
     */
    @Transactional
    @Override
    public boolean updateOldScreenData(Long orgId,Integer type) {
        if (type==1){
            return false;
        }
        String name = "屏保";
        int i = 1;
        DeviceSubject deviceSubject = new DeviceSubject();
        deviceSubject.setOrgId(orgId);
        deviceSubject.setType(2);
        List<DeviceSubject> deviceSubjectsUpdate = new ArrayList<>();
        List<DeviceSubject> deviceSubjects = deviceSubjectMapper.listByCondition(deviceSubject);
        if (CollectionUtils.isNotEmpty(deviceSubjects)){
            for (DeviceSubject subject : deviceSubjects) {
                if (subject!=null && subject.getType() == 2 &&  StringUtils.isNotBlank(subject.getName()) && UUIDValidator.isValidUUID(subject.getName())){
                    subject.setName(name+i);
                    deviceSubjectsUpdate.add(subject);
                    i++;
                }
            }
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                CompletableFuture.runAsync(() -> {
                    for (DeviceSubject subject : deviceSubjectsUpdate) {
                        deviceSubjectMapper.update(subject);
                    }
                });
            }
        });
        return true;
    }

    private void getPathUrl(DeviceSubject deviceSubject, DeviceSubjectDTO deviceSubjectDTO) {
        if (StringUtils.isNotBlank(deviceSubject.getImgUrls())) {
            List<String> urls = new ArrayList<>();
            String[] imgUrls = deviceSubject.getImgUrls().split(",");
            for (String imgUrl : imgUrls) {
                urls.add(fileManager.getUrlByRelativePath(imgUrl));
            }
            deviceSubjectDTO.setImgUrls(urls);
        }
        if (StringUtils.isNotBlank(deviceSubject.getLogoImgUrl())) {
            deviceSubjectDTO
                    .setLogoImgUrl(fileManager.getUrlByRelativePath(deviceSubject.getLogoImgUrl()));
        }
    }
}
