<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.moredian.magicube.device.dao.mapper.AccessoryMapper">

  <resultMap id="baseResultMap" type="com.moredian.magicube.device.dao.entity.AccessoryInfo">
    <result column="accessory_id" property="accessoryId"/>
    <result column="org_id" property="orgId"/>
    <result column="accessory_name" property="accessoryName"/>
    <result column="accessory_sn" property="accessorySn"/>
    <result column="accessory_type" property="accessoryType"/>
    <result column="device_sn" property="deviceSn"/>
    <result column="device_id" property="deviceId"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
    <result column="match_status" property="matchStatus"/>
  </resultMap>

  <sql id="base_column_list">
    accessory_id,org_id,accessory_name,accessory_sn,accessory_type,device_sn,device_id,gmt_create,gmt_modify,match_status
  </sql>

  <insert id="addAccessory">
    INSERT INTO hive_device_accessories(<include refid="base_column_list"/>)
    VALUES(#{accessoryId},#{orgId},#{accessoryName},#{accessorySn},#{accessoryType},#{deviceSn},#{deviceId},NOW(3),NOW(3),#{matchStatus})
  </insert>

  <update id="updateGmtModifyById">
    UPDATE hive_device_accessories
    SET gmt_modify = NOW(3)
    WHERE accessory_id = #{accessoryId}
  </update>

  <delete id="deleteByCondition">
    DELETE
    FROM hive_device_accessories
    WHERE org_id = #{orgId}
      AND device_sn = #{deviceSn}
      AND accessory_sn = #{accessorySn}
      AND accessory_type = #{accessoryType}
  </delete>

  <delete id="deleteByOrgIdAndDeviceSn">
    DELETE
    FROM hive_device_accessories
    WHERE org_id = #{orgId}
      AND device_sn = #{deviceSn}
  </delete>

  <select id="findAccessoryInfoByCondition" resultMap="baseResultMap">
    SELECT
    <include refid="base_column_list"/>
    FROM hive_device_accessories
    <include refid="condition"/>
  </select>

  <sql id="condition">
    <where>
      <if test="orgId != null">
        AND org_id = #{orgId}
      </if>
      <if test="deviceId != null">
        AND device_id = #{deviceId}
      </if>
      <if test="deviceSn != null">
        AND device_sn = #{deviceSn}
      </if>
      <if test="accessorySn != null">
        AND accessory_sn = #{accessorySn}
      </if>
      <if test="accessoryType != null">
        AND accessory_type = #{accessoryType}
      </if>
      <if test="deviceSnList != null and deviceSnList.size() > 0">
        AND device_sn IN
        <foreach collection="deviceSnList" item="singleSn" open="(" close=")" separator=",">
          #{singleSn}
        </foreach>
      </if>
    </where>
  </sql>

</mapper>