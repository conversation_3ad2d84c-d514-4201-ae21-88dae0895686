<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceIotInvokeMessageMapper">
  <resultMap id="BaseResultMap" type="com.moredian.magicube.device.dao.entity.DeviceIotInvokeMessage">
    <!--@mbg.generated-->
    <!--@Table hive_device_iot_invoke_message-->
    <id column="message_id" jdbcType="BIGINT" property="messageId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="device_id" jdbcType="BIGINT" property="deviceId" />
    <result column="device_sn" jdbcType="VARCHAR" property="deviceSn" />
    <result column="message_type" jdbcType="INTEGER" property="messageType" />
    <result column="message_status" jdbcType="INTEGER" property="messageStatus" />
    <result column="params" jdbcType="LONGVARCHAR" property="params" />
    <result column="response" jdbcType="LONGVARCHAR" property="response" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modify" jdbcType="TIMESTAMP" property="gmtModify" />
  </resultMap>
  <sql id="Base_Column_List">
    message_id, org_id, device_id, device_sn, message_type, message_status, params, response,
    gmt_create, gmt_modify
  </sql>
</mapper>