package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceActiveState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备激活状态
 *
 * <AUTHOR>
 * @since 2024-11-21
 */

@Mapper
public interface DeviceActiveStateMapper {

    /**
     * 查询设备激活状态
     *
     * @param deviceSn 设备sn
     * @return
     */
    DeviceActiveState getByDeviceSn(String deviceSn);

    /**
     * 新增设备激活状态信息
     *
     * @param activeState 设备激活状态信息
     * @return
     */
    void insert(DeviceActiveState activeState);

    /**
     * 修改设备激活状态信息
     *
     * @param activeState 设备激活状态信息
     * @return
     */
    int update(DeviceActiveState activeState);

    /**
     * 更新设备绑定团队url
     *
     * @param activeState 设备激活状态信息
     * @return
     */
    int updateBindOrgUrl(DeviceActiveState activeState);
}
