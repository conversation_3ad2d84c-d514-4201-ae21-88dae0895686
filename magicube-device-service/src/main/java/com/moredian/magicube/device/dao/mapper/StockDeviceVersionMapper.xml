<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.StockDeviceVersionMapper">

    <resultMap id="baseResultMap" type="com.moredian.magicube.device.dao.entity.StockDeviceVersion">
        <result column="device_sn" property="deviceSn"/>
        <result column="app_type" property="appType"/>
        <result column="app_version_code" property="appVersionCode"/>
        <result column="rom_type" property="romType"/>
        <result column="rom_version_code" property="romVersionCode"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_columns">
        device_sn,app_type,app_version_code,rom_type,rom_version_code,gmt_create,gmt_modify
    </sql>

    <select id="getBatchStockDeviceVersion" resultMap="baseResultMap">
        SELECT <include refid="sql_columns"/>
        FROM hive_stock_device_version
        WHERE device_sn in
        <if test="deviceSnList != null and deviceSnList.size() > 0">
            <foreach collection="deviceSnList" index="index" item="deviceSn" open="(" separator="," close=")">
                #{deviceSn}
            </foreach>
        </if>
    </select>

    <insert id="batchSaveStockDeviceVersion">
        INSERT INTO
        hive_stock_device_version
        (
        <include refid="sql_columns"/>
        )
        VALUES
        <foreach collection="stockDeviceVersionList" item="item" index="index" open="" separator="," close="">
            (
            #{item.deviceSn},
            #{item.appType},
            #{item.appVersionCode},
            #{item.romType},
            #{item.romVersionCode},
            now(3),
            now(3)
            )
        </foreach>
    </insert>

</mapper>