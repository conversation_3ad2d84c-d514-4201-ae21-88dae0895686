package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备电量信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hive_device_electric")
public class DeviceElectric extends TimedEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 设备Id
     */
    private Long deviceId;

    /**
     * 设备SN
     */
    private String deviceSn;

    /**
     * 电量
     */
    private Integer electric;


}
