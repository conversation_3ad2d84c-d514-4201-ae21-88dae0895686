<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.NoticeRecordDao">

  <resultMap id="NoticeRecordMap" type="com.moredian.magicube.device.dao.entity.NoticeRecord">
    <result column="notice_record_id" property="noticeRecordId"/>
    <result column="org_id" property="orgId"/>
    <result column="device_sn" property="deviceSn"/>
    <result column="notice_event" property="noticeEvent"/>
    <result column="notice_result" property="noticeResult"/>
    <result column="last_offline_time" property="lastOfflineTime"/>
    <result column="gmt_create" property="gmtCreate"/>
    <result column="gmt_modify" property="gmtModify"/>
  </resultMap>

  <sql id="table_name">hive_notice_record</sql>

  <sql id="all_columns">
    notice_record_id,org_id,device_sn,notice_event,notice_result,last_offline_time,gmt_create,gmt_modify
  </sql>

  <insert id="insertNoticeRecord" parameterType="com.moredian.magicube.device.dao.entity.NoticeRecord">
    insert into <include refid="table_name"/>(<include refid="all_columns"/>) values
    (#{noticeRecordId},#{orgId},#{deviceSn},#{noticeEvent},#{noticeResult},#{lastOfflineTime},now(3),now(3))
  </insert>

  <select id="countRecords" resultType="java.lang.Integer">
    select count(1) from <include refid="table_name"/>
    <where>
      <if test="orgId != null">
        org_id=#{orgId}
      </if>
      <if test="deviceSn != null">
        and device_sn=#{deviceSn}
      </if>
      <if test="noticeEvent != null">
        and notice_event=#{noticeEvent}
      </if>
      <if test="noticeResult != null">
        and notice_result=#{noticeResult}
      </if>
      <if test="startDay != null and endDay != null">
        and gmt_create between #{startDay} and #{endDay}
      </if>
    </where>
  </select>

  <select id="getLatestRecordTimeByDeviceSn" resultType="java.util.Date">
    select max(gmt_create) from <include refid="table_name"/> where
    org_id=#{orgId} and device_sn=#{deviceSn} and notice_event=#{noticeEvent} and notice_result=1
  </select>

  <select id="getLastOfflineTime" resultType="java.lang.Long">
    select max(last_offline_time) from <include refid="table_name"/> where
    org_id=#{orgId} and device_sn=#{deviceSn} and notice_event=#{noticeEvent} and notice_result=1
  </select>
</mapper>