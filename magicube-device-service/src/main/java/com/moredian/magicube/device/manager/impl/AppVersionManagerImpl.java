package com.moredian.magicube.device.manager.impl;

import com.github.pagehelper.Page;
import com.moredian.bee.mybatis.domain.PaginationDomain;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.AppVersion;
import com.moredian.magicube.device.dao.mapper.AppVersionMapper;
import com.moredian.magicube.device.manager.AppVersionManager;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * app版本信息Manager
 *
 * <AUTHOR>
 */

@Slf4j
@Service
public class AppVersionManagerImpl implements AppVersionManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private AppVersionMapper appVersionMapper;

    @Override
    public Long addAppVersion(AppVersion appVersion) {
        Long versionId = idgeneratorService.getNextIdByTypeName(BeanConstants.APP_VERSION).pickDataThrowException();
        appVersion.setVersionId(versionId);
        appVersion.setIsEnforceUpdate(Boolean.FALSE);
        appVersion.setIsDelFlag(Boolean.FALSE);
        appVersion.setUserCreate(0L);
        appVersionMapper.addAppVersion(appVersion);
        return versionId;
    }

    @Override
    public int updateAppVersion(AppVersion appVersion) {
        return appVersionMapper.updateAppVersion(appVersion);
    }

    @Override
    public int updateAppVersionSelective(AppVersion appVersion) {
        return appVersionMapper.updateAppVersionSelective(appVersion);
    }

    @Override
    public int removeAppVersionById(Long id) {
        return appVersionMapper.removeAppVersionById(id);
    }

    @Override
    public AppVersion getAppVersionById(Long id) {
        return appVersionMapper.getAppVersionById(id);
    }

    @Override
    public AppVersion getAppVersionBySCTypeCode(Integer systemType, Integer appType, Integer versionCode) {
        AppVersion appVersion = new AppVersion();
        appVersion.setSystemType(systemType);
        appVersion.setAppType(appType);
        appVersion.setVersionCode(versionCode);
        AppVersion oneVersion = appVersionMapper.getAppVersionBySCTypeCode(appVersion);
        if (oneVersion == null) {
            return appVersion;
        } else {
            oneVersion.setSystemType(systemType);
            oneVersion.setAppType(appType);
            oneVersion.setVersionCode(versionCode);
            return oneVersion;
        }
    }

    @Override
    public AppVersion getNewAppVersionBySCType(Integer systemType, Integer appType) {
        AppVersion appVersion = new AppVersion();
        appVersion.setSystemType(systemType);
        appVersion.setAppType(appType);
        AppVersion oneVersion = appVersionMapper.getNewAppVersionBySCType(appVersion);
        if (oneVersion == null) {
            return appVersion;
        } else {
            oneVersion.setSystemType(systemType);
            oneVersion.setAppType(appType);
            return oneVersion;
        }
    }

    @Override
    public PaginationDomain<AppVersion> getPaginationAppVersion(PaginationDomain<AppVersion> pagination, AppVersion appVersion) {
        int count = appVersionMapper.getAppVersionCount(appVersion);
        pagination.setTotalCount(count);
        if (count > 0) {
            Map queryMap = new HashMap<>();
            queryMap.putAll(new BeanMap(appVersion));
            queryMap.putAll(new BeanMap(pagination));
            pagination.setData(appVersionMapper.getPaginationAppVersion(queryMap));
        }
        return pagination;
    }

    @Override
    public AppVersion getNewAppVersionBySCTypeAndIsActive(Integer systemType, Integer appType) {
        AppVersion appVersion = new AppVersion();
        appVersion.setSystemType(systemType);
        appVersion.setAppType(appType);
        AppVersion oneVersion = appVersionMapper.getNewAppVersionBySCTypeAndIsActive(appVersion);
        if (oneVersion == null) {
            return appVersion;
        } else {
            oneVersion.setSystemType(systemType);
            oneVersion.setAppType(appType);
            return oneVersion;
        }
    }

    @Override
    public int getAppVersionCount(AppVersion appVersion) {
        return appVersionMapper.getAppVersionCount(appVersion);
    }

    @Override
    public int getEnforceUpdateAppVersionCount(AppVersion appVersion) {
        return appVersionMapper.getEnforceUpdateAppVersionCount(appVersion);
    }

    @Override
    public int getLowVersionCount(Integer versionCode, Integer appType) {
        return appVersionMapper.getLowVersionCount(versionCode, appType);
    }

    @Override
    public AppVersion getAppVersionInfo(AppVersion appVersion) {
        return appVersionMapper.getAppVersionInfo(appVersion);
    }

    @Override
    public List<AppVersion> list() {
        return appVersionMapper.list();
    }
}