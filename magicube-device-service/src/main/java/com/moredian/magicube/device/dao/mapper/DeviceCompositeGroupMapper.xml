<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.moredian.magicube.device.dao.mapper.DeviceCompositeGroupMapper" >
	<resultMap id="deviceCompositeGroupResultMap" type="com.moredian.magicube.device.dao.entity.DeviceCompositeGroup">
		<result column="device_composite_group_id" property="deviceCompositeGroupId" />
		<result column="gmt_create" property="gmtCreate" />
		<result column="gmt_modify" property="gmtModify" />
		<result column="org_id" property="orgId" />
		<result column="group_id" property="groupId" />
		<result column="range_id" property="rangeId" />
		<result column="range_type" property="rangeType" />
	</resultMap>

	<resultMap id="DeviceCompositeGroupSizeSizeDtoMap" type="com.moredian.magicube.device.dto.composite.GroupDeviceSizeDTO">
		<result column="group_id" property="groupId" />
		<result column="composite_group_size" property="compositeGroupSize" />
	</resultMap>

	<sql id="sql_select">
			device_composite_group_id,
			org_id,
			group_id,
			range_id,
			range_type,
			 gmt_create,
			gmt_modify
	</sql>

	<sql id="sql_bulk_values">
		#{item.deviceCompositeGroupId},
		#{item.orgId},
		#{item.groupId},
		#{item.rangeId},
		#{item.rangeType},
		now(3),
		now(3)
	</sql>


	<insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.DeviceCompositeGroup">
		insert into hive_device_composite_group(
			device_composite_group_id,
			org_id,
			group_id,
			range_id,
			range_type,
			gmt_create,
			gmt_modify	
		)
		values (
		 	#{deviceCompositeGroupId},
			#{orgId},
			#{groupId},
			#{rangeId},
			#{rangeType},
			now(3),
			now(3)
		) ON DUPLICATE KEY UPDATE gmt_modify = now(3)
	</insert>

	<insert id="batchInsert" parameterType="java.util.List">
		insert into hive_device_composite_group
		(device_composite_group_id,org_id,group_id,range_id,range_type,gmt_create,gmt_modify)
		values
		<foreach collection="itemList" item="item" separator=",">
			(#{item.deviceCompositeGroupId},#{item.orgId},#{item.groupId},#{item.rangeId},#{item.rangeType},now(),now())
		</foreach>
	</insert>

	<delete id="deleteByIds" parameterType="map">
	    delete from hive_device_composite_group where org_id = #{orgId}
		and device_composite_group_id in
		<foreach collection="deviceCompositeGroupIdList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<select id="selectByGroupId" resultMap="deviceCompositeGroupResultMap">
		select <include refid="sql_select"/>
		    from hive_device_composite_group where org_id = #{orgId} and group_id= #{groupId}
	</select>

	<select id="selectByGroupIdList" resultMap="deviceCompositeGroupResultMap">
		select <include refid="sql_select"/>
		from hive_device_composite_group where org_id = #{orgId}
		<if test="rangeType != null">
			and range_type = #{rangeType}
		</if>
		and group_id in
		<foreach collection="groupIdList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="countDeviceCompositeGroupSizeByGroupId" resultMap="DeviceCompositeGroupSizeSizeDtoMap">
		SELECT group_id,COUNT(1) AS composite_group_size FROM hive_device_composite_group WHERE org_id=#{orgId} and rang_type=1 AND group_id IN
		<foreach collection="groupIds" item="groupId" open="(" separator="," close=")" index="index">
			#{groupId}
		</foreach>
		GROUP BY group_id
	</select>

	<select id="selectByDeviceIdList" resultMap="deviceCompositeGroupResultMap">
		select <include refid="sql_select"/>
		from hive_device_composite_group where org_id = #{orgId}
	 	and range_type = 2
		and range_id in
		<foreach collection="deviceIdList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectByDeviceCompositeIdList" resultMap="deviceCompositeGroupResultMap">
		select <include refid="sql_select"/>
		from hive_device_composite_group where org_id = #{orgId}
		and range_type = 1
		and range_id in
		<foreach collection="deviceCompositeIdList" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>


</mapper>