package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
  *@Description      
  *<AUTHOR>
  *@create          2025-03-03 16:12
  */ 
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "hive_device_iot_invoke_message")
public class DeviceIotInvokeMessage extends TimedEntity {
    /**
     * 消息id
     */
    @TableId(value = "message_id", type = IdType.INPUT)
    private Long messageId;

    /**
     * 机构id
     */
    @TableField(value = "org_id")
    private Long orgId;

    /**
     * 设备id
     */
    @TableField(value = "device_id")
    private Long deviceId;

    /**
     * 设备sn
     */
    @TableField(value = "device_sn")
    private String deviceSn;

    /**
     * 消息类型
     * @see com.moredian.magicube.device.enums.iot.IotFunctionIdEnum
     */
    @TableField(value = "message_type")
    private Integer messageType;

    /**
     * 消息状态，0：待执行，1：执行成功，2：执行失败，3：超时。
     * @see com.moredian.magicube.device.enums.iot.MessageStatusEnum
     */
    @TableField(value = "message_status")
    private Integer messageStatus;

    /**
     * 参数
     */
    @TableField(value = "params")
    private String params;

    /**
     * 响应
     */
    @TableField(value = "response")
    private String response;
}