package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.DeviceVersion;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DeviceRomVersionMapper {

    /**
     * 根据设备Sn获取rom版本信息
     *
     * @param deviceSn
     * @return
     */
    DeviceVersion getDeviceRomVersionBySn(@Param("deviceSn") String deviceSn);

    /**
     * 新增设备rom版本信息
     *
     * @param deviceVersion
     * @return
     */
    int insert(DeviceVersion deviceVersion);

    /**
     * 根据设备Sn修改rom版本信息
     *
     * @param deviceVersion
     * @return
     */
    int updateDeviceRomVersionByDeviceSn(DeviceVersion deviceVersion);

    /**
     * 批量获取设备rom版本号
     *
     * @param deviceSnList
     */
    List<DeviceVersion> getBatchDeviceRomVersion(@Param("deviceSnList") List<String> deviceSnList);

    /**
     * 获取机构下比指定os版本低的设备信息
     *
     * @param orgIdList
     * @param appType
     * @param versionCode
     * @return
     */
    List<DeviceVersion> findDeviceOsVersionByOrgIdListAndVersion(@Param("orgIdList") List<Long> orgIdList, @Param("appType") Integer appType,
                                                                  @Param("versionCode") Integer versionCode);

    /**
     * 获取机构下比指定版本低的设备信息
     *
     * @param orgIdList
     * @param appType
     * @param versionCode
     * @param apkAppType
     * @return
     */
    List<DeviceVersion> findDeviceRomVersionByOrgIdListAndVersion(@Param("orgIdList") List<Long> orgIdList, @Param("appType") Integer appType,
                                                                  @Param("versionCode") Integer versionCode, @Param("apkAppType") Integer apkAppType);



    /**
     * 批量后去设备ROM版本
     * @param orgId
     * @param deviceIdList
     * @return
     */
    List<DeviceVersion>  getBatchDeviceRomVersionByOrgIdAndDeviceIds(@Param("orgId") Long orgId, @Param("deviceIdList") List<Long> deviceIdList);
}