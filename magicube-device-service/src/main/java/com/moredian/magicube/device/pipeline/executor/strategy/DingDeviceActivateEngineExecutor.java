package com.moredian.magicube.device.pipeline.executor.strategy;


import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.executor.AbstractActivateDeviceEngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.ding.BindingGroupPipe;
import com.moredian.magicube.device.pipeline.pipe.ding.DingDeviceCheckPipe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 钉钉设备二维码激活引擎执行器
 *
 * <AUTHOR>
 */
@Component
public class DingDeviceActivateEngineExecutor extends AbstractActivateDeviceEngineExecutor {

    @Autowired
    private DingDeviceCheckPipe dingDeviceCheckPipe;

    @Autowired
    protected BindingGroupPipe bindingGroupPipe;

    @Override
    public void afterPropertiesSet() {
        pipeline.addPipe(commonActivateCheckPipe);
        pipeline.addPipe(checkDeviceAuthorizePipe);
        pipeline.addPipe(dingDeviceCheckPipe);
        pipeline.addPipe(verifyChannelAndArithmeticPipe);
        pipeline.addPipe(insertDevicePipe);
        pipeline.addPipe(uploadApkVersionPipe);
        pipeline.addPipe(bindingGroupPipe);
        pipeline.addPipe(enableBizPipe);
        pipeline.addPipe(insertDeviceLogPipe);
        pipeline.addPipe(updateDeviceWhitePipe);
        pipeline.addPipe(monitorDataPipe);
        pipeline.addPipe(publishMsgPipe);
        pipeline.addPipe(buildResultPipe);
    }

    @Override
    protected void validCustomParameter(ActivateDeviceDTO dto) {

    }

    @Override
    protected String setKey(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return "DingDeviceActivate_" + dto.getDeviceSn();
    }

    @Override
    public Integer getType() {
        return DeviceActivateEngineEnums.DING_DEVICE_QR_CODE_ACTIVATE.getCode();
    }

}
