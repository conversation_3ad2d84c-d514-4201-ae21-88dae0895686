package com.moredian.magicube.device.manager.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.redis.util.RedissonLockComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.config.RedisKeys;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceProjectCustomParam;
import com.moredian.magicube.device.dao.entity.DeviceProjectInfo;
import com.moredian.magicube.device.dao.mapper.DeviceProjectInfoMapper;
import com.moredian.magicube.device.dto.project.DeviceProjectInfoDTO;
import com.moredian.magicube.device.dto.project.DeviceProjectInfoResponse;
import com.moredian.magicube.device.dto.project.PullDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.ReportDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateProjectInfoResponse;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceProjectInfoManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/10
 */
@Component
@Slf4j
public class DeviceProjectInfoManagerImpl implements DeviceProjectInfoManager {

    @Autowired
    private RedissonLockComponent redissonLockComponent;

    @Autowired
    private DeviceProjectInfoMapper deviceProjectInfoMapper;

    @Autowired
    private DeviceManager deviceManager;

    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public Long reportProjectInfo(ReportDeviceProjectInfoRequest request) {
        BizAssert.notNull(request, "request must not be null");
        BizAssert.isTrue(StringUtils.isNotBlank(request.getDeviceSn()), "deviceSn must not be blank");
        BizAssert.isTrue(StringUtils.isNotBlank(request.getProjectInfo()), "projectInfo must not be blank");
        DeviceProjectInfoDTO deviceProjectInfoDto = parseProjectInfo(request.getProjectInfo());
        Device device = deviceManager.getByDeviceSn(request.getDeviceSn());
        BizAssert.notNull(device, DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
        Long id = null;
        String key = RedisKeys.getKey(RedisKeys.DEVICE_REPORT_PROJECT_INFO, request.getDeviceSn());
        try {
            if (StringUtils.isNotBlank(request.getDeviceSn())) {
                if (redissonLockComponent.acquire(key)) {
                    DeviceProjectInfo exist = deviceProjectInfoMapper.query(request.getDeviceSn());
                    if (exist == null) {
                        id = idgeneratorService.getNextIdByTypeName(DeviceProjectInfo.class.getName()).pickDataThrowException();
                        DeviceProjectInfo deviceProjectInfo = new DeviceProjectInfo();
                        deviceProjectInfo.setId(id);
                        deviceProjectInfo.setDeviceSn(request.getDeviceSn());
                        deviceProjectInfo.setProjectId(deviceProjectInfoDto.getProjectId());
                        deviceProjectInfo.setVersion(deviceProjectInfoDto.getVersion());
                        deviceProjectInfo.setProjectInfo(request.getProjectInfo());
                        deviceProjectInfo.setSceneType(deviceProjectInfoDto.getSceneType());
                        deviceProjectInfoMapper.insert(deviceProjectInfo);
                    } else {
                        exist.setProjectId(deviceProjectInfoDto.getProjectId());
                        exist.setVersion(deviceProjectInfoDto.getVersion());
                        exist.setSceneType(deviceProjectInfoDto.getSceneType());
                        exist.setProjectInfo(request.getProjectInfo());
                        deviceProjectInfoMapper.update(exist);
                        id = exist.getId();
                    }
                } else {
                    log.info("设备上报项目信息分布式锁获取失败:{}", JsonUtils.toJson(request));
                }
            }
        } finally {
            redissonLockComponent.release(key);
        }
        return id;
    }

    @Override
    public DeviceProjectInfoResponse pullProjectInfo(PullDeviceProjectInfoRequest request) {
        BizAssert.notNull(request, "request must not be null");
        BizAssert.isTrue(StringUtils.isNotBlank(request.getDeviceSn()), "deviceSn must not be null");
        if (!request.getEnforce()) {
            BizAssert.isTrue(StringUtils.isNotBlank(request.getProjectId()), "projectId must not be blank");
        }
        //校验设备是否存在
        Device device = deviceManager.getByDeviceSn(request.getDeviceSn());
        BizAssert.notNull(device, DeviceErrorCode.DEVICE_NOT_EXIST, DeviceErrorCode.DEVICE_NOT_EXIST.getMessage());
        QueryDeviceProjectInfoRequest queryRequest = new QueryDeviceProjectInfoRequest();
        queryRequest.setDeviceSnList(Lists.newArrayList(request.getDeviceSn()));
        if (!request.getEnforce()) {
            queryRequest.setProjectId(request.getProjectId());
        }
        List<DeviceProjectInfo> deviceProjectInfoList = deviceProjectInfoMapper.findDeviceProjectInfo(queryRequest);
        if (CollectionUtils.isNotEmpty(deviceProjectInfoList)) {
            DeviceProjectInfo deviceProjectInfo = deviceProjectInfoList.get(0);
            return convert(deviceProjectInfo);
        }
        return null;
    }

    @Override
    public List<DeviceProjectInfoResponse> findDeviceProjectInfo(QueryDeviceProjectInfoRequest request) {
        BizAssert.notNull(request, "request must not be null");
        List<DeviceProjectInfo> deviceProjectInfoList = deviceProjectInfoMapper.findDeviceProjectInfo(request);
        if (CollectionUtils.isNotEmpty(deviceProjectInfoList)) {
            return deviceProjectInfoList.stream().map(deviceProjectInfo -> convert(deviceProjectInfo)).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdateProjectInfoResponse updateProjectInfo(UpdateDeviceProjectInfoRequest request) {
        BizAssert.notNull(request, "request must not be null");
        BizAssert.notBlank(request.getProjectId(), "projectId must not be blank");
        BizAssert.notBlank(request.getDeviceSn(), "deviceSn must not be blank");
        BizAssert.notBlank(request.getProjectInfo(), "projectInfo must not be blank");
        UpdateProjectInfoResponse response = new UpdateProjectInfoResponse();
        QueryDeviceProjectInfoRequest queryRequest = new QueryDeviceProjectInfoRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setDeviceSnList(Lists.newArrayList(request.getDeviceSn()));
        List<DeviceProjectInfo> deviceProjectInfoList = deviceProjectInfoMapper.findDeviceProjectInfo(queryRequest);
        if (CollectionUtils.isEmpty(deviceProjectInfoList)) {
            log.info("未匹配到符合更改条件的设备，projectId:[{}]", request.getProjectId());
            return response;
        }
        DeviceProjectInfoDTO deviceProjectInfoDto = parseProjectInfo(request.getProjectInfo());
        List<String> changedDeviceSnList = new ArrayList<>();
        List<String> unChangedDeviceSnList = new ArrayList<>();
        if (deviceProjectInfoDto != null) {
            //校验projectId，云端不能修改projectId
            for (DeviceProjectInfo deviceProjectInfo : deviceProjectInfoList) {
                if (deviceProjectInfo.getProjectId().equals(deviceProjectInfoDto.getProjectId())) {
                    deviceProjectInfo.setVersion(deviceProjectInfoDto.getVersion());
                    deviceProjectInfo.setProjectInfo(request.getProjectInfo());
                    Integer count = deviceProjectInfoMapper.update(deviceProjectInfo);
                    if (count > 0) {
                        changedDeviceSnList.add(deviceProjectInfo.getDeviceSn());
                        continue;
                    }
                }
                unChangedDeviceSnList.add(deviceProjectInfo.getDeviceSn());
            }
        }
        response.setChangedDeviceSnList(changedDeviceSnList);
        response.setUnchangedDeviceSnList(unChangedDeviceSnList);
        return response;
    }

    @Override
    public DeviceProjectCustomParam queryDeviceProjectCustomParam(String deviceSn) {
        QueryDeviceProjectInfoRequest queryDeviceProjectInfoRequest = new QueryDeviceProjectInfoRequest();
        queryDeviceProjectInfoRequest.setDeviceSnList(Lists.newArrayList(deviceSn));
        List<DeviceProjectInfoResponse> deviceProjectInfoResponses = findDeviceProjectInfo(queryDeviceProjectInfoRequest);
        if (CollectionUtils.isEmpty(deviceProjectInfoResponses)) {
            return null;
        }
        DeviceProjectInfoResponse response = deviceProjectInfoResponses.get(0);
        if (response == null) {
            return null;
        }
        DeviceProjectInfoDTO deviceProjectInfoDto = parseProjectInfo(response.getProjectInfo());
        if (deviceProjectInfoDto == null) {
            return null;
        }
        DeviceProjectCustomParam deviceProjectCustomParam = parseCustomParam(deviceProjectInfoDto.getCustomParams());
        return deviceProjectCustomParam;
    }

    private DeviceProjectInfoDTO parseProjectInfo(String projectInfo) {
        if (StringUtils.isBlank(projectInfo)) {
            return null;
        }
        DeviceProjectInfoDTO deviceProjectInfoDto = null;
        try {
            deviceProjectInfoDto = JSON.parseObject(projectInfo, DeviceProjectInfoDTO.class);
        } catch (Exception e) {
            log.info("项目信息[{}]反序列化异常[{}]", projectInfo, e);
            return null;
        }
        BizAssert.notNull(deviceProjectInfoDto, "projectInfo must not be null");
        BizAssert.notBlank(deviceProjectInfoDto.getProjectId(), "projectId must not be blank");
        BizAssert.notBlank(deviceProjectInfoDto.getVersion(), "version must not be blank");
        BizAssert.notBlank(deviceProjectInfoDto.getCustomParams(), "customParams must not be blank");
        return deviceProjectInfoDto;
    }

    private DeviceProjectCustomParam parseCustomParam(String customParam) {
        if (StringUtils.isBlank(customParam)) {
            return null;
        }
        DeviceProjectCustomParam deviceProjectCustomParam = null;
        try {
            deviceProjectCustomParam = JSON.parseObject(customParam, DeviceProjectCustomParam.class);
        } catch (Exception e) {
            log.info("项目信息[{}]反序列化异常[{}]", customParam, e);
        }
        return deviceProjectCustomParam;
    }

    private DeviceProjectInfoResponse convert(DeviceProjectInfo deviceProjectInfo) {
        if (deviceProjectInfo == null) {
            return null;
        }
        DeviceProjectInfoResponse response = new DeviceProjectInfoResponse();
        response.setId(deviceProjectInfo.getId());
        response.setDeviceSn(deviceProjectInfo.getDeviceSn());
        response.setProjectId(deviceProjectInfo.getProjectId());
        response.setVersion(deviceProjectInfo.getVersion());
        response.setSceneType(deviceProjectInfo.getSceneType());
        response.setProjectInfo(deviceProjectInfo.getProjectInfo());
        response.setGmtCreate(deviceProjectInfo.getGmtCreate());
        response.setGmtModify(deviceProjectInfo.getGmtModify());
        return response;
    }
}
