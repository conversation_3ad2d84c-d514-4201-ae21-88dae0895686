package com.moredian.magicube.device.jetlinks.ruleArrange;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version $Id: RuleInstance.java, v 1.0 Exp $
 */
@Data
@Accessors(chain = true)
public class RuleInstance {

    private String id;

    private String modelId;

    private String name;

    private String description;

    private String modelType;

    private String modelMeta;

    private Integer modelVersion;

    private Long createTime;

    private String creatorId;

    private String state;

    private String creatorName;

    /**
     * scene | ruleArrange
     */
    private String ruleType;
}
