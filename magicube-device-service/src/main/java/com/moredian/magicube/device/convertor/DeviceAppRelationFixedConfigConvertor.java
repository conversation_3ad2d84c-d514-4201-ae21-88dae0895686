package com.moredian.magicube.device.convertor;

import com.moredian.magicube.device.dto.device.DeviceAppRelationFixedConfigDTO;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationFixedConfig;

public class DeviceAppRelationFixedConfigConvertor {

    /**
     * Converts a DeviceAppRelationFixedConfigDTO to a DeviceAppRelationFixedConfig.
     *
     * @param dto the DTO to convert
     * @return the converted entity
     */
    public static DeviceAppRelationFixedConfig dtoToEntity(DeviceAppRelationFixedConfigDTO dto) {
        if (dto == null) {
            return null;
        }

        DeviceAppRelationFixedConfig entity = new DeviceAppRelationFixedConfig();

        // Map DTO properties to Entity properties
                entity.setId(dto.getId());
                entity.setBizType(dto.getBizType());
                entity.setBizId(dto.getBizId());
                entity.setDefaultAppCode(dto.getDefaultAppCode());
                entity.setAvailableAppCodeList(dto.getAvailableAppCodeList());
                entity.setGmtCreate(dto.getGmtCreate());
                entity.setGmtModify(dto.getGmtModify());
        
        return entity;
    }

    /**
     * Converts a DeviceAppRelationFixedConfig to a DeviceAppRelationFixedConfigDTO.
     *
     * @param entity the entity to convert
     * @return the converted DTO
     */
    public static DeviceAppRelationFixedConfigDTO entityToDto(DeviceAppRelationFixedConfig entity) {
        if (entity == null) {
            return null;
        }

        DeviceAppRelationFixedConfigDTO dto = new DeviceAppRelationFixedConfigDTO();

        // Map Entity properties to DTO properties
                dto.setId(entity.getId());
                dto.setBizType(entity.getBizType());
                dto.setBizId(entity.getBizId());
                dto.setDefaultAppCode(entity.getDefaultAppCode());
                dto.setAvailableAppCodeList(entity.getAvailableAppCodeList());
                dto.setGmtCreate(entity.getGmtCreate());
                dto.setGmtModify(entity.getGmtModify());
        
        return dto;
    }
}