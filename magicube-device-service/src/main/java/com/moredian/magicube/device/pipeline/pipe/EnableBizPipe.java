package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.BizType;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 开启业务管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EnableBizPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @SI
    private OrgService orgService;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        Integer deviceType = dto.getDeviceType();
        //需要绑定默认组的，开通RECOGNIZE和OPENDOOR 目前来说是这样，以后可能有变更
        if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.NEED_DEFAULT_GROUP_LIST, deviceType)) {
            orgService.enableBizList(context.getOrgId(), Collections.singletonList(BizType.RECOGNIZE.getValue()));
            orgService.enableBizList(context.getOrgId(), Collections.singletonList(BizType.OPENDOOR.getValue()));
        } else if (deviceTypePropertyManager.containsDeviceType(DeviceTypeConstants.SINGLE_VISITOR_DEVICE_TYPE_LIST, deviceType)) {
            orgService.enableBizList(context.getOrgId(), Collections.singletonList(BizType.RECOGNIZE.getValue()));
            orgService.enableBizList(context.getOrgId(), Collections.singletonList(BizType.VISITOR.getValue()));
        }
    }
}
