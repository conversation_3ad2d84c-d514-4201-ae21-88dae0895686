package com.moredian.magicube.device.manager.bo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/14 16:31
 */
@Data
public class TreeNode implements Serializable {

    private static final long serialVersionUID = -5241407308023975291L;

    private Long id;

    private List<TreeNode> children;


    public TreeNode(Long id) {
        this.id = id;
        this.children = new ArrayList<TreeNode>();
    }
}
