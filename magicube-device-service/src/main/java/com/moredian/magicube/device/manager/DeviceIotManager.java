package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dto.device.DeviceStatusDTO;
import com.moredian.magicube.device.dto.device.RebootDTO;
import com.moredian.magicube.device.dto.device.TransferDTO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceIotManager {

    /**
     * 查询设备状态
     *
     * @param deviceSn 设备sn
     * @return
     */
    DeviceStatusDTO getStatusByDeviceSn(String deviceSn);

    /**
     * 根据设备sn列表查询设备状态列表
     *
     * @param deviceSns 设备sn列表
     * @return
     */
    List<DeviceStatusDTO> listDeviceStateByDeviceSns(List<String> deviceSns);

    /**
     * 重启设备
     *
     * @param dto 重启信息
     * @return
     */
    DeviceStatusDTO reboot(RebootDTO dto);

    /**
     * 透传命令
     *
     * @param dto 透传信息
     * @return
     */
    DeviceStatusDTO transfer(TransferDTO dto);
}