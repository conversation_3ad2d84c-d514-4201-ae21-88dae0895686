package com.moredian.magicube.device.third.lock.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description 上报通通锁wifi信息
 * @create 2025-03-31 14:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UploadWifiReq extends CommonReq{

    /**
     * 网络名称
     */
    private String networkName;

    /**
     * wifi，mac地址
     */
    private String wifiMac;

    /**
     * 信号强度
     */
    private Integer rssi;

    /**
     * 是否使用静态ip
     */
    private Boolean useStaticIp;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 子网掩码
     */
    private String subnetMask;

    /**
     * 默认网关
     */
    private String defaultGateway;

    /**
     * 首选dns
     */
    private String preferredDns;

    /**
     * 备选dns
     */
    private String alternateDns;
}
