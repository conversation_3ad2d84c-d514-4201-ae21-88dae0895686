package com.moredian.magicube.device.pipeline.pipe;

import com.moredian.bee.monitor.IMonitorService;
import com.moredian.bee.monitor.MonitorData;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.monitor.ActiveUnbindMonitorObj;
import com.moredian.magicube.device.monitor.CommonLogConstants;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 新增设备管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MonitorDataPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private IMonitorService monitorService;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        Device device = context.getDevice();
        //构造打印监控日志所需对象
        MonitorData monitorData = new MonitorData();
        monitorData.setTimestamp(System.currentTimeMillis());
        monitorData.setDeviceId(device.getDeviceId());
        monitorData.setDeviceSn(device.getDeviceSn());
        monitorData.setOrgId(device.getOrgId());
        monitorData.setData(new ActiveUnbindMonitorObj(CommonLogConstants.MONITOR_BIZ_ACTIVE_DEVICE));
        //日志形式埋点
        monitorService.addMonitorData(CommonLogConstants.LOG_SYSTEM, CommonLogConstants.ACTIVATE_UNBIND_DEVICE_LOG_STORE, monitorData);
    }
}
