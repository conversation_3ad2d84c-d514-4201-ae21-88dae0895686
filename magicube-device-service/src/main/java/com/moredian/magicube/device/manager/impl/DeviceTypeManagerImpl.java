package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.filemanager.FileManager;
import com.moredian.bee.filemanager.enums.FilePathType;
import com.moredian.bee.filemanager.model.SaveFileResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.constant.BeanConstants;
import com.moredian.magicube.device.dao.entity.DeviceType;
import com.moredian.magicube.device.dao.mapper.DeviceTypeMapper;
import com.moredian.magicube.device.dto.type.QueryDeviceTypeDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceTypeManager;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class DeviceTypeManagerImpl implements DeviceTypeManager {

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private FileManager fileManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(DeviceType deviceType) {
        BizAssert.notNull(deviceType.getDeviceType(), "deviceType must not be null");
        DeviceType dt = deviceTypeMapper.getByDeviceType(deviceType.getDeviceType());
        if (dt != null && dt.getStatus() == YesNoFlag.YES.getValue()) {
            BizAssert.notNull(null, DeviceErrorCode.DEVICE_TYPE_EXIST,
                DeviceErrorCode.DEVICE_TYPE_EXIST.getMessage());
        }
        deviceType.setStatus(YesNoFlag.YES.getValue());
        deviceType.setDeviceTypeInnerName(deviceType.getDeviceTypeInnerName());
        deviceType.setDeviceTypeName(StringUtils.isBlank(deviceType.getDeviceTypeName())
                ? deviceType.getDeviceTypeInnerName() : deviceType.getDeviceTypeName());
        deviceType.setDeviceTypeNameHy(StringUtils.isBlank(deviceType.getDeviceTypeNameHy())
            ? deviceType.getDeviceTypeInnerName() : deviceType.getDeviceTypeNameHy());
        deviceType.setDeviceTypeNameDing(StringUtils.isBlank(deviceType.getDeviceTypeNameDing())
            ? deviceType.getDeviceTypeInnerName() : deviceType.getDeviceTypeNameDing());
        deviceType.setDeviceTypeNameMl(StringUtils.isBlank(deviceType.getDeviceTypeNameMl())
            ? deviceType.getDeviceTypeInnerName() : deviceType.getDeviceTypeNameMl());
        deviceType.setDeviceTypeDisplayNameHy(StringUtils.isBlank(deviceType.
            getDeviceTypeDisplayNameHy()) ? deviceType.getDeviceTypeInnerName()
            : deviceType.getDeviceTypeDisplayNameHy());
        deviceType.setDeviceTypeDisplayNameDing(StringUtils.isBlank(deviceType
            .getDeviceTypeDisplayNameDing()) ? deviceType.getDeviceTypeInnerName()
            : deviceType.getDeviceTypeDisplayNameDing());
        deviceType.setDeviceTypeDisplayNameMl(StringUtils.isBlank(deviceType
            .getDeviceTypeDisplayNameMl()) ? deviceType.getDeviceTypeInnerName()
            : deviceType.getDeviceTypeDisplayNameMl());
        //临时图片转为永久图片
        if (StringUtils.isNotBlank(deviceType.getDeviceTypePic())) {
            String relativePath = fileManager.getRelativePathByUrl(
                deviceType.getDeviceTypePic());
            String fileName = deviceType.getDeviceTypePic().substring(deviceType
                .getDeviceTypePic().lastIndexOf("/") + 1);
            SaveFileResponse saveFileResponse = fileManager.copyByRelativePath(relativePath,
                FilePathType.TYPE_DEVICEIMAGE, fileName).pickDataThrowException();
            deviceType.setDeviceTypePic(saveFileResponse.getRelativePath());
        }
        if (dt == null) {
            deviceType.setId(idgeneratorService.getNextIdByTypeName(BeanConstants.DEVICE_TYPE)
                .pickDataThrowException());
            deviceTypeMapper.insert(deviceType);
        } else {
            deviceType.setId(dt.getId());
            update(deviceType);
        }
        return deviceType.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(DeviceType deviceType) {
        BizAssert.notNull(deviceType.getId(), "deviceTypeId must not be null");
        DeviceType dt = deviceTypeMapper.getById(deviceType.getId());
        BizAssert.isTrue(dt != null, DeviceErrorCode.DEVICE_TYPE_NOT_EXIST,
            DeviceErrorCode.DEVICE_TYPE_NOT_EXIST.getMessage());
        //临时图片转为永久图片
        if (StringUtils.isNotBlank(deviceType.getDeviceTypePic())) {
            String relativePath = fileManager.getRelativePathByUrl(deviceType.getDeviceTypePic());
            String fileName = deviceType.getDeviceTypePic().substring(deviceType
                .getDeviceTypePic().lastIndexOf("/") + 1);
            SaveFileResponse saveFileResponse = fileManager.copyByRelativePath(relativePath,
                FilePathType.TYPE_DEVICEIMAGE, fileName).pickDataThrowException();
            deviceType.setDeviceTypePic(saveFileResponse.getRelativePath());
        }
        deviceTypeMapper.update(deviceType);
        return deviceType.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusByDeviceType(Integer deviceType, Integer status) {
        BizAssert.notNull(deviceType, "deviceType must not be null");
        DeviceType dt = deviceTypeMapper.getByDeviceType(deviceType);
        BizAssert.isTrue(dt != null, DeviceErrorCode.DEVICE_TYPE_NOT_EXIST,
            DeviceErrorCode.DEVICE_TYPE_NOT_EXIST.getMessage());
        DeviceType updateDeviceType = new DeviceType();
        updateDeviceType.setId(dt.getId());
        updateDeviceType.setStatus(status);
        return deviceTypeMapper.update(updateDeviceType);
    }

    @Override
    public Boolean deleteByDeviceType(Integer deviceType) {
        BizAssert.notNull(deviceType, "deviceType must not be null");
        deviceTypeMapper.deleteByDeviceType(deviceType);
        return Boolean.TRUE;
    }

    @Override
    public List<DeviceType> list() {
        return deviceTypeMapper.list();
    }

    @Override
    public List<DeviceType> listByTypes(List<Integer> deviceTypes) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceTypes), "deviceTypes must not be null");
        return deviceTypeMapper.listByTypes(deviceTypes);
    }

    @Override
    public DeviceType getById(Long deviceTypeId) {
        BizAssert.notNull(deviceTypeId, "deviceTypeId must not be null");
        return deviceTypeMapper.getById(deviceTypeId);
    }

    @Override
    public String getName(Integer deviceType) {
        if (deviceType == null) {
            return null;
        }
        return deviceTypeMapper.getName(deviceType);
    }

    @Override
    public DeviceType getByDeviceType(Integer deviceType) {
        BizAssert.notNull(deviceType, "deviceType must not be null");
        return deviceTypeMapper.getByType(deviceType);
    }

    @Override
    public List<DeviceType> listByCondition(QueryDeviceTypeDTO dto) {
        return deviceTypeMapper.listByCondition(dto);
    }

    @Override
    public List<DeviceType> listLikeName(String keywords) {
        return deviceTypeMapper.listDeviceTypeLikeName(keywords);
    }

    @Override
    public List<DeviceType> getByProductModelCode(String productModelCode) {
        BizAssert.notBlank(productModelCode, "productModelCode must not be null");
        return deviceTypeMapper.getByProductModelCode(productModelCode);
    }

    @Override
    public List<DeviceType> listByProductModelCodes(List<String> productModelCodes) {
        BizAssert.isTrue(CollectionUtils.isNotEmpty(productModelCodes), "productModelCodes must not be null");
        return deviceTypeMapper.listByProductModelCodes(productModelCodes);
    }
}
