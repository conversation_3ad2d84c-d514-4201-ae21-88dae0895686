package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.dao.entity.DeviceTypeProperty;
import java.util.List;

/**
 * 设备类型属性相关接口
 *
 * <AUTHOR>
 */
public interface DeviceTypePropertyManager {

    /**
     * 新增设备类型属性
     *
     * @param deviceTypeProperty 设备类型属性信息
     * @return
     */
    Long insert(DeviceTypeProperty deviceTypeProperty);

    /**
     * 编辑设备类型属性
     *
     * @param deviceTypeProperty 设备类型属性信息
     * @return
     */
    Long update(DeviceTypeProperty deviceTypeProperty);

    /**
     * 根据Id查询设备类型属性
     *
     * @param id 设备类型Id
     * @return
     */
    DeviceTypeProperty getById(Long id);

    /**
     * 根据属性key查询设备类型属性
     *
     * @param propertyKey 属性key
     * @return
     */
    DeviceTypeProperty getByPropertyKey(String propertyKey);

    /**
     * 查询设备类型属性列表
     *
     * @return
     */
    List<DeviceTypeProperty> list();

    /**
     * 根据主键Id查询设备类型属性列表
     *
     * @param ids 设备类型属性Id列表
     * @return
     */
    List<DeviceTypeProperty> listByIds(List<Long> ids);

    /**
     * 根据属性key查询设备类型
     *
     * @param propertyKey 属性key
     * @return 设备List
     */
    List<Integer> getDeviceTypeByPropertyKey(String propertyKey);

    /**
     * 属性是否关联指定设备类型
     *
     * @param propertyKey 属性key
     * @param deviceType 设备类型
     * @return 设备List
     */
    Boolean containsDeviceType(String propertyKey, Integer deviceType);
}