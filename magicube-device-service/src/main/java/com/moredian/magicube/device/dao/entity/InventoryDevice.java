package com.moredian.magicube.device.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;

import com.moredian.magicube.device.dao.base.TimedEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备白名单
 *
 * <AUTHOR>
 * @since 2021-09-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("da_device_inventory")
public class InventoryDevice {

    private static final long serialVersionUID = 1L;

    /**
     * 设备sn
     */
    private String serialNumber;

    /**
     * Mac地址1
     */
    private String macAddress;

    /**
     * Mac地址2
     */
    private String macAddress2;

    /**
     * 私有key
     */
    private String privateKey;

    /**
     * 批量标记
     */
    private Integer batchFlag;

    /**
     * 机构号
     */
    private Long orgId;

    /**
     * 激活状态 0-未激活 1-已激活
     */
    private Integer activityStatus;

    /**
     * 第三方设备id
     */
    private String thirdDeviceId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 设备来源 1魔点设备,2三方设备
     */
    private Integer deviceSource;

    /**
     * 激活码
     */
    private String activationCode;

    /**
     * 是否开放平台激活
     */
    private Boolean isOpenPlat;

    /**
     * 是否开放平台激活
     */
    private Boolean isEduDevice;

    /**
     * 设备激活oaBase 1-非钉 2-钉钉
     */
    private Integer deviceActivateOaBase;

    /**
     * 设备激活sdkBase 1-非钉 2-钉钉
     */
    private Integer deviceActivateSdkBase;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
