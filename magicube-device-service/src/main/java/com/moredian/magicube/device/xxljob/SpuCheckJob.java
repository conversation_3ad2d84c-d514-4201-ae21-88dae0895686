package com.moredian.magicube.device.xxljob;

import static com.moredian.magicube.device.config.ParamConstants.REGEX_CHINESE;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Maps;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.job.annotation.BeeXxlJob;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.hephaestus.request.relation.GetRelationBySerialNumberRequest;
import com.moredian.hephaestus.response.relation.SimpleSpuInventoryRelationResponse;
import com.moredian.hephaestus.service.SpuInventoryRelationService;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.config.ParamConstants;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.helper.DeviceTypeNameHelper;
import com.moredian.magicube.device.manager.DeviceTypePropertyConfigManager;
import com.moredian.magicube.device.service.DeviceService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description：spu校验定时任务
 * @date ：2024/07/25 16:52
 */
@Component
@Slf4j
public class SpuCheckJob {

    @SI
    private OrgService orgService;

    @SI
    private DeviceService deviceService;

    @SI
    private SpuInventoryRelationService spuInventoryRelationService;

    @Autowired
    private DeviceTypeNameHelper deviceTypeNameHelper;

    @BeeXxlJob(value = "spuCheck", name = "手动执行SPU校验任务")
    public ReturnT<String> spuCheck(String params) {
        ReturnT<String> success = ReturnT.SUCCESS;
        if (ObjectUtil.isNotEmpty(params)) {
            List<Long> orgIds = Arrays.stream(params.split(",")).map(Long::parseLong).collect(
                Collectors.toList());
            for (Long orgId : orgIds) {
                try {
                    if (extracted(orgId)) {
                        continue;
                    }
                } catch (Exception e) {
                    log.error("单机构spu校验任务异常e:{},orgId:{}", e, orgId);
                }
            }
        } else {
            //查询所有机构
            List<Long> allOrgIds = orgService.findAllOrgId().pickDataThrowException();
            for (Long orgId : allOrgIds) {
                try {
                    if (extracted(orgId)) {
                        continue;
                    }
                } catch (Exception e) {
                    log.error("全量spu校验任务异常e:{},orgId:{}", e, orgId);
                }
            }
        }
        return success;
    }

    private boolean extracted(Long orgId) {
        List<DeviceInfoDTO> deviceInfos = deviceService.listByOrgId(orgId)
            .pickDataThrowException();
        if (CollectionUtils.isEmpty(deviceInfos)) {
            return true;
        }
        List<String> deviceSns = deviceInfos.stream().map(DeviceInfoDTO::getDeviceSn)
            .collect(java.util.stream.Collectors.toList());
        // 查询设备物料简称
        Map<String/*deviceSn*/, String/*shortMaterialName*/> shortMaterialNameMap = Maps.newHashMap();
        // 获取设备物料信息
        if (CollectionUtils.isNotEmpty(deviceSns)) {
            GetRelationBySerialNumberRequest getRelationBySerialNumberRequest = new GetRelationBySerialNumberRequest();
            getRelationBySerialNumberRequest.setSerialNumberList(deviceSns);
            List<SimpleSpuInventoryRelationResponse> relationList = spuInventoryRelationService.findSpuInventoryRelationBySerialNumberList(
                getRelationBySerialNumberRequest).pickDataThrowException();
            if (CollectionUtils.isNotEmpty(relationList)) {
                for (SimpleSpuInventoryRelationResponse relation : relationList) {
                    if (org.apache.commons.lang.StringUtils.isNotBlank(
                        relation.getShortMaterialName())) {
                        shortMaterialNameMap.put(relation.getSerialNumber(),
                            relation.getShortMaterialName());
                    }
                }
            }
        }
        for (DeviceInfoDTO deviceInfoDTO : deviceInfos) {
            String shortMaterialName = shortMaterialNameMap.get(
                deviceInfoDTO.getDeviceSn());
            //使用老接口获取的设备类型名称
            String oldDeviceTypeName;
            if (StringUtil.isNotBlank(shortMaterialName)) {
                oldDeviceTypeName = shortMaterialName;
            } else {
                if (DeviceType.isNeedConvert(deviceInfoDTO.getDeviceType())) {
                    oldDeviceTypeName = DeviceType.getConvertName(String.valueOf(deviceInfoDTO.getDeviceType()));
                } else {
                    oldDeviceTypeName = DeviceType.getName(deviceInfoDTO.getDeviceType());
                }
                if (StringUtils.isBlank(oldDeviceTypeName)) {
                    continue;
                }
                oldDeviceTypeName = oldDeviceTypeName.replaceAll(REGEX_CHINESE, StringUtils.EMPTY);
                if (StringUtil.isBlank(oldDeviceTypeName) && DeviceType.isNeedDefaultSpu(
                    deviceInfoDTO.getDeviceType())) {
                    oldDeviceTypeName = DeviceType.getDefaultSpu(String.valueOf(deviceInfoDTO.getDeviceType()));
                }
            }

            //使用新接口获取的设备类型名称
            String newDeviceTypeName = deviceTypeNameHelper.getDisplayDeviceTypeName(
                deviceInfoDTO.getDeviceSn());
            if (StringUtil.isNotBlank(newDeviceTypeName) && !newDeviceTypeName.equals(
                oldDeviceTypeName)) {
                log.info(
                    "spu校验任务->新老设备类型不一致：deviceSn:{},deviceType:{},oldDeviceTypeName:{},newDeviceTypeName:{}",
                    deviceInfoDTO.getDeviceSn(), deviceInfoDTO.getDeviceType(),
                    oldDeviceTypeName, newDeviceTypeName);
            }
        }
        return false;
    }
}
