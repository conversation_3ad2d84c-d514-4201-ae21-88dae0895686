package com.moredian.magicube.device.service.impl.lock;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.BizException;
import com.moredian.bee.common.exception.CommonErrorCode;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.StringUtil;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.IIotDevicePropertyService;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyInfoDTO;
import com.moredian.device.pipeline.api.DevicePipelineStateService;
import com.moredian.device.pipeline.dto.request.UpdateDeviceSceneTypeReq;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.AuthorizeAppEnum;
import com.moredian.magicube.common.enums.SceneTypeEnums;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.common.model.msg.DeviceUnbindMsg;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceIotInvokeMessage;
import com.moredian.magicube.device.dao.entity.DeviceNetworkInfo;
import com.moredian.magicube.device.dao.entity.DeviceOnlineState;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.activate.ActivateDeviceResultDTO;
import com.moredian.magicube.device.dto.device.UpdateDeviceDTO;
import com.moredian.magicube.device.dto.lock.*;
import com.moredian.magicube.device.dto.network.InsertDeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import com.moredian.magicube.device.enums.iot.IotFunctionIdEnum;
import com.moredian.magicube.device.enums.iot.MessageStatusEnum;
import com.moredian.magicube.device.helper.SceneTypeHelper;
import com.moredian.magicube.device.helper.SpaceHelper;
import com.moredian.magicube.device.manager.*;
import com.moredian.magicube.device.manager.helper.IotDeviceManagerHelper;
import com.moredian.magicube.device.service.ActivityService;
import com.moredian.magicube.device.service.DeviceNetworkInfoService;
import com.moredian.magicube.device.service.lock.TTLockService;
import com.moredian.magicube.device.third.lock.TTLockOpenService;
import com.moredian.magicube.device.third.lock.req.UploadWifiReq;
import com.moredian.magicube.device.third.lock.res.InitializeLockResp;
import com.moredian.ota.api.msg.DeviceOnlineStateMsg;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.moredian.magicube.device.enums.DeviceActivateEngineEnums.THIRD_DEVICE_ACTIVATE;
import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.DEVICE_ACTIVATE_SUCCESS;
import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.DEVICE_BIND_ORG;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-24 14:59
 */
@SI
@Slf4j
public class TTLockServiceImpl implements TTLockService {

    @Resource
    private TTLockOpenService ttLockOpenService;
    @Resource
    private RedissonCacheComponent redissonCacheComponent;
    @Resource
    private ActivityManager activityManager;
    @Resource
    private DeviceManager deviceManager;
    @Resource
    private SpaceHelper spaceHelper;
    @Resource
    private SceneTypeHelper sceneTypeHelper;
    @Resource
    private IotDeviceManagerHelper iotDeviceManagerHelper;
    @Resource
    private DeviceOnlineStateManager deviceOnlineStateManager;
    @Resource
    private DeviceIotInvokeMessageManager deviceIotInvokeMessageManager;

    @SI
    private ActivityService activityService;
    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;
    @SI
    private DevicePipelineStateService devicePipelineStateService;
    @SI
    private IIotDevicePropertyService iIotDevicePropertyService;
    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;
    @SI
    private DeviceNetworkInfoService deviceNetworkInfoService;
    @SI
    private IdgeneratorService idgeneratorService;

    @Value("${ttLock.jetLinks.productId:1895417436393504768}")
    private String jetLinksProductId;


    @Override
    public ServiceResponse<LockInfoDTO> initialLock(InitialLockDTO initialLockDTO) {
        String lockData = initialLockDTO.getLockData();
        BizAssert.isTrue(StringUtil.isNotBlank(lockData), "lockData must be not empty");

        ServiceResponse<LockInfoDTO> res = ServiceResponse.createSuccessResponse();

        InitializeLockResp initializeLock = ttLockOpenService.initializeLock(lockData);
        LockInfoDTO lockInfo = LockInfoDTO.builder()
                .deviceSn(String.valueOf(initializeLock.getLockId()))
                .build();
        // 暂存lockData，存入redis
        String key = RedisConstants.getKey(RedisConstants.TT_LOCK_DATA, lockInfo.getDeviceSn());
        redissonCacheComponent.setObjectCache(key, initialLockDTO.getLockData(), 20L, TimeUnit.MINUTES);
        res.setData(lockInfo);
        return res;
    }

    @Override
    @Transactional
    public ServiceResponse<Boolean> unbindLock(LockInfoDTO lockInfoDTO) {
        String deviceSn = lockInfoDTO.getDeviceSn();
        Device device = deviceManager.getByDeviceSn(deviceSn);
        ServiceResponse<Boolean> res = ServiceResponse.createSuccessResponse();
        if (device == null) {
            res.setData(Boolean.TRUE);
            return res;
        }
        // 平台删除设备并发出消息
        boolean deleteResult = deviceManager.deleteById(device.getOrgId(), device.getDeviceId());
        ErrorContext errorContext = new ErrorContext("FD0020180305", "设备解绑失败");
        BizAssert.isTrue(deleteResult, errorContext, errorContext.getMessage());

        // 魔链删除通通锁设备
        Boolean b = iotDeviceInstanceService.batchUnDeployAndDeleteDevice(-1L, Lists.newArrayList(deviceSn))
                .pickDataThrowException();
        if (!b) {
            log.error("通通锁解绑失败,orgId=>{}.deviceSn=>{}", device.getOrgId(), deviceSn);
            throw new BizException(new ErrorContext(DeviceErrorCode.DEVICE_UNBIND_FAIL, DeviceErrorCode.DEVICE_UNBIND_FAIL.getMessage()));
        }
        // 发送设备解绑消息
        DeviceUnbindMsg deviceUnbindMsg = new DeviceUnbindMsg();
        deviceUnbindMsg.setDeviceId(device.getDeviceId());
        deviceUnbindMsg.setDeviceSn(device.getDeviceSn());
        deviceUnbindMsg.setDeviceType(device.getDeviceType());
        if (StringUtils.isNotBlank(device.getPosition())) {
            deviceUnbindMsg.setPosition(device.getPosition());
        }
        TreeDeviceRelationDTO relationDTO = spaceTreeDeviceRelationService
                .getByOrgIdAndDeviceId(device.getOrgId(), String.valueOf(device.getDeviceId())).pickDataThrowException();
        if (relationDTO != null) {
            deviceUnbindMsg.setTreeId(relationDTO.getTreeId());
        }
        deviceUnbindMsg.setName(device.getDeviceName());
        deviceUnbindMsg.setOrgId(device.getOrgId());
        deviceUnbindMsg.setTimeStamp(System.currentTimeMillis());
        log.info(" --------> 通通锁解绑EventBus.publish(DeviceUnbindMsg) >>> msg={}",
                JsonUtils.toJson(deviceUnbindMsg));
        EventBus.publish(deviceUnbindMsg);
        res.setData(Boolean.TRUE);
        return res;
    }

    @Override
    public ServiceResponse<Boolean> activate(LockInfoDTO lockInfoDTO) {
        ServiceResponse<Boolean> res = ServiceResponse.createSuccessResponse();

        String deviceSn = lockInfoDTO.getDeviceSn();
        Long orgId = lockInfoDTO.getOrgId();
        Integer deviceType = lockInfoDTO.getDeviceType();
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn must be not empty");
        BizAssert.isTrue(orgId != null, "orgId must be not empty");
        BizAssert.isTrue(deviceType != null, "deviceType must be not empty");

        // 设备激活
        ActivateDeviceDTO activateDeviceDTO = new ActivateDeviceDTO();
        activateDeviceDTO.setJetLinkProductId(jetLinksProductId);
        activateDeviceDTO.setOrgId(orgId);
        activateDeviceDTO.setDeviceSn(deviceSn);
        activateDeviceDTO.setDeviceType(deviceType);
        activateDeviceDTO.setDeviceActivateType(THIRD_DEVICE_ACTIVATE.getCode());
        ActivateDeviceResultDTO activity = activityService.activate(activateDeviceDTO).pickDataThrowException();
        if (activity != null) {
            // 上报SceneType和绑根空间
            Device device = deviceManager.getByDeviceSn(deviceSn);

            UpdateDeviceSceneTypeReq req = new UpdateDeviceSceneTypeReq();
            req.setDeviceSn(deviceSn);
            req.setDeviceId(device.getDeviceId());
            req.setOrgId(device.getOrgId());
            req.setSceneType(SceneTypeEnums.STANDARD_MODEL.getValue());
            devicePipelineStateService.updateCloudSceneType(req)
                    .pickDataThrowException();

            // 绑定根空间
            spaceHelper.bindRootSpace(device.getOrgId(), device.getDeviceId());

            // 更新关联应用
            UpdateDeviceDTO updateDeviceDTO = new UpdateDeviceDTO();
            updateDeviceDTO.setDeviceId(device.getDeviceId());
            updateDeviceDTO.setOrgId(device.getOrgId());
            updateDeviceDTO.setAppCode(String.join(",", AuthorizeAppEnum.FACEDOOR.getCode()));
            updateDeviceDTO.setAppCodeList(String.join(",", AuthorizeAppEnum.FACEDOOR.getCode()));
            deviceManager.update(updateDeviceDTO);
        }
        // 修改激活状态为激活成功
        activityService.deviceActiveStateChange(deviceSn, DEVICE_ACTIVATE_SUCCESS.getCode(), null);
        res.setData(activity != null);
        return res;
    }

    @Override
    public ServiceResponse<Boolean> uploadNetworkCache(String deviceSn, NetworkInfoDTO networkInfoDTO) {
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn must be not empty");
        ServiceResponse<Boolean> res = ServiceResponse.createSuccessResponse();

        Device device = deviceManager.getByDeviceSn(deviceSn);
        // 不为空表示，设备已经激活，直接调通通锁开放接口上报网络信息
        if (device != null) {
            UploadWifiReq req = new UploadWifiReq();
            BeanUtils.copyProperties(networkInfoDTO, req);
            req.setLockId(Integer.valueOf(deviceSn));
            ttLockOpenService.uploadWifiInfo(req);

            // 网络信息入库
            InsertDeviceNetworkInfoDTO deviceNetworkInfoDTO = new InsertDeviceNetworkInfoDTO();
            deviceNetworkInfoDTO.setOrgId(device.getOrgId());
            deviceNetworkInfoDTO.setNetworkType(2);
            WifiInfo wifiInfo = new WifiInfo();
            wifiInfo.setIp(networkInfoDTO.getIp());
            wifiInfo.setName(networkInfoDTO.getNetworkName());
            wifiInfo.setPassword(networkInfoDTO.getWifiPassword());
            deviceNetworkInfoDTO.setWifiInfo(wifiInfo);
            deviceNetworkInfoDTO.setWifiMac(networkInfoDTO.getWifiMac());
            deviceNetworkInfoDTO.setDeviceId(device.getDeviceId());
            deviceNetworkInfoDTO.setConnectType(1);
            deviceNetworkInfoService.upload(deviceNetworkInfoDTO).pickDataThrowException();
            res.setData(Boolean.TRUE);
            return res;
        }

        // 表示设备还未激活，先缓存在redis中
        String key = RedisConstants.getKey(RedisConstants.TT_LOCK_NETWORK_INFO, deviceSn);
        redissonCacheComponent.setObjectCache(key, networkInfoDTO, 1L, TimeUnit.HOURS);
        if (Boolean.TRUE.equals(networkInfoDTO.getChangeActivateStatus())) {
            // 设置激活状态到绑定团队状态
            Boolean flag = activityManager.deviceActiveStateChange(deviceSn, DEVICE_BIND_ORG.getCode(), null, null);
            res.setData(flag);
        }
        return res;
    }

    @Override
    public ServiceResponse<LockInfoDTO> lockInfoBySn(String deviceSn) {
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn must be not empty");
        Device device = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.isTrue(device != null, "device is not exist");

        // 查询魔链设备物模型信息
        LockInfoDTO.LockInfo lockInfo = iotDeviceManagerHelper.getIotDeviceLastProperties(-1L, deviceSn, LockInfoDTO.LockInfo.class);
        Map<String, Boolean> deviceStateMap = iotDeviceManagerHelper.getIotDeviceStateMap(-1L, Lists.newArrayList(deviceSn));
        lockInfo.setOnline(deviceStateMap.getOrDefault(deviceSn, Boolean.FALSE));

        LockInfoDTO lockInfoDTO = LockInfoDTO.builder()
                .deviceSn(deviceSn)
                .deviceType(device.getDeviceType())
                .deviceName(device.getDeviceName())
                .lockInfo(lockInfo)
                .orgId(device.getOrgId())
                .build();
        return new ServiceResponse<>(lockInfoDTO);
    }

    @Override
    public ServiceResponse<Long> updatePassword(UpdateLockDTO dto) {
        // 是否开启密码
        Integer enablePassword = dto.getEnablePassword();
        String password = dto.getNewKeyboardPwd();
        Long deviceId = dto.getDeviceId();
        String deviceSn = dto.getDeviceSn();
        BizAssert.isTrue(enablePassword != null, "enablePassword must be not empty");
        BizAssert.isTrue(StringUtil.isNotBlank(password), "password must be not empty");
        BizAssert.isTrue(deviceId != null, "deviceId must be not empty");
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn must be not empty");
        Device device = deviceManager.getById(deviceId);
        BizAssert.isTrue(device != null, "device is not exist");

        LockInfoDTO lockInfoDTO = this.lockInfoBySn(deviceSn).pickDataThrowException();
        LockInfoDTO.LockInfo info = lockInfoDTO.getLockInfo();
        Integer keyboardPwdId = info.getKeyboardPwdId();
        Long deviceIdLink = info.getDeviceId();

        ServiceResponse<Long> res = ServiceResponse.createSuccessResponse();
        Long messageId = idgeneratorService.getNextIdByTypeName(TTLockServiceImpl.class.getName()).pickDataThrowException();
        Map<String, Object> paramMap = new HashMap<>();

        InvokeMessageDTO invokeDTO;
        if (YesNoFlag.NO.getValue() == enablePassword) { // 删除密码
            if (keyboardPwdId == null || keyboardPwdId == -1 || !deviceIdLink.equals(deviceId)) {
                // 表示当前锁还没有设置过密码，直接生成消息并置为成功
                DeviceIotInvokeMessage successMsg = new DeviceIotInvokeMessage();
                successMsg.setMessageStatus(MessageStatusEnum.SUCCESS_STATUS.getType());
                successMsg.setOrgId(device.getOrgId());
                successMsg.setDeviceId(device.getDeviceId());
                successMsg.setMessageType(IotFunctionIdEnum.TTLOCK_DELETE_PASSWORD.getCode());
                successMsg.setDeviceSn(deviceSn);
                successMsg.setMessageId(messageId);
                deviceIotInvokeMessageManager.save(successMsg);
                res.setData(messageId);
                return res;
            }else { // 表示设置过通行密码，需要删除
                paramMap.put("keyboardPwdId", keyboardPwdId);
                invokeDTO = InvokeMessageDTO.builder()
                        .messageType(IotFunctionIdEnum.TTLOCK_DELETE_PASSWORD.getCode())
                        .deviceSn(deviceSn)
                        .params(paramMap)
                        .build();
            }
        }else { // 添加或则修改密码
            if (keyboardPwdId == null || keyboardPwdId == -1
                    || deviceIdLink == null || !deviceIdLink.equals(deviceId)) {
                // 添加密码
                paramMap.put("keyboardPwd", password);
                paramMap.put("deviceId", device.getDeviceId());
                invokeDTO = InvokeMessageDTO.builder()
                        .messageType(IotFunctionIdEnum.TTLOCK_ADD_PASSWORD.getCode())
                        .deviceSn(deviceSn)
                        .params(paramMap)
                        .build();
            }else { // 修改密码
                paramMap.put("keyboardPwdId", keyboardPwdId);
                paramMap.put("newKeyboardPwd", password);
                invokeDTO = InvokeMessageDTO.builder()
                        .messageType(IotFunctionIdEnum.TTLOCK_UPDATE_PASSWORD.getCode())
                        .deviceSn(deviceSn)
                        .params(paramMap)
                        .build();
            }
        }
        Long messageResId = deviceIotInvokeMessageManager.invoke(invokeDTO);
        res.setData(messageResId);
        return res;
    }

    @Override
    public ServiceResponse<Boolean> uploadLockOnlineState(UpdateLockDTO dto) {
        String deviceSn = dto.getDeviceSn();
        Boolean online = ObjectUtil.isNotEmpty(dto.getOnline()) ? dto.getOnline() : Boolean.FALSE;
        BizAssert.isTrue(StringUtil.isNotBlank(deviceSn), "deviceSn must be not empty");
        Device device = deviceManager.getByDeviceSn(deviceSn);
        BizAssert.isTrue(device != null, "device is not exist");

        List<DeviceOnlineState> deviceOnlineStates = deviceOnlineStateManager
                .selectByOrgIdAndDeviceId(device.getOrgId(), Lists.newArrayList(device.getDeviceId()));

        // 如果上报状态与上次的状态一致，则不更新
        if (CollectionUtil.isNotEmpty(deviceOnlineStates)) {
            DeviceOnlineState deviceOnlineState = deviceOnlineStates.get(0);
            if (online.equals(deviceOnlineState.getOnlineFlag())) {
                return ServiceResponse.createSuccessResponse();
            }
        }

        DeviceOnlineStateMsg msg = new DeviceOnlineStateMsg();
        msg.setMsgTimestamp(new Date().getTime());
        msg.setDeviceId(device.getDeviceId());
        msg.setDeviceSn(deviceSn);
        msg.setOrgId(device.getOrgId());
        msg.setOnlineFlag(online);

        deviceOnlineStateManager.insert(msg);
        ServiceResponse<Boolean> res = ServiceResponse.createSuccessResponse();
        res.setData(Boolean.TRUE);
        return res;
    }
}
