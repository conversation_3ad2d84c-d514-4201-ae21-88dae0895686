package com.moredian.magicube.device.config;

import javax.sql.DataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;

/**
 * 苞米豆插件配置
 *
 * <AUTHOR>
 * @date 2019-10-21 20:07:42
 */

@Configuration
@EnableTransactionManagement
@MapperScan("com.moredian.magicube.device.dao.mapper")
public class MybatisPlusConfig {

    @Autowired
    private MybatisPlusAutoConfiguration mybatisPlusAutoConfiguration;

    /**
     * 使用baomidou代替原来的mybatis插件
     */
    @Bean("sqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        return mybatisPlusAutoConfiguration.sqlSessionFactory(dataSource);
    }
}
