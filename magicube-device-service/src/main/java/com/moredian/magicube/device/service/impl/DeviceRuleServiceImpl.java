package com.moredian.magicube.device.service.impl;

import static com.moredian.magicube.device.constant.DeviceIotPropertyConstants.JET_LINK_RULE_TYPE_RULE_ARRANGE;
import static com.moredian.magicube.device.constant.DeviceIotPropertyConstants.JET_LINK_RULE_TYPE_SCENE;
import static com.moredian.magicube.device.constant.DeviceIotPropertyConstants.OCCUPIED_TO_OCCUPY;
import static com.moredian.magicube.device.constant.DeviceTypeConstants.IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST;
import static com.moredian.magicube.device.constant.DeviceTypeConstants.IOT_VIRTUAL_DEVICE_TYPE_LIST;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.exception.ErrorContext;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.filemanager.FileManager;
import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.IIotDeviceInstanceService;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceInstance.req.QueryDeviceListRequest;
import com.moredian.bridge.api.service.deviceInstance.req.WriteDevicePropertyRequest;
import com.moredian.bridge.api.service.deviceProperty.IIotDevicePropertyService;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.bridge.api.service.scene.IRuleArrangementService;
import com.moredian.bridge.api.service.scene.ISceneInteractionRuleService;
import com.moredian.bridge.api.service.scene.dto.AddOrUpdateRuleArrangementDTO;
import com.moredian.bridge.api.service.scene.dto.AddSceneInteractionDTO;
import com.moredian.bridge.api.service.scene.dto.DeleteRuleArrangementDTO;
import com.moredian.bridge.api.service.scene.dto.DeleteSceneInteractionDTO;
import com.moredian.bridge.api.service.scene.dto.UpdateSceneInteractionDTO;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.config.ModeTriggerTemplateStrategyReader;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.dao.entity.RuleTemplate;
import com.moredian.magicube.device.dao.entity.SpaceRuleRelation;
import com.moredian.magicube.device.dto.device.DeviceRuleBaseDTO;
import com.moredian.magicube.device.dto.device.DeviceRuleDTO;
import com.moredian.magicube.device.dto.device.DeviceRuleTemplateDTO;
import com.moredian.magicube.device.dto.device.IotDeviceTypeDTO;
import com.moredian.magicube.device.dto.device.RuleBindSpaceDTO;
import com.moredian.magicube.device.dto.device.RuleDeleteDTO;
import com.moredian.magicube.device.dto.device.RuleDeviceTypeQueryDTO;
import com.moredian.magicube.device.dto.device.RuleQueryDTO;
import com.moredian.magicube.device.dto.device.RuleSpaceQueryDTO;
import com.moredian.magicube.device.dto.device.RuleTemplateQueryDTO;
import com.moredian.magicube.device.dto.device.SpaceDTO;
import com.moredian.magicube.device.dto.device.SpaceRuleUpdateDTO;
import com.moredian.magicube.device.dto.device.rule.QueryRuleConfigDTO;
import com.moredian.magicube.device.dto.device.rule.RuleConfigDTO;
import com.moredian.magicube.device.dto.device.rule.RuleLockDTO;
import com.moredian.magicube.device.enums.ModeTriggerEnum;
import com.moredian.magicube.device.enums.RuleDeviceTypeEnum;
import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.magicube.device.jetlinks.ruleArrange.RuleInstance;
import com.moredian.magicube.device.manager.DeviceRuleRelationManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.RuleManager;
import com.moredian.magicube.device.manager.RuleTemplateManager;
import com.moredian.magicube.device.manager.SpaceRuleRelationManager;
import com.moredian.magicube.device.service.DeviceRuleService;
import com.moredian.magicube.device.service.strategy.ModeTriggerTemplateParseStrategy;
import com.moredian.magicube.device.service.strategy.impl.HumanOnDeviceTemplateParseStrategy;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version $Id: DeviceRuleServiceImpl.java, v 1.0 Exp $
 */
@SI
@Slf4j
public class DeviceRuleServiceImpl implements DeviceRuleService {

    private static final Integer TRIGGER_VALUE_ONLY_CHANGE = 1;

    @Resource
    private RuleManager ruleManager;
    @Resource
    private RuleTemplateManager ruleTemplateManager;
    @Resource
    private SpaceRuleRelationManager spaceRuleRelationManager;
    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;
    @Resource
    private ModeTriggerTemplateStrategyReader modeTriggerTemplateStrategyReader;
    @Resource
    private FileManager fileManager;
    @Resource
    private DeviceRuleRelationManager deviceRuleRelationManager;
    @Resource
    private RedissonCacheComponent redissonCacheComponent;

    @SI
    private ISceneInteractionRuleService sceneInteractionRuleService;
    @SI
    private IIotDeviceInstanceService deviceInstanceService;
    @SI
    private SpaceTreeDeviceRelationService spaceTreeDeviceRelationService;
    @SI
    private IIotDevicePropertyService iotDevicePropertyService;
    @SI
    private IRuleArrangementService ruleArrangementService;
    @SI
    private IIotDeviceInstanceService iotDeviceInstanceService;
    @SI
    private IdgeneratorService idgeneratorService;

    @Override
    public ServiceResponse<List<DeviceRuleDTO>> getRuleList(RuleQueryDTO dto) {
        BizAssert.notNull(dto.getOrgId(), "orgId must be not null");

        Long orgId = dto.getOrgId();
        Long spaceId = dto.getSpaceId();
        Integer spaceType = dto.getSpaceType();
        Long deviceId = dto.getDeviceId();
        Wrapper<Rule> wrapper = Wrappers.query(new Rule()).eq(Rule::getOrgId, orgId)
                .eq(ObjectUtil.isNotEmpty(spaceType), Rule::getSpaceType, spaceType)
                .eq(Rule::getDelFlag, 0).orderByDesc(Rule::getGmtCreate);
        List<Rule> ruleList = ruleManager.list(wrapper);

        ServiceResponse<List<DeviceRuleDTO>> res = ServiceResponse.createSuccessResponse();
        if (CollectionUtils.isEmpty(ruleList)) {
            res.setData(Lists.newArrayList());
        }else {
            List<DeviceRuleDTO> deviceRuleDTOList = getDeviceRuleDTOList(orgId, ruleList, spaceId, deviceId);
            res.setData(deviceRuleDTOList);
        }
        return res;
    }

    @Override
    public ServiceResponse<List<DeviceRuleTemplateDTO>> getRuleDefaultTemplate(RuleTemplateQueryDTO dto) {
        Long orgId = dto.getOrgId();
        BizAssert.notNull(dto.getOrgId(), "orgId must be not null");
        List<DeviceRuleTemplateDTO> deviceRuleDTOList = getRuleDefaultTemplateList(orgId);
        return new ServiceResponse<>(deviceRuleDTOList);
    }

    @Override
    public ServiceResponse<List<IotDeviceTypeDTO>> getRuleDeviceTypes(
        RuleDeviceTypeQueryDTO dto) {
        List<IotDeviceTypeDTO> respList = Arrays.stream(RuleDeviceTypeEnum.values())
            .map(r -> {
                IotDeviceTypeDTO resp = new IotDeviceTypeDTO();
                resp.setDeviceType(r.getCode());
                resp.setDeviceTypeName(r.getDesc());
                return resp;
            }).collect(Collectors.toList());
        return new ServiceResponse<>(respList);
    }

    @Override
    public ServiceResponse<Boolean> updateSpaceRule(@Valid SpaceRuleUpdateDTO dto) {
        log.info("updateSpaceRule request: {}", dto);

        Long orgId = dto.getOrgId();
        Long ruleId = dto.getRuleId();
        Long spaceId = dto.getSpaceId();
        Integer state = dto.getState();

        Rule rule = ruleManager.getRule(orgId, ruleId);
        ModeTriggerEnum template = ModeTriggerEnum.getTemplate(rule.getModeType(), rule.getTriggerType());

        if (RuleStateEnum.DISABLE.getCode() == state && ModeTriggerEnum.SAVE_ENERGY_DEVICE_TRIGGER.equals(template)) {
            //删除规则空间
            deleteRuleSpace(orgId, ruleId, spaceId);
            return new ServiceResponse(Boolean.TRUE);
        }

        List<Long> spaceIdList = spaceRuleRelationManager.getSpaceIdByOrgIdAndRuleId(orgId, ruleId);

        if (RuleStateEnum.DISABLE.getCode() == state)  {
            spaceIdList.remove(spaceId);
        } else {

            ServiceResponse serviceResponse = checkSpace(orgId, rule, spaceId);
            if (serviceResponse != null) {
                return serviceResponse;
            }

            spaceIdList.add(spaceId);
        }
        List<Integer> dbDeviceTypes = JSONUtil.toList(JSONUtil.parseArray(rule.getDeviceType()),
            Integer.class);
        RuleBindSpaceDTO request = new RuleBindSpaceDTO()
            .setOrgId(orgId)
            .setRuleId(ruleId)
//            此处设备类型不变更，赋值处理。为空会进行默认填充处理
            .setDeviceTypes(RuleDeviceTypeEnum.convertRuleDeviceType(dbDeviceTypes))
            .setSpaceIdList(spaceIdList);
        return ruleBingSpace(request);
    }

    private ServiceResponse checkSpace(Long orgId, Rule rule, Long spaceId) {
        List<TreeDeviceRelationDTO> treeDeviceRelationList = getTreeDeviceReationList(orgId, rule, Lists.newArrayList(spaceId));
        RuleTemplate template = ruleTemplateManager.getTemplate(rule.getTemplateId());
        int existSpaceDevice = existSpaceDeviceForRule(treeDeviceRelationList, template);
        if (existSpaceDevice == 1) {
            return null;
        }

        if(existSpaceDevice == 0 || existSpaceDevice == 2) return new ServiceResponse(new ErrorContext("-1", "当前会议室没有绑定任何可控制的设备，无法启用此场景"));
        else if (existSpaceDevice == 3) {
            return new ServiceResponse(new ErrorContext("-1", "当前会议室没有绑定【人体感应传感器】类型设备，无法启用此场景"));
        }
        return null;
    }

    private void deleteRuleSpace(Long orgId, Long ruleId, Long spaceId) {
        List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgIdAndSpaceId(orgId, ruleId, spaceId);
        deleteSpaceRuleRelation(orgId, ruleId, spaceRuleRelationList);
    }

    private void deleteRuleDevice(Long orgId, Long ruleId, List<Long> spaceId) {
        ServiceResponse<List<TreeDeviceRelationDTO>> treeDeviceRelationResp = spaceTreeDeviceRelationService.listByTreeIdsAndSource(orgId, spaceId, null);
        List<Long> deviceIdList = Optional.ofNullable(treeDeviceRelationResp.getData()).orElse(Lists.newArrayList()).stream().map(TreeDeviceRelationDTO::getDeviceId).collect(Collectors.toList());

        deviceRuleRelationManager.deleteByOrgIdAndDeviceId(orgId, ruleId, deviceIdList);
    }

    @Override
    public ServiceResponse<Boolean> ruleBingSpace(RuleBindSpaceDTO request) {
        log.info("ruleBingSpace request: {}", request);
        request.checkParamAvailable();

        Long orgId = request.getOrgId();
        Long ruleId = request.getRuleId();
        List<Long> spaceIdList = request.getSpaceIdList();
        // 规则设备类型转换成物联网设备类型
        List<Integer> deviceTypes = RuleDeviceTypeEnum.convertDeviceType(request.getDeviceTypes());
        String ruleName = request.getRuleName();

        Rule rule;
        if (ruleId == null) {
            //新增操作
            RuleTemplate template = ruleTemplateManager.getTemplate(request.getTemplateId());
            Long insertId = ruleManager.insert(orgId, template, deviceTypes, ruleName, request.getSpaceType(), request.getHumanPriority());
            rule = ruleManager.getRule(orgId, insertId);
        } else {
            Integer requestSource = request.getRequestSource();
            // 不是消息推送需要加上锁限制
            if (requestSource == null || requestSource != 2){
                // 如果是更新操作，需要加上一分钟限制
                String key = RedisConstants.getKey(RedisConstants.JETLINK_RULE_ARRANGE, ruleId);
                // 如果存在提示用户不能频繁修改
                Long ruleIdRedis = (Long) redissonCacheComponent.getObjectCache(key);
                BizAssert.isTrue(ObjectUtil.isEmpty(ruleIdRedis), "不能频繁修改规则，请一分钟后再试");
            }
            //更新操作
            rule = ruleManager.getRule(orgId, ruleId);
            // 如果人为干预修改为false，需要删除规则下的所有锁
            Boolean humanPriority = request.getHumanPriority();
            if (Boolean.FALSE.equals(humanPriority)) {
                deleteRuleLock(ruleId);
            }
            rule.setHumanPriority(humanPriority);
        }
        if (StringUtils.isNotBlank(request.getTriggerValue())) {
            rule.setTriggerValue(wrapperUpdateTriggerValue(request.getTriggerValue(), rule));
        }

        if (StringUtils.isNotBlank(ruleName)) {
            rule.setRuleName(ruleName);
        }

        if (CollectionUtils.isNotEmpty(deviceTypes)) {
            if (!Objects.equals(JSONUtil.toJsonStr(deviceTypes), rule.getDeviceType())) {
                rule.setDeviceType(JSONUtil.toJsonStr(deviceTypes));
            }
        } else {
            deviceTypes = JSONUtil.toList(JSONUtil.parseArray(rule.getDeviceType()), Integer.class);
        }

        //只是修改了触发值，没有操作规则关联空间，这时候关联空间中的设备应该已最新触发值为准
        Integer triggerValueOnlyChange = request.getTriggerValueOnlyChange();
        if (Objects.equals(TRIGGER_VALUE_ONLY_CHANGE, triggerValueOnlyChange)) {
            List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgIdAndRuleId(orgId, ruleId);
            spaceIdList = spaceRuleRelationList.stream().map(SpaceRuleRelation::getSpaceId).collect(Collectors.toList());
            log.info("只修改触发设备值，orgId: {} ,ruleId: {} ,spaceIdList: {}", orgId, ruleId, spaceIdList);
        }

        if (CollectionUtils.isEmpty(spaceIdList)) {
            deleteRuleRelatedAllSpace(orgId, rule);
            log.info("删除规则关联全部空间，orgId: {} ,ruleId: {} ,spaceIdList: {}", orgId, ruleId, spaceIdList);
            return new ServiceResponse<>(Boolean.TRUE);
        }

        List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgIdAndRuleId(orgId, rule.getRuleId());

        //1、 查询空间设备关系
        List<TreeDeviceRelationDTO> treeDeviceRelationList = getTreeDeviceReationList(orgId, rule,
            spaceIdList);
        if (CollectionUtils.isEmpty(treeDeviceRelationList)) {
            if (spaceIdList.size() == 1) {
                //关联设备为空，且关联一个空间，场景删除
                rule.setDelFlag(YesNoFlag.YES.getValue());
            }
            deleteRuleRelatedAllSpace(orgId, rule);
            log.info("没有找到空间关联的设备，orgId: {} ,ruleId: {} ,spaceIdList: {}", orgId, ruleId,
                spaceIdList);
            return new ServiceResponse<>(Boolean.TRUE);
        } else {
            handlerNoTriggerDeviceType(orgId, rule, treeDeviceRelationList, spaceIdList);
        }

        //2、 推送场景规则
        log.info("orgId: {} ,ruleId: {} ,treeDeviceRelationList: {}", orgId, ruleId, treeDeviceRelationList);
        addSceneRule(orgId, rule, treeDeviceRelationList, deviceTypes, spaceIdList, spaceRuleRelationList);

        return new ServiceResponse<>(Boolean.TRUE);
    }

    /**
     * 删除规则锁
     */
    private void deleteRuleLock(Long ruleId) {
        String key = RedisConstants.getKey(RedisConstants.JETLINK_RULE_ARRANGE_LOCK, ruleId);
        redissonCacheComponent.delObjectCache(key);
    }

    @Override
    public ServiceResponse<Boolean> ruleLock(RuleLockDTO dto) {
        ServiceResponse<Boolean> res = ServiceResponse.createSuccessResponse();
        // 0：解锁，1：加锁
        Integer lockType = dto.getLockType();
        Long ruleId = dto.getRuleId();
        BizAssert.notNull(lockType, "lockType must be not null");
        BizAssert.notNull(ruleId, "ruleId must be not null");
        String key = RedisConstants.getKey(RedisConstants.JETLINK_RULE_ARRANGE_LOCK, ruleId);
        // （加锁）规则编排中开启设备会给规则加锁
        if (lockType == 1) {
            redissonCacheComponent.setObjectCache(key, ruleId);
        }else if (lockType == 0) {
            // （解锁）持续检测到无人，会掉这个接口去解锁
            redissonCacheComponent.delObjectCache(key);
        }
        res.setData(Boolean.TRUE);
        return res;
    }

    @Override
    public ServiceResponse<RuleConfigDTO> queryRuleConfig(QueryRuleConfigDTO dto) {
        Long ruleId = dto.getRuleId();
        BizAssert.notNull(ruleId, "ruleId must be not null");

        Rule rule = ruleManager.getByRuleId(ruleId);
        Boolean humanPriority = rule.getHumanPriority();
        RuleConfigDTO data = new RuleConfigDTO();
        data.setHumanPriority(humanPriority);
        data.setRuleId(ruleId);

        // 查询规则是否有锁
        String key = RedisConstants.getKey(RedisConstants.JETLINK_RULE_ARRANGE_LOCK, ruleId);
        Object lock = redissonCacheComponent.getObjectCache(key);
        data.setLock(ObjectUtil.isNotNull(lock));

        ServiceResponse<RuleConfigDTO> res = ServiceResponse.createSuccessResponse();
        res.setData(data);
        return res;
    }


    private void handlerNoTriggerDeviceType(Long orgId, Rule rule,
        List<TreeDeviceRelationDTO> treeDeviceRelationList, List<Long> spaceIdList) {
        ModeTriggerEnum modeTriggerEnum = ModeTriggerEnum.getTemplate(rule.getModeType(),
            rule.getTriggerType());
        boolean needTriggerDeviceType = modeTriggerEnum != null && modeTriggerEnum.equals(
            ModeTriggerEnum.SAVE_ENERGY_DEVICE_TRIGGER);
        if(!needTriggerDeviceType){
            return;
        }
        //存在，但无传感器||只有传感器,无可关设备
        boolean existTriggerDeviceType = treeDeviceRelationList.stream()
            .anyMatch(x -> deviceTypePropertyManager.containsDeviceType(
                IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST, x.getDeviceType()));
        treeDeviceRelationList = treeDeviceRelationList.stream()
            .filter(v -> !deviceTypePropertyManager.containsDeviceType(
                IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST, v.getDeviceType()))
            .collect(Collectors.toList());
        if (!existTriggerDeviceType || CollectionUtils.isEmpty(treeDeviceRelationList)) {
            if (spaceIdList.size() == 1) {
                //关联设备为空，且关联一个空间，场景删除
                rule.setDelFlag(YesNoFlag.YES.getValue());
            }
            deleteRuleRelatedAllSpace(orgId, rule);
        }
    }

    private String wrapperUpdateTriggerValue(String triggerValue, Rule rule) {
        ModeTriggerEnum template = ModeTriggerEnum.getTemplate(rule.getModeType(), rule.getTriggerType());
        if (ModeTriggerEnum.SAVE_ENERGY_TIMER_TRIGGER.equals(template)) {
            return triggerValue + ":00";
        }
        return triggerValue;
    }

    private String wrapperQueryTriggerValue(String triggerValue, Integer modeType, Integer triggerType) {
        ModeTriggerEnum template = ModeTriggerEnum.getTemplate(modeType, triggerType);
        if (ModeTriggerEnum.SAVE_ENERGY_TIMER_TRIGGER.equals(template)) {
            return triggerValue.substring(0, triggerValue.lastIndexOf(":"));
        }
        return triggerValue;
    }

    private void deleteRuleRelatedAllSpace(Long orgId, Rule rule) {
        ModeTriggerEnum modeTriggerEnum = ModeTriggerEnum.getTemplate(rule.getModeType(), rule.getTriggerType());
        if (modeTriggerEnum.equals(ModeTriggerEnum.SAVE_ENERGY_TIMER_TRIGGER)) {

            if (StringUtils.isNotBlank(rule.getSceneId())) {
                deleteScene(orgId, rule.getSceneId());
            }
        } else if (modeTriggerEnum.equals(ModeTriggerEnum.SAVE_ENERGY_DEVICE_TRIGGER)
        || modeTriggerEnum.equals(ModeTriggerEnum.USE_ENERGY_HUMAN_TRIGGER)) {
            List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgIdAndRuleId(orgId, rule.getRuleId());
            deleteSceneOrRuleArrange(orgId, spaceRuleRelationList);
        }
        ruleManager.updateRuleAndDevice(orgId, rule, null, null);
    }

    private void deleteScene(Long orgId, String sceneId) {
        DeleteSceneInteractionDTO deleteRequest = new DeleteSceneInteractionDTO();
        deleteRequest.setOrgId(orgId);
        deleteRequest.setId(sceneId);
        sceneInteractionRuleService.deleteSceneInteractionRule(deleteRequest);
    }

    /**
     * 删除规则编排，先停止规则，然后延迟3s后删除规则
     */
    private void deleteRuleArrange(Long orgId, String ruleArrangeId) {
        DeleteRuleArrangementDTO deleteRequest = new DeleteRuleArrangementDTO();
        deleteRequest.setOrgId(orgId);
        deleteRequest.setId(ruleArrangeId);
        ruleArrangementService.stopRuleArrangement(orgId, ruleArrangeId);
        CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 删除规则编排
            Boolean deleteFlag = ruleArrangementService.deleteRuleArrangement(deleteRequest)
                .pickDataThrowException();
            if (Boolean.FALSE.equals(deleteFlag)){
                log.error("deleteRuleArrange delete fail: {}", deleteFlag);
            }
            log.info("startRuleArrangement result: {}", deleteFlag);
            return "result";
        });

    }

    private void addSceneRule(Long orgId, Rule rule,
        List<TreeDeviceRelationDTO> treeDeviceReationList, List<Integer> selectDeviceTypes, List<Long> spaceIdListTemp,
        List<SpaceRuleRelation> spaceRuleRelationList) {
        ModeTriggerEnum modeTriggerEnum = ModeTriggerEnum.getTemplate(rule.getModeType(), rule.getTriggerType());
        if (modeTriggerEnum == null) {
            return;
        }

        if (modeTriggerEnum.equals(ModeTriggerEnum.SAVE_ENERGY_TIMER_TRIGGER)) {
            Object sceneRuleObj = getSceneRule(orgId, rule, modeTriggerEnum, treeDeviceReationList, selectDeviceTypes);
            String sceneId = rule.getSceneId();
            if (sceneRuleObj != null) {
                String sceneRule = String.valueOf(sceneRuleObj);
                if (StringUtils.isBlank(sceneId)) {
                    sceneId = addScene(orgId, sceneRule);
                    rule.setSceneId(sceneId);
                    rule.setRuleJson(sceneRule);
                } else {
                    updateSceneRule(orgId, sceneId, sceneRule);
                    rule.setRuleJson(sceneRule);
                }
            }
            treeDeviceReationList = treeDeviceReationList.stream()
                //只要支持场景的
                .filter(v -> RuleDeviceTypeEnum.containDeviceType(v.getDeviceType()))
                .filter(v -> selectDeviceTypes.contains(v.getDeviceType()))
                .collect(Collectors.toList());

            List<Long> spaceIdList = treeDeviceReationList.stream().map(TreeDeviceRelationDTO::getTreeId).distinct().collect(Collectors.toList());
            List<Long> deviceIdList = treeDeviceReationList.stream().map(TreeDeviceRelationDTO::getDeviceId).collect(Collectors.toList());

            //更新规则关联空间，关联设备
            ruleManager.updateRuleAndDevice(orgId, rule, spaceIdList, deviceIdList);
        } else if (modeTriggerEnum.equals(ModeTriggerEnum.SAVE_ENERGY_DEVICE_TRIGGER) ||
                modeTriggerEnum.equals(ModeTriggerEnum.USE_ENERGY_HUMAN_TRIGGER)) {
            //Map<spaceId, sceneRule>
            Map<Long, RuleInstance> spaceSceneRuleMap = (Map<Long, RuleInstance>)getSceneRule(orgId, rule, modeTriggerEnum, treeDeviceReationList, selectDeviceTypes);
            if (spaceSceneRuleMap == null || spaceSceneRuleMap.isEmpty()) {
                log.info("节能无人检测场景下组装JetLinks场景失败，orgId: {} ,rule: {} ,modeTriggerEnum: {}",
                        orgId, rule, modeTriggerEnum);
                List<Long> spaceIdList = treeDeviceReationList.stream().map(TreeDeviceRelationDTO::getTreeId).distinct().collect(Collectors.toList());
                List<Long> deviceIdList = treeDeviceReationList.stream().map(TreeDeviceRelationDTO::getDeviceId).collect(Collectors.toList());
                //新增或修改空间规则
                addOrUpdateSpaceRuleRelation(orgId, rule, spaceSceneRuleMap, spaceIdListTemp, spaceRuleRelationList);
                //更新规则关联的设备
                ruleManager.updateRuleAndDevice(orgId, rule, deviceIdList);
                return;
            }
            //符合规则条件的空间ID列表
            List<Long> spaceIdList = new ArrayList<>(spaceSceneRuleMap.keySet());
            //得到关联的设备Id和触发设备信息
            List<Long> deviceIdList = Lists.newArrayList();
            List<String> triggerDeviceSnList = Lists.newArrayList();
            //符合规则条件的空间ID列表
            updateIotTriggerDeviceProperty(orgId, rule, spaceIdList, treeDeviceReationList, deviceIdList, triggerDeviceSnList);

            //新增或修改空间规则
            addOrUpdateSpaceRuleRelation(orgId, rule, spaceSceneRuleMap, spaceIdListTemp, spaceRuleRelationList);

            treeDeviceReationList = treeDeviceReationList.stream()
                //只要支持场景的
                .filter(v -> RuleDeviceTypeEnum.containDeviceType(v.getDeviceType()))
                .filter(v -> selectDeviceTypes.contains(v.getDeviceType()))
                .collect(Collectors.toList());
            deviceIdList = treeDeviceReationList.stream().map(TreeDeviceRelationDTO::getDeviceId).collect(Collectors.toList());
            //更新规则关联的设备
            ruleManager.updateRuleAndDevice(orgId, rule, deviceIdList);
        }
    }

    private void updateIotTriggerDeviceProperty(Long orgId, Rule rule,List<Long> spaceIdList,
        List<TreeDeviceRelationDTO> treeDeviceReationList, List<Long> deviceIdList,List<String> triggerDeviceSnList){
        getDeviceIdAndTriggerDeviceSn(treeDeviceReationList, spaceIdList, deviceIdList, triggerDeviceSnList);
        //更新iot触发设备属性
//        updateIotTriggerDeviceProperty(orgId, rule, triggerDeviceSnList);
    }

    private void getDeviceIdAndTriggerDeviceSn(List<TreeDeviceRelationDTO> treeDeviceReationList, List<Long> spaceIdList, List<Long> deviceIdList, List<String> triggerDeviceSnList) {
        treeDeviceReationList.forEach(
            v->{
                if (spaceIdList.contains(v.getTreeId())) {
                    deviceIdList.add(v.getDeviceId());

                    if (deviceTypePropertyManager.containsDeviceType(IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST, v.getDeviceType())) {
                        triggerDeviceSnList.add(v.getDeviceSn());
                    }
                }
            }
        );
    }

    private void updateIotTriggerDeviceProperty(Long orgId, Rule rule, List<String> triggerDeviceSnList) {
        if (CollectionUtils.isEmpty(triggerDeviceSnList)) {
            return;
        }
        for (String triggerDeviceSn : triggerDeviceSnList) {

            WriteDevicePropertyRequest request = new WriteDevicePropertyRequest();
            request.setOrgId(orgId);
            request.setDeviceId(triggerDeviceSn);

            //单位分钟：triggerValue * 魔链平台比率(600)
            BigDecimal multiply = new BigDecimal(rule.getTriggerValue()).multiply(
                new BigDecimal("600"));
            Map<String, Object> propertyMap = new HashMap<>(1);
            propertyMap.put(OCCUPIED_TO_OCCUPY, multiply.doubleValue());
            request.setPropertyMap(propertyMap);
            ServiceResponse<Boolean> serviceResponse = iotDeviceInstanceService.writeDeviceProperty(request);
            log.info("设置设备属性，isSuccess: {}, propertyMap: {}", serviceResponse.isSuccess(), JsonUtils.toJson(request));
        }
    }

    private void addOrUpdateSpaceRuleRelation(Long orgId, Rule rule, Map<Long, RuleInstance> spaceSceneRuleMap,
        List<Long> spaceIdList, List<SpaceRuleRelation> spaceRuleRelationListTemp) {
        //Map<spaceId, SpaceRuleRelation>
        Map<Long, SpaceRuleRelation> spaceRuleRelationMap = getSpaceRuleRelationMap(orgId, rule.getRuleId());
        List<Long> spaceIdTemp = spaceRuleRelationListTemp.stream().map(SpaceRuleRelation::getSpaceId)
            .collect(Collectors.toList());

        //新增修改空间关系
        List<SpaceRuleRelation> spaceRuleRelationList = addOrUpdateSceneRuleAndReturnRelation(orgId, rule.getRuleId(), spaceSceneRuleMap, spaceRuleRelationMap);
        List<Long> spaceIds = spaceRuleRelationList.stream().map(SpaceRuleRelation::getSpaceId)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(spaceRuleRelationList) || ObjectUtil.isNotEmpty(spaceIdList)) {
            for (Long item : spaceIdList) {
                if(!spaceIdTemp.contains(item) && !spaceIds.contains(item)){
                    SpaceRuleRelation spaceRuleRelation = new SpaceRuleRelation();
                    spaceRuleRelation.setOrgId(orgId);
                    spaceRuleRelation.setRuleId(rule.getRuleId());
                    spaceRuleRelation.setSpaceId(item);
                    spaceRuleRelationList.add(spaceRuleRelation);
                }
            }
            spaceRuleRelationManager.addOrUpdate(spaceRuleRelationList);
        }


        //删除空间关系
        deleteSpaceRuleRelation(orgId, rule.getRuleId(), spaceIdList, spaceRuleRelationMap);
    }

    private void deleteSpaceRuleRelation(Long orgId, Long ruleId, List<Long> spaceIdList, Map<Long, SpaceRuleRelation> spaceRuleRelationMap) {
        if (spaceRuleRelationMap == null || spaceRuleRelationMap.isEmpty()) {
            log.info("此规则未绑定过空间");
            return;
        }
        List<SpaceRuleRelation> deleteSpaceRuleRelationList = spaceRuleRelationMap.entrySet().stream()
                .filter(v -> spaceIdList == null || !spaceIdList.contains(v.getKey())).map(v -> v.getValue()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deleteSpaceRuleRelationList)) {
            return;
        }

        deleteSpaceRuleRelation(orgId, ruleId, deleteSpaceRuleRelationList);
    }

    private void deleteSpaceRuleRelation(Long orgId, Long ruleId, List<SpaceRuleRelation> deleteSpaceRuleRelationList) {
        deleteSceneOrRuleArrange(orgId, deleteSpaceRuleRelationList);

        //删除规则空间关系
        List<Long> spaceRuleRelationIdList = deleteSpaceRuleRelationList.stream().map(SpaceRuleRelation::getSpaceRuleRelationId).collect(Collectors.toList());
        spaceRuleRelationManager.delete(orgId, spaceRuleRelationIdList);

        List<Long> spaceIdList = deleteSpaceRuleRelationList.stream().map(SpaceRuleRelation::getSpaceId).collect(Collectors.toList());
        deleteRuleDevice(orgId, ruleId, spaceIdList);
    }

    private void deleteSceneOrRuleArrange(Long orgId, List<SpaceRuleRelation> deleteSpaceRuleRelationList) {
        for (SpaceRuleRelation spaceRuleRelation : deleteSpaceRuleRelationList) {
            if (StringUtils.isNotBlank(spaceRuleRelation.getSceneId())) {
                deleteScene(orgId, spaceRuleRelation.getSceneId());
            }
            if (StringUtils.isNotBlank(spaceRuleRelation.getRuleArrangeId())) {
                deleteRuleArrange(orgId, spaceRuleRelation.getRuleArrangeId());
            }
        }
    }

    private Map<Long, SpaceRuleRelation> getSpaceRuleRelationMap(Long orgId, Long ruleId) {
        List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgIdAndRuleId(orgId, ruleId);
        Map<Long, SpaceRuleRelation> spaceRuleRelationMap = spaceRuleRelationList.stream().collect(Collectors.toMap(SpaceRuleRelation::getSpaceId, v -> v));
        return spaceRuleRelationMap;
    }

    private List<SpaceRuleRelation> addOrUpdateSceneRuleAndReturnRelation(Long orgId, Long ruleId, Map<Long, RuleInstance> spaceSceneRuleMap, Map<Long, SpaceRuleRelation> spaceRuleRelationMap) {
        if (spaceSceneRuleMap == null || spaceSceneRuleMap.isEmpty()) {
            log.info("场景规则为空");
            return new ArrayList<>();
        }
        List<SpaceRuleRelation> updateSpaceRuleRelationList = Lists.newArrayList();

        Long spaceId;
        RuleInstance ruleInstance;
        Boolean sceneOrRuleArrangeNoExist;
        SpaceRuleRelation spaceRuleRelation;
        //判断选择的空间是否有了场景规则，进而新增场景规则或更新场景规则
        for (Entry<Long, RuleInstance> spaceSceneRuleEntry : spaceSceneRuleMap.entrySet()) {

            spaceId = spaceSceneRuleEntry.getKey();
            ruleInstance = spaceSceneRuleEntry.getValue();
            String ruleType = ruleInstance.getRuleType();

            spaceRuleRelation = (spaceRuleRelationMap == null || spaceRuleRelationMap.isEmpty()) ? null : spaceRuleRelationMap.get(spaceId);

            sceneOrRuleArrangeNoExist = (spaceRuleRelation == null) ? true :
                            (   JET_LINK_RULE_TYPE_SCENE.equals(ruleType)
                                    &&
                                StringUtils.isBlank(spaceRuleRelation.getSceneId())
                            )
                            ||
                            ( JET_LINK_RULE_TYPE_RULE_ARRANGE.equals(ruleType)
                                    &&
                                StringUtils.isBlank(spaceRuleRelation.getRuleArrangeId())
                            );

            if (sceneOrRuleArrangeNoExist) {
                if (spaceRuleRelation == null) {
                    spaceRuleRelation = new SpaceRuleRelation();
                    spaceRuleRelation.setOrgId(orgId);
                    spaceRuleRelation.setRuleId(ruleId);
                    spaceRuleRelation.setSpaceId(spaceId);
                }
                ruleInstance.setModelMeta(ruleInstance.getModelMeta().replaceAll("#\\{ruleArrangeId}", String.valueOf(ruleId)));
                //新增JetLinks场景
                addSceneOrRuleArrange(orgId, ruleInstance, spaceRuleRelation, ruleId);
            } else  {
                ruleInstance.setModelMeta(ruleInstance.getModelMeta().replaceAll("#\\{ruleArrangeId}", String.valueOf(ruleId)));
                //更新JetLinks场景
                updateSceneOrRuleArrange(orgId, ruleInstance, spaceRuleRelation, ruleId);
            }
            updateSpaceRuleRelationList.add(spaceRuleRelation);
        }
        return updateSpaceRuleRelationList;
    }

    private Boolean updateSceneRule(Long orgId, String sceneId, String sceneRule) {
        UpdateSceneInteractionDTO updateSceneInteraction = new UpdateSceneInteractionDTO();
        updateSceneInteraction.setId(sceneId);
        updateSceneInteraction.setOrgId(orgId);
        updateSceneInteraction.setRuleMetaData(String.valueOf(sceneRule));
        ServiceResponse<Boolean> serviceResponse = sceneInteractionRuleService.updateSceneInteractionRule(updateSceneInteraction);
        return serviceResponse.getData();
    }

    private void updateSceneOrRuleArrange(Long orgId, RuleInstance ruleInstance, SpaceRuleRelation spaceRuleRelation,
        Long ruleId) {
        if (JET_LINK_RULE_TYPE_SCENE.equals(ruleInstance.getRuleType())) {
            UpdateSceneInteractionDTO updateSceneInteraction = new UpdateSceneInteractionDTO();
            updateSceneInteraction.setId(spaceRuleRelation.getSceneId());
            updateSceneInteraction.setOrgId(orgId);
            updateSceneInteraction.setRuleMetaData(ruleInstance.getModelMeta());
            sceneInteractionRuleService.updateSceneInteractionRule(updateSceneInteraction);

            spaceRuleRelation.setRuleJson(ruleInstance.getModelMeta());
        } else if (JET_LINK_RULE_TYPE_RULE_ARRANGE.equals(ruleInstance.getRuleType())) {
            AddOrUpdateRuleArrangementDTO ruleArrangementDTO = new AddOrUpdateRuleArrangementDTO();

            ruleArrangementDTO.setModelId(ruleInstance.getModelId());
            ruleArrangementDTO.setName(ruleInstance.getName());
            ruleArrangementDTO.setDescription(ruleInstance.getDescription());
            ruleArrangementDTO.setModelType(ruleInstance.getModelType());
            ruleArrangementDTO.setModelMeta(ruleInstance.getModelMeta());
            ruleArrangementDTO.setModelVersion(ruleInstance.getModelVersion());
            ruleArrangementDTO.setCreateTime(ruleInstance.getCreateTime());
            ruleArrangementDTO.setCreatorId(ruleInstance.getCreatorId());
            ruleArrangementDTO.setState(ruleInstance.getState());
            ruleArrangementDTO.setCreatorName(ruleInstance.getCreatorName());

            ruleArrangementDTO.setId(spaceRuleRelation.getRuleArrangeId());
            ruleArrangementDTO.setOrgId(orgId);
            ruleArrangementService.updateRuleArrangement(ruleArrangementDTO);
            startRuleArrange(orgId, spaceRuleRelation.getRuleArrangeId(), ruleId);
            spaceRuleRelation.setRuleArrangeJson(JSON.toJSONString(ruleInstance));
        }
    }


    /**
     * todo:先异步调用接口启动规则，后面优化成延迟队列
     * 加锁处理，限制用户频繁修改规则引擎，编辑和启动产生锁竞争导致超时
     */
    private void startRuleArrange(Long orgId, String ruleArrangeId, Long ruleId){
        // 如果是更新操作，需要加上一分钟限制
        String key = RedisConstants.getKey(RedisConstants.JETLINK_RULE_ARRANGE, ruleId);
        // 设置一分钟过期时间
        redissonCacheComponent.setObjectCache(key, ruleId, 1L, TimeUnit.MINUTES);
        CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 启动规则编排
            Boolean startRuleArrangementResp =  ruleArrangementService.startRuleArrangement(orgId
                ,ruleArrangeId).pickDataThrowException();
            if (Boolean.FALSE.equals(startRuleArrangementResp)){
                log.error("startRuleArrangement start fail: {}", startRuleArrangementResp);
            }
            log.info("startRuleArrangement result: {}", startRuleArrangementResp);
            return "result";
        });
    }


    private void addSceneOrRuleArrange(Long orgId, RuleInstance ruleInstance, SpaceRuleRelation spaceRuleRelation
    , Long ruleId) {

        if (JET_LINK_RULE_TYPE_SCENE.equals(ruleInstance.getRuleType())) {
            String sceneId = addScene(orgId, ruleInstance.getModelMeta());

            spaceRuleRelation.setRuleJson(ruleInstance.getModelMeta());
            spaceRuleRelation.setSceneId(sceneId);

        } else if (JET_LINK_RULE_TYPE_RULE_ARRANGE.equals(ruleInstance.getRuleType())) {
            AddOrUpdateRuleArrangementDTO ruleArrangementDTO = new AddOrUpdateRuleArrangementDTO();

            ruleArrangementDTO.setModelId(ruleInstance.getModelId());
            ruleArrangementDTO.setName(ruleInstance.getName());
            ruleArrangementDTO.setDescription(ruleInstance.getDescription());
            ruleArrangementDTO.setModelType(ruleInstance.getModelType());
            ruleArrangementDTO.setModelMeta(ruleInstance.getModelMeta());
//            ruleArrangementDTO.setModelVersion(ruleInstance.getModelVersion());
//            ruleArrangementDTO.setCreateTime(ruleInstance.getCreateTime());
//            ruleArrangementDTO.setCreatorId(ruleInstance.getCreatorId());
//            ruleArrangementDTO.setState(ruleInstance.getState());
//            ruleArrangementDTO.setCreatorName(ruleInstance.getCreatorName());

            ruleArrangementDTO.setOrgId(orgId);
            //新增规则编排
            ServiceResponse<String> addRuleArrangementResp = ruleArrangementService.addRuleArrangement(ruleArrangementDTO);
            log.info("addRuleArrangement isSuccess: {} ,message: {}", addRuleArrangementResp
                    .isSuccess(), spaceRuleRelation.setRuleArrangeJson(JSON.toJSONString(ruleInstance)),
                spaceRuleRelation.setRuleArrangeId(addRuleArrangementResp.getData()));
            startRuleArrange(orgId, addRuleArrangementResp.getData(), ruleId);
        }
    }

    private String addScene(Long orgId, String ruleInstance) {
        //创建场景
        AddSceneInteractionDTO sceneInteraction = new AddSceneInteractionDTO();
        sceneInteraction.setOrgId(orgId);
        sceneInteraction.setRuleMetaData(ruleInstance);
        ServiceResponse<String> addSceneRuleResp = sceneInteractionRuleService.addSceneInteractionRule(sceneInteraction);
        log.info("addSceneInteractionRule isSuccess: {} ,message: {}", addSceneRuleResp.isSuccess(),
                addSceneRuleResp.getErrorContext() == null ? "" : addSceneRuleResp.getErrorContext().getMessage());
        //启动场景
        ServiceResponse<Boolean> startSceneResp = sceneInteractionRuleService.startSceneInteractionRule(orgId, addSceneRuleResp.getData());
        log.info("startSceneInteractionRule isSuccess: {} ,message: {}", startSceneResp.isSuccess(),
                startSceneResp.getErrorContext() == null ? "" : startSceneResp.getErrorContext().getMessage());
        return addSceneRuleResp.getData();
    }

    private List<TreeDeviceRelationDTO> getTreeDeviceReationList(Long orgId, Rule rule, List<Long> spaceIdList) {
        List<Integer> deviceTypeList = getDeviceTypeForRule(rule.getTemplateId());
        if (CollectionUtils.isEmpty(deviceTypeList)) {
            return null;
        }
        ServiceResponse<List<TreeDeviceRelationDTO>> treeDeviceResp = spaceTreeDeviceRelationService.listByTreeIdAndDeviceType(orgId, spaceIdList, deviceTypeList);
        return treeDeviceResp.getData();
    }

    private Object getSceneRule(Long orgId, Rule rule, ModeTriggerEnum templateEnum,
        List<TreeDeviceRelationDTO> treeDeviceReationList,
        List<Integer> selectDeviceTypes) {
        List<String> deviceSnList = treeDeviceReationList.stream().map(
                v-> deviceTypePropertyManager.containsDeviceType(IOT_VIRTUAL_DEVICE_TYPE_LIST, v.getDeviceType())  ? v.getParentDeviceSn() : v.getDeviceSn()
        ).distinct().collect(Collectors.toList());

        //获取JetLinks设备信息
        Map<String, IotDeviceInfoDTO> iotDeviceMap = getIotDeviceMap(orgId, deviceSnList);
        log.info("orgId: {} ,iotDeviceMap: {}", orgId, iotDeviceMap);

        if (iotDeviceMap == null || iotDeviceMap.isEmpty()) {
            log.error("查询不到JetLinks设备信息，orgId: {} ,parentDeviceSnList:{}", orgId, deviceSnList);
            return null;
        }

        Map<String, BatchIotDevicePropertyInfoDTO> iotDevicePropertyMap = getIotDevicePropertyMap(orgId, deviceSnList);
        log.info("orgId: {} ,iotDevicePropertyMap: {}", orgId, iotDevicePropertyMap);

        ModeTriggerTemplateParseStrategy templateParseStrategy = modeTriggerTemplateStrategyReader.getStrategyByTemplateEnum(templateEnum);
        Object sceneRule = templateParseStrategy.getSceneRule(rule, treeDeviceReationList, iotDeviceMap, iotDevicePropertyMap, selectDeviceTypes);
        return sceneRule;
    }

    private Map<String, BatchIotDevicePropertyInfoDTO> getIotDevicePropertyMap(Long orgId, List<String> iotDeviceIdList) {
        ServiceResponse<List<BatchIotDevicePropertyInfoDTO>> devicePropertyInfoResp = iotDevicePropertyService.batchGetDevicePropertyDefineList(orgId, iotDeviceIdList);
        return devicePropertyInfoResp.isSuccess() ? devicePropertyInfoResp.getData().stream().collect(Collectors.toMap(BatchIotDevicePropertyInfoDTO::getId, v -> v, (k1, k2) -> k1)) : null;
    }

    private Map<String, IotDeviceInfoDTO> getIotDeviceMap(Long orgId, List<String> deviceSnList) {
        QueryDeviceListRequest iotDeviceRequest = new QueryDeviceListRequest();
        iotDeviceRequest.setOrgId(orgId);
        iotDeviceRequest.setIds(deviceSnList);
        ServiceResponse<List<IotDeviceInfoDTO>> deviceInfoResp = deviceInstanceService.queryDeviceInfoListByCondition(iotDeviceRequest);
        return deviceInfoResp.isSuccess() ? deviceInfoResp.getData().stream().collect(Collectors.toMap(IotDeviceInfoDTO::getId, v -> v, (k1, k2) -> k1)) : null;
    }


    @Override
    public ServiceResponse<List<SpaceDTO>> getRuleRelatableSpace(RuleSpaceQueryDTO dto) {
        dto.checkParamAvailable();
        Long orgId = dto.getOrgId();
        Long ruleId = dto.getRuleId();
        Long templateId = dto.getTemplateId();
        Integer spaceType = dto.getSpaceType();

        Rule rule = ruleManager.getRule(orgId, ruleId);
        if (rule != null) {
            templateId = rule.getTemplateId();
        }

        //根据空间类型|规则查询 空间设备关系数据
        List<TreeDeviceRelationDTO> treeDeviceRelationList =
            getTreeDeviceRelationByRuleAndSpaceType(orgId, templateId, dto.getSpaceName(), spaceType);

        //过滤出符合规则的空间数据
        List<SpaceDTO> spaceDTOList = getSpaceWith(treeDeviceRelationList, templateId);
        return new ServiceResponse<>(spaceDTOList);
    }

    @Override
    public ServiceResponse<Boolean> deleteRule(RuleDeleteDTO dto) {
        Long orgId = dto.getOrgId();
        Long ruleId = dto.getRuleId();
        Rule rule = ruleManager.getRule(orgId, ruleId);
        BizAssert.notNull(rule, "该场景不存在");
        rule.setDelFlag(YesNoFlag.YES.getValue());
        deleteRuleRelatedAllSpace(orgId, rule);
        log.info("删除规则关联全部空间，orgId: {} ,ruleId: {}", orgId, ruleId);
        return new ServiceResponse<>(Boolean.TRUE);
    }

    private List<SpaceDTO> getSpaceWith(List<TreeDeviceRelationDTO> treeDeviceRelationList, Long templateId) {
        List<SpaceDTO> spaceDTOList = Lists.newArrayList();

        SpaceDTO spaceDTO;
        Map<Long, List<TreeDeviceRelationDTO>> treeDeviceMap = treeDeviceRelationList.stream().collect(Collectors.groupingBy(TreeDeviceRelationDTO::getTreeId));
        for (Entry<Long, List<TreeDeviceRelationDTO>> treeDeviceEntry : treeDeviceMap.entrySet()) {

            List<TreeDeviceRelationDTO> itemTreeDeviceRelationList = treeDeviceEntry.getValue();
            spaceDTO = filterImplementDeviceTypeSpace(itemTreeDeviceRelationList, templateId);
            if (spaceDTO == null) {
                continue;
            }
            spaceDTOList.add(spaceDTO);
        }
        return spaceDTOList;
    }

    private List<TreeDeviceRelationDTO> getTreeDeviceRelationByRuleAndSpaceType(Long orgId,
        Long templateId, String spaceName, Integer spaceType) {
        List<Integer> deviceTypeList = getDeviceTypeForRule(templateId);
        ServiceResponse<List<TreeDeviceRelationDTO>> serviceResponse = spaceTreeDeviceRelationService.listBySpaceTypeAndDeviceType(orgId,
            spaceType, deviceTypeList, spaceName);
        List<TreeDeviceRelationDTO> treeDeviceRelationList = serviceResponse.getData();
        return CollectionUtil.isEmpty(treeDeviceRelationList) ? Lists.newArrayList()
            : treeDeviceRelationList;
    }

    private List<Integer> getDeviceTypeForRule(Long templateId) {
        if (templateId == null) {
            return null;
        }

        RuleTemplate template = ruleTemplateManager.getTemplate(templateId);
        if (template == null || StringUtils.isBlank(template.getImplementDeviceTypeProperty())) {
            log.info("模版没有执行设备类型属性code");
            return null;
        }

        List<Integer> triggerDeviceTypeList = null;
        if (StringUtils.isNotBlank(template.getTriggerDeviceTypeProperty())) {
            triggerDeviceTypeList = deviceTypePropertyManager.getDeviceTypeByPropertyKey(template.getTriggerDeviceTypeProperty());
        }
        triggerDeviceTypeList = Optional.ofNullable(triggerDeviceTypeList).orElse(Lists.newLinkedList());

        List<Integer> implementDeviceTypeList = deviceTypePropertyManager.getDeviceTypeByPropertyKey(template.getImplementDeviceTypeProperty());
        implementDeviceTypeList = Optional.ofNullable(implementDeviceTypeList).orElse(Lists.newLinkedList());

        triggerDeviceTypeList.addAll(implementDeviceTypeList);
        return triggerDeviceTypeList;
    }

    /**
     * 过滤含有指定设备类型的空间
     */
    private SpaceDTO filterImplementDeviceTypeSpace(List<TreeDeviceRelationDTO> treeDeviceRelationList, Long templateId) {
        RuleTemplate template = ruleTemplateManager.getTemplate(templateId);
        if (existSpaceDeviceForRule(treeDeviceRelationList, template) != 1) {
            return null;
        }
        //IOT_ENERGY_SAVE_DEVICE_TYPE_LIST
        String deviceType = treeDeviceRelationList.stream().filter(v -> RuleDeviceTypeEnum.containDeviceType(v.getDeviceType()))
            .map(v -> {
                if (RuleDeviceTypeEnum.AIR_CONDITIONER.getDeviceTypes().contains(v.getDeviceType())) {
                    return RuleDeviceTypeEnum.AIR_CONDITIONER.getDesc();
                }

                if (RuleDeviceTypeEnum.SWITCH.getDeviceTypes().contains(v.getDeviceType())) {
                    return RuleDeviceTypeEnum.SWITCH.getDesc();
                }
                if (RuleDeviceTypeEnum.MEASUREMENT_SOCKET.getDeviceTypes().contains(v.getDeviceType())) {
                    return RuleDeviceTypeEnum.MEASUREMENT_SOCKET.getDesc();
                }
                return "";
            }
        ).distinct().sorted().collect(Collectors.joining(","));

        TreeDeviceRelationDTO treeDeviceRelation = treeDeviceRelationList.get(0);

        SpaceDTO spaceDTO = new SpaceDTO();
        spaceDTO.setSpaceId(treeDeviceRelation.getTreeId());
        spaceDTO.setPathSpaceName(treeDeviceRelation.getPathTreeName());
        spaceDTO.setDeviceType(deviceType);
        return spaceDTO;
    }

    private int existSpaceDeviceForRule(List<TreeDeviceRelationDTO> treeDeviceRelationList, RuleTemplate template) {
        if (CollectionUtils.isEmpty(treeDeviceRelationList)) {
            return 0;
        }

        int existImplementDeviceType = 0;

        ModeTriggerEnum modeTriggerEnum = ModeTriggerEnum.getTemplate(template.getModeType(), template.getTriggerType());
        //判断模式-触发模式下，空间下设备类型是否满足条件
        if (ModeTriggerEnum.SAVE_ENERGY_TIMER_TRIGGER.equals(modeTriggerEnum)) {
            existImplementDeviceType = 1;
        } else if (ModeTriggerEnum.SAVE_ENERGY_DEVICE_TRIGGER.equals(modeTriggerEnum)
                || ModeTriggerEnum.USE_ENERGY_HUMAN_TRIGGER.equals(modeTriggerEnum)) {

            boolean existDevice = false, existTriggerDevice = false;
            for (TreeDeviceRelationDTO treeDeviceRelation : treeDeviceRelationList) {
                if (RuleDeviceTypeEnum.containDeviceType(treeDeviceRelation.getDeviceType())) {
                    existDevice = true;
                } else if (deviceTypePropertyManager.containsDeviceType(IOT_ENERGY_SAVE_TRIGGER_DEVICE_TYPE_LIST, treeDeviceRelation.getDeviceType())) {
                    existTriggerDevice = true;
                }
            }
            existImplementDeviceType = (existDevice && existTriggerDevice) ? 1 : (!existDevice ? 2 : 3);
        }
        return existImplementDeviceType;
    }

    private List<DeviceRuleDTO> getDeviceRuleDTOList(Long orgId, List<Rule> ruleList, Long spaceId, Long deviceId) {
        List<Long> spaceRuleIdList = null;
        if (spaceId != null) {
            spaceRuleIdList = spaceRuleRelationManager.getRuleIdByOrgIdAndSpaceId(orgId, spaceId);
            // 已使用的规则排序优先
            List<Long> finalSpaceRuleIdList = spaceRuleIdList;
            ruleList.sort(Comparator.comparing(
                item -> finalSpaceRuleIdList.contains(item.getRuleId()) ? 1 : 0,
                Comparator.reverseOrder()));
        }

        List<Long> deviceRuleIdList = null;
        if (deviceId != null) {
            deviceRuleIdList = deviceRuleRelationManager.getRuleIdByOrgIdAndDeviceId(orgId, deviceId);
            List<Long> finalDeviceRuleIdList = deviceRuleIdList;
            ruleList.sort(Comparator.comparing(
                item -> finalDeviceRuleIdList.contains(item.getRuleId()) ? 1 : 0,
                Comparator.reverseOrder()));
        }

        List<DeviceRuleDTO> deviceRuleDTOList = new ArrayList<>();
        List<SpaceRuleRelation> spaceRuleRelationList;
        List<Long> spaceIdList;
        Map<Long, TreeDeviceRelationDTO> treeMap;
        TreeDeviceRelationDTO treeDeviceRelationDTO;

        for (Rule rule : ruleList) {
            DeviceRuleDTO deviceRuleDTO = new DeviceRuleDTO();
            deviceRuleDTO.setRuleId(rule.getRuleId());
            deviceRuleDTO.setRuleName(rule.getRuleName());
            deviceRuleDTO.setHumanPriority(rule.getHumanPriority());
            deviceRuleDTO.setModeType(rule.getModeType());
            deviceRuleDTO.setTriggerType(rule.getTriggerType());
            deviceRuleDTO.setTriggerValue(wrapperQueryTriggerValue(rule.getTriggerValue(), rule.getModeType(), rule.getTriggerType()));
            List<Integer> dbDeviceTypes = JSONUtil.toList(JSONUtil.parseArray(rule.getDeviceType()),
                Integer.class);
            List<Integer> showDeviceTypes = RuleDeviceTypeEnum.convertRuleDeviceType(dbDeviceTypes);
            deviceRuleDTO.setDeviceTypes(showDeviceTypes);

            if (spaceId != null) {
                deviceRuleDTO.setState((spaceRuleIdList == null || !spaceRuleIdList.contains(rule.getRuleId())) ?
                    RuleStateEnum.DISABLE.getCode() : RuleStateEnum.ACTIVE.getCode());
            }
            if (deviceId != null) {
                deviceRuleDTO.setState((deviceRuleIdList == null || !deviceRuleIdList.contains(rule.getRuleId())) ?
                    RuleStateEnum.DISABLE.getCode() : RuleStateEnum.ACTIVE.getCode());
            }

            DeviceRuleBaseDTO ruleBaseDTO = wrapperRuleOrTemplate(rule.getModeType(),
                rule.getTriggerType(), deviceRuleDTO.getTriggerValue(), rule.getDescription(), showDeviceTypes);
            deviceRuleDTO.setModeName(ruleBaseDTO.getModeName());
            deviceRuleDTO.setTriggerDescription(ruleBaseDTO.getTriggerDescription());
            deviceRuleDTO.setTriggerTip(ruleBaseDTO.getTriggerTip());
            deviceRuleDTO.setPicUrl(ruleBaseDTO.getPicUrl());
            deviceRuleDTO.setPicDetailUrl(ruleBaseDTO.getPicDetailUrl());
            deviceRuleDTO.setDescription(ruleBaseDTO.getDescription());

            String deviceDesc = deviceRuleDTO.getDescription();
            deviceDesc = deviceDesc.replaceAll(" ", "");
            String[] split;
            if (deviceDesc.contains(",")){
                split = deviceDesc.split(",");
                deviceRuleDTO.setTimeDesc(split[0]);
                deviceRuleDTO.setDeviceDesc(split[1]);

            } else if (deviceDesc.contains("，")) {
                split = deviceDesc.split("，");
                deviceRuleDTO.setTimeDesc(split[0]);
                deviceRuleDTO.setDeviceDesc(split[1]);
            }
            spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgIdAndRuleId(orgId, rule.getRuleId());

            spaceIdList = spaceRuleRelationList.stream().map(SpaceRuleRelation::getSpaceId).collect(Collectors.toList());
            treeMap = getTreeMap(orgId, spaceIdList);

            List<SpaceDTO> treeList = new ArrayList<>(spaceRuleRelationList.size());
            for (SpaceRuleRelation spaceRuleRelation : spaceRuleRelationList) {
                SpaceDTO spaceDTO = new SpaceDTO();
                spaceDTO.setSpaceId(spaceRuleRelation.getSpaceId());
                if (treeMap != null) {
                    treeDeviceRelationDTO = treeMap.get(spaceRuleRelation.getSpaceId());
                    spaceDTO.setPathSpaceName(treeDeviceRelationDTO == null ? "" : treeDeviceRelationDTO.getPathTreeName());
                }
                treeList.add(spaceDTO);
            }

            deviceRuleDTO.setSpaceList(treeList);
            deviceRuleDTOList.add(deviceRuleDTO);
        }
        return deviceRuleDTOList;
    }

    private List<DeviceRuleTemplateDTO> getRuleDefaultTemplateList(Long orgId) {
        List<RuleTemplate> defaultRuleTemplateList = ruleTemplateManager.getDefaultTemplateList();
        if (CollectionUtils.isEmpty(defaultRuleTemplateList)) {
            return new ArrayList<>();
        }

        List<DeviceRuleTemplateDTO> res = new ArrayList<>();
        for (RuleTemplate ruleTemplate : defaultRuleTemplateList) {
            DeviceRuleTemplateDTO dto = new DeviceRuleTemplateDTO();
            dto.setTemplateId(ruleTemplate.getRuleTemplateId());
            dto.setRuleName(ruleTemplate.getTemplateName());
            dto.setModeType(ruleTemplate.getModeType());
            dto.setTriggerType(ruleTemplate.getTriggerType());
            dto.setTriggerValue(
                wrapperQueryTriggerValue(ruleTemplate.getTriggerValue(), ruleTemplate.getModeType(),
                    ruleTemplate.getTriggerType()));

            List<Integer> showDeviceTypes = RuleDeviceTypeEnum.convertRuleDeviceType(
                RuleDeviceTypeEnum.SUPPORT_ALL_DEVICE_TYPES);
            DeviceRuleBaseDTO ruleBaseDTO = wrapperRuleOrTemplate(ruleTemplate.getModeType(),
                ruleTemplate.getTriggerType(), dto.getTriggerValue(),
                ruleTemplate.getDescription(), showDeviceTypes);
            dto.setDeviceTypes(showDeviceTypes);
            dto.setModeName(ruleBaseDTO.getModeName());
            dto.setTriggerDescription(ruleBaseDTO.getTriggerDescription());
            dto.setTriggerTip(ruleBaseDTO.getTriggerTip());
            dto.setPicUrl(ruleBaseDTO.getPicUrl());
            dto.setPicDetailUrl(ruleBaseDTO.getPicDetailUrl());
            dto.setDescription(ruleBaseDTO.getDescription());
            dto.setHumanPriority(ruleBaseDTO.getHumanPriority());
            res.add(dto);
        }
        return res;
    }

    private Map<Long, TreeDeviceRelationDTO> getTreeMap(Long orgId, List<Long> spaceIdList) {
        ServiceResponse<List<TreeDeviceRelationDTO>> treeDeviceResp = spaceTreeDeviceRelationService.listByTreeIdsAndSource(orgId, spaceIdList, null);
        Map<Long, TreeDeviceRelationDTO> treeDeviceRelationDTOMap = treeDeviceResp.isSuccess() ? treeDeviceResp.getData().stream().collect(Collectors.toMap(TreeDeviceRelationDTO::getTreeId, v -> v, (k1, k2) -> k1)) : null;
        return treeDeviceRelationDTOMap;
    }

    private DeviceRuleBaseDTO wrapperRuleOrTemplate(Integer modeType, Integer triggerType, String triggerValue, String desc, List<Integer> showDeviceTypes) {
        ModeTriggerEnum modeTriggerEnum = ModeTriggerEnum.getTemplate(modeType, triggerType);
        DeviceRuleBaseDTO ruleBaseDTO = new DeviceRuleBaseDTO();
        if (ModeTriggerEnum.SAVE_ENERGY_TIMER_TRIGGER.equals(modeTriggerEnum)) {
            ruleBaseDTO.setModeName("节能场景");
            ruleBaseDTO.setTriggerDescription("定时关闭设备");

            ruleBaseDTO.setPicUrl(fileManager.getUrlByRelativePath("/subject/saveEnergyTimer.png"));
            ruleBaseDTO.setPicDetailUrl(fileManager.getUrlByRelativePath("/subject/saveEnergyTimerSmall.png"));
        } else if (ModeTriggerEnum.SAVE_ENERGY_DEVICE_TRIGGER.equals(modeTriggerEnum)) {
            ruleBaseDTO.setModeName("节能场景");
            ruleBaseDTO.setTriggerDescription("无人时关闭设备");
            ruleBaseDTO.setTriggerTip("该场景需要人体感应传感器");

            ruleBaseDTO.setPicUrl(fileManager.getUrlByRelativePath("/subject/saveEnergyMonitor.png"));
            ruleBaseDTO.setPicDetailUrl(fileManager.getUrlByRelativePath("/subject/saveEnergyMonitorSmall.png"));
        }else if (ModeTriggerEnum.USE_ENERGY_HUMAN_TRIGGER.equals(modeTriggerEnum)) {
            ruleBaseDTO.setModeName("规则场景");
            ruleBaseDTO.setTriggerDescription("有人时开启设备");
            ruleBaseDTO.setTriggerTip("该场景需要人体感应传感器");
            ruleBaseDTO.setHumanPriority(Boolean.TRUE);

            ruleBaseDTO.setPicUrl(fileManager.getUrlByRelativePath("/subject/humanOnMonitor.png"));
            ruleBaseDTO.setPicDetailUrl(fileManager.getUrlByRelativePath("/subject/humanOnMonitorSmall.png"));
        }
        ruleBaseDTO.setDescription(
            desc.replaceAll("#\\{triggerValue\\}", triggerValue)
                .replaceAll("#\\{deviceTypeValue\\}",
                    RuleDeviceTypeEnum.convertDeviceTypeDesc(showDeviceTypes)));
        return ruleBaseDTO;
    }

}
