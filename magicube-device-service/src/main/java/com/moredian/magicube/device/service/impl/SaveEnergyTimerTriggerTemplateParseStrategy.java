package com.moredian.magicube.device.service.impl;

/**
 * <AUTHOR>
 * @version $Id: SaveEnergyTimerStrategy.java, v 1.0 Exp $
 */
import com.google.common.collect.Lists;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceInstance.dto.IotDeviceInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.bridge.api.service.deviceProperty.dto.IotDevicePropertyDefineDTO;
import com.moredian.magicube.common.enums.OrgStatus;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.dao.entity.RuleTemplate;
import com.moredian.magicube.device.enums.ModeTriggerEnum;
import com.moredian.magicube.device.enums.RuleDeviceTypeEnum;
import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.manager.RuleManager;
import com.moredian.magicube.device.service.strategy.ModeTriggerTemplateParseStrategy;
import com.moredian.space.dto.device.TreeDeviceRelationDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.moredian.magicube.device.constant.DeviceTypeConstants.IOT_VIRTUAL_DEVICE_TYPE_LIST;

@Service(value="SaveEnergyTimerTriggerTemplateParseStrategy")
@Slf4j
public class SaveEnergyTimerTriggerTemplateParseStrategy implements ModeTriggerTemplateParseStrategy {

    ModeTriggerEnum templateEnum = ModeTriggerEnum.SAVE_ENERGY_TIMER_TRIGGER;

    @Resource
    private RuleManager ruleManager;
    @SI
    private OrgService orgService;
    @Resource
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Override
    public String getSceneRule(Rule rule, List<TreeDeviceRelationDTO> treeDeviceReationList,
        Map<String, IotDeviceInfoDTO> iotDeviceMap,
        Map<String, BatchIotDevicePropertyInfoDTO> iotDevicePropertyMap,
        List<Integer> selectDeviceTypes) {

        RuleTemplate ruleTemplate = ruleManager.getRuleTemplate(rule.getOrgId(), rule.getRuleId());
        if (ruleTemplate == null) {
            log.info("模版为空：orgId: {}, ruleId: {}", rule.getOrgId(), rule.getRuleId());
            return null;
        }

        //查询节能设备
        treeDeviceReationList = treeDeviceReationList.stream()
            .filter(v -> RuleDeviceTypeEnum.containDeviceType(v.getDeviceType()))
            .filter(v -> selectDeviceTypes.contains(v.getDeviceType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(treeDeviceReationList)) {
            log.info("节能设备列表为空 ruleId: {}", rule.getRuleId());
            return null;
        }

        StringJoiner actionJoiner = new StringJoiner(",", "[", "]");
        StringJoiner treeNames = new StringJoiner(",", "(", ")");
        for (TreeDeviceRelationDTO treeDeviceRelation : treeDeviceReationList) {

            IotDeviceInfoDTO iotDevice = getIotDevice(treeDeviceRelation, iotDeviceMap);
            if (iotDevice == null) {
                log.info("iot设备不存在，deviceRelation： {} ,iotDeviceMap: {}", treeDeviceRelation, iotDeviceMap);
                continue;
            }
            treeNames.add(treeDeviceRelation.getTreeName());
            String group = getGroup(treeDeviceRelation);
            String switchPropertyId = getPropertyId(treeDeviceRelation, group);
            if (StringUtils.isBlank(switchPropertyId)) {
                log.error("treeDeviceRelation: {}, switchPropertyId: {}", treeDeviceRelation, switchPropertyId);
                return null;
            }

            String iotDevicePropertyName= "开关", iotDevicePropertyValueName = RuleStateEnum.DISABLE.getDesc();

            IotDevicePropertyDefineDTO iotDevicePropertyDefine = getSwitchPropertyDefine(iotDevicePropertyMap, iotDevice, switchPropertyId);
            if (iotDevicePropertyDefine != null) {
                iotDevicePropertyName = iotDevicePropertyDefine.getName();
                String propertyValueName = getPropertyValueName(iotDevicePropertyDefine);
                if (StringUtils.isNotBlank(propertyValueName)) {
                    iotDevicePropertyValueName = propertyValueName;
                }
            }

            String actionJson = ruleTemplate.getActionJson();
            String action = actionJson
                    .replaceAll("#\\{deviceId\\}", iotDevice.getId()).replaceAll("#\\{deviceName\\}", iotDevice.getName())
                    .replaceAll("#\\{productId\\}", iotDevice.getProductId()).replaceAll("#\\{productName\\}", iotDevice.getProductName())
                    .replaceAll("#\\{propertyId\\}", switchPropertyId).replaceAll("#\\{propertyValue\\}", String.valueOf(RuleStateEnum.DISABLE.getCode()))
                    .replaceAll("#\\{propertyName\\}", iotDevicePropertyName).replaceAll("#\\{propertyValueName\\}", iotDevicePropertyValueName);
            actionJoiner.add(action);
        }

        String ruleJson = ruleTemplate.getRuleJson();
        OrgInfo orgInfo = orgService.getOrgInfo(rule.getOrgId(), Lists.newArrayList(OrgStatus.USABLE.getValue()))
            .pickDataThrowException();

        String newRuleName = rule.getRuleName() + "-" + orgInfo.getOrgName();
        String ruleMetaData = ruleJson.replaceAll("#\\{ruleName\\}", newRuleName)
            .replaceAll("#\\{time\\}", rule.getTriggerValue())
            .replaceAll("#\\{actionDeviceList\\}", actionJoiner.toString())
            .replaceAll("#\\{description}", treeNames.toString());

        return ruleMetaData;
    }

    @Override
    public Boolean virtualIotDevice(Integer deviceType) {
        return deviceTypePropertyManager.containsDeviceType(IOT_VIRTUAL_DEVICE_TYPE_LIST, deviceType);
    }

    public static void main(String[] args) {
        String string = "{\"executor\":\"device\",\"device\":{\"source\":\"fixed\",\"upperKey\":\"\",\"selector\":\"fixed\",\"selectorValues\":[{\"value\":\"#{deviceId}\",\"name\":\"#{deviceName}\"}],\"productId\":\"#{productId}\",\"message\":{\"messageType\":\"WRITE_PROPERTY\",\"properties\":{\"#{propertyId}\":{\"value\":\"#{propertyValue}\",\"source\":\"fixed\"}},\"inputs\":[]}},\"terms\":[],\"options\":{\"selector\":\"fixed\",\"triggerName\":\"触发设备\",\"productName\":\"#{productName}\",\"name\":\"#{deviceName}\",\"propertiesName\":\"#{propertyName}\",\"propertiesValue\":\"#{propertyValueName}\",\"otherColumns\":[],\"type\":\"设置\",\"columns\":[],\"termsColumns\":[],\"terms\":[]}}";
        System.out.println(string.replaceAll("#\\{deviceId\\}", "sss"));
    }
    @Override
    public ModeTriggerEnum getTemplate() {
        return templateEnum;
    }
}
