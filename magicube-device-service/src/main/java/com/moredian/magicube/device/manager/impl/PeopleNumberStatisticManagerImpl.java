package com.moredian.magicube.device.manager.impl;

import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.idgenerator.service.IdgeneratorService;
import com.moredian.magicube.device.dao.entity.PeopleNumberStatistic;
import com.moredian.magicube.device.dao.mapper.PeopleNumberStatisticMapper;
import com.moredian.magicube.device.manager.PeopleNumberStatisticManager;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
public class PeopleNumberStatisticManagerImpl implements PeopleNumberStatisticManager {

    @SI
    private IdgeneratorService idgeneratorService;

    @Autowired
    private PeopleNumberStatisticMapper peopleNumberStatisticMapper;

    @Override
    public Long insert(PeopleNumberStatistic ps) {
        BizAssert.notNull(ps.getOrgId(), "orgId must not be null");
        BizAssert.notBlank(ps.getDeviceSn(), "deviceSn must not be null");
        PeopleNumberStatistic people = peopleNumberStatisticMapper
            .getByOrgIdAndDeviceSn(ps.getOrgId(), ps.getDeviceSn());
        Long id;
        if (people == null) {
            id = idgeneratorService.getNextIdByTypeName(PeopleNumberStatistic.class.getName())
                .pickDataThrowException();
            ps.setId(id);
            peopleNumberStatisticMapper.insert(ps);
        } else {
            id = people.getId();
            people.setInsidePeopleNum(ps.getInsidePeopleNum());
            peopleNumberStatisticMapper.update(people);
        }
        return id;
    }

    @Override
    public List<PeopleNumberStatistic> listByOrgIdAndDeviceSns(Long orgId, List<String> deviceSns) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceSns), "deviceSn must not be null");
        return peopleNumberStatisticMapper.listByOrgIdAndDeviceSns(orgId, deviceSns);
    }

    @Override
    public List<PeopleNumberStatistic> listByOrgIdAndDeviceId(Long orgId, List<Long> deviceIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(deviceIds), "deviceIds must not be empty");
        return peopleNumberStatisticMapper.listByOrgIdAndDeviceIds(orgId, deviceIds);
    }
}
