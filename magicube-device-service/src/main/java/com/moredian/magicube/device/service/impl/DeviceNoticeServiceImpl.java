package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.NoticeRecord;
import com.moredian.magicube.device.dto.notice.NoticeRecordDTO;
import com.moredian.magicube.device.dto.notice.StatisticsModelDTO;
import com.moredian.magicube.device.manager.NoticeRecordManager;
import com.moredian.magicube.device.model.StatisticsModel;
import com.moredian.magicube.device.service.DeviceNoticeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2022/8/31
 */
@SI
public class DeviceNoticeServiceImpl implements DeviceNoticeService {

    @Autowired
    private NoticeRecordManager noticeRecordManager;

    @Override
    public ServiceResponse<Long> addRecord(NoticeRecordDTO noticeRecord) {
        NoticeRecord noticeRecord1 = new NoticeRecord();
        BeanUtils.copyProperties(noticeRecord, noticeRecord1);
        Long id = noticeRecordManager.addRecord(noticeRecord1);
        return new ServiceResponse<>(id);
    }

    @Override
    public ServiceResponse<Integer> countRecords(StatisticsModelDTO statisticsModel) {
        StatisticsModel statisticsModel1 = new StatisticsModel();
        BeanUtils.copyProperties(statisticsModel, statisticsModel1);
        int num = noticeRecordManager.countRecords(statisticsModel1);
        return new ServiceResponse<>(num);
    }

    @Override
    public ServiceResponse<Long> getLastOfflineTime(Long orgId, String deviceSn,
        Integer noticeEvent) {
        Long lastOfflineTime = noticeRecordManager.getLastOfflineTime(orgId, deviceSn, noticeEvent);
        return new ServiceResponse<>(lastOfflineTime);
    }
}
