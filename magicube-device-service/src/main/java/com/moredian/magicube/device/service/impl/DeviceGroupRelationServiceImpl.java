package com.moredian.magicube.device.service.impl;

import com.google.common.collect.Lists;
import com.moredian.bee.common.exception.BizAssert;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.core.org.model.GroupInfo;
import com.moredian.magicube.core.org.service.GroupService;
import com.moredian.magicube.device.dao.entity.DeviceGroup;
import com.moredian.magicube.device.dao.mapper.DeviceGroupMapper;
import com.moredian.magicube.device.dto.group.DeviceGroupDTO;
import com.moredian.magicube.device.dto.group.DeviceGroupPersonCountDTO;
import com.moredian.magicube.device.dto.group.GroupDeviceSizeDTO;
import com.moredian.magicube.device.dto.group.GroupInfoDTO;
import com.moredian.magicube.device.dto.group.QueryGroupDTO;
import com.moredian.magicube.device.dto.group.ResetGroupRelationDTO;
import com.moredian.magicube.device.dto.group.SimpleDeviceGroupDTO;
import com.moredian.magicube.device.manager.DeviceGroupManager;
import com.moredian.magicube.device.service.DeviceGroupRelationService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */

@SI
@Slf4j
public class DeviceGroupRelationServiceImpl implements DeviceGroupRelationService {

    @Autowired
    private DeviceGroupManager deviceGroupManager;

    @Autowired
    private DeviceGroupMapper deviceGroupMapper;

    @SI
    private GroupService groupService;

    @Override
    public ServiceResponse<List<Long>> listDeviceIdByOrgIdAndGroupId(Long orgId, Long groupId) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceGroupManager.listDeviceIdByOrgIdAndGroupId(orgId, groupId));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Long>> listDeviceIdByOrgIdAndGroupIds(Long orgId,
        List<Long> groupIds) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceGroupManager.listDeviceIdByOrgIdAndGroupIds(orgId, groupIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Long>> listDeviceIdByCondition(Long orgId, Long groupId, int offset,
        int limit) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse
            .setData(deviceGroupManager.listDeviceIdByCondition(orgId, groupId, offset, limit));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Map<Long, List<Long>>> getGroupIdToDeviceIdsMapByOrgIdAndGroupIds(
        Long orgId, List<Long> groupIds) {
        ServiceResponse<Map<Long, List<Long>>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        Map<Long, List<Long>> groupIdToDeviceIdsMap = new HashMap<>();
        List<DeviceGroup> deviceGroups = deviceGroupManager.listByOrgIdAndGroupIds(orgId, groupIds);
        if (CollectionUtils.isNotEmpty(deviceGroups)) {
            for (DeviceGroup deviceGroup : deviceGroups) {
                if (groupIdToDeviceIdsMap.containsKey(deviceGroup.getGroupId())) {
                    groupIdToDeviceIdsMap.get(deviceGroup.getGroupId())
                        .add(deviceGroup.getDeviceId());
                } else {
                    groupIdToDeviceIdsMap.put(deviceGroup.getGroupId(),
                        Lists.newArrayList(deviceGroup.getDeviceId()));
                }
            }
        }
        serviceResponse.setData(groupIdToDeviceIdsMap);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Map<Long, List<Long>>> getDeviceIdToGroupIdsByOrgIdAndDeviceIds(
        Long orgId, List<Long> deviceIds, Integer appType) {
        ServiceResponse<Map<Long, List<Long>>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        Map<Long, List<Long>> deviceIdToGroupIdsMap = new HashMap<>();
        List<DeviceGroup> list = deviceGroupManager.listByOrgIdAndDeviceIds(orgId, deviceIds);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> groupIds = list.stream().map(DeviceGroup::getGroupId)
                .collect(Collectors.toList());
            if (appType != null) {
                //过滤对应设备类型
                groupIds = filterGroupAppType(orgId, appType, groupIds);
            }

            //组装map
            List<DeviceGroup> dgs = deviceGroupManager.listByOrgIdAndGroupIds(orgId, groupIds);
            if (CollectionUtils.isNotEmpty(dgs)) {
                dgs.forEach(x -> {
                    //过滤非传进来的设备组
                    if (!deviceIds.contains(x.getDeviceId())) {
                        return;
                    }
                    if (deviceIdToGroupIdsMap.containsKey(x.getDeviceId())) {
                        deviceIdToGroupIdsMap.get(x.getDeviceId()).add(x.getGroupId());
                    } else {
                        deviceIdToGroupIdsMap
                            .put(x.getDeviceId(), Lists.newArrayList(x.getGroupId()));
                    }
                });
            }
        }
        serviceResponse.setData(deviceIdToGroupIdsMap);
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<Long>> listGroupIdByCondition(QueryGroupDTO dto) {
        ServiceResponse<List<Long>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<Long> groupIds = deviceGroupManager
            .listGroupIdByOrgIdAndDeviceIds(dto.getOrgId(), dto.getDeviceIds());
        if (CollectionUtils.isNotEmpty(groupIds)) {
            if(CollectionUtils.isNotEmpty(dto.getAppTypeList())){
                List<Integer> appTypeList = dto.getAppTypeList();
                if(dto.getAppType() != null && !appTypeList.contains(dto.getAppType())){
                    appTypeList.add(dto.getAppType());
                }
                groupIds = filterGroupAppTypeList(dto.getOrgId(), appTypeList, groupIds);
            }else{
                if (dto.getAppType() != null) {
                    groupIds = filterGroupAppType(dto.getOrgId(), dto.getAppType(), groupIds);
                }
            }
            serviceResponse.setData(groupIds);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<String>> listGroupNameByCondition(QueryGroupDTO dto) {
        ServiceResponse<List<String>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<Long> groupIds = deviceGroupManager
            .listGroupIdByOrgIdAndDeviceIds(dto.getOrgId(), dto.getDeviceIds());
        if (CollectionUtils.isNotEmpty(groupIds)) {
            if (dto.getAppType() != null) {
                groupIds = filterGroupAppType(dto.getOrgId(), dto.getAppType(), groupIds);
            }
            List<String> groupNames = groupService.findGroupNameByIds(dto.getOrgId(), groupIds)
                .pickDataThrowException();
            serviceResponse.setData(groupNames);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<GroupInfoDTO>> listGroupByCondition(QueryGroupDTO dto) {
        ServiceResponse<List<GroupInfoDTO>> serviceResponse = ServiceResponse
            .createSuccessResponse();
        List<Long> groupIds = deviceGroupManager
            .listGroupIdByOrgIdAndDeviceIds(dto.getOrgId(), dto.getDeviceIds());
        if (CollectionUtils.isNotEmpty(groupIds)) {
            if (dto.getAppType() != null) {
                groupIds = filterGroupAppType(dto.getOrgId(), dto.getAppType(), groupIds);
            }
            List<GroupInfo> groupInfos = groupService.findGroupByIds(dto.getOrgId(), groupIds)
                .pickDataThrowException();
            if (CollectionUtils.isNotEmpty(groupInfos)) {
                List<GroupInfoDTO> groupInfoDTOS = new ArrayList<>(groupInfos.size());
                groupInfos.forEach(groupInfo -> {
                        GroupInfoDTO groupInfoDTO = new GroupInfoDTO();
                        BeanUtils.copyProperties(groupInfo, groupInfoDTO);
                        groupInfoDTOS.add(groupInfoDTO);
                    }
                );
                serviceResponse.setData(groupInfoDTOS);
            }
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deleteByCondition(Long orgId, List<Long> deviceIds) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceGroupManager.deleteByCondition(orgId, deviceIds, null));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> deleteByDeviceIds(Long orgId, List<Long> deviceIds, List<Long> groupIds) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(deviceGroupManager.deleteByCondition(orgId, deviceIds, groupIds));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> resetRelationByDeviceId(Long orgId, Long deviceId,
        List<Long> groupIds, Integer appType) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse
            .setData(
                deviceGroupManager.resetRelationByDeviceId(orgId, deviceId, groupIds, appType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> insertRelationByDeviceIds(Long orgId, List<Long> deviceIds, List<Long> groupIds, Integer appType) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse
                .setData(
                        deviceGroupManager.insertRelationByDeviceIds(orgId, deviceIds, groupIds, appType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Boolean> resetRelationByGroupId(Long orgId, Long groupId,
        List<Long> deviceIds, Integer appType) {
        ServiceResponse<Boolean> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(
            deviceGroupManager.resetRelationByGroupId(orgId, groupId, deviceIds, appType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<ResetGroupRelationDTO> resetRelationByGroupIdAndDeviceIdList(Long orgId, Long groupId,
        List<Long> deviceIds, Integer appType) {
        ServiceResponse<ResetGroupRelationDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        serviceResponse.setData(
                deviceGroupManager.resetRelationByGroupIdAndDeviceIdList(orgId, groupId, deviceIds, appType));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<Integer> distinctCountDeviceGroupPerson(DeviceGroupPersonCountDTO dto) {
        return new ServiceResponse<>(deviceGroupManager.distinctCountDeviceGroupPerson(dto));
    }

    @Override
    public List<DeviceGroupDTO> findRelationByGroupId(Long orgId, Long groupId) {
        List<DeviceGroupDTO> groupDtoList = new ArrayList<>();
        List<DeviceGroup> deviceGroupList = deviceGroupManager.findRelationByGroupId(orgId, groupId);
        if(CollectionUtils.isNotEmpty(deviceGroupList)) {
            for (DeviceGroup deviceGroup : deviceGroupList) {
                DeviceGroupDTO deviceGroupDto = new DeviceGroupDTO();
                deviceGroupDto.setDeviceGroupId(deviceGroup.getDeviceGroupId());
                deviceGroupDto.setOrgId(deviceGroup.getOrgId());
                deviceGroupDto.setDeviceId(deviceGroup.getDeviceId());
                deviceGroupDto.setGroupId(deviceGroup.getGroupId());
                deviceGroupDto.setGroupCode(deviceGroup.getGroupCode());
                deviceGroupDto.setGmtCreate(deviceGroup.getGmtCreate());
                deviceGroupDto.setGmtModify(deviceGroup.getGmtModify());
                groupDtoList.add(deviceGroupDto);

            }
        }
        return groupDtoList;
    }

    @Override
    public ServiceResponse<List<GroupDeviceSizeDTO>> countDeviceSizeGroupByGroupId(Long orgId, List<Long> groupIdList) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(groupIdList), "groupIdList must not be null");
        List<GroupDeviceSizeDTO> sizeDtoList = deviceGroupMapper.countDeviceSizeGroupByGroupId(orgId, groupIdList);
        return new ServiceResponse<>(sizeDtoList);
    }

    @Override
    public ServiceResponse<List<SimpleDeviceGroupDTO>> findDeviceGroupByGroupIds(Long orgId, List<Long> groupIds) {
        BizAssert.notNull(orgId, "orgId must not be null");
        BizAssert.isTrue(CollectionUtils.isNotEmpty(groupIds), "groupIds must not be empty");
        return new ServiceResponse<>(deviceGroupManager.findDeviceGroupByGroupIds(orgId, groupIds));
    }

    private List<Long> filterGroupAppType(Long orgId, Integer appType, List<Long> allGroupIds) {
        List<Long> groupIds = new ArrayList<>(allGroupIds.size());
        List<GroupInfo> groupInfos = groupService.findGroupByIds(orgId, allGroupIds)
            .pickDataThrowException();
        if (CollectionUtils.isNotEmpty(groupInfos)) {
            groupIds = groupInfos.stream()
                .filter(groupInfo -> appType.equals(groupInfo.getAppType()))
                .map(GroupInfo::getGroupId)
                .collect(Collectors.toList());
        }
        return groupIds;
    }

    private List<Long> filterGroupAppTypeList(Long orgId, List<Integer> appTypeList, List<Long> allGroupIds) {
        List<Long> groupIds = new ArrayList<>(allGroupIds.size());
        List<GroupInfo> groupInfos = groupService.findGroupByIds(orgId, allGroupIds)
                .pickDataThrowException();
        if (CollectionUtils.isNotEmpty(groupInfos)) {
            groupIds = groupInfos.stream()
                    .filter(groupInfo -> appTypeList.contains(groupInfo.getAppType()))
                    .map(GroupInfo::getGroupId)
                    .collect(Collectors.toList());
        }
        return groupIds;
    }
}