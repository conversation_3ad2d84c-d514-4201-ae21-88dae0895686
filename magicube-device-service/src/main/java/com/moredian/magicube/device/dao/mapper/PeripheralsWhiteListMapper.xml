<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.moredian.magicube.device.dao.mapper.PeripheralsWhiteListMapper">

    <resultMap id="peripheralsWhiteListResultMap" type="com.moredian.magicube.device.dao.entity.PeripheralsWhiteList">
        <result column="peripherals_white_list_id" property="peripheralsWhiteListId"/>
        <result column="peripherals_sn" property="peripheralsSn"/>
        <result column="peripherals_type" property="peripheralsType"/>
        <result column="status" property="status"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modify" property="gmtModify"/>
    </resultMap>

    <sql id="sql_table">
        hive_peripherals_white_list
    </sql>

    <sql id="sql_columns">
        peripherals_white_list_id,
        peripherals_sn,
        peripherals_type,
        status,
        gmt_create,
        gmt_modify
    </sql>

    <sql id="sql_values">
        #{peripheralsWhiteListId},
        #{peripheralsSn},
        #{peripheralsType},
        #{status},
        now(3),
        now(3)
    </sql>

    <insert id="insert" parameterType="com.moredian.magicube.device.dao.entity.PeripheralsWhiteList">
        INSERT INTO
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        )
        VALUES
        (
        <include refid="sql_values"/>
        )
    </insert>

    <insert id="batchInsert">
        insert into
        <include refid="sql_table"/>
        (
        <include refid="sql_columns"/>
        ) values
        <foreach collection="list" item="item" index="index" open="" separator="," close="">
            (
            #{item.peripheralsWhiteListId},
            #{item.peripheralsSn},
            #{item.peripheralsType},
            #{item.status},
            now(3),
            now(3)
            )
        </foreach>
    </insert>

    <update id="update" parameterType="map">
        update
        <include refid="sql_table"/>
        <set>
            <if test="status != null ">
                status = #{status},
            </if>
            gmt_modify = now(3)
        </set>
        where peripherals_white_list_id = #{peripheralsWhiteListId}
    </update>

    <select id="getBySnAndType" resultMap="peripheralsWhiteListResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where peripherals_sn = #{peripheralsSn}
        and peripherals_type = #{peripheralsType}
        and status = 1
    </select>

    <select id="listByPeripheralsSns" resultMap="peripheralsWhiteListResultMap">
        select
        <include refid="sql_columns"/>
        from
        <include refid="sql_table"/>
        where status = 1
        and peripherals_sn in
        <foreach collection="peripheralsSns" item="peripheralsSn" open="(" close=")" separator=",">
            #{peripheralsSn}
        </foreach>
    </select>

    <select id="countPeripheralsSn" resultType="int">
        select count(1) from <include refid="sql_table"/> where status = 1
    </select>

    <select id="pagePeripheralsSn"  resultMap="peripheralsWhiteListResultMap">
        select  <include refid="sql_columns"/> from <include refid="sql_table"/>
        where status = 1
        order by peripherals_white_list_id desc
        limit #{sizeBegin},#{pageSize}
    </select>
</mapper>