package com.moredian.magicube.device.pipeline.pipe.code;


import com.moredian.bee.common.exception.BizAssert;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.error.DeviceErrorCode;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.WhiteDeviceManager;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.core.pipeline.AbstractPipe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 激活码校验管道
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CodeCheckPipe extends AbstractPipe<ActivateDeviceDTO, ActivateDeviceContext> {

    @Autowired
    private WhiteDeviceManager whiteDeviceManager;

    @Autowired
    private DeviceManager deviceManager;

    @Override
    protected boolean isFilter(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        return false;
    }

    @Override
    protected void bizHandler(ActivateDeviceDTO dto, ActivateDeviceContext context) {
        // 1.判断激活码是否有效
        InventoryDevice inventoryDevice = whiteDeviceManager.getByDeviceSnAndActivationCode(dto.getDeviceSn(), dto.getActivationCode());
        BizAssert.isTrue(inventoryDevice != null, DeviceErrorCode.INVALID_ACTIVE_EXCEPTION, DeviceErrorCode.INVALID_ACTIVE_EXCEPTION.getMessage());

        // 2.设备是否存在校验
        Device existDevice = deviceManager.getByDeviceSn(dto.getDeviceSn());
        BizAssert.isTrue(existDevice == null, DeviceErrorCode.DEVICE_EXIST, DeviceErrorCode.DEVICE_EXIST.getMessage());
    }
}
