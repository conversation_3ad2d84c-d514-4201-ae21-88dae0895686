package com.moredian.magicube.device.dao.mapper;

import com.moredian.magicube.device.dao.entity.BeforeActivationDeviceVersion;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BeforeActivationDeviceVersionMapper {


    /**
     * 新增或更新设备激活前rom版本信息
     *
     * @param deviceVersion
     * @return
     */
    int insertOrUpdateDeviceRomVersion(BeforeActivationDeviceVersion deviceVersion);

    /**
     * 批量获取激活前设备rom版本号
     *
     * @param deviceSnList
     * @return
     */
    List<BeforeActivationDeviceVersion> getBatchBeforeActivationDeviceRomVersion(@Param("deviceSnList") List<String> deviceSnList);
}