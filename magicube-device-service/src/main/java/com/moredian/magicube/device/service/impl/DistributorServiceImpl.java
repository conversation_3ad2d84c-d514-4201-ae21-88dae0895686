package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.dao.entity.Distributor;
import com.moredian.magicube.device.dto.distributor.DistributorDTO;
import com.moredian.magicube.device.dto.distributor.InsertDistributorDTO;
import com.moredian.magicube.device.manager.DistributorManager;
import com.moredian.magicube.device.service.DistributorService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 渠道商锁定设备
 *
 * <AUTHOR>
 */
@SI
public class DistributorServiceImpl implements DistributorService {

    @Autowired
    private DistributorManager distributorManager;

    @Override
    public ServiceResponse<Long> insert(InsertDistributorDTO dto) {
        ServiceResponse<Long> serviceResponse = ServiceResponse.createSuccessResponse();
        Distributor distributor = new Distributor();
        BeanUtils.copyProperties(dto, distributor);
        serviceResponse.setData(distributorManager.insert(distributor));
        return serviceResponse;
    }

    @Override
    public ServiceResponse<List<DistributorDTO>> listByOrgName(String orgName) {
        ServiceResponse<List<DistributorDTO>> serviceResponse = ServiceResponse.createSuccessResponse();
        List<Distributor> distributors = distributorManager.listByOrgName(orgName);
        List<DistributorDTO> distributorDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(distributors)) {
            for (Distributor distributor : distributors) {
                DistributorDTO distributorDTO = new DistributorDTO();
                BeanUtils.copyProperties(distributor, distributorDTO);
                distributorDTOS.add(distributorDTO);
            }
            serviceResponse.setData(distributorDTOS);
        }
        return serviceResponse;
    }

    @Override
    public ServiceResponse<DistributorDTO> getByDeviceSn(String deviceSn) {
        ServiceResponse<DistributorDTO> serviceResponse = ServiceResponse.createSuccessResponse();
        Distributor distributor = distributorManager.getByDeviceSn(deviceSn);
        if (distributor != null) {
            DistributorDTO distributorDTO = new DistributorDTO();
            BeanUtils.copyProperties(distributor, distributorDTO);
            serviceResponse.setData(distributorDTO);
        }
        return serviceResponse;
    }
}
