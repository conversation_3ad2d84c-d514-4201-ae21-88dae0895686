package com.moredian.magicube.device.service.impl;

import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.UUID;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.iothub.control.ControllerErrorCode;
import com.moredian.iothub.control.api.v1.HubControlServiceV1;
import com.moredian.iothub.control.api.v1.request.TransferRequest;
import com.moredian.iothub.control.api.v1.response.TransferResponse;
import com.moredian.magicube.common.enums.TransferEventType;
import com.moredian.magicube.device.dao.entity.TransferMessageInfo;
import com.moredian.magicube.device.dto.project.DeviceProjectInfoResponse;
import com.moredian.magicube.device.dto.project.PullDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.ReportDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.TransferData;
import com.moredian.magicube.device.dto.project.UpdateDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateProjectInfoResponse;
import com.moredian.magicube.device.manager.DeviceProjectInfoManager;
import com.moredian.magicube.device.service.DeviceProjectInfoService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/10
 */
@SI
@Slf4j
public class DeviceProjectInfoServiceImpl implements DeviceProjectInfoService {

    @Autowired
    private DeviceProjectInfoManager deviceProjectInfoManager;

    @SI
    private HubControlServiceV1 hubControlServiceV1;

    @Override
    public ServiceResponse<Long> reportProjectInfo(ReportDeviceProjectInfoRequest request) {
        return new ServiceResponse<>(deviceProjectInfoManager.reportProjectInfo(request));
    }

    @Override
    public ServiceResponse<DeviceProjectInfoResponse> pullProjectInfo(
        PullDeviceProjectInfoRequest request) {
        return new ServiceResponse<>(deviceProjectInfoManager.pullProjectInfo(request));
    }

    @Override
    public ServiceResponse<List<DeviceProjectInfoResponse>> findDeviceProjectInfo(
        QueryDeviceProjectInfoRequest request) {
        return new ServiceResponse<>(deviceProjectInfoManager.findDeviceProjectInfo(request));
    }

    @Override
    public ServiceResponse<UpdateProjectInfoResponse> updateProjectInfo(
        UpdateDeviceProjectInfoRequest request) {
        UpdateProjectInfoResponse projectResponse = deviceProjectInfoManager.updateProjectInfo(request);
        if (projectResponse != null && CollectionUtils.isNotEmpty(projectResponse.getChangedDeviceSnList())) {
            //发送项目信息变更mqtt
            for (String deviceSn : projectResponse.getChangedDeviceSnList()) {
                try {
                    // 发送命令
                    TransferMessageInfo<TransferData> transferMessageInfo = new TransferMessageInfo<>();
                    transferMessageInfo.setEventType(TransferEventType.PULL_PROJECT_INFO.getValue());
                    transferMessageInfo.setSeverity(3);
                    transferMessageInfo.setSeqId(UUID.random19() + TransferEventType.PULL_PROJECT_INFO.getName());
                    transferMessageInfo.setMessage(TransferEventType.PULL_PROJECT_INFO.getName());
                    TransferData data = new TransferData();
                    data.setEnforce(request.getEnforce());
                    transferMessageInfo.setData(data);
                    String jsonStr = JsonUtils.toJson(transferMessageInfo);
                    String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
                    TransferRequest transferRequest = new TransferRequest();
                    transferRequest.setSerialNumber(deviceSn);
                    transferRequest.setBody(base64Message);
                    transferRequest.setCommand(0);
                    ServiceResponse<TransferResponse> response = hubControlServiceV1.transfer(transferRequest);
                    if (response != null && !response.isSuccess() && ControllerErrorCode.DEVICE_OFFLINE.getCode().equals(response.getErrorContext().getCode())) {
                        log.info("Notify device ProjectInfoChange, 设备已离线，本次不发送通知,deviceSn:[{}]", deviceSn);
                        continue;
                    }

                    TransferResponse result = response.pickDataThrowException();
                    log.info("Notify device ProjectInfoChange, deviceSn:[{}]", deviceSn);
                } catch (Exception e) {
                    log.info("Notify device ProjectInfoChange failed. deviceSn:[{}],error:[{}]", deviceSn, e);
                }
            }
        }
        return new ServiceResponse<>(projectResponse);
    }
}
