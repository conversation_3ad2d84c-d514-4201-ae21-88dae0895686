package com.moredian.magicube.device.pipeline.executor.strategy;

import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import com.moredian.magicube.device.pipeline.bo.ActivateDeviceContext;
import com.moredian.magicube.device.pipeline.executor.AbstractActivateDeviceEngineExecutor;
import com.moredian.magicube.device.pipeline.pipe.device.DeviceNameConvertPipe;
import com.moredian.magicube.device.pipeline.pipe.md.MdArithmeticPipe;
import com.moredian.magicube.device.pipeline.pipe.reverse.InsertActivationRecordPipe;
import com.moredian.magicube.device.pipeline.pipe.reverse.ReverseOrgIdPipe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 非钉激活流程去掉空间绑定
 * @create 2025-03-12 18:13
 */
@Component
public class MoReverseActivateRemoveSpaceExecutor extends AbstractActivateDeviceEngineExecutor {

    @Autowired
    private MdArithmeticPipe mdArithmeticPipe;

    @Autowired
    private DeviceNameConvertPipe deviceNameConvertPipe;

    @Autowired
    private InsertActivationRecordPipe insertActivationRecordPipe;

    @Autowired
    private ReverseOrgIdPipe reverseOrgIdPipe;


    @Override
    public void afterPropertiesSet() throws Exception {
        pipeline.addPipe(reverseOrgIdPipe);
        pipeline.addPipe(commonActivateCheckPipe);
        pipeline.addPipe(checkDeviceAuthorizePipe);
        pipeline.addPipe(mdArithmeticPipe);
        pipeline.addPipe(deviceNameConvertPipe);
        pipeline.addPipe(insertDevicePipe);
        pipeline.addPipe(uploadApkVersionPipe);
        pipeline.addPipe(enableBizPipe);
        pipeline.addPipe(insertDeviceLogPipe);
        pipeline.addPipe(insertActivationRecordPipe);
        pipeline.addPipe(updateDeviceWhitePipe);
        pipeline.addPipe(monitorDataPipe);
        pipeline.addPipe(publishMsgPipe);
        pipeline.addPipe(buildResultPipe);
    }


    @Override
    protected void validCustomParameter(ActivateDeviceDTO activateDeviceDTO) {

    }

    @Override
    protected String setKey(ActivateDeviceDTO activateDeviceDTO, ActivateDeviceContext activateDeviceContext) {
        return "MoReverseActivate_" + activateDeviceDTO.getDeviceType();
    }

    @Override
    public Integer getType() {
        return DeviceActivateEngineEnums.MO_REVERSE_REMOVE_SPACE_ACTIVATE.getCode();
    }
}
