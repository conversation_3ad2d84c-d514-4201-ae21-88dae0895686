package com.moredian.space.message;

import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.magicube.common.model.msg.device.DeviceNameChangeEvent;
import com.moredian.magicube.device.dao.entity.InventoryDevice;
import com.moredian.magicube.device.dao.mapper.InventoryDeviceMapper;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.service.DeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description：设备绑定空间（绑定或则换绑）消息
 * @date ：2024/08/27 11:35
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeviceBindSpaceListener {

    private final DeviceService deviceService;
    private final InventoryDeviceMapper inventoryDeviceMapper;

    /**
     * 设备绑定空间消息
     */
    @Subscribe
    public void subscribeBindMsg(SaveTreeDeviceRelationMsg msg) {
        log.info("监听设备绑定空间消息，msg: {}", msg);
        sendDeviceNameChangeMsg(Long.parseLong(msg.getDeviceId()));
    }

    /**
     * 设备绑定空间变更消息
     */
    @Subscribe
    public void subscribeDisableOrgMsg(UpdateTreeDeviceRelationMsg msg) {
        log.info("设备绑定空间变更消息，msg: {}", msg);
        sendDeviceNameChangeMsg(Long.parseLong(msg.getDeviceId()));
    }

    private void sendDeviceNameChangeMsg(Long deviceId) {
        DeviceInfoDTO deviceInfoDTO = deviceService.getById(deviceId).pickDataThrowException();
        if (deviceInfoDTO != null) {
            // 通知设备名称被修改的事件，主要是通知调用钉钉开放平台接口修改
            DeviceNameChangeEvent deviceNameChangeEvent = new DeviceNameChangeEvent();
            BeanUtils.copyProperties(deviceInfoDTO, deviceNameChangeEvent);
            deviceNameChangeEvent.setName(deviceInfoDTO.showName());
            deviceNameChangeEvent.setDeviveType(deviceInfoDTO.getDeviceType());
            InventoryDevice inventoryDevice = inventoryDeviceMapper
                .getByDeviceSn(deviceInfoDTO.getDeviceSn());
            if (inventoryDevice != null) {
                deviceNameChangeEvent.setTpDevId(inventoryDevice.getThirdDeviceId());
            }
            log.info("设备绑定空间，发送设备名称变更消息deviceNameChangeEvent:{}", deviceNameChangeEvent);
            EventBus.publish(deviceNameChangeEvent);
        }
    }
}
