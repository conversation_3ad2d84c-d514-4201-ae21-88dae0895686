package com.moredian.space.message;

import com.google.common.collect.Lists;
import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.rmq.annotation.Subscribe;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.RefreshDeviceConfigMsg;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dao.entity.DeviceActiveState;
import com.moredian.magicube.device.dao.entity.SpaceRuleRelation;
import com.moredian.magicube.device.dto.device.RuleBindSpaceDTO;
import com.moredian.magicube.device.dto.device.RuleDeleteDTO;
import com.moredian.magicube.device.manager.ActivityManager;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.SpaceRuleRelationManager;
import com.moredian.magicube.device.service.DeviceRuleService;
import com.moredian.space.dto.device.SpaceTreeDeviceRelationDTO;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import com.moredian.space.service.v2.SpaceTreeDeviceRelationServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.DEVICE_ACTIVATE_SUCCESS;
import static com.moredian.magicube.device.enums.DeviceActiveStateEnum.RELEVANCE_MEETING_SUCCESS;

/**
 * <AUTHOR>
 * @version $Id: SpaceTreeDeviceListener.java, v 1.0 Exp $
 * @desc
 */
@Slf4j
@Component
public class SpaceTreeDeviceListener {

    @Resource
    private DeviceRuleService deviceRuleService;

    @Resource
    private SpaceRuleRelationManager spaceRuleRelationManager;

    @SI
    private SpaceTreeDeviceRelationServiceV2 spaceTreeDeviceRelationServiceV2;

    @Resource
    private DeviceManager deviceManager;

    @Resource
    private ActivityManager activityManager;


    /**
     * 订阅空间发生更新得消息
     * 1. 用于刷新设备配置，发出刷新配置消息。(treeIds 反查空间下的设备，再循环发配置刷新消息)
     * @param msg
     */
    @Subscribe
    public void subscribeTreeUpdate(UpdateTreeMsg msg) {
        log.info("订阅空间更新消息，msg[{}]", msg);
        List<Long> treeIds = Lists.newArrayList();
        Long treeId = msg.getTreeId();
        Long orgId = msg.getOrgId();
        List<Long> treeIdList = msg.getTreeIdList();
        if (CollectionUtils.isNotEmpty(treeIdList)) {
            treeIds.addAll(treeIdList);
        }
        if (treeId != null) {
            treeIds.add(treeId);
        }

        if (ObjectUtils.isEmpty(treeIds)) {
            log.warn("空间ids为空，不做处理");
            return;
        }

        List<SpaceTreeDeviceRelationDTO> treeDeviceList = spaceTreeDeviceRelationServiceV2
                .listByOrgIdsAndTreeIds(Lists.newArrayList(orgId), treeIds).pickDataThrowException();
        if (CollectionUtils.isNotEmpty(treeDeviceList)) {
            List<Long> deviceIds = treeDeviceList.stream().map(SpaceTreeDeviceRelationDTO::getDeviceId)
                    .map(Long::valueOf)
                    .distinct()
                    .collect(Collectors.toList());

            List<Device> devices = deviceManager.listByIds(deviceIds);
            // 这些设备进行刷新配置操作
            if(CollectionUtils.isNotEmpty(devices)) {
                for (Device device : devices) {
                    RefreshDeviceConfigMsg refreshMsg = new RefreshDeviceConfigMsg();
                    refreshMsg.setDeviceSn(device.getDeviceSn());
                    refreshMsg.setTimeStamp(System.currentTimeMillis());
                    refreshMsg.setOrgId(device.getOrgId());
                    refreshMsg.setOperationType(2);
                    EventBus.publish(refreshMsg);
                }
            }
        }
    }



    /**
     * 删除空间树消息
     */
    @Subscribe
    public void subscribeDisableOrgMsg(DeleteTreeMsg msg) {
        log.info("监听删除空间树消息，msg: {}", msg);

        Long orgId = msg.getOrgId();

        List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgId(orgId);
        if (CollectionUtils.isEmpty(spaceRuleRelationList)) {
            log.info("机构无iot规则或规则没绑定过空间，orgId: {}", orgId);
            return;
        }

        Map<Long, List<SpaceRuleRelation>> ruleSpaceMap = spaceRuleRelationList.stream().collect(Collectors.groupingBy(SpaceRuleRelation::getRuleId));
        List<Long> spaceIdList, opSpaceIdList;
        List<SpaceRuleRelation> ruleSpaceList;

        for (Map.Entry<Long, List<SpaceRuleRelation>> ruleSpaceEntry : ruleSpaceMap.entrySet()) {

            ruleSpaceList = ruleSpaceEntry.getValue();
            spaceIdList = ruleSpaceList.stream().map(SpaceRuleRelation::getSpaceId).collect(Collectors.toList());

            opSpaceIdList = deepCopy(spaceIdList);

            opSpaceIdList.retainAll(msg.getTreeIds());

            if (CollectionUtils.isEmpty(opSpaceIdList)) {
                log.info("机构规则没有绑定过删除的空间树");
                continue;
            }
            spaceIdList.removeAll(opSpaceIdList);

            RuleBindSpaceDTO request = new RuleBindSpaceDTO().setOrgId(orgId).setRuleId(ruleSpaceEntry.getKey()).setSpaceIdList(spaceIdList)
                .setTriggerValue("");
            request.setRequestSource(2);
            deviceRuleService.ruleBingSpace(request);
        }
    }

    /**
     * 设备绑定空间消息,多个设备增量->同步jet link
     */
    @Subscribe
    public void subscribeDisableOrgMsg(SaveTreeDeviceRelationMsg msg) {
        log.info("监听设备绑定空间消息，msg: {}", msg);

        Long orgId = msg.getOrgId();

        List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgId(orgId);
        if (CollectionUtils.isEmpty(spaceRuleRelationList)) {
            log.info("机构无iot规则或规则没绑定过空间，orgId: {}", orgId);
            return;
        }

        Map<Long, List<SpaceRuleRelation>> ruleSpaceMap = spaceRuleRelationList.stream().collect(Collectors.groupingBy(SpaceRuleRelation::getRuleId));

        List<Long> spaceIdList;
        List<SpaceRuleRelation> ruleSpaceList;

        for (Map.Entry<Long, List<SpaceRuleRelation>> ruleSpaceEntry : ruleSpaceMap.entrySet()) {

            ruleSpaceList = ruleSpaceEntry.getValue();
            spaceIdList = ruleSpaceList.stream().map(SpaceRuleRelation::getSpaceId).collect(Collectors.toList());

            if (!spaceIdList.contains(msg.getTreeId())) {
                log.info("机构规则没有绑定过（设备关联的空间）");
                continue;
            }

            RuleBindSpaceDTO request = new RuleBindSpaceDTO().setOrgId(orgId).setRuleId(ruleSpaceEntry.getKey()).setSpaceIdList(spaceIdList)
                .setTriggerValue("");
            request.setRequestSource(2);
            deviceRuleService.ruleBingSpace(request);
        }
    }

    /**
     * 设备绑定空间变更消息
     */
    @Subscribe
    public void subscribeDisableOrgMsg(UpdateTreeDeviceRelationMsg msg) {
        log.info("设备绑定空间变更消息，msg: {}", msg);

        String deviceId = msg.getDeviceId();
        Device device = deviceManager.getById(Long.valueOf(deviceId));

        if (device != null) {
            DeviceActiveState deviceActiveState = activityManager.getDeviceActiveState(device.getDeviceSn());
            if (deviceActiveState != null){
                // 设备状态在激活成功状态，修改当前状态为关联成功状态
                if (DEVICE_ACTIVATE_SUCCESS.getCode() == deviceActiveState.getState()) {
                    log.info("修改设备激活状态[{}]", deviceActiveState);
                    activityManager.deviceActiveStateChange(device.getDeviceSn(), RELEVANCE_MEETING_SUCCESS.getCode(),
                            null, null);
                }
            }
        }

        Long orgId = msg.getOrgId();

        List<SpaceRuleRelation> spaceRuleRelationList = spaceRuleRelationManager.getRelationByOrgId(orgId);
        if (CollectionUtils.isEmpty(spaceRuleRelationList)) {
            log.info("机构无iot规则或规则没绑定过空间，orgId: {}", orgId);
            return;
        }

        Map<Long, List<SpaceRuleRelation>> ruleSpaceMap = spaceRuleRelationList.stream().collect(Collectors.groupingBy(SpaceRuleRelation::getRuleId));

        List<Long> spaceIdList;
        for (Map.Entry<Long, List<SpaceRuleRelation>> ruleSpaceEntry : ruleSpaceMap.entrySet()) {
            spaceIdList = ruleSpaceEntry.getValue().stream().map(SpaceRuleRelation::getSpaceId).collect(Collectors.toList());

            if (!spaceIdList.contains(msg.getTreeId()) && !spaceIdList.contains(msg.getOldTreeId())) {
                log.info("机构规则没有绑定过（设备关联的空间）");
                continue;
            }

            RuleBindSpaceDTO request = new RuleBindSpaceDTO().setOrgId(orgId).setRuleId(ruleSpaceEntry.getKey()).setSpaceIdList(spaceIdList)
                .setTriggerValue("");
            request.setRequestSource(2);
            deviceRuleService.ruleBingSpace(request);
        }
    }

    private static List<Long> deepCopy(List<Long> spaceIdList) {
        List<Long> opSpaceIdList = Lists.newArrayList();

        spaceIdList.stream().forEach(spaceId -> {
            opSpaceIdList.add(spaceId);
        });
        return opSpaceIdList;
    }

    public static void main(String[] args) {
        List<Long> spaceIdList = Lists.newArrayList(1L,2L,3L,4L);
        List<Long> opSpaceIdList = deepCopy(spaceIdList);

        List<Long>   deleteTreeIds = Lists.newArrayList(3L,4L,5L);

        opSpaceIdList.removeAll(deleteTreeIds);
        System.out.println(spaceIdList);
    }
}
