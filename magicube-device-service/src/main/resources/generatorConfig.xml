<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="Mysql" targetRuntime="MyBatis3">
        <commentGenerator>
            <property name="suppressDate" value="true" />
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true" />
        </commentGenerator>

    <!--    <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
            &lt;!&ndash; caseSensitive默认false，当数据库表名区分大小写时，可以将该属性设置为true &ndash;&gt;
            <property name="caseSensitive" value="false"/>
        </plugin>-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="************************************************************"
                        userId="moredian"
                        password="moredian@1">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.moredian.magicube.device.dao.entity"
                            targetProject="./src/main/java"/>

        <sqlMapGenerator targetPackage="com.moredian.magicube.device.dao.mapper"
                         targetProject="./src/main/java"/>

        <javaClientGenerator targetPackage="com.moredian.magicube.device.dao.mapper"
                             targetProject="./src/main/java"
                             type="XMLMAPPER"/>

        <table tableName="hive_rule" domainObjectName="HiveRule" enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>
        <table tableName="hive_rule_template" domainObjectName="HiveRuleTemplate" enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>
        <table tableName="hive_space_rule_relation" domainObjectName="HiveSpaceRuleRelation" enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>
        <table tableName="hive_device_rule_relation" domainObjectName="HiveDeviceRuleRelation" enableCountByExample="false" enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>

    </context>
</generatorConfiguration>