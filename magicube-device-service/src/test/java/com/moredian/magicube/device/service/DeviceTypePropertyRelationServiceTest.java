package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypePropertyRelationDTO;
import com.moredian.magicube.device.dto.type.UpdateDeviceTypePropertyDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceTypePropertyRelationServiceTest extends BaseTest {

    @Autowired
    private DeviceTypePropertyRelationService deviceTypePropertyRelationService;

    @Test
    public void insert() {
        InsertDeviceTypePropertyRelationDTO dto = new InsertDeviceTypePropertyRelationDTO();
        dto.setDeviceType(61);
        dto.setDeviceTypePropertyId(1721737464256659456L);
        Long id = deviceTypePropertyRelationService.insert(dto).pickDataThrowException();
        log.info("新增设备类型和属性关系id:{}", id);
    }

    @Test
    public void resetRelationByDeviceType() {
        List<Long> propertyIds = new ArrayList<>();
        propertyIds.add(1721737464256659456L);
        propertyIds.add(1721737343997575168L);
        Boolean result = deviceTypePropertyRelationService
            .resetRelationByDeviceType(61, propertyIds).pickDataThrowException();
        log.info("重置设备类型和多个属性的关系result:{}", result);
    }

    @Test
    public void resetRelationByPropertyId() {
        List<Integer> deviceTypes = new ArrayList<>();
        deviceTypes.add(61);
        deviceTypes.add(62);
        Boolean result = deviceTypePropertyRelationService
            .resetRelationByPropertyId(1721737464256659456L, deviceTypes).pickDataThrowException();
        log.info("重置属性和多个设备类型的关系result:{}", result);
    }

    @Test
    public void listPropertyIdByDeviceType() {
        List<Long> result = deviceTypePropertyRelationService.listPropertyIdByDeviceType(61)
            .pickDataThrowException();
        log.info("根据设备类型查询属性Id列表result:{}", result);
    }

    @Test
    public void listDeviceTypeByPropertyId() {
        List<Integer> result = deviceTypePropertyRelationService
            .listDeviceTypeByPropertyId(1721737464256659456L)
            .pickDataThrowException();
        log.info("根据设备类型属性Id查询设备类型列表result:{}", result);
    }

    @Test
    public void test() {
        List<Integer> result = deviceTypePropertyRelationService
            .listDeviceTypeByPropertyId(1721737464256659456L)
            .pickDataThrowException();
        log.info("根据设备类型属性Id查询设备类型列表result:{}", result);
    }
}
