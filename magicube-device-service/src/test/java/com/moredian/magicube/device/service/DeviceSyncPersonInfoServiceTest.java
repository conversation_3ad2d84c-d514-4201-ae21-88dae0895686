package com.moredian.magicube.device.service;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.person.PagePersonInfoDTO;
import com.moredian.magicube.device.dto.person.PersonInfoDTO;
import com.moredian.magicube.device.dto.person.QueryPersonInfoDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/26
 */
public class DeviceSyncPersonInfoServiceTest extends BaseTest {

    @Autowired
    private DeviceSyncPersonInfoService deviceSyncPersonInfoService;

    @Test
    public void pagePersonInfo(){
        PagePersonInfoDTO dto = new PagePersonInfoDTO();
        //orgId=1773715321882935296,deviceId=1785995655584415744,deviceSn=K49919231026KN0043,groupIdList=[1775544144727375872],needFeature=true,needCard=true,needGroup=true,modelVersion=1.6.0,featureType=2,pageNo=1,pageSize=4
        dto.setDI(1785995655584415744L);
        dto.setDS("K49919231026KN0043");
        dto.setOI(1773715321882935296L);
        dto.setFT(2);
        dto.setGL(Arrays.asList(1775544144727375872L));
        dto.setMV("1.6.0");
        dto.setNC(true);
        dto.setNF(true);
        dto.setNG(true);
        dto.setPageNo(1);
        dto.setPageSize(4);
        Pagination<PersonInfoDTO> personInfoDTOPagination = deviceSyncPersonInfoService.pagePersonInfo(dto).pickDataThrowException();
        System.out.println(JsonUtils.toJson(personInfoDTOPagination));
    }

    @Test
    public void queryPersonInfo(){
        QueryPersonInfoDTO dto = new QueryPersonInfoDTO();
        dto.setDI(1785995655584415744L);
        dto.setDS("K49919231026KN0043");
        dto.setOI(1773715321882935296L);
        dto.setFT(2);
        dto.setMV("1.6.0");
        dto.setNC(true);
        dto.setNF(true);
        dto.setNG(true);
        dto.setPL(Arrays.asList(1773715321882935296L));
        List<PersonInfoDTO> personInfoDTOS = deviceSyncPersonInfoService.queryPersonInfo(dto).pickDataThrowException();
        System.out.println(JsonUtils.toJson(personInfoDTOS));
    }
}
