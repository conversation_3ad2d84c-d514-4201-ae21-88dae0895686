package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date : 2024/3/14
 */
public class ThirdDeviceRelationServiceImplTest extends BaseTest {
    @Autowired
    private ThirdDeviceRelationService thirdDeviceRelationService;

    @Test
    public void testGetByTpId() {
        System.out.println(thirdDeviceRelationService.getByTpId("123", 5).pickDataThrowException());
    }
}