package com.moredian.magicube.device.service;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.common.enums.TransferEventType;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dao.entity.Device;
import com.moredian.magicube.device.dto.device.*;
import com.moredian.magicube.device.dto.version.VersionDTO;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.manager.DeviceTypePropertyManager;
import com.moredian.magicube.device.service.helper.DdBizManageHelper;
import com.moredian.space.dto.device.BindSpaceTreeDto;
import com.moredian.space.service.SpaceTreeDeviceRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


@Slf4j
public class DeviceServiceTest extends BaseTest {

    private static final Long orgId = 1770119291794882560L;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceManager deviceManager;

    @Autowired
    private DeviceIotService deviceIotService;

    @Autowired
    private DeviceTypePropertyManager deviceTypePropertyManager;

    @Autowired
    private SpaceTreeDeviceRelationService treeDeviceRelationService;

    @Resource
    private DdBizManageHelper ddBizManageHelper;

    @SI
    private ActivityService activityService;
    @Test
    void test() {
        VersionDTO versionDTO = ddBizManageHelper.getNewAppVersionBySCTypeAndIsActiveAndOrgId(
                1788499758956937216L,
                null,500
                );
        System.out.println(versionDTO.toString());
    }



    @Test
    void updateSceneType() {
        String deviceSn = "870115230413KN0032";
        Integer sceneType = 36;
        Long memberId = 17701192917948820L;


        Boolean b = activityService.updateSceneType(deviceSn, sceneType, memberId).pickDataThrowException();
        log.info("修改设备场景类型b:{}", b);


    }


    @Test
    public void getStatusByDeviceSn() {
        DeviceStatusDTO deviceStatusDTO = deviceIotService.getStatusByDeviceSn("270604210101KR0014")
            .pickDataThrowException();
        log.info("查询设备状态deviceStatusDTO:{}", deviceStatusDTO);
    }

    @Test
    public void getStatusByDeviceSn1() {
        DeviceStatusDTO deviceStatusDTO = deviceIotService.getStatusByDeviceSn(null)
            .pickDataThrowException();
        log.info("查询设备状态deviceStatusDTO:{}", deviceStatusDTO);
    }

    @Test
    public void listDeviceStateByDeviceSns() {
        List<String> deviceSns = new ArrayList<>();
        deviceSns.add("270604210101KR0014");
        deviceSns.add("260910210121KN2035");
        List<DeviceStatusDTO> deviceStatusDTOs = deviceIotService
            .listDeviceStateByDeviceSns(deviceSns).pickDataThrowException();
        log.info("根据设备sn列表查询设备状态列表deviceStatusDTOs:{}", deviceStatusDTOs);
    }

    @Test
    public void listDeviceStateByDeviceSns1() {
        String str = "04392602a06b,1801878260501757952";

        List<String> list = Arrays.asList(str.split(","));

//        List<String> deviceSns = new ArrayList<>();
//        deviceSns.add("270604210101KR0014");
//        deviceSns.add("260910210121KN2035");
        List<DeviceStatusDTO> deviceStatusDTOs = deviceIotService.listDeviceStateByDeviceSns(list)
            .pickDataThrowException();
        log.info("根据设备sn列表查询设备状态列表deviceStatusDTOs:{}", deviceStatusDTOs);
    }

    @Test
    public void reboot() {
        RebootDTO dto = new RebootDTO();
        dto.setSerialNumber("270604210101KR0014");
        DeviceStatusDTO deviceStatusDTO = deviceIotService.reboot(dto).pickDataThrowException();
        log.info("重启设备deviceStatusDTO:{}", deviceStatusDTO);
    }

    @Test
    public void reboot1() {
        RebootDTO dto = new RebootDTO();
//        dto.setSerialNumber("270604210101KR0014");
        DeviceStatusDTO deviceStatusDTO = deviceIotService.reboot(dto).pickDataThrowException();
        log.info("重启设备deviceStatusDTO:{}", deviceStatusDTO);
    }

    @Test
    public void transfer() {
        TransferMessageDTO<String> transferMessageDTO = new TransferMessageDTO<>();
        transferMessageDTO.setEventType(TransferEventType.ADMIN_OPEN_DOOR.getValue());
        transferMessageDTO.setSeverity(5);
        transferMessageDTO.setSeqId(UUID.randomUUID().toString());
        transferMessageDTO.setMessage("Download config file");
        String jsonStr = JsonUtils.toJson(transferMessageDTO);
        String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
        TransferDTO dto = new TransferDTO();
        dto.setSerialNumber("270604210101KR0014");
        dto.setBody(base64Message);
        DeviceStatusDTO deviceStatusDTO = deviceIotService.transfer(dto).pickDataThrowException();
        log.info("透传命令deviceStatusDTO:{}", deviceStatusDTO);
    }

    @Test
    public void transfer1() {
        TransferMessageDTO<String> transferMessageDTO = new TransferMessageDTO<>();
        transferMessageDTO.setEventType(TransferEventType.ADMIN_OPEN_DOOR.getValue());
        transferMessageDTO.setSeverity(5);
        transferMessageDTO.setSeqId(UUID.randomUUID().toString());
        transferMessageDTO.setMessage("Download config file");
        String jsonStr = JsonUtils.toJson(transferMessageDTO);
        String base64Message = Base64.encodeBase64String(jsonStr.getBytes());
        TransferDTO dto = new TransferDTO();
//        dto.setSerialNumber("270604210101KR0014");
        dto.setBody(base64Message);
        DeviceStatusDTO deviceStatusDTO = deviceIotService.transfer(dto).pickDataThrowException();
        log.info("透传命令deviceStatusDTO:{}", deviceStatusDTO);
    }

    @Test
    public void getByDeviceSn() {
        DeviceInfoDTO deviceInfoDTO = deviceService.getByDeviceSn("439918220715KN0008")
            .pickDataThrowException();
        log.info("根据设备sn获取设备信息deviceInfoDTO:{}", deviceInfoDTO);
    }

    @Test
    public void getByDeviceSn1() {
        DeviceInfoDTO deviceInfoDTO = deviceService.getByDeviceSn(null).pickDataThrowException();
        log.info("根据设备sn获取设备信息deviceInfoDTO:{}", deviceInfoDTO);
    }

    @Test
    public void listByDeviceSns() {
        List<String> deviceSns = new ArrayList<>();
        deviceSns.add("270604210101KR0014");
        deviceSns.add("260910210121KN2035");
        List<DeviceInfoDTO> deviceInfoDTOs = deviceService.listByDeviceSns(deviceSns)
            .pickDataThrowException();
        log.info("根据设备sn列表获取设备信息列表deviceInfoDTOs:{}", deviceInfoDTOs);
    }

    @Test
    public void listByDeviceSns1() {
        List<String> deviceSns = new ArrayList<>();
//        deviceSns.add("270604210101KR0014");
//        deviceSns.add("260910210121KN2035");
        List<DeviceInfoDTO> deviceInfoDTOs = deviceService.listByDeviceSns(deviceSns)
            .pickDataThrowException();
        log.info("根据设备sn列表获取设备信息列表deviceInfoDTOs:{}", deviceInfoDTOs);
    }

    @Test
    public void getById() {
        DeviceInfoDTO deviceInfoDTO = deviceService.getById(1713378092099043328L)
            .pickDataThrowException();
        log.info("根据设备Id获取设备信息deviceInfoDTO:{}", deviceInfoDTO);
    }

    @Test
    public void getById1() {
        DeviceInfoDTO deviceInfoDTO = deviceService.getById(1828905290859282432L).pickDataThrowException();
        log.info("根据设备Id获取设备信息deviceInfoDTO:{}", deviceInfoDTO);
    }

    @Test
    public void listByIds() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1797089521750245376L);
//        deviceIds.add(1712217256928215041L);
        List<DeviceInfoDTO> deviceInfoDTOs = deviceService.listByIds(deviceIds)
            .pickDataThrowException();
        log.info("根据设备Id获取设备信息deviceInfoDTOs:{}", deviceInfoDTOs);
    }

    @Test
    public void listByIds1() {
        List<DeviceInfoDTO> deviceInfoDTOs = deviceService.listByIds(null).pickDataThrowException();
        log.info("根据设备Id获取设备信息deviceInfoDTOs:{}", deviceInfoDTOs);
    }

    @Test
    public void listByOrgId() {
        List<DeviceInfoDTO> deviceInfoDTOs = deviceService.listByOrgId(orgId)
            .pickDataThrowException();
            log.info("查询该机构下所有设备信息deviceInfoDTOs:{}", deviceInfoDTOs);
    }

    @Test
    public void listByOrgId1() {
        List<DeviceInfoDTO> deviceInfoDTOs = deviceService.listByOrgId(null)
            .pickDataThrowException();
        log.info("查询该机构下所有设备信息deviceInfoDTOs:{}", deviceInfoDTOs);
    }

    @Test
    public void update() {
        UpdateDeviceDTO dto = new UpdateDeviceDTO();
        dto.setDeviceId(1713378092099043328L);
        dto.setOrgId(orgId);
        dto.setDeviceName("方杰测试设备");
        Boolean result = deviceService.update(dto).pickDataThrowException();
        log.info("修改设备信息result:{}", result);
    }

    @Test
    public void update1() {
        UpdateDeviceDTO dto = new UpdateDeviceDTO();
//        dto.setDeviceId(1713378092099043328L);
        dto.setOrgId(orgId);
        dto.setDeviceName("方杰测试设备");
        Boolean result = deviceService.update(dto).pickDataThrowException();
        log.info("修改设备信息result:{}", result);
    }

    @Test
    public void update2() {
        UpdateDeviceDTO dto = new UpdateDeviceDTO();
        dto.setDeviceId(1713378092099043328L);
        dto.setOrgId(orgId);
        dto.setDeviceName("方杰测试设备111111111111111111111");
        Boolean result = deviceService.update(dto).pickDataThrowException();
        log.info("修改设备信息result:{}", result);
    }

    @Test
    public void batchUpdate() {
        List<UpdateDeviceDTO> list = new ArrayList<>();
        UpdateDeviceDTO dto = new UpdateDeviceDTO();
        dto.setDeviceId(1713378092099043328L);
        dto.setOrgId(orgId);
        dto.setDeviceName("方杰测试设备11");
        list.add(dto);
        Boolean result = deviceService.batchUpdate(list).pickDataThrowException();
        log.info("批量修改设备result:{}", result);
    }

    @Test
    public void batchUpdate1() {
        List<UpdateDeviceDTO> list = new ArrayList<>();
        UpdateDeviceDTO dto = new UpdateDeviceDTO();
//        dto.setDeviceId(1713378092099043328L);
        dto.setOrgId(orgId);
        dto.setDeviceName("方杰测试设备11");
        list.add(dto);
        Boolean result = deviceService.batchUpdate(list).pickDataThrowException();
        log.info("批量修改设备result:{}", result);
    }

    @Test
    public void batchUpdate2() {
        List<UpdateDeviceDTO> list = new ArrayList<>();
        UpdateDeviceDTO dto = new UpdateDeviceDTO();
        dto.setDeviceId(1713378092099043328L);
        dto.setOrgId(orgId);
        dto.setDeviceName("方杰测试设备111111222222222222222221");
        list.add(dto);
        Boolean result = deviceService.batchUpdate(list).pickDataThrowException();
        log.info("批量修改设备result:{}", result);
    }

    @Test
    public void deleteById() {
        Boolean result = deviceService.deleteById(orgId, 1713378092099043328L)
            .pickDataThrowException();
        log.info("删除设备result:{}", result);
    }

    @Test
    public void deleteById1() {
        Boolean result = deviceService.deleteById(orgId, null).pickDataThrowException();
        log.info("删除设备result:{}", result);
    }

    @Test
    public void listPage() {
        QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
        queryDeviceDTO.setOrgId(1773715442142019584L);
        queryDeviceDTO.setPageNo(1);
        queryDeviceDTO.setPageSize(20);
        Pagination<DeviceInfoDTO> result = deviceService.listPage(queryDeviceDTO)
            .pickDataThrowException();
        log.info("分页查询设备信息result:{}", result);
    }

    @Test
    public void listPage1() {
        QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
        queryDeviceDTO.setOrgId(orgId);
        queryDeviceDTO.setOnlineStatus(1);
        queryDeviceDTO.setPageNo(1);
        queryDeviceDTO.setPageSize(20);
        Pagination<DeviceInfoDTO> result = deviceService.listPage(queryDeviceDTO)
            .pickDataThrowException();
        log.info("分页查询设备信息result:{}", result);
    }

    @Test
    public void listPage2() {
        QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
        queryDeviceDTO.setOrgId(orgId);
        queryDeviceDTO.setOnlineStatus(1);
        queryDeviceDTO.setDeviceTypes(deviceTypePropertyManager.getDeviceTypeByPropertyKey(
            DeviceTypeConstants.ROOSTER_DEVICE_LIST));
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1713378092099043328L);
        deviceIds.add(1713378092099043323L);
        queryDeviceDTO.setDeviceIds(deviceIds);
        queryDeviceDTO.setPageNo(1);
        queryDeviceDTO.setPageSize(20);
        Pagination<DeviceInfoDTO> result = deviceService.listPage(queryDeviceDTO)
            .pickDataThrowException();
        log.info("分页查询设备信息result:{}", result);
    }

    @Test
    public void listPage3() {
        QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
//        queryDeviceDTO.setOrgId(orgId);
        queryDeviceDTO.setOnlineStatus(1);
        Pagination<DeviceInfoDTO> result = deviceService.listPage(queryDeviceDTO)
            .pickDataThrowException();
        log.info("分页查询设备信息result:{}", result);
    }

    @Test
    public void getByOrgIdAndDeviceName() {
        DeviceInfoDTO result = deviceService.getByOrgIdAndDeviceName(orgId, "F2门禁机KR0014")
            .pickDataThrowException();
        log.info("按设备名称查询设备详情result:{}", result);
    }

    @Test
    public void getByOrgIdAndDeviceName1() {
        DeviceInfoDTO result = deviceService.getByOrgIdAndDeviceName(null, "F2门禁机KR0014")
            .pickDataThrowException();
        log.info("按设备名称查询设备详情result:{}", result);
    }

    @Test
    public void listDeviceIdByLikeName() {
        List<Long> result = deviceService.listDeviceIdByLikeName(orgId, "F2门禁机KR0014")
            .pickDataThrowException();
        log.info("按设备名称查询模糊查询设备ID列表result:{}", result);
    }

    @Test
    public void listDeviceIdByLikeName1() {
        List<Long> result = deviceService.listDeviceIdByLikeName(null, "F2门禁机KR0014")
            .pickDataThrowException();
        log.info("按设备名称查询模糊查询设备ID列表result:{}", result);
    }

    @Test
    public void listDeviceNameByIds() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1713378092099043328L);
        deviceIds.add(1713378092099043323L);
        List<String> result = deviceService.listDeviceNameByIds(deviceIds).pickDataThrowException();
        log.info("根据设备Id列表查询设备名称列表result:{}", result);
    }

    @Test
    public void listDeviceNameByIds1() {
        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1713378092099043328L);
//        deviceIds.add(1713378092099043323L);
        List<String> result = deviceService.listDeviceNameByIds(deviceIds).pickDataThrowException();
        log.info("根据设备Id列表查询设备名称列表result:{}", result);
    }

    @Test
    public void listDeviceIdByOrgIdAndType() {
        List<Long> result = deviceService.listDeviceIdByOrgIdAndType(orgId, 67)
            .pickDataThrowException();
        log.info("根据设备类型查询设备Id列表result:{}", result);
    }

    @Test
    public void listDeviceIdByOrgIdAndType1() {
        List<Long> result = deviceService.listDeviceIdByOrgIdAndType(null, 67)
            .pickDataThrowException();
        log.info("根据设备类型查询设备Id列表result:{}", result);
    }

    @Test
    public void listByOrgIdAndType() {
        List<DeviceInfoDTO> result = deviceService.listByOrgIdAndType(orgId, 67)
            .pickDataThrowException();
        log.info("根据设备类型查询设备信息列表result:{}", result);
    }

    @Test
    public void listByOrgIdAndType1() {
        List<DeviceInfoDTO> result = deviceService.listByOrgIdAndType(null, 67)
            .pickDataThrowException();
        log.info("根据设备类型查询设备信息列表result:{}", result);
    }

    @Test
    public void listByOrgIdAndTypes() {
        List<DeviceInfoDTO> result = deviceService
            .listByOrgIdAndTypes(orgId, DeviceType.getDoorDevices()).pickDataThrowException();
        log.info("根据设备类型列表查询设备信息列表result:{}", result);
    }

    @Test
    public void listByOrgIdAndTypes1() {
        List<DeviceInfoDTO> result = deviceService.listByOrgIdAndTypes(orgId, new ArrayList<>())
            .pickDataThrowException();
        log.info("根据设备类型列表查询设备信息列表result:{}", result);
    }

    @Test
    public void listByLikeCondition() {
        QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
        queryDeviceDTO.setOrgId(1770119291794882560L);
        queryDeviceDTO.setDeviceTypes(DeviceType.getDoorDevices());
//        queryDeviceDTO.setKeywords("KR");
//        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1713378092099043328L);
//        deviceIds.add(1713378092099043323L);
//        queryDeviceDTO.setDeviceIds(deviceIds);
        List<DeviceInfoDTO> result = deviceService.listByLikeCondition(queryDeviceDTO)
            .pickDataThrowException();
        log.info("根据条件模糊查询设备信息列表result:{}", result);
    }

    @Test
    public void listByLikeCondition1() {
        QueryDeviceDTO queryDeviceDTO = new QueryDeviceDTO();
//        queryDeviceDTO.setOrgId(orgId);
        queryDeviceDTO.setDeviceTypes(DeviceType.getDoorDevices());
        queryDeviceDTO.setKeywords("KR");
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1713378092099043328L);
        deviceIds.add(1713378092099043323L);
        queryDeviceDTO.setDeviceIds(deviceIds);
        List<DeviceInfoDTO> result = deviceService.listByLikeCondition(queryDeviceDTO)
            .pickDataThrowException();
        log.info("根据条件模糊查询设备信息列表result:{}", result);
    }

    @Test
    public void doDeviceCapacityExceeded() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1713378092099043328L);
        deviceIds.add(1713378092099043323L);
        Boolean result = deviceService.doDeviceCapacityExceeded(orgId, deviceIds)
            .pickDataThrowException();
        log.info("处理设备容量超限result:{}", result);
    }

    @Test
    public void listDeviceIndustryAppTypeByDeviceSns() {
        List<String> deviceSns = new ArrayList<>();
        deviceSns.add("270604210101KR0014");
        deviceSns.add("270604210101KR0012");
        List<DeviceIndustryAppTypeDTO> result = deviceService
            .listDeviceIndustryAppTypeByDeviceSns(deviceSns).pickDataThrowException();
        log.info("获取设备行业app列表result:{}", result);
    }

    @Test
    public void getByOrgIdAndTpId() {
        DeviceInfoDTO result = deviceService.getByOrgIdAndTpId(orgId, "1713378092099043328")
            .pickDataThrowException();
        log.info("根据第三方设备id获取设备信息result:{}", result);
    }

    @Test
    public void getByOrgIdAndTpId1() {
        DeviceInfoDTO result = deviceService.getByOrgIdAndTpId(orgId, null)
            .pickDataThrowException();
        log.info("根据第三方设备id获取设备信息result:{}", result);
    }

    @Test
    public void listByDeviceSnListAndDeviceName(){
        QuerySpecificDeviceDTO dto = new QuerySpecificDeviceDTO();
        dto.setOrgId(1701966080525729792L);
        List<String> deviceSnList = new ArrayList<>();
        deviceSnList.add("250903210127KN0029");
        deviceSnList.add("300905210806KN0059");
        deviceSnList.add("300905210827KN0006");
        deviceSnList.add("339914220111KN0046");
        deviceSnList.add("339914220311KN0026");
        deviceSnList.add("339914220311KN0037");
        dto.setDeviceSnList(deviceSnList);
        dto.setDeviceName("考勤机");
        List<DeviceInfoDTO> deviceInfoDTOS = deviceService.listByDeviceSnListAndDeviceName(dto).pickDataThrowException();
        System.out.println(JsonUtils.toJson(deviceInfoDTOS));
    }

    @Test
    public void reportDeviceElectric() {
        ReportDeviceElectricDTO dto = new ReportDeviceElectricDTO();
        dto.setDeviceSn("K49912230711KN0002");
        dto.setDeviceId(1775533244100378624L);
        dto.setElectric(54);
        Boolean aBoolean = deviceService.reportDeviceElectric(dto).pickDataThrowException();
        System.out.println(aBoolean);
    }

    @Test
    public void queryElectricByDeviceId(){
        Long deviceId = 1775542289301504000L;
        DeviceElectricDTO deviceElectricDTO = deviceManager.queryElectricByDeviceId(deviceId);
        System.out.println(JsonUtils.toJson(deviceElectricDTO));
    }

    @Test
    public void findElectricByDeviceIdList(){
        List<Long> deviceIdList = new ArrayList<>();
        deviceIdList.add(1775542289301504000L);
        deviceIdList.add(1775075658284662784L);
        List<DeviceElectricDTO> electricByDeviceIdList = deviceManager.findElectricByDeviceIdList(deviceIdList);
        System.out.println(JsonUtils.toJson(electricByDeviceIdList));
    }

    @Test
    public void updateBluetoothMac(){
        Device device = deviceManager.getByDeviceSn("K49919231026KN0043");
        deviceManager.updateBluetoothMac("123", device.getDeviceId());
    }

    @Test
    public void getByOrgIdAndDeviceId() {
        Long orgId = 1773745197675446272L;
        Long deviceId = 1789590233973522432L;
        DeviceDTO deviceDTO = deviceService.getByOrgIdAndDeviceId(orgId, deviceId).pickDataThrowException();
        System.out.println(JsonUtils.toJson(deviceDTO));
    }

    @Test
    public void listAllDevices(){
        List<DeviceDTO> deviceDTOS = deviceService.listAllDevices().pickDataThrowException();
        System.out.println(deviceDTOS);
    }

    @Test
    public void saveIotDeviceTest() {
        deviceManager.syncIotDeviceByOrgId(1777013504578945024L);
        System.out.println("11111");
    }

    @Test
    public void checkHasPersonTest() {
        DeviceHasPersonDTO dto = deviceService.checkDeviceHasPerson(1795749844141211648L,
            1782714626987786240L).pickDataThrowException();
        System.out.println(dto);
    }

    @Test
    public void deviceInfoTest() {
        DeviceInfoDTO deviceInfoDTO = deviceService.getByOrgIdAndId(1782714626987786240L,
            1795749844141211648L).pickDataThrowException();
        System.out.println(deviceInfoDTO);
    }

    @Test
    public void bindSpaceTest() {
        BindSpaceTreeDto bindSpaceTreeDto = new BindSpaceTreeDto();
        bindSpaceTreeDto.setOrgId(1782714626987786240L);
        bindSpaceTreeDto.setDeviceId("1795834643975503875");
        bindSpaceTreeDto.setTreeId(1782714893275758594L);
        bindSpaceTreeDto.setDeviceSource(1);
        //由于事务原因，激活绑空间时，空间服务不发送绑定消息，上移到设备服务中发送。
        bindSpaceTreeDto.setSendMsgFlag(Boolean.FALSE);
        Long treeDeviceRelationId = treeDeviceRelationService.bindSpace(bindSpaceTreeDto).pickDataThrowException();
        System.out.println(treeDeviceRelationId);
    }

    @Test
    public void getByOrgAndDeviceSn() {
        DeviceInfoDTO deviceInfoDTO = deviceService.getByOrgAndDeviceSn(1776993610290429952L, "270108210728KN3867").pickDataThrowException();
        System.out.println("--------------------");
        System.out.println(deviceInfoDTO.showName());
        System.out.println(deviceInfoDTO);
    }

    @Test
    public void batchUpdate3() {
        List<UpdateDeviceDTO> list = new ArrayList<>();
        UpdateDeviceDTO dto = new UpdateDeviceDTO();
        dto.setDeviceId(1803075366131073024L);
        dto.setOrgId(1773715442142019584L);
        dto.setAppCode("2");
        dto.setAppCodeList("2");
        list.add(dto);
        UpdateDeviceDTO dto2 = new UpdateDeviceDTO();
        dto2.setDeviceId(1801194917779734528L);
        dto2.setOrgId(1773715442142019584L);
        dto2.setAppCode("2");
        dto2.setAppCodeList("2");
        list.add(dto2);
        Boolean result = deviceManager.batchUpdateAppCodeSelective(list);
        log.info("批量修改设备result:{}", result);
    }
}

