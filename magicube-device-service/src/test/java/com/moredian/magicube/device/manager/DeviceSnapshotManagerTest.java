package com.moredian.magicube.device.manager;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.snapshot.DeviceSnapshotDTO;
import com.moredian.magicube.device.model.GenerateDeviceSnapshotModel;
import com.moredian.magicube.device.model.QueryDeviceSnapshotModel;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/1/3
 */
public class DeviceSnapshotManagerTest extends BaseTest {

    @Autowired
    private DeviceSnapshotManager deviceSnapshotManager;

    @Test
    public void generateSnapshot() {
        GenerateDeviceSnapshotModel model = new GenerateDeviceSnapshotModel();
        model.setOrgId(1772555002108182528L);
        model.setDeviceSn("270108210427KN1678");
        model.setDeviceId(1787155769200738304L);
        List<Long> bondGroupIdList = new ArrayList<>();
        bondGroupIdList.add(1787236892543025152L);
        bondGroupIdList.add(1787237244730343424L);
        model.setBondGroupIdList(bondGroupIdList);
        model.setTimestamp(System.currentTimeMillis());
        Long id = deviceSnapshotManager.generateSnapshot(model);
        System.out.println(id);
    }

    @Test
    public void getDeviceSnapshot() {
        QueryDeviceSnapshotModel model = new QueryDeviceSnapshotModel();
        model.setOrgId(1772555002108182528L);
        model.setDeviceId(1787155769200738304L);
        DeviceSnapshotDTO deviceSnapshot = deviceSnapshotManager.getDeviceSnapshot(model);
        System.out.println(deviceSnapshot);
    }

    @Test
    public void deleteById() {
        Boolean aBoolean = deviceSnapshotManager.deleteById(1787035827742703616L);
        System.out.println(aBoolean);
    }

    @Test
    public void queryOvertimeDeviceSnapshot() {
        List<DeviceSnapshotDTO> deviceSnapshotDTOS = deviceSnapshotManager.queryOvertimeDeviceSnapshot(1);
        System.out.println(JsonUtils.toJson(deviceSnapshotDTOS));
    }
}
