package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.accessory.AccessoryInfoDTO;
import com.moredian.magicube.device.dto.accessory.AccessoryQueryDTO;
import com.moredian.magicube.device.dto.accessory.BindAccessoryDTO;
import com.moredian.magicube.device.dto.accessory.UnbindAccessoryDTO;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2021/12/8
 */
public class AccessoryServiceTest extends BaseTest {

    @Autowired
    private AccessoryService accessoryService;

    /**
     * 测试用例 - 绑定外设
     */
    @Test
    public void bindAccessoryTest() {
        BindAccessoryDTO bindAccessoryRequest = new BindAccessoryDTO();
        bindAccessoryRequest.setOrgId(1638097460339408896L);
        bindAccessoryRequest.setDeviceId(1718582295608164352L);
        bindAccessoryRequest.setDeviceSn("090102180816KN0820");
//        bindAccessoryRequest.setOrgId(1718386977675411456L);
//        bindAccessoryRequest.setDeviceId(1718542850628517888L);
//        bindAccessoryRequest.setDeviceSn("090102180523KN2947");
        bindAccessoryRequest.setAccessoryName("test-扫码器");
        bindAccessoryRequest.setAccessoryType(3);
        bindAccessoryRequest.setAccessorySn("000000000000000000");
        accessoryService.bindAccessory(bindAccessoryRequest);
    }

    /**
     * 测试用例 - 解绑外设
     */
    @Test
    public void unbindAccessoryTest() {
        UnbindAccessoryDTO unbindAccessoryRequest = new UnbindAccessoryDTO();
        unbindAccessoryRequest.setOrgId(1638097460339408896L);
//        unbindAccessoryRequest.setOrgId(1718386977675411456L);
//        unbindAccessoryRequest.setDeviceId(1718582295608164352L);
        unbindAccessoryRequest.setDeviceSn("090102180816KN0820");
//        unbindAccessoryRequest.setDeviceSn("090102180523KN2947");
        unbindAccessoryRequest.setAccessoryType(3);
        unbindAccessoryRequest.setAccessorySn("000000000000000000");
        accessoryService.unbindAccessory(unbindAccessoryRequest);
    }

    /**
     * 测试用例 - 外设列表查询
     */
    @Test
    public void listAccessoryInfoTest() {
        AccessoryQueryDTO queryRequest = new AccessoryQueryDTO();
        queryRequest.setOrgId(1638097460339408896L);
        List<AccessoryInfoDTO> accessoryInfoResponseList = accessoryService.listAccessoryInfo(queryRequest).getData();
        System.out.println(accessoryInfoResponseList);
    }

}
