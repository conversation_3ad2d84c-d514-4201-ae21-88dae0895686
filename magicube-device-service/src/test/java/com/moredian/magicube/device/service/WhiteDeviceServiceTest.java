package com.moredian.magicube.device.service;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.DeviceAuthDetailDTO;
import com.moredian.magicube.device.dto.white.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 设备白名单测试用例
 */

@Slf4j
public class WhiteDeviceServiceTest extends BaseTest {

    @Autowired
    private WhiteDeviceService whiteDeviceService;

    private static final Long orgId = 1712115199915327488L;

    @Test
    public void insert() {
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        //dto.setOrgId(orgId);
        dto.setSerialNumber("260910210121KN2034");
        dto.setMacAddress("08E9F63AA0F2");
        //dto.setMacAddress2("08E9F63AA0F1");
        dto.setDeviceType(65);
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        //dto.setActivationCode("1274536264911804");
        Boolean result = whiteDeviceService.insert(dto).pickDataThrowException();
        log.info("新增白名单结果:{}", result);
    }

    @Test
    public void insert1() {
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setOrgId(orgId);
        dto.setSerialNumber("260910210121KN2034");
        dto.setMacAddress("08E9F63AA0F2");
        dto.setMacAddress2("08E9F63AA0F1");
        dto.setDeviceType(65);
        //dto.setPrivateKey("260910210121KN2034");
        dto.setActivationCode("1274536264911804");
        Boolean result = whiteDeviceService.insert(dto).pickDataThrowException();
        log.info("新增白名单结果:{}", result);
    }

    @Test
    public void batchInsert() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        int sn = 1115;
        for (int i = 0; i < 1999; i++) {
            InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
            String device = String.valueOf(sn + i);
            dto.setSerialNumber("270604210101KR" + device);
            dto.setMacAddress("08E9F63AA0F2");
            dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
            list.add(dto);
        }
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert1() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        int sn = 15;
        for (int i = 0; i < 100; i++) {
            InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
            String device = String.valueOf(sn + i);
            dto.setSerialNumber("270604210101KR" + device);
            dto.setMacAddress("08E9F63AA0F2");
            dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
            list.add(dto);
        }
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert2() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
//        dto.setSerialNumber("270604210101KR1115");
        dto.setMacAddress("08E9F63AA0F2");
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert3() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setSerialNumber("270604210101KR1115");
        dto.setMacAddress("08E9F63AA0F2");
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert4() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setSerialNumber("270604210101KR9115");
        dto.setMacAddress("08E9F63AA0F2");
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto);
        InsertWhiteDeviceDTO dto2 = new InsertWhiteDeviceDTO();
        dto2.setSerialNumber("270604210101KR9115");
        dto2.setMacAddress("08E9F63AA0F2");
        dto2.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto2);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert5() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setSerialNumber("270604210101KR9115");
//        dto.setMacAddress("08E9F63AA0F2");
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert6() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setSerialNumber("270604210101KR9115");
        dto.setMacAddress("08E9F63AA0F211");
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert7() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setSerialNumber("270604210101KR9115");
        dto.setMacAddress("08E9F63AA0F2");
//        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert8() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setSerialNumber("270604210101KR9115");
        dto.setMacAddress("08E9F63AA0F2");
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a041111");
        list.add(dto);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void batchInsert9() {
        List<InsertWhiteDeviceDTO> list = new ArrayList<>();
        InsertWhiteDeviceDTO dto = new InsertWhiteDeviceDTO();
        dto.setSerialNumber("270604210101KR9115");
        dto.setMacAddress("08E9F63AA0F2");
        dto.setMacAddress2("08E3F63AA0F211");
        dto.setPrivateKey("e2d0aadbf1f9fa507d2ddf1dba894a04");
        list.add(dto);
        Stopwatch timeWatcher = Stopwatch.createStarted();
        List<ImportWhiteDeviceErrorDTO> result = whiteDeviceService.batchInsert(list).pickDataThrowException();
        log.info("批量新增白名单结果:{},花费时间:{}", result, timeWatcher);
    }

    @Test
    public void listPage() {
        QueryWhiteDeviceDTO queryWhiteDeviceDTO = new QueryWhiteDeviceDTO();
//        queryWhiteDeviceDTO.setOrgId(orgId);
//        queryWhiteDeviceDTO.setSerialNumber("260910210121KN2034");
//        queryWhiteDeviceDTO.setMacAddress("08E9F63AA0F2");
//        queryWhiteDeviceDTO.setSerialNumbers(Lists.newArrayList("010101180109KN0058", "010101180109KN0059"));
        queryWhiteDeviceDTO.setPageSize(20);
        queryWhiteDeviceDTO.setPageNo(1);
        Pagination<WhiteDeviceDTO> result = whiteDeviceService.listPage(queryWhiteDeviceDTO).pickDataThrowException();
        log.info("分页查询设备白名单列表信息:{}", result.getData());
    }

    @Test
    public void delete() {
        Boolean result = whiteDeviceService.delete("260910210121KN2034").pickDataThrowException();
        log.info("根据设备sn删除设备白名单:{}", result);
    }

    @Test
    public void delete1() {
        Boolean result = whiteDeviceService.delete(null).pickDataThrowException();
        log.info("根据设备sn删除设备白名单:{}", result);
    }

    @Test
    public void listByDeviceSns() {
        List<WhiteDeviceDTO> result = whiteDeviceService.listByDeviceSns(Collections.singletonList("260910210121KN2034")).pickDataThrowException();
        log.info("根据设备Sn列表查询白名单设备信息列表:{}", result);
    }


    @Test
    public void batchUpdate() {
        List<UpdateWhiteDeviceDTO> dto = new ArrayList<>();
        UpdateWhiteDeviceDTO updateWhiteDeviceDTO = new UpdateWhiteDeviceDTO();
        updateWhiteDeviceDTO.setSerialNumber("dddd213123");
        updateWhiteDeviceDTO.setNewSerialNumber("aaaa213123");


        UpdateWhiteDeviceDTO updateWhiteDeviceDTO2 = new UpdateWhiteDeviceDTO();
        updateWhiteDeviceDTO2.setSerialNumber("dasddq2");
        updateWhiteDeviceDTO2.setNewSerialNumber("dasddq2312312");
        dto.add(updateWhiteDeviceDTO);
        dto.add(updateWhiteDeviceDTO2);
        Boolean result = whiteDeviceService.batchUpdate(dto).pickDataThrowException();
        log.info("新增白名单结果:{}", result);
    }

    @Test
    public void batchUpdate1() {
        List<UpdateWhiteDeviceDTO> dto = new ArrayList<>();
        UpdateWhiteDeviceDTO updateWhiteDeviceDTO = new UpdateWhiteDeviceDTO();
        updateWhiteDeviceDTO.setOrgId(orgId);
        //updateWhiteDeviceDTO.setSerialNumber("260910210121KN2034");
        updateWhiteDeviceDTO.setActivationCode("1274536264911804");
        dto.add(updateWhiteDeviceDTO);
        Boolean result = whiteDeviceService.batchUpdate(dto).pickDataThrowException();
        log.info("新增白名单结果:{}", result);
    }

    @Test
    void getSdkByDeviceSNTest(){
        DeviceAuthDetailDTO sdkBaseDTO = whiteDeviceService.getSdkBaseBySN(
            "110215221214KN2481").pickDataThrowException();

        log.info("sdkBaseDTO=>{}", sdkBaseDTO);
    }

    @Test
    void testgetBatchSimpleDeviceInfo() {
        GetWhiteDeviceListRequest request = new GetWhiteDeviceListRequest();
        request.setDeviceSns(Lists.newArrayList("580215250312KN0033", "439918220715KN0013", "740E15230530KN0161"));

        List<SimpleWhiteDeviceResponse> simpleWhiteDeviceResponses = whiteDeviceService.getBatchSimpleDeviceInfo(request).pickDataThrowException();


        log.info("simpleWhiteDeviceResponses=>{}", simpleWhiteDeviceResponses);

    }
}