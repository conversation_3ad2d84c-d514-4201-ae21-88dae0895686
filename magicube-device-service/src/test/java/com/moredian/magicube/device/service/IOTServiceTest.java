package com.moredian.magicube.device.service;

import com.google.common.collect.Lists;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.constant.DeviceTypeConstants;
import com.moredian.magicube.device.dto.device.UpdateIotDevicePropertyDTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.moredian.magicube.device.dto.device.iot.IotDeviceListDTO;
import com.moredian.magicube.device.dto.device.iot.PageQueryIotDeviceListDTO;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @description：TODO
 * @date ：2024/11/20 17:23
 */
@Slf4j
public class IOTServiceTest extends BaseTest {


    @SI
    private IotDeviceService iotDeviceService;
    @SI
    private DeviceTypePropertyService deviceTypePropertyService;


    @Test
    void writeTest(){

        UpdateIotDevicePropertyDTO dto = new UpdateIotDevicePropertyDTO();

        dto.setDeviceId(1814950993654185989L);
        dto.setOrgId(1787789903942123520L);
        Map<String, Object> map = new HashMap<>();

        map.put("MinDetectionDistance", "你好");
        dto.setDeviceProperties(map);

        Boolean b = iotDeviceService.updateDeviceProperties(dto).pickDataThrowException();

        log.info("flag=>{}", b);

    }

    @Test
    void iotDeviceListTest() {
        PageQueryIotDeviceListDTO dto = new PageQueryIotDeviceListDTO();
        dto.setOrgId(1776990818561687552L);
        dto.setPropertyValue(true);
        List<Integer> deviceType = deviceTypePropertyService.getDeviceTypeByPropertyKey(DeviceTypeConstants.IOT_DEVICE_TYPE_ENERGY_LIST).pickDataThrowException();
        dto.setDeviceTypes(deviceType);

        Pagination<IotDeviceListDTO> page = iotDeviceService.listByConditionPage(dto).pickDataThrowException();
        log.info("data=>{}", page);
    }



}
