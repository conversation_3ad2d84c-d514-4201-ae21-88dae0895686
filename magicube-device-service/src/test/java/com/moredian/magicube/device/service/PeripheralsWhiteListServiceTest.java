package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.peripherals.InsertPeripheralsWhiteListDTO;
import com.moredian.magicube.device.dto.peripherals.PeripheralsWhiteListDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 设备白名单测试用例
 */
@Slf4j
public class PeripheralsWhiteListServiceTest extends BaseTest {

    @Autowired
    private PeripheralsWhiteListService peripheralsWhiteListService;

    @Test
    public void batchInsert() {
        List<InsertPeripheralsWhiteListDTO> list = new ArrayList<>();
        InsertPeripheralsWhiteListDTO dto = new InsertPeripheralsWhiteListDTO();
        dto.setPeripheralsSn("CB360200000002");
        dto.setPeripheralsType(1);
        dto.setStatus(1);
        list.add(dto);
        InsertPeripheralsWhiteListDTO insertPeripheralsWhiteListDTO = new InsertPeripheralsWhiteListDTO();
        insertPeripheralsWhiteListDTO.setPeripheralsSn("CB360200000003");
        insertPeripheralsWhiteListDTO.setPeripheralsType(1);
        insertPeripheralsWhiteListDTO.setStatus(0);
        list.add(insertPeripheralsWhiteListDTO);
        Boolean result = peripheralsWhiteListService.batchInsert(list).pickDataThrowException();
        log.info("新增外设白名单结果:{}", result);
    }

    @Test
    public void batchInsert1() {
        List<InsertPeripheralsWhiteListDTO> list = new ArrayList<>();
        InsertPeripheralsWhiteListDTO dto = new InsertPeripheralsWhiteListDTO();
        //dto.setPeripheralsSn("CB360200000002");
        dto.setPeripheralsType(1);
        dto.setStatus(1);
        list.add(dto);
        Boolean result = peripheralsWhiteListService.batchInsert(list).pickDataThrowException();
        log.info("新增外设白名单结果:{}", result);
    }

    @Test
    public void getBySnAndType() {
        PeripheralsWhiteListDTO result = peripheralsWhiteListService.getBySnAndType("CB360200000001", 1).pickDataThrowException();
        log.info("根据外设sn和外设类型查询外设白名单信息:{}", result);
    }

    @Test
    public void getByPeripheralsSns() {
        List<PeripheralsWhiteListDTO> result = peripheralsWhiteListService.getByPeripheralsSns(Collections.singletonList("CB360200000001")).pickDataThrowException();
        log.info("根据外设设备sn列表查询外设白名单列表:{}", result);
    }

    @Test
    public void updateStatusById() {
        Integer result = peripheralsWhiteListService.updateStatusById(1712133676864634880L, 0).pickDataThrowException();
        log.info("根据外设白名单Id修改外设白名单状态:{}", result);
    }
}