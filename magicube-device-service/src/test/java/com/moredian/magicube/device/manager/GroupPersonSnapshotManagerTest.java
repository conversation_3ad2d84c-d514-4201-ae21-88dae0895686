package com.moredian.magicube.device.manager;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dao.entity.GroupPersonSnapshot;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/1/3
 */
public class GroupPersonSnapshotManagerTest extends BaseTest {

    @Autowired
    private GroupPersonSnapshotManager groupPersonSnapshotManager;

    @Test
    public void deleteBySnapshotId() {
        Boolean result = groupPersonSnapshotManager.deleteBySnapshotId(1621545644223102976L, 1787035827742703616L);
        System.out.println(result);
    }

    @Test
    public void getDistinctPersonSize() {
        Long orgId = 1772555002108182528L;
        Long snapshotId = 1787239529652944896L;
        List<Long> groupIdList = new ArrayList<>();
        groupIdList.add(1787236892543025152L);
        groupIdList.add(1787237244730343424L);
        int distinctPersonSize = groupPersonSnapshotManager.getDistinctPersonSize(orgId, snapshotId, groupIdList);
        System.out.println(distinctPersonSize);
    }

    @Test
    public void pageGroupDistinctPerson() {
        Long orgId = 1772555002108182528L;
        Long snapshotId = 1787239529652944896L;
        List<Long> groupIdList = new ArrayList<>();
        groupIdList.add(1787236892543025152L);
        groupIdList.add(1787237244730343424L);
        Integer pageNo = 1;
        Integer pageSize = 5;
        List<GroupPersonSnapshot> groupPersonSnapshots = groupPersonSnapshotManager.pageGroupDistinctPerson(orgId, snapshotId, groupIdList, pageNo, pageSize);
        System.out.println(JsonUtils.toJson(groupPersonSnapshots));
    }

}
