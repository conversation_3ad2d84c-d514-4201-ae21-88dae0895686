package com.moredian.magicube.device.service;

import com.google.common.collect.Lists;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.SimpleDeviceInfoDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDetailDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypeDTO;
import com.moredian.magicube.device.dto.type.UpdateDeviceTypeDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import com.moredian.magicube.device.service.DeviceTypeService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

@Slf4j
public class DeviceTypeServiceTest extends BaseTest {

    @Autowired
    private DeviceTypeService deviceTypeService;
    @Resource
    private DeviceService deviceService;

    @Test
    public void insert() {
        InsertDeviceTypeDTO dto = new InsertDeviceTypeDTO();
        dto.setDeviceType(21);
        dto.setDeviceTypeName("识别服务器");
        dto.setDescription("识别服务器");
        Long deviceTypeId = deviceTypeService.insert(dto).pickDataThrowException();
        log.info("新增设备类型deviceTypeId:{}", deviceTypeId);
    }

    @Test
    public void update() {
        UpdateDeviceTypeDTO dto = new UpdateDeviceTypeDTO();
        dto.setDeviceTypeId(1721728247256842240L);
        dto.setDescription("D2标准版标准版");
        dto.setDeviceTypeName("d2门禁机");
        Long deviceTypeId = deviceTypeService.update(dto).pickDataThrowException();
        log.info("编辑设备类型deviceTypeId:{}", deviceTypeId);
    }

    @Test
    public void update1() {
        UpdateDeviceTypeDTO dto = new UpdateDeviceTypeDTO();
        dto.setDeviceTypeId(1721728367515926528L);
        dto.setStatus(YesNoFlag.NO.getValue());
        Long deviceTypeId = deviceTypeService.update(dto).pickDataThrowException();
        log.info("编辑设备类型deviceTypeId:{}", deviceTypeId);
    }

    @Test
    public void list() {
        List<DeviceTypeDTO> result = deviceTypeService.list().pickDataThrowException();
        log.info("查询设备类型列表result:{}", result);
    }

    @Test
    public void getById() {
        DeviceTypeDetailDTO result = deviceTypeService.getById(1721728247256842240L)
            .pickDataThrowException();
        log.info("查询设备类型详情result:{}", result);
    }

    @Test
    void testGetDeviceName() {
        String deviceSn = "449918221024KN0108";
        Integer deviceType = 119;

        DeviceTypeDTO deviceTypeDTO = deviceTypeService.getDeviceDisplayName(deviceSn, deviceType).pickDataThrowException();

        log.info("查询设备类型详情result:{}", deviceTypeDTO);
    }

    @Test
    void test() {
        String deviceSn = "090205201224KN3151";
        List<SimpleDeviceInfoDTO> simpleDeviceInfoDTOS = deviceService.listBaseByDeviceSns(Lists.newArrayList(deviceSn)).pickDataThrowException();
        log.info("simpleDeviceInfoDTOS:{}", simpleDeviceInfoDTOS);
    }
}
