package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.DeviceTypePropertyDetailDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypePropertyDTO;
import com.moredian.magicube.device.dto.type.UpdateDeviceTypePropertyDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceTypePropertyServiceTest extends BaseTest {

    @Autowired
    private DeviceTypePropertyService deviceTypePropertyService;

    @Test
    public void insert() {
        InsertDeviceTypePropertyDTO dto = new InsertDeviceTypePropertyDTO();
        dto.setPropertyKey("OPEN_DOOR_DEVICE_LIST");
        dto.setDescription("支持开门业务的设备类型列表");
        Long id = deviceTypePropertyService.insert(dto).pickDataThrowException();
        log.info("新增设备类型属性id:{}", id);
    }

    @Test
    public void update() {
        UpdateDeviceTypePropertyDTO dto = new UpdateDeviceTypePropertyDTO();
        dto.setId(1721737343997575168L);
        dto.setDescription("支持开门业务的设备类型列表XXXX");
        Long deviceTypeId = deviceTypePropertyService.update(dto).pickDataThrowException();
        log.info("编辑设备类型deviceTypeId:{}", deviceTypeId);
    }

    @Test
    public void list() {
        List<DeviceTypePropertyDTO> result = deviceTypePropertyService.list()
            .pickDataThrowException();
        log.info("查询设备类型属性列表result:{}", result);
    }

    @Test
    public void getById() {
        DeviceTypePropertyDetailDTO result = deviceTypePropertyService
            .getById(1721737343997575168L).pickDataThrowException();
        log.info("根据Id查询设备类型属性result:{}", result);
    }

    @Test
    public void getByPropertyKey() {
        DeviceTypePropertyDetailDTO result = deviceTypePropertyService
            .getByPropertyKey("DOOR_DEVICE_LIST").pickDataThrowException();
        log.info("根据属性key查询设备类型属性result:{}", result);
    }
}
