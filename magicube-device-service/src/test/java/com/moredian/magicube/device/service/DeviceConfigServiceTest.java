package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceConfigServiceTest extends BaseTest {

    @Autowired
    private DeviceConfigService deviceConfigService;

    private static final Long orgId = 1712115199915327488L;

    @Test
    public void insertOrUpdate() {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
            + "<moredian configVersion=\"18\">\n"
            + "  <bizversion>1</bizversion>\n"
            + "  <cardreader>\n"
            + "    <type>1</type>\n"
            + "    <vender>shensi</vender>\n"
            + "  </cardreader>\n"
            + "  <facedetected>\n"
            + "    <service>0</service>\n"
            + "    <detconfig minbrightness=\"0\" maxbrightness=\"255\" gaussianblur=\"0.16\" motionblur=\"0.4\" minfacesize=\"100\" recogtype=\"0\"/>\n"
            + "  </facedetected>\n"
            + "  <camera type=\"0\"/>\n"
            + "  <device>\n"
            + "    <uniqueNumber>270604210101KR0014</uniqueNumber>\n"
            + "    <deviceName>F2门禁机KR1114</deviceName>\n"
            + "    <controlDoorTime>5000</controlDoorTime>\n"
            + "    <switchInfrared>1</switchInfrared>\n"
            + "    <multiPerson>0</multiPerson>\n"
            + "    <offlineThreshold>80</offlineThreshold>\n"
            + "    <doorOverTime>15</doorOverTime>\n"
            + "    <voiceSize>0</voiceSize>\n"
            + "    <enableTTS>0</enableTTS>\n"
            + "    <enableVisitor>1</enableVisitor>\n"
            + "    <visitorBeginTime>00:00</visitorBeginTime>\n"
            + "    <visitorEndTime>23:59</visitorEndTime>\n"
            + "    <visitorOpenWeekDays>1,2,3,4,5,6,7</visitorOpenWeekDays>\n"
            + "    <onlineRecognize>1</onlineRecognize>\n"
            + "    <enableSignIn>0</enableSignIn>\n"
            + "    <enableAttendence>0</enableAttendence>\n"
            + "    <enableStare>1</enableStare>\n"
            + "    <enableMaskAlgorithms>0</enableMaskAlgorithms>\n"
            + "    <faceModel>3</faceModel>\n"
            + "    <attenceInGroup>0</attenceInGroup>\n"
            + "    <enableUploadFaildRegResult>0</enableUploadFaildRegResult>\n"
            + "    <enablePassword>0</enablePassword>\n"
            + "    <openDoorPassword/>\n"
            + "    <enableScanCodeDoor>1</enableScanCodeDoor>\n"
            + "    <enableMeasureTempurature>1</enableMeasureTempurature>\n"
            + "    <isCWWhiteDevice>0</isCWWhiteDevice>\n"
            + "    <enableElevator>0</enableElevator>\n"
            + "    <temperatureAllDay>1</temperatureAllDay>\n"
            + "    <temperatureBeginTime>00:00</temperatureBeginTime>\n"
            + "    <temperatureEndTime>23:59</temperatureEndTime>\n"
            + "    <communicationProtocol>2</communicationProtocol>\n"
            + "    <enableDoorBell>0</enableDoorBell>\n"
            + "    <enableCardAttendance>0</enableCardAttendance>\n"
            + "    <enableSelfBindingCard>1</enableSelfBindingCard>\n"
            + "    <enableMeeting>0</enableMeeting>\n"
            + "    <outdoorMode>0</outdoorMode>\n"
            + "    <enableScanCode>0</enableScanCode>\n"
            + "    <deviceLanguage>zh-CN</deviceLanguage>\n"
            + "  </device>\n"
            + "  <orgConfig>\n"
            + "    <bodyTempuratureThreashholdRange>0-37.3</bodyTempuratureThreashholdRange>\n"
            + "    <measureBodyTemperature>0</measureBodyTemperature>\n"
            + "    <attenceReception>1</attenceReception>\n"
            + "  </orgConfig>\n"
            + "  <admin>\n"
            + "    <offlinepassword>kEgeTJwNGxjfPwFvNrNK6g==</offlinepassword>\n"
            + "    <enableScreenSaver>true</enableScreenSaver>\n"
            + "  </admin>\n"
            + "  <gpio>\n"
            + "    <type>2</type>\n"
            + "    <value>118</value>\n"
            + "  </gpio>\n"
            + "  <opentime>\n"
            + "    <group groupName=\"测试\" groupCode=\"1714736100628496384\" groupId=\"1714736100628496384\">\n"
            + "      <allMemberFlag>1</allMemberFlag>\n"
            + "      <visitorFlag>1</visitorFlag>\n"
            + "      <groupType>6</groupType>\n"
            + "      <deptIds/>\n"
            + "      <weekdays>1,2,3,4,5,6,7</weekdays>\n"
            + "      <begintime>00:00</begintime>\n"
            + "      <endtime>23:59</endtime>\n"
            + "      <beginDay>0</beginDay>\n"
            + "      <endDay>4102502399999</endDay>\n"
            + "    </group>\n"
            + "  </opentime>\n"
            + "  <resource>\n"
            + "    <subject id=\"0004\" holdseconds=\"4\"/>\n"
            + "  </resource>\n"
            + "<attendence changedAt=\"1635304643627\"/></moredian>";
        Boolean result = deviceConfigService.insertOrUpdate(orgId, 1714571861079097344L, xml)
            .pickDataThrowException();
        log.info("更新设备配置deviceLogId:{}", result);
    }

    @Test
    public void insertOrUpdate1() {
        Boolean result = deviceConfigService.insertOrUpdate(orgId, 1714571861079097344L, null)
            .pickDataThrowException();
        log.info("更新设备配置deviceLogId:{}", result);
    }

    @Test
    public void getXmlConfig() {
        String result = deviceConfigService.getXmlConfig("270604210101KR0014")
            .pickDataThrowException();
        log.info("查询设备配置deviceLogId:{}", result);
    }
}
