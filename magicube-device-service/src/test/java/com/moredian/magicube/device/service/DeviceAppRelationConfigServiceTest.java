package com.moredian.magicube.device.service;

import com.google.common.collect.Lists;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.deliver.api.run.AuthorizeAppDataService;
import com.moredian.deliver.dto.AuthorizeAppDTO;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.DeviceAppRelationConfigDTO;
import com.moredian.magicube.device.dto.device.QueryDeviceAppRelationConfigDTO;
import com.moredian.magicube.device.helper.SceneTypeHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DeviceAppRelationConfigServiceTest extends BaseTest {

    @SI
    private DeviceAppRelationConfigService deviceAppRelationConfigService;

    @SI
    private DeviceAppRelationFixedConfigService deviceAppRelationFixedConfigService;
    @SI
    private AuthorizeAppDataService authorizeAppDataService;
    @Resource
    private SceneTypeHelper sceneTypeHelper;


    @Test
    public  void test1(){
        List<DeviceAppRelationConfigDTO> deviceAppRelationConfigDTOS = deviceAppRelationConfigService.listAll().pickDataThrowException();
        deviceAppRelationConfigDTOS.forEach(System.out::println);
    }

    @Test
    public  void test2(){
        QueryDeviceAppRelationConfigDTO queryDeviceAppRelationConfigDTO = new QueryDeviceAppRelationConfigDTO();
        queryDeviceAppRelationConfigDTO.setPageNo(1);
        queryDeviceAppRelationConfigDTO.setPageSize(10);
        Pagination<DeviceAppRelationConfigDTO> deviceAppRelationConfigDTOPagination = deviceAppRelationConfigService.listPage(queryDeviceAppRelationConfigDTO).pickDataThrowException();
        System.out.println(deviceAppRelationConfigDTOPagination.getData());
    }

    @Test
    public  void test3(){
        List<DeviceAppRelationConfigDTO> deviceAppRelationConfigDTOS = deviceAppRelationConfigService.listAll().pickDataThrowException();
        deviceAppRelationConfigDTOS.forEach(System.out::println);
    }

    @Test
    public  void test4(){
        List<DeviceAppRelationConfigDTO> deviceAppRelationConfigDTOS = deviceAppRelationConfigService.listAll().pickDataThrowException();
        deviceAppRelationConfigDTOS.forEach(System.out::println);
    }

    @Test
    public  void test5(){
        AuthorizeAppDTO authorizeAppDTO = authorizeAppDataService.listAuthorizeApp(1785606411288313856L).pickDataThrowException();
        System.out.println(authorizeAppDTO.toString());
    }

    @Test
    public void test6() {
        deviceAppRelationConfigService.getByOrgIdAndDeviceIdList(1776990818561687552L, Lists.newArrayList(1808701318022299648L, 1808701146223607808L), null);
    }
    @Test
    public void test7() {
        Boolean b = deviceAppRelationFixedConfigService.deleteByDeviceSn("22222").pickDataThrowException();
        System.out.println(b);

    }

    @Test
    void test8() {
        List<Integer> spaceTypeList = Lists.newArrayList(31, 32, 33, 35, 36, 37, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 130, 131);
        Map<Integer, Integer> versionMap = new HashMap<>();
        versionMap.put(5801001, 10101020);
        versionMap.put(9406001, 20102213);
        versionMap.put(9002001, 20102215);
        versionMap.put(6501008, 10101018);
        versionMap.put(5603008, 10101010);
        versionMap.put(5603001, 10101035);
        versionMap.put(7501001, 10200010);

        Date now = new Date();

        for (Map.Entry<Integer, Integer> entry : versionMap.entrySet()) {
            Integer appType = entry.getKey();
            Integer version = entry.getValue();
            for (Integer spaceType : spaceTypeList) {
                DeviceAppRelationConfigDTO deviceAppRelationConfigDTO = new DeviceAppRelationConfigDTO();
                deviceAppRelationConfigDTO.setSpaceType(spaceType);
                deviceAppRelationConfigDTO.setAppType(appType);
                deviceAppRelationConfigDTO.setVersionCode(version);
                deviceAppRelationConfigDTO.setDefaultAppCode("001");
                deviceAppRelationConfigDTO.setAvailableAppCodeList("001,002,005");
                deviceAppRelationConfigDTO.setGmtCreate(now);
                deviceAppRelationConfigDTO.setGmtModify(now);
                deviceAppRelationConfigService.insert(deviceAppRelationConfigDTO).pickDataThrowException();
            }
        }

    }
}
