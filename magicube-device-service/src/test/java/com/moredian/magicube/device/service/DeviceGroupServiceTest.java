package com.moredian.magicube.device.service;

import com.moredian.magicube.common.enums.GroupAppType;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.group.GroupInfoDTO;
import com.moredian.magicube.device.dto.group.QueryGroupDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.moredian.magicube.device.dto.group.ResetGroupRelationDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceGroupServiceTest extends BaseTest {

    @Autowired
    private DeviceGroupRelationService deviceGroupRelationService;

    private static final Long orgId = 1712115199915327488L;

    @Test
    public void listDeviceIdByOrgIdAndGroupId() {
        List<Long> result = deviceGroupRelationService
            .listDeviceIdByOrgIdAndGroupId(orgId, 1712115199915327488L)
            .pickDataThrowException();
        log.info("根据机构号和组Id获取设备Id列表result:{}", result);
    }

    @Test
    public void listDeviceIdByOrgIdAndGroupId1() {
        List<Long> result = deviceGroupRelationService
            .listDeviceIdByOrgIdAndGroupId(null, 1712115199915327488L)
            .pickDataThrowException();
        log.info("根据机构号和组Id获取设备Id列表result:{}", result);
    }

    @Test
    public void listDeviceIdByOrgIdAndGroupIds() {
        List<Long> groupIds = new ArrayList<>();
        groupIds.add(1712115199915327488L);
        groupIds.add(1714736100628496384L);
        List<Long> result = deviceGroupRelationService
            .listDeviceIdByOrgIdAndGroupIds(orgId, groupIds).pickDataThrowException();
        log.info("根据机构号和组Id列表获取设备Id列表result:{}", result);
    }

    @Test
    public void listDeviceIdByOrgIdAndGroupIds1() {
        List<Long> result = deviceGroupRelationService
            .listDeviceIdByOrgIdAndGroupIds(null, Collections.singletonList(1712115199915327488L))
            .pickDataThrowException();
        log.info("根据机构号和组Id列表获取设备Id列表result:{}", result);
    }

    @Test
    public void listDeviceIdByCondition() {
        List<Long> result = deviceGroupRelationService
            .listDeviceIdByCondition(orgId, 1714736100628496384L, 0, 10)
            .pickDataThrowException();
        log.info("查询设备Id列表result:{}", result);
    }

    @Test
    public void listDeviceIdByCondition1() {
        List<Long> result = deviceGroupRelationService
            .listDeviceIdByCondition(null, 1714736100628496384L, 0, 10)
            .pickDataThrowException();
        log.info("查询设备Id列表result:{}", result);
    }

    @Test
    public void getGroupIdToDeviceIdsMapByOrgIdAndGroupIds() {
        List<Long> groupIds = new ArrayList<>();
        groupIds.add(1712115199915327488L);
        groupIds.add(1714736100628496384L);
        Map<Long, List<Long>> result = deviceGroupRelationService
            .getGroupIdToDeviceIdsMapByOrgIdAndGroupIds(orgId, groupIds)
            .pickDataThrowException();
        log.info("获取组Id对设备Id列表map result:{}", result);
    }

    @Test
    public void getGroupIdToDeviceIdsMapByOrgIdAndGroupIds1() {
        List<Long> groupIds = new ArrayList<>();
        groupIds.add(1712115199915327488L);
        groupIds.add(1714736100628496384L);
        Map<Long, List<Long>> result = deviceGroupRelationService
            .getGroupIdToDeviceIdsMapByOrgIdAndGroupIds(null, groupIds)
            .pickDataThrowException();
        log.info("获取组Id对设备Id列表map result:{}", result);
    }

    @Test
    public void getDeviceIdToGroupIdsByOrgIdAndDeviceIds() {
        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1714571861079097344L);
        deviceIds.add(1811040717988954112L);
        Map<Long, List<Long>> result = deviceGroupRelationService
            .getDeviceIdToGroupIdsByOrgIdAndDeviceIds(1811067930901741568L, deviceIds, null)
            .pickDataThrowException();
        log.info("获取设备Id对组Id列表map result:{}", result);
    }

    @Test
    public void getDeviceIdToGroupIdsByOrgIdAndDeviceIds1() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        Map<Long, List<Long>> result = deviceGroupRelationService
            .getDeviceIdToGroupIdsByOrgIdAndDeviceIds(null, deviceIds, null)
            .pickDataThrowException();
        log.info("获取设备Id对组Id列表map result:{}", result);
    }

    @Test
    public void listGroupIdByCondition() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
        dto.setAppType(0);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<Long> result = deviceGroupRelationService.listGroupIdByCondition(dto)
            .pickDataThrowException();
        log.info("获取组Id列表result:{}", result);
    }

    @Test
    public void listGroupIdByCondition1() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
//        dto.setAppType(0);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<Long> result = deviceGroupRelationService.listGroupIdByCondition(dto)
            .pickDataThrowException();
        log.info("获取组Id列表result:{}", result);
    }

    @Test
    public void listGroupIdByCondition2() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
        dto.setAppType(1);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<Long> result = deviceGroupRelationService.listGroupIdByCondition(dto)
            .pickDataThrowException();
        log.info("获取组Id列表result:{}", result);
    }

    @Test
    public void listGroupIdByCondition3() {
        QueryGroupDTO dto = new QueryGroupDTO();
//        dto.setOrgId(orgId);
//        dto.setAppType(1);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<Long> result = deviceGroupRelationService.listGroupIdByCondition(dto)
            .pickDataThrowException();
        log.info("获取组Id列表result:{}", result);
    }

    @Test
    public void listGroupNameByCondition() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
        dto.setAppType(0);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<String> result = deviceGroupRelationService.listGroupNameByCondition(dto)
            .pickDataThrowException();
        log.info("获取组名称列表result:{}", result);
    }

    @Test
    public void listGroupNameByCondition1() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
//        dto.setAppType(0);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<String> result = deviceGroupRelationService.listGroupNameByCondition(dto)
            .pickDataThrowException();
        log.info("获取组名称列表result:{}", result);
    }

    @Test
    public void listGroupNameByCondition2() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
        dto.setAppType(1);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<String> result = deviceGroupRelationService.listGroupNameByCondition(dto)
            .pickDataThrowException();
        log.info("获取组名称列表result:{}", result);
    }

    @Test
    public void listGroupNameByCondition3() {
        QueryGroupDTO dto = new QueryGroupDTO();
//        dto.setOrgId(orgId);
//        dto.setAppType(1);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<String> result = deviceGroupRelationService.listGroupNameByCondition(dto)
            .pickDataThrowException();
        log.info("获取组名称列表result:{}", result);
    }

    @Test
    public void listGroupByCondition() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
        dto.setAppType(0);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<GroupInfoDTO> result = deviceGroupRelationService.listGroupByCondition(dto)
            .pickDataThrowException();
        log.info("获取组信息列表result:{}", result);
    }

    @Test
    public void listGroupByCondition1() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
//        dto.setAppType(0);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<GroupInfoDTO> result = deviceGroupRelationService.listGroupByCondition(dto)
            .pickDataThrowException();
        log.info("获取组信息列表result:{}", result);
    }

    @Test
    public void listGroupByCondition2() {
        QueryGroupDTO dto = new QueryGroupDTO();
        dto.setOrgId(orgId);
        dto.setAppType(1);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<GroupInfoDTO> result = deviceGroupRelationService.listGroupByCondition(dto)
            .pickDataThrowException();
        log.info("获取组信息列表result:{}", result);
    }

    @Test
    public void listGroupByCondition3() {
        QueryGroupDTO dto = new QueryGroupDTO();
//        dto.setOrgId(orgId);
//        dto.setAppType(1);
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        dto.setDeviceIds(deviceIds);
        List<GroupInfoDTO> result = deviceGroupRelationService.listGroupByCondition(dto)
            .pickDataThrowException();
        log.info("获取组信息列表result:{}", result);
    }

    @Test
    public void deleteByCondition() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        Boolean result = deviceGroupRelationService.deleteByCondition(orgId, deviceIds)
            .pickDataThrowException();
        log.info("根据条件删除关系result:{}", result);
    }

    @Test
    public void deleteByCondition1() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        deviceIds.add(1714571861079097345L);
        Boolean result = deviceGroupRelationService.deleteByCondition(null, deviceIds)
            .pickDataThrowException();
        log.info("根据条件删除关系result:{}", result);
    }

    @Test
    public void resetRelationByDeviceId() {
        List<Long> groupIds = new ArrayList<>();
        groupIds.add(1712115199915327488L);
        groupIds.add(1714736100628496384L);
        groupIds.add(1714736100628496385L);
        Boolean result = deviceGroupRelationService
            .resetRelationByDeviceId(orgId, 1714571861079097344L, groupIds,
                GroupAppType.GATE.getValue())
            .pickDataThrowException();
        log.info("重置设备和多个组的关系result:{}", result);
    }

    @Test
    public void resetRelationByDeviceId1() {
        List<Long> groupIds = new ArrayList<>();
//        groupIds.add(1712115199915327488L);
//        groupIds.add(1714736100628496384L);
        Boolean result = deviceGroupRelationService
            .resetRelationByDeviceId(orgId, 1714571861079097344L, groupIds,
                GroupAppType.GATE.getValue())
            .pickDataThrowException();
        log.info("重置设备和多个组的关系result:{}", result);
    }

    @Test
    public void resetRelationByDeviceId2() {
        List<Long> groupIds = new ArrayList<>();
        groupIds.add(1775638092842008576L);
        groupIds.add(1775733028799119360L);
        Boolean result = deviceGroupRelationService
            .resetRelationByDeviceId(1774351380769669120L, 1776106312996749312L, groupIds,
                GroupAppType.GATE.getValue())
            .pickDataThrowException();
        log.info("重置设备和多个组的关系result:{}", result);
    }

    @Test
    public void insertRelation() {
        List<Long> groupIds = new ArrayList<>();
        groupIds.add(1775638092842008576L);
//        groupIds.add(1775733028799119360L);
//        groupIds.add(1775640369174675456L);

        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1776106312996749312L);
        deviceIds.add(1776093660023095296L);
        Boolean result = deviceGroupRelationService
                .insertRelationByDeviceIds(1774351380769669120L, deviceIds, groupIds,
                        GroupAppType.MEETING.getValue())
                .pickDataThrowException();
        log.info("插入多个设备和多个组的关系result:{}", result);
    }

    @Test
    public void resetRelationByGroupId() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        Boolean result = deviceGroupRelationService
            .resetRelationByGroupId(orgId, 1712115199915327488L, deviceIds,
                GroupAppType.GATE.getValue())
            .pickDataThrowException();
        log.info("重置一个组和多台设备的关系result:{}", result);
    }

    @Test
    public void resetRelationByGroupId1() {
        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1714571861079097344L);
        Boolean result = deviceGroupRelationService
            .resetRelationByGroupId(orgId, 1712115199915327488L, deviceIds,
                GroupAppType.GATE.getValue())
            .pickDataThrowException();
        log.info("重置一个组和多台设备的关系result:{}", result);
    }

    @Test
    public void resetRelationByGroupId2() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097345L);
        Boolean result = deviceGroupRelationService
            .resetRelationByGroupId(orgId, 1712115199915327488L, deviceIds,
                GroupAppType.GATE.getValue())
            .pickDataThrowException();
        log.info("重置一个组和多台设备的关系result:{}", result);
    }

    @Test
    public void resetRelationByGroupId3() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1714571861079097344L);
        Boolean result = deviceGroupRelationService
            .resetRelationByGroupId(1595548335802941440L, 1712115199915327488L, deviceIds,
                GroupAppType.GATE.getValue())
            .pickDataThrowException();
        log.info("重置一个组和多台设备的关系result:{}", result);
    }

    @Test
    public void resetRelationByGroupIdAndDeviceIdList() {
        List<Long> deviceIds = new ArrayList<>();

        deviceIds.add(1596343188220542976L);
        deviceIds.add(1596615540686716928L);
        deviceIds.add(1596691939564978176L);
        ResetGroupRelationDTO resetGroupRelationDTO = deviceGroupRelationService
                .resetRelationByGroupIdAndDeviceIdList(1595548335802941440L, 1595548335802941441L, deviceIds,
                       null)
                .pickDataThrowException();
        log.info("重置一个组和多台设备的关系result:{}", resetGroupRelationDTO);
    }
}
