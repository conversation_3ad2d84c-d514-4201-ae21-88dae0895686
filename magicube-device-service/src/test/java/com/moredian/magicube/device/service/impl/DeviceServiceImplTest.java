package com.moredian.magicube.device.service.impl;


import com.moredian.bee.common.utils.FacePicture;
import com.moredian.bee.common.utils.NetworkFacePicture;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.ImagePathDTO;
import com.moredian.magicube.device.dto.device.SaveImageDTO;
import com.moredian.magicube.device.service.DeviceService;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import sun.misc.BASE64Encoder;

/**
 * 设备服务测试
 *
 * <AUTHOR>
 * @date 2023/12/07
 */
class DeviceServiceImplTest extends BaseTest {


    @Autowired
    private DeviceService deviceService;

    @Test
    void saveImage() {
        FacePicture picture = new NetworkFacePicture("https://minio.shana.asia/shana/600k.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=Wcjapple1%2F20231207%2F%2Fs3%2Faws4_request&X-Amz-Date=20231207T060338Z&X-Amz-Expires=432000&X-Amz-SignedHeaders=host&X-Amz-Signature=7280a4ce171e356378465f822721f8e2328c15d8aa03897eb0f3e10a22c6ccfb");
        BASE64Encoder encoder = new BASE64Encoder();
        String base64 = encoder.encode(picture.getImageData());
        byte[] imageData = Base64.decodeBase64(base64);
        SaveImageDTO saveImageDTO = new SaveImageDTO();
        saveImageDTO.setImage(imageData);
        ImagePathDTO imagePathDTO = deviceService.saveImage(saveImageDTO).pickDataThrowException();
        System.out.println(imagePathDTO.getImagePath());
        // /temporary/2023/12/7/14/de55e6de5c9340afbdae1ed59927669c.jpg
    }
}