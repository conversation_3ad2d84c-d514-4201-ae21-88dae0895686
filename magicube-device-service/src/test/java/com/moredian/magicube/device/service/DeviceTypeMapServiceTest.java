package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.type.DeviceCapacityDTO;
import com.moredian.magicube.device.dto.type.SaveDeviceCapacityDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/25
 */
public class DeviceTypeMapServiceTest extends BaseTest {

    @Autowired
    private DeviceTypeMapService deviceTypeMapService;

    @Test
    public void getDevicesCapacity() {
        List<Integer> deviceTypeList = new ArrayList<>();
        for(int i = 20;i<128;i++) {
            deviceTypeList.add(i);
        }
        List<DeviceCapacityDTO> deviceCapacityDTOS = deviceTypeMapService.getDeviceListCapacity(deviceTypeList).pickDataThrowException();
        System.out.println(deviceCapacityDTOS);
    }

    @Test
    public void saveDeviceCapacity(){
        SaveDeviceCapacityDTO dto = new SaveDeviceCapacityDTO();
        dto.setDeviceType(6);
        dto.setCapacity(7);
        Boolean result = deviceTypeMapService.saveDeviceCapacity(dto).pickDataThrowException();
        System.out.println(result);
    }
}
