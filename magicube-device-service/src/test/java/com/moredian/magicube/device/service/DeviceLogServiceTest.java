package com.moredian.magicube.device.service;

import com.moredian.magicube.common.enums.DeviceOperEventType;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.log.DeviceLogDTO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceLogServiceTest extends BaseTest {

    @Autowired
    private DeviceLogService deviceLogService;

    private static final Long orgId = 1712115199915327488L;

    @Test
    public void insert() {
        DeviceLogDTO dto = new DeviceLogDTO();
        dto.setOrgId(orgId);
        dto.setDeviceId(1713378092099043328L);
        dto.setDeviceSn("270604210101KR0014");
        dto.setDeviceType(63);
        dto.setEnvenType(DeviceOperEventType.ACTIVE_DEVICE.getValue());
        dto.setEnvenDesc(DeviceOperEventType.ACTIVE_DEVICE.getDesc());
        dto.setOperTime(System.currentTimeMillis());
        Long deviceLogId = deviceLogService.insert(dto).pickDataThrowException();
        log.info("新增设备日志deviceLogId:{}", deviceLogId);
    }

    @Test
    public void insert1() {
        DeviceLogDTO dto = new DeviceLogDTO();
//        dto.setOrgId(orgId);
//        dto.setDeviceId(1713378092099043328L);
//        dto.setDeviceSn("270604210101KR0014");
        dto.setDeviceType(63);
        dto.setEnvenType(DeviceOperEventType.UNBIND_DEVICE.getValue());
        dto.setEnvenDesc(DeviceOperEventType.UNBIND_DEVICE.getDesc());
        dto.setOperTime(System.currentTimeMillis());
        Long deviceLogId = deviceLogService.insert(dto).pickDataThrowException();
        log.info("新增设备日志deviceLogId:{}", deviceLogId);
    }

    @Test
    public void update() {
        DeviceLogDTO dto = new DeviceLogDTO();
        dto.setOrgId(1768823251053576192L);
        dto.setDeviceId(1768823921068474368L);
        dto.setDeviceSn("439918220715KN0058");
        dto.setDeviceType(118);
        dto.setEnvenType(DeviceOperEventType.ACTIVE_DEVICE.getValue());
        dto.setEnvenDesc(DeviceOperEventType.ACTIVE_DEVICE.getDesc());
        dto.setOperTime(System.currentTimeMillis());
        dto.setMemberId(1768823251053576192L);
        Boolean result = deviceLogService.updateDeviceLogOperatorId(dto).pickDataThrowException();
        log.info("更新设备激活日志的操作人Id:{}", result);
    }

    @Test
    public void getByOrgIdAndId() {
        DeviceLogDTO deviceLogDTO = deviceLogService.getByOrgIdAndId(orgId, 1713305833569255424L)
            .pickDataThrowException();
        log.info("根据设备日志Id获取设备日志信息deviceLogDTO:{}", deviceLogDTO);
    }

    @Test
    public void getByOrgIdAndId1() {
        DeviceLogDTO deviceLogDTO = deviceLogService.getByOrgIdAndId(null, 1713305833569255424L)
            .pickDataThrowException();
        log.info("根据设备日志Id获取设备日志信息deviceLogDTO:{}", deviceLogDTO);
    }

    @Test
    public void listByOrgIdAndDeviceId() {
        List<DeviceLogDTO> deviceLogDTOs = deviceLogService
            .listByOrgIdAndDeviceId(orgId, 1713378092099043328L).pickDataThrowException();
        log.info("根据设备Id获取设备日志信息列表deviceLogDTOs:{}", deviceLogDTOs);
    }

    @Test
    public void listByOrgIdAndDeviceId1() {
        List<DeviceLogDTO> deviceLogDTOs = deviceLogService
            .listByOrgIdAndDeviceId(null, 1713378092099043328L).pickDataThrowException();
        log.info("根据设备Id获取设备日志信息列表deviceLogDTOs:{}", deviceLogDTOs);
    }

    @Test
    public void listByOrgIdAndDeviceSn() {
        List<DeviceLogDTO> deviceLogDTOs = deviceLogService
            .listByOrgIdAndDeviceSn(orgId, "270604210101KR0014").pickDataThrowException();
        log.info("根据设备sn获取设备日志信息列表deviceLogDTOs:{}", deviceLogDTOs);
    }

    @Test
    public void listByOrgIdAndDeviceSn1() {
        List<DeviceLogDTO> deviceLogDTOs = deviceLogService
            .listByOrgIdAndDeviceSn(null, "270604210101KR0014").pickDataThrowException();
        log.info("根据设备sn获取设备日志信息列表deviceLogDTOs:{}", deviceLogDTOs);
    }

    @Test
    public void listDistinctDeviceTypeByOrgId() {
        List<Integer> deviceTypes = deviceLogService.listDistinctDeviceTypeByOrgId(orgId)
            .pickDataThrowException();
        log.info("根据机构号获取设备日志信息类型去重列表deviceTypes:{}", deviceTypes);
    }

    @Test
    public void listDistinctDeviceTypeByOrgId1() {
        List<Integer> deviceTypes = deviceLogService.listDistinctDeviceTypeByOrgId(null)
            .pickDataThrowException();
        log.info("根据机构号获取设备日志信息类型去重列表deviceTypes:{}", deviceTypes);
    }

    @Test
    public void countByOrgId() {
        Integer count = deviceLogService.countByOrgId(orgId).pickDataThrowException();
        log.info("根据机构号统计设备日志数量count:{}", count);
    }

    @Test
    public void countByOrgId1() {
        Integer count = deviceLogService.countByOrgId(null).pickDataThrowException();
        log.info("根据机构号统计设备日志数量count:{}", count);
    }

    @Test
    public void listByDeviceSn(){
        String deviceSn = "439918220913KN0023";
        List<DeviceLogDTO> deviceLogDTOS = deviceLogService.listByDeviceSn(deviceSn).pickDataThrowException();
        System.out.println(deviceLogDTOS);
    }
}
