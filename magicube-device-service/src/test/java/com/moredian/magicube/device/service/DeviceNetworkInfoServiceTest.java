package com.moredian.magicube.device.service;

import com.alibaba.fastjson.JSON;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.network.DeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.network.InsertDeviceNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.UploadNetworkCodeStatusDTO;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceNetworkInfoServiceTest extends BaseTest {

    private static final Long orgId = 1770119291794882560L;

    @Autowired
    private DeviceNetworkInfoService deviceNetworkInfoService;

    @Autowired
    private FunctionQrCodeService functionQrCodeService;

    @Test
    public void upload() {
        InsertDeviceNetworkInfoDTO dto = new InsertDeviceNetworkInfoDTO();
        dto.setOrgId(orgId);
        dto.setDeviceId(1771917611781586944L);
        dto.setConnectType(1);
        dto.setNetworkType(1);
        dto.setCableStatic(JSON.parseObject("{\"dns\":\"*******\",\"gateway\":\"\",\"ip\":\"\",\"mask\":\"\"}", CableStatic.class));
        dto.setWifiInfo(JSON.parseObject("{\"name\":\"\",\"password\":\"\"}", WifiInfo.class));
        Boolean result = deviceNetworkInfoService.upload(dto)
            .pickDataThrowException();
        log.info("上报设备网络信息:{}", result);
    }

    @Test
    public void getByOrgIdAndDeviceId() {
        DeviceNetworkInfoDTO result = deviceNetworkInfoService.getByOrgIdAndDeviceId(orgId,1771922112907313152L)
            .pickDataThrowException();
        log.info("根据机构号和设备Id获取网络信息result:{}", result);
    }

    @Test
    public void uploadNetworkCodeStatus(){
        UploadNetworkCodeStatusDTO dto = new UploadNetworkCodeStatusDTO();
        dto.setOrgNetworkId(1777700656396632064L);
        dto.setOrgId(0L);
        dto.setStatus(2);
        dto.setBindOrgUrl("testbind");
        Boolean aBoolean = functionQrCodeService.uploadNetworkCodeStatus(dto).pickDataThrowException();
        System.out.println(aBoolean);
    }

    @Test
    public void getActivateQrCodeById() {
        functionQrCodeService.getActivateQrCodeById(1769834879060541440L, 1799754136640552960L);
    }
}

