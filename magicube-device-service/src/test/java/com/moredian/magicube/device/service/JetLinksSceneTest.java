package com.moredian.magicube.device.service;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.scene.IRuleArrangementService;
import com.moredian.bridge.api.service.scene.ISceneInteractionRuleService;
import com.moredian.bridge.api.service.scene.dto.AddOrUpdateRuleArrangementDTO;
import com.moredian.bridge.api.service.scene.dto.AddSceneInteractionDTO;
import com.moredian.magicube.common.util.JsonUtils;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.jetlinks.rule.arrange.ArrangeMetaData;
import com.moredian.magicube.device.jetlinks.rule.arrange.DeviceMessageSenderNode;
import com.moredian.magicube.device.jetlinks.rule.arrange.FunctionNode;
import com.moredian.magicube.device.jetlinks.rule.arrange.Node;
import com.moredian.magicube.device.jetlinks.rule.arrange.TabNode;
import com.moredian.magicube.device.utils.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @description：TODO
 * @date ：2024/07/16 14:19
 */
@Slf4j
public class JetLinksSceneTest extends BaseTest {

    private static final Long orgId = 1777013504578945024L;

    public String sceneJson = "{\"name\":\"#{ruleName}\",\"triggerType\":\"timer\",\"trigger\":{\"type\":\"timer\",\"typeName\":\"手动触发\",\"timer\":{\"trigger\":\"week\",\"mod\":\"once\",\"when\":[],\"once\":{\"time\":\"#{time}\"}}},\"parallel\":false,\"state\":{\"text\":\"禁用\",\"value\":\"disable\"},\"branches\":[{\"when\":[],\"then\":[{\"parallel\":false,\"actions\":[{\"executor\":\"device\",\"device\":{\"selector\":\"fixed\",\"source\":\"fixed\",\"selectorValues\":[{\"value\":\"#{deviceId}\",\"name\":\"#{deviceName}\"}],\"productId\":\"0051\",\"upperKey\":\"\",\"message\":{\"messageType\":\"WRITE_PROPERTY\",\"properties\":{\"#{deviceProperties}\":{\"value\":\"0\",\"source\":\"fixed\"}},\"inputs\":[]}},\"options\":{\"selector\":\"fixed\",\"triggerName\":\"触发设备\",\"productName\":\"vensi-计量插座\",\"name\":\"#{deviceName}\",\"propertiesName\":\"#{propertiesName}\",\"propertiesValue\":\"#{propertiesValue}\",\"otherColumns\":[],\"type\":\"设置\",\"columns\":[]}}]}],\"executeAnyway\":true}],\"options\":{\"trigger\":{\"when\":\"每天\",\"time\":\"#{time} 执行1次\"},\"when\":[{\"terms\":[{\"terms\":[[\"\",\"eq\",\"\",\"and\"]]}]}]}}\n";
    public String ruleName = "add-test";

    public String time = "14:50:00";

    public String deviceId = "5C0272FFFEE9A9D101";

    public String deviceName = "vensi-计量插座-5C0272FFFEE9A9D101";

    public String deviceProperties = "PowerSwitch";

    public String propertiesName = "开关";

    public String propertiesValue = "关闭";

    @Resource
    private ISceneInteractionRuleService sceneInteractionRuleService;

    @SI
    private IRuleArrangementService ruleArrangementService;

    @SI
    private OrgService orgService;

    public static void main(String[] args) {

        String json = "{\"name\":\"#{ruleName}\"";
        String test = json.replaceAll("#\\{ruleName}", "test");
        System.out.println(test);


    }



    @Test
    void addScene(){
        String ruleMetaData = sceneJson.replaceAll("#\\{ruleName}", ruleName)
            .replaceAll("#\\{time}", time)
            .replaceAll("#\\{deviceId}", deviceId)
            .replaceAll("#\\{deviceName}", deviceName)
            .replaceAll("#\\{deviceProperties}", deviceProperties)
            .replaceAll("#\\{propertiesName}", propertiesName)
            .replaceAll("#\\{propertiesValue}", propertiesValue);


        // jetLinks 创建场景
        AddSceneInteractionDTO sceneInteraction = new AddSceneInteractionDTO();
        sceneInteraction.setOrgId(orgId);
        sceneInteraction.setRuleMetaData(ruleMetaData);
        ServiceResponse<String> addSceneRuleResp = sceneInteractionRuleService.addSceneInteractionRule(sceneInteraction);
        log.info("addSceneInteractionRule isSuccess: {} ,message: {}", addSceneRuleResp.isSuccess(),
            addSceneRuleResp.getErrorContext() == null ? "" : addSceneRuleResp.getErrorContext().getMessage());
        //启动场景
        ServiceResponse<Boolean> startSceneResp = sceneInteractionRuleService.startSceneInteractionRule(orgId, addSceneRuleResp.getData());
        log.info("startSceneInteractionRule isSuccess: {} ,message: {}", startSceneResp.isSuccess(),
            startSceneResp.getErrorContext() == null ? "" : startSceneResp.getErrorContext().getMessage());

        Assertions.assertEquals(startSceneResp.getData(), Boolean.TRUE);
    }


    /**
     * 规则编排测试
     */
    @Test
    void addArrange(){
        // 主节点
        TabNode tabNode = new TabNode();
        tabNode.setLabel("测试无人关闭规则");
        tabNode.setDisabled(Boolean.TRUE);

        // 设备指令节点
        DeviceMessageSenderNode deviceMessageSenderNode = new DeviceMessageSenderNode();
        List<List<String>> wires1 = new ArrayList<>();
        deviceMessageSenderNode.setWires(wires1);
        deviceMessageSenderNode.setZ(tabNode.getId());

        // 判断开关节点
        FunctionNode powerFunctionNode = new FunctionNode();
        List<List<String>> wires2 = new ArrayList<>();
        List<String> array2 = new ArrayList<>();
        array2.add(deviceMessageSenderNode.getId());
        wires2.add(array2);
        powerFunctionNode.setWires(wires2);
        powerFunctionNode.setZ(tabNode.getId());
        powerFunctionNode.setFunc(FunctionNode.NO_HUMAN_POWER_OFF);

        List<Node> flows = new ArrayList<>();
        flows.add(tabNode);
        flows.add(deviceMessageSenderNode);
        flows.add(powerFunctionNode);
        ArrangeMetaData metaData = new ArrangeMetaData();
        metaData.setFlows(flows);


        AddOrUpdateRuleArrangementDTO dto = new AddOrUpdateRuleArrangementDTO();
        dto.setId(StringUtils.uuidStr());
        dto.setOrgId(orgId);
        dto.setModelType("node-red");
        dto.setName("测试无人关闭规则");
        dto.setModelId(dto.getId());
        dto.setModelVersion(1);
        dto.setState("started");
        dto.setModelMeta(StringUtils.getSpecialJsonStr(metaData));


        String s = ruleArrangementService.addRuleArrangement(dto).pickDataThrowException();

        System.out.println(s);
    }














}
