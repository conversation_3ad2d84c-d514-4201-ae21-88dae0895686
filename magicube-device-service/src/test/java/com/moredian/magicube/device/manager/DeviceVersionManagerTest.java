package com.moredian.magicube.device.manager;

import com.google.common.collect.Lists;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @description：设备版本号测试
 * @date ：2024/08/13 10:14
 */
@Slf4j
class DeviceVersionManagerTest extends BaseTest {

    @Resource
    private DeviceVersionManager deviceVersionManager;


    @Test
    void listByOrgIdAndDeviceIdsTest(){
        Long orgId = 1782714626987786240L;
        List<Long> deviceIds = Lists.newArrayList(1806898041643335680L);

        List<DeviceCurrVersionDTO> version = deviceVersionManager.listByOrgIdAndDeviceIds(
            orgId, deviceIds);

        log.info("version=>{}", version);
    }






}
