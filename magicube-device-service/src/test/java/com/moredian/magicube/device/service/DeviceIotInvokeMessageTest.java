package com.moredian.magicube.device.service;

import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dao.entity.DeviceIotInvokeMessage;
import com.moredian.magicube.device.dto.lock.MessageInfoDTO;
import com.moredian.magicube.device.manager.DeviceIotInvokeMessageManager;
import com.moredian.magicube.device.service.lock.DeviceIotInvokeMessageService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-03 16:19
 */
@Slf4j
public class DeviceIotInvokeMessageTest extends BaseTest {

    @Resource
    private DeviceIotInvokeMessageManager deviceIotInvokeMessageManager;

    @SI
    private DeviceIotInvokeMessageService deviceIotInvokeMessageService;


    @Test
    void getById() {

//        MessageInfoDTO infoDTO = deviceIotInvokeMessageService.getByMessageId(100L).pickDataThrowException();
//        log.info("infoDTO:{}", infoDTO);

    }


    @Test
    void updateMessage() {

//        Boolean b = deviceIotInvokeMessageService.updateStatusByMessageId(100L, 2).pickDataThrowException();
//        log.info("b:{}", b);

    }



    @Test
    void list() {

        DeviceIotInvokeMessage message = new DeviceIotInvokeMessage();
        message.setMessageId(100L);
        message.setDeviceId(100L);
        message.setOrgId(100L);
        message.setMessageStatus(1);
        message.setMessageType(1);
        message.setDeviceSn("1000");
//        deviceIotInvokeMessageManager.save(message);

        List<DeviceIotInvokeMessage> list = deviceIotInvokeMessageManager.list();


        log.info("list:{}", list);
    }


}
