package com.moredian.magicube.device.service;

import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dto.lock.InitialLockDTO;
import com.moredian.magicube.device.dto.lock.LockInfoDTO;
import com.moredian.magicube.device.dto.lock.UpdateLockDTO;
import com.moredian.magicube.device.manager.helper.IotDeviceManagerHelper;
import com.moredian.magicube.device.service.lock.TTLockService;
import com.moredian.magicube.device.third.lock.TTLockOpenService;
import com.moredian.magicube.device.third.lock.res.InitializeLockResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @create 2025-03-11 17:23
 */
@Slf4j
public class TTLockServiceTest extends BaseTest {

    @Resource
    private TTLockOpenService ttLockOpenService;
    @Resource
    private IotDeviceManagerHelper iotDeviceManagerHelper;

    @SI
    private TTLockService ttLockService;

    @Resource
    private RedissonCacheComponent redissonCacheComponent;


    @Test
    void initializeLockTest() {
        String lockData = "ijTBP6UPjuAQajEAoK2tNyTinU2FyLYzfmkMSj2exUTT7uY+g8ghPx1wz7l7DE3l7NdKe98fr10ni3DtaM5bI0DvjEJRSOjnHuLD1lSssXSnVT5W9dNeg+24+oOU8cG7GBCsC+SaPy22mZGV7WHLR4YXuRLINrK8erBx6QV3JJIee13CRcqTfbC/VidMQWpC1OXTcJVl+3oszqKXNSIytWzLpL7irl0OXhx+cNpFjDV5BDzU0IeaGF7KuxMQJ9h+4Aog48jhMD4XSPK65XJuBEfwt6rZCaqTU3ShtZQkIzKc2vjwRxdnzXe3YEqOaR8S+6IzJFPzExX7ABuzsw3KqrBmL4zETpXLYxE2cmoTtOAs/9fWkKKIkJnxrkkE2u665d8BT2qCoR0tQfSNQ4LAl0P075Mor6wTPXWIP1Kum03Lq5z0kjxyA75RjyE8JWa9W9EDtWQVYrJHwE0naoiLHF2LFD7vCuZXCYf78rQAJt1x0OU8p1OHTbkm4OL1mnNImVM0ECQP/1LJQO0n5tUkUlb0/CGhkJYot61Vo8+w8MMXFF9bJHK1lM9c1I6Etygytt8BxUOsQdet7Ai6/bL/haK7DlpSCK6mzmptdLwBpQwdxN5pcm75FfWbVC+/ed5TxONacuIYlyUmwd6r1yd1/xv9pN9BDLrSGRZD/2ywHdyqQuiH239j/ggnZ5Bog+fzYMcNwYlcred/jrPRXzyFFnGzAayxq5ER8nemJteZOkINvCh6rFnOHcFR08vDA0nctYLmNFc5Ajhu4G3IZOTY9Pltt/TNYA==";
        InitializeLockResp initializeLockResp = ttLockOpenService.initializeLock(lockData);
        log.info("initializeLockResp:{}", initializeLockResp);
    }

    @Test
    void initializeLockTest1() {
        String lockData = "ijTBP6UPjuAQajEAoK2tNyTinU2FyLYzfmkMSj2exUTT7uY+g8ghPx1wz7l7DE3l7NdKe98fr10ni3DtaM5bI0DvjEJRSOjnHuLD1lSssXSnVT5W9dNeg+24+oOU8cG7GBCsC+SaPy22mZGV7WHLR4YXuRLINrK8erBx6QV3JJIee13CRcqTfbC/VidMQWpC1OXTcJVl+3oszqKXNSIytWzLpL7irl0OXhx+cNpFjDV5BDzU0IeaGF7KuxMQJ9h+4Aog48jhMD4XSPK65XJuBEfwt6rZCaqTU3ShtZQkIzKc2vjwRxdnzXe3YEqOaR8S+6IzJFPzExX7ABuzsw3KqrBmL4zETpXLYxE2cmoTtOAs/9fWkKKIkJnxrkkE2u665d8BT2qCoR0tQfSNQ4LAl0P075Mor6wTPXWIP1Kum03Lq5z0kjxyA75RjyE8JWa9W9EDtWQVYrJHwE0naoiLHF2LFD7vCuZXCYf78rQAJt1x0OU8p1OHTbkm4OL1mnNImVM0ECQP/1LJQO0n5tUkUlb0/CGhkJYot61Vo8+w8MMXFF9bJHK1lM9c1I6Etygytt8BxUOsQdet7Ai6/bL/haK7DlpSCK6mzmptdLwBpQwdxN5pcm75FfWbVC+/ed5TxONacuIYlyUmwd6r1yd1/xv9pN9BDLrSGRZD/2ywHdyqQuiH239j/ggnZ5Bog+fzYMcNwYlcred/jrPRXzyFFnGzAayxq5ER8nemJteZOkINvCh6rFnOHcFR08vDA0nctYLmNFc5Ajhu4G3IZOTY9Pltt/TNYA==";
        InitialLockDTO dto = new InitialLockDTO();
        dto.setLockData(lockData);

//        String s = ttLockService.initialLock(dto).pickDataThrowException();
//        log.info("initializeLockResp:{}", s);

    }


    @Test
    void testUnbind() {
        LockInfoDTO dto = new LockInfoDTO();
        dto.setDeviceSn("21783618723");

        Boolean b = ttLockService.unbindLock(dto).pickDataThrowException();
        log.info("b:{}", b);
    }

    @Test
    void testActivate() {

        LockInfoDTO dto1 = new LockInfoDTO();
        dto1.setDeviceSn("18498421");

        Boolean b1 = ttLockService.unbindLock(dto1).pickDataThrowException();
        log.info("b:{}", b1);

        LockInfoDTO dto = new LockInfoDTO();
        dto.setDeviceSn("18498421");
        dto.setOrgId(1777013504578945024L);
        dto.setDeviceType(DeviceType.TTLOCK.getValue());

        Boolean b = ttLockService.activate(dto).pickDataThrowException();
        log.info("b:{}", b);
    }

    @Test
    void getPropertiesTest() {
        String deviceSn = "21783618723";

        LockInfoDTO.LockInfo lock = iotDeviceManagerHelper.getIotDeviceLastProperties(-1L, deviceSn, LockInfoDTO.LockInfo.class);
        log.info("lock:{}", lock);
    }


    @Test
    void getLockInfo() {
        String deviceSn = "18498421";

        LockInfoDTO dto = ttLockService.lockInfoBySn(deviceSn).pickDataThrowException();

        log.info("dto:{}", dto);
    }

    @Test
    void uploadLockOnlineState() {
        UpdateLockDTO dto = new UpdateLockDTO();
        dto.setOnline(Boolean.TRUE);
        dto.setDeviceSn("18498421");

        Boolean b = ttLockService.uploadLockOnlineState(dto).pickDataThrowException();
        log.info("b:{}", b);
    }

    @Test
    void updatePasswordTest() {
        UpdateLockDTO dto = new UpdateLockDTO();
        dto.setDeviceSn("21994425");
        dto.setEnablePassword(1);
        dto.setDeviceId(1829724315352825856L);
        dto.setNewKeyboardPwd("654321");

        Long b = ttLockService.updatePassword(dto).pickDataThrowException();
        log.info("b:{}", b);
    }


    @Test
    void getTokenTest() {
        String token = ttLockOpenService.getToken();
        log.info("token:{}", token);

//        String tokenKey = RedisConstants.getKey(RedisConstants.TT_LOCK_ACCESS_TOKEN);
//
//        Boolean b = redissonCacheComponent.delStrCache(tokenKey);
//        log.info("b:{}", b);

    }


}
