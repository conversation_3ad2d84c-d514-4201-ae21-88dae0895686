package com.moredian.magicube.device.service;

import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.constant.RedisConstants;
import com.moredian.magicube.device.dao.entity.DeviceTypePropertyConfig;
import com.moredian.magicube.device.manager.DeviceTypePropertyConfigManager;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @description：TODO
 * @date ：2024/07/25 16:43
 */
@Slf4j
public class DeviceTypePropertyConfigTest extends BaseTest {

    @Resource
    private DeviceTypePropertyConfigManager deviceTypePropertyConfigManager;
    @Resource
    private RedissonCacheComponent redissonCacheComponent;


    @Test
    void getCacheTest(){
        Integer deviceType = 505;

        String key = RedisConstants.getKey(RedisConstants.DEVICE_TYPE_PROPERTY_CONFIG, deviceType);

        List<DeviceTypePropertyConfig> list
            = (List<DeviceTypePropertyConfig>) redissonCacheComponent.getObjectCache(key);

        log.info("list=>{}", list);
    }






}
