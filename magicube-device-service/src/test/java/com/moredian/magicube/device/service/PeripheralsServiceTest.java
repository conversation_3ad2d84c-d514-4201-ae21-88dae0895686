package com.moredian.magicube.device.service;

import com.moredian.magicube.common.enums.PerBindDeviceType;
import com.moredian.magicube.common.enums.PeripheralConnectionStatusEnums;
import com.moredian.magicube.common.enums.PeripheralsType;
import com.moredian.magicube.common.enums.YesNoFlag;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.peripherals.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 设备白名单测试用例
 */

@Slf4j
public class PeripheralsServiceTest extends BaseTest {

    @Autowired
    private PeripheralsService peripheralsService;

    private static final Long orgId = 1712115199915327488L;

    @Test
    public void insert() {
        InsertPeripheralsDTO dto = new InsertPeripheralsDTO();
        dto.setOrgId(orgId);
        dto.setPeripheralsSn("CB360200000002");
        dto.setPeripheralsName("腕温仪_CB360200000002");
        dto.setDeviceSn("260910210121KN2034");
        dto.setDeviceName("门禁机260910210121KN2034");
        dto.setStatus(1);
        dto.setAvailable(1);
        dto.setBindTime(new Date());
        dto.setPeripheralsType(1);
        Boolean result = peripheralsService.insert(dto).pickDataThrowException();
        log.info("增加外设:{}", result);
    }

    @Test
    public void insert1() {
        InsertPeripheralsDTO dto = new InsertPeripheralsDTO();
        //dto.setOrgId(orgId);
        //dto.setPeripheralsSn("CB360200000002");
        dto.setPeripheralsName("腕温仪_CB360200000002");
        dto.setDeviceSn("260910210121KN2034");
        dto.setDeviceName("门禁机260910210121KN2034");
        dto.setStatus(1);
        dto.setAvailable(1);
        dto.setBindTime(new Date());
        dto.setPeripheralsType(1);
        Boolean result = peripheralsService.insert(dto).pickDataThrowException();
        log.info("增加外设:{}", result);
    }

    @Test
    public void update() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
        dto.setPeripheralsId(1712138272479641600L);
        dto.setOrgId(orgId);
        dto.setAvailable(0);
        Boolean result = peripheralsService.update(dto).pickDataThrowException();
        log.info("更新外设:{}", result);
    }

    @Test
    public void update1() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
        //dto.setPeripheralsId(1712138272479641600L);
        //dto.setOrgId(orgId);
        dto.setAvailable(0);
        Boolean result = peripheralsService.update(dto).pickDataThrowException();
        log.info("更新外设:{}", result);
    }

    @Test
    public void listByCondition() {
        QueryPeripheralsDTO dto = new QueryPeripheralsDTO();
        dto.setOrgId(orgId);
        dto.setDeviceSn("260910210121KN2034");
        dto.setStatus(1);
        dto.setDeviceSns(Collections.singletonList("260910210121KN2034"));
        List<PeripheralsDTO> result = peripheralsService.listByCondition(dto).pickDataThrowException();
        log.info("根据条件查询外设信息列表:{}", result);
    }

    @Test
    public void listByCondition1() {
        QueryPeripheralsDTO dto = new QueryPeripheralsDTO();
        //dto.setOrgId(orgId);
        dto.setDeviceSn("260910210121KN2034");
        dto.setStatus(1);
        //dto.setDeviceSns(Collections.singletonList("260910210121KN2034"));
        List<PeripheralsDTO> result = peripheralsService.listByCondition(dto).pickDataThrowException();
        log.info("根据条件查询外设信息列表:{}", result);
    }

    @Test
    public void listByOrgIdAndDeviceSn() {
        List<PeripheralsDTO> result = peripheralsService.listByOrgIdAndDeviceSn(orgId, "260910210121KN2034").pickDataThrowException();
        log.info("根据设备sn查询该机构下外设信息列表:{}", result);
    }

    @Test
    public void listByOrgIdAndDeviceSn1() {
        List<PeripheralsDTO> result = peripheralsService.listByOrgIdAndDeviceSn(0L, null).pickDataThrowException();
        log.info("根据设备sn查询该机构下外设信息列表:{}", result);
    }

    @Test
    public void disConnect() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
        dto.setOrgId(orgId);
        dto.setDeviceSn("260910210121KN2034");
        dto.setPeripheralsSn("CB360200000002");
        Boolean result = peripheralsService.disConnect(dto).pickDataThrowException();
        log.info("外设断开:{}", result);
    }

    @Test
    public void disConnect1() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
//        dto.setOrgId(orgId);
//        dto.setDeviceSn("260910210121KN2034");
        dto.setPeripheralsSn("CB360200000002");
        Boolean result = peripheralsService.disConnect(dto).pickDataThrowException();
        log.info("外设断开:{}", result);
    }

    @Test
    public void mobileConnect() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
        dto.setOrgId(orgId);
        dto.setPeripheralsName("额温枪CB360200000004");
        dto.setPeripheralsSn("CB360200000004");
        dto.setDeviceSn("260910210121KN2034");
        dto.setDeviceName("门禁机260910210121KN2034");
        dto.setDeviceType(PerBindDeviceType.MOBILE_PHONE.getType());
        dto.setPeripheralsType(PeripheralsType.FOREHEAD_PER.getType());
        dto.setStatus(YesNoFlag.YES.getValue());
        dto.setAvailable(1);
        Boolean result = peripheralsService.mobileConnect(dto).pickDataThrowException();
        log.info("手机端-连接操作:{}", result);
    }

    @Test
    public void mobileConnect1() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
//        dto.setOrgId(orgId);
//        dto.setPeripheralsName("额温枪CB360200000004");
        dto.setPeripheralsSn("CB360200000004");
        dto.setDeviceSn("260910210121KN2034");
        dto.setDeviceName("门禁机260910210121KN2034");
        dto.setDeviceType(PerBindDeviceType.MOBILE_PHONE.getType());
        dto.setPeripheralsType(PeripheralsType.FOREHEAD_PER.getType());
        dto.setStatus(YesNoFlag.YES.getValue());
        dto.setAvailable(1);
        Boolean result = peripheralsService.mobileConnect(dto).pickDataThrowException();
        log.info("手机端-连接操作:{}", result);
    }

    @Test
    public void mobileDisConnect() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
        dto.setOrgId(orgId);
        dto.setDeviceSn("260910210121KN2034");
        dto.setPeripheralsSn("CB360200000004");
        dto.setDeviceType(PerBindDeviceType.MOBILE_PHONE.getType());
        dto.setStatus(PeripheralConnectionStatusEnums.UNBIND.getValue());
        Boolean result = peripheralsService.mobileDisConnect(dto).pickDataThrowException();
        log.info("手机端-断开以及解绑操作:{}", result);
    }

    @Test
    public void mobileDisConnect1() {
        UpdatePeripheralsDTO dto = new UpdatePeripheralsDTO();
//        dto.setOrgId(orgId);
        dto.setDeviceSn("260910210121KN2034");
        dto.setPeripheralsSn("CB360200000004");
        dto.setDeviceType(PerBindDeviceType.MOBILE_PHONE.getType());
//        dto.setStatus(PeripheralConnectionStatusEnums.UNBIND.getValue());
        Boolean result = peripheralsService.mobileDisConnect(dto).pickDataThrowException();
        log.info("手机端-断开以及解绑操作:{}", result);
    }

    @Test
    public void existHistoryPeripherals() {
        Boolean result = peripheralsService.existHistoryPeripherals(orgId).pickDataThrowException();
        log.info("查询机构是否存在历史外设设备:{}", result);
    }

    @Test
    public void existHistoryPeripherals1() {
        Boolean result = peripheralsService.existHistoryPeripherals(null).pickDataThrowException();
        log.info("查询机构是否存在历史外设设备:{}", result);
    }

    @Test
    public void listByOrgIdAndDeviceSns() {
        List<PeripheralsDTO> result = peripheralsService.listByOrgIdAndDeviceSns(orgId, Collections.singletonList("260910210121KN2034")).pickDataThrowException();
        log.info("根据设备sn列表获取外设信息列表:{}", result);
    }

    @Test
    public void listByOrgIdAndDeviceSns1() {
        List<PeripheralsDTO> result = peripheralsService.listByOrgIdAndDeviceSns(orgId, new ArrayList<>()).pickDataThrowException();
        log.info("根据设备sn列表获取外设信息列表:{}", result);
    }
}