package com.moredian.magicube.device.service;

import com.google.common.collect.Lists;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.subject.DeviceSubjectDTO;
import com.moredian.magicube.device.dto.subject.InsertDeviceSubjectDTO;
import com.moredian.magicube.device.dto.subject.ScreenSaver;
import com.moredian.magicube.device.dto.subject.UpdateDeviceSubjectDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DeviceSubjectServiceTest extends BaseTest {

    @Autowired
    private DeviceSubjectService deviceSubjectService;

    private static final Long orgId = 1643182358020489216L;

    /**
     * {"type":2,
     * "imgUrls":[""],
     * "allDeviceFlag":1,"deviceIds":[]}
     *{"type":2,"imgUrls":["http://mlhy-dev2.oss-cn-hangzhou.aliyuncs.com/temporary/2023/12/18/10/528d8c29ea9243d18307b8a4302dc388.jpg"],"allDeviceFlag":1,"deviceIds":[]}
     *
     * {"name":"主题","templateType":1,
     * "imgUrls":["https://mlhy2-test.oss-cn-hangzhou.aliyuncs.com/postImage/2023/12/12/14/website_resource_1681095665634.png"],
     * "deviceIds":[],"logoImgUrl":"","companyName":"321313231231","id":"1785056372006584320"}
     *
     *
     *
     *     Long orgId = 1643182358020489216L;
     *         Long deviceId = 1768210805922201600L;
     */
    @Test
    public void insert() {
//        InsertDeviceSubjectDTO insertDeviceSubjectDTO = new InsertDeviceSubjectDTO();
//        insertDeviceSubjectDTO.setOrgId(1643182358020489216L);
////        insertDeviceSubjectDTO.setName("屏保111");
//        insertDeviceSubjectDTO.setAllDeviceFlag(0);
//        insertDeviceSubjectDTO.setType(2);
////        insertDeviceSubjectDTO.setTemplateType(1);
//        insertDeviceSubjectDTO.setDeviceIds(Collections.singletonList(1768210805922201600L));
////        insertDeviceSubjectDTO.setCompanyName("*********");
//        insertDeviceSubjectDTO.setImgUrls(Lists.newArrayList("http://mlhy-dev2.oss-cn-hangzhou.aliyuncs.com/temporary/2023/12/11/14/1795045590c64255a5d8f914e652382b.jpg"));
//        ScreenSaver screenSaver = new ScreenSaver();
//        screenSaver.setWeekdays(Collections.singletonList(1));
//        screenSaver.setTimeList(Collections.singletonList("8:00-10:00"));
//        screenSaver.setIsAllDay(0);
//        screenSaver.setEnableVoice(0);
//        screenSaver.setFirstScreenImg("http://mlhy-dev2.oss-cn-hangzhou.aliyuncs.com/temporary/2023/12/11/14/1795045590c64255a5d8f914e652382b.jpg");
//        screenSaver.setIsVideo(1);
//        insertDeviceSubjectDTO.setScreenSaver(screenSaver);
//        Long result = deviceSubjectService.insert(insertDeviceSubjectDTO)
//            .pickDataThrowException();
//        log.info("新增设备主题result:{}", result);
    }

//    {"type":2,
//    "imgUrls":["https://mlhy2-test.oss-cn-hangzhou.aliyuncs.com/postImage/2023/12/12/11/a6d835a823f240ec92f1e49d2f5eead9.jpg","https://mlhy2-test.oss-cn-hangzhou.aliyuncs.com/postImage/2023/12/12/11/eee8328528de4c298ec0ccfc64566f29.jpg"],
//    "allDeviceFlag":0,"deviceIds":["1784335298537193472"],"id":"1784987867278213121"}


    /**
     * {"type":2,
     * "imgUrls":["https://mlhy2-test.oss-cn-hangzhou.aliyuncs.com/temporary/2023/12/12/14/c4937bb1948c49128bb4a21f045aa856.jpg"],
     * "allDeviceFlag":1,"deviceIds":[],"id":"1785055916740050944"}
     *
     *
     *
     * {"name":"主题","templateType":1,
     * "imgUrls":["https://mlhy2-test.oss-cn-hangzhou.aliyuncs.com/postImage/2023/4/10/11/website_resource_1681095665634.png"],
     * "deviceIds":[],"logoImgUrl":"","companyName":"3213132","type":1}
     *
     *
     * {"name":"主题","templateType":1,
     * "imgUrls":["https://mlhy2-test.oss-cn-hangzhou.aliyuncs.com/postImage/2023/12/12/14/website_resource_1681095665634.png"],
     * "deviceIds":[],"logoImgUrl":"","companyName":"321313231231","id":"1785056372006584320"}
     */
    @Test
    public void update() {
//        UpdateDeviceSubjectDTO insertDeviceSubjectDTO = new UpdateDeviceSubjectDTO();
//        insertDeviceSubjectDTO.setOrgId(1643182358020489216L);
////        insertDeviceSubjectDTO.setName("屏保111");
//        insertDeviceSubjectDTO.setId(1794752028749070336L);
//        insertDeviceSubjectDTO.setAllDeviceFlag(1);
//        insertDeviceSubjectDTO.setType(2);
//        insertDeviceSubjectDTO.setType(2);
////        insertDeviceSubjectDTO.setTemplateType(1);
//        insertDeviceSubjectDTO.setDeviceIds(Collections.singletonList(1768210805922201600L));
////        insertDeviceSubjectDTO.setCompanyName("*********");
//        insertDeviceSubjectDTO.setImgUrls(Lists.newArrayList("http://mlhy-dev2.oss-cn-hangzhou.aliyuncs.com/temporary/2023/12/11/14/1795045590c64255a5d8f914e652382b.jpg"));
//        ScreenSaver screenSaver = new ScreenSaver();
//        screenSaver.setWeekdays(Collections.singletonList(1));
//        screenSaver.setTimeList(Collections.singletonList("8:00-10:00"));
//        screenSaver.setIsAllDay(0);
//        screenSaver.setEnableVoice(0);
//        screenSaver.setFirstScreenImg("http://mlhy-dev2.oss-cn-hangzhou.aliyuncs.com/temporary/2023/12/11/14/1795045590c64255a5d8f914e652382b.jpg");
//        screenSaver.setIsVideo(1);
//        insertDeviceSubjectDTO.setScreenSaver(screenSaver);
//        Boolean result = deviceSubjectService.update(insertDeviceSubjectDTO)
//            .pickDataThrowException();
//        log.info("编辑设备主题result:{}", result);
    }

    @Test
    public void getByOrgIdAndSubjectId() {
        DeviceSubjectDTO result = deviceSubjectService.getByOrgIdAndSubjectId(1643182358020489216L,1794755602161860608L)
            .pickDataThrowException();
        log.info("根据机构id和主题id查询设备主题信息result:{}", result);
    }

    @Test
    public void listByOrgIdAndType() {
        List<DeviceSubjectDTO> result = deviceSubjectService.listByOrgIdAndType(orgId,2)
                .pickDataThrowException();
        log.info("查询设备主题列表result:{}", result);
    }


    @Test
    public void listByOrgIdAndTypePage() {
        DeviceSubjectDTO deviceSubjectDTO = new DeviceSubjectDTO();
        deviceSubjectDTO.setOrgId(orgId);
        deviceSubjectDTO.setType(2);
        deviceSubjectDTO.setPageNo(1);
        deviceSubjectDTO.setPageSize(2);
        Pagination<DeviceSubjectDTO> result = deviceSubjectService.listByOrgIdAndTypePage(deviceSubjectDTO)
                .pickDataThrowException();
        log.info("查询设备主题列表result:{}", result);
    }


    @Test
    public void updateScreenSwitch() {
        Boolean result = deviceSubjectService.updateScreenSwitch(1643182358020489216L, 1794755602161860608L, 0)
                .pickDataThrowException();
        log.info("查询设备主题列表result:{}", result);
    }

    @Test
    public void deleteByOrgIdAndIds() {
        Boolean result = deviceSubjectService.deleteByOrgIdAndIds(orgId,Lists.newArrayList(1775462042132545536L))
            .pickDataThrowException();
        log.info("批量删除设备主题result:{}", result);
    }
}
