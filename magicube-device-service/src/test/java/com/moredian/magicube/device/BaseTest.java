package com.moredian.magicube.device;

import ch.qos.logback.classic.PatternLayout;
import com.moredian.bee.config.properties.BeeClientConfiguration;
import com.moredian.bee.log.support.RequestConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.MapPropertySource;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest(classes = DeviceServiceStarter.class)
public abstract class BaseTest {

    static {
        System.setProperty("spring.main.allow-circular-references", "true");
        System.setProperty("spring.mvc.pathmatch.matchingStrategy", "ANT_PATH_MATCHER");
        System.setProperty("spring.main.allowBeanDefinitionOverriding", "true");
        setDefaultSpringBootProperty();
    }

    private static void setDefaultSpringBootProperty() {
        PatternLayout.defaultConverterMap.put("req", RequestConverter.class.getName());
        Map<String, Object> restProperties = new HashMap<>();
        CompositePropertySource cp = BeeClientConfiguration.getBeeCompositePropertySource();
        String[] var2 = cp.getPropertyNames();
        //    int var3 = var2.length;

        for (String propertyKey : var2) {
            if (propertyKey.startsWith("bee.log")) {
                System.setProperty(propertyKey, (String) cp.getProperty(propertyKey));
            }

            if (propertyKey.startsWith("bee.log.level.")) {
                restProperties.put(propertyKey.replaceAll("bee\\.log\\.level", "logging.level"), cp.getProperty(propertyKey));
            }
        }

        MapPropertySource resetSource = new MapPropertySource("beenvReset", restProperties);
        cp.addFirstPropertySource(resetSource);
        System.setProperty("bee.config.appname", BeeClientConfiguration.getBeeClientLocalProperties().getAppName());
    }
}