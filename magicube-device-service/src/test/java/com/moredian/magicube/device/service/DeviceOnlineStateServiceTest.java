package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.manager.DeviceOnlineStateManager;
import com.moredian.ota.api.msg.DeviceOnlineStateMsg;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;


@Slf4j
public class DeviceOnlineStateServiceTest extends BaseTest {

    private static final Long orgId = 1770119291794882560L;

    @Resource
    private DeviceOnlineStateManager deviceOnlineStateManager;

    @Test
    void insert() {
        long timeMillis = System.currentTimeMillis();
        DeviceOnlineStateMsg msg = new DeviceOnlineStateMsg();
        msg.setOrgId(orgId);
        msg.setDeviceId(orgId);
        msg.setDeviceSn("840115230613GN0202");
        msg.setOnlineFlag(true);
        msg.setMsgTimestamp(timeMillis);

        DeviceOnlineStateMsg msg2 = new DeviceOnlineStateMsg();
        BeanUtils.copyProperties(msg, msg2);
        msg2.setOnlineFlag(true);
        DeviceOnlineStateMsg msg3 = new DeviceOnlineStateMsg();
        BeanUtils.copyProperties(msg, msg3);
        msg3.setOnlineFlag(false);
//        deviceOnlineStateManager.insert(msg);
//        new Thread(() -> {
//            try {
//                Thread.sleep(100);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            deviceOnlineStateManager.insert(msg2);
//        }).start();
//
//        new Thread(() -> {
//            try {
//                Thread.sleep(100);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            deviceOnlineStateManager.insert(msg3);
//        }).start();


        deviceOnlineStateManager.insert(msg3);
    }

}

