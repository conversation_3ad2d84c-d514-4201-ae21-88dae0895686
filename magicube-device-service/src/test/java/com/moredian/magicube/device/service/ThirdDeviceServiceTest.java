package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.third.ThirdDeviceResponse;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @description：TODO
 * @date ：2024/11/14 14:29
 */
@Slf4j
public class ThirdDeviceServiceTest extends BaseTest {

    @Resource
    private ThirdDeviceService thirdDeviceService;


    @Test
    void getThirdDeviceByIdListTest() {
        Long orgId = 1773715442142019584L;

        List<Long> thirdDeviceIdList = new ArrayList<>();
        thirdDeviceIdList.add(1L);

        List<ThirdDeviceResponse> list = thirdDeviceService.getThirdDeviceByIdList(
            orgId, thirdDeviceIdList).pickDataThrowException();

        log.info("list=>{}", list);
    }






}
