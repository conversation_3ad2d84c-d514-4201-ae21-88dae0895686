package com.moredian.magicube.device.manager;

import com.moredian.magicube.device.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2023-06-29
 */
public class DeviceFaceModelManagerTest extends BaseTest {

    @Autowired
    private DeviceFaceModelManager deviceFaceModelManager;

    @Test
    public void findDeviceByOrgTest() {
        System.out.println(deviceFaceModelManager.findDeviceByOrg(1700361876107427840L));
    }

    @Test
    public void deleteByDeviceTest() {
        System.out.println(deviceFaceModelManager.deleteByDevice(1700361876107427840L, 1754602408203255808L));
    }

}
