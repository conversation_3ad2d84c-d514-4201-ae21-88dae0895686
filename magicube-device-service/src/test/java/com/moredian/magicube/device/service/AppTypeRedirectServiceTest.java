package com.moredian.magicube.device.service;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.common.utils.Pagination;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.AppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.PageAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.QueryAppTypeRedirectDTO;
import com.moredian.magicube.device.dto.device.SaveAppTypeRedirectDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/20
 */
public class AppTypeRedirectServiceTest extends BaseTest {

    @Autowired
    private AppTypeRedirectService appTypeRedirectService;

    @Test
    public void save(){
        for (int i = 4; i < 20; i++) {
            SaveAppTypeRedirectDTO saveAppTypeRedirectDTO = new SaveAppTypeRedirectDTO();
            saveAppTypeRedirectDTO.setAppType(3 + i);
            saveAppTypeRedirectDTO.setRedirectUrl("http://www.baidu.com1");
            Long aLong = appTypeRedirectService.save(saveAppTypeRedirectDTO).pickDataThrowException();
            System.out.println(aLong);
        }
    }

    @Test
    public void detail(){
        QueryAppTypeRedirectDTO dto = new QueryAppTypeRedirectDTO();
        dto.setAppType(3);
        dto.setId(1785801909609693184L);
        dto.setRedirectUrl("http://www.baidu.com1");
        AppTypeRedirectDTO appTypeRedirectDTO = appTypeRedirectService.detail(dto).pickDataThrowException();
        System.out.println(JsonUtils.toJson(appTypeRedirectDTO));
    }

    @Test
    public void page(){
        PageAppTypeRedirectDTO dto = new PageAppTypeRedirectDTO();
        dto.setPageSize(20);
        dto.setPageNo(1);
        dto.setRedirectUrl("www");
        Pagination<AppTypeRedirectDTO> page = appTypeRedirectService.page(dto).pickDataThrowException();
        System.out.println(JsonUtils.toJson(page));
    }
}
