package com.moredian.magicube.device.service;

import com.moredian.magicube.common.model.msg.DeviceUnbindMsg;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.type.InsertDeviceTypeDTO;
import com.moredian.magicube.device.subscriber.DeviceUnbindMsgSubscriber;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class DeviceUnbindMsgSubscriberTest  extends BaseTest {

    @Autowired
    private DeviceUnbindMsgSubscriber deviceUnbindMsgSubscriber;

    @Test
    public void deviceUnbindMsgSubscriber() {
        DeviceUnbindMsg deviceUnbindMsg = new DeviceUnbindMsg();
        deviceUnbindMsg.setOrgId(1781902422902308864L);
        deviceUnbindMsg.setDeviceId(1785778879995052032L);
        deviceUnbindMsg.setDeviceSn("879912230327KN0001");
        deviceUnbindMsgSubscriber.subDeviceUnbindMsg(deviceUnbindMsg);
    }

}
