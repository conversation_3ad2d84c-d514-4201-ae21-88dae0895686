package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.monitor.MonitorLogDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/10/13
 */
public class DeviceLogMonitorServiceTest extends BaseTest {

    @Autowired
    private DeviceLogMonitorService deviceLogMonitorService;

    @Test
    public void syncMonitorLog() {
        String deviceSn = "K49919230915KN0026";
        List<MonitorLogDto> monitorLogDtoList = new ArrayList<>();
        MonitorLogDto dto = new MonitorLogDto();
        dto.setEventCode("1");
        dto.setEventData("2");
        dto.setEventTime(111L);
        dto.setFiles("https://123");
        dto.setIndicatorNum(new BigDecimal(1));
        dto.setPersonId("111");
        monitorLogDtoList.add(dto);
        deviceLogMonitorService.syncMonitorLog(deviceSn, monitorLogDtoList);
    }
}
