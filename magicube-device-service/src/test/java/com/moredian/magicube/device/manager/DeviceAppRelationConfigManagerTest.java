package com.moredian.magicube.device.manager;
import com.moredian.space.message.UpdateTreeDeviceRelationMsg;
import java.util.List;

import com.moredian.bee.rmq.EventBus;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.model.msg.device.DeviceActiveEvent;
import com.moredian.magicube.common.model.msg.device.DeviceAppVersionReportMsg;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dao.entity.DeviceAppRelationConfig;
import com.moredian.magicube.device.service.DeviceService;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class DeviceAppRelationConfigManagerTest  extends BaseTest {

    @Resource
    private DeviceAppRelationConfigManager deviceAppRelationConfigManager;


    @Test
    public void test1(){

        DeviceAppRelationConfig deviceAppRelationConfig = new DeviceAppRelationConfig();
        deviceAppRelationConfig.setSpaceType(1);
        deviceAppRelationConfig.setAppType(101);
        deviceAppRelationConfig.setVersionCode(102);
        deviceAppRelationConfig.setDefaultAppCode("100,101,102");
        deviceAppRelationConfig.setAvailableAppCodeList("100,101,102");

        Long insert = deviceAppRelationConfigManager.insert(deviceAppRelationConfig);
        System.out.println(insert);
    }





    @Test
    public void test2(){
        DeviceAppRelationConfig update = new DeviceAppRelationConfig();
        update.setId(1799806621140910080L);
        update.setAppType(105);
        update.setVersionCode(105);
        update.setDefaultAppCode("100,101,102,105");
        int i = deviceAppRelationConfigManager.updateSelectiveById(update);
        System.out.println(i == 1);


    }
    @Test
    public void test3(){

        List<DeviceAppRelationConfig> deviceAppRelationConfigs = deviceAppRelationConfigManager.selectBySpaceTypeAndAppTypeAndVersionCode(1, 105, 104);
        deviceAppRelationConfigs.forEach(System.out::println);

    }

    @Test
    public void test4(){
        int i = deviceAppRelationConfigManager.deleteById(1799806518061694976L);

        System.out.println(i ==1);
    }

    /**
     * 设备激活消息
     * @throws InterruptedException
     */
    @Test
    public void test5() throws InterruptedException {

        DeviceActiveEvent deviceActiveEvent = new DeviceActiveEvent();
        deviceActiveEvent.setOrgId(1785606411288313856L);
        deviceActiveEvent.setDeviceId(1799200953442762752L);
        deviceActiveEvent.setDeviceSn("840212231025GN0050");
        deviceActiveEvent.setDeviceType(124);
        deviceActiveEvent.setTimeStamp(System.currentTimeMillis());
        deviceActiveEvent.setSpaceType(36);
        deviceActiveEvent.setTreeId(1785606531547398144L);

        EventBus.publish(deviceActiveEvent);
        while (true){
            Thread.sleep(10000);
            System.out.println("防止程序退出");
        }
    }

    /**
     * 设备切换空间消息
     * @throws InterruptedException
     */
    @Test
    public void test6() throws InterruptedException {

        UpdateTreeDeviceRelationMsg updateTreeDeviceRelationMsg = new UpdateTreeDeviceRelationMsg();
        updateTreeDeviceRelationMsg.setOldTreeId(1776184627430424576L);
        updateTreeDeviceRelationMsg.setTreeId(1774372245720793088L);
        updateTreeDeviceRelationMsg.setDeviceId("1775733535605260288");
        updateTreeDeviceRelationMsg.setDeviceSource(1);
        updateTreeDeviceRelationMsg.setOrgId(1774371429677006848L);
        updateTreeDeviceRelationMsg.setTreeDeviceRelationId(0L);


        EventBus.publish(updateTreeDeviceRelationMsg);
        while (true){
            Thread.sleep(10000);
            System.out.println("防止程序退出");
        }
    }

    /**
     * 设备上报版本消息
     * @throws InterruptedException
     */

    @Test
    public void test8() throws InterruptedException {

        DeviceAppVersionReportMsg deviceAppVersionReportMsg = new DeviceAppVersionReportMsg();

        deviceAppVersionReportMsg.setDeviceSn("840212231025GN0050");
        deviceAppVersionReportMsg.setAppType(70019);
        deviceAppVersionReportMsg.setVersionCode(30307012);
        deviceAppVersionReportMsg.setDeviceId(1799200953442762752L);
        deviceAppVersionReportMsg.setOrgId(1785606411288313856L);

        EventBus.publish(deviceAppVersionReportMsg);
        while (true){
            Thread.sleep(10000);
            System.out.println("防止程序退出");
        }
    }

    @SI
    private DeviceService deviceService;


    /**
     *
     */
    @Test
    public void test9(){

    }


}




