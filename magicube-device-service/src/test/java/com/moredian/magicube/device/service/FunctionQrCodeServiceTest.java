package com.moredian.magicube.device.service;

import com.alibaba.fastjson.JSON;
import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.bee.filemanager.FileManager;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.ActivateQrCodeSimpleDto;
import com.moredian.magicube.device.dto.qrcode.CableStatic;
import com.moredian.magicube.device.dto.qrcode.InsertNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeCreateDto;
import com.moredian.magicube.device.dto.qrcode.NetworkQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.SimpleActivateQrCodeDto;
import com.moredian.magicube.device.dto.qrcode.UpdateNetworkInfoDTO;
import com.moredian.magicube.device.dto.qrcode.WifiInfo;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant;
import com.moredian.magicube.device.enums.ActivateQrCodeConstant.RoleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/4/26
 */
@Slf4j
public class FunctionQrCodeServiceTest extends BaseTest {

    @Autowired
    private FunctionQrCodeService functionQrCodeService;

    @Autowired
    private FileManager fileManager;

    @Test
    public void createActivateQrCode() {
        ActivateQrCodeCreateDto dto = new ActivateQrCodeCreateDto();
        dto.setOrgId(1774371429677006848L);
        dto.setMemberId(1774371429677006848L);
        dto.setRoleType(RoleTypeEnum.ADMINISTRATOR.getType());
        dto.setQrCodeName("测试二维码");
        dto.setDeviceAddressName("测试位置");
        dto.setTreeId(1774372254310727680L);
        dto.setConnectType(3);
        dto.setNetworkType(2);
        dto.setCableStatic(JSON.parseObject("{\"dns\":\"************\",\"gateway\":\"\",\"ip\":\"\",\"mask\":\"\"}", CableStatic.class));
        dto.setWifiInfo(JSON.parseObject("{\"name\":\"DingTalk\",\"password\":\"njytddrr\"}", WifiInfo.class));
        Long id = functionQrCodeService.createActivateQrCode(dto).pickDataThrowException();
        log.info("创建激活二维码:{}", id);
    }

    @Test
    public void testGetNewestNetWorkInfoByOrgId() {
        /*Long orgId = 1722541387645190144L;
        NetworkQrCodeDto networkQrCodeDto = functionQrCodeService.getNewestNetWorkInfoByOrgId(orgId).pickDataThrowException();
        System.out.println(JsonUtils.toJson(networkQrCodeDto));*/
        String url = fileManager.getUrlByRelativePath("/temporary/2023/8/16/16/1692173604669.jpg");
    }

    @Test
    public void getSimpleQrCodeByQrCodeId() {
        Long qrCodeId = 1761240202623320064L;
        SimpleActivateQrCodeDto dto = functionQrCodeService.getSimpleQrCodeByQrCodeId(qrCodeId).pickDataThrowException();
        log.info("获取激活码简单信息:{}", dto);
    }

    @Test
    public void insertNetworkInfo() {
        InsertNetworkInfoDTO dto = new InsertNetworkInfoDTO();
        dto.setOrgId(1770119291794882560L);
        dto.setConnectType(3);
        dto.setNetworkType(2);
        dto.setCableStatic(JSON.parseObject("{\"dns\":\"************\",\"gateway\":\"\",\"ip\":\"\",\"mask\":\"\"}", CableStatic.class));
        dto.setWifiInfo(JSON.parseObject("{\"name\":\"DingTalk\",\"password\":\"njytddrr\"}", WifiInfo.class));
        Long id = functionQrCodeService.insertNetworkInfo(dto).pickDataThrowException();
        log.info("新增机构配网信息:{}", id);
    }

    @Test
    public void createNetworkInfo() {
        NetworkQrCodeCreateDto dto = new NetworkQrCodeCreateDto();
        dto.setOrgId(1770119291794882560L);
        dto.setConnectType(3);
        dto.setNetworkType(2);
        dto.setResetNetworkType(2);
        dto.setSource(3);
//        dto.setCableStatic(JSON.parseObject("{\"dns\":\"************\",\"gateway\":\"\",\"ip\":\"\",\"mask\":\"\"}", CableStatic.class));
        dto.setWifiInfo(JSON.parseObject("{\"name\":\"DingTalk\",\"password\":\"njytddrr\"}", WifiInfo.class));
        dto.setServerAddress("***********");
        Long id = functionQrCodeService.createNetworkQrCode(dto).pickDataThrowException();
        log.info("新增机构配网信息:{}", id);
    }

    @Test
    public void testGetActivateQrCodeByOrgIdAndId(){
        ActivateQrCodeDto qrCodeDto = functionQrCodeService.getActivateQrCodeByOrgIdAndId(
            1773558538396762112L, 1784678122826760192L).pickDataThrowException();
        System.out.println(qrCodeDto);

    }

    @Test
    public void listHistoryOrgByMemberId(){
        Long memberId = 1768822950405865472L;
        Integer roleType = 1;
        List<ActivateQrCodeSimpleDto> activateQrCodeSimpleDtos = functionQrCodeService.listHistoryOrgByMemberId(memberId, roleType).pickDataThrowException();
        System.out.println(JsonUtils.toJson(activateQrCodeSimpleDtos));
    }

    @Test
    public void updateNetworkInfo() {
        Long id = 1785251054284177408L;
        Long orgId = 1784427717643468800L;
        UpdateNetworkInfoDTO dto = new UpdateNetworkInfoDTO();
        dto.setId(id);
        dto.setNetworkType(ActivateQrCodeConstant.NetworkTypeEnum.WIRED.getType());
        dto.setConnectType(ActivateQrCodeConstant.ConnectTypeEnum.STATIC_CONFIG.getType());
        CableStatic cableStatic = new CableStatic();
        cableStatic.setIp("*******");
        cableStatic.setDns("*******");
        cableStatic.setMask("*******");
        cableStatic.setGateway("*******");
        dto.setCableStatic(cableStatic);
        dto.setWifiInfo(null);
        dto.setOrgId(orgId);
        Boolean aBoolean = functionQrCodeService.updateNetworkInfo(dto).pickDataThrowException();
        System.out.println(aBoolean);
    }
}
