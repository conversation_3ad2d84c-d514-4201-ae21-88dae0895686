package com.moredian.magicube.device.service;

import com.alibaba.fastjson.JSON;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.migration.*;
import com.moredian.magicube.device.manager.MigrationDeviceManager;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class MigrationDeviceServiceTest extends BaseTest {

    @Resource
    private MigrationDeviceService migrationDeviceService;
    @Resource
    private MigrationDeviceManager migrationDeviceManager;

    private final Long orgId = 1797742365369171968L, deviceId = 1808139441810702336L;
    private final String deviceSn = "409914220421KN0007";

    @Test
    void getList() {
        ServiceResponse<MigrationDeviceRes> res = migrationDeviceService.getList(orgId);
        System.out.println("getList---------" + JSON.toJSONString(res));
    }

    @Test
    void reqBindOrgUrl() {
        ServiceResponse<Boolean> res = migrationDeviceService.reqBindOrgUrl(orgId, 1809075985199398912L);
        System.out.println("reqBindOrgUrl---------" + JSON.toJSONString(res));
    }

    @Test
    void getBindOrgUrl() {
        ServiceResponse<MigrationDeviceBindOrgUrlRes> res = migrationDeviceService.getBindOrgUrl(orgId, deviceId);
        System.out.println("getBindOrgUrl---------" + JSON.toJSONString(res));
    }

    @Test
    void bindOrgUrlSync() {
        ServiceResponse<Boolean> res = migrationDeviceService.bindOrgUrlSync("300318230406KN0478", "www.baidu.com");
        System.out.println("bindOrgUrlSync---------" + JSON.toJSONString(res));
    }

    @Test
    void getOrgIsEqual() {
        ServiceResponse<MigrationDeviceOrgRes> res = migrationDeviceService.getOrgIsEqual(deviceSn, "dingd716f501650fbc6c35c2f4657eb6378f");
        System.out.println("getOrgIsEqual---------" + JSON.toJSONString(res));
    }

    @Test
    void getMigrationDeviceStatus() {
        ServiceResponse<MigrationDeviceStatusRes> res = migrationDeviceService.getMigrationDeviceStatus(orgId, deviceId);
        System.out.println("getMigrationDeviceStatus---------" + JSON.toJSONString(res));

    }

    @Test
    void migrationDeviceStatusSync() {
        ServiceResponse<Boolean> res = migrationDeviceService.migrationDeviceStatusSync(orgId, deviceSn, true);
        System.out.println("migrationDeviceStatusSync---------" + JSON.toJSONString(res));
    }

    @Test
    void getMigrationDeviceCount() {
        ServiceResponse<MigrationDeviceOrgCountRes> res = migrationDeviceService.getMigrationDeviceCount(orgId);
        System.out.println("getMigrationDeviceCount---------" + JSON.toJSONString(res));
    }

    @Test
    void mdOrgTransferDingOrgMsg() {
        migrationDeviceManager.mdOrgTransferDingOrgMsg(1797742365369171968L);
    }

    @Test
    void subDeviceActiveMsg(){
        migrationDeviceManager.subDeviceActiveMsg(1802703585171996672L,1807819672905580544L,"230214220111KN0781");
    }
}