package com.moredian.magicube.device.mockito;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.IotDeviceDTO;
import com.moredian.magicube.device.service.IotDeviceService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description：TODO
 * @date ：2024/07/23 10:02
 */

@Slf4j
public class DemoTest extends BaseTest {

    @MockBean
    private IotDeviceService iotDeviceService;

    @Test
    public void mockList() {
        IotDeviceDTO iotDeviceDTO = iotDeviceService.getById(1777013504578945024L,
            1803703204450402338L).pickDataThrowException();

        log.info("iotDeviceDTO=>{}", iotDeviceDTO);

    }


}
