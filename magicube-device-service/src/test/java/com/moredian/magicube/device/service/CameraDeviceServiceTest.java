package com.moredian.magicube.device.service;

import com.google.common.collect.Lists;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.carme.InsertPeopleNumStatisticDTO;
import com.moredian.magicube.device.dto.carme.PeopleNumberDTO;
import com.moredian.magicube.device.dto.dahua.CameraDeviceInfoDTO;
import com.moredian.magicube.device.dto.dahua.CameraPeopleNumDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDetailDTO;
import com.moredian.magicube.device.dto.type.InsertDeviceTypeDTO;
import com.moredian.magicube.device.manager.dahua.CameraDeviceManager;
import com.moredian.magicube.device.service.dahua.CameraDeviceService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
public class CameraDeviceServiceTest extends BaseTest {

    @SI
    private PeopleNumberStatisticService peopleNumberStatisticService;

    @SI
    private CameraDeviceService cameraDeviceService;

    @Resource
    private CameraDeviceManager cameraDeviceManager;

    @Test
    public void list() {
        List<CameraDeviceInfoDTO> result = cameraDeviceService.list().pickDataThrowException();
        log.info("查询摄像头列表result:{}", result);
    }

    @Test
    public void insert() {
        InsertPeopleNumStatisticDTO dto = new InsertPeopleNumStatisticDTO();
        dto.setOrgId(1777013504578945024L);
        dto.setDeviceId(1792656359586529280L);
        dto.setDeviceSn("9M084FCPAGC62D3");
        dto.setInsidePeopleNum(100);
        Long result = peopleNumberStatisticService.insert(dto).pickDataThrowException();
        log.info("新增摄像头人数统计信息result:{}", result);
    }

    @Test
    public void listByOrgIdAndDeviceSns() {
        List<String> deviceSns = Lists.newArrayList();
        deviceSns.add("9M084FCPAGC62D3");
        List<PeopleNumberDTO> result = peopleNumberStatisticService
            .listByOrgIdAndDeviceSns(1777013504578945024L, deviceSns).pickDataThrowException();
        log.info("根据设备sn列表查询摄像头人数统计信息result:{}", result);
    }

    @Test
    void getCameraPeopleNum() {
        CameraPeopleNumDTO cameraPeopleNumDTOS = cameraDeviceService.getCameraPeopleNum(1808443181897875456L,
            "1812578994885754880").pickDataThrowException();
        log.info("cameraPeopleNumDTOS => {}", cameraPeopleNumDTOS);
    }

    @Test
    void updateByDeviceSnTest() {
        CameraDeviceInfoDTO dto = new CameraDeviceInfoDTO();
        dto.setPort(222);
        dto.setDeviceIp("***********");

        String deviceSn = "9M084FCPAGC62D3";
        Boolean aBoolean = cameraDeviceManager.updateByDeviceSn(deviceSn, dto);


        log.info("updateByDeviceSnTest => {}", aBoolean);
    }
}
