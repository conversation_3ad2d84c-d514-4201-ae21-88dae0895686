package com.moredian.magicube.device.service;

import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.DeviceReportInfoDTO;
import com.moredian.magicube.device.dto.device.ReportDeviceInfoDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/1/25
 */
public class DeviceReportServiceTest extends BaseTest {

    @Autowired
    private DeviceReportService deviceReportService;


    @Test
    public void transfer() {
        DeviceReportInfoDTO dto = new DeviceReportInfoDTO();
        dto.setOrgId(1773531574592077824L);
        dto.setDeviceId(1789137003254644736L);
        deviceReportService.notifyDeviceReportInfo(dto);
    }

    @Test
    public void reportDeviceInfo() {
        Long orgId = 1773715442142019584L;
        Long deviceId = 1785226950927712256L;
        String deviceSn = "K49919231026KN0003";
        ReportDeviceInfoDTO reportDeviceInfoDTO = new ReportDeviceInfoDTO();
        reportDeviceInfoDTO.setOrgId(orgId);
        reportDeviceInfoDTO.setDeviceId(deviceId);
        reportDeviceInfoDTO.setDeviceSn(deviceSn);
        Boolean result = deviceReportService.reportDeviceInfo(reportDeviceInfoDTO).pickDataThrowException();
        System.out.println(result);
    }
}
