package com.moredian.magicube.device.service;

import com.alibaba.fastjson.JSON;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.device.DeviceInfoDTO;
import com.moredian.magicube.device.dto.subject.DeviceSubjectSimpleDTO;
import java.util.ArrayList;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;


@Slf4j
class DeviceSubjectRelationServiceTest extends BaseTest {


    @Autowired
    private DeviceSubjectRelationService deviceSubjectRelationService;

    @Test
    void resetRelationBySubjectId() {
    }

    @Test
    void getDeviceSubjectByOrgIdAndDeviceId() {
//        1787789903942123520",
//        "deviceId": "1794481274010730496
        Long orgId = 1643182358020489216L;
        Long deviceId = 1768210805922201600L;
        List<DeviceSubjectSimpleDTO> dtos = deviceSubjectRelationService.
            getDeviceSubjectByOrgIdAndDeviceId(orgId, deviceId, null).pickDataThrowException();
        log.info("{}", JSON.toJSONString(dtos));
    }

    @Test
    void checkDevice() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1777386797366509568L);
        List<DeviceInfoDTO> dtos = deviceSubjectRelationService
            .checkDevice(1773810807595859968L, deviceIds, null,1).pickDataThrowException();
    }
}