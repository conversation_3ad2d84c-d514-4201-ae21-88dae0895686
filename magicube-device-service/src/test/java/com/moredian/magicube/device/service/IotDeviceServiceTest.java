package com.moredian.magicube.device.service;

import com.moredian.bee.common.utils.Pagination;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.mapping.IOrgIotClientMappingService;
import com.moredian.magicube.core.org.model.OrgInfo;
import com.moredian.magicube.core.org.request.OrgPagingQueryRequest;
import com.moredian.magicube.core.org.service.OrgService;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dao.entity.DeviceTypePropertyConfig;
import com.moredian.magicube.device.dao.mapper.DeviceTypePropertyConfigMapper;
import com.moredian.magicube.device.dto.device.IotDeviceDTO;
import com.moredian.magicube.device.dto.device.IotDevicePropertyDTO.Properties;
import com.moredian.magicube.device.dto.device.IotDeviceTypeCountDTO;
import com.moredian.magicube.device.dto.device.PageQueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.QueryIotDeviceDTO;
import com.moredian.magicube.device.dto.device.iot.DeviceFunctionInvokeDTO;
import com.moredian.magicube.device.dto.type.DeviceTypeDTO;
import com.moredian.magicube.device.manager.DeviceManager;
import com.moredian.magicube.device.utils.StringUtils;
import com.moredian.magicube.device.xxljob.DeviceSyncJob;
import com.moredian.magicube.ocean.service.DictService;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;


@Slf4j
class IotDeviceServiceTest extends BaseTest {

    private static final Long orgId = 1777013504578945024L;

    @Resource
    private IotDeviceService iotDeviceService;

    @Resource
    private DeviceSyncJob deviceSyncJob;

    @Resource
    private DeviceManager deviceManager;

    @Resource
    private DeviceTypePropertyConfigMapper deviceTypePropertyConfigMapper;

    @SI
    private IOrgIotClientMappingService iOrgIotClientMappingService;

    @SI
    private DictService dictService;

    @BeforeAll
    public static void create() {
        System.out.println("这是一个全局测试");
    }





    @Test
    void listIotDeviceTypeTest() {
        List<DeviceTypeDTO> result = iotDeviceService.listIotDeviceType().pickDataThrowException();
        log.info("查询iot设备类型信息result:{}", result);
        Assertions.assertNotNull(result);
    }

    /**
     * 魔链 iot 设备同步魔蓝平台
     */
    @Test
    void deviceSync() {
        // 机构是否开通魔链判断，未开通魔链直接返回空
        List<Long> orgIdList = iOrgIotClientMappingService.getAuthorizedOrgIdList()
            .pickDataThrowException();
        StringJoiner param = new StringJoiner(StringUtils.COMMA);
        orgIdList.stream().map(String::valueOf).forEach(param::add);
        String orgId = "1787789903942123520";
        ReturnT<String> result = deviceSyncJob.deviceSync(orgId);
        Assertions.assertEquals(200, result.getCode());
    }

    /**
     * 查询设备列表根节点设备
     */
    @Test
    void listIotDevice() {
        QueryIotDeviceDTO queryDeviceDTO = new QueryIotDeviceDTO();
        queryDeviceDTO.setOrgId(1777013504578945024L);
        queryDeviceDTO.setMemberId(1804740499181928448L);
//        queryDeviceDTO.setParentDeviceId("C49894C1AE5E");
        List<IotDeviceDTO> result = iotDeviceService.listIotDevice(queryDeviceDTO)
            .pickDataThrowException();
        log.info("查询iot设备列表result:{}", result);
    }



    /**
     * 根据设备id查询设备详情
     */
    @Test
    void getById() {
        Long orgId = 1776990818561687552L;
        IotDeviceDTO result = iotDeviceService.getById(orgId,1820584624946937856L).pickDataThrowException();
        log.info("查询Iot设备详情result:{}", result);
    }



    @Test
    public void listByConditionPage() {
        PageQueryIotDeviceDTO queryDeviceDTO = new PageQueryIotDeviceDTO();
        queryDeviceDTO.setOrgId(orgId);
        queryDeviceDTO.setPageNo(1);
        queryDeviceDTO.setPageSize(20);

        Pagination<IotDeviceDTO> result = iotDeviceService.listByConditionPage(queryDeviceDTO)
            .pickDataThrowException();
        log.info("根据条件分页查询Iot设备信息列表result:{}", result.getData());
    }


    @Test
    public void latestDeviceProperty(){
        List<Properties> dtos = iotDeviceService.latestDeviceProperty(
                1777013504578945024L, "000001521069")
            .pickDataThrowException();
        log.info("获取最新Iot设备属性result:{}",dtos);
    }

    @Test
    public void listDeviceTypeNum() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1800210047419023360L);
        deviceIds.add(1800210047419023361L);
        deviceIds.add(1800210047419023362L);
        deviceIds.add(1800210047419023363L);
        deviceIds.add(1800210047419023364L);
        deviceIds.add(1800210047419023365L);
        deviceIds.add(1800210047419023366L);
        deviceIds.add(1800210047419023367L);
        deviceIds.add(1800210047419023368L);
        deviceIds.add(1800210047419023369L);
        deviceIds.add(1800210047419023370L);
        deviceIds.add(1800210047419023371L);
        List<IotDeviceTypeCountDTO> result = iotDeviceService.listDeviceTypeCount(orgId,deviceIds)
            .pickDataThrowException();
        log.info("根据设备Id列表查询不同Iot设备类型数量result:{}", result);
    }

    @Test
    public void checkUsableIotDevice() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1800210047419023360L);
        deviceIds.add(1800210047419023361L);
        deviceIds.add(1800210047419023362L);
        deviceIds.add(1800210047419023363L);
        deviceIds.add(1800210047419023364L);
        deviceIds.add(1800210047419023365L);
        deviceIds.add(1800210047419023366L);
        deviceIds.add(1800210047419023367L);
        deviceIds.add(1800210047419023368L);
        deviceIds.add(1800210047419023369L);
        deviceIds.add(1800210047419023370L);
        deviceIds.add(1800210047419023371L);
        Boolean result = iotDeviceService.checkUsableIotDevice(orgId,deviceIds,1)
            .pickDataThrowException();
        log.info("校验是否有可用Iot物联网设备result:{}", result);
    }

    @Test
    public void getDeviceOfMember(){
        Long memberId = 1804740499181928448L;
        Long orgId = 1777013504578945024L;

        QueryIotDeviceDTO queryDeviceDTO = new QueryIotDeviceDTO();
        queryDeviceDTO.setOrgId(orgId);
        queryDeviceDTO.setMemberId(memberId);
        queryDeviceDTO.setParentDeviceId("LINKQI-202308283639");
        List<IotDeviceDTO> result = iotDeviceService.listIotDevice(queryDeviceDTO)
            .pickDataThrowException();
        log.info("查询iot设备列表result:{}", result);
    }



    @Test
    public void getDeviceOfMemberPage(){
        Long memberId = 1804740499181928448L;
        Long orgId = 1777013504578945024L;

        PageQueryIotDeviceDTO queryDeviceDTO = new PageQueryIotDeviceDTO();
        queryDeviceDTO.setOrgId(orgId);
        queryDeviceDTO.setMemberId(memberId);
        queryDeviceDTO.setPageNo(1);
        queryDeviceDTO.setPageSize(20);

        Pagination<IotDeviceDTO> result = iotDeviceService.listByConditionPage(queryDeviceDTO)
            .pickDataThrowException();
        log.info("根据条件分页查询Iot设备信息列表result:{}", result.getData());
    }


    @Test
    public void listIotDeviceByIotDeviceId(){
        Long id = 1787789903942123520L;

        List<IotDeviceDTO> result = iotDeviceService.listIotDeviceByIotDeviceId(id,
            1802811294361845774L).pickDataThrowException();
        log.info("该设备所在空间下的iot设备信息列表result:{}",result);
    }


    @Test
    void listDeviceTypePropertyConfig() {

        DeviceTypePropertyConfig propertyConfig = DeviceTypePropertyConfig.builder()
            .deviceType(504).build();

        List<DeviceTypePropertyConfig> list = deviceTypePropertyConfigMapper.listByEntity(propertyConfig);

        log.info("list=>{}", list);
    }

    @Resource
    private OrgService orgService;


    @Test
    void getOrgIds(){
        OrgPagingQueryRequest request = new OrgPagingQueryRequest();

        List<OrgInfo> orgInfos = orgService.pagingQueryOrgList(request).pickDataThrowException();


        log.info("orgInfos=>{}", orgInfos);
    }

    @Test
    void testFunctionInvoke() {
        DeviceFunctionInvokeDTO dto = new DeviceFunctionInvokeDTO();
        dto.setDeviceSn("000D6F00175D88F5");
        dto.setFunctionId("logupload");
        Map<String, Object> map = new HashMap<>();
        map.put("url", "1");
        map.put("code", "34");
        dto.setParams(map);


        Boolean b = iotDeviceService.deviceFunctionInvoke(dto).pickDataThrowException();

        log.info("b=>{}", b);
    }

}

