package com.moredian.magicube.device.service;

import com.moredian.bee.redis.util.RedissonCacheComponent;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.common.enums.DeviceType;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.constant.HiveConst;
import com.moredian.magicube.device.constant.StatusCode;
import com.moredian.magicube.device.dto.activate.ActivateDeviceDTO;
import com.moredian.magicube.device.dto.activate.ActivateDeviceResultDTO;
import com.moredian.magicube.device.dto.activate.GenerateQrCodeDTO;
import com.moredian.magicube.device.dto.activate.QrCodeDTO;
import com.moredian.magicube.device.dto.activate.QrCodeStatusDTO;
import com.moredian.magicube.device.dto.activate.QueryActivateInfoDTO;
import com.moredian.magicube.device.dto.activate.QueryActivationStatusDTO;
import com.moredian.magicube.device.dto.activate.QueryQrCodeStatusDTO;
import com.moredian.magicube.device.dto.activate.UpdateQrCodeStatusDTO;
import com.moredian.magicube.device.dto.device.DeviceActiveStateChangeDTO;
import com.moredian.magicube.device.dto.device.DeviceAuthDetailDTO;
import com.moredian.magicube.device.dto.device.DeviceBaseInfoDTO;
import com.moredian.magicube.device.dto.qrcode.DeviceActiveStateDTO;
import com.moredian.magicube.device.enums.DeviceActivateEngineEnums;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ActivityServiceTest extends BaseTest {

    @SI
    private ActivityService activityService;

    @Autowired
    private RedissonCacheComponent redissonCacheComponent;

    private static final Long orgId = 1712115199915327488L;

    @Test
    public void setRedis() {
            redissonCacheComponent.setObjectCache("496738712THIRD_PARTY_EQUIPMENT_ACTIVE_STATUS_KEY",
                StatusCode.THIRD_PARTY_ACTIVE_SUCCESS, HiveConst.THIRD_PARTY_TIME_OUT);
    }

    @Test
    public void generateQrCode() {
        GenerateQrCodeDTO dto = new GenerateQrCodeDTO();
        dto.setDeviceSn("439918220913KN0023");
        dto.setCheckCode("111");
        dto.setIsCheck(false);
        QrCodeDTO result = activityService.generateQrCode(dto).pickDataThrowException();
        log.info("生成激活码接口,获取设备激活码接口result:{}", result);
    }

    @Test
    public void generateQrCode1() {
        GenerateQrCodeDTO dto = new GenerateQrCodeDTO();
//        dto.setDeviceSn("270604210101KR0014");
        dto.setCheckCode("111");
        dto.setIsCheck(false);
        QrCodeDTO result = activityService.generateQrCode(dto).pickDataThrowException();
        log.info("生成激活码接口,获取设备激活码接口result:{}", result);
    }

    @Test
    public void getStatusByQrCode() {
        QueryQrCodeStatusDTO dto = new QueryQrCodeStatusDTO();
        dto.setQrCode("f6pe8haaj2j2maj6duqe6iauigtlkocmpo6j");
        QrCodeStatusDTO result = activityService.getStatusByQrCode(dto).pickDataThrowException();
        log.info("根据二维码获取对应状态result:{}", result);
    }

    @Test
    public void getStatusByQrCode1() {
        QueryQrCodeStatusDTO dto = new QueryQrCodeStatusDTO();
//        dto.setQrCode("f6pe8haaj2j2maj6duqe6iauigtlkocmpo6j");
        QrCodeStatusDTO result = activityService.getStatusByQrCode(dto).pickDataThrowException();
        log.info("根据二维码获取对应状态result:{}", result);
    }

    @Test
    public void updateQrCodeStatus() {
        UpdateQrCodeStatusDTO dto = new UpdateQrCodeStatusDTO();
        dto.setQrCode("f6pe8haaj2j2maj6duqe6iauigtlkocmpo6j");
        dto.setOrgId(orgId);
        QrCodeStatusDTO result = activityService.updateQrCodeStatus(dto).pickDataThrowException();
        log.info("修改扫码状态result:{}", result);
    }

    @Test
    public void updateQrCodeStatus1() {
        UpdateQrCodeStatusDTO dto = new UpdateQrCodeStatusDTO();
//        dto.setQrCode("f6pe8haaj2j2maj6duqe6iauigtlkocmpo6j");
//        dto.setOrgId(orgId);
        QrCodeStatusDTO result = activityService.updateQrCodeStatus(dto).pickDataThrowException();
        log.info("修改扫码状态result:{}", result);
    }

    @Test
    public void updateQrCodeStatusBySn() {
        QrCodeStatusDTO result = activityService
            .updateQrCodeStatusBySn(orgId, 1712115199915327411L, "270604210101KR0014")
            .pickDataThrowException();
        log.info("根据sn修改扫码状态result:{}", result);
    }

    @Test
    public void updateQrCodeStatusBySn1() {
        QrCodeStatusDTO result = activityService
            .updateQrCodeStatusBySn(null, 1712115199915327411L, "270604210101KR0014")
            .pickDataThrowException();
        log.info("根据sn修改扫码状态result:{}", result);
    }

    @Test
    public void getActivityStatusByQrCode() {
        QueryActivationStatusDTO result = activityService
            .getActivityStatusByQrCode("f6pe8haaj2j2maj6duqe6iauigtlkocmpo6j")
            .pickDataThrowException();
        log.info("根据二维码获取激活状态result:{}", result);
    }

    @Test
    public void getActivityStatusByQrCode1() {
        QueryActivationStatusDTO result = activityService
            .getActivityStatusByQrCode(null)
            .pickDataThrowException();
        log.info("根据二维码获取激活状态result:{}", result);
    }

    @Test
    public void getThirdPartyActivityStatusByTpId() {
        QueryActivationStatusDTO result = activityService
            .getThirdPartyActivityStatusByTpId("ding43be461023b931f135c2f4657eb6378f")
            .pickDataThrowException();
        log.info("根据第三方设备Id获取激活状态result:{}", result);
    }

    @Test
    public void getThirdPartyActivityStatusByTpId1() {
        QueryActivationStatusDTO result = activityService
            .getThirdPartyActivityStatusByTpId(null).pickDataThrowException();
        log.info("根据第三方设备Id获取激活状态result:{}", result);
    }

    @Test
    public void activate() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.MO_DEVICE_QR_CODE_ACTIVATE.getCode());
        dto.setQrCode("f6pe8haaj2j2maj6duqe6iauigtlkocmpo6j");
        dto.setDeviceSn("270604210101KR0014");
        dto.setDeviceType(67);
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("魔点设备二维码激活result:{}", result);
    }

    @Test
    public void activate1() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.ACTIVATION_CODE_ACTIVATE.getCode());
        dto.setActivationCode("1419063290801484");
        dto.setDeviceSn("270604210101KR0014");
        dto.setDeviceType(67);
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("魔点激活码激活result:{}", result);
    }

    @Test
    public void activate2() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.DING_DEVICE_QR_CODE_ACTIVATE.getCode());
        dto.setThirdDeviceId("1419063290801484");
        dto.setCorpId("ding43be461023b931f135c2f4657eb6378f");
        dto.setDeviceSn("160105201026KN2263");
        dto.setDeviceType(65);
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("钉钉设备二维码激活result:{}", result);
    }

    @Test
    public void activate3() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(
            DeviceActivateEngineEnums.NO_ENABLE_DING_DEVICE_QR_CODE_ACTIVATE.getCode());
        dto.setThirdDeviceId("1419063290801484");
        dto.setCorpId("ding43be461023b931f135c2f4657eb6378f");
        dto.setDeviceSn("160105201026KN2263");
        dto.setDeviceType(65);
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("没开通魔点门禁微应用激活的钉钉设备二维码激活result:{}", result);
    }

    @Test
    public void activate4() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.MO_DEVICE_QR_CODE_ACTIVATE.getCode());
//        dto.setQrCode("f6pe8haaj2j2maj6duqe6iauigtlkocmpo6j");
//        dto.setDeviceSn("270604210101KR0014");
        dto.setDeviceType(67);
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("魔点设备二维码激活result:{}", result);
    }

    @Test
    public void activate5() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.MO_REVERSE_ACTIVATE.getCode());
        dto.setOrgId(0L);
        dto.setDeviceSn("439918220715KN0048");
        dto.setDeviceName("439918220715KN0048");
        dto.setDeviceType(118);
        dto.setRegCoreType(1);
        dto.setQrCodeId(1772354238156898304L);
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("魔点设备二维码激活result:{}", result);
    }

    @Test
    public void activate6() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.DING_REVERSE_ACTIVATE.getCode());
        dto.setDeviceSn("879912230129KN0001");
        dto.setDeviceName("79912230129KN0001");
        dto.setDeviceType(123);
        dto.setRegCoreType(1);
//        dto.setQrCodeId(1772354238156898304L);
        dto.setCorpId("ding38312c281b175bf2a1320dcb25e91351");
        dto.setThirdDeviceId("1331409705");
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("魔点设备二维码激活result:{}", result);
    }

    @Test
    public void activate7() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.DING_REVERSE_REMOVE_SPACE_ACTIVATE.getCode());
        dto.setDeviceSn("110205211213KN2692");
        dto.setDeviceName("方杰测试");
        dto.setDeviceType(DeviceType.BOARD_HSE8J10L.getValue());
        dto.setRegCoreType(1);
//        dto.setQrCodeId(1772354238156898304L);
        dto.setCorpId("ding7ced35e38d48845aee0f45d8e4f7c288");
        dto.setThirdDeviceId("1331409705");
        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("钉钉反扫二维码去空间激活result:{}", result);
    }

    @Test
    public void unbindDevice() {
        Boolean result = activityService.unbindDevice("K49912230711KN0002")
            .pickDataThrowException();
        log.info("解绑设备result:{}", result);
    }

    @Test
    public void unbindDevice1() {
        Boolean result = activityService.unbindDevice(null).pickDataThrowException();
        log.info("解绑设备result:{}", result);
    }

    @Test
    public void getActivateInfo() {
        QueryActivateInfoDTO dto = new QueryActivateInfoDTO();
        dto.setDeviceSn("090102180523KN4642");
        dto.setIsNeedOfflinePassword(true);
        ActivateDeviceResultDTO result = activityService.getActivateInfo(dto)
            .pickDataThrowException();
        log.info("获取激活信息result:{}", result);
    }

    @Test
    public void getActivateInfo1() {
        QueryActivateInfoDTO dto = new QueryActivateInfoDTO();
//        dto.setDeviceSn("160105201026KN2263");
//        dto.setIsNeedOfflinePassword(true);
        ActivateDeviceResultDTO result = activityService.getActivateInfo(dto)
            .pickDataThrowException();
        log.info("获取激活信息result:{}", result);
    }

    @Test
    void deviceInitConfig() {
        String deviceSn = "110215221118KN0553";
        DeviceBaseInfoDTO result = activityService.deviceInitConfig(deviceSn).pickDataThrowException();
        log.info("获取设备初始化配置result:{}", result);
    }

    @Test
    void authDetailBySnTest(){
        String deviceSn = "A99GF210XHAQLHM";
        DeviceAuthDetailDTO authDetailDTO = activityService.authDetailBySn(deviceSn)
            .pickDataThrowException();
        log.info("获取设备授权详情result:{}", authDetailDTO);
    }

    @Test
    void deviceActiveStateChange(){
        Boolean result = activityService.deviceActiveStateChange("1235",
            4,null).pickDataThrowException();
        log.info("设备激活状态变更result:{}", result);
    }


    @Test
    void deviceActiveStateChange1(){
        DeviceActiveStateChangeDTO dto = new DeviceActiveStateChangeDTO();
        dto.setState(1);
        dto.setDeviceSn("123");
        dto.setBindOrgId(2000L);
        Boolean result = activityService.deviceActiveStateChange(dto).pickDataThrowException();
        log.info("设备激活状态变更result:{}", result);
    }

    @Test
    void checkOrgAuthorize(){
        Boolean result = activityService.checkOrgAuthorize("ding45c2feaf22c73c65ee0f45d8e4f7c28").pickDataThrowException();
        log.info("校验机构是否免管控result:{}", result);
    }

    @Test
    void getDeviceActiveState(){
        DeviceActiveStateDTO result = activityService.getDeviceActiveState("123").pickDataThrowException();
        log.info("获取设备激活信息result:{}", result);
    }


    @Test
    public void ttLockActivate() {
        ActivateDeviceDTO dto = new ActivateDeviceDTO();
        dto.setDeviceActivateType(DeviceActivateEngineEnums.THIRD_DEVICE_ACTIVATE.getCode());
        dto.setDeviceSn("123123836");
        dto.setDeviceType(520);
        dto.setOrgId(1777013504578945024L);
        dto.setJetLinkProductId("1895417436393504768");


        ActivateDeviceResultDTO result = activityService.activate(dto).pickDataThrowException();
        log.info("通通锁设备激活:{}", result);
    }

}
