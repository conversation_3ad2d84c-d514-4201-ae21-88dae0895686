package com.moredian.magicube.device.service.impl;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.moredian.bee.common.rpc.ServiceResponse;
import com.moredian.bee.filemanager.FileManager;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.bridge.api.service.deviceProperty.IIotDevicePropertyService;
import com.moredian.bridge.api.service.deviceProperty.dto.BatchIotDevicePropertyInfoDTO;
import com.moredian.magicube.common.enums.SpaceTypeEnums;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dao.entity.Rule;
import com.moredian.magicube.device.dto.device.DeviceRuleDTO;
import com.moredian.magicube.device.dto.device.DeviceRuleTemplateDTO;
import com.moredian.magicube.device.dto.device.IotDeviceTypeDTO;
import com.moredian.magicube.device.dto.device.RuleBindSpaceDTO;
import com.moredian.magicube.device.dto.device.RuleDeviceTypeQueryDTO;
import com.moredian.magicube.device.dto.device.RuleQueryDTO;
import com.moredian.magicube.device.dto.device.RuleSpaceQueryDTO;
import com.moredian.magicube.device.dto.device.RuleTemplateQueryDTO;
import com.moredian.magicube.device.dto.device.SpaceDTO;
import com.moredian.magicube.device.dto.device.SpaceRuleUpdateDTO;
import com.moredian.magicube.device.dto.device.rule.QueryRuleConfigDTO;
import com.moredian.magicube.device.dto.device.rule.RuleConfigDTO;
import com.moredian.magicube.device.dto.device.rule.RuleLockDTO;
import com.moredian.magicube.device.enums.RuleStateEnum;
import com.moredian.magicube.device.manager.RuleManager;
import com.moredian.magicube.device.manager.RuleTemplateManager;
import com.moredian.magicube.device.service.DeviceRuleService;
import com.moredian.space.message.DeleteTreeMsg;
import com.moredian.space.message.SpaceTreeDeviceListener;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;


@Slf4j
public class DeviceRuleServiceImplTest extends BaseTest {
    @Resource
    RuleManager ruleManager;
    @Resource
    RuleTemplateManager ruleTemplateManager;
    @SI
    DeviceRuleService deviceRuleService;
    @SI
    private IIotDevicePropertyService iotDevicePropertyService;
    @Resource
    private FileManager fileManager;
    @Resource
    SpaceTreeDeviceListener spaceTreeDeviceListener;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getList(){
        List<Rule> list = ruleManager.list();
        log.info("list:{}", list);
    }


    @Test
    void testGetDeviceRuleList() {
        RuleQueryDTO ruleQuery = new RuleQueryDTO();
        ruleQuery.setOrgId(1777013504578945024L);
        ServiceResponse<List<DeviceRuleDTO>> result = deviceRuleService.getRuleList(ruleQuery);
        Assertions.assertEquals(result.isSuccess(), true);
    }

    @Test
    void testGetDeviceRuleTemplateList() {
        RuleTemplateQueryDTO ruleQuery = new RuleTemplateQueryDTO();
        ruleQuery.setOrgId(1777013504578945024L);
        ServiceResponse<List<DeviceRuleTemplateDTO>> result = deviceRuleService.getRuleDefaultTemplate(ruleQuery);
        Assertions.assertEquals(result.isSuccess(), true);
    }

    @Test
    void testGetRuleDeviceTypes() {
        RuleDeviceTypeQueryDTO ruleQuery = new RuleDeviceTypeQueryDTO();
        ruleQuery.setOrgId(1777013504578945024L);
        ServiceResponse<List<IotDeviceTypeDTO>> result = deviceRuleService.getRuleDeviceTypes(ruleQuery);
        Assertions.assertEquals(result.isSuccess(), true);
    }

    @Test
    void testGetRuleRelatableSpace() {
        RuleSpaceQueryDTO dto = new RuleSpaceQueryDTO();
        dto.setOrgId(1777013504578945024L);
        dto.setSpaceType(SpaceTypeEnums.MEETING_ROOM_MODEL.getValue());
        dto.setRuleId(1800187851028037632L);
        ServiceResponse<List<SpaceDTO>> result = deviceRuleService.getRuleRelatableSpace(dto);
        Assertions.assertEquals(result.isSuccess(), true);
    }

    @Test
    void testRuleBingSpace() {
//        when(ruleManager.getRule(anyLong(), anyLong())).thenReturn(new Rule());
        RuleBindSpaceDTO ruleBindSpaceDTO = new RuleBindSpaceDTO();
        ruleBindSpaceDTO.setOrgId(1777013504578945024L);
        ruleBindSpaceDTO.setRuleName("有人开灯规则测试");
        ruleBindSpaceDTO.setSpaceIdList(Lists.newArrayList(1808156767708774400L));
        ruleBindSpaceDTO.setTriggerValue("5");
        ruleBindSpaceDTO.setDeviceTypes(Lists.newArrayList(1,2,3));
        ruleBindSpaceDTO.setTemplateId(3L);
        ServiceResponse<Boolean> result = deviceRuleService.ruleBingSpace(ruleBindSpaceDTO);
        Assertions.assertEquals(result.isSuccess(), true);
    }

    @Test
    void testUpdateSpaceRule() {

        SpaceRuleUpdateDTO request = new SpaceRuleUpdateDTO();
        request.setOrgId(1777013504578945024L);
        request.setRuleId(1800187851028037632L);
        request.setSpaceId(1792037334540091392L);
        request.setState(RuleStateEnum.ACTIVE.getCode());
        ServiceResponse<Boolean> result = deviceRuleService.updateSpaceRule(request);
        Assertions.assertEquals(result.isSuccess(), true);
    }

    @Test
    void testIIotDevicePropertyService() {
        Long orgId = 1777013504578945024L;
        List<String> iotDeviceIdLit = Lists.newArrayList("000D6F00175D77D2");
        ServiceResponse<List<BatchIotDevicePropertyInfoDTO>> listServiceResponse = iotDevicePropertyService.batchGetDevicePropertyDefineList(orgId, iotDeviceIdLit);
        Assertions.assertEquals(listServiceResponse.isSuccess(), true);
    }

    @Test
    public void test() {
        System.out.println(fileManager.getUrlByRelativePath("/subject/a.png"));
    }

    @Test
    void listenerTest() {
        DeleteTreeMsg deleteTreeMsg = new DeleteTreeMsg();
        deleteTreeMsg.setOrgId(1777013504578945024L);
        deleteTreeMsg.setTreeIds(Lists.newArrayList(1792037334540091392L, 20L));
        spaceTreeDeviceListener.subscribeDisableOrgMsg(deleteTreeMsg);
    }

    @Test
    void testRuleLock() {

        RuleLockDTO dto = new RuleLockDTO();
        dto.setRuleId(1L);
        dto.setLockType(0);

        Boolean b = deviceRuleService.ruleLock(dto).pickDataThrowException();

        log.info("b:{}", b);
    }


    @Test
    void testGetConfig() {

        QueryRuleConfigDTO dto = new QueryRuleConfigDTO();
        dto.setRuleId(1819378649669828608L);

        RuleConfigDTO b = deviceRuleService.queryRuleConfig(dto).pickDataThrowException();

        log.info("b:{}", b);
    }
}
