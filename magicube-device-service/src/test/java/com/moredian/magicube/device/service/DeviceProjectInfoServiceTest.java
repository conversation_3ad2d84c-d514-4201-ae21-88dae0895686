package com.moredian.magicube.device.service;

import com.google.common.collect.Lists;
import com.moredian.bee.tube.annotation.SI;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dao.entity.DeviceProjectCustomParam;
import com.moredian.magicube.device.dto.project.DeviceProjectInfoResponse;
import com.moredian.magicube.device.dto.project.PullDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.QueryDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.ReportDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateDeviceProjectInfoRequest;
import com.moredian.magicube.device.dto.project.UpdateProjectInfoResponse;
import com.moredian.magicube.device.manager.DeviceProjectInfoManager;
import com.xier.sesame.common.utils.JsonUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/5/10
 */
public class DeviceProjectInfoServiceTest extends BaseTest {

    @SI
    private DeviceProjectInfoService deviceProjectInfoService;

    @Autowired
    private DeviceProjectInfoManager deviceProjectInfoManager;

    @Test
    public void testReportProjectInfo() throws InterruptedException {
        Thread thread = new Thread(() -> {
            ReportDeviceProjectInfoRequest request = new ReportDeviceProjectInfoRequest();
            request.setDeviceSn("090205201124KN2867");
            request.setProjectInfo("{\"projectId\":\"PRO10000002\",\"version\":\"*******\",\"customParams\":{\"faceLimit\":10002}}");
            Long aLong = deviceProjectInfoService.reportProjectInfo(request).pickDataThrowException();
            System.out.println(aLong);
        });
        Thread thread1 = new Thread(() -> {
            ReportDeviceProjectInfoRequest request = new ReportDeviceProjectInfoRequest();
            request.setDeviceSn("090205201124KN2867");
            request.setProjectInfo("{\"projectId\":\"PRO10000003\",\"version\":\"1.0.0.3\",\"customParams\":{\"faceLimit\":10003}}");
            Long aLong = deviceProjectInfoService.reportProjectInfo(request).pickDataThrowException();
            System.out.println(aLong);
        });
        Thread thread2 = new Thread(() -> {
            ReportDeviceProjectInfoRequest request = new ReportDeviceProjectInfoRequest();
            request.setDeviceSn("090205201124KN2867");
            request.setProjectInfo("{\"projectId\":\"PRO10000004\",\"version\":\"1.0.0.4\",\"customParams\":{\"faceLimit\":10004}}");
            Long aLong = deviceProjectInfoService.reportProjectInfo(request).pickDataThrowException();
            System.out.println(aLong);
        });
        thread.start();
        thread1.start();
        thread2.start();
        TimeUnit.SECONDS.sleep(1000);
    }

    @Test
    public void testPullProjectInfo() {
        PullDeviceProjectInfoRequest request = new PullDeviceProjectInfoRequest();
        request.setDeviceSn("090205201124KN2867");
        request.setProjectId("PRO10000004");
        DeviceProjectInfoResponse response = deviceProjectInfoService.pullProjectInfo(request).pickDataThrowException();
        System.out.println(JsonUtils.toJson(response));
    }

    @Test
    public void testFindDeviceProjectInfo() {
        QueryDeviceProjectInfoRequest request = new QueryDeviceProjectInfoRequest();
        request.setProjectId("PRO10000004");
        request.setDeviceSnList(Lists.newArrayList("090205201124KN2867"));
        List<DeviceProjectInfoResponse> deviceProjectInfoResponses = deviceProjectInfoService.findDeviceProjectInfo(request).pickDataThrowException();
        System.out.println(JsonUtils.toJson(deviceProjectInfoResponses));
    }

    @Test
    public void UpdateDeviceProjectInfo() {
        UpdateDeviceProjectInfoRequest request = new UpdateDeviceProjectInfoRequest();
        request.setProjectId("OPP.20220718005");
        request.setDeviceSn("879912230116KN0001");
        request.setProjectInfo("{    \"projectId\":\"OPP.20220718005\",    \"sceneType\":\"36\",    \"sceneTypeVersion\":\"1.0.0\",    \"version\":\"*******\",    \"customParams\":    {            }}");
        UpdateProjectInfoResponse updateProjectInfoResponse = deviceProjectInfoService.updateProjectInfo(request).pickDataThrowException();
        System.out.println(JsonUtils.toJson(updateProjectInfoResponse));
    }

    @Test
    public void queryingProjectCustomParam(){
        DeviceProjectCustomParam deviceProjectCustomParam = deviceProjectInfoManager.queryDeviceProjectCustomParam("360118221008KN679");
        System.out.println(JsonUtils.toJson(deviceProjectCustomParam));
    }
}
