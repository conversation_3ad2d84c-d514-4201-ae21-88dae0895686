package com.moredian.magicube.device.service;

import com.moredian.bee.common.utils.JsonUtils;
import com.moredian.magicube.device.BaseTest;
import com.moredian.magicube.device.dto.version.ChangeDeviceAppVersionDTO;
import com.moredian.magicube.device.dto.version.ChangeLatchDeviceVersionDto;
import com.moredian.magicube.device.dto.version.DeviceCurrVersionDTO;
import com.moredian.magicube.device.dto.version.DeviceEnforceUpdateDTO;
import com.moredian.magicube.device.dto.version.DeviceVersionDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 设备版本相关测试用例
 */

@Slf4j
public class DeviceVersionServiceTest extends BaseTest {

    @Autowired
    private DeviceVersionService deviceVersionService;

    private static final Long orgId = 1712115199915327488L;

    @Test
    public void changeDeviceAppVersion() {
        ChangeDeviceAppVersionDTO dto = new ChangeDeviceAppVersionDTO();
        dto.setOrgId(orgId);
        dto.setDeviceId(1713378092099043328L);
//        dto.setSn("260910210121KN2034");
        dto.setAppType(30003);
        dto.setVersionCode(3);
        deviceVersionService.changeDeviceAppVersion(dto).pickDataThrowException();
    }

    @Test
    public void changeDeviceRomVersion() {
        ChangeDeviceAppVersionDTO dto = new ChangeDeviceAppVersionDTO();
        dto.setOrgId(orgId);
        dto.setDeviceId(1713378092099043328L);
//        dto.setSn("260910210121KN2034");
        dto.setAppType(1048);
        dto.setVersionCode(10030);
        deviceVersionService.changeDeviceRomVersion(dto).pickDataThrowException();
    }

    @Test
    public void changeDeviceEnforceUpdateStatus() {
        DeviceEnforceUpdateDTO dto = new DeviceEnforceUpdateDTO();
        dto.setDeviceId(1713378092099043328L);
        dto.setAppType(1048);
        dto.setVersionCode(10030);
        dto.setSystemType(1);
        dto.setTryTimes(1);
        dto.setEnforceUpdateTime(new Date());
        deviceVersionService.changeDeviceEnforceUpdateStatus(dto).pickDataThrowException();
    }

    @Test
    public void getDeviceEnforceUpdateStatus() {
        DeviceEnforceUpdateDTO result = deviceVersionService.getDeviceEnforceUpdateStatus(1713378092099043328L).pickDataThrowException();
        log.info("获取设备强制升级状态:{}", result);
    }

    @Test
    public void getDeviceEnforceUpdateStatus1() {
        DeviceEnforceUpdateDTO result = deviceVersionService.getDeviceEnforceUpdateStatus(null).pickDataThrowException();
        log.info("获取设备强制升级状态:{}", result);
    }

    @Test
    public void listApkByOrgIdAndDeviceIds() {
        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1773810807595859968L);
        deviceIds.add(1777001822267899904L);
        List<DeviceVersionDTO> result = deviceVersionService.listApkByOrgIdAndDeviceIds(1773810807595859968L, deviceIds).pickDataThrowException();
        log.info("获取设备当前app版本列表:{}", result);
    }

    @Test
    public void listApkByOrgIdAndDeviceIds1() {
        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1713378092099043328L);
//        deviceIds.add(1712217256928215041L);
        List<DeviceVersionDTO> result = deviceVersionService.listApkByOrgIdAndDeviceIds(null, deviceIds).pickDataThrowException();
        log.info("获取设备当前app版本列表:{}", result);
    }

    @Test
    public void listRomByOrgIdAndDeviceIds() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1713378092099043328L);
        deviceIds.add(1712217256928215041L);
        List<DeviceVersionDTO> result = deviceVersionService.listRomByOrgIdAndDeviceIds(orgId, deviceIds).pickDataThrowException();
        log.info("批量获取设备当前ROM版本:{}", result);
    }

    @Test
    public void listRomByOrgIdAndDeviceIds1() {
        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1713378092099043328L);
//        deviceIds.add(1712217256928215041L);
        List<DeviceVersionDTO> result = deviceVersionService.listRomByOrgIdAndDeviceIds(null, deviceIds).pickDataThrowException();
        log.info("批量获取设备当前ROM版本:{}", result);
    }

    @Test
    public void listByOrgIdAndDeviceIds() {
        List<Long> deviceIds = new ArrayList<>();
        deviceIds.add(1713378092099043328L);
        deviceIds.add(1712217256928215041L);
        List<DeviceCurrVersionDTO> result = deviceVersionService.listByOrgIdAndDeviceIds(orgId, deviceIds).pickDataThrowException();
        log.info("批量获取设备当前APP、ROM版本信息及设备最新可用APP、ROM版本信息:{}", result);
    }

    @Test
    public void listByOrgIdAndDeviceIds1() {
        List<Long> deviceIds = new ArrayList<>();
//        deviceIds.add(1713378092099043328L);
//        deviceIds.add(1712217256928215041L);
        List<DeviceCurrVersionDTO> result = deviceVersionService.listByOrgIdAndDeviceIds(null, deviceIds).pickDataThrowException();
        log.info("批量获取设备当前APP、ROM版本信息及设备最新可用APP、ROM版本信息:{}", result);
    }

    @Test
    public void changeLatchDeviceVersion() {
        ChangeLatchDeviceVersionDto dto = new ChangeLatchDeviceVersionDto();
        dto.setOrgId(1773622275711434752L);
        dto.setDeviceId(1773663584706887680L);
        dto.setDeviceSn("K49912230711KN0002");
        dto.setAppType(500);
        dto.setAppVersion(1000);
        dto.setRomType(5000);
        dto.setRomVersion(1000);
        dto.setLockBoard3861LVersion(1000);
        dto.setLockBoard804Version(1000);
        dto.setLockBoard805Version(1000);
        dto.setModelVersion(1000);
        dto.setLockVersion(1000);
        Boolean aBoolean = deviceVersionService.changeLatchDeviceVersion(dto).pickDataThrowException();
        System.out.println(JsonUtils.toJson(dto));
        System.out.println(aBoolean);
    }
}