<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.moredian.magicube</groupId>
        <artifactId>device-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>magicube-device-service</artifactId>
    <version>${current.version}</version>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.moredian.bee</groupId>
                <artifactId>bee-pom-dependencies</artifactId>
                <version>2.3.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <properties>
        <current.version>3.1.55-SNAPSHOT</current.version>
        <magicube.device.api>3.1.55-SNAPSHOT</magicube.device.api>
        <baomidou.version>3.0.6</baomidou.version>
        <magicube.common.version>3.0.68-SNAPSHOT</magicube.common.version>
        <magicube-ocean-api.version>3.0.1-SNAPSHOT</magicube-ocean-api.version>
        <magicube.core.version>3.1.45-SNAPSHOT</magicube.core.version>
        <mysql-connector.version>5.1.49</mysql-connector.version>
        <commons-codec.version>1.15</commons-codec.version>
        <javax-el.version>2.2.4</javax-el.version>
        <mybatis-generator.version>1.3.7</mybatis-generator.version>
        <mapper.version>4.1.5</mapper.version>
        <pagehelper.version>1.2.3</pagehelper.version>
        <moredian.iothub.control.version>2.4.0-SNAPSHOT</moredian.iothub.control.version>
        <ding.api.version>1.6.64-SNAPSHOT</ding.api.version>
        <deliver.api.version>3.0.33-SNAPSHOT</deliver.api.version>
        <guard.api.version>1.1.1-SNAPSHOT</guard.api.version>
        <hephaestus.api.version>3.0.3-SNAPSHOT</hephaestus.api.version>
        <moredian.ota.version>5.0.5-SNAPSHOT</moredian.ota.version>
        <moredian.bee.monitor.version>1.0.1</moredian.bee.monitor.version>
        <cloudeye.unified.recognition.api.verison>3.7.2-SNAPSHOT</cloudeye.unified.recognition.api.verison>
        <space.core.api.version>3.1.11-SNAPSHOT</space.core.api.version>
        <com.moredian.pipeline.device.api.version>3.0.6-SNAPSHOT</com.moredian.pipeline.device.api.version>
        <eden.whale.api.version>1.0.1-SNAPSHOT</eden.whale.api.version>
        <com.moredian.bee.job>1.1.0</com.moredian.bee.job>
        <magicube.iot.bridge.api.version>3.0.3-SNAPSHOT</magicube.iot.bridge.api.version>
        <bee-conf-core.version>3.0.0-SNAPSHOT</bee-conf-core.version>
        <magicube.auth.api.version>3.0.3-SNAPSHOT</magicube.auth.api.version>
    </properties>

    <profiles>
        <profile>
            <id>show</id>
            <properties>
                <package.environment>show</package.environment>
                <current.version>3.1.55</current.version>
                <magicube.device.api>3.1.55</magicube.device.api>
                <baomidou.version>3.0.6</baomidou.version>
                <magicube.common.version>3.0.68</magicube.common.version>
                <magicube-ocean-api.version>3.0.1</magicube-ocean-api.version>
                <magicube.core.version>3.1.45</magicube.core.version>
                <mysql-connector.version>5.1.49</mysql-connector.version>
                <commons-codec.version>1.15</commons-codec.version>
                <javax-el.version>2.2.5</javax-el.version>
                <mybatis-generator.version>1.3.7</mybatis-generator.version>
                <mapper.version>4.1.5</mapper.version>
                <pagehelper.version>1.2.3</pagehelper.version>
                <moredian.iothub.control.version>2.4.0</moredian.iothub.control.version>
                <ding.api.version>1.6.64</ding.api.version>
                <deliver.api.version>3.0.33</deliver.api.version>
                <guard.api.versionn>1.1.1</guard.api.versionn>
                <hephaestus.api.version>3.0.3</hephaestus.api.version>
                <moredian.ota.version>5.0.5</moredian.ota.version>
                <moredian.bee.monitor.version>1.0.1</moredian.bee.monitor.version>
                <cloudeye.unified.recognition.api.verison>3.7.2</cloudeye.unified.recognition.api.verison>
                <space.core.api.version>3.1.11</space.core.api.version>
                <com.moredian.pipeline.device.api.version>3.0.6</com.moredian.pipeline.device.api.version>
                <eden.whale.api.version>1.0.1</eden.whale.api.version>
                <com.moredian.bee.job>1.1.0</com.moredian.bee.job>
                <magicube.iot.bridge.api.version>3.0.3</magicube.iot.bridge.api.version>
                <bee-conf-core.version>3.0.0</bee-conf-core.version>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-conf-core</artifactId>
            <version>${bee-conf-core.version}</version>
        </dependency>

        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Third Party Dependencies Begin, Alphabetical order -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector.version}</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>${mybatis-generator.version}</version>
        </dependency>

        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-mybatisboot</artifactId>
        </dependency>

        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper</artifactId>
            <version>${mapper.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${baomidou.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${baomidou.version}</version>
        </dependency>


        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.16</version>
        </dependency>

        <!--接入prometheus-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.moredian.zxing</groupId>
            <artifactId>zxing</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- Third Party Dependencies End -->

        <!-- Moredian Dependencies Begin, Alphabetical order -->
        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bee-conf-core</artifactId>
                    <groupId>com.moredian.bee</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-rmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-mybatisboot</artifactId>
        </dependency>

        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-filemanager</artifactId>
        </dependency>

        <dependency>
            <groupId>com.moredian.magicube</groupId>
            <artifactId>magicube-common</artifactId>
            <version>${magicube.common.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.moredian.magicube</groupId>
            <artifactId>magicube-ocean-api</artifactId>
            <version>${magicube-ocean-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.moredian.magicube</groupId>
            <artifactId>magicube-core-api</artifactId>
            <version>${magicube.core.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.moredian.magicube</groupId>
            <artifactId>magicube-device-api</artifactId>
            <version>${magicube.device.api}</version>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>javassist</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.moredian.idgenerator</groupId>
            <artifactId>idgenerator-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.moredian.iothub</groupId>
            <artifactId>iothub-control-api</artifactId>
            <version>${moredian.iothub.control.version}</version>
        </dependency>
        <dependency>
            <groupId>com.moredian.deliver</groupId>
            <artifactId>deliver-api</artifactId>
            <version>${deliver.api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xier.ding</groupId>
            <artifactId>ding-api</artifactId>
            <version>${ding.api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xier.guard</groupId>
            <artifactId>guard-api</artifactId>
            <version>${guard.api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.moredian.hephaestus</groupId>
            <artifactId>hephaestus-api</artifactId>
            <version>${hephaestus.api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>magicube-common</artifactId>
                    <groupId>com.moredian.magicube</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.moredian.ota</groupId>
            <artifactId>ota-api</artifactId>
            <version>${moredian.ota.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fishnet-device-api</artifactId>
                    <groupId>com.moredian.fishnet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>magicube-common</artifactId>
                    <groupId>com.moredian.magicube</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--日志监控-->
        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-monitor-default</artifactId>
            <version>${moredian.bee.monitor.version}</version>
        </dependency>
        <!--慧眼-->
        <dependency>
            <groupId>com.moredian.cloudeye</groupId>
            <artifactId>unified-recognition-api</artifactId>
            <version>${cloudeye.unified.recognition.api.verison}</version>
        </dependency>

        <dependency>
            <groupId>com.moredian.space</groupId>
            <artifactId>space-core-api</artifactId>
            <version>${space.core.api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.moredian.device</groupId>
            <artifactId>device-pipeline-api</artifactId>
            <version>${com.moredian.pipeline.device.api.version}</version>
        </dependency>

        <!--增量消息中心-->
        <dependency>
            <groupId>com.moredian.eden</groupId>
            <artifactId>whale-api</artifactId>
            <version>${eden.whale.api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.moredian.bee</groupId>
            <artifactId>bee-job</artifactId>
            <version>${com.moredian.bee.job}</version>
        </dependency>

        <!-- 这是Linux版本的demo-->
        <dependency>
            <groupId>com.dahua.netsdk</groupId>
            <artifactId>dahua-netsdk-linux</artifactId>
            <version>1.0.0</version>
            <classifier>demo</classifier>
        </dependency>

        <dependency>
            <groupId>com.moredian.magicube</groupId>
            <artifactId>iot-bridge-api</artifactId>
            <version>${magicube.iot.bridge.api.version}</version>
        </dependency>

        <!--1.1.0是linux的jni包 -->
<!--        <dependency>-->
<!--            <groupId>com.dahua.netsdk</groupId>-->
<!--            <artifactId>dahua-netsdk-jni</artifactId>-->
<!--            <version>1.1.0</version>-->
<!--        </dependency>-->
        <!-- 大华摄像头sdk end -->

        <!-- Moredian Dependencies End -->
    </dependencies>

    <build>
        <plugins>
            <!-- The configuration of maven-assembly-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.6</version>
                <!-- The configuration of the plugin -->
                <configuration>
                    <finalName>${project.artifactId}</finalName>
                    <!-- Specifies the configuration file of the assembly plugin -->
                    <descriptors>
                        <descriptor>src/main/assembly/package.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>device-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.4</version>
                <configuration>
                    <configurationFile>/Users/<USER>/Documents/code/magicube-device/magicube-device-service/src/main/resources/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.49</version>
                    </dependency>
                </dependencies>
            </plugin>

        </plugins>
    </build>
</project>
