-- 2021/09/28 新增设备外设白名单表
CREATE TABLE `hive_peripherals_white_list` (
  `peripherals_white_list_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `peripherals_sn` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '外设sn',
  `peripherals_type` tinyint(4) DEFAULT NULL COMMENT '外设的设备类型',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-断开 1-连接',
  `gmt_create` datetime(3) NOT NULL,
  `gmt_modify` datetime(3) NOT NULL,
  PRIMARY KEY (`peripherals_white_list_id`),
  UNIQUE KEY `idx_peripherals_sn` (`peripherals_sn`)
) ENGINE=InnoDB AUTO_INCREMENT=130225 DEFAULT CHARSET=utf8mb4 COMMENT='设备外设白名单表';


CREATE TABLE `hive_device_type_property_config`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `device_type` int(11) NOT NULL COMMENT '物联网设备类型',
    `property_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物模型标识',
    `property_json` json NULL COMMENT '模型属性json串',
    `sort` int(11) NULL DEFAULT NULL COMMENT '显示排序',
    `visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '前端是否可见',
    `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0:未删除，1：删除',
    `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `gmt_modify` datetime NULL DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
);