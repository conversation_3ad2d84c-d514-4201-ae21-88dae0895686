-- 表新增，分库
 CREATE TABLE `hive_notice_record` (
   `notice_record_id` BIGINT(20) NOT NULL COMMENT '记录id，主键',
   `org_id` BIGINT(20) NOT NULL COMMENT '机构id',
   `device_sn` VARCHAR(64) NULL DEFAULT '' COMMENT '设备sn',
   `notice_event` INT NOT NULL COMMENT '触发通知事件',
   `notice_result` INT NOT NULL COMMENT '通知结果，0-失败，1-成功',
   `last_offline_time` BIGINT(20) DEFAULT NULL COMMENT '上一次离线时间',
   `gmt_create` DATETIME(3) NOT NULL,
   `gmt_modify` DATETIME(3) NOT NULL,
   PRIMARY KEY (`notice_record_id`),
   KEY `idx_org_device` (`org_id`,`device_sn`))
 ENGINE = InnoDB
 DEFAULT CHARACTER SET = utf8mb4
 COLLATE = utf8mb4_bin
 COMMENT = '设备工作通知记录表';