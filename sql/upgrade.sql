-- 2021/09/28 塔下增加是否教育设备字段
ALTER TABLE `da_device_inventory` ADD `is_edu_device` tinyint(1) DEFAULT '0' COMMENT '是教育行业设备：默认0-不是，1-是';

ALTER TABLE `hive_peripherals` ADD COLUMN `peripherals_connect_id` varchar(64) NULL COMMENT '外设连接设备的唯一标识';

ALTER TABLE `hive_peripherals` ADD COLUMN `mobile_sys_type` tinyint(4) NULL COMMENT '手机系统类型';

-- 2022/01/11 小程序迭代（新设备服务切换）
ALTER TABLE `da_device_inventory` ADD `is_edu_device` tinyint(1) DEFAULT '0' COMMENT '是教育行业设备：默认0-不是，1-是',algorithm=copy, lock=none;

ALTER TABLE `hive_peripherals` ADD `peripherals_connect_id` varchar(64) DEFAULT NULL COMMENT '外设连接设备的唯一标识',algorithm=copy, lock=none;

ALTER TABLE `hive_peripherals` ADD `mobile_sys_type` tinyint(4) DEFAULT NULL COMMENT '手机系统类型',algorithm=copy, lock=none ;

ALTER TABLE hive_orgi_equipment CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,algorithm=copy, lock=none;

ALTER TABLE `hive_device_subject` ADD `template_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '模板类型：1-模板一 2-模板二',algorithm=copy, lock=none ;

ALTER TABLE `hive_orgi_equipment` ADD COLUMN `parent_unique_number` VARCHAR(64) NULL DEFAULT NULL COMMENT '父设备sn';

ALTER TABLE `hive_orgi_equipment` ADD COLUMN `virtual_flag` tinyint(2) DEFAULT 0 COMMENT '0-非虚拟设备，1-虚拟设备';

-- 修改了父设备sn规则，需要清除原有数据再重新同步IOT设备
update hive_orgi_equipment set parent_unique_number = '';

-- 修改设备类型名称
UPDATE `hive`.`hive_device_type` SET `device_type` = 501, `device_type_name` = '网关', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:33:30.568', `gmt_modify` = '2024-03-29 15:33:30.568' WHERE `id` = 1794845066330636288;
UPDATE `hive`.`hive_device_type` SET `device_type` = 502, `device_type_name` = '16路空调控制器', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:33:55.597', `gmt_modify` = '2024-03-29 15:33:55.597' WHERE `id` = 1794845092100440065;
UPDATE `hive`.`hive_device_type` SET `device_type` = 503, `device_type_name` = '三键开关', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:34:00.897', `gmt_modify` = '2024-03-29 15:34:00.897' WHERE `id` = 1794845100690374656;
UPDATE `hive`.`hive_device_type` SET `device_type` = 504, `device_type_name` = '二键开关', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:34:06.902', `gmt_modify` = '2024-03-29 15:34:06.902' WHERE `id` = 1794845100690374657;
UPDATE `hive`.`hive_device_type` SET `device_type` = 505, `device_type_name` = '计量插座', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:34:20.403', `gmt_modify` = '2024-03-29 15:34:20.403' WHERE `id` = 1794845109280309248;
UPDATE `hive`.`hive_device_type` SET `device_type` = 506, `device_type_name` = '人体感应传感器', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:34:51.355', `gmt_modify` = '2024-03-29 15:34:51.355' WHERE `id` = 1794845152229982208;
UPDATE `hive`.`hive_device_type` SET `device_type` = 507, `device_type_name` = '温湿度传感器', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:34:58.483', `gmt_modify` = '2024-03-29 15:34:58.483' WHERE `id` = 1794845160819916800;
UPDATE `hive`.`hive_device_type` SET `device_type` = 508, `device_type_name` = '环境监测传感器', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:35:23.880', `gmt_modify` = '2024-03-29 15:35:23.880' WHERE `id` = 1794845186589720576;
UPDATE `hive`.`hive_device_type` SET `device_type` = 599, `device_type_name` = '其他iot设备类型', `description` = '', `status` = 1, `gmt_create` = '2024-03-29 15:40:46.534', `gmt_modify` = '2024-03-29 15:40:46.534' WHERE `id` = 1794845521597169664;

-- 增加单键开关类型
INSERT INTO `hive`.`hive_device_type` (`id`, `device_type`, `device_type_name`, `description`, `status`, `gmt_create`, `gmt_modify`) VALUES (1794845521597169665, 509, '单键开关', NULL, 1, '2024-06-04 14:56:11.000', '2024-06-04 14:56:15.000');
INSERT INTO `hive`.`hive_device_type_property_relation` (`id`, `device_type_property_id`, `device_type`, `gmt_create`, `gmt_modify`) VALUES (1800180291885596673, 1794759948668764160, 509, '2024-06-04 14:59:29.000', '2024-06-04 14:59:31.000');


ALTER TABLE hive_orgi_equipment MODIFY COLUMN memo VARCHAR(256);
ALTER TABLE hive_orgi_equipment MODIFY COLUMN equipment_name VARCHAR(256);

ALTER TABLE hive_rule ADD COLUMN space_type INT DEFAULT 36 COMMENT '应用场景id，默认36：会议室、35：教室';

UPDATE `hive_rule_template`
SET rule_json = '{"description":"#{description}","name":"#{ruleName}","triggerType":"timer","trigger":{"type":"timer","timer":{"trigger":"week","when":[],"mod":"once","once":{"time":"#{time}"}},"typeName":"手动触发"},"parallel":false,"branches":[{"when":[],"shakeLimit":{"enabled":false,"time":1,"threshold":1,"alarmFirst":false},"then":[{"parallel":false,"actions":#{actionDeviceList}},{"parallel":true,"actions":[]}],"executeAnyway":false}],"state":{"text":"正常","value":"started"},"options":{"trigger":{"when":"每天","time":"#{time} 执行1次"},"when":[{"terms":[{"terms":[["","eq","","and"]]}]}]}}'
WHERE rule_template_id = 1;

UPDATE `hive_rule_template`
SET rule_json = '{"description":"#{description}","name":"#{ruleName}","triggerType":"device","trigger":{"type":"device","device":{"source":"fixed","selector":"fixed","selectorValues":[{"value":"#{triggerDeviceId}","name":"#{triggerDeviceName}"}],"productId":"#{triggerDeviceProductId}","operation":{"operator":"reportProperty"}},"typeName":"设备触发"},"parallel":false,"branches":[{"when":[{"type":"and","termType":"eq","options":[],"terms":[{"column":"properties.#{motionStatusProperty}.current","value":{"source":"manual","value":"#{motionStatusValue}"},"type":"and","termType":"eq","options":[],"terms":[],"error":false}]}],"shakeLimit":{"enabled":false,"time":1,"threshold":1,"alarmFirst":false},"then":[{"parallel":false,"actions":#{actionDeviceList}}],"executeAnyway":false}],"state":{"text":"正常","value":"started"},"options":{"trigger":{"name":"#{triggerDeviceName}","extraName":"","onlyName":false,"type":"属性上报","typeIcon":"icon-file-upload-outline","productName":"","selectorIcon":"icon-shebei1","action":""},"when":[{"terms":[{"terms":[["当前值","eq",{"0":"#{motionStatusText}"},"and",["","","","并且"]]]}]}]}}'
WHERE rule_template_id = 2

-- 2024-08-21 14:59:29.000 设备操作记录表增加操作人员Id
ALTER TABLE hive_device_log ADD COLUMN `member_id` bigint(20) DEFAULT '0' COMMENT '操作人员Id';

-- 设备注册同步数据功能
ALTER TABLE hive_device_face_model ADD COLUMN model_type INTEGER ( 10 ) NOT NULL DEFAULT 2 COMMENT '1服务识别用的端特征值，2Android终端识别用的特征值';

-- 摄像头新增字段
ALTER TABLE hive_device_camera_info ADD COLUMN register_type INTEGER ( 10 ) NOT NULL DEFAULT 1 COMMENT '注册类型 1-平台注册 2-设备注册';
ALTER TABLE hive_device_camera_info ADD COLUMN local_device_ip varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '本地设备ip';
ALTER TABLE hive_device_camera_info ADD COLUMN local_port int(4) NOT NULL DEFAULT '0' COMMENT '本地端口号';



-- 新增设备激活状态表
CREATE TABLE `hive_device_active_state` (
  `id` bigint(20) NOT NULL COMMENT '设备激活状态Id',
  `device_sn` varchar(50) NOT NULL COMMENT '设备号',
  `state` int(1) NOT NULL DEFAULT '1' COMMENT '设备激活状态：1-配置网络 2-待设备联网 3-设备联网中 4-设备联网成功 5-团队绑定中 6-团队绑定成功 7-运行模式选择中 8-激活中 9-激活成功 10-关联钉钉会议室',
  `bind_org_url` varchar(256) COLLATE utf8mb4_bin DEFAULT '' COMMENT '绑定机构页面url（仅用于钉钉SDK激活流程）',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modify` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `device_sn` (`device_sn`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='设备激活状态表';
